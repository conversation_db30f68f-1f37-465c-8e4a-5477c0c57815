name: Check Server package version Modify

on:
  pull_request:
    branches:
      - document
      - develop
    paths:
      - api/**
      - .github/workflows/check_package_modify.yml
    types: [opened, synchronize]
  workflow_dispatch:

jobs:
  server_package_modify_guard:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Check for changes in specific files
        run: |
          git fetch origin ${{ github.base_ref }} --depth=1
          git fetch origin ${{ github.head_ref }} --depth=1
          if git diff --name-only origin/develop...HEAD | grep -q 'api/.*/server/package*.json'; then
            echo "Error: server package files have changes. Please do not modify thiese file."
            exit 1
          else
            echo "No changes in package.json & package-lock.json"
          fi