name: Generate test coverage reports

on:
  pull_request:
    types: [opened, reopened, synchronize]
    paths:
      - "api/**/server/**"
  workflow_dispatch:

defaults:
  run:
    working-directory: "api"

jobs:
  report_coverrage_monolithic-service_server:
    runs-on: ubuntu-latest
    environment:
      name: TEST
    permissions:
      contents: write
      issues: write
      pull-requests: write
      checks: write
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
        with:
          fetch-depth: "0"

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "22"

      - name: Export all secrets and variables to $GITHUB_ENV (flattened)
        run: |
          # Secrets
          echo "ALCHEMY_API_KEY=$(echo "${{ secrets.ALCHEMY_API_KEY }}" | tr -d '\n')" >> $GITHUB_ENV
          echo "DB_PASSWORD=$(echo "${{ secrets.DB_PASSWORD }}" | tr -d '\n')" >> $GITHUB_ENV
          echo "FIREBASE_CLIENT_EMAIL=$(echo "${{ secrets.FIREBASE_CLIENT_EMAIL }}" | tr -d '\n')" >> $GITHUB_ENV
          echo "FIREBASE_PRIVATE_KEY=$(echo "${{ secrets.FIREBASE_PRIVATE_KEY }}" | tr -d '\n')" >> $GITHUB_ENV
          echo "FIREBASE_TOKEN=$(echo "${{ secrets.FIREBASE_TOKEN }}" | tr -d '\n')" >> $GITHUB_ENV
          echo "JSON_RPC_URL=$(echo "${{ secrets.JSON_RPC_URL }}" | tr -d '\n')" >> $GITHUB_ENV
          echo "STRIPE_SECRET_KEY=$(echo "${{ secrets.STRIPE_SECRET_KEY }}" | tr -d '\n')" >> $GITHUB_ENV
          echo "STRIPE_WEBHOOK_SECRET=$(echo "${{ secrets.STRIPE_WEBHOOK_SECRET }}" | tr -d '\n')" >> $GITHUB_ENV

          # Variables
          echo "ALCHEMY_ADDRESS_ACTIVITY_WEBHOOK_ID=$(echo "${{ vars.ALCHEMY_ADDRESS_ACTIVITY_WEBHOOK_ID }}" | tr -d '\n')" >> $GITHUB_ENV
          echo "ALCHEMY_ADDRESS_ACTIVITY_WEBHOOK_SIGNING_KEY=$(echo "${{ vars.ALCHEMY_ADDRESS_ACTIVITY_WEBHOOK_SIGNING_KEY }}" | tr -d '\n')" >> $GITHUB_ENV
          echo "ALCHEMY_AUTH_TOKEN=$(echo "${{ vars.ALCHEMY_AUTH_TOKEN }}" | tr -d '\n')" >> $GITHUB_ENV
          echo "ALCHEMY_CHAIN_NAME=$(echo "${{ vars.ALCHEMY_CHAIN_NAME }}" | tr -d '\n')" >> $GITHUB_ENV
          echo "ALCHEMY_NFT_ACTIVITY_WEBHOOK_ID=$(echo "${{ vars.ALCHEMY_NFT_ACTIVITY_WEBHOOK_ID }}" | tr -d '\n')" >> $GITHUB_ENV
          echo "ALCHEMY_NFT_ACTIVITY_WEBHOOK_SIGNING_KEY=$(echo "${{ vars.ALCHEMY_NFT_ACTIVITY_WEBHOOK_SIGNING_KEY }}" | tr -d '\n')" >> $GITHUB_ENV
          echo "BASE_MAX_FEE_PER_GAS=$(echo "${{ vars.BASE_MAX_FEE_PER_GAS }}" | tr -d '\n')" >> $GITHUB_ENV
          echo "CHAIN_ID=$(echo "${{ vars.CHAIN_ID }}" | tr -d '\n')" >> $GITHUB_ENV
          echo "CORS_DOMAINS=$(echo "${{ vars.CORS_DOMAINS }}" | tr -d '\n')" >> $GITHUB_ENV
          echo "DB_DATABASE=$(echo "${{ vars.DB_DATABASE }}" | tr -d '\n')" >> $GITHUB_ENV
          echo "DB_HOST=$(echo "${{ vars.DB_HOST }}" | tr -d '\n')" >> $GITHUB_ENV
          echo "DB_PORT=$(echo "${{ vars.DB_PORT }}" | tr -d '\n')" >> $GITHUB_ENV
          echo "DB_USER=$(echo "${{ vars.DB_USER }}" | tr -d '\n')" >> $GITHUB_ENV
          echo "FIREBASE_PROJECT_ID=$(echo "${{ vars.FIREBASE_PROJECT_ID }}" | tr -d '\n')" >> $GITHUB_ENV
          echo "FIRESTORE_DB=$(echo "${{ vars.FIRESTORE_DB }}" | tr -d '\n')" >> $GITHUB_ENV
          echo "FLAVOR=$(echo "${{ vars.FLAVOR }}" | tr -d '\n')" >> $GITHUB_ENV
          echo "GAS_LIMIT_MULTIPLIER=$(echo "${{ vars.GAS_LIMIT_MULTIPLIER }}" | tr -d '\n')" >> $GITHUB_ENV
          echo "GCP_PROJECT_ID=$(echo "${{ vars.GCP_PROJECT_ID }}" | tr -d '\n')" >> $GITHUB_ENV
          echo "GCS_BUCKET_NAME=$(echo "${{ vars.GCS_BUCKET_NAME }}" | tr -d '\n')" >> $GITHUB_ENV
          echo "LIFF_ID=$(echo "${{ vars.LIFF_ID }}" | tr -d '\n')" >> $GITHUB_ENV
          echo "MAX_FEE_PER_GAS_MULTIPLIER=$(echo "${{ vars.MAX_FEE_PER_GAS_MULTIPLIER }}" | tr -d '\n')" >> $GITHUB_ENV
          echo "MAX_PRIORITY_FEE_PER_GAS_MULTIPLIER=$(echo "${{ vars.MAX_PRIORITY_FEE_PER_GAS_MULTIPLIER }}" | tr -d '\n')" >> $GITHUB_ENV
          echo "METADATA_URL=$(echo "${{ vars.METADATA_URL }}" | tr -d '\n')" >> $GITHUB_ENV
          echo "OPENAI_API_KEY=$(echo "${{ vars.OPENAI_API_KEY }}" | tr -d '\n')" >> $GITHUB_ENV
          echo "PORT=$(echo "${{ vars.PORT }}" | tr -d '\n')" >> $GITHUB_ENV
          echo "REDIS_URL=$(echo "${{ vars.REDIS_URL }}" | tr -d '\n')" >> $GITHUB_ENV
          echo "SERVICE_ID=$(echo "${{ vars.SERVICE_ID }}" | tr -d '\n')" >> $GITHUB_ENV
          echo "FIRESTORE_EMULATOR_HOST=$(echo "${{ vars.FIRESTORE_EMULATOR_HOST }}" | tr -d '\n')" >> $GITHUB_ENV

      - name: Set up Firestore Emulator configuration
        run: |
          echo '{
            "emulators": {
              "firestore": {
                "host": "127.0.0.1",
                "port": 8082
              }
            }
          }' > firebase.json

      - name: Start Firestore Emulator
        run: |
          npm install -g firebase-tools
          firebase setup:emulators:firestore
          firebase emulators:start --only firestore &

      - name: Check any differences from the base in the target ts files
        run: |
          if git diff --name-only origin/${{ github.base_ref }}...HEAD | grep -q '^monolithic-service/server/src/.*\.ts$'; then
            echo "No change occurred"
            exit 0
          else
            echo "Some changes have been detected and we will start measuring test coverage"
          fi

      - name: Measure test coverage against change differences
        id: coverage-report
        run: |
          mkdir coverage
          cd ./monolithic-service/server
          npm ci
          npx jest --collectCoverage=true --coverageReporters=json

      - name: Test Report
        uses: dorny/test-reporter@v1
        if: always()
        with:
          name: "monolithic server test"
          path: "api/monolithic-service/server/junit.xml"
          reporter: "jest-junit"
          fail-on-error: "false"