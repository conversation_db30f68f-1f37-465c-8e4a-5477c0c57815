services:
  # monolithic-service-api-spec:
  #   build:
  #     context: ./api/monolithic-service/openapi
  #     dockerfile: "Dockerfile.dev"
  #   command: |
  #     /bin/sh -c '
  #     cd /openapi
  #     nodemon
  #     '
  #   volumes:
  #     - type: bind
  #       source: ./api/monolithic-service/openapi
  #       target: /openapi
  #     - type: bind
  #       source: ./document/src/docs
  #       target: /data
  #   restart: always

  monolithic-service-server:
    depends_on:
      - monolithic-service-db
      - firebase-firestore-emulator
    command: "npm run dev"
    build:
      context: ./api/monolithic-service/server
      dockerfile: "Dockerfile.dev"
    volumes:
      - ./api/monolithic-service/server:/app
      - /app/node_modules
    working_dir: /app
    ports:
      - "${MONOLITHIC_SERVICE_IP}:80:80"
    tty: true
    networks:
      - monolithic-service-nw
    restart: always
    env_file:
      - ./api/monolithic-service/server/.env

  monolithic-service-db:
    image: postgres:16
    environment:
      POSTGRES_USER: root
      POSTGRES_PASSWORD: secret
    ports:
      - "${MONOLITHIC_SERVICE_IP}:5432:5432"
    volumes:
      - ./api/monolithic-service/server/src/db/init/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - monolithic-service-nw
    restart: always

  firebase-firestore-emulator:
    build:
      context: ./api/monolithic-service/server
      dockerfile: "Dockerfile.firebase.dev"
    command: firebase emulators:start --project marbull-membership-dev
    volumes:
      - type: bind
        source: ./api/monolithic-service/server/src/firebase/emulator/emulator_data
        target: /app/emulator_data
      - type: bind
        source: ./api/monolithic-service/server/firebase.json
        target: /firebase.json
    ports:
      - "${MONOLITHIC_SERVICE_IP}:8082:8082"
      - "${MONOLITHIC_SERVICE_IP}:4000:4000"
    networks:
      - monolithic-service-nw
    restart: always

  redis_server:
    container_name: redis_server
    image: redis:latest
    ports:
      - "6379:6379"
    networks:
      - monolithic-service-nw

networks:
  monolithic-service-nw:
    driver: bridge
