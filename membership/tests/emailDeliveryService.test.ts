// import { EmailDeliveryService } from '../../src/services/emailDeliveryService';
// import { Resend } from 'resend';
// import { EmailTemplateType } from '../../src/constants/emailTemplates';
// import { languageCode } from '../../src/enum/languageCode';

// // Mock the Resend module
// jest.mock('resend', () => {
//   const mockSend = jest.fn();
//   return {
//     Resend: jest.fn().mockImplementation(() => {
//       return {
//         emails: {
//           send: mockSend
//         }
//       };
//     })
//   };
// });

// // Mock the logger
// jest.mock('../../src/utils/logger', () => ({
//   logger: {
//     info: jest.fn(),
//     error: jest.fn()
//   }
// }));

// describe('EmailDeliveryService', () => {
//   let emailDeliveryService: EmailDeliveryService;
//   let mockSend: jest.Mock;

//   beforeEach(() => {
//     jest.clearAllMocks();
//     // Get the mocked send function
//     mockSend = (new Resend('').emails.send as jest.Mock);
//     emailDeliveryService = new EmailDeliveryService();
//   });

//   describe('sendEmail', () => {
//     test('should return true when email is sent successfully', async () => {
//       // Setup
//       mockSend.mockResolvedValue({
//         data: { id: 'email-id' },
//         error: null
//       });

//       // Execute
//       const result = await emailDeliveryService.sendEmail('<EMAIL>', {
//         subject: 'Test Subject',
//         html: '<p>Test Content</p>'
//       });

//       // Verify
//       expect(result).toBe(true);
//       expect(mockSend).toHaveBeenCalledWith({
//         from: "Acme <<EMAIL>>",
//         to: ['<EMAIL>'],
//         subject: 'Test Subject',
//         html: '<p>Test Content</p>'
//       });
//     });

//     test('should return false when Resend returns an error', async () => {
//       // Setup
//       mockSend.mockResolvedValue({
//         data: null,
//         error: { message: 'Failed to send email' }
//       });

//       // Execute
//       const result = await emailDeliveryService.sendEmail('<EMAIL>', {
//         subject: 'Test Subject',
//         html: '<p>Test Content</p>'
//       });

//       // Verify
//       expect(result).toBe(false);
//     });

//     test('should return false when an exception occurs', async () => {
//       // Setup
//       mockSend.mockRejectedValue(new Error('Network error'));

//       // Execute
//       const result = await emailDeliveryService.sendEmail('<EMAIL>', {
//         subject: 'Test Subject',
//         html: '<p>Test Content</p>'
//       });

//       // Verify
//       expect(result).toBe(false);
//     });
//   });

//   describe('sendTemplatedEmail', () => {
//     test('should use the correct template for English language', async () => {
//       // Setup
//       mockSend.mockResolvedValue({
//         data: { id: 'email-id' },
//         error: null
//       });

//       // Execute
//       const result = await emailDeliveryService.sendTemplatedEmail(
//         '<EMAIL>',
//         EmailTemplateType.OTP_VERIFICATION,
//         languageCode.EN_US,
//         { otp: '123456' }
//       );

//       // Verify
//       expect(result).toBe(true);
//       expect(mockSend).toHaveBeenCalledWith({
//         from: "Acme <<EMAIL>>",
//         to: ['<EMAIL>'],
//         subject: 'Email Verification',
//         html: expect.stringContaining('123456')
//       });
//     });

//     test('should use the correct template for Japanese language', async () => {
//       // Setup
//       mockSend.mockResolvedValue({
//         data: { id: 'email-id' },
//         error: null
//       });

//       // Execute
//       const result = await emailDeliveryService.sendTemplatedEmail(
//         '<EMAIL>',
//         EmailTemplateType.OTP_VERIFICATION,
//         languageCode.JA,
//         { otp: '123456' }
//       );

//       // Verify
//       expect(result).toBe(true);
//       expect(mockSend).toHaveBeenCalledWith({
//         from: "Acme <<EMAIL>>",
//         to: ['<EMAIL>'],
//         subject: 'メール認証',
//         html: expect.stringContaining('123456')
//       });
//     });

//     test('should fall back to Japanese when language is not supported', async () => {
//       // Setup
//       mockSend.mockResolvedValue({
//         data: { id: 'email-id' },
//         error: null
//       });

//       // Execute
//       const result = await emailDeliveryService.sendTemplatedEmail(
//         '<EMAIL>',
//         EmailTemplateType.OTP_VERIFICATION,
//         'fr' as any, // Unsupported language
//         { otp: '123456' }
//       );

//       // Verify
//       expect(result).toBe(true);
//       expect(mockSend).toHaveBeenCalledWith({
//         from: "Acme <<EMAIL>>",
//         to: ['<EMAIL>'],
//         subject: 'メール認証', // Should fall back to Japanese
//         html: expect.stringContaining('123456')
//       });
//     });
//   });
// });
