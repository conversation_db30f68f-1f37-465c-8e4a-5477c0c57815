import { Project } from 'ts-morph';
import * as fs from 'fs';
import * as path from 'path';

const project = new Project({
  tsConfigFilePath: 'tsconfig.json',
});

const edges: { from: string; to: string }[] = [];

function getClassNameFromDecorator(decoratorArg: string): string {
  return decoratorArg.replace(/['"`]/g, '');
}

for (const sourceFile of project.getSourceFiles()) {
  for (const cls of sourceFile.getClasses()) {
    const fromClass = cls.getName();
    if (!fromClass) continue;

    const constructor = cls.getConstructors()[0];
    if (!constructor) continue;

    for (const param of constructor.getParameters()) {
      const injectDecorator = param.getDecorator('inject');
      if (injectDecorator) {
        const arg = injectDecorator.getArguments()[0]?.getText();
        if (arg) {
          const toClass = getClassNameFromDecorator(arg);
          edges.push({ from: fromClass, to: toClass });
        }
      }
    }
  }
}

// Mermaid erDiagram
const entities = new Set<string>();
edges.forEach(({ from, to }) => {
  entities.add(from);
  entities.add(to);
});

let mermaid = `erDiagram\n`;

// Define entities
entities.forEach((entity) => {
  mermaid += `  ${entity} {\n    string id\n  }\n`;
});

// Define relationships
edges.forEach(({ from, to }) => {
  mermaid += `  ${from} ||--o{ ${to} : injects\n`;
});

// Save to file
const outputDir = path.join(__dirname);
const outputPath = path.join(outputDir, 'di-er-graph.mmd');
fs.mkdirSync(outputDir, { recursive: true });
fs.writeFileSync(outputPath, mermaid);
console.log('Mermaid ER diagram written to scripts/di-er-graph.mmd');