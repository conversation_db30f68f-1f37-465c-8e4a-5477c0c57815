erDiagram
  TransactionComponent {
    string id
  }
  VaultKeyRepository {
    string id
  }
  AccountController {
    string id
  }
  AccountService {
    string id
  }
  ActionController {
    string id
  }
  ActionService {
    string id
  }
  AdminController {
    string id
  }
  AdminService {
    string id
  }
  AuthController {
    string id
  }
  AuthService {
    string id
  }
  ImageController {
    string id
  }
  ImageService {
    string id
  }
  NftsController {
    string id
  }
  NftsService {
    string id
  }
  MetadataService {
    string id
  }
  NftRegisterService {
    string id
  }
  TransactionService {
    string id
  }
  NftMintService {
    string id
  }
  NftTransactionUpdateService {
    string id
  }
  BulkMintService {
    string id
  }
  I18nService {
    string id
  }
  NotificationController {
    string id
  }
  NotificationService {
    string id
  }
  ProductsController {
    string id
  }
  ProductService {
    string id
  }
  QuestController {
    string id
  }
  QuestService {
    string id
  }
  QuestionnaireService {
    string id
  }
  QuestionnaireController {
    string id
  }
  RewardController {
    string id
  }
  RewardService {
    string id
  }
  SerialCodeController {
    string id
  }
  SerialCodeService {
    string id
  }
  ServiceInfoController {
    string id
  }
  ServiceInfoService {
    string id
  }
  TenantsController {
    string id
  }
  TenantsService {
    string id
  }
  UserController {
    string id
  }
  UserService {
    string id
  }
  UserOperationController {
    string id
  }
  UserOperationService {
    string id
  }
  VaultKeyController {
    string id
  }
  VaultKeyService {
    string id
  }
  WebhookController {
    string id
  }
  WebhookService {
    string id
  }
  DeliveryNftsFirestoreRepository {
    string id
  }
  FirebaseComponent {
    string id
  }
  NftsFirestoreRepository {
    string id
  }
  AccountRepository {
    string id
  }
  ActionRepository {
    string id
  }
  QuestRepository {
    string id
  }
  QuestActivityRepository {
    string id
  }
  ClaimedRewardRepository {
    string id
  }
  ServiceInfoRepository {
    string id
  }
  LineComponent {
    string id
  }
  NftContractsRepository {
    string id
  }
  TransactionQueuesRepository {
    string id
  }
  ActionActivityRepository {
    string id
  }
  AchievementActionRepository {
    string id
  }
  OnlineCheckinActionRepository {
    string id
  }
  QrCheckinActionRepository {
    string id
  }
  QuestionnaireActionRepository {
    string id
  }
  QuestionnaireRepository {
    string id
  }
  QuestionnaireQuestionRepository {
    string id
  }
  QuestionnaireQuestionAnswerRepository {
    string id
  }
  QuestionnaireResultAnswerRepository {
    string id
  }
  QuestionnaireResultRankRepository {
    string id
  }
  SerialCodeActionRepository {
    string id
  }
  MetadataFetchService {
    string id
  }
  AuthProviderRepository {
    string id
  }
  TransactionsRepository {
    string id
  }
  TokenBoundAccountImplementationRepository {
    string id
  }
  TokenBoundAccountRegistryAddressRepository {
    string id
  }
  NftBaseMetadatasRepository {
    string id
  }
  GptComponent {
    string id
  }
  RedisComponent {
    string id
  }
  CloudStorageComponent {
    string id
  }
  NftMetadatasRepository {
    string id
  }
  NftContractTypesRepository {
    string id
  }
  RewardRepository {
    string id
  }
  NotificationRepository {
    string id
  }
  CheckoutRepository {
    string id
  }
  ProductRepository {
    string id
  }
  CustomFieldsRepository {
    string id
  }
  QuestionnaireThemeRepository {
    string id
  }
  QuestActionRepository {
    string id
  }
  QuestRewardRepository {
    string id
  }
  CertificateRewardRepository {
    string id
  }
  CouponRewardRepository {
    string id
  }
  DigitalContentRewardRepository {
    string id
  }
  SerialCodeProjectsRepository {
    string id
  }
  SerialCodesRepository {
    string id
  }
  AccountSerialCodesRepository {
    string id
  }
  TenantRepository {
    string id
  }
  PlanRepository {
    string id
  }
  AttemptTransactionsRepository {
    string id
  }
  ViemComponent {
    string id
  }
  UserOperationQueuesRepository {
    string id
  }
  UserRepository {
    string id
  }
  AlchemyComponent {
    string id
  }
  TransactionComponent ||--o{ VaultKeyRepository : injects
  AccountController ||--o{ AccountService : injects
  ActionController ||--o{ ActionService : injects
  AdminController ||--o{ AdminService : injects
  AuthController ||--o{ AuthService : injects
  ImageController ||--o{ ImageService : injects
  NftsController ||--o{ NftsService : injects
  NftsController ||--o{ MetadataService : injects
  NftsController ||--o{ NftRegisterService : injects
  NftsController ||--o{ TransactionService : injects
  NftsController ||--o{ NftMintService : injects
  NftsController ||--o{ NftTransactionUpdateService : injects
  NftsController ||--o{ BulkMintService : injects
  NftsController ||--o{ I18nService : injects
  NotificationController ||--o{ NotificationService : injects
  ProductsController ||--o{ ProductService : injects
  QuestController ||--o{ QuestService : injects
  QuestController ||--o{ QuestionnaireService : injects
  QuestionnaireController ||--o{ QuestionnaireService : injects
  RewardController ||--o{ RewardService : injects
  SerialCodeController ||--o{ SerialCodeService : injects
  ServiceInfoController ||--o{ ServiceInfoService : injects
  TenantsController ||--o{ TenantsService : injects
  UserController ||--o{ UserService : injects
  UserOperationController ||--o{ UserOperationService : injects
  VaultKeyController ||--o{ VaultKeyService : injects
  WebhookController ||--o{ WebhookService : injects
  DeliveryNftsFirestoreRepository ||--o{ FirebaseComponent : injects
  NftsFirestoreRepository ||--o{ FirebaseComponent : injects
  AccountService ||--o{ AccountRepository : injects
  AccountService ||--o{ ActionRepository : injects
  AccountService ||--o{ QuestRepository : injects
  AccountService ||--o{ QuestActivityRepository : injects
  AccountService ||--o{ ClaimedRewardRepository : injects
  AccountService ||--o{ UserService : injects
  AccountService ||--o{ ServiceInfoRepository : injects
  AccountService ||--o{ LineComponent : injects
  AccountService ||--o{ FirebaseComponent : injects
  AccountService ||--o{ NftsService : injects
  AccountService ||--o{ NftContractsRepository : injects
  AccountService ||--o{ VaultKeyRepository : injects
  AccountService ||--o{ TransactionQueuesRepository : injects
  AccountService ||--o{ MetadataService : injects
  AccountService ||--o{ NotificationService : injects
  ActionService ||--o{ ActionRepository : injects
  ActionService ||--o{ ActionActivityRepository : injects
  ActionService ||--o{ AchievementActionRepository : injects
  ActionService ||--o{ OnlineCheckinActionRepository : injects
  ActionService ||--o{ QuestRepository : injects
  ActionService ||--o{ QuestActivityRepository : injects
  ActionService ||--o{ QrCheckinActionRepository : injects
  ActionService ||--o{ QuestionnaireActionRepository : injects
  ActionService ||--o{ QuestionnaireRepository : injects
  ActionService ||--o{ QuestionnaireQuestionRepository : injects
  ActionService ||--o{ QuestionnaireQuestionAnswerRepository : injects
  ActionService ||--o{ QuestionnaireResultAnswerRepository : injects
  ActionService ||--o{ QuestionnaireResultRankRepository : injects
  ActionService ||--o{ SerialCodeActionRepository : injects
  ActionService ||--o{ SerialCodeService : injects
  AdminService ||--o{ NftsFirestoreRepository : injects
  AdminService ||--o{ NftContractsRepository : injects
  AdminService ||--o{ MetadataFetchService : injects
  AuthService ||--o{ AuthProviderRepository : injects
  AuthService ||--o{ UserService : injects
  AuthService ||--o{ LineComponent : injects
  AuthService ||--o{ FirebaseComponent : injects
  AuthService ||--o{ ServiceInfoRepository : injects
  BulkMintService ||--o{ TransactionService : injects
  BulkMintService ||--o{ NftMintService : injects
  BulkMintService ||--o{ NftContractsRepository : injects
  BulkMintService ||--o{ TransactionQueuesRepository : injects
  BulkMintService ||--o{ TransactionsRepository : injects
  BulkMintService ||--o{ VaultKeyRepository : injects
  BulkMintService ||--o{ ServiceInfoRepository : injects
  BulkMintService ||--o{ TokenBoundAccountImplementationRepository : injects
  BulkMintService ||--o{ TokenBoundAccountRegistryAddressRepository : injects
  BulkMintService ||--o{ AccountRepository : injects
  BulkMintService ||--o{ DeliveryNftsFirestoreRepository : injects
  BulkMintService ||--o{ NftBaseMetadatasRepository : injects
  BulkMintService ||--o{ MetadataService : injects
  I18nService ||--o{ NftsFirestoreRepository : injects
  I18nService ||--o{ GptComponent : injects
  I18nService ||--o{ RedisComponent : injects
  ImageService ||--o{ CloudStorageComponent : injects
  MetadataFetchService ||--o{ TransactionComponent : injects
  MetadataService ||--o{ NftMetadatasRepository : injects
  MetadataService ||--o{ NftBaseMetadatasRepository : injects
  MetadataService ||--o{ NftContractTypesRepository : injects
  NftMintService ||--o{ NftContractsRepository : injects
  NftMintService ||--o{ TransactionQueuesRepository : injects
  NftMintService ||--o{ VaultKeyRepository : injects
  NftMintService ||--o{ ServiceInfoRepository : injects
  NftMintService ||--o{ DeliveryNftsFirestoreRepository : injects
  NftRegisterService ||--o{ TransactionService : injects
  NftRegisterService ||--o{ WebhookService : injects
  NftRegisterService ||--o{ NftContractTypesRepository : injects
  NftRegisterService ||--o{ NftContractsRepository : injects
  NftRegisterService ||--o{ VaultKeyRepository : injects
  NftRegisterService ||--o{ ServiceInfoRepository : injects
  NftRegisterService ||--o{ MetadataService : injects
  NftRegisterService ||--o{ NftBaseMetadatasRepository : injects
  NftRegisterService ||--o{ TransactionQueuesRepository : injects
  NftRegisterService ||--o{ TransactionsRepository : injects
  NftsService ||--o{ NftContractsRepository : injects
  NftsService ||--o{ AccountRepository : injects
  NftsService ||--o{ RewardRepository : injects
  NotificationService ||--o{ AccountRepository : injects
  NotificationService ||--o{ NotificationRepository : injects
  ProductService ||--o{ NftMintService : injects
  ProductService ||--o{ CheckoutRepository : injects
  ProductService ||--o{ NftContractsRepository : injects
  ProductService ||--o{ ServiceInfoRepository : injects
  ProductService ||--o{ AccountRepository : injects
  ProductService ||--o{ ProductRepository : injects
  ProductService ||--o{ CustomFieldsRepository : injects
  ProductService ||--o{ MetadataService : injects
  QuestionnaireService ||--o{ ServiceInfoRepository : injects
  QuestionnaireService ||--o{ QuestionnaireRepository : injects
  QuestionnaireService ||--o{ QuestionnaireThemeRepository : injects
  QuestionnaireService ||--o{ QuestionnaireResultAnswerRepository : injects
  QuestionnaireService ||--o{ QuestionnaireQuestionRepository : injects
  QuestService ||--o{ QuestRepository : injects
  QuestService ||--o{ ActionRepository : injects
  QuestService ||--o{ RewardRepository : injects
  QuestService ||--o{ QuestActionRepository : injects
  QuestService ||--o{ ActionActivityRepository : injects
  QuestService ||--o{ QuestRewardRepository : injects
  QuestService ||--o{ ClaimedRewardRepository : injects
  QuestService ||--o{ ServiceInfoRepository : injects
  QuestService ||--o{ NftContractTypesRepository : injects
  QuestService ||--o{ NftRegisterService : injects
  QuestService ||--o{ QrCheckinActionRepository : injects
  QuestService ||--o{ OnlineCheckinActionRepository : injects
  QuestService ||--o{ AchievementActionRepository : injects
  QuestService ||--o{ QuestionnaireActionRepository : injects
  QuestService ||--o{ SerialCodeActionRepository : injects
  QuestService ||--o{ QuestionnaireResultRankRepository : injects
  RewardService ||--o{ RewardRepository : injects
  RewardService ||--o{ AccountRepository : injects
  RewardService ||--o{ CertificateRewardRepository : injects
  RewardService ||--o{ CouponRewardRepository : injects
  RewardService ||--o{ DigitalContentRewardRepository : injects
  RewardService ||--o{ ClaimedRewardRepository : injects
  RewardService ||--o{ AchievementActionRepository : injects
  RewardService ||--o{ QuestActivityRepository : injects
  RewardService ||--o{ NftMintService : injects
  RewardService ||--o{ NftsService : injects
  RewardService ||--o{ MetadataService : injects
  RewardService ||--o{ QuestionnaireResultAnswerRepository : injects
  SerialCodeService ||--o{ SerialCodeProjectsRepository : injects
  SerialCodeService ||--o{ SerialCodesRepository : injects
  SerialCodeService ||--o{ AccountSerialCodesRepository : injects
  SerialCodeService ||--o{ RewardService : injects
  ServiceInfoService ||--o{ ServiceInfoRepository : injects
  ServiceInfoService ||--o{ TenantRepository : injects
  ServiceInfoService ||--o{ TokenBoundAccountRegistryAddressRepository : injects
  ServiceInfoService ||--o{ TokenBoundAccountImplementationRepository : injects
  TenantsService ||--o{ TenantRepository : injects
  TenantsService ||--o{ PlanRepository : injects
  TenantsService ||--o{ VaultKeyService : injects
  TransactionService ||--o{ TransactionComponent : injects
  TransactionService ||--o{ AttemptTransactionsRepository : injects
  TransactionService ||--o{ TransactionsRepository : injects
  TransactionService ||--o{ TransactionQueuesRepository : injects
  TransactionService ||--o{ ServiceInfoRepository : injects
  TransactionService ||--o{ NftContractsRepository : injects
  NftTransactionUpdateService ||--o{ AccountRepository : injects
  NftTransactionUpdateService ||--o{ NftsFirestoreRepository : injects
  NftTransactionUpdateService ||--o{ NftContractsRepository : injects
  NftTransactionUpdateService ||--o{ NftBaseMetadatasRepository : injects
  NftTransactionUpdateService ||--o{ DeliveryNftsFirestoreRepository : injects
  NftTransactionUpdateService ||--o{ ServiceInfoRepository : injects
  NftTransactionUpdateService ||--o{ ViemComponent : injects
  NftTransactionUpdateService ||--o{ TransactionComponent : injects
  NftTransactionUpdateService ||--o{ TransactionsRepository : injects
  NftTransactionUpdateService ||--o{ TransactionQueuesRepository : injects
  UserOperationService ||--o{ NftContractsRepository : injects
  UserOperationService ||--o{ AccountRepository : injects
  UserOperationService ||--o{ UserOperationQueuesRepository : injects
  UserOperationService ||--o{ TokenBoundAccountImplementationRepository : injects
  UserOperationService ||--o{ MetadataService : injects
  UserService ||--o{ UserRepository : injects
  UserService ||--o{ AccountRepository : injects
  VaultKeyService ||--o{ VaultKeyRepository : injects
  VaultKeyService ||--o{ WebhookService : injects
  WebhookService ||--o{ TransactionsRepository : injects
  WebhookService ||--o{ TransactionQueuesRepository : injects
  WebhookService ||--o{ TokenBoundAccountRegistryAddressRepository : injects
  WebhookService ||--o{ MetadataService : injects
  WebhookService ||--o{ NftTransactionUpdateService : injects
  WebhookService ||--o{ AlchemyComponent : injects
  WebhookService ||--o{ NftContractsRepository : injects
  WebhookService ||--o{ BulkMintService : injects
  WebhookService ||--o{ ServiceInfoRepository : injects
  WebhookService ||--o{ UserRepository : injects
