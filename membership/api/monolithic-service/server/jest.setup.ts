import 'reflect-metadata';
import './test/types/global.d';
import dotenv from 'dotenv';
import { createTestDb, migrateDb, resetDb } from './test/helpers/kysely-db';
import { Kysely } from 'kysely';
import { Database } from './src/db/database';

process.env.ALCHEMY_ADDRESS_ACTIVITY_WEBHOOK_ID = 'dummy-address-webhook-id';
process.env.ALCHEMY_NFT_ACTIVITY_WEBHOOK_ID = 'dummy-nft-webhook-id';
process.env.ALCHEMY_ADDRESS_ACTIVITY_WEBHOOK_SIGNING_KEY = 'dummy-signing-key';
process.env.ALCHEMY_NFT_ACTIVITY_WEBHOOK_SIGNING_KEY = 'dummy-nft-signing-key';
process.env.ALCHEMY_CHAIN_NAME = 'eth-mainnet';
process.env.ALCHEMY_API_KEY = 'dummy-api-key';
process.env.ALCHEMY_AUTH_TOKEN = 'dummy-auth-token';
process.env.GCP_PROJECT_ID = 'dummy-gcp-project';
process.env.GCS_BUCKET_NAME = 'dummy-gcs-bucket';
process.env.DB_DATABASE = 'test-monolithic-service';

dotenv.config();

// const skip = process.env.GITHUB_ACTIONS === 'true';

// // eslint-disable-next-line no-undef
// beforeAll(async () => {
//   if (!skip) {
//     global.testDb = await createTestDb();
//     await migrateDb(global.testDb);
//   }
// });

// // eslint-disable-next-line no-undef
// beforeEach(async () => {
//   if (!skip && global.testDb) {
//     await resetDb(global.testDb);
//     await migrateDb(global.testDb);
//   }
// });

// // eslint-disable-next-line no-undef
// afterAll(async () => {
//   if (!skip && global.testDb) {
//     await resetDb(global.testDb);
//     await global.testDb.destroy();
//   }
// });