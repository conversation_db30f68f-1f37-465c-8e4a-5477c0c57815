# Origin Validation Middleware

pathごとにアクセス元のoriginを検証し、特定のorigin以外からのアクセスを制限するmiddlewareです。

## 特徴

- **Path-based validation**: パスごとに異なるorigin制限を設定可能
- **ワイルドカード対応**: `*.domain.com` 形式でのサブドメイン許可
- **Strict/Loose mode**: 完全一致またはドメイン部分一致の選択可能
- **動的設定**: 実行時に設定の追加/更新/削除が可能
- **包括的ログ**: セキュリティ監査のための詳細なログ出力

## 設定方法

### 環境変数での設定

```bash
PATH_ORIGIN_CONFIG='[
  {
    "path": "/admin",
    "allowedOrigins": ["https://admin.marbull.com"],
    "strict": true
  },
  {
    "path": "/auth",
    "allowedOrigins": ["*.marbull.com"],
    "strict": false
  }
]'
```

### 設定項目

| 項目 | 型 | 必須 | 説明 |
|------|-----|------|------|
| `path` | string | ✓ | 保護するパス（例: `/admin`, `/webhook`） |
| `allowedOrigins` | string[] | ✓ | 許可するオリジンのリスト |
| `strict` | boolean |  | `true`: 完全一致, `false`: ドメイン部分一致（デフォルト: false） |

### Origin指定方法

#### 完全指定
```json
{
  "allowedOrigins": ["https://admin.marbull.com"]
}
```

#### ワイルドカード（サブドメイン許可）
```json
{
  "allowedOrigins": ["*.marbull.com"]
}
```
`app.marbull.com`, `api.marbull.com` なども許可されます。

## API管理エンドポイント

### 現在の設定を取得
```bash
GET /admin/origin/origin-config
Authorization: Basic <credentials>
```

### 設定を追加/更新
```bash
POST /admin/origin/origin-config
Authorization: Basic <credentials>
Content-Type: application/json

{
  "path": "/new-endpoint",
  "allowedOrigins": ["https://trusted.domain.com"],
  "strict": true
}
```

### 設定を削除
```bash
DELETE /admin/origin/origin-config/%2Fadmin
Authorization: Basic <credentials>
```

注意: URLエンコーディングが必要（`/admin` → `%2Fadmin`）

## 動作例

### 1. Admin画面の保護
```json
{
  "path": "/admin",
  "allowedOrigins": [
    "https://admin.marbull.com",
    "https://admin-staging.marbull.com"
  ],
  "strict": true
}
```

### 2. API エンドポイントの保護
```json
{
  "path": "/webhook",
  "allowedOrigins": [
    "https://webhook.marbull.com",
    "https://api.external-service.com"
  ],
  "strict": true
}
```

### 3. ユーザー向けAPIの柔軟な設定
```json
{
  "path": "/users",
  "allowedOrigins": ["*.marbull.com"],
  "strict": false
}
```

## 検証ロジック

1. **パスマッチング**: リクエストパスが設定パスで始まるかチェック
2. **Origin取得**: `Origin` ヘッダーまたは `Referer` ヘッダーから取得
3. **Origin検証**: 設定された許可リストと照合
4. **結果処理**: 
   - ✅ 許可: 次のmiddlewareに進む
   - ❌ 拒否: 403 Forbiddenレスポンスを返却

## ログ出力

### 成功時
```json
{
  "level": "info",
  "message": "Origin validation passed",
  "requestPath": "/admin/users",
  "origin": "https://admin.marbull.com",
  "configPath": "/admin"
}
```

### 失敗時
```json
{
  "level": "warn",
  "message": "Origin validation failed",
  "requestPath": "/admin/users", 
  "origin": "https://malicious.com",
  "allowedOrigins": ["https://admin.marbull.com"],
  "userAgent": "Mozilla/5.0...",
  "ip": "*************"
}
```

## セキュリティ考慮事項

1. **Origin Spoofing**: サーバーサイドの検証のため、クライアントサイドの偽装は無効
2. **Referer Fallback**: Originヘッダーがない場合のフォールバック
3. **Strict Mode**: 重要なエンドポイントでは `strict: true` を推奨
4. **ログ監視**: 不正アクセス試行の早期検知のためログ監視を実装

## トラブルシューティング

### 403 Forbidden が返される場合

1. **Origin確認**: ブラウザの開発者ツールでリクエストヘッダーを確認
2. **設定確認**: `/admin/origin/origin-config` で現在の設定を確認
3. **ログ確認**: 詳細なエラー情報をログから取得

### 設定が反映されない場合

1. **JSON形式**: 環境変数の JSON が正しい形式か確認
2. **パス優先度**: より具体的（長い）パスが優先されることを確認
3. **再起動**: アプリケーションの再起動で設定を再読み込み

## 使用例

### 開発環境
```bash
PATH_ORIGIN_CONFIG='[
  {
    "path": "/admin",
    "allowedOrigins": ["http://localhost:3000", "https://dev.marbull.com"],
    "strict": false
  }
]'
```

### 本番環境
```bash
PATH_ORIGIN_CONFIG='[
  {
    "path": "/admin",
    "allowedOrigins": ["https://admin.marbull.com"],
    "strict": true
  },
  {
    "path": "/webhook",
    "allowedOrigins": ["https://secure-webhook.marbull.com"],
    "strict": true
  }
]'
```