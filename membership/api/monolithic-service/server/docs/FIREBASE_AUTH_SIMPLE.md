# Firebase Authentication Middleware (簡素版)

環境変数ベースでFirebase認証を行うmiddlewareです。設定変更APIは含まれていません。

## 設定方法

### 環境変数

```bash
# Firebase プロジェクト設定
FIREBASE_PROJECT_ID=your-project-id

# 認証設定（JSON形式）- オプション
FIREBASE_AUTH_CONFIG='[
  {
    "path": "/admin",
    "requiredRoles": ["admin"],
    "requireAccountId": false,
    "requireServiceId": false
  }
]'
```

### デフォルト設定

環境変数が設定されていない場合、以下のデフォルト設定が使用されます：

```json
[
  {
    "path": "/admin",
    "requiredRoles": ["admin", "super_admin"],
    "allowedEmails": ["<EMAIL>"],
    "requireAccountId": false,
    "requireServiceId": false
  },
  {
    "path": "/users",
    "requiredClaims": { "email_verified": true },
    "requireAccountId": true,
    "requireServiceId": true
  },
  {
    "path": "/accounts",
    "requiredClaims": { "email_verified": true },
    "requireAccountId": true,
    "requireServiceId": true
  },
  {
    "path": "/services",
    "requiredClaims": { "email_verified": true },
    "requireAccountId": false,
    "requireServiceId": true
  },
  {
    "path": "/nfts",
    "requiredClaims": { "email_verified": true },
    "requireAccountId": true,
    "requireServiceId": true
  },
  {
    "path": "/vaults",
    "requiredRoles": ["admin", "vault_manager"],
    "requireAccountId": false,
    "requireServiceId": true
  },
  {
    "path": "/webhook",
    "optional": true,
    "requireAccountId": false,
    "requireServiceId": false
  }
]
```

## Claims構造

```json
{
  "uid": "firebase-user-id",
  "email": "<EMAIL>",
  "email_verified": true,
  "role": "user",
  "access": {
    "account_id": "account-123",
    "service_id": "service-456"
  }
}
```

## 使用方法

### ユーザー情報取得

```typescript
import { FirebaseAuthMiddleware } from '../utils/middleware/firebaseAuthMiddleware';

// ユーザー情報取得
const user = FirebaseAuthMiddleware.getUser(c);

if (user) {
  console.log('User ID:', user.uid);
  console.log('Account ID:', user.account_id);
  console.log('Service ID:', user.service_id);
}

// ロール確認
const isAdmin = FirebaseAuthMiddleware.hasRole(c, 'admin');
const hasPermission = FirebaseAuthMiddleware.hasAnyRole(c, ['admin', 'manager']);
```

## エラーレスポンス

### 401 Unauthorized
```json
{
  "status": 401,
  "code": "MISSING_AUTH_TOKEN",
  "message": "Authorization token is required"
}
```

### 403 Forbidden
```json
{
  "status": 403,
  "code": "INVALID_ACCESS_CLAIMS",
  "message": "Access denied: invalid access claims",
  "reason": "Account account-123 does not belong to service service-456"
}
```

## セキュリティ機能

- **Token検証**: Firebase Admin SDKによる安全な検証
- **Claims検証**: カスタムクレーム、ロール、メールの検証
- **Database検証**: account_idとservice_idのデータベース存在確認
- **関連性チェック**: アカウントとサービスの関連性検証
- **詳細ログ**: セキュリティ監査のための詳細ログ