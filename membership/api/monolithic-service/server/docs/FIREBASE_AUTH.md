# Firebase Authentication Middleware

FirebaseのID Tokenベースでアクセス権限を検証するmiddlewareです。pathごとに異なる認証・認可ルールを設定できます。

## 特徴

- **Firebase ID Token検証**: Firebase Adminを使用した安全なトークン検証
- **Path-based Authorization**: パスごとに異なる認可ルール
- **Claims Validation**: カスタムクレームの検証
- **Database Integration**: account_idとservice_idのデータベース検証
- **Role-based Access**: ロールベースのアクセス制御
- **Email Restriction**: 特定のメールアドレスのみ許可

## 設定方法

### 環境変数

```bash
# Firebase プロジェクト設定
FIREBASE_PROJECT_ID=your-project-id

# 認証設定（JSON形式）
FIREBASE_AUTH_CONFIG='[
  {
    "path": "/admin",
    "requiredRoles": ["admin"],
    "requireAccountId": false,
    "requireServiceId": false
  }
]'
```

### Firebase Admin初期化

Firebase Admin SDKは自動的に初期化されます：
- Google Cloud環境では Application Default Credentials を使用
- ローカル開発では `GOOGLE_APPLICATION_CREDENTIALS` 環境変数で指定

## 設定項目

| 項目 | 型 | 必須 | 説明 |
|------|-----|------|------|
| `path` | string | ✓ | 保護するパス（例: `/admin`, `/users`） |
| `requiredClaims` | object |  | 必須のカスタムクレーム |
| `requiredRoles` | string[] |  | 必要なロール（OR条件） |
| `allowedEmails` | string[] |  | 許可するメールアドレス |
| `customValidation` | function |  | カスタム検証関数 |
| `optional` | boolean |  | トークン無しでも通すか |
| `requireAccountId` | boolean |  | account_idの検証が必要か |
| `requireServiceId` | boolean |  | service_idの検証が必要か（デフォルト: true） |

## Claims構造

Firebase ID Tokenには以下のクレーム構造が期待されます：

```json
{
  "uid": "firebase-user-id",
  "email": "<EMAIL>",
  "email_verified": true,
  "role": "user",
  "access": {
    "account_id": "account-123",
    "service_id": "service-456"
  }
}
```

## 検証フロー

### 1. Token検証
- Authorization ヘッダーからトークン抽出
- Firebase Admin SDKでトークン検証
- 有効期限とイシュアーの確認

### 2. Claims検証
- `requiredClaims`で指定されたクレームの値確認
- `requiredRoles`に含まれるロールの確認
- `allowedEmails`に含まれるメールアドレスの確認

### 3. Access Claims検証
- `claims.access.service_id`の存在と形式確認
- データベースでサービスの存在確認
- `claims.access.account_id`の存在と形式確認（必要な場合）
- データベースでアカウントの存在確認
- アカウントとサービスの関連性確認

### 4. Context設定
認証成功時、以下の情報をコンテキストに設定：

```typescript
c.set('user', {
  uid: string,
  email: string,
  emailVerified: boolean,
  account_id?: string,
  service_id?: string,
  role?: string,
  claims: DecodedIdToken
});
```

## Path別デフォルト設定

### アカウント必須パス
- `/accounts` - アカウント管理
- `/users` - ユーザー操作
- `/nfts` - NFT操作

### アカウント不要パス
- `/admin` - 管理機能
- `/services` - サービス管理
- `/vaults` - Vault管理
- `/webhook` - Webhook受信
- `/auth` - 認証関連

## 使用例

### 1. 管理者のみアクセス可能
```json
{
  "path": "/admin",
  "requiredRoles": ["admin", "super_admin"],
  "requireAccountId": false,
  "requireServiceId": false
}
```

### 2. 認証済みユーザーのアカウント操作
```json
{
  "path": "/accounts",
  "requiredClaims": { "email_verified": true },
  "requireAccountId": true,
  "requireServiceId": true
}
```

### 3. 特定のメールドメインのみ許可
```json
{
  "path": "/internal",
  "allowedEmails": ["<EMAIL>", "<EMAIL>"],
  "requireAccountId": false,
  "requireServiceId": true
}
```

### 4. Webhook（トークン不要）
```json
{
  "path": "/webhook",
  "optional": true,
  "requireAccountId": false,
  "requireServiceId": false
}
```

## トークン形式

### Authorization Header
```
Authorization: Bearer <firebase-id-token>
Authorization: Firebase <firebase-id-token>
```

### 直接指定
```
Authorization: <firebase-id-token>
```

## エラーレスポンス

### 401 Unauthorized
```json
{
  "status": 401,
  "code": "MISSING_AUTH_TOKEN",
  "message": "Authorization token is required"
}
```

```json
{
  "status": 401,
  "code": "INVALID_AUTH_TOKEN", 
  "message": "Invalid authorization token"
}
```

### 403 Forbidden
```json
{
  "status": 403,
  "code": "INSUFFICIENT_PERMISSIONS",
  "message": "Access denied: insufficient permissions",
  "reason": "User role 'user' is not in required roles: admin, super_admin"
}
```

```json
{
  "status": 403,
  "code": "INVALID_ACCESS_CLAIMS",
  "message": "Access denied: invalid access claims", 
  "reason": "Account account-123 does not belong to service service-456"
}
```

## ヘルパーメソッド

### ユーザー情報取得
```typescript
import { FirebaseAuthMiddleware } from './utils/middleware/firebaseAuthMiddleware';

// ユーザー情報取得
const user = FirebaseAuthMiddleware.getUser(c);

// ロール確認
const isAdmin = FirebaseAuthMiddleware.hasRole(c, 'admin');

// 複数ロール確認
const hasPermission = FirebaseAuthMiddleware.hasAnyRole(c, ['admin', 'manager']);
```

## 管理API

### 現在の設定取得
```bash
GET /admin/firebase-auth/firebase-auth-config
Authorization: Basic <admin-credentials>
```

### 設定追加/更新
```bash
POST /admin/firebase-auth/firebase-auth-config
Authorization: Basic <admin-credentials>
Content-Type: application/json

{
  "path": "/new-endpoint",
  "requiredRoles": ["admin"],
  "requireAccountId": true,
  "requireServiceId": true
}
```

### 設定削除
```bash
DELETE /admin/firebase-auth/firebase-auth-config/%2Fnew-endpoint
Authorization: Basic <admin-credentials>
```

### 現在のユーザー情報
```bash
GET /admin/firebase-auth/me
Authorization: Bearer <firebase-id-token>
```

## セキュリティ考慮事項

### 1. Token Security
- Firebase ID Tokenは短期間で期限切れ（1時間）
- トークンのクライアントサイド保存は避ける
- HTTPS必須

### 2. Claims Security
- カスタムクレームはFirebase Admin SDKでのみ設定可能
- クライアントサイドでの変更は不可能

### 3. Database Validation
- アカウントとサービスの存在確認
- 関連性の検証で不正なアクセスを防止

### 4. Logging
- 認証失敗の詳細ログ
- IP アドレスとUser-Agentの記録
- セキュリティ監査トレイル

## トラブルシューティング

### Token検証エラー
1. **Invalid Token**: トークンの期限切れまたは改ざん
2. **Project Mismatch**: 異なるFirebaseプロジェクトのトークン
3. **Missing Claims**: 必要なカスタムクレームが未設定

### Claims検証エラー
1. **Missing Access**: `claims.access`が未設定
2. **Invalid IDs**: account_idまたはservice_idの形式エラー
3. **Not Found**: データベースにアカウントまたはサービスが存在しない
4. **Relation Error**: アカウントとサービスの関連性エラー

### 設定エラー
1. **Invalid JSON**: `FIREBASE_AUTH_CONFIG`の形式エラー
2. **Missing Config**: パスに対応する設定が見つからない
3. **Circular Dependency**: カスタム検証関数でのエラー

## 開発時の注意点

### Local Development
```bash
# Firebase Admin SDK用の認証情報
export GOOGLE_APPLICATION_CREDENTIALS="/path/to/service-account-key.json"

# プロジェクトID
export FIREBASE_PROJECT_ID="your-dev-project"
```

### Testing
```typescript
// テスト用のmock
jest.mock('./utils/middleware/firebaseAuthMiddleware', () => ({
  firebaseAuthMiddleware: {
    middleware: (c, next) => {
      c.set('user', { uid: 'test-user', account_id: 'test-account' });
      return next();
    }
  }
}));
```