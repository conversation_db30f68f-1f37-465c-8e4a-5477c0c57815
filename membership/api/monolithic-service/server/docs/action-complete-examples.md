# Action Complete API Examples

This document provides examples of how to use the Action Complete API for different action types, including the new Location Check-in functionality.

## API Endpoint
```
POST /api/v1/accounts/{accountId}/actions/{actionId}/complete
```

## Request Headers
```
Content-Type: application/json
service-id-header: your-service-id
```

## Action Types and Required Data

### 1. QR Check-in Action
```json
{
  "actionType": "QR-CHECKIN",
  "qrVerificationData": "action-qr-test-id"
}
```

### 2. Serial Code Action
```json
{
  "actionType": "SERIAL-CODE",
  "serialCodeData": "A23B-C456-DEF7-89GH",
  "serialCodeProjectId": "8552df83-cbd5-44db-b67e-0cbeb2785918"
}
```

### 3. Questionnaire Action
```json
{
  "actionType": "QUESTIONNAIRE",
  "answerData": "eyJxdWVzdGlvbnMiOlt7ImlkIjoiMSIsImFuc3dlciI6IkEifV19"
}
```

### 4. Location Check-in Action (NEW)
```json
{
  "actionType": "LOCATION-CHECKIN",
  "locationData": {
    "latitude": 35.6812,
    "longitude": 139.7671,
    "accuracy": 5.0,
    "timestamp": "2023-01-01T12:00:00Z"
  }
}
```

## Location Check-in Details

### Required Fields
- **`latitude`**: Current latitude coordinate (-90 to 90)
- **`longitude`**: Current longitude coordinate (-180 to 180)

### Optional Fields
- **`accuracy`**: GPS accuracy in meters (helps with validation)
- **`timestamp`**: When location was captured (defaults to current time)

### Example Scenarios

#### Successful Check-in (Within Geofence)
**Request:**
```json
{
  "actionType": "LOCATION-CHECKIN",
  "locationData": {
    "latitude": 35.6812,
    "longitude": 139.7671,
    "accuracy": 3.5
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "Action completed successfully",
  "completedAt": "2023-01-01T12:00:00Z",
  "locationResult": {
    "withinGeofence": true,
    "distance": 45.5,
    "geofenceType": "CIRCLE",
    "geofenceName": "Tokyo Station"
  }
}
```

#### Failed Check-in (Outside Geofence)
**Request:**
```json
{
  "actionType": "LOCATION-CHECKIN",
  "locationData": {
    "latitude": 35.6900,
    "longitude": 139.7671,
    "accuracy": 4.2
  }
}
```

**Response:**
```json
{
  "success": false,
  "message": "Location check-in failed: You are 150m away from Tokyo Station (100m radius required)",
  "locationResult": {
    "withinGeofence": false,
    "distance": 150.0,
    "geofenceType": "CIRCLE",
    "geofenceName": "Tokyo Station"
  }
}
```

### Geofence Types Supported

#### Circle Geofence
- **Center Point**: Latitude/longitude coordinates
- **Radius**: Distance in meters from center
- **Validation**: User must be within the radius

#### Polygon Geofence
- **Boundary**: Array of coordinate points forming a polygon
- **Validation**: User must be inside the polygon area
- **Distance**: Calculated to nearest edge of polygon

### Error Handling

#### Missing Location Data
```json
{
  "error": "ValidationError",
  "message": "`locationData` is required when actionType is LOCATION-CHECKIN",
  "path": ["locationData"]
}
```

#### Invalid Coordinates
```json
{
  "error": "ValidationError",
  "message": "Latitude must be between -90 and 90",
  "path": ["locationData", "latitude"]
}
```

#### Action Not Found
```json
{
  "error": "NotFoundError",
  "message": "Action not found or not a location check-in action"
}
```

#### Already Completed
```json
{
  "error": "ValidationError",
  "message": "Action has already been completed",
  "completedAt": "2023-01-01T10:30:00Z"
}
```

## Integration Notes

### Frontend Implementation
1. **Get User Location**: Use `navigator.geolocation.getCurrentPosition()`
2. **Include Accuracy**: Pass GPS accuracy for better validation
3. **Handle Errors**: Provide clear feedback for location failures
4. **Rate Limiting**: Respect minimum 2-second intervals between attempts

### Backend Processing
1. **Validation**: Verify account, action, and location data
2. **Geofence Check**: Use PostGIS spatial functions for accuracy
3. **Attempt Logging**: Record all attempts for analytics
4. **Action Completion**: Mark action as complete if successful

### Security Considerations
- **Location Spoofing**: Consider GPS accuracy in validation
- **Rate Limiting**: Prevent rapid-fire location attempts
- **Audit Trail**: Log all location attempts for security review
- **Privacy**: Handle location data according to privacy policies
