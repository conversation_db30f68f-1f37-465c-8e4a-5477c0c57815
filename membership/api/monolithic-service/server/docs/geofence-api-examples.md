# Geofence API Examples

This document provides comprehensive examples for using the Geofence Management API.

## API Endpoints

- `POST /services/geofences` - Create a new geofence
- `PUT /services/geofences/{geofenceId}` - Update an existing geofence
- `GET /services/geofences` - Get all geofences for a service
- `GET /services/geofences/{geofenceId}` - Get a specific geofence
- `DELETE /services/geofences/{geofenceId}` - Delete a geofence

## Request Headers

All requests require the following header:
```
service-id-header: your-service-id
Content-Type: application/json
```

## Examples

### 1. Create Circle Geofence

**Request:**
```http
POST /services/geofences
Content-Type: application/json
service-id-header: service-123

{
  "name": "Tokyo Station Circle",
  "geofence": {
    "geofenceType": "CIRCLE",
    "center": {
      "latitude": 35.6812,
      "longitude": 139.7671
    },
    "radiusMeters": 100
  }
}
```

**Response:**
```json
{
  "geofenceId": "550e8400-e29b-41d4-a716-446655440000",
  "name": "Tokyo Station Circle",
  "geofence": {
    "geofenceType": "CIRCLE",
    "center": {
      "latitude": 35.6812,
      "longitude": 139.7671
    },
    "radiusMeters": 100
  }
}
```

### 2. Create Polygon Geofence

**Request:**
```http
POST /services/geofences
Content-Type: application/json
service-id-header: service-123

{
  "name": "Tokyo Station Building",
  "geofence": {
    "geofenceType": "POLYGON",
    "coordinates": [
      { "latitude": 35.6812, "longitude": 139.7671 },
      { "latitude": 35.6812, "longitude": 139.7681 },
      { "latitude": 35.6822, "longitude": 139.7681 },
      { "latitude": 35.6822, "longitude": 139.7671 },
      { "latitude": 35.6812, "longitude": 139.7671 }
    ]
  }
}
```

**Response:**
```json
{
  "geofenceId": "550e8400-e29b-41d4-a716-446655440001",
  "name": "Tokyo Station Building",
  "geofence": {
    "geofenceType": "POLYGON",
    "coordinates": [
      { "latitude": 35.6812, "longitude": 139.7671 },
      { "latitude": 35.6812, "longitude": 139.7681 },
      { "latitude": 35.6822, "longitude": 139.7681 },
      { "latitude": 35.6822, "longitude": 139.7671 },
      { "latitude": 35.6812, "longitude": 139.7671 }
    ]
  }
}
```

### 3. Update Geofence

**Request:**
```http
PUT /services/geofences/550e8400-e29b-41d4-a716-446655440000
Content-Type: application/json
service-id-header: service-123

{
  "name": "Updated Tokyo Station Circle",
  "geofence": {
    "geofenceType": "CIRCLE",
    "center": {
      "latitude": 35.6812,
      "longitude": 139.7671
    },
    "radiusMeters": 150
  }
}
```

**Response:**
```json
{
  "geofenceId": "550e8400-e29b-41d4-a716-446655440000",
  "name": "Updated Tokyo Station Circle",
  "geofence": {
    "geofenceType": "CIRCLE",
    "center": {
      "latitude": 35.6812,
      "longitude": 139.7671
    },
    "radiusMeters": 150
  }
}
```

### 4. Get All Geofences

**Request:**
```http
GET /services/geofences
service-id-header: service-123
```

**Response:**
```json
{
  "geofences": [
    {
      "geofenceId": "550e8400-e29b-41d4-a716-446655440000",
      "name": "Updated Tokyo Station Circle",
      "geofence": {
        "geofenceType": "CIRCLE",
        "center": {
          "latitude": 35.6812,
          "longitude": 139.7671
        },
        "radiusMeters": 150
      }
    },
    {
      "geofenceId": "550e8400-e29b-41d4-a716-446655440001",
      "name": "Tokyo Station Building",
      "geofence": {
        "geofenceType": "POLYGON",
        "coordinates": [
          { "latitude": 35.6812, "longitude": 139.7671 },
          { "latitude": 35.6812, "longitude": 139.7681 },
          { "latitude": 35.6822, "longitude": 139.7681 },
          { "latitude": 35.6822, "longitude": 139.7671 },
          { "latitude": 35.6812, "longitude": 139.7671 }
        ]
      }
    }
  ]
}
```

### 5. Get All Geofences with Filters

**Request:**
```http
GET /services/geofences?geofenceType=CIRCLE&search=Tokyo&page=1&limit=10
service-id-header: service-123
```

**Response:**
```json
{
  "geofences": [
    {
      "geofenceId": "550e8400-e29b-41d4-a716-446655440000",
      "name": "Updated Tokyo Station Circle",
      "geofence": {
        "geofenceType": "CIRCLE",
        "center": {
          "latitude": 35.6812,
          "longitude": 139.7671
        },
        "radiusMeters": 150
      }
    }
  ]
}
```

### 6. Get Specific Geofence

**Request:**
```http
GET /services/geofences/550e8400-e29b-41d4-a716-446655440000
service-id-header: service-123
```

**Response:**
```json
{
  "geofenceId": "550e8400-e29b-41d4-a716-446655440000",
  "name": "Updated Tokyo Station Circle",
  "geofence": {
    "geofenceType": "CIRCLE",
    "center": {
      "latitude": 35.6812,
      "longitude": 139.7671
    },
    "radiusMeters": 150
  }
}
```

### 7. Delete Geofence

**Request:**
```http
DELETE /services/geofences/550e8400-e29b-41d4-a716-446655440000
service-id-header: service-123
```

**Response:**
```
204 No Content
```

## Query Parameters

### GET /services/geofences

- `page` (optional): Page number for pagination (default: 1)
- `limit` (optional): Number of items per page (default: 20, max: 100)
- `geofenceType` (optional): Filter by type (`CIRCLE` or `POLYGON`)
- `search` (optional): Search by geofence name

## Error Responses

### 400 Bad Request - Invalid Data
```json
{
  "error": "ValidationError",
  "message": "Circle geofence requires center coordinates and radius"
}
```

### 401 Unauthorized
```json
{
  "error": "Unauthorized",
  "message": "Service ID header is required"
}
```

### 404 Not Found
```json
{
  "error": "NotFound",
  "message": "Geofence not found"
}
```

### 500 Internal Server Error
```json
{
  "error": "InternalServerError",
  "message": "Failed to create geofence"
}
```

## Validation Rules

### Circle Geofence
- `center` is required with valid latitude (-90 to 90) and longitude (-180 to 180)
- `radiusMeters` must be greater than 0

### Polygon Geofence
- `coordinates` must have at least 3 points
- First and last coordinates must be the same (closed polygon)
- Each coordinate must have valid latitude and longitude values

### General
- `name` is required and must be 1-255 characters
- `geofenceType` must be either "CIRCLE" or "POLYGON"

## Integration Notes

### Frontend Implementation
1. **Validation**: Validate coordinates and geometry before sending requests
2. **Error Handling**: Handle validation errors and provide user feedback
3. **Map Integration**: Use with mapping libraries like Google Maps or Mapbox
4. **Real-time Updates**: Consider WebSocket connections for real-time geofence updates

### Backend Integration
1. **Location Actions**: Use created geofences with location-checkin actions
2. **Caching**: Consider caching frequently accessed geofences
3. **Performance**: Use spatial indexes for efficient geofence queries
4. **Monitoring**: Log geofence operations for analytics and debugging
