{"name": "api_monolithic-service", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node ./dist/index.js", "migrate": "npx kysely migrate:latest", "rollback": "npx kysely migrate:down", "lint": "eslint .", "lint:fix": "eslint . --fix", "prettier": "prettier --ignore-path .gitignore --write \"./src/**/*.+(js|ts|json)\"", "prettier:fix": "prettier --write src", "test": "npx jest", "test_api": "dotenv -e .env npx cucumber-js features", "batch:metadata-sync": "node ./dist/index.js batch:metadata-sync", "batch:fix-and-sync-nft": "node ./dist/index.js batch:fix-and-sync-nft", "batch:retry-nft-confirm": "node ./dist/index.js batch:retry-nft-confirm", "dev:batch:metadata-sync": "tsx watch src/index.ts batch:metadata-sync", "dev:batch:fix-and-sync-nft": "tsx watch src/index.ts batch:fix-and-sync-nft", "dev:batch:retry-nft-confirm": "tsx watch src/index.ts batch:retry-nft-confirm", "seed": "tsx src/db/seed.ts", "unk": "tsx src/unk.ts"}, "dependencies": {"@cuonghx.gu-tech/ethers-gcp-kms-signer": "0.9.1", "@google-cloud/kms": "^4.5.0", "@google-cloud/storage": "7.15.0", "@google-cloud/tasks": "^6.0.1", "@hono/node-server": "1.13.7", "@hono/swagger-ui": "^0.5.1", "@hono/zod-openapi": "^0.19.2", "@hono/zod-validator": "0.4.2", "@types/markdown-it": "^14.1.2", "alchemy-sdk": "^3.5.3", "axios": "1.7.9", "ethers": "6.13.5", "firebase-admin": "12.7.0", "hono": "^4.7.9", "kysely": "0.27.5", "markdown-it": "^14.1.0", "moralis": "^2.27.2", "openai": "^4.100.0", "openapi3-ts": "^4.4.0", "pg": "8.13.1", "pino": "^9.6.0", "redis": "^5.0.1", "reflect-metadata": "0.2.2", "simple-spellchecker": "^1.0.2", "stripe": "^17.6.0", "textlint": "^14.4.2", "textlint-rule-preset-japanese": "^10.0.4", "textlint-rule-preset-jtf-style": "^3.0.1", "ts-pattern": "^5.7.1", "tsyringe": "4.8.0", "uuid": "11.0.5", "viem": "^2.22.16", "web3": "4.16.0", "zod": "3.24.1"}, "devDependencies": {"@cucumber/cucumber": "11.2.0", "@eslint/js": "^9.19.0", "@eslint/json": "^0.12.0", "@faker-js/faker": "^8.0.0", "@types/bunyan-format": "^0.2.9", "@types/jest": "^29.5.14", "@types/node": "22.12.0", "@types/pg": "8.11.11", "@types/uuid": "10.0.0", "eslint": "^9.26.0", "eslint-config-prettier": "10.0.1", "eslint-plugin-jest": "^28.11.0", "globals": "^16.1.0", "jest": "29.7.0", "jest-junit": "16.0.0", "kysely-ctl": "0.10.1", "prettier": "3.4.2", "ts-jest": "29.2.5", "tsx": "4.19.2", "typescript-eslint": "^8.32.1"}}