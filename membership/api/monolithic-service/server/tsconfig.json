{"compilerOptions": {"lib": ["ESNext"], "module": "commonjs", "moduleResolution": "node", "esModuleInterop": true, "skipLibCheck": true, "types": ["node", "jest"], "jsx": "react-jsx", "jsxImportSource": "hono/jsx", "experimentalDecorators": true, "emitDecoratorMetadata": true, "target": "ESNext", "rootDir": "./src", "outDir": "./dist", "forceConsistentCasingInFileNames": true, "strict": true}, "include": ["./src/**/*.ts"], "exclude": ["node_modules"]}