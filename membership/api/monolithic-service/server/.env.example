PORT=80
DB_HOST=127.0.0.10
DB_DATABASE=monothilic-service
DB_PORT=5432
DB_USER=migration
DB_PASSWORD=P@ssw0rd
CORS_DOMAINS='["https://liff-membership.ngrok.dev"]'

FIREBASE_PROJECT_ID=marbull-membership-dev
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_PRIVATE_KEY=
FIRESTORE_DB=env-dev
FIRESTORE_EMULATOR_HOST=0.0.0.0:8082  // Use it when using Firebase Emulator
LINE_CHANNEL_ID=
FIRESTORE_DB=dev-1
CHAIN_ID='80002'

GCP_PROJECT_ID=marbull-membership-dev

MORALIS_API_KEY=
JSON_RPC_URL=
MORALIS_MEMBERSHIP_STREAM_ID=membership
MORALIS_REWARD_STREAM_ID=reward
MORALIS_TBA_STREAM_ID=tba

ALCHEMY_API_KEY=
ALCHEMY_AUTH_TOKEN=
ALCHEMY_CHAIN_NAME=polygon-amoy
ALCHEMY_ADDRESS_ACTIVITY_WEBHOOK_ID=
ALCHEMY_ADDRESS_ACTIVITY_WEBHOOK_SIGNING_KEY=
ALCHEMY_NFT_ACTIVITY_WEBHOOK_ID=
ALCHEMY_NFT_ACTIVITY_WEBHOOK_SIGNING_KEY=

#gwei
BASE_MAX_FEE_PER_GAS=600
#percentage
GAS_LIMIT_MULTIPLIER=100
#percentage
MAX_PRIORITY_FEE_PER_GAS_MULTIPLIER=100

MIGRATION_DIR=src/db/dev_migrations

STRIPE_SECRET_KEY=
STRIPE_WEBHOOK_SECRET=

DOC_USERNAME=admin
DOC_PASSWORD=password

GCS_BUCKET_NAME=test3827638535785015575628

OPENAI_API_KEY=
REDIS_URL=redis://redis_server:6379
