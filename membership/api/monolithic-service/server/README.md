# User Application MS

### Development startup guide

#### Prerequisites
* `nvm` installed locally
* Node.js v22.8.0 is used in nvm
  * Node.js versions can be switched in nvm by executing `nvm use 22.8.0`
  * If v22.8.0 is not installed, install by executing `nvm install 22.8.0`
* Necessary dependencies are installed
  * Install necessary dependencies by executing `npm ci`
  * **WARNING**: Do **NOT** execute `npm install`, as it will cause the installed dependency tree to be non-deterministic, as well as causing package-lock.json to be modified unexpectedly
* Docker installed locally
  * Docker Desktop is recommended, and can be installed here: https://www.docker.com/products/docker-desktop/

#### DB startup and migration
1. Make sure that docker is ready to use. When using Docker Desktop, the Docker Desktop app should be open.
2. Start the DB container using the following command
```
$ docker compose -f ../../../docker-user-application-ms.dev.yml up -d user-application-db
```
3. Migrate DB schema using the following command
```
$ npm run migrate
```

#### API startup
1. Execute the following command to start up the Hono server
```
$ npm run dev
```
2. The API will be available in `http://localhost:82`

#### Linting and code formatting
1. Run the following command to lint your code
```
$ npm run lint
```
2. Run the following command to format your code
```
$ npm run prettier
```

#### Unit testing
1. Run the following command to run unit test cases
```
$ npm run test
```

#### Firebase Emulator setup
1. Run the following command to install firebase cli
```
$ npm install -g firebase-tools
```

2. Run the following command to start Firestore emulator
```
$ firebase emulators:start --only firestore
```