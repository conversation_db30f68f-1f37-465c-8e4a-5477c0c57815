-- Migration: Add geofence and location check-in functionality
-- Date: 2025-01-01

-- Create geofences table to store location boundaries
CREATE TABLE IF NOT EXISTS geofences (
    geofence_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    service_id VARCHAR(255) NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    latitude DECIMAL(10, 8) NOT NULL, -- Center latitude
    longitude DECIMAL(11, 8) NOT NULL, -- Center longitude
    radius_meters INTEGER NOT NULL, -- Radius in meters
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT geofences_service_id_fk FOREIGN KEY (service_id) REFERENCES services(service_id) ON DELETE CASCADE,
    CONSTRAINT geofences_latitude_check CHECK (latitude >= -90 AND latitude <= 90),
    CONSTRAINT geofences_longitude_check CHECK (longitude >= -180 AND longitude <= 180),
    CONSTRAINT geofences_radius_check CHECK (radius_meters > 0)
);

-- Create index for efficient geospatial queries
CREATE INDEX idx_geofences_service_id ON geofences(service_id);
CREATE INDEX idx_geofences_location ON geofences(latitude, longitude);
CREATE INDEX idx_geofences_active ON geofences(is_active);

-- Add location check-in action type to actions table
-- Assuming actions table already exists, we'll add a new action_type
INSERT INTO action_types (action_type, description) 
VALUES ('LOCATION_CHECKIN', 'Location check-in action requiring user to be within specified geofence')
ON CONFLICT (action_type) DO NOTHING;

-- Create location_checkin_actions table for location-specific action data
CREATE TABLE IF NOT EXISTS location_checkin_actions (
    location_action_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    action_id UUID NOT NULL,
    geofence_id UUID NOT NULL,
    required_duration_seconds INTEGER DEFAULT 0, -- Minimum time to stay in geofence
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT location_checkin_actions_action_id_fk FOREIGN KEY (action_id) REFERENCES actions(action_id) ON DELETE CASCADE,
    CONSTRAINT location_checkin_actions_geofence_id_fk FOREIGN KEY (geofence_id) REFERENCES geofences(geofence_id) ON DELETE CASCADE,
    CONSTRAINT location_checkin_actions_duration_check CHECK (required_duration_seconds >= 0)
);

-- Create indexes for location check-in actions
CREATE INDEX idx_location_checkin_actions_action_id ON location_checkin_actions(action_id);
CREATE INDEX idx_location_checkin_actions_geofence_id ON location_checkin_actions(geofence_id);

-- Create location_checkin_attempts table to track user location submissions
CREATE TABLE IF NOT EXISTS location_checkin_attempts (
    attempt_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id VARCHAR(255) NOT NULL,
    action_id UUID NOT NULL,
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    accuracy_meters DECIMAL(8, 2), -- GPS accuracy in meters
    is_successful BOOLEAN NOT NULL,
    distance_from_target DECIMAL(10, 2), -- Distance from geofence center in meters
    attempted_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT location_checkin_attempts_account_id_fk FOREIGN KEY (account_id) REFERENCES accounts(account_id) ON DELETE CASCADE,
    CONSTRAINT location_checkin_attempts_action_id_fk FOREIGN KEY (action_id) REFERENCES actions(action_id) ON DELETE CASCADE,
    CONSTRAINT location_checkin_attempts_latitude_check CHECK (latitude >= -90 AND latitude <= 90),
    CONSTRAINT location_checkin_attempts_longitude_check CHECK (longitude >= -180 AND longitude <= 180),
    CONSTRAINT location_checkin_attempts_accuracy_check CHECK (accuracy_meters >= 0)
);

-- Create indexes for location check-in attempts
CREATE INDEX idx_location_checkin_attempts_account_id ON location_checkin_attempts(account_id);
CREATE INDEX idx_location_checkin_attempts_action_id ON location_checkin_attempts(action_id);
CREATE INDEX idx_location_checkin_attempts_attempted_at ON location_checkin_attempts(attempted_at);

-- Add function to calculate distance between two points (Haversine formula)
CREATE OR REPLACE FUNCTION calculate_distance_meters(
    lat1 DECIMAL(10, 8),
    lon1 DECIMAL(11, 8),
    lat2 DECIMAL(10, 8),
    lon2 DECIMAL(11, 8)
) RETURNS DECIMAL(10, 2) AS $$
DECLARE
    earth_radius CONSTANT DECIMAL := 6371000; -- Earth radius in meters
    dlat DECIMAL;
    dlon DECIMAL;
    a DECIMAL;
    c DECIMAL;
BEGIN
    dlat := RADIANS(lat2 - lat1);
    dlon := RADIANS(lon2 - lon1);
    
    a := SIN(dlat/2) * SIN(dlat/2) + 
         COS(RADIANS(lat1)) * COS(RADIANS(lat2)) * 
         SIN(dlon/2) * SIN(dlon/2);
    
    c := 2 * ATAN2(SQRT(a), SQRT(1-a));
    
    RETURN earth_radius * c;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Add trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_geofences_updated_at 
    BEFORE UPDATE ON geofences 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_location_checkin_actions_updated_at 
    BEFORE UPDATE ON location_checkin_actions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
