import { WebhookType } from '../../src/enum/webhookType';
import { config } from '../../src/configs/config';
import { BigNumber } from '@ethersproject/bignumber';
import {
  WebhookRequest,
  AddressActivityWebhook,
  TransactionReceipt,
  NFTActivityWebhook,
} from '../../src/dtos/webhook/schemas';


export const mockNftDeployWebhookResponse: WebhookRequest = {
  webhookId: config.alchemyAddressWebhookId,
  id: 'mockId-1',
  createdAt: '2025-02-26T02:20:28.643Z',
  type: WebhookType.ADDRESS_ACTIVITY,
  event: {
    network: 'MATIC_AMOY',
    activity: [
      {
        fromAddress: '******************************************',
        blockNum: '0x11ac14b',
        hash: '0xaaa1d691b818e6d034627429aea47e4f62b9efa5982d350564f938c85041bba8',
        value: 0,
        asset: 'MATIC',
        category: 'external',
        rawContract: {
          rawValue: '0x0',
          decimals: 18,
        },
      },
    ],
    source: 'chainlake-kafka',
  },
};

export const mockTbaDeployWebhookResponse: WebhookRequest = {
  webhookId: config.alchemyAddressWebhookId,
  id: 'mockId-2',
  createdAt: '2025-02-26T02:40:22.657Z',
  type: WebhookType.ADDRESS_ACTIVITY,
  event: {
    network: 'MATIC_AMOY',
    activity: [
      {
        fromAddress: '******************************************',
        toAddress: '0x000000006551c19487814612e58fe06813775758',
        blockNum: '0x11ac37d',
        hash: '0x81c1fb4802335eda461891b2a18598f3a6050bc4df817ac46174743a67601738',
        value: 0,
        asset: 'MATIC',
        category: 'external',
        rawContract: {
          rawValue: '0x0',
          decimals: 18,
        },
      },
    ],
    source: 'chainlake-kafka',
  },
};

export const mockErc721MintAddressActivityWebhookResponse: AddressActivityWebhook = {
  webhookId: config.alchemyAddressWebhookId,
  id: 'mockId-3',
  createdAt: '2025-02-26T02:22:06.641Z',
  type: WebhookType.ADDRESS_ACTIVITY,
  event: {
    network: 'MATIC_AMOY',
    activity: [
      {
        fromAddress: '******************************************',
        toAddress: '0xe0650260c315c30b9c28305777ae500a0ade4b71',
        blockNum: '0x11ac179',
        hash: '0x96473e4fa0ff9162806ccb19a32b65c299b81b07dcaa2e65caf670840304e1b3',
        value: 0,
        asset: 'MATIC',
        category: 'external',
        rawContract: {
          rawValue: '0x0',
          decimals: 18,
        },
      },
    ],
    source: 'chainlake-kafka',
  },
};

export const mockErc1155MintAddressActivityWebhookResponse: AddressActivityWebhook = {
  webhookId: config.alchemyAddressWebhookId,
  id: 'mockId-3a',
  createdAt: '2025-02-27T03:18:02.532Z',
  type: WebhookType.ADDRESS_ACTIVITY,
  event: {
    network: 'MATIC_AMOY',
    activity: [
      {
        fromAddress: '******************************************',
        toAddress: '0x511b6abd1aa3ebc9d33f7efd282e5c5e109d620d',
        blockNum: '0x11b6452',
        hash: '0xcc848e5e748882ccac85715c8f618da4b97ad52b24d53d1567a30fe9016e4a80',
        value: 0,
        asset: 'MATIC',
        category: 'external',
        rawContract: {
          rawValue: '0x0',
          decimals: 18,
        },
      },
    ],
    source: 'chainlake-kafka',
  },
};

export const mockErc721TransferAddressActivityWebhookResponse: AddressActivityWebhook = {
  webhookId: config.alchemyAddressWebhookId,
  id: 'mockId-3b',
  createdAt: '2025-02-27T04:16:30.627Z',
  type: WebhookType.ADDRESS_ACTIVITY,
  event: {
    network: 'MATIC_AMOY',
    activity: [
      {
        fromAddress: '******************************************',
        toAddress: '0xe0650260c315c30b9c28305777ae500a0ade4b71',
        blockNum: '0x11b6ac5',
        hash: '0x05645ac51f704ca5e44276ba7fb946593de533b454d3d791624b56332738e888',
        value: 0,
        asset: 'MATIC',
        category: 'external',
        rawContract: {
          rawValue: '0x0',
          decimals: 18,
        },
      },
    ],
    source: 'chainlake-kafka',
  },
};

export const mockErc721MintNftActivityWebhookResponse: NFTActivityWebhook = {
  webhookId: config.alchemyNftWebhookId,
  id: 'mockId-4',
  createdAt: '2025-02-26T08:56:42.621Z',
  type: WebhookType.NFT_ACTIVITY,
  event: {
    network: 'MATIC_AMOY',
    activity: [
      {
        fromAddress: '0x0000000000000000000000000000000000000000',
        toAddress: '******************************************',
        contractAddress: '0xe0650260c315c30b9c28305777ae500a0ade4b71',
        blockNum: '0x11aecfe',
        hash: '0x045dbdaf741528016ff93123bdae7e3a49454087ae3d0e21f526a65db77032d0',
        erc721TokenId: '0x1b207',
        category: 'erc721',
        log: {
          blockHash: '0xc912fdbe43676d0003eae75f63098469d28f07a4c5d5a82b0a329feb9a91e5e5',
          address: '0xe0650260c315c30b9c28305777ae500a0ade4b71',
          logIndex: '0x5',
          data: '0x',
          removed: false,
          topics: [
            '0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef',
            '0x0000000000000000000000000000000000000000000000000000000000000000',
            '0x0000000000000000000000005b4b196f1538f05d472f5322d2702ff8aa0d5e4a',
            '0x000000000000000000000000000000000000000000000000000000000001b207',
          ],
          blockNumber: '0x11aecfe',
          transactionIndex: '0x3',
          transactionHash: '0x045dbdaf741528016ff93123bdae7e3a49454087ae3d0e21f526a65db77032d0',
        },
        value: 0,
      },
    ],
    source: 'chainlake-kafka',
  },
};

export const mockErc721TransferWebhookResponse: NFTActivityWebhook = {
  webhookId: config.alchemyNftWebhookId,
  id: 'mockId-5',
  createdAt: '2025-02-26T09:00:22.590Z',
  type: WebhookType.NFT_ACTIVITY,
  event: {
    network: 'MATIC_AMOY',
    activity: [
      {
        fromAddress: '******************************************',
        toAddress: '0x65150b5fa861481651225ef4412136dcbf696232',
        contractAddress: '0xe0650260c315c30b9c28305777ae500a0ade4b71',
        blockNum: '0x11aed65',
        hash: '0x2ae31e193e4c583a9781cb166c7b4e69d7a957f56d734ef8282c89ddf491cb78',
        erc721TokenId: '0x2b67',
        category: 'erc721',
        log: {
          blockHash: '0xdbeee4b6fd0a1bdefe98000c2e5e332473688bc5a297e6735a217dbd2a05815c',
          address: '0xe0650260c315c30b9c28305777ae500a0ade4b71',
          logIndex: '0xc',
          data: '0x',
          removed: false,
          topics: [
            '0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef',
            '0x0000000000000000000000005b4b196f1538f05d472f5322d2702ff8aa0d5e4a',
            '0x00000000000000000000000065150b5fa861481651225ef4412136dcbf696232',
            '0x0000000000000000000000000000000000000000000000000000000000002b67',
          ],
          blockNumber: '0x11aed65',
          transactionIndex: '0x7',
          transactionHash: '0x2ae31e193e4c583a9781cb166c7b4e69d7a957f56d734ef8282c89ddf491cb78',
        },
        value: 0,
      },
    ],
    source: 'chainlake-kafka',
  },
};

export const mockErc721BurnWebhookResponse: NFTActivityWebhook = {
  webhookId: config.alchemyNftWebhookId,
  id: 'mockId-6',
  createdAt: '2025-02-26T09:01:30.653Z',
  type: WebhookType.NFT_ACTIVITY,
  event: {
    network: 'MATIC_AMOY',
    activity: [
      {
        fromAddress: '******************************************',
        toAddress: '0x0000000000000000000000000000000000000000',
        contractAddress: '0xe0650260c315c30b9c28305777ae500a0ade4b71',
        blockNum: '0x11aed85',
        hash: '0x65cefb5f8df45e98bea860c31c4cfa5749aa425238c31a87bc957c79a19cf953',
        erc721TokenId: '0x1b207',
        category: 'erc721',
        log: {
          blockHash: '0xd9ef1d3731627b4d511ef24e54e2c762fc08ba0398717b2396d7a92063907073',
          address: '0xe0650260c315c30b9c28305777ae500a0ade4b71',
          logIndex: '0xe',
          data: '0x',
          removed: false,
          topics: [
            '0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef',
            '0x0000000000000000000000005b4b196f1538f05d472f5322d2702ff8aa0d5e4a',
            '0x0000000000000000000000000000000000000000000000000000000000000000',
            '0x000000000000000000000000000000000000000000000000000000000001b207',
          ],
          blockNumber: '0x11aed85',
          transactionIndex: '0x7',
          transactionHash: '0x65cefb5f8df45e98bea860c31c4cfa5749aa425238c31a87bc957c79a19cf953',
        },
        value: 0,
      },
    ],
    source: 'chainlake-kafka',
  },
};

export const mockErc1155MintWebhookResponse: NFTActivityWebhook = {
  webhookId: config.alchemyNftWebhookId,
  id: 'mockId-7',
  createdAt: '2025-02-26T09:26:44.742Z',
  type: WebhookType.NFT_ACTIVITY,
  event: {
    network: 'MATIC_AMOY',
    activity: [
      {
        fromAddress: '0x0000000000000000000000000000000000000000',
        toAddress: '******************************************',
        contractAddress: '0x72b5d4759b11ed58be62e717c3b9dfbafa408280',
        blockNum: '0x11af04e',
        hash: '0x317e0f34a408e1860f292302dbf196081ab743f4631f1d196608132765cfd5cb',
        erc1155Metadata: [
          {
            tokenId: '0x1b207',
            value: '0xa',
          },
        ],
        category: 'erc1155',
        log: {
          blockHash: '0xbce95f0495c8e5e66f3d0730407618e6f3d207c271365d3f4269ecd06cc73bf4',
          address: '0x72b5d4759b11ed58be62e717c3b9dfbafa408280',
          logIndex: '0x0',
          data: '0x000000000000000000000000000000000000000000000000000000000001b207000000000000000000000000000000000000000000000000000000000000000a',
          removed: false,
          topics: [
            '0xc3d58168c5ae7397731d063d5bbf3d657854427343f4c083240f7aacaa2d0f62',
            '0x0000000000000000000000005b4b196f1538f05d472f5322d2702ff8aa0d5e4a',
            '0x0000000000000000000000000000000000000000000000000000000000000000',
            '0x0000000000000000000000005b4b196f1538f05d472f5322d2702ff8aa0d5e4a',
          ],
          blockNumber: '0x11af04e',
          transactionIndex: '0x0',
          transactionHash: '0x317e0f34a408e1860f292302dbf196081ab743f4631f1d196608132765cfd5cb',
        },
        value: 0,
      },
    ],
    source: 'chainlake-kafka',
  },
};

export const mockErc1155TransferWebhookResponse: NFTActivityWebhook = {
  webhookId: config.alchemyNftWebhookId,
  id: 'mockId-8',
  createdAt: '2025-02-26T09:41:03.010Z',
  type: WebhookType.NFT_ACTIVITY,
  event: {
    network: 'MATIC_AMOY',
    activity: [
      {
        fromAddress: '******************************************',
        toAddress: '0x65150b5fa861481651225ef4412136dcbf696232',
        contractAddress: '0x72b5d4759b11ed58be62e717c3b9dfbafa408280',
        blockNum: '0x11af1e1',
        hash: '0x62508ebd3febfe89e482894a5aaea37da3020b938af28ffb8869b3bfe4cc1492',
        erc1155Metadata: [
          {
            tokenId: '0x1b207',
            value: '0x5',
          },
        ],
        category: 'erc1155',
        log: {
          blockHash: '0x6ec3a341a17241953858f936373505b0785535049e0d717e09d5523d520fa359',
          address: '0x72b5d4759b11ed58be62e717c3b9dfbafa408280',
          logIndex: '0x2',
          data: '0x000000000000000000000000000000000000000000000000000000000001b2070000000000000000000000000000000000000000000000000000000000000005',
          removed: false,
          topics: [
            '0xc3d58168c5ae7397731d063d5bbf3d657854427343f4c083240f7aacaa2d0f62',
            '0x0000000000000000000000005b4b196f1538f05d472f5322d2702ff8aa0d5e4a',
            '0x0000000000000000000000005b4b196f1538f05d472f5322d2702ff8aa0d5e4a',
            '0x00000000000000000000000065150b5fa861481651225ef4412136dcbf696232',
          ],
          blockNumber: '0x11af1e1',
          transactionIndex: '0x1',
          transactionHash: '0x62508ebd3febfe89e482894a5aaea37da3020b938af28ffb8869b3bfe4cc1492',
        },
        value: 0,
      },
    ],
    source: 'chainlake-kafka',
  },
};

export const mockErc1155BurnWebhookResponse: NFTActivityWebhook = {
  webhookId: config.alchemyNftWebhookId,
  id: 'mockId-9',
  createdAt: '2025-02-26T09:41:44.684Z',
  type: WebhookType.NFT_ACTIVITY,
  event: {
    network: 'MATIC_AMOY',
    activity: [
      {
        fromAddress: '******************************************',
        toAddress: '0x0000000000000000000000000000000000000000',
        contractAddress: '0x72b5d4759b11ed58be62e717c3b9dfbafa408280',
        blockNum: '0x11af1f5',
        hash: '0xd47a7613456b75a19791decc724f5ba5b0c6ba44090a7901eb122dc73717f926',
        erc1155Metadata: [
          {
            tokenId: '0x1b207',
            value: '0x2',
          },
        ],
        category: 'erc1155',
        log: {
          blockHash: '0x3252bb534531edda09dd8014c193ce8f249a0aba685f690bc270be01bdd4ab39',
          address: '0x72b5d4759b11ed58be62e717c3b9dfbafa408280',
          logIndex: '0x3',
          data: '0x000000000000000000000000000000000000000000000000000000000001b2070000000000000000000000000000000000000000000000000000000000000002',
          removed: false,
          topics: [
            '0xc3d58168c5ae7397731d063d5bbf3d657854427343f4c083240f7aacaa2d0f62',
            '0x0000000000000000000000005b4b196f1538f05d472f5322d2702ff8aa0d5e4a',
            '0x0000000000000000000000005b4b196f1538f05d472f5322d2702ff8aa0d5e4a',
            '0x0000000000000000000000000000000000000000000000000000000000000000',
          ],
          blockNumber: '0x11af1f5',
          transactionIndex: '0x1',
          transactionHash: '0xd47a7613456b75a19791decc724f5ba5b0c6ba44090a7901eb122dc73717f926',
        },
        value: 0,
      },
    ],
    source: 'chainlake-kafka',
  },
};

export const mockMintTxReceipt: TransactionReceipt = {
  to: "0xE0650260C315C30B9C28305777Ae500A0adE4B71",
  from: "0x5b4B196F1538f05D472f5322D2702ff8aA0D5e4a",
  contractAddress: null,
  transactionIndex: 0,
  gasUsed: BigNumber.from("0xccdb"),
  logsBloom:
    "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000008000000000000000000000008000000000000000000000000008000000800000000000000000000100080000000000000000020000000000000000000800000000000080000080000010000000020000000000000000000000000040000000000000000000000000000000008000200000000000000080000000000000000000000000000000000000000000004000000002000000000201000000000000000000000000000000100004000020000200000000000000000000000000000000000000000008001000000000100000",
  blockHash: "0x91b0c7767fffb10c168a44a1bd37d93887f523584adef52f30b839aff8d42e18",
  transactionHash: "0x96473e4fa0ff9162806ccb19a32b65c299b81b07dcaa2e65caf670840304e1b3",
  logs: [
    {
      transactionIndex: 0,
      blockNumber: 18530681,
      transactionHash: "0x96473e4fa0ff9162806ccb19a32b65c299b81b07dcaa2e65caf670840304e1b3",
      address: "0xE0650260C315C30B9C28305777Ae500A0adE4B71",
      topics: [
        "0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef",
        "0x0000000000000000000000000000000000000000000000000000000000000000",
        "0x0000000000000000000000005b4b196f1538f05d472f5322d2702ff8aa0d5e4a",
        "0x0000000000000000000000000000000000000000000000000000000000bc614e"
      ],
      data: "0x",
      logIndex: 0,
      blockHash: "0x91b0c7767fffb10c168a44a1bd37d93887f523584adef52f30b839aff8d42e18",
      removed: false,
    },
    {
      transactionIndex: 0,
      blockNumber: 18530681,
      transactionHash: "0x96473e4fa0ff9162806ccb19a32b65c299b81b07dcaa2e65caf670840304e1b3",
      address: "0x0000000000000000000000000000000000001010",
      topics: [
        "0x4dfe1bbbcf077ddc3e01291eea2d5c70c2b422b415d95645b9adcfd678cb1d63",
        "0x0000000000000000000000000000000000000000000000000000000000001010",
        "0x0000000000000000000000005b4b196f1538f05d472f5322d2702ff8aa0d5e4a",
        "0x0000000000000000000000006dc2dd54f24979ec26212794c71afefed722280c"
      ],
      data: "0x000000000000000000000000000000000000000000000000000607d4d57fe78c0000000000000000000000000000000000000000000000003d66868cb283ec2a000000000000000000000000000000000000000000000616fdd554d0ed2f818d0000000000000000000000000000000000000000000000003d607eb7dd04049e000000000000000000000000000000000000000000000616fddb5ca5c2af6919",
      logIndex: 1,
      blockHash: "0x91b0c7767fffb10c168a44a1bd37d93887f523584adef52f30b839aff8d42e18",
      removed: false,
    },
  ],
  blockNumber: 18530681,
  confirmations: 258656,
  cumulativeGasUsed: BigNumber.from("0xccdb"),
  effectiveGasPrice: BigNumber.from("0x0789444673"),
  status: 1,
  type: 2,
  byzantium: true,
};

export const mockErc1155MintTxReceipt: TransactionReceipt = {
  to: "0x511B6abD1AA3Ebc9d33f7eFD282E5C5E109d620D",
  from: "0x5b4B196F1538f05D472f5322D2702ff8aA0D5e4a",
  contractAddress: null,
  transactionIndex: 0,
  gasUsed: BigNumber.from("0xc3f7"),
  logsBloom:
    "0x00000000000000000000000000000000000000002000000000000000000000000000000000000000000100000000000000008000000000000000000040002000000000000000000000000000000000800000000000000000000100000000000000000000020000100000000000000800000000000000000080000000000000000000000000000000000000000000000000000000000000000000000000000000200000000000000080000000000000000000000000000000000000000000004000000000000000000001000040004000000000000008000000100004000020000000000000000000000000000000000000000000000008000000080000100000",
  blockHash: "0x8b3d5a8314eb9987a0144ba65b35ddb27354f25251b835aabc5fb2018a13ee7f",
  transactionHash: "0xcc848e5e748882ccac85715c8f618da4b97ad52b24d53d1567a30fe9016e4a80",
  logs: [
    {
      transactionIndex: 0,
      blockNumber: 18572370,
      transactionHash: "0xcc848e5e748882ccac85715c8f618da4b97ad52b24d53d1567a30fe9016e4a80",
      address: "0x511B6abD1AA3Ebc9d33f7eFD282E5C5E109d620D",
      topics: [
        "0xc3d58168c5ae7397731d063d5bbf3d657854427343f4c083240f7aacaa2d0f62",
        "0x0000000000000000000000005b4b196F1538f05D472f5322D2702ff8aA0D5e4a",
        "0x0000000000000000000000000000000000000000000000000000000000000000",
        "0x0000000000000000000000005b4b196F1538f05D472f5322D2702ff8aA0D5e4a"
      ],
      data: "0x000000000000000000000000000000000000000000000000000000000001b207000000000000000000000000000000000000000000000000000000000000000a",
      logIndex: 0,
      blockHash: "0x8b3d5a8314eb9987a0144ba65b35ddb27354f25251b835aabc5fb2018a13ee7f",
      removed: false,
    },
    {
      transactionIndex: 0,
      blockNumber: 18572370,
      transactionHash: "0xcc848e5e748882ccac85715c8f618da4b97ad52b24d53d1567a30fe9016e4a80",
      address: "0x0000000000000000000000000000000000001010",
      topics: [
        "0x4dfe1bbbcf077ddc3e01291eea2d5c70c2b422b415d95645b9adcfd678cb1d63",
        "0x0000000000000000000000000000000000000000000000000000000000001010",
        "0x0000000000000000000000005b4b196F1538f05D472f5322D2702ff8aA0D5e4a",
        "0x0000000000000000000000004631753190f2f5a15a7ba172bbac102b7d95fa22"
      ],
      data: "0x0000000000000000000000000000000000000000000000000006e77f375d47610000000000000000000000000000000000000000000000003b4a21551103d4100000000000000000000000000000000000000000000006dcc74fd764fd4cacd50000000000000000000000000000000000000000000000003b4339d5d9a68caf0000000000000000000000000000000000000000000006dcc756bee434a9f436",
      logIndex: 1,
      blockHash: "0x8b3d5a8314eb9987a0144ba65b35ddb27354f25251b835aabc5fb2018a13ee7f",
      removed: false,
    }
  ],
  blockNumber: 18572370,
  confirmations: 217018,
  cumulativeGasUsed: BigNumber.from("0xc3f7"),
  effectiveGasPrice: BigNumber.from("0x0904fb4976"),
  status: 1,
  type: 2,
  byzantium: true,
};

export const mockErc721TransferTxReceipt: TransactionReceipt = {
  to: "0xE0650260C315C30B9C28305777Ae500A0adE4B71",
  from: "0x5b4B196F1538f05D472f5322D2702ff8aA0D5e4a",
  contractAddress: null,
  transactionIndex: 1,
  gasUsed: BigNumber.from("0xa937"),
  logsBloom:
    "0x00000000080000000010000000000000000000000000000000000000000000000000000000000000000000000000000000008000100080000000000000000008000000080000000000000008000004800000020000000000000100000000000000000000000000000000000000000000000000000080000080000010000000000000000000000000000000000000000000000000000000000000000000000000200000000000000080000000000000000000000000000000000000000000004000000002000000000201000000000000000000010000000000100004000000000000000000000000000000000000000000000000000008001000000000100000",
  blockHash: "0xc0dc83b672d5f94cbb1534df3d538a0a70da464a7f34f390559942c55ba5dea9",
  transactionHash: "0x05645ac51f704ca5e44276ba7fb946593de533b454d3d791624b56332738e888",
  logs: [
    {
      transactionIndex: 1,
      blockNumber: 18574021,
      transactionHash: "0x05645ac51f704ca5e44276ba7fb946593de533b454d3d791624b56332738e888",
      address: "0xE0650260C315C30B9C28305777Ae500A0adE4B71",
      topics: [
        "0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef",
        "0x0000000000000000000000005b4b196f1538f05d472f5322d2702ff8aa0d5e4a",
        "0x00000000000000000000000065150b5fa861481651225ef4412136dcbf696232",
        "0x000000000000000000000000000000000000000000000000000000000000022b"
      ],
      data: "0x",
      logIndex: 1,
      blockHash: "0xc0dc83b672d5f94cbb1534df3d538a0a70da464a7f34f390559942c55ba5dea9",
      removed: false,
    },
    {
      transactionIndex: 1,
      blockNumber: 18574021,
      transactionHash: "0x05645ac51f704ca5e44276ba7fb946593de533b454d3d791624b56332738e888",
      address: "0x0000000000000000000000000000000000001010",
      topics: [
        "0x4dfe1bbbcf077ddc3e01291eea2d5c70c2b422b415d95645b9adcfd678cb1d63",
        "0x0000000000000000000000000000000000000000000000000000000000001010",
        "0x0000000000000000000000005b4b196f1538f05d472f5322d2702ff8aa0d5e4a",
        "0x0000000000000000000000006ab3d36c46ecfb9b9c0bd51cb1c3da5a2c81cea6"
      ],
      data: "0x00000000000000000000000000000000000000000000000000049df3b835f4000000000000000000000000000000000000000000000000003b3da3438ba6fd1500000000000000000000000000000000000000000000064c34a0bcd76c2d6f590000000000000000000000000000000000000000000000003b39054fd371091500000000000000000000000000000000000000000000064c34a55acb24636359",
      logIndex: 2,
      blockHash: "0xc0dc83b672d5f94cbb1534df3d538a0a70da464a7f34f390559942c55ba5dea9",
      removed: false,
    }
  ],
  blockNumber: 18574021,
  confirmations: 215448,
  cumulativeGasUsed: BigNumber.from("0x011ab7"),
  effectiveGasPrice: BigNumber.from("0x06fc23ac0f"),
  status: 1,
  type: 2,
  byzantium: true,
};


export const mockErc721MintTx = {
  hash: '0x96473e4fa0ff9162806ccb19a32b65c299b81b07dcaa2e65caf670840304e1b3',
  type: 2,
  accessList: [],
  blockHash: '0x91b0c7767fffb10c168a44a1bd37d93887f523584adef52f30b839aff8d42e18',
  blockNumber: 18530681,
  transactionIndex: 0,
  confirmations: 259112,
  from: '0x5b4B196F1538f05D472f5322D2702ff8aA0D5e4a',
  gasPrice: BigNumber.from('0x0789444673'),
  maxPriorityFeePerGas: BigNumber.from('0x0789444664'),
  maxFeePerGas: BigNumber.from('0x0789444682'),
  gasLimit: BigNumber.from('0x012b99'),
  to: '0xE0650260C315C30B9C28305777Ae500A0adE4B71',
  value: BigNumber.from('0x00'),
  nonce: 19,
  data: '0xa14481940000000000000000000000005b4b196f1538f05d472f5322d2702ff8aa0d5e4a0000000000000000000000000000000000000000000000000000000000bc614e',
  r: '0x1a1500acbcb90fff34454eff38706bfa648441caf5b303e90b9228a5be584a30',
  s: '0x3b8c865d666e35a7846db180ae71f9f142656262f640777cd651730642a7899d',
  v: 0,
  creates: null,
  chainId: 80002,
};
