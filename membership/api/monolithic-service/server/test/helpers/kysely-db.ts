import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, FileMigrationProvider, sql } from 'kysely';
import { Database, db } from '../../src/db/database';
import { promises as fs } from 'fs';
import path from 'path';

export async function createTestDb(): Promise<Kysely<Database>> {
  return db;
}

export async function migrateDb(db: Kysely<Database>) {
  const migrationsDir = path.resolve(__dirname, '../../src/db/migrations');
  const provider = new FileMigrationProvider({
    fs,
    path,
    migrationFolder: migrationsDir,
  });

  // run migrations
  const migrator = new Migrator({ db, provider });

  try {
    await sql`CREATE EXTENSION IF NOT EXISTS postgis;`.execute(db);
    const { error } = await migrator.migrateToLatest();
    if (error) throw error;
  } catch (err: unknown) {
    console.error('Database migration failed:', (err as Error).message);
  }
}

export async function resetDb(db: <PERSON><PERSON><PERSON><Database>) {
  try {
    await sql`
        DROP SCHEMA public CASCADE;
        CREATE SCHEMA public;
      `.execute(db);
  } catch (err: unknown) {
    console.error('Database reset failed:', (err as Error).message);
  }
}
