/* eslint-disable @typescript-eslint/no-explicit-any */
import { WebhookService, WebhookTxType } from '../../src/services/webhookService';
import { VaultTransactionQueuesRepository } from '../../src/repositories/vaultTransactionQueuesRepository';
import { AccountRepository } from '../../src/repositories/accountRepository';
import { TransactionService } from '../../src/services/transactionService';
import { ServiceInfoRepository } from '../../src/repositories/serviceInfoRepository';
import { TokenBoundAccountImplementationRepository } from '../../src/repositories/tokenBoundAccountImplementationsRepository';
import { TokenBoundAccountRegistryAddressRepository } from '../../src/repositories/tokenBoundAccountRegistryAddressRepository';
import { MetadataService } from '../../src/services/metadataService';
import { VaultTransactionStatus } from '../../src/enum/vaultTransactionStatus';
import { UnauthorizedError } from '../../src/errors/unauthorizedError';
import { ServiceEntity } from '../../src/tables/servicesTable';
import { NftTransactionUpdateService } from '../../src/services/transactionUpdateService';
import { Transaction } from 'kysely';
import { TokenBoundAccountRegistryEntity } from '../../src/tables/tokenBoundAccountRegistriesTable';
import { VaultKeyRepository } from '../../src/repositories/vaultKeyRepository';
import { NftContractsRepository } from '../../src/repositories/nftContractsRepository';
import * as webhookTestData from '../data/webhookTestData';
import { WebhookRequest } from '../../src/dtos/webhook/schemas';
import { NftType } from '../../src/enum/nftType';
import axios from 'axios';
import { AlchemyComponent } from '../../src/components/alchemyComponent';
import { AccountService } from '../../src/services/accountService';
import { TransactionReceipt } from 'alchemy-sdk';
import { TransactionsRepository } from '../../src/repositories/transactionsRepository';
import { TransactionQueuesRepository } from '../../src/repositories/transactionQueuesRepository';
import { BulkMintService } from '../../src/services/bulkMintService';
import { UserRepository } from '../../src/repositories/userRepository';
import { TransactionStatus } from '../../src/enum/transactionStatus';
// import '@types/jest';
jest.mock('axios');

jest.mock('ethers', () => {
  const actualEthers = jest.requireActual('ethers');
  return {
    ...actualEthers,
    providers: {
      JsonRpcProvider: jest.fn(() => {
        return {
          getTransactionCount: jest.fn().mockResolvedValue(0),
          estimateGas: jest.fn().mockResolvedValue({}),
          getGasPrice: jest.fn().mockResolvedValue({}),
        };
      }),
    },
    Interface: jest.fn().mockImplementation((abi) => {
      return new actualEthers.Interface(abi);
    }),
  };
});

jest.mock('../../src/db/database', () => ({
  db: {
    selectFrom: jest.fn(),
    transaction: jest.fn().mockReturnValue({
      execute: jest.fn().mockImplementation(async (callback) => {
        const mockTransaction = {} as Transaction<any>;
        return await callback(mockTransaction);
      }),
    } as unknown as jest.Mocked<Transaction<any>>),
  },
}));

jest.mock('moralis');

describe('WebhookService', () => {
  let webhookService: WebhookService;
  let mockRepositories: Record<string, jest.Mocked<any>>;
  beforeEach(() => {
    process.env.JSON_RPC_URL = 'http://127.0.0.1:8545';
    process.env.ALCHEMY_API_KEY = 'xxxxxsss';
    process.env.ALCHEMY_CHAIN_NAME = 'polygon-amoy';
    process.env.ALCHEMY_AUTH_TOKEN = 'auth-token';
    process.env.GAS_LIMIT_MULTIPLIER = '200';
    process.env.BASE_MAX_FEE_PER_GAS = '600';
    process.env.MAX_FEE_PER_GAS_MULTIPLIER = '150';
    process.env.MAX_PRIORITY_FEE_PER_GAS_MULTIPLIER = '150';
    process.env.GCP_PROJECT_ID = 'sample_gcp_project_id';
    process.env.GCS_BUCKET_NAME = 'mock-bucket-name'; // Added GCS_BUCKET_NAME

    mockRepositories = {
      vaultTransactionQueuesRepository: {
        selectVaultTransactionsByTxHashs: jest.fn(),
        updateVaultTransactionStatus: jest.fn(),
        insertQueue: jest.fn(),
        selectVaultTransactionsByFromAddressAndNonce: jest.fn(),
        updateVaultTransactionHash: jest.fn(),
      } as unknown as jest.Mocked<VaultTransactionQueuesRepository>,
      accountRepository: {
        checkAccountTbaRegstration: jest.fn(),
        updateAccountTBA: jest.fn(),
      } as unknown as jest.Mocked<AccountRepository>,
      transactionService: {
        callContractViewFunction: jest.fn(),
        callContractFunction: jest.fn(),
        isTokenBoundAccountDeployed: jest.fn(),
      } as unknown as jest.Mocked<TransactionService>,
      serviceInfoRepository: {
        getServiceById: jest.fn(),
      } as unknown as jest.Mocked<ServiceInfoRepository>,
      tokenBoundAccountImplementationRepository: {
        selectByServiceId: jest.fn(),
      } as unknown as jest.Mocked<TokenBoundAccountImplementationRepository>,
      tokenBoundAccountRegistryAddressRepository: {
        selectAddressByServiceId: jest.fn(),
      } as unknown as jest.Mocked<TokenBoundAccountRegistryAddressRepository>,
      metadataService: {
        insertMetadata: jest.fn(),
      } as unknown as jest.Mocked<MetadataService>,
      nftTransactionUpdateService: {
        updateFirestoreNftsCollection: jest.fn(),
      } as unknown as jest.Mocked<NftTransactionUpdateService>,
      vaultKeyRepository: {
        getVaultKeyIdWithLock: jest.fn(),
        updateNonce: jest.fn(),
      } as unknown as jest.Mocked<VaultKeyRepository>,
      nftContractsRepository: {
        insertNftContract: jest.fn(),
        selectNftTypeByAddress: jest.fn(),
        selectNftContractByContractAddress: jest.fn(),
        updateNextTokenId: jest.fn(),
        getNftContractsByIdWithLock: jest.fn(),
      } as unknown as jest.Mocked<NftContractsRepository>,
      alchemyComponent: {
        getTransactionReceipt: jest.fn(),
        getTransaction: jest.fn(),
      } as unknown as jest.Mocked<AlchemyComponent>,
      accountService: {
        recreateTba: jest.fn(),
      } as unknown as jest.Mocked<AccountService>,
      transactionsRepository: {
        selectTransactionsByTxHashs: jest.fn(),
        updateTransactionStatus: jest.fn(),
        selectTransactionsByNonce: jest.fn(),
      } as unknown as jest.Mocked<TransactionsRepository>,
      transactionQueuesRepository: {
        selectQueueByTransactionId: jest.fn(),
        updateQueueStatus: jest.fn(),
      } as unknown as jest.Mocked<TransactionQueuesRepository>,
      bulkMintService: {} as unknown as jest.Mocked<BulkMintService>,
      userRepository: {} as unknown as jest.Mocked<UserRepository>,
    };

    webhookService = new WebhookService(
      mockRepositories.transactionsRepository,
      mockRepositories.transactionQueuesRepository,
      mockRepositories.tokenBoundAccountRegistryAddressRepository,
      mockRepositories.metadataService,
      mockRepositories.nftTransactionUpdateService,
      mockRepositories.alchemyComponent,
      mockRepositories.nftContractsRepository,
      mockRepositories.bulkMintService,
      mockRepositories.serviceInfoRepository,
      mockRepositories.userRepository,
    );

    (axios.get as jest.Mock).mockReset();
    (axios.post as jest.Mock).mockReset();

    mockRepositories.transactionQueuesRepository.selectQueueByTransactionId.mockResolvedValue([
      {
        queue_id: 'queue_id',
        service_id: 'service_id',
        from_address: 'from_address',
        to_address: 'to_address',
        status: 'PROCESSING',
        tx_type: 'MINT_MEMBERSHIP',
        nft_type: 'MEMBERSHIP',
        nft_contract_address: 'nft_contract_address',
        token_id: 'token_id',
        created_date: 'created_date',
      },
      {
        queue_id: 'queue_id',
        service_id: 'service_id',
        from_address: 'from_address',
        to_address: 'to_address',
        status: 'PROCESSING',
        tx_type: 'MINT_REWARD',
        nft_type: 'CONTENT',
        nft_contract_address: 'nft_contract_address',
        token_id: 'token_id',
        created_date: 'created_date',
      },
    ]);

    jest.spyOn(webhookService as any, 'extractReceiptForNfts').mockReturnValue([
      {
        address: 'modular_contract_address',
        hash: '0x96473e4fa0ff9162806ccb19a32b65c299b81b07dcaa2e65caf670840304e1b3',
        from: '0x0000000000000000000000000000000000000000',
        to: '0x5b4B196F1538f05D472f5322D2702ff8aA0D5e4a',
        tokenId: '12345678',
        amount: '1',
      },
    ]);
    jest.spyOn(webhookService as any, 'verifyTokenId');
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('handleAddressActivity', () => {
    const signature = 'valid_signature';

    test('should update transaction status and metadata for membership nft mint', async () => {
      jest.spyOn(webhookService as any, 'verifyWebhookSignature').mockResolvedValue(undefined);

      mockRepositories.transactionsRepository.selectTransactionsByTxHashs.mockResolvedValue([
        {
          transaction_id: 'tx1',
          service_id: 'service1',
          tx_hash: '0x96473e4fa0ff9162806ccb19a32b65c299b81b07dcaa2e65caf670840304e1b3',
          status: TransactionStatus.EXECUTED,
        },
      ]);
      mockRepositories.vaultTransactionQueuesRepository.selectVaultTransactionsByTxHashs.mockResolvedValue([
        {
          tx_hash: '0x96473e4fa0ff9162806ccb19a32b65c299b81b07dcaa2e65caf670840304e1b3',
          service_id: 'service1',
          transaction_id: 'tx1',
          status: VaultTransactionStatus.EXECUTED,
          nft_type: 'MEMBERSHIP',
        },
      ]);

      mockRepositories.serviceInfoRepository.getServiceById.mockResolvedValue({
        tenant_id: 'mockTenantId',
        service_id: 'mockServiceId',
        service_name: 'Mock Service',
        membership_nft_contract_id: 'mockMembershipNftContractId',
      } as unknown as ServiceEntity);

      mockRepositories.tokenBoundAccountImplementationRepository.selectByServiceId.mockResolvedValue({
        token_bound_account_implementation_address: '0x41C8f39463A868d3A88af00cd0fe7102F30E44eC',
      });
      mockRepositories.tokenBoundAccountRegistryAddressRepository.selectAddressByServiceId.mockResolvedValue({
        service_id: 'mockServiceId',
        token_bound_account_registry_id: 'mockTokenBoundAccountRegistryId',
        token_bound_account_registry_address: '0x000000006551c19487814612e58FE06813775758',
        abi: [
          {
            inputs: [
              {
                internalType: 'address',
                name: 'implementation',
                type: 'address',
              },
              {
                internalType: 'bytes32',
                name: 'salt',
                type: 'bytes32',
              },
              {
                internalType: 'uint256',
                name: 'chainId',
                type: 'uint256',
              },
              {
                internalType: 'address',
                name: 'tokenContract',
                type: 'address',
              },
              {
                internalType: 'uint256',
                name: 'tokenId',
                type: 'uint256',
              },
            ],
            name: 'createAccount',
            outputs: [
              {
                internalType: 'address',
                name: '',
                type: 'address',
              },
            ],
            stateMutability: 'nonpayable',
            type: 'function',
          },
        ],
        salt: '0x93fba6df018a4c90c0180981ba95daf6575ce5a3b328b14d9044bbe668ef379c',
        chain_id: 80002,
      } as unknown as TokenBoundAccountRegistryEntity);
      mockRepositories.vaultKeyRepository.getVaultKeyIdWithLock.mockResolvedValue({
        vault_key_id: 'mockKeyId',
        tenant_id: 'mockTenantId',
        key_ring_project: 'key_ring_project:',
        key_ring_location: 'key_ring_location',
        key_ring_name: 'key_ring_name',
        key_version: 'key_version',
        vault_wallet_address: '******************************************',
        nonce: 100,
      });
      mockRepositories.vaultKeyRepository.updateNonce.mockResolvedValue();
      mockRepositories.transactionService.callContractFunction.mockResolvedValue({
        data: '0xaaaa1',
        from: '******************************************',
        hash: '0xaaaa3',
        nonce: 1000,
      });
      mockRepositories.nftContractsRepository.selectNftTypeByAddress.mockResolvedValue(NftType.MEMBERSHIP);
      mockRepositories.nftContractsRepository.getNftContractsByIdWithLock.mockResolvedValue({
        nft_contract_id: 'nft_contract_id',
        service_id: 'mockServiceId',
      });
      (axios.get as jest.Mock).mockResolvedValue(webhookTestData.mockMintTxReceipt);
      mockRepositories.alchemyComponent.getTransactionReceipt.mockResolvedValue(
        webhookTestData.mockMintTxReceipt as TransactionReceipt,
      );

      const mockAddressActivityWebhookResponse = {
        ...webhookTestData.mockErc721MintAddressActivityWebhookResponse,
        webhookId: 'dummy-address-webhook-id',
      };
      await webhookService.handleBlockchainWebhook(mockAddressActivityWebhookResponse as WebhookRequest, signature);

      expect(mockRepositories.nftTransactionUpdateService.updateFirestoreNftsCollection).toHaveBeenCalledWith({
        type: WebhookTxType.NFT_TRANSFER,
        transactionHash: '0x96473e4fa0ff9162806ccb19a32b65c299b81b07dcaa2e65caf670840304e1b3',
        nftFrom: '0x0000000000000000000000000000000000000000',
        nftTo: '0x5b4B196F1538f05D472f5322D2702ff8aA0D5e4a',
        nftAmount: '1',
        nftTokenId: '12345678',
        nftContractAddress: 'modular_contract_address',
        timestamp: '2025-02-26T02:22:06.641Z',
        isConfirmed: false,
      });
    });

    test('should update transaction status and metadata for membership nft mint (Case: txhash is not in the database)', async () => {
      jest.spyOn(webhookService as any, 'verifyWebhookSignature').mockResolvedValue(undefined);

      mockRepositories.transactionsRepository.selectTransactionsByTxHashs.mockResolvedValue([]);
      mockRepositories.transactionsRepository.selectTransactionsByNonce.mockResolvedValue([
        {
          transaction_id: 'tx1',
          service_id: 'service1',
          tx_hash: '0x96473e4fa0ff9162806ccb19a32b65c299b81b07dcaa2e65caf670840304e1b3',
          status: TransactionStatus.EXECUTED,
        },
      ]);
      mockRepositories;

      mockRepositories.serviceInfoRepository.getServiceById.mockResolvedValue({
        tenant_id: 'mockTenantId',
        service_id: 'mockServiceId',
        service_name: 'Mock Service',
        membership_nft_contract_id: 'mockMembershipNftContractId',
      } as unknown as ServiceEntity);

      mockRepositories.tokenBoundAccountImplementationRepository.selectByServiceId.mockResolvedValue({
        token_bound_account_implementation_address: '0x41C8f39463A868d3A88af00cd0fe7102F30E44eC',
      });
      mockRepositories.tokenBoundAccountRegistryAddressRepository.selectAddressByServiceId.mockResolvedValue({
        service_id: 'mockServiceId',
        token_bound_account_registry_id: 'mockTokenBoundAccountRegistryId',
        token_bound_account_registry_address: '0x000000006551c19487814612e58FE06813775758',
        abi: [
          {
            inputs: [
              {
                internalType: 'address',
                name: 'implementation',
                type: 'address',
              },
              {
                internalType: 'bytes32',
                name: 'salt',
                type: 'bytes32',
              },
              {
                internalType: 'uint256',
                name: 'chainId',
                type: 'uint256',
              },
              {
                internalType: 'address',
                name: 'tokenContract',
                type: 'address',
              },
              {
                internalType: 'uint256',
                name: 'tokenId',
                type: 'uint256',
              },
            ],
            name: 'createAccount',
            outputs: [
              {
                internalType: 'address',
                name: '',
                type: 'address',
              },
            ],
            stateMutability: 'nonpayable',
            type: 'function',
          },
        ],
        salt: '0x93fba6df018a4c90c0180981ba95daf6575ce5a3b328b14d9044bbe668ef379c',
        chain_id: 80002,
      } as unknown as TokenBoundAccountRegistryEntity);
      mockRepositories.vaultKeyRepository.getVaultKeyIdWithLock.mockResolvedValue({
        vault_key_id: 'mockKeyId',
        tenant_id: 'mockTenantId',
        key_ring_project: 'key_ring_project:',
        key_ring_location: 'key_ring_location',
        key_ring_name: 'key_ring_name',
        key_version: 'key_version',
        vault_wallet_address: '******************************************',
        nonce: 100,
      });
      mockRepositories.vaultKeyRepository.updateNonce.mockResolvedValue();
      mockRepositories.transactionService.callContractFunction.mockResolvedValue({
        data: '0xaaaa1',
        from: '******************************************',
        hash: '0xaaaa3',
        nonce: 1000,
      });
      mockRepositories.nftContractsRepository.selectNftTypeByAddress.mockResolvedValue(NftType.MEMBERSHIP);
      mockRepositories.nftContractsRepository.getNftContractsByIdWithLock.mockResolvedValue({
        nft_contract_id: 'nft_contract_id',
        service_id: 'mockServiceId',
      });
      mockRepositories.alchemyComponent.getTransaction.mockResolvedValue(webhookTestData.mockErc721MintTx);
      mockRepositories.alchemyComponent.getTransactionReceipt.mockResolvedValue(
        webhookTestData.mockMintTxReceipt as TransactionReceipt,
      );

      const mockAddressActivityWebhookResponseCase2 = {
        ...webhookTestData.mockErc721MintAddressActivityWebhookResponse,
        webhookId: 'dummy-address-webhook-id',
      };
      await webhookService.handleBlockchainWebhook(
        mockAddressActivityWebhookResponseCase2 as WebhookRequest,
        signature,
      );

      expect(mockRepositories.nftTransactionUpdateService.updateFirestoreNftsCollection).toHaveBeenCalledWith({
        type: WebhookTxType.NFT_TRANSFER,
        transactionHash: '0x96473e4fa0ff9162806ccb19a32b65c299b81b07dcaa2e65caf670840304e1b3',
        nftFrom: '0x0000000000000000000000000000000000000000',
        nftTo: '0x5b4B196F1538f05D472f5322D2702ff8aA0D5e4a',
        nftAmount: '1',
        nftTokenId: '12345678',
        nftContractAddress: 'modular_contract_address',
        timestamp: '2025-02-26T02:22:06.641Z',
        isConfirmed: false,
      });
    });

    test('should update transaction status and metadata for reward nft mint', async () => {
      jest.spyOn(webhookService as any, 'verifyWebhookSignature').mockResolvedValue(undefined);

      mockRepositories.vaultTransactionQueuesRepository.selectVaultTransactionsByTxHashs.mockResolvedValue([
        {
          tx_hash: '0x96473e4fa0ff9162806ccb19a32b65c299b81b07dcaa2e65caf670840304e1b3',
          service_id: 'service1',
          transaction_id: 'tx1',
          status: VaultTransactionStatus.EXECUTED,
          nft_type: NftType.CONTENT,
        },
      ]);
      mockRepositories.transactionsRepository.selectTransactionsByTxHashs.mockResolvedValue([
        {
          transaction_id: 'tx1',
          service_id: 'service1',
          tx_hash: '0x96473e4fa0ff9162806ccb19a32b65c299b81b07dcaa2e65caf670840304e1b3',
          status: TransactionStatus.EXECUTED,
        },
      ]);

      mockRepositories.serviceInfoRepository.getServiceById.mockResolvedValue({
        tenant_id: 'mockTenantId',
        service_id: 'mockServiceId',
        service_name: 'Mock Service',
        membership_nft_contract_id: 'mockMembershipNftContractId',
      } as unknown as ServiceEntity);

      mockRepositories.tokenBoundAccountImplementationRepository.selectByServiceId.mockResolvedValue({
        token_bound_account_implementation_address: '0x41C8f39463A868d3A88af00cd0fe7102F30E44eC',
      });
      mockRepositories.tokenBoundAccountRegistryAddressRepository.selectAddressByServiceId.mockResolvedValue({
        service_id: 'mockServiceId',
        token_bound_account_registry_id: 'mockTokenBoundAccountRegistryId',
        token_bound_account_registry_address: '0x000000006551c19487814612e58FE06813775758',
        abi: [
          {
            inputs: [
              {
                internalType: 'address',
                name: 'implementation',
                type: 'address',
              },
              {
                internalType: 'bytes32',
                name: 'salt',
                type: 'bytes32',
              },
              {
                internalType: 'uint256',
                name: 'chainId',
                type: 'uint256',
              },
              {
                internalType: 'address',
                name: 'tokenContract',
                type: 'address',
              },
              {
                internalType: 'uint256',
                name: 'tokenId',
                type: 'uint256',
              },
            ],
            name: 'createAccount',
            outputs: [
              {
                internalType: 'address',
                name: '',
                type: 'address',
              },
            ],
            stateMutability: 'nonpayable',
            type: 'function',
          },
        ],
        salt: '0x93fba6df018a4c90c0180981ba95daf6575ce5a3b328b14d9044bbe668ef379c',
        chain_id: 80002,
      } as unknown as TokenBoundAccountRegistryEntity);
      mockRepositories.vaultKeyRepository.getVaultKeyIdWithLock.mockResolvedValue({
        vault_key_id: 'mockKeyId',
        tenant_id: 'mockTenantId',
        key_ring_project: 'key_ring_project:',
        key_ring_location: 'key_ring_location',
        key_ring_name: 'key_ring_name',
        key_version: 'key_version',
        vault_wallet_address: '******************************************',
        nonce: 100,
      });
      mockRepositories.vaultKeyRepository.updateNonce.mockResolvedValue();
      mockRepositories.transactionService.callContractFunction.mockResolvedValue({
        data: '0xaaaa1',
        from: '******************************************',
        hash: '0xaaaa3',
        nonce: 1000,
      });
      mockRepositories.nftContractsRepository.selectNftTypeByAddress.mockResolvedValue(NftType.CONTENT);
      mockRepositories.nftContractsRepository.getNftContractsByIdWithLock.mockResolvedValue({
        nft_contract_id: 'nft_contract_id',
        service_id: 'mockServiceId',
      });
      (axios.get as jest.Mock).mockResolvedValue(webhookTestData.mockMintTxReceipt);
      mockRepositories.alchemyComponent.getTransactionReceipt.mockResolvedValue(
        webhookTestData.mockMintTxReceipt as TransactionReceipt,
      );

      const mockAddressActivityWebhookResponseCase3 = {
        ...webhookTestData.mockErc721MintAddressActivityWebhookResponse,
        webhookId: 'dummy-address-webhook-id',
      };
      await webhookService.handleBlockchainWebhook(
        mockAddressActivityWebhookResponseCase3 as WebhookRequest,
        signature,
      );

      expect(mockRepositories.metadataService.insertMetadata).toHaveBeenCalled();
      expect(mockRepositories.nftTransactionUpdateService.updateFirestoreNftsCollection).toHaveBeenCalledWith({
        type: WebhookTxType.NFT_TRANSFER,
        transactionHash: '0x96473e4fa0ff9162806ccb19a32b65c299b81b07dcaa2e65caf670840304e1b3',
        nftFrom: '0x0000000000000000000000000000000000000000',
        nftTo: '0x5b4B196F1538f05D472f5322D2702ff8aA0D5e4a',
        nftAmount: '1',
        nftTokenId: '12345678',
        nftContractAddress: 'modular_contract_address',
        timestamp: '2025-02-26T02:22:06.641Z',
        isConfirmed: false,
      });
    });

    test('should update transaction status and metadata for coupon nft mint', async () => {
      jest.spyOn(webhookService as any, 'verifyWebhookSignature').mockResolvedValue(undefined);
      jest.spyOn(webhookService as any, 'extractReceiptForNfts').mockReturnValue([
        {
          address: 'modular_contract_address',
          hash: '0xcc848e5e748882ccac85715c8f618da4b97ad52b24d53d1567a30fe9016e4a80',
          from: '0x0000000000000000000000000000000000000000',
          to: '0x5b4B196F1538f05D472f5322D2702ff8aA0D5e4a',
          tokenId: '111111',
          amount: '10',
        },
      ]);

      mockRepositories.vaultTransactionQueuesRepository.selectVaultTransactionsByTxHashs.mockResolvedValue([
        {
          tx_hash: '0xcc848e5e748882ccac85715c8f618da4b97ad52b24d53d1567a30fe9016e4a80',
          service_id: 'service1',
          transaction_id: 'tx1',
          status: VaultTransactionStatus.EXECUTED,
          nft_type: NftType.COUPON,
        },
      ]);
      mockRepositories.transactionsRepository.selectTransactionsByTxHashs.mockResolvedValue([
        {
          transaction_id: 'tx1',
          service_id: 'service1',
          tx_hash: '0xcc848e5e748882ccac85715c8f618da4b97ad52b24d53d1567a30fe9016e4a80',
          status: TransactionStatus.EXECUTED,
        },
      ]);

      mockRepositories.serviceInfoRepository.getServiceById.mockResolvedValue({
        tenant_id: 'mockTenantId',
        service_id: 'mockServiceId',
        service_name: 'Mock Service',
        membership_nft_contract_id: 'mockMembershipNftContractId',
      } as unknown as ServiceEntity);

      mockRepositories.tokenBoundAccountImplementationRepository.selectByServiceId.mockResolvedValue({
        token_bound_account_implementation_address: '0x41C8f39463A868d3A88af00cd0fe7102F30E44eC',
      });
      mockRepositories.tokenBoundAccountRegistryAddressRepository.selectAddressByServiceId.mockResolvedValue({
        service_id: 'mockServiceId',
        token_bound_account_registry_id: 'mockTokenBoundAccountRegistryId',
        token_bound_account_registry_address: '0x000000006551c19487814612e58FE06813775758',
        abi: [
          {
            inputs: [
              {
                internalType: 'address',
                name: 'implementation',
                type: 'address',
              },
              {
                internalType: 'bytes32',
                name: 'salt',
                type: 'bytes32',
              },
              {
                internalType: 'uint256',
                name: 'chainId',
                type: 'uint256',
              },
              {
                internalType: 'address',
                name: 'tokenContract',
                type: 'address',
              },
              {
                internalType: 'uint256',
                name: 'tokenId',
                type: 'uint256',
              },
            ],
            name: 'createAccount',
            outputs: [
              {
                internalType: 'address',
                name: '',
                type: 'address',
              },
            ],
            stateMutability: 'nonpayable',
            type: 'function',
          },
        ],
        salt: '0x93fba6df018a4c90c0180981ba95daf6575ce5a3b328b14d9044bbe668ef379c',
        chain_id: 80002,
      } as unknown as TokenBoundAccountRegistryEntity);
      mockRepositories.vaultKeyRepository.getVaultKeyIdWithLock.mockResolvedValue({
        vault_key_id: 'mockKeyId',
        tenant_id: 'mockTenantId',
        key_ring_project: 'key_ring_project:',
        key_ring_location: 'key_ring_location',
        key_ring_name: 'key_ring_name',
        key_version: 'key_version',
        vault_wallet_address: '******************************************',
        nonce: 100,
      });
      mockRepositories.vaultKeyRepository.updateNonce.mockResolvedValue();
      mockRepositories.transactionService.callContractFunction.mockResolvedValue({
        data: '0xaaaa1',
        from: '******************************************',
        hash: '0xaaaa3',
        nonce: 1000,
      });
      mockRepositories.nftContractsRepository.selectNftTypeByAddress.mockResolvedValue(NftType.COUPON);
      mockRepositories.nftContractsRepository.getNftContractsByIdWithLock.mockResolvedValue({
        nft_contract_id: 'nft_contract_id',
        service_id: 'mockServiceId',
      });
      (axios.get as jest.Mock).mockResolvedValue(webhookTestData.mockErc1155MintTxReceipt);
      mockRepositories.alchemyComponent.getTransactionReceipt.mockResolvedValue(
        webhookTestData.mockErc1155MintTxReceipt,
      );

      const mockAddressActivityWebhookResponseCase4 = {
        ...webhookTestData.mockErc1155MintAddressActivityWebhookResponse,
        webhookId: 'dummy-address-webhook-id',
      };
      await webhookService.handleBlockchainWebhook(
        mockAddressActivityWebhookResponseCase4 as WebhookRequest,
        signature,
      );

      expect(mockRepositories.metadataService.insertMetadata).toHaveBeenCalled();
      expect(mockRepositories.nftTransactionUpdateService.updateFirestoreNftsCollection).toHaveBeenCalledWith({
        type: WebhookTxType.NFT_TRANSFER,
        transactionHash: '0xcc848e5e748882ccac85715c8f618da4b97ad52b24d53d1567a30fe9016e4a80',
        nftFrom: '0x0000000000000000000000000000000000000000',
        nftTo: '0x5b4B196F1538f05D472f5322D2702ff8aA0D5e4a',
        nftAmount: '10',
        nftTokenId: '111111',
        nftContractAddress: 'modular_contract_address',
        timestamp: '2025-02-27T03:18:02.532Z',
        isConfirmed: false,
      });
    });

    test('should update transaction status for tba deploy tx', async () => {
      jest.spyOn(webhookService as any, 'verifyWebhookSignature').mockResolvedValue(undefined);
      // Mock attachReceipt to fix error
      jest.spyOn(webhookService as any, 'attachReceipt').mockResolvedValue([
        webhookTestData.mockMintTxReceipt as unknown as TransactionReceipt,
      ]);

      mockRepositories.vaultTransactionQueuesRepository.selectVaultTransactionsByTxHashs.mockResolvedValue([
        {
          tx_hash: '0x81c1fb4802335eda461891b2a18598f3a6050bc4df817ac46174743a67601738',
          service_id: 'service1',
          transaction_id: 'tx1',
          status: VaultTransactionStatus.EXECUTED,
          nft_type: 'MEMBERSHIP',
        },
      ]);
      mockRepositories.transactionsRepository.selectTransactionsByTxHashs.mockResolvedValue([
        {
          transaction_id: 'tx1',
          service_id: 'service1',
          tx_hash: '0x81c1fb4802335eda461891b2a18598f3a6050bc4df817ac46174743a67601738',
          status: TransactionStatus.EXECUTED,
        },
      ]);

      mockRepositories.serviceInfoRepository.getServiceById.mockResolvedValue({
        tenant_id: 'mockTenantId',
        service_id: 'mockServiceId',
        service_name: 'Mock Service',
        membership_nft_contract_id: 'mockMembershipNftContractId',
      } as unknown as ServiceEntity);
      mockRepositories.tokenBoundAccountImplementationRepository.selectByServiceId.mockResolvedValue({
        token_bound_account_implementation_address: '0x41C8f39463A868d3A88af00cd0fe7102F30E44eC',
      });
      mockRepositories.tokenBoundAccountRegistryAddressRepository.selectAddressByServiceId.mockResolvedValue({
        service_id: 'mockServiceId',
        token_bound_account_registry_id: 'mockTokenBoundAccountRegistryId',
        token_bound_account_registry_address: '0x000000006551c19487814612e58FE06813775758',
        abi: [
          {
            inputs: [
              {
                internalType: 'address',
                name: 'implementation',
                type: 'address',
              },
              {
                internalType: 'bytes32',
                name: 'salt',
                type: 'bytes32',
              },
              {
                internalType: 'uint256',
                name: 'chainId',
                type: 'uint256',
              },
              {
                internalType: 'address',
                name: 'tokenContract',
                type: 'address',
              },
              {
                internalType: 'uint256',
                name: 'tokenId',
                type: 'uint256',
              },
            ],
            name: 'createAccount',
            outputs: [
              {
                internalType: 'address',
                name: '',
                type: 'address',
              },
            ],
            stateMutability: 'nonpayable',
            type: 'function',
          },
        ],
        salt: '0x93fba6df018a4c90c0180981ba95daf6575ce5a3b328b14d9044bbe668ef379c',
        chain_id: 80002,
      } as unknown as TokenBoundAccountRegistryEntity);

      mockRepositories.vaultKeyRepository.getVaultKeyIdWithLock.mockResolvedValue({
        vault_key_id: 'mockKeyId',
        tenant_id: 'mockTenantId',
        key_ring_project: 'key_ring_project:',
        key_ring_location: 'key_ring_location',
        key_ring_name: 'key_ring_name',
        key_version: 'key_version',
        vault_wallet_address: '******************************************',
        nonce: 100,
      });
      mockRepositories.vaultKeyRepository.updateNonce.mockResolvedValue();

      const mockAddressActivityWebhookResponseCase5 = {
        ...webhookTestData.mockTbaDeployWebhookResponse,
        webhookId: 'dummy-address-webhook-id',
      };
      await webhookService.handleBlockchainWebhook(
        mockAddressActivityWebhookResponseCase5 as WebhookRequest,
        signature,
      );
    });

    test('should update transaction status for nft deploy tx', async () => {
      jest.spyOn(webhookService as any, 'verifyWebhookSignature').mockResolvedValue(undefined);
      // Mock attachReceipt to fix error
      jest.spyOn(webhookService as any, 'attachReceipt').mockResolvedValue([
        webhookTestData.mockMintTxReceipt as unknown as TransactionReceipt,
      ]);

      mockRepositories.vaultTransactionQueuesRepository.selectVaultTransactionsByTxHashs.mockResolvedValue([
        {
          tx_hash: '0xaaa1d691b818e6d034627429aea47e4f62b9efa5982d350564f938c85041bba8',
          service_id: 'service1',
          transaction_id: 'tx1',
          status: VaultTransactionStatus.EXECUTED,
          nft_type: 'MEMBERSHIP',
        },
      ]);
      mockRepositories.transactionsRepository.selectTransactionsByTxHashs.mockResolvedValue([
        {
          transaction_id: 'tx1',
          service_id: 'service1',
          tx_hash: '0xaaa1d691b818e6d034627429aea47e4f62b9efa5982d350564f938c85041bba8',
          status: TransactionStatus.EXECUTED,
        },
      ]);
      mockRepositories.serviceInfoRepository.getServiceById.mockResolvedValue({
        tenant_id: 'mockTenantId',
        service_id: 'mockServiceId',
        service_name: 'Mock Service',
        membership_nft_contract_id: 'mockMembershipNftContractId',
      } as unknown as ServiceEntity);
      mockRepositories.vaultKeyRepository.getVaultKeyIdWithLock.mockResolvedValue({
        vault_key_id: 'mockKeyId',
        tenant_id: 'mockTenantId',
        key_ring_project: 'key_ring_project:',
        key_ring_location: 'key_ring_location',
        key_ring_name: 'key_ring_name',
        key_version: 'key_version',
        vault_wallet_address: '******************************************',
        nonce: 100,
      });
      mockRepositories.vaultKeyRepository.updateNonce.mockResolvedValue();

      const mockAddressActivityWebhookResponseCase6 = {
        ...webhookTestData.mockNftDeployWebhookResponse,
        webhookId: 'dummy-address-webhook-id',
      };
      await webhookService.handleBlockchainWebhook(
        mockAddressActivityWebhookResponseCase6 as WebhookRequest,
        signature,
      );
    });

    test('should update transaction status for nft transfer tx but not update firestore', async () => {
      jest.spyOn(webhookService as any, 'verifyWebhookSignature').mockResolvedValue(undefined);
      jest.spyOn(webhookService as any, 'extractReceiptForNfts').mockReturnValue([]);

      mockRepositories.vaultTransactionQueuesRepository.selectVaultTransactionsByTxHashs.mockResolvedValue([
        {
          tx_hash: '0x05645ac51f704ca5e44276ba7fb946593de533b454d3d791624b56332738e888',
          service_id: 'service1',
          transaction_id: 'tx1',
          status: VaultTransactionStatus.EXECUTED,
          nft_type: 'MEMBERSHIP',
        },
      ]);
      mockRepositories.transactionsRepository.selectTransactionsByTxHashs.mockResolvedValue([
        {
          transaction_id: 'tx1',
          service_id: 'service1',
          tx_hash: '0x05645ac51f704ca5e44276ba7fb946593de533b454d3d791624b56332738e888',
          status: TransactionStatus.EXECUTED,
        },
      ]);

      mockRepositories.serviceInfoRepository.getServiceById.mockResolvedValue({
        tenant_id: 'mockTenantId',
        service_id: 'mockServiceId',
        service_name: 'Mock Service',
        membership_nft_contract_id: 'mockMembershipNftContractId',
      } as unknown as ServiceEntity);

      mockRepositories.tokenBoundAccountImplementationRepository.selectByServiceId.mockResolvedValue({
        token_bound_account_implementation_address: '0x41C8f39463A868d3A88af00cd0fe7102F30E44eC',
      });
      mockRepositories.tokenBoundAccountRegistryAddressRepository.selectAddressByServiceId.mockResolvedValue({
        service_id: 'mockServiceId',
        token_bound_account_registry_id: 'mockTokenBoundAccountRegistryId',
        token_bound_account_registry_address: '0x000000006551c19487814612e58FE06813775758',
        abi: [
          {
            inputs: [
              {
                internalType: 'address',
                name: 'implementation',
                type: 'address',
              },
              {
                internalType: 'bytes32',
                name: 'salt',
                type: 'bytes32',
              },
              {
                internalType: 'uint256',
                name: 'chainId',
                type: 'uint256',
              },
              {
                internalType: 'address',
                name: 'tokenContract',
                type: 'address',
              },
              {
                internalType: 'uint256',
                name: 'tokenId',
                type: 'uint256',
              },
            ],
            name: 'createAccount',
            outputs: [
              {
                internalType: 'address',
                name: '',
                type: 'address',
              },
            ],
            stateMutability: 'nonpayable',
            type: 'function',
          },
        ],
        salt: '0x93fba6df018a4c90c0180981ba95daf6575ce5a3b328b14d9044bbe668ef379c',
        chain_id: 80002,
      } as unknown as TokenBoundAccountRegistryEntity);
      mockRepositories.vaultKeyRepository.getVaultKeyIdWithLock.mockResolvedValue({
        vault_key_id: 'mockKeyId',
        tenant_id: 'mockTenantId',
        key_ring_project: 'key_ring_project:',
        key_ring_location: 'key_ring_location',
        key_ring_name: 'key_ring_name',
        key_version: 'key_version',
        vault_wallet_address: '******************************************',
        nonce: 100,
      });
      mockRepositories.vaultKeyRepository.updateNonce.mockResolvedValue();
      mockRepositories.transactionService.callContractFunction.mockResolvedValue({
        data: '0xaaaa1',
        from: '******************************************',
        hash: '0xaaaa3',
        nonce: 1000,
      });
      mockRepositories.nftContractsRepository.selectNftTypeByAddress.mockResolvedValue(NftType.MEMBERSHIP);
      mockRepositories.nftContractsRepository.getNftContractsByIdWithLock.mockResolvedValue({
        nft_contract_id: 'nft_contract_id',
        service_id: 'mockServiceId',
      });
      (axios.get as jest.Mock).mockResolvedValue(webhookTestData.mockErc721TransferTxReceipt);
      mockRepositories.alchemyComponent.getTransactionReceipt.mockResolvedValue(
        webhookTestData.mockErc721TransferTxReceipt,
      );

      const mockAddressActivityWebhookResponseCase7 = {
        ...webhookTestData.mockErc721TransferAddressActivityWebhookResponse,
        webhookId: 'dummy-address-webhook-id',
      };
      await webhookService.handleBlockchainWebhook(
        mockAddressActivityWebhookResponseCase7 as WebhookRequest,
        signature,
      );

      expect(mockRepositories.nftTransactionUpdateService.updateFirestoreNftsCollection).not.toHaveBeenCalled();
    });
  });

  describe('handleNftActivity', () => {
    const signature = 'valid_signature';

    test('should update firestore for erc721 nft transfer', async () => {
      jest.spyOn(webhookService as any, 'verifyWebhookSignature').mockResolvedValue(undefined);
      const spyHandleNftActivity = jest.spyOn(webhookService as any, 'handleNftActivity');
      const mockNftActivityWebhookResponse = {
        ...webhookTestData.mockErc721TransferWebhookResponse,
        webhookId: 'dummy-nft-webhook-id',
      };
      await webhookService.handleBlockchainWebhook(mockNftActivityWebhookResponse as WebhookRequest, signature);

      expect(spyHandleNftActivity).toHaveBeenCalled();
      expect(mockRepositories.nftTransactionUpdateService.updateFirestoreNftsCollection).toHaveBeenCalledWith({
        type: WebhookTxType.NFT_TRANSFER,
        transactionHash: '0x2ae31e193e4c583a9781cb166c7b4e69d7a957f56d734ef8282c89ddf491cb78',
        nftFrom: '0x5b4B196F1538f05D472f5322D2702ff8aA0D5e4a',
        nftTo: '0x65150B5Fa861481651225Ef4412136DCBf696232',
        nftAmount: '1',
        nftTokenId: '11111',
        nftContractAddress: '0xE0650260C315C30B9C28305777Ae500A0adE4B71',
        timestamp: '2025-02-26T09:00:22.590Z',
        isConfirmed: false,
      });
    });

    test('should update firestore for erc1155 nft transfer', async () => {
      jest.spyOn(webhookService as any, 'verifyWebhookSignature').mockResolvedValue(undefined);
      const spyHandleNftActivity = jest.spyOn(webhookService as any, 'handleNftActivity');
      const mockNftActivityWebhookResponseCase2 = {
        ...webhookTestData.mockErc1155TransferWebhookResponse,
        webhookId: 'dummy-nft-webhook-id',
      };
      await webhookService.handleBlockchainWebhook(mockNftActivityWebhookResponseCase2 as WebhookRequest, signature);

      expect(spyHandleNftActivity).toHaveBeenCalled();
      expect(mockRepositories.nftTransactionUpdateService.updateFirestoreNftsCollection).toHaveBeenCalledWith({
        type: WebhookTxType.NFT_TRANSFER,
        transactionHash: '0x62508ebd3febfe89e482894a5aaea37da3020b938af28ffb8869b3bfe4cc1492',
        nftFrom: '0x5b4B196F1538f05D472f5322D2702ff8aA0D5e4a',
        nftTo: '0x65150B5Fa861481651225Ef4412136DCBf696232',
        nftAmount: '5',
        nftTokenId: '111111',
        nftContractAddress: '0x72b5d4759b11eD58bE62e717C3B9DfBAfa408280',
        timestamp: '2025-02-26T09:41:03.010Z',
        isConfirmed: false,
      });
    });

    test('should pass nft mint tx', async () => {
      jest.spyOn(webhookService as any, 'verifyWebhookSignature').mockResolvedValue(undefined);
      const spyHandleNftActivity = jest.spyOn(webhookService as any, 'handleNftActivity');
      const mockNftActivityWebhookResponseCase3 = {
        ...webhookTestData.mockErc721MintNftActivityWebhookResponse,
        webhookId: 'dummy-nft-webhook-id',
      };
      await webhookService.handleBlockchainWebhook(mockNftActivityWebhookResponseCase3 as WebhookRequest, signature);

      expect(spyHandleNftActivity).toHaveBeenCalled();
      expect(mockRepositories.nftTransactionUpdateService.updateFirestoreNftsCollection).not.toHaveBeenCalled();
    });
  });

  test('should throw an error if signature is invalid', async () => {
    jest.spyOn(webhookService as any, 'verifyWebhookSignature').mockImplementation(() => {
      throw new UnauthorizedError('Invalid signature');
    });
    await expect(
      webhookService.handleBlockchainWebhook(
        webhookTestData.mockErc721MintAddressActivityWebhookResponse as WebhookRequest,
        'invalid_signature',
      ),
    ).rejects.toThrow(new UnauthorizedError('Invalid signature'));
  });

  // test('should throw an error if unexpected streamId is provided', async () => {
  //   const invalidTransaction = {
  //     ...webhookTestData.mockErc721MintAddressActivityWebhookResponse,
  //     streamId: 'unexpected_stream_id',
  //   };
  //   jest.spyOn(webhookService as any, 'verifyWebhookSignature').mockResolvedValue(undefined);

  //   mockRepositories.vaultTransactionQueuesRepository.selectVaultTransactionsByTxHashs.mockResolvedValue([
  //     {
  //       tx_hash: '0xa73761d1f6d98058a83940d3b00208cb27f8de75665f7a4acda633bb0a6b2fc5',
  //       service_id: 'service1',
  //       transaction_id: 'tx1',
  //       status: VaultTransactionStatus.MINED,
  //       nft_type: 'CERTIFICATE',
  //     },
  //   ]);

  //   mockRepositories.serviceInfoRepository.getServiceById.mockResolvedValue({
  //     tenant_id: 'mockTenantId',
  //     service_id: 'mockServiceId',
  //     service_name: 'Mock Service',
  //   } as unknown as ServiceEntity);

  //   mockRepositories.tokenBoundAccountImplementationRepository.selectByServiceId.mockResolvedValue({
  //     token_bound_account_implementation_address: '0xMockImplementationAddress',
  //   });
  //   mockRepositories.tokenBoundAccountRegistryAddressRepository.selectAddressByServiceId.mockResolvedValue({
  //     token_bound_account_registry_address: '0xMockRegistryAddress',
  //     abi: [],
  //     salt: 'mockSalt',
  //   });
  //   const signature = 'valid_signature';

  //   await expect(webhookService.handleBlockchainWebhook(invalidTransaction, signature)).rejects.toThrow(
  //     'Unexpected Stream received',
  //   );
  // });

  // });
});
