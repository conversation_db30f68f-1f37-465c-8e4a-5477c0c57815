import { QuestDetailResponse } from '../../src/dtos/quests/response/questDetailResponse';
import { QuestListItemResponse } from '../../src/dtos/quests/response/questResponseItem';
import {
  CreateActionRequest,
  CreateQuestRewardRequest,
  CreateQuestRewardResponse,
  QuestCreateRequest,
  QuestCreateResponse,
} from '../../src/dtos/services/schemas';
import { ActionType } from '../../src/enum/actionType';
import { CertificateType } from '../../src/enum/certificateType';
import { languageCode } from '../../src/enum/languageCode';
import { QuestRewardPriorityType } from '../../src/enum/questRewardPriorityType';
import { QuestType } from '../../src/enum/questType';
import { RewardAcquirementType } from '../../src/enum/rewardAcquirementType';
import { RewardComponentType } from '../../src/enum/rewardComponentType';
import { InternalServerError } from '../../src/errors/internalServerError';
import { NotFoundError } from '../../src/errors/notFoundError';
import { ValidationError } from '../../src/errors/validationError';
import { AchievementActionRepository } from '../../src/repositories/achievementActionRepository';
import { ActionActivityRepository } from '../../src/repositories/actionActivityRepository';
import { ActionRepository } from '../../src/repositories/actionRepository';
import { ClaimedRewardRepository } from '../../src/repositories/claimedRewardRepository';
import { NftBaseMetadatasRepository } from '../../src/repositories/nftBaseMetadatasRepository';
import { NftComponentRepository } from '../../src/repositories/nftComponentRepository';
import { NftContractTypesRepository } from '../../src/repositories/nftContractTypesRepository';
import { NftContractsRepository } from '../../src/repositories/nftContractsRepository';
import { OnlineCheckinActionRepository } from '../../src/repositories/onlineCheckinActionRepository';
import { QrCheckinActionRepository } from '../../src/repositories/qrCheckinActionRepository';
import { QuestActionRepository } from '../../src/repositories/questActionRepository';
import { QuestRepository, QuestWithReward } from '../../src/repositories/questRepository';
import { QuestionnaireActionRepository } from '../../src/repositories/questionnaireActionRepository';
import { QuestionnaireResultRankRepository } from '../../src/repositories/questionnaireResultRankRepository';
import { RewardComponentRepository } from '../../src/repositories/rewardComponentRepository';
import { ExtendedQuestReward, RewardRepository } from '../../src/repositories/rewardRepository';
import { SerialCodeActionRepository } from '../../src/repositories/serialCodeActionRepository';
import { ServiceInfoRepository } from '../../src/repositories/serviceInfoRepository';
import { TransactionQueuesRepository } from '../../src/repositories/transactionQueuesRepository';
import { TransactionsRepository } from '../../src/repositories/transactionsRepository';
import { VaultKeyRepository } from '../../src/repositories/vaultKeyRepository';
import { MetadataService } from '../../src/services/metadataService';
import { NftRegisterService } from '../../src/services/nftRegisterService';
import { QuestService } from '../../src/services/questService';
import { TransactionService } from '../../src/services/transactionService';
import { WebhookService } from '../../src/services/webhookService';
import { ActionEntity, ActionWithTranslations } from '../../src/tables/actionTable';
import { QuestEntity, QuestWithTranslations } from '../../src/tables/questTable';
import { RewardEntity } from '../../src/tables/rewardTable';
import { ServiceEntity } from '../../src/tables/servicesTable';
import { ActionTranslationEntity } from '../../src/tables/translations/actionTranslationsTable';
import { QuestTranslationEntity } from '../../src/tables/translations/questTranslationsTable';
import { RewardTranslationEntity } from '../../src/tables/translations/rewardTranslationsTable';
import { PointComponentRepository } from '../../src/repositories/pointComponentRepository';
import { PointType } from '../../src/enum/pointType';
import { RewardType } from '../../src/enum/rewardType';

jest.mock('uuid', () => ({
  v4: jest.fn().mockReturnValue('mock-uuid'),
}));

jest.mock('../../src/repositories/questRepository', () => ({
  QuestRepository: jest.fn().mockImplementation(() => {
    return {
      selectQuests: jest.fn(),
      selectQuestById: jest.fn(),
      selectQuestsWithRewards: jest.fn(),
      selectActiveQuest: jest.fn(),
      selectActiveStatusQuest: jest.fn(),
      selectQuestByActionId: jest.fn(),
      insertQuest: jest.fn(),
      insertQuestTranslations: jest.fn(),
      insertActionTranslation: jest.fn(),
    };
  }),
}));

jest.mock('uuid', () => ({
  v4: jest.fn().mockReturnValue('mock-uuid'),
}));

const mockTrx = {
  commit: jest.fn(),
  rollback: jest.fn(),
};
jest.mock('../../src/db/database', () => {
  return {
    db: {
      selectFrom: jest.fn(),
      transaction: jest.fn().mockReturnValue({
        execute: jest.fn().mockImplementation(async (callback) => {
          return await callback(mockTrx);
        }),
      }),
    },
  };
});

jest.mock('../../src/repositories/actionRepository.ts');
jest.mock('../../src/repositories/rewardRepository');
jest.mock('../../src/repositories/questActionRepository');
jest.mock('../../src/repositories/actionActivityRepository');
jest.mock('../../src/repositories/questRewardRepository');
jest.mock('../../src/repositories/claimedRewardRepository');
jest.mock('../../src/repositories/serviceInfoRepository');
jest.mock('../../src/repositories/nftContractTypesRepository');
jest.mock('../../src/repositories/qrCheckinActionRepository');
jest.mock('../../src/repositories/onlineCheckinActionRepository');
jest.mock('../../src/repositories/achievementActionRepository');
jest.mock('../../src/repositories/questionnaireRepository');
jest.mock('../../src/repositories/contentPurchaseActionRepository');
jest.mock('../../src/repositories/questionnaireResultRankRepository');
jest.mock('../../src/repositories/questionnaireActionRepository');
jest.mock('../../src/services/nftRegisterService');
jest.mock('../../src/repositories/nftComponentRepository');
jest.mock('../../src/repositories/rewardComponentRepository');
jest.mock('../../src/repositories/pointComponentRepository');
jest.mock('../../src/components/redisComponent');

describe('QuestService', () => {
  let questService: QuestService;
  let mockQuestRepository: jest.Mocked<QuestRepository>;
  let mockActionRepository: jest.Mocked<ActionRepository>;
  let mockRewardRepository: jest.Mocked<RewardRepository>;
  let mockQuestActionRepository: jest.Mocked<QuestActionRepository>;
  let mockActionActivityRepository: jest.Mocked<ActionActivityRepository>;
  let mockClaimedRewardRepository: jest.Mocked<ClaimedRewardRepository>;
  let mockServiceInfoRepository: jest.Mocked<ServiceInfoRepository>;
  let mockNftRegisterService: jest.Mocked<NftRegisterService>;
  let mockQrCheckinActionRepository: jest.Mocked<QrCheckinActionRepository>;
  let mockOnlineCheckinActionRepository: jest.Mocked<OnlineCheckinActionRepository>;
  let mockAchievementActionRepository: jest.Mocked<AchievementActionRepository>;
  let mockSerialCodeActionRepository: jest.Mocked<SerialCodeActionRepository>;
  let mockQuestionnaireResultRankRepository: jest.Mocked<QuestionnaireResultRankRepository>;
  let mockQuestionnaireActionRepository: jest.Mocked<QuestionnaireActionRepository>;
  let mockNftComponentRepository: jest.Mocked<NftComponentRepository>;
  let mockRewardComponentRepository: jest.Mocked<RewardComponentRepository>;
  let mockPointComponentRepository: jest.Mocked<PointComponentRepository>;

  beforeEach(() => {
    process.env.ALCHEMY_ADDRESS_ACTIVITY_WEBHOOK_ID = 'mockAddressActivityWebhookId';
    process.env.ALCHEMY_NFT_ACTIVITY_WEBHOOK_ID = 'mockNftActivityWebhookId';
    process.env.ALCHEMY_ADDRESS_ACTIVITY_WEBHOOK_SIGNING_KEY = 'mockSigningKey';
    process.env.ALCHEMY_NFT_ACTIVITY_WEBHOOK_SIGNING_KEY = 'mockSigningKey';
    process.env.ALCHEMY_CHAIN_NAME = 'polygon-amoy';
    process.env.ALCHEMY_API_KEY = 'xxxxxsss';
    process.env.ALCHEMY_AUTH_TOKEN = 'mockAuthToken';
    process.env.GCP_PROJECT_ID = 'sample_gcp_project_id';
    process.env.GCS_BUCKET_NAME = 'mock-bucket-name';

    mockQuestRepository = new QuestRepository() as jest.Mocked<QuestRepository>;
    mockActionRepository = new ActionRepository() as jest.Mocked<ActionRepository>;
    mockRewardRepository = new RewardRepository() as jest.Mocked<RewardRepository>;
    mockQuestActionRepository = new QuestActionRepository() as jest.Mocked<QuestActionRepository>;
    mockActionActivityRepository = new ActionActivityRepository() as jest.Mocked<ActionActivityRepository>;
    mockClaimedRewardRepository = new ClaimedRewardRepository() as jest.Mocked<ClaimedRewardRepository>;
    mockServiceInfoRepository = new ServiceInfoRepository() as jest.Mocked<ServiceInfoRepository>;
    mockQuestionnaireResultRankRepository =
      new QuestionnaireResultRankRepository() as jest.Mocked<QuestionnaireResultRankRepository>;

    mockNftRegisterService = new NftRegisterService(
      {} as TransactionService,
      {} as WebhookService,
      {} as NftContractTypesRepository,
      {} as NftContractsRepository,
      {} as VaultKeyRepository,
      {} as ServiceInfoRepository,
      {} as MetadataService,
      {} as NftBaseMetadatasRepository,
      {} as TransactionQueuesRepository,
      {} as TransactionsRepository,
    ) as jest.Mocked<NftRegisterService>;
    mockQrCheckinActionRepository = new QrCheckinActionRepository() as jest.Mocked<QrCheckinActionRepository>;
    mockOnlineCheckinActionRepository =
      new OnlineCheckinActionRepository() as jest.Mocked<OnlineCheckinActionRepository>;
    mockAchievementActionRepository = new AchievementActionRepository() as jest.Mocked<AchievementActionRepository>;
    mockQuestionnaireActionRepository =
      new QuestionnaireActionRepository() as jest.Mocked<QuestionnaireActionRepository>;

    mockSerialCodeActionRepository = new SerialCodeActionRepository() as jest.Mocked<SerialCodeActionRepository>;
    mockNftComponentRepository = new NftComponentRepository() as jest.Mocked<NftComponentRepository>;
    mockRewardComponentRepository = new RewardComponentRepository() as jest.Mocked<RewardComponentRepository>;
    mockPointComponentRepository = new PointComponentRepository() as jest.Mocked<PointComponentRepository>;

    questService = new QuestService(
      mockQuestRepository,
      mockActionRepository,
      mockRewardRepository,
      mockQuestActionRepository,
      mockActionActivityRepository,
      mockClaimedRewardRepository,
      mockServiceInfoRepository,
      mockNftRegisterService,
      mockQrCheckinActionRepository,
      mockOnlineCheckinActionRepository,
      mockAchievementActionRepository,
      mockQuestionnaireActionRepository,
      mockSerialCodeActionRepository,
      mockQuestionnaireResultRankRepository,
      mockNftComponentRepository,
      mockRewardComponentRepository,
      mockPointComponentRepository,
    );
  });
  describe('method getQuests()', () => {
    test('should return quests with correct mapping', async () => {
      const mockServiceId = 'test-service-id';
      const mockQuests: QuestWithReward[] = [
        {
          quest_id: 'quest1',
          quest_title: 'Quest 1',
          quest_type: QuestType.CUSTOM,
          reward_title: 'Reward 1',
          reward_thumbnail_image_url: 'http://example.com/thumb1.jpg',
          quest_thumbnail_image_url: 'http://example.com/thumb1.jpg',
          quest_available_start_date: new Date('2022-12-31T17:00:00Z'),
          quest_available_end_date: new Date('2023-12-31T16:59:59Z'),
          quest_reward_priority_type: 'MAIN',
          reward_id: 'reward1',
          order_index: 1,
        },
        {
          quest_id: 'quest2',
          quest_title: 'Quest 2',
          quest_type: QuestType.CUSTOM,
          reward_title: 'Reward 2',
          reward_thumbnail_image_url: 'http://example.com/thumb2.jpg',
          quest_thumbnail_image_url: 'http://example.com/thumb2.jpg',
          quest_available_start_date: new Date('2022-12-31T17:00:00Z'),
          quest_available_end_date: new Date('2023-12-31T16:59:59Z'),
          quest_reward_priority_type: 'SUB',
          reward_id: 'reward2',
          order_index: 2,
        },
      ];

      const mockQuestResult: QuestListItemResponse[] = [
        {
          questId: 'quest1',
          title: 'Quest 1',
          questType: QuestType.CUSTOM,
          mainRewardTitle: 'Reward 1',
          thumbnailImageUrl: 'http://example.com/thumb1.jpg',
          mainRewardThumbnailImageUrl: 'http://example.com/thumb1.jpg',
          orderIndex: 1,
          startedAt: '2022-12-31T17:00:00.000Z',
          expiredAt: '2023-12-31T16:59:59.000Z',
        },
        {
          questId: 'quest2',
          title: 'Quest 2',
          questType: QuestType.CUSTOM,
          mainRewardTitle: 'Reward 2',
          thumbnailImageUrl: 'http://example.com/thumb2.jpg',
          mainRewardThumbnailImageUrl: 'http://example.com/thumb2.jpg',
          orderIndex: 2,
          startedAt: '2022-12-31T17:00:00.000Z',
          expiredAt: '2023-12-31T16:59:59.000Z',
        },
      ];

      mockQuestRepository.selectQuestsWithRewards.mockResolvedValue(mockQuests);

      const result = await questService.getQuests(mockServiceId, languageCode.EN_US);

      expect(mockQuestRepository.selectQuestsWithRewards).toHaveBeenCalledWith(mockServiceId, languageCode.EN_US);
      expect(result).toEqual(mockQuestResult);
    });

    test('should handle empty quest list', async () => {
      const mockServiceId = 'test-service-id';
      mockQuestRepository.selectQuestsWithRewards.mockResolvedValue([]);

      const result = await questService.getQuests(mockServiceId, languageCode.EN_US);

      expect(result).toEqual([]);
    });

    test('should prioritize MAIN quests over SUB quests', async () => {
      const mockServiceId = 'test-service-id';
      const mockQuests: QuestWithReward[] = [
        {
          quest_id: 'quest1',
          quest_title: 'Quest 1.2',
          quest_type: QuestType.CUSTOM,
          reward_title: 'Reward 1.2',
          quest_thumbnail_image_url: 'http://example.com/thumb1.jpg',
          reward_thumbnail_image_url: 'http://example.com/thumb1.jpg',
          quest_available_start_date: new Date('2022-12-31T17:00:00Z'),
          quest_available_end_date: new Date('2023-12-31T16:59:59Z'),
          quest_reward_priority_type: 'SUB',
          reward_id: 'reward2',
          order_index: 1,
        },
        {
          quest_id: 'quest1',
          quest_title: 'Quest 1',
          quest_type: QuestType.CUSTOM,
          reward_title: 'Reward 1',
          quest_thumbnail_image_url: 'http://example.com/thumb1.jpg',
          reward_thumbnail_image_url: 'http://example.com/thumb1.jpg',
          quest_available_start_date: new Date('2022-12-31T17:00:00Z'),
          quest_available_end_date: new Date('2023-12-31T16:59:59Z'),
          quest_reward_priority_type: 'MAIN',
          reward_id: 'reward1',
          order_index: 2,
        },
      ];

      const mockQuestResult: QuestListItemResponse[] = [
        {
          questId: 'quest1',
          title: 'Quest 1',
          questType: QuestType.CUSTOM,
          mainRewardTitle: 'Reward 1',
          thumbnailImageUrl: 'http://example.com/thumb1.jpg',
          mainRewardThumbnailImageUrl: 'http://example.com/thumb1.jpg',
          orderIndex: 2,
          startedAt: '2022-12-31T17:00:00.000Z',
          expiredAt: '2023-12-31T16:59:59.000Z',
        },
      ];

      mockQuestRepository.selectQuestsWithRewards.mockResolvedValue(mockQuests);

      const result = await questService.getQuests(mockServiceId, languageCode.EN_US);

      expect(result).toEqual(mockQuestResult);
    });

    test('should handle all SUB quests with different reward_ids', async () => {
      const mockServiceId = 'test-service-id';
      const mockQuests = [
        {
          quest_id: 'quest1',
          quest_title: 'Quest 1.1',
          quest_type: QuestType.CUSTOM,
          reward_title: 'Reward 1.1',
          quest_thumbnail_image_url: 'http://example.com/thumb1.jpg',
          reward_thumbnail_image_url: 'http://example.com/thumb1.jpg',
          quest_available_start_date: new Date('2022-12-31T17:00:00Z'),
          quest_available_end_date: new Date('2023-12-31T16:59:59Z'),
          quest_reward_priority_type: 'SUB',
          reward_id: 'reward1',
          order_index: 1,
        },
        {
          quest_id: 'quest1',
          quest_title: 'Quest 1.2',
          quest_type: QuestType.CUSTOM,
          reward_title: 'Reward 1.2',
          quest_thumbnail_image_url: 'http://example.com/thumb1.jpg',
          reward_thumbnail_image_url: 'http://example.com/thumb1.jpg',
          quest_available_start_date: new Date('2022-12-31T17:00:00Z'),
          quest_available_end_date: new Date('2023-12-31T16:59:59Z'),
          quest_reward_priority_type: 'SUB',
          reward_id: 'reward2',
          order_index: 2,
        },
      ];

      const mockQuestResult: QuestListItemResponse[] = [
        {
          questId: 'quest1',
          title: 'Quest 1.1',
          questType: QuestType.CUSTOM,
          mainRewardTitle: 'Reward 1.1',
          thumbnailImageUrl: 'http://example.com/thumb1.jpg',
          mainRewardThumbnailImageUrl: 'http://example.com/thumb1.jpg',
          orderIndex: 1,
          startedAt: '2022-12-31T17:00:00.000Z',
          expiredAt: '2023-12-31T16:59:59.000Z',
        },
      ];

      mockQuestRepository.selectQuestsWithRewards.mockResolvedValue(mockQuests);

      const result = await questService.getQuests(mockServiceId, languageCode.EN_US);

      expect(result).toEqual(mockQuestResult);
    });
  });

  describe('method getQuest()', () => {
    test('success case - get quest by ID', async () => {
      const mockQuestId = 'quest1';
      const mockServiceId = 'test-service-id';
      const mockQuest: QuestWithTranslations = {
        quest_id: 'quest1',
        service_id: 'test-service-id',
        quest_title: 'Quest 1',
        quest_description: 'Description of Quest 1',
        quest_cover_image_url: 'http://example.com/cover1.jpg',
        quest_thumbnail_image_url: 'http://example.com/thumb1.jpg',
        quest_available_start_date: new Date('2022-12-31T17:00:00Z'),
        quest_available_end_date: new Date('2023-12-31T16:59:59Z'),
        quest_type: QuestType.STATUS,
        order_index: 1,
      };

      const mockRewards: ExtendedQuestReward[] = [
        {
          reward_id: 'reward1',
          reward_title: 'Reward 1',
          reward_thumbnail_image_url: 'http://example.com/reward1.jpg',
          quest_reward_priority_type: QuestRewardPriorityType.MAIN,
          rank_id: undefined,
          gacha_weight: undefined,
          order_index: 1,
        },
      ];
      const mockActions: ActionWithTranslations[] = [
        {
          action_id: 'action1',
          service_id: 'test-service-id',
          action_title: 'Action 1',
          action_description: 'Description of Action 1',
          action_cover_image_url: 'http://example.com/cover1.jpg',
          action_thumbnail_image_url: 'http://example.com/action1.jpg',
          action_label: 'Label 1',
          action_available_start_date: new Date('2022-12-31T17:00:00Z'),
          action_available_end_date: new Date('2023-12-31T16:59:59Z'),
          action_type: ActionType.ACHIEVEMENT,
          geofence_id: 'geofence1',
          order_index: 1,
        },
      ];

      const mockQuestDetail: QuestDetailResponse = {
        questId: 'quest1',
        title: 'Quest 1',
        description: 'Description of Quest 1',
        coverImageUrl: 'http://example.com/cover1.jpg',
        startedAt: new Date('2022-12-31T17:00:00.000Z').toISOString(),
        expiredAt: new Date('2023-12-31T16:59:59.000Z').toISOString(),
        questType: QuestType.STATUS,
        rewards: [
          {
            rewardId: 'reward1',
            title: 'Reward 1',
            thumbnailImageUrl: 'http://example.com/reward1.jpg',
            rewardPriorityType: QuestRewardPriorityType.MAIN,
            orderIndex: 1,
          },
        ],
        actions: [
          {
            actionId: 'action1',
            title: 'Action 1',
            availableStartDate: new Date('2022-12-31T17:00:00Z').toISOString(),
            availableEndDate: new Date('2023-12-31T16:59:59Z').toISOString(),
            thumbnailImageUrl: 'http://example.com/action1.jpg',
            actionType: ActionType.ACHIEVEMENT,
            orderIndex: 1,
          },
        ],
      };

      mockQuestRepository.selectQuestById.mockResolvedValue(mockQuest);
      mockRewardRepository.selectRewardsByQuestId.mockResolvedValue(mockRewards);
      mockActionRepository.selectActionsByQuestId.mockResolvedValue(mockActions);

      const result = await questService.getQuest(mockQuestId, mockServiceId, languageCode.EN_US);

      expect(result).toEqual(mockQuestDetail);
    });

    test('failure case - quest not found', async () => {
      const mockQuestId = 'nonexistentQuestId';
      const mockServiceId = 'test-service-id';

      mockQuestRepository.selectQuestById.mockResolvedValue(undefined);

      await expect(questService.getQuest(mockQuestId, mockServiceId, languageCode.EN_US)).rejects.toThrow(
        NotFoundError,
      );
    });
  });

  describe('method createQuest()', () => {
    test('should create a new quest successfully', async () => {
      const serviceId = 'test-service-id';
      const tenantId = 'test-tenant-id';
      const questData: QuestCreateRequest = {
        questTranslations: [
          {
            language: languageCode.JA,
            title: 'Test Quest',
            description: 'A quest description',
          },
        ],
        thumbnailImageUrl: 'http://example.com/quests/icons/mock-uuid.png',
        coverImageUrl: 'http://example.com/quests/covers/mock-uuid.png',
        availableStartDate: '2024-01-01T00:00:00Z',
        availableEndDate: '2024-12-31T23:59:59Z',
        questType: QuestType.CUSTOM,
        orderIndex: 1,
      };
      const mockService: ServiceEntity = {
        service_id: serviceId,
        tenant_id: tenantId,
        service_url: 'https://test-service.com',
        service_logo_image_url: 'https://marbullx.com/logo',
        market_cover_image_url: 'https://example.com/market-cover.jpg',
        theme_primary_color_lowest: '0xFFFFFFFFFFFF',
        theme_primary_color_lower: '0xFFFFFFFFFFFF',
        theme_primary_color_higher: '0xFFFFFFFFFFFF',
        theme_primary_color_highest: '0xFFFFFFFFFFFF',
        is_market_enabled: true,
        membership_nft_contract_id: '0x1234567768',
        stripe_account_id: 'mockStripeAccountId',
        line_channel_id: 'line_channel_id',
        commission_rate: 1,
        modular_contract_id: '0x1234567768',
      };
      const generatedQuestId = 'mock-uuid';

      const mockQuestEntity: QuestEntity = {
        quest_id: generatedQuestId,
        service_id: serviceId,
        quest_cover_image_url: 'http://example.com/quests/covers/mock-uuid.png',
        quest_thumbnail_image_url: 'http://example.com/quests/icons/mock-uuid.png',
        quest_available_start_date: new Date(questData.availableStartDate),
        quest_available_end_date: new Date(questData.availableEndDate),
        quest_type: questData.questType,
        order_index: 1,
      };

      const mockQuestTranslations: QuestTranslationEntity[] = [
        {
          quest_id: generatedQuestId,
          service_id: serviceId,
          language: languageCode.JA,
          quest_title: questData.questTranslations[0].title,
          quest_description: questData.questTranslations[0].description,
        },
      ];
      const mockQuestResult: QuestCreateResponse = {
        questId: generatedQuestId,
        questTranslations: [
          {
            language: languageCode.JA,
            title: questData.questTranslations[0].title,
            description: questData.questTranslations[0].description,
          },
        ],
        coverImageUrl: 'http://example.com/quests/covers/mock-uuid.png',
        thumbnailImageUrl: 'http://example.com/quests/icons/mock-uuid.png',
        availableStartDate: new Date(questData.availableStartDate).toISOString(),
        availableEndDate: new Date(questData.availableEndDate).toISOString(),
        questType: questData.questType,
        orderIndex: 1,
      };

      mockServiceInfoRepository.getServiceById.mockResolvedValue(mockService);
      mockQuestRepository.insertQuest.mockResolvedValue(mockQuestEntity);
      mockQuestRepository.insertQuestTranslations.mockResolvedValue(mockQuestTranslations);
      const result = await questService.createQuest(serviceId, questData);

      expect(mockServiceInfoRepository.getServiceById).toHaveBeenCalledWith(serviceId);
      expect(mockQuestRepository.insertQuest).toHaveBeenCalledWith(mockQuestEntity);
      expect(result).toEqual(mockQuestResult);
    });
  });

  describe('method getAccountQuest()', () => {
    const mockAccountId = 'account1';
    const mockQuestId = 'quest1';
    const mockServiceId = 'test-service-id';

    test('should return quest details with status, claimed rewards, and cleared actions', async () => {
      const mockQuest: QuestWithTranslations = {
        quest_id: mockQuestId,
        service_id: mockServiceId,
        quest_title: 'Sample Quest',
        quest_description: 'This is a sample quest description.',
        quest_cover_image_url: 'http://example.com/cover.jpg',
        quest_thumbnail_image_url: 'http://example.com/thumbnail.jpg',
        quest_available_start_date: new Date('2023-01-01T00:00:00Z'),
        quest_available_end_date: new Date('2023-12-31T23:59:59Z'),
        quest_type: QuestType.STATUS,
        order_index: 1,
      };
      const mockQuestActions = [
        { quest_id: mockQuestId, action_id: 'action1', service_id: mockServiceId },
        { quest_id: mockQuestId, action_id: 'action2', service_id: mockServiceId },
        { quest_id: mockQuestId, action_id: 'action3', service_id: mockServiceId },
        { quest_id: mockQuestId, action_id: 'action4', service_id: mockServiceId },
        { quest_id: mockQuestId, action_id: 'action5', service_id: mockServiceId },
      ];

      mockQuestRepository.selectQuestById.mockResolvedValue(mockQuest);
      mockQuestActionRepository.selectQuestActionsByQuestId.mockResolvedValue(mockQuestActions);
      mockClaimedRewardRepository.selectAllClaimedRewardByClearedQuests.mockResolvedValue(['reward1', 'reward2']);
      mockActionActivityRepository.selectAllActionIdByCompletedAction.mockResolvedValue([
        'action1',
        'action2',
        'action3',
        'action4',
        'action5',
      ]);

      const result = await questService.getAccountQuest(mockAccountId, mockQuestId, mockServiceId, languageCode.JA);

      expect(mockQuestActionRepository.selectQuestActionsByQuestId).toHaveBeenCalledWith(mockQuestId, mockServiceId);
      expect(mockClaimedRewardRepository.selectAllClaimedRewardByClearedQuests).toHaveBeenCalledWith(
        mockAccountId,
        mockQuestId,
        mockServiceId,
      );
      expect(mockActionActivityRepository.selectAllActionIdByCompletedAction).toHaveBeenCalledWith(
        mockAccountId,
        mockQuestId,
        mockServiceId,
      );

      expect(result).toEqual({
        questStatus: 'COMPLETED',
        claimedRewardIds: ['reward1', 'reward2'],
        clearedActionIds: ['action1', 'action2', 'action3', 'action4', 'action5'],
      });
    });

    test('should throw NotFoundError if quest does not exist', async () => {
      mockQuestRepository.selectQuestById.mockResolvedValue(undefined);

      await expect(
        questService.getAccountQuest(mockAccountId, mockQuestId, mockServiceId, languageCode.JA),
      ).rejects.toThrow(NotFoundError);
    });

    test('should handle errors gracefully in getAccountQuest', async () => {
      const mockError = new Error('Test error');
      mockQuestRepository.selectQuestById.mockRejectedValue(mockError);

      await expect(
        questService.getAccountQuest(mockAccountId, mockQuestId, mockServiceId, languageCode.JA),
      ).rejects.toThrow('Test error');
    });
  });

  describe('method createQuestAction()', () => {
    const serviceId = 'test-service-id';
    const tenantId = 'test-tenant-id';
    const questId = 'test-quest-id';
    const createRequest: CreateActionRequest = {
      actionTranslations: [
        {
          language: languageCode.JA,
          title: 'Test Action',
          description: 'Test action description',
        },
      ],
      thumbnailImageUrl: 'http://example.com/thumb1.jpg',
      coverImageUrl: 'http://example.com/cover1.jpg',
      availableStartDate: '2024-01-01T00:00:00Z',
      availableEndDate: '2024-12-31T23:59:59Z',
      geofenceId: 'test-geofence-id',
      actionDetailInfo: { type: ActionType.ACHIEVEMENT, rewardId: 'test-reward-id', milestone: 10, statusRank: 1 },
      orderIndex: 1,
    };

    const mockServiceEntity: ServiceEntity = {
      service_id: serviceId,
      tenant_id: tenantId,
      service_url: 'https://test-service.com',
      service_logo_image_url: 'https://marbullx.com/logo',
      market_cover_image_url: 'https://example.com/market-cover.jpg',
      theme_primary_color_lowest: '0xFFFFFFFFFFFF',
      theme_primary_color_lower: '0xFFFFFFFFFFFF',
      theme_primary_color_higher: '0xFFFFFFFFFFFF',
      theme_primary_color_highest: '0xFFFFFFFFFFFF',
      is_market_enabled: true,
      membership_nft_contract_id: '0x1234567768',
      stripe_account_id: 'mockStripeAccountId',
      line_channel_id: 'line_channel_id',
      commission_rate: 1,
      modular_contract_id: '0x1234567768',
    };

    const mockQuest: QuestWithTranslations = {
      quest_id: questId,
      service_id: serviceId,
      quest_title: 'Quest 1',
      quest_description: 'Description of Quest 1',
      quest_cover_image_url: 'http://example.com/cover1.jpg',
      quest_thumbnail_image_url: 'http://example.com/thumb1.jpg',
      quest_available_start_date: new Date('2024-01-01'),
      quest_available_end_date: new Date('2024-12-31'),
      quest_type: QuestType.STATUS,
      order_index: 1,
    };

    const mockReward: RewardEntity = {
      reward_id: 'test-reward-id',
      service_id: serviceId,
      reward_cover_image_url: 'http://example.com/reward1_cover.jpg',
      reward_thumbnail_image_url: 'http://example.com/reward1_thumbnail.jpg',
      order_index: 1,
    };

    const mockActionId = 'mock-uuid';

    const mockAction: ActionEntity = {
      service_id: mockServiceEntity.service_id,
      action_id: mockActionId,
      action_cover_image_url: 'http://example.com/cover1.jpg',
      action_thumbnail_image_url: 'http://example.com/thumb1.jpg',
      action_available_start_date: new Date(createRequest.availableStartDate),
      action_available_end_date: new Date(createRequest.availableEndDate),
      action_type: ActionType.ACHIEVEMENT,
      geofence_id: createRequest.geofenceId || undefined,
      order_index: 1,
    };

    const mockActionWithTranslations: ActionTranslationEntity[] = [
      {
        action_id: mockActionId,
        service_id: serviceId,
        language: languageCode.JA,
        action_title: createRequest.actionTranslations[0].title,
        action_description: createRequest.actionTranslations[0].description,
        action_label: 'Test Label',
      },
    ];

    test('should create a quest action successfully with ACHIEVEMENT type', async () => {
      mockServiceInfoRepository.getServiceById.mockResolvedValue(mockServiceEntity);
      mockQuestRepository.selectQuestById.mockResolvedValue(mockQuest);
      mockRewardRepository.selectRewardById.mockResolvedValue(mockReward);
      mockActionRepository.insertAction.mockResolvedValue(mockAction);
      mockActionRepository.insertActionTranslation.mockResolvedValue(mockActionWithTranslations);
      mockQuestActionRepository.insertQuestAction.mockResolvedValue({
        quest_id: questId,
        action_id: mockActionId,
        service_id: serviceId,
      });

      const result = await questService.createQuestAction(serviceId, questId, createRequest);

      expect(result).toHaveProperty('actionId');
      expect(mockQuestRepository.selectQuestById).toHaveBeenCalledWith(questId, serviceId, languageCode.JA);
      expect(mockRewardRepository.selectRewardById).toHaveBeenCalledWith(mockReward.reward_id, serviceId);
      expect(mockActionRepository.insertAction).toHaveBeenCalledWith(serviceId, mockAction);
      expect(mockQuestActionRepository.insertQuestAction).toHaveBeenCalledWith({
        quest_id: questId,
        action_id: 'mock-uuid',
        service_id: serviceId,
      });
    });

    test('should create a quest action successfully with QR_CHECKIN type', async () => {
      createRequest.actionDetailInfo = { type: ActionType.QR_CHECKIN, qrVerificationData: 'test-qr-data' };

      mockServiceInfoRepository.getServiceById.mockResolvedValue(mockServiceEntity);
      mockQuestRepository.selectQuestById.mockResolvedValue(mockQuest);

      const mockQRAction = {
        action_id: mockActionId,
        service_id: serviceId,
        qr_verification_data: 'test-qr-data',
      };
      mockAction.action_type = ActionType.QR_CHECKIN;

      mockActionRepository.insertAction.mockResolvedValue(mockAction);
      mockActionRepository.insertActionTranslation.mockResolvedValue(mockActionWithTranslations);
      mockQuestActionRepository.insertQuestAction.mockResolvedValue({
        quest_id: questId,
        action_id: mockActionId,
        service_id: serviceId,
      });
      mockQrCheckinActionRepository.insertQrCheckinAction.mockResolvedValue(mockQRAction);

      const result = await questService.createQuestAction(serviceId, questId, createRequest);

      expect(result).toHaveProperty('actionId');
      expect(mockActionRepository.insertAction).toHaveBeenCalledWith(serviceId, mockAction);
      expect(mockQrCheckinActionRepository.insertQrCheckinAction).toHaveBeenCalledWith(serviceId, mockQRAction);
    });

    test('should create a quest action successfully with ONLINE_CHECKIN type', async () => {
      createRequest.actionDetailInfo = { type: ActionType.ONLINE_CHECKIN, targetUrl: 'http://example.com/checkin' };

      mockServiceInfoRepository.getServiceById.mockResolvedValue(mockServiceEntity);
      mockQuestRepository.selectQuestById.mockResolvedValue(mockQuest);

      const mockOnlineCheckinAction = {
        action_id: mockActionId,
        service_id: serviceId,
        online_checkin_url: 'http://example.com/checkin',
      };
      mockAction.action_type = ActionType.ONLINE_CHECKIN;
      mockActionRepository.insertAction.mockResolvedValue(mockAction);
      mockActionRepository.insertActionTranslation.mockResolvedValue(mockActionWithTranslations);
      mockQuestActionRepository.insertQuestAction.mockResolvedValue({
        quest_id: questId,
        action_id: mockActionId,
        service_id: serviceId,
      });
      mockOnlineCheckinActionRepository.insertOnlineCheckinAction.mockResolvedValue(mockOnlineCheckinAction);

      const result = await questService.createQuestAction(serviceId, questId, createRequest);

      expect(result).toHaveProperty('actionId');
      expect(mockActionRepository.insertAction).toHaveBeenCalledWith(serviceId, mockAction);
      expect(mockOnlineCheckinActionRepository.insertOnlineCheckinAction).toHaveBeenCalledWith(
        serviceId,
        mockOnlineCheckinAction,
      );
    });

    test('should create a quest action successfully with QUESTIONNAIRE type', async () => {
      createRequest.actionDetailInfo = { type: ActionType.QUESTIONNAIRE, questionnaireId: 'test-questionnaire-id' };

      mockServiceInfoRepository.getServiceById.mockResolvedValue(mockServiceEntity);
      mockQuestRepository.selectQuestById.mockResolvedValue(mockQuest);

      const mockQuestionnaireAction = {
        action_id: mockActionId,
        service_id: serviceId,
        questionnaire_id: 'test-questionnaire-id',
      };
      mockAction.action_type = ActionType.QUESTIONNAIRE;
      mockActionRepository.insertAction.mockResolvedValue(mockAction);
      mockActionRepository.insertActionTranslation.mockResolvedValue(mockActionWithTranslations);
      mockQuestActionRepository.insertQuestAction.mockResolvedValue({
        quest_id: questId,
        action_id: mockActionId,
        service_id: serviceId,
      });
      mockQuestionnaireActionRepository.insertQuestionnaireAction.mockResolvedValue(undefined);

      const result = await questService.createQuestAction(serviceId, questId, createRequest);

      expect(result).toHaveProperty('actionId');
      expect(mockActionRepository.insertAction).toHaveBeenCalledWith(serviceId, mockAction);
      expect(mockQuestionnaireActionRepository.insertQuestionnaireAction).toHaveBeenCalledWith(
        serviceId,
        mockQuestionnaireAction,
      );
    });

    test('should throw ValidationError if serviceId does not match tenantId', async () => {
      const mockAnotherService: ServiceEntity = {
        service_id: serviceId,
        tenant_id: 'another-tenant-id',
        service_url: 'https://test-service.com',
        service_logo_image_url: 'https://marbullx.com/logo',
        market_cover_image_url: 'https://example.com/market-cover.jpg',
        theme_primary_color_lowest: '0xFFFFFFFFFFFF',
        theme_primary_color_lower: '0xFFFFFFFFFFFF',
        theme_primary_color_higher: '0xFFFFFFFFFFFF',
        theme_primary_color_highest: '0xFFFFFFFFFFFF',
        is_market_enabled: true,
        membership_nft_contract_id: '0x1234567768',
        stripe_account_id: 'mockStripeAccountId',
        line_channel_id: 'line_channel_id',
        commission_rate: 1,
        modular_contract_id: '0x1234567768',
      };
      mockServiceInfoRepository.getServiceById.mockResolvedValue(mockAnotherService);

      await expect(questService.createQuestAction(serviceId, questId, createRequest)).rejects.toThrow(NotFoundError);
    });

    test('should throw NotFoundError if questId does not exist', async () => {
      mockServiceInfoRepository.getServiceById.mockResolvedValue(mockServiceEntity);
      mockQuestRepository.selectQuestById.mockResolvedValue(undefined);

      await expect(questService.createQuestAction(serviceId, questId, createRequest)).rejects.toThrow(NotFoundError);
    });

    test('should throw ValidationError if rewardId is invalid', async () => {
      createRequest.actionDetailInfo = {
        type: ActionType.ACHIEVEMENT,
        rewardId: 'test-reward-id',
        milestone: 10,
        statusRank: 1,
      };
      mockServiceInfoRepository.getServiceById.mockResolvedValue(mockServiceEntity);
      mockQuestRepository.selectQuestById.mockResolvedValue(mockQuest);
      mockRewardRepository.selectRewardById.mockRejectedValue(new InternalServerError());

      await expect(questService.createQuestAction(serviceId, questId, createRequest)).rejects.toThrow(
        InternalServerError,
      );
    });
  });

  describe('createQuestReward()', () => {
    const serviceId = 'test-service-id';
    const tenantId = 'test-tenant-id';
    const questId = 'test-quest-id';
    const generatedRewardId = 'mock-uuid';

    const requestBody: CreateQuestRewardRequest = {
      nfts: [
        {
          nftName: 'ValidNFT',
          nftSymbol: 'VNFT',
          nftCollectionName: 'ValidCollection',
          nftMetadata:
            'eyJpbWFnZSI6ICJodHRwczovL2V4YW1wbGUuY29tL2ltYWdlLnBuZyIsICJleHRlcm5hbF91cmwiOiAiaHR0cHM6Ly9leGFtcGxlLmNvbSIsICJkZXNjcmlwdGlvbiI6ICIbJEIkMyRsJE9ANSQ3JCRGfEtcOGwkRyQ5ISMbKEIiLCAibmFtZSI6ICIbJEIkMyRsJE9ANSQ3JCRGfEtcOGwkRyQ5ISMbKEIiLCAiYXR0cmlidXRlcyI6IFt7ICJ0cmFpdF90eXBlIjogIkNvbG9yIiwgInZhbHVlIjogIkJsdWUifSwgeyAidHJhaXRfdHlwZSI6ICJTaXplIiwgInZhbHVlIjogIkxhcmdlIn1dfQ==',
          deliveryImageUrl: 'https://aa.example.com',
          nftContractType: RewardType.CERTIFICATE,
          nftContractTypeId: '123e4567-e89b-12d3-a456-************',
          certificateReward: {
            certificateType: CertificateType.STANDARD,
            statusCertificateRank: 1,
          },
        },
      ],
      rewardTranslations: [
        {
          language: languageCode.EN_US,
          title: 'Valid-Title',
          description: btoa('# Valid Markdown description'),
        },
      ],
      thumbnailImageUrl: 'http://example.com/quests/icons/1.png',
      coverImageUrl: 'http://example.com/quests/covers/1.png',
      acquirementType: RewardAcquirementType.DISTRIBUTION,
      questRewardPriorityType: QuestRewardPriorityType.MAIN,
      orderIndex: 1,
      points: [
        {
          pointType: PointType.REWARD,
          amount: 100,
          expireDate: new Date('2026-12-31T23:59:59Z').toISOString(),
        },
        {
          pointType: PointType.STATUS,
          amount: 200,
        },
      ],
    };
    // Mock data for TenantsTable
    const mockTenant = {
      tenant_id: tenantId,
      tenant_name: 'Mock Tenant',
      plan_id: 'basic-plan',
    };

    // Mock data for ServicesTable
    const mockService = {
      service_id: serviceId,
      tenant_id: mockTenant.tenant_id,
      service_name: 'Mock Service',
      service_url: 'https://mockservice.com',
      service_logo_image_url: 'https://mockservice.com/logo.png',
      market_cover_image_url: 'https://mockservice.com/cover.jpg',
      theme_primary_color_lowest: '#e0f7fa',
      theme_primary_color_lower: '#b2ebf2',
      theme_primary_color_higher: '#4dd0e1',
      theme_primary_color_highest: '#0097a7',
      membership_nft_contract_id: '123e4567-e89b-12d3-a456-************',
      service_policy: 'Mock policy text.',
      service_pane: 'Mock service pane.',
      is_market_enabled: true,
      stripe_account_id: 'acct_mockstripe123',
      line_channel_id: 'mocklinechannel123',
      commission_rate: 5.0,
      modular_contract_id: '0x1234567768',
    };

    // Mock data for QuestsTable
    const mockQuest: QuestWithTranslations = {
      quest_id: questId,
      service_id: mockService.service_id,
      quest_title: 'Mock Quest',
      quest_description: 'This is a mock quest description.',
      quest_cover_image_url: 'https://mockquest.com/cover.jpg',
      quest_thumbnail_image_url: 'https://mockquest.com/thumbnail.jpg',
      quest_available_start_date: new Date('2024-01-01T00:00:00Z'),
      quest_available_end_date: new Date('2024-12-31T23:59:59Z'),
      quest_type: QuestType.CUSTOM,
      order_index: 1,
    };

    const mockNftRegisterResponse = {
      nftContractId: 'mock-nft-contract-id',
      contractAddress: 'contractAddress',
    };

    const mockCreatedReward = {
      reward_id: generatedRewardId,
      service_id: serviceId,
      reward_cover_image_url: `https://example.com/rewards/covers/${generatedRewardId}.png`,
      reward_thumbnail_image_url: `https://example.com/rewards/icons/${generatedRewardId}.png`,
      reward_acquirement_type: requestBody.acquirementType,
    };

    const mockCreatedRewardTranslation: RewardTranslationEntity[] = [
      {
        reward_id: generatedRewardId,
        service_id: serviceId,
        language: languageCode.EN_US,
        reward_title: requestBody.rewardTranslations[0].title,
        reward_description: requestBody.rewardTranslations[0].description,
      },
    ];

    beforeEach(() => {
      jest.clearAllMocks();
    });

    test('should create a quest reward successfully', async () => {
      mockServiceInfoRepository.getServiceById.mockResolvedValue(mockService);
      mockQuestRepository.selectQuestById.mockResolvedValue(mockQuest);
      mockNftRegisterService.register.mockResolvedValue(mockNftRegisterResponse);
      mockRewardRepository.insertReward.mockResolvedValue(mockCreatedReward);
      mockRewardRepository.insertRewardTranslations.mockResolvedValue(mockCreatedRewardTranslation);

      const result = await questService.createQuestReward(questId, serviceId, requestBody);

      const expectedResult: CreateQuestRewardResponse = {
        rewardId: mockCreatedReward.reward_id,
        serviceId: mockCreatedReward.service_id,
        coverImageUrl: mockCreatedReward.reward_cover_image_url,
        thumbnailImageUrl: mockCreatedReward.reward_thumbnail_image_url,
        rewardTranslations: requestBody.rewardTranslations,
        acquirementType: mockCreatedReward.reward_acquirement_type,
        questRewardPriorityType: requestBody.questRewardPriorityType,
        nfts: [
          {
            nftContractId: mockNftRegisterResponse.nftContractId,
            nftContractType: RewardType.CERTIFICATE,
          },
        ],
        points: [
          {
            pointType: PointType.REWARD,
            amount: 100,
          },
          {
            pointType: PointType.STATUS,
            amount: 200,
          },
        ],
      };

      expect(mockServiceInfoRepository.getServiceById).toHaveBeenCalledWith(serviceId);
      expect(mockQuestRepository.selectQuestById).toHaveBeenCalledWith(questId, serviceId, languageCode.JA);
      expect(mockNftRegisterService.register).toHaveBeenCalledWith(serviceId, {
        nftContractTypeId: requestBody?.nfts?.[0]?.nftContractTypeId,
        nftName: requestBody?.nfts?.[0]?.nftName,
        nftSymbol: requestBody?.nfts?.[0]?.nftSymbol,
        nftCollectionName: requestBody?.nfts?.[0]?.nftCollectionName,
        metadata: requestBody?.nfts?.[0]?.nftMetadata,
        deliveryImageUrl: requestBody?.nfts?.[0]?.deliveryImageUrl,
      });
      expect(mockNftComponentRepository.insertNftComponents).toHaveBeenCalledWith(
        serviceId,
        [
          {
            nft_component_id: 'mock-uuid',
            service_id: serviceId,
            nft_contract_id: 'mock-nft-contract-id',
            token_id: undefined,
            nft_contract_type: requestBody?.nfts?.[0]?.nftContractType,
            certificate_type: requestBody?.nfts?.[0]?.certificateReward?.certificateType,
            status_certificate_rank: requestBody?.nfts?.[0]?.certificateReward?.statusCertificateRank,
          },
        ],
        mockTrx,
      );
      expect(mockRewardComponentRepository.insertRewardComponents).toHaveBeenCalledWith(
        serviceId,
        [
          {
            reward_component_id: 'mock-uuid',
            service_id: serviceId,
            reward_id: 'mock-uuid',
            reward_component_type: RewardComponentType.NFT,
            nft_component_id: 'mock-uuid',
          },
        ],
        mockTrx,
      );
      expect(mockPointComponentRepository.insertPointComponents).toHaveBeenCalledWith(
        serviceId,
        [
          {
            point_component_id: 'mock-uuid',
            service_id: serviceId,
            amount: requestBody?.points?.[0]?.amount, 
            point_type: requestBody?.points?.[0]?.pointType,
            expires_on: requestBody?.points?.[0]?.expireDate ? new Date(requestBody?.points?.[0]?.expireDate) : null,
          },
          {
            point_component_id: 'mock-uuid',
            service_id: serviceId,
            amount: requestBody?.points?.[1]?.amount,
            point_type: requestBody?.points?.[1]?.pointType,
            expires_on: null,
          },
        ],
        mockTrx,
      );
      expect(mockRewardComponentRepository.insertRewardComponents).toHaveBeenCalledWith(
        serviceId,
        [
          {
            reward_component_id: 'mock-uuid',
            service_id: serviceId,
            reward_id: 'mock-uuid',
            reward_component_type: RewardComponentType.POINT,
            point_component_id: 'mock-uuid',
          },
          {
            reward_component_id: 'mock-uuid',
            service_id: serviceId,
            reward_id: 'mock-uuid',
            reward_component_type: RewardComponentType.POINT,
            point_component_id: 'mock-uuid',
          },
        ],
        mockTrx,
      );
      expect(mockRewardRepository.insertReward).toHaveBeenCalled();

      expect(result).toEqual(expectedResult);
    });

    test('should throw ValidationError if quest does not belong to service', async () => {
      mockServiceInfoRepository.getServiceById.mockResolvedValue(mockService);
      mockQuestRepository.selectQuestById.mockResolvedValue(undefined);

      await expect(questService.createQuestReward(questId, serviceId, requestBody)).rejects.toThrow(ValidationError);
      expect(mockQuestRepository.selectQuestById).toHaveBeenCalledWith(questId, serviceId, languageCode.JA);
    });

    test('should throw error if nftRegisterService.register fails', async () => {
      mockServiceInfoRepository.getServiceById.mockResolvedValue(mockService);
      mockQuestRepository.selectQuestById.mockResolvedValue(mockQuest);
      mockRewardRepository.insertReward.mockResolvedValue(mockCreatedReward);
      mockNftRegisterService.register.mockRejectedValue(new Error('NFT registration failed'));

      await expect(questService.createQuestReward(questId, serviceId, requestBody)).rejects.toThrow(
        'NFT registration failed',
      );
    });

    test('should throw error if rewardRepository.insertReward fails', async () => {
      mockServiceInfoRepository.getServiceById.mockResolvedValue(mockService);
      mockQuestRepository.selectQuestById.mockResolvedValue(mockQuest);
      mockNftRegisterService.register.mockResolvedValue(mockNftRegisterResponse);
      mockRewardRepository.insertReward.mockRejectedValue(new Error('DB insert failed'));

      await expect(questService.createQuestReward(questId, serviceId, requestBody)).rejects.toThrow('DB insert failed');
    });
  });
});
