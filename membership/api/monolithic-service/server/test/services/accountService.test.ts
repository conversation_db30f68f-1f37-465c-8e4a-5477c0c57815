import { AccountService } from '../../src/services/accountService';
import { AccountRepository } from '../../src/repositories/accountRepository';
import { UserService } from '../../src/services/userService';
import { FirebaseComponent } from '../../src/components/firebaseComponent';
import { LineComponent } from '../../src/components/lineComponent';
import { NotFoundError } from '../../src/errors/notFoundError';
import { UserResponse } from '../../src/responsedto/userResponse';
import { QuestRepository } from '../../src/repositories/questRepository';
import { QuestActivityRepository, QuestInfo } from '../../src/repositories/questActivityRepository';
import { ClaimedRewardRepository, RewardInfo } from '../../src/repositories/claimedRewardRepository';
import { ActionInfo, ActionRepository } from '../../src/repositories/actionRepository';
import { ServiceInfoRepository } from '../../src/repositories/serviceInfoRepository';
import { ServiceEntity } from '../../src/tables/servicesTable';
import { NftsService } from '../../src/services/nftsService';
import { NftContract, NftContractsRepository } from '../../src/repositories/nftContractsRepository';
import { VaultKeyRepository } from '../../src/repositories/vaultKeyRepository';
import { MetadataService } from '../../src/services/metadataService';
import { InternalServerError } from '../../src/errors/internalServerError';
import { AccountStatus } from '../../src/enum/accoutStatus';
import { ConflictError } from '../../src/errors/conflictError';
import { RewardType } from '../../src/enum/rewardType';
import { RewardUsageStatus } from '../../src/enum/rewardUsageStatus';
import { AccountEntity } from '../../src/tables/accountTable';
import { QuestEntity } from '../../src/tables/questTable';
import { QuestType } from '../../src/enum/questType';
import { NftType } from '../../src/enum/nftType';
import { TxType } from '../../src/enum/txType';
import { VaultKeysEntity } from '../../src/tables/vaultKeyTable';
import { UserRepository } from '../../src/repositories/userRepository';
import { TransactionQueuesRepository } from '../../src/repositories/transactionQueuesRepository';
import { TransactionQueuesEntity } from '../../src/tables/transactionQueuesTable';
import { TransactionQueueStatus } from '../../src/enum/transactionQueueStatus';
import { RewardRepository } from '../../src/repositories/rewardRepository';
import { NftMetadatasRepository } from '../../src/repositories/nftMetadatasRepository';
import { NftBaseMetadatasRepository } from '../../src/repositories/nftBaseMetadatasRepository';
import { NftContractTypesRepository } from '../../src/repositories/nftContractTypesRepository';
import { languageCode } from '../../src/enum/languageCode';
import { NotificationService } from '../../src/services/notificationService';
import { NotificationRepository } from '../../src/repositories/notificationRepository';

jest.mock('uuid', () => ({
  v4: jest.fn().mockReturnValue('mock-uuid'),
}));

jest.mock('../../src/repositories/accountRepository');
jest.mock('../../src/repositories/actionRepository');
jest.mock('../../src/services/userService');
jest.mock('../../src/services/metadataService');
jest.mock('../../src/components/firebaseComponent');
jest.mock('../../src/components/lineComponent');
jest.mock('../../src/components/rpcComponent');
jest.mock('../../src/repositories/questRepository');
jest.mock('../../src/repositories/questActivityRepository');
jest.mock('../../src/repositories/certificateRewardRepository');
jest.mock('../../src/repositories/claimedRewardRepository');
jest.mock('../../src/repositories/userRepository');
jest.mock('../../src/repositories/serviceInfoRepository');
jest.mock('../../src/repositories/vaultKeyRepository');
jest.mock('../../src/repositories/vaultTransactionQueuesRepository');
jest.mock('../../src/repositories/nftContractsRepository');
jest.mock('../../src/repositories/deliveryNftsFirestoreRepository');

import axios from 'axios';
jest.mock('axios');
jest.mock('../../src/services/nftsService');
jest.mock('../../src/db/database', () => {
  return {
    db: {
      transaction: jest.fn(() => ({
        execute: jest.fn(async (callback) => callback()),
      })),
    },
  };
});
jest.mock('../../src/components/cloudTaskComponent', () => ({
  CloudTaskComponent: jest.fn().mockImplementation(() => ({
    enqueueMintTask: jest.fn().mockResolvedValue(undefined),
  })),
}));

describe('AccountService', () => {
  let mockAccountRepository: jest.Mocked<AccountRepository>;
  let mockActionRepository: jest.Mocked<ActionRepository>;
  let mockQuestRepository: jest.Mocked<QuestRepository>;
  let mockQuestActivityRepository: jest.Mocked<QuestActivityRepository>;
  let mockClaimedRewardRepository: jest.Mocked<ClaimedRewardRepository>;
  let mockServiceInfoRepository: jest.Mocked<ServiceInfoRepository>;
  let mockNftContractsRepository: jest.Mocked<NftContractsRepository>;
  let mockVaultKeyRepository: jest.Mocked<VaultKeyRepository>;
  let mockFirebaseComponent: jest.Mocked<FirebaseComponent>;
  let mockLineComponent: jest.Mocked<LineComponent>;
  let mockUserRepository: jest.Mocked<UserRepository>;
  let mockRewardRepository: jest.Mocked<RewardRepository>;
  let mockNftMetadatasRepository: jest.Mocked<NftMetadatasRepository>;
  let mockNftBaseMetadatasRepository: jest.Mocked<NftBaseMetadatasRepository>;
  let mockNftContractTypesRepository: jest.Mocked<NftContractTypesRepository>;

  let mockUserService: jest.Mocked<UserService>;
  let mockNftsService: jest.Mocked<NftsService>;
  let accountService: jest.Mocked<AccountService>;

  let mockMetadataService: jest.Mocked<MetadataService>;

  let mockTransactionQueuesRepository: jest.Mocked<TransactionQueuesRepository>;
  let mockNotificationRepository: jest.Mocked<NotificationRepository>;
  let mockNotificationService: jest.Mocked<NotificationService>;
  beforeEach(() => {
    process.env.JSON_RPC_URL = 'http://127.0.0.1:8545';
    process.env.ALCHEMY_API_KEY = 'xxxxxsss';
    process.env.ALCHEMY_CHAIN_NAME = 'polygon-amoy';
    process.env.GAS_LIMIT_MULTIPLIER = '200';
    process.env.BASE_MAX_FEE_PER_GAS = '600';
    process.env.MAX_FEE_PER_GAS_MULTIPLIER = '150';
    process.env.MAX_PRIORITY_FEE_PER_GAS_MULTIPLIER = '150';
    process.env.GCP_PROJECT_ID = 'sample_gcp_project_id';

    mockAccountRepository = new AccountRepository() as jest.Mocked<AccountRepository>;
    mockActionRepository = new ActionRepository() as jest.Mocked<ActionRepository>;
    mockQuestRepository = new QuestRepository() as jest.Mocked<QuestRepository>;
    mockQuestActivityRepository = new QuestActivityRepository() as jest.Mocked<QuestActivityRepository>;
    mockClaimedRewardRepository = new ClaimedRewardRepository() as jest.Mocked<ClaimedRewardRepository>;
    mockServiceInfoRepository = new ServiceInfoRepository() as jest.Mocked<ServiceInfoRepository>;
    mockVaultKeyRepository = new VaultKeyRepository() as jest.Mocked<VaultKeyRepository>;
    mockNftContractsRepository = new NftContractsRepository() as jest.Mocked<NftContractsRepository>;
    mockRewardRepository = new RewardRepository() as jest.Mocked<RewardRepository>;
    mockNftMetadatasRepository = new NftMetadatasRepository() as jest.Mocked<NftMetadatasRepository>;
    mockNftBaseMetadatasRepository = new NftBaseMetadatasRepository() as jest.Mocked<NftBaseMetadatasRepository>;
    mockNftContractTypesRepository = new NftContractTypesRepository() as jest.Mocked<NftContractTypesRepository>;

    mockFirebaseComponent = new FirebaseComponent() as jest.Mocked<FirebaseComponent>;
    mockLineComponent = new LineComponent() as jest.Mocked<LineComponent>;

    mockTransactionQueuesRepository = new TransactionQueuesRepository() as jest.Mocked<TransactionQueuesRepository>;

    mockUserRepository = new UserRepository() as jest.Mocked<UserRepository>;

    mockNftsService = new NftsService(
      mockNftContractsRepository,
      mockAccountRepository,
      mockRewardRepository,
    ) as jest.Mocked<NftsService>;
    mockUserService = new UserService(mockUserRepository, mockAccountRepository) as jest.Mocked<UserService>;

    mockMetadataService = new MetadataService(
      mockNftMetadatasRepository,
      mockNftBaseMetadatasRepository,
      mockNftContractTypesRepository,
    ) as jest.Mocked<MetadataService>;

    mockNotificationRepository = new NotificationRepository() as jest.Mocked<NotificationRepository>;
    mockNotificationService = new NotificationService(
      mockAccountRepository,
      mockNotificationRepository,
    ) as jest.Mocked<NotificationService>;

    accountService = new AccountService(
      mockAccountRepository,
      mockActionRepository,
      mockQuestRepository,
      mockQuestActivityRepository,
      mockClaimedRewardRepository,
      mockUserService,
      mockServiceInfoRepository,
      mockLineComponent,
      mockFirebaseComponent,
      mockNftsService,
      mockNftContractsRepository,
      mockVaultKeyRepository,
      mockTransactionQueuesRepository,
      mockMetadataService,
      mockNotificationService,
    ) as jest.Mocked<AccountService>;

    jest.spyOn(mockNftContractsRepository, 'selectNftContractById').mockResolvedValue(undefined);
  });

  describe('AccountService', () => {
    const authorization = 'mockAuthorization';
    const lineIdToken = 'mockLineIdToken';
    const serviceId = 'mockServiceId';
    const accountId = 'mockAccountId';
    const lineChannelId = 'line_channel_id';
    const decodedLineToken = {
      sub: 'mockSub',
      name: 'Test User',
      picture: 'http://example.com/image.png',
    };
    const decodedFirebaseToken = {
      uid: 'mockUid',
      aud: 'mockAud',
      auth_time: **********,
      exp: **********,
      firebase: {
        identities: {},
        sign_in_provider: 'mockProvider',
      },
      iat: **********,
      iss: 'mockIss',
      sub: 'mockSub',
    };
    const user = new UserResponse(
      'mockUid',
      'JP',
      '**********',
      'mockMnemonicBackupKey',
      '0x**********abcdef',
      AccountStatus.ACTIVE,
    );
    const service: ServiceEntity = {
      service_id: serviceId,
      tenant_id: 'tenantId',
      service_url: 'https://test-service.com',
      service_logo_image_url: 'https://marbullx.com/logo',
      market_cover_image_url: 'https://example.com/market-cover.jpg',
      theme_primary_color_lowest: '0xFFFFFFFFFFFF',
      theme_primary_color_lower: '0xFFFFFFFFFFFF',
      theme_primary_color_higher: '0xFFFFFFFFFFFF',
      theme_primary_color_highest: '0xFFFFFFFFFFFF',
      is_market_enabled: true,
      membership_nft_contract_id: '0x**********abcdef',
      stripe_account_id: 'stripe_account_id',
      line_channel_id: lineChannelId,
      commission_rate: 0.1,
      modular_contract_id: '0x**********abcdef',
    };

    beforeEach(() => {
      mockLineComponent.verifyLineIdToken.mockResolvedValue(decodedLineToken);
      mockFirebaseComponent.verifyFirebaseIdToken.mockResolvedValue(decodedFirebaseToken);
    });

    describe('createAccount', () => {
      beforeEach(() => {
        jest.clearAllMocks();
      });

      test('should create a new account and return the account response', async () => {
        const membershipContractId = '0x**********abcdef';
        const expectedAccountResponse = {
          accountId: 'mock-uuid',
          displayName: 'Test User',
          profileImage: 'http://example.com/image.png',
          tokenBoundAccountAddress: undefined,
          membership: {
            tokenId: 0,
            contractAddress: '0xBC4CA0EdA7647A8aB7C2061c2E118A18a936f13D',
          },
          membershipNftImageUrl: 'http://example.com/mocked_image.png',
        };
        const mockAccount: AccountEntity = {
          account_id: 'mock-uuid',
          service_id: serviceId,
          user_id: decodedFirebaseToken.uid,
          membership_id: 0,
          display_name: decodedLineToken.name,
          profile_image_url: decodedLineToken.picture,
          token_bound_account_address: undefined,
          status: AccountStatus.ACTIVE,
          transaction_id: 'mock-uuid',
          queue_id: 'mock-uuid',
          created_at: expect.any(Date),
          updated_at: expect.any(Date),
          last_login_at: expect.any(Date),
          membership_metadata_url: 'http://example.com/mocked_image.png',
        };
        const mockVaultKey: VaultKeysEntity = {
          vault_key_id: 'vault_key_id',
          tenant_id: 'tenant_id',
          key_ring_project: 'key_ring_project',
          key_ring_location: 'key_ring_location',
          key_ring_name: 'key_ring_name',
          key_version: 'key_version',
          vault_wallet_address: 'vault_wallet_address',
          nonce: 0,
        };
        const mockContract: NftContract = {
          nft_contract_id: 'nft_contract_id',
          service_id: 'mockServiceId',
        };
        const mockTxQueue: TransactionQueuesEntity = {
          queue_id: '',
          service_id: '',
          from_address: '',
          to_address: '',
          status: TransactionQueueStatus.PENDING,
          tx_type: TxType.MINT_REWARD,
          nft_type: NftType.COUPON,
          nft_contract_address: '',
          created_date: new Date(),
          token_id: 0,
        };
        mockServiceInfoRepository.selectLineChannelId.mockResolvedValue(lineChannelId);
        mockUserService.getUser.mockResolvedValue(user);
        mockAccountRepository.selectAccountByUserId.mockResolvedValue(undefined);
        mockServiceInfoRepository.getServiceById.mockResolvedValue({
          ...service,
          membership_nft_contract_id: membershipContractId,
        });
        mockVaultKeyRepository.getVaultKeyIdWithLock.mockResolvedValue(mockVaultKey);
        mockNftContractsRepository.getNftContractsByIdWithLock.mockResolvedValue(mockContract);
        mockTransactionQueuesRepository.insertQueue = jest.fn();
        mockTransactionQueuesRepository.insertQueue.mockResolvedValue(mockTxQueue);

        mockVaultKeyRepository.updateNonce.mockResolvedValue([]);
        mockNftContractsRepository.updateNextTokenId.mockResolvedValue();
        mockMetadataService.insertMetadata.mockResolvedValue('https://test-service.com/membership_metadata.json');

        mockAccountRepository.selectAccountByUserId.mockResolvedValue(undefined);
        mockAccountRepository.insertAccount.mockResolvedValue(mockAccount);
        mockNftContractsRepository.selectNftContractById.mockResolvedValue({
          nft_contract_id: '0x**********abcdef',
          service_id: 'mockServiceId',
          nft_contract_address: '0xBC4CA0EdA7647A8aB7C2061c2E118A18a936f13D',
        });
        (axios.get as jest.Mock).mockImplementation((url: string) => {
          if (url === 'https://test-service.com/membership_metadata.json') {
            return Promise.resolve({
              data: { image: 'http://example.com/mocked_image.png' },
            });
          } else {
            return Promise.reject(new Error('Unexpected URL'));
          }
        });

        const result = await accountService.createAccount(authorization, lineIdToken, serviceId);

        // expect(CloudTaskComponent.prototype.enqueueMintTask).toHaveBeenCalled();
        expect(result).toEqual(expectedAccountResponse);
        expect(mockLineComponent.verifyLineIdToken).toHaveBeenCalledWith(lineIdToken, lineChannelId);
        expect(mockFirebaseComponent.verifyFirebaseIdToken).toHaveBeenCalledWith(authorization);
        expect(mockUserService.getUser).toHaveBeenCalledWith(decodedFirebaseToken.uid);
        expect(mockAccountRepository.selectAccountByUserId).toHaveBeenCalledWith(decodedFirebaseToken.sub, serviceId);
        expect(mockAccountRepository.insertAccount).toHaveBeenCalled();
      });

      test('should throw NotFoundError if user does not have contract account address', async () => {
        mockServiceInfoRepository.selectLineChannelId.mockResolvedValue(lineChannelId);
        user.contractAccountAddress = undefined;

        await expect(accountService.createAccount(authorization, lineIdToken, serviceId)).rejects.toThrow(
          NotFoundError,
        );
      });

      test('should throw NotFoundError if user does not exist', async () => {
        mockServiceInfoRepository.selectLineChannelId.mockResolvedValue(lineChannelId);
        mockUserService.getUser.mockRejectedValue(new NotFoundError('User not found'));

        await expect(accountService.createAccount(authorization, lineIdToken, serviceId)).rejects.toThrow(
          NotFoundError,
        );
      });
      test('should throw ConflictError if account already exists', async () => {
        mockServiceInfoRepository.selectLineChannelId.mockResolvedValue(lineChannelId);
        mockUserService.getUser.mockResolvedValue(
          new UserResponse(
            'mockUid',
            'JP',
            '**********',
            'mockMnemonicBackupKey',
            '0x**********abcdef',
            AccountStatus.ACTIVE,
          ),
        );
        mockAccountRepository.selectAccountByUserId.mockResolvedValue({
          account_id: 'existingAccountId',
          service_id: serviceId,
          user_id: decodedFirebaseToken.uid,
          membership_id: 100,
          display_name: 'Existing User',
          profile_image_url: 'http://example.com/existing.png',
          token_bound_account_address: '0xExistingAddress',
          status: AccountStatus.ACTIVE,
          transaction_id: 'existingTransactionId',
          queue_id: 'existingQueueId',
          created_at: new Date('2020/01/01 00:00:00'),
          updated_at: new Date('2020/01/01 00:00:00'),
          last_login_at: new Date('2020/01/01 00:00:00'),
          membership_metadata_url: 'http://example.com/membership_metadata.json',
        });

        await expect(accountService.createAccount(authorization, lineIdToken, serviceId)).rejects.toThrow(
          ConflictError,
        );
      });
      test('should throw NotFoundError if service is not found', async () => {
        mockServiceInfoRepository.selectLineChannelId.mockResolvedValue(undefined);
        mockUserService.getUser.mockResolvedValue(
          new UserResponse(
            'mockUid',
            'JP',
            '**********',
            'mockMnemonicBackupKey',
            '0x**********abcdef',
            AccountStatus.ACTIVE,
          ),
        );
        mockAccountRepository.selectAccountByUserId.mockResolvedValue(undefined);
        mockServiceInfoRepository.getServiceById.mockResolvedValue(undefined);

        await expect(accountService.createAccount(authorization, lineIdToken, serviceId)).rejects.toThrow(
          NotFoundError,
        );
      });

      test('should throw InternalServerError if account insertion fails', async () => {
        mockServiceInfoRepository.selectLineChannelId.mockResolvedValue(lineChannelId);
        const membershipContractId = '0x**********abcdef';
        mockUserService.getUser.mockResolvedValue(
          new UserResponse(
            'mockUid',
            'JP',
            '**********',
            'mockMnemonicBackupKey',
            '0x**********abcdef',
            AccountStatus.ACTIVE,
          ),
        );
        mockAccountRepository.selectAccountByUserId.mockResolvedValue(undefined);
        mockServiceInfoRepository.getServiceById.mockResolvedValue({
          ...service,
          membership_nft_contract_id: membershipContractId,
        });
        mockAccountRepository.insertAccount.mockRejectedValue(new InternalServerError('Insertion failed'));

        await expect(accountService.createAccount(authorization, lineIdToken, serviceId)).rejects.toThrow(
          InternalServerError,
        );
      });
    });

    describe('updateUserLineProfile', () => {
      const account: AccountEntity = {
        account_id: 'account-123',
        user_id: '1',
        service_id: 'service-123',
        membership_id: 1,
        display_name: 'Test User',
        profile_image_url: 'http://example.com/image.png',
        token_bound_account_address: '0xBC4CA0EdA7647A8aB7C2061c2E118A18a936f13D',
        transaction_id: 'transaction-id',
        queue_id: 'queue-id',
        status: AccountStatus.ACTIVE,
        created_at: new Date('2020/01/01 00:00:00'),
        updated_at: new Date('2020/01/01 00:00:00'),
        last_login_at: new Date('2020/01/01 00:00:00'),
        membership_metadata_url: 'http://example.com/membership_metadata.json',
      };
      test('should update the user profile successfully', async () => {
        const decodedLineToken = {
          sub: 'mockSub',
          name: 'Test User',
          picture: 'http://example.com/image.png',
        };

        mockLineComponent.verifyLineIdToken.mockResolvedValue(decodedLineToken);
        mockAccountRepository.selectAccountById.mockResolvedValue(account);
        mockServiceInfoRepository.getServiceById.mockResolvedValue(service);
        mockNftContractsRepository.selectNftContractById.mockResolvedValue({
          nft_contract_id: 'mockNftContractId',
          service_id: 'mockServiceId',
          nft_contract_address: '0x**********abcdef',
        });

        // axios.getのモックを追加
        (axios.get as jest.Mock).mockResolvedValue({
          data: {
            image: 'http://example.com/mocked_image.png',
          },
        });

        const result = await accountService.updateUserLineProfile(account.account_id, lineIdToken, account.service_id);

        expect(mockAccountRepository.selectAccountById).toHaveBeenCalledWith(account.account_id, account.service_id);
        expect(mockLineComponent.verifyLineIdToken).toHaveBeenCalledWith(lineIdToken, lineChannelId);
        expect(mockAccountRepository.updateUserLineProfile).toHaveBeenCalledWith(
          account.account_id,
          account.service_id,
          decodedLineToken.name,
          decodedLineToken.picture,
        );
        expect(result).toEqual({
          accountId: account.account_id,
          displayName: decodedLineToken.name,
          profileImage: decodedLineToken.picture,
          membership: {
            tokenId: account.membership_id,
            contractAddress: '0x**********abcdef',
          },
          tokenBoundAccountAddress: account.token_bound_account_address,
          membershipNftImageUrl: 'http://example.com/mocked_image.png',
        });
      });
      test('should throw NotFoundError if service is not found', async () => {
        mockAccountRepository.selectAccountByUserId.mockResolvedValue(account);
        mockServiceInfoRepository.getServiceById.mockResolvedValue(undefined);

        await expect(accountService.updateUserLineProfile(accountId, lineIdToken, serviceId)).rejects.toThrow(
          NotFoundError,
        );
      });
      test('should throw NotFoundError if service is not found', async () => {
        mockAccountRepository.selectAccountByUserId.mockResolvedValue(account);
        mockServiceInfoRepository.getServiceById.mockResolvedValue(undefined);

        await expect(accountService.updateUserLineProfile(accountId, lineIdToken, serviceId)).rejects.toThrow(
          NotFoundError,
        );
      });

      test('should throw NotFoundError if membership NFT is not found', async () => {
        mockAccountRepository.selectAccountByUserId.mockResolvedValue(account);
        mockServiceInfoRepository.getServiceById.mockResolvedValue(service);
        mockNftContractsRepository.selectNftContractById.mockResolvedValue(undefined);

        await expect(accountService.updateUserLineProfile(accountId, lineIdToken, serviceId)).rejects.toThrow(
          NotFoundError,
        );
      });
    });

    describe('getAccount', () => {
      const account: AccountEntity = {
        account_id: accountId,
        service_id: serviceId,
        user_id: 'mockUserId',
        membership_id: 100,
        display_name: 'Test User',
        profile_image_url: 'http://example.com/image.png',
        token_bound_account_address: '0xBC4CA0EdA7647A8aB7C2061c2E118A18a936f13D',
        transaction_id: 'transaction-id',
        queue_id: 'queue-id',
        status: AccountStatus.ACTIVE,
        created_at: new Date('2020/01/01 00:00:00'),
        updated_at: new Date('2020/01/01 00:00:00'),
        last_login_at: new Date('2020/01/01 00:00:00'),
        membership_metadata_url: 'http://example.com/membership_metadata.json',
      };

      const membershipNft = {
        nft_contract_id: 'mockNftContractId',
        service_id: serviceId,
        nft_contract_address: '0x**********abcdef',
      };
      test('should return account response when account exists', async () => {
        mockAccountRepository.selectAccountById.mockResolvedValue(account);
        mockServiceInfoRepository.getServiceById.mockResolvedValue(service);
        mockNftContractsRepository.selectNftContractById.mockResolvedValue(membershipNft);

        // axios.getのモックを追加
        (axios.get as jest.Mock).mockResolvedValue({
          data: {
            image: 'http://example.com/mocked_image.png',
          },
        });

        const result = await accountService.getAccount(accountId, serviceId);

        expect(result).toEqual({
          accountId: account.account_id,
          displayName: account.display_name,
          profileImage: account.profile_image_url,
          membership: {
            tokenId: account.membership_id,
            contractAddress: membershipNft.nft_contract_address,
          },
          tokenBoundAccountAddress: account.token_bound_account_address,
          membershipNftImageUrl: 'http://example.com/mocked_image.png',
        });
        expect(mockAccountRepository.selectAccountById).toHaveBeenCalledWith(accountId, serviceId);
        expect(mockServiceInfoRepository.getServiceById).toHaveBeenCalledWith(serviceId);
        expect(mockNftContractsRepository.selectNftContractById).toHaveBeenCalledWith(
          service.membership_nft_contract_id,
        );
      });

      test('should throw NotFoundError if account does not exist', async () => {
        mockAccountRepository.selectAccountById.mockResolvedValue(undefined);

        await expect(accountService.getAccount(accountId, serviceId)).rejects.toThrow(NotFoundError);
        expect(mockAccountRepository.selectAccountById).toHaveBeenCalledWith(accountId, serviceId);
      });

      test('should throw NotFoundError if service is not found', async () => {
        mockAccountRepository.selectAccountById.mockResolvedValue(account);
        mockServiceInfoRepository.getServiceById.mockResolvedValue(undefined);

        await expect(accountService.getAccount(accountId, serviceId)).rejects.toThrow(NotFoundError);
        expect(mockServiceInfoRepository.getServiceById).toHaveBeenCalledWith(serviceId);
      });

      test('should throw NotFoundError if membership NFT is not found', async () => {
        mockAccountRepository.selectAccountById.mockResolvedValue(account);
        mockServiceInfoRepository.getServiceById.mockResolvedValue(service);
        mockNftContractsRepository.selectNftContractById.mockResolvedValue(undefined);

        await expect(accountService.getAccount(accountId, serviceId)).rejects.toThrow(NotFoundError);
        expect(mockNftContractsRepository.selectNftContractById).toHaveBeenCalledWith(
          service.membership_nft_contract_id,
        );
      });
    });

    describe('getAccountStatus', () => {
      const account: AccountEntity = {
        account_id: accountId,
        service_id: serviceId,
        user_id: 'mockUserId',
        membership_id: 100,
        display_name: 'Test User',
        profile_image_url: 'http://example.com/image.png',
        token_bound_account_address: '0xBC4CA0EdA7647A8aB7C2061c2E118A18a936f13D',
        transaction_id: 'transaction-id',
        queue_id: 'queue-id',
        status: AccountStatus.ACTIVE,
        created_at: new Date('2020/01/01 00:00:00'),
        updated_at: new Date('2020/01/01 00:00:00'),
        last_login_at: new Date('2020/01/01 00:00:00'),
        membership_metadata_url: 'http://example.com/membership_metadata.json',
      };

      const statusQuest: QuestEntity = {
        quest_id: 'quest_001',
        service_id: serviceId,
        quest_cover_image_url: 'http://example.com/cover_image.png',
        quest_thumbnail_image_url: 'http://example.com/image.png',
        quest_available_start_date: new Date('2024-01-01T00:00:00'),
        quest_available_end_date: new Date('2024-01-31T23:59:59'),
        quest_type: QuestType.STATUS,
        order_index: 1,
      };

      const badges = [
        {
          rewardId: 'reward1',
          title: 'Badge 1',
          description: 'Description 1',
          imageUrl: 'http://example.com/badge1.png',
          rank: 1,
        },
      ];

      const contract = {
        nft_contract_id: 'mockNftContractId',
        service_id: serviceId,
        nft_contract_type_id: 'mockNftContractTypeId',
        nft_collection_name: 'mockNftCollectionName',
        nft_contract_address: '0x**********abcdef',
        nft_contract_implementation_address: '0xMockImplementationAddress',
        nft_contract_abi: {},
      };

      test('should return account status successfully', async () => {
        mockAccountRepository.selectAccountById.mockResolvedValue(account);
        mockServiceInfoRepository.getServiceById.mockResolvedValue(service);
        mockNftContractsRepository.selectNftContractById.mockResolvedValue(contract);
        mockQuestRepository.selectEnableStatusQuestByCurrentDate.mockResolvedValue(statusQuest);
        mockNftsService.getUserBadges.mockResolvedValue(badges);
        mockQuestActivityRepository.countCompletedQuestActivitiesByAccountId.mockResolvedValue(5);
        mockClaimedRewardRepository.countClaimedRewardsByAccountId.mockResolvedValue(3);
        mockClaimedRewardRepository.countRewardsByAccountIdAndStatus.mockResolvedValue(1);
        mockQuestActivityRepository.countQuestActivitiesByAccountIdAndPeriod.mockResolvedValue(2);

        const result = await accountService.getAccountStatus(accountId, serviceId);

        expect(result).toEqual({
          completedQuests: 5,
          currentPeriodCompletedQuests: 2,
          obtainedRewards: 3,
          unusedCoupon: 1,
          badges,
        });
        expect(mockAccountRepository.selectAccountById).toHaveBeenCalledWith(accountId, serviceId);
        expect(mockQuestRepository.selectEnableStatusQuestByCurrentDate).toHaveBeenCalledWith(serviceId);
        expect(mockNftsService.getUserBadges).toHaveBeenCalledWith(serviceId, accountId);
        expect(mockQuestActivityRepository.countCompletedQuestActivitiesByAccountId).toHaveBeenCalledWith(
          accountId,
          serviceId,
        );
        expect(mockClaimedRewardRepository.countClaimedRewardsByAccountId).toHaveBeenCalledWith(accountId, serviceId);
        expect(mockClaimedRewardRepository.countRewardsByAccountIdAndStatus).toHaveBeenCalledWith(
          accountId,
          serviceId,
          RewardType.COUPON,
          RewardUsageStatus.ACTIVE,
        );
        expect(mockQuestActivityRepository.countQuestActivitiesByAccountIdAndPeriod).toHaveBeenCalledWith(
          accountId,
          serviceId,
          statusQuest.quest_available_start_date,
          statusQuest.quest_available_end_date,
        );
      });

      test('should throw NotFoundError if account does not exist', async () => {
        mockAccountRepository.selectAccountById.mockResolvedValue(undefined);
        await expect(accountService.getAccountStatus(accountId, serviceId)).rejects.toThrow(NotFoundError);
      });
      test('should return 0 for currentPeriodCompletedQuests if no status quest is found', async () => {
        mockAccountRepository.selectAccountById.mockResolvedValue(account);
        mockServiceInfoRepository.getServiceById.mockResolvedValue(service);
        mockNftContractsRepository.selectNftContractById.mockResolvedValue(contract);

        mockQuestRepository.selectEnableStatusQuestByCurrentDate.mockResolvedValue(undefined);

        const result = await accountService.getAccountStatus(accountId, serviceId);

        expect(result.currentPeriodCompletedQuests).toEqual(0);
      });
      test('should return empty badges if getUserBadges throws an error', async () => {
        mockAccountRepository.selectAccountById.mockResolvedValue(account);
        mockServiceInfoRepository.getServiceById.mockResolvedValue(service);
        mockNftContractsRepository.selectNftContractById.mockResolvedValue(contract);
        mockQuestRepository.selectEnableStatusQuestByCurrentDate.mockResolvedValue(statusQuest);

        mockNftsService.getUserBadges.mockRejectedValue(new Error('Failed to fetch badges'));

        const result = await accountService.getAccountStatus(accountId, serviceId);

        expect(result.badges).toEqual([]);
      });
    });

    describe('deleteAccount', () => {
      test('should throw NotFoundError if account does not exist', async () => {
        mockAccountRepository.selectAccountById.mockResolvedValue(undefined);
        await expect(accountService.deleteAccount(accountId, serviceId)).rejects.toThrow(NotFoundError);
      });

      test('should delete account successfully', async () => {
        const account: AccountEntity = {
          account_id: accountId,
          service_id: serviceId,
          user_id: 'mockUserId',
          membership_id: 100,
          display_name: 'Test User',
          profile_image_url: 'http://example.com/image.png',
          token_bound_account_address: '0xBC4CA0EdA7647A8aB7C2061c2E118A18a936f13D',
          transaction_id: 'transaction-id',
          queue_id: 'queue-id',
          status: AccountStatus.ACTIVE,
          created_at: new Date('2020/01/01 00:00:00'),
          updated_at: new Date('2020/01/01 00:00:00'),
          last_login_at: new Date('2020/01/01 00:00:00'),
          membership_metadata_url: 'http://example.com/membership_metadata.json',
        };

        mockAccountRepository.selectAccountById.mockResolvedValue(account);
        mockAccountRepository.updateAccountState.mockResolvedValue(undefined);

        await expect(accountService.deleteAccount(accountId, serviceId)).resolves.toBeUndefined();
        expect(mockAccountRepository.selectAccountById).toHaveBeenCalledWith(accountId, serviceId);
        expect(mockAccountRepository.updateAccountState).toHaveBeenCalledWith(
          accountId,
          AccountStatus.DELETED,
          serviceId,
        );
      });

      test('should throw InternalServerError if account deletion fails', async () => {
        const account: AccountEntity = {
          account_id: accountId,
          service_id: serviceId,
          user_id: 'mockUserId',
          membership_id: 100,
          display_name: 'Test User',
          profile_image_url: 'http://example.com/image.png',
          token_bound_account_address: '0xBC4CA0EdA7647A8aB7C2061c2E118A18a936f13D',
          transaction_id: 'transaction-id',
          queue_id: 'queue-id',
          status: AccountStatus.ACTIVE,
          created_at: new Date('2020/01/01 00:00:00'),
          updated_at: new Date('2020/01/01 00:00:00'),
          last_login_at: new Date('2020/01/01 00:00:00'),
          membership_metadata_url: 'http://example.com/membership_metadata.json',
        };

        mockAccountRepository.selectAccountById.mockResolvedValue(account);
        mockAccountRepository.updateAccountState.mockRejectedValue(new InternalServerError('Deletion failed'));

        await expect(accountService.deleteAccount(accountId, serviceId)).rejects.toThrow(InternalServerError);
        expect(mockAccountRepository.selectAccountById).toHaveBeenCalledWith(accountId, serviceId);
        expect(mockAccountRepository.updateAccountState).toHaveBeenCalledWith(
          accountId,
          AccountStatus.DELETED,
          serviceId,
        );
      });
    });

    describe('getAccountActivityHistory', () => {
      const account: AccountEntity = {
        account_id: accountId,
        service_id: serviceId,
        user_id: 'mockUserId',
        membership_id: 100,
        display_name: 'Test User',
        profile_image_url: 'http://example.com/image.png',
        token_bound_account_address: '0xBC4CA0EdA7647A8aB7C2061c2E118A18a936f13D',
        transaction_id: 'transaction-id',
        queue_id: 'queue-id',
        status: AccountStatus.ACTIVE,
        created_at: new Date('2020/01/01 00:00:00'),
        updated_at: new Date('2020/01/01 00:00:00'),
        last_login_at: new Date('2020/01/01 00:00:00'),
        membership_metadata_url: 'http://example.com/membership_metadata.json',
      };

      const actions: ActionInfo[] = [
        {
          action_id: '1',
          action_title: 'Action 1',
          action_description: 'Actions desc',
          action_thumbnail_image_url: 'action-url',
          action_type: 'TYPE',
          finish_date: new Date('2024-10-10'),
        },
      ];

      const quests: QuestInfo[] = [
        {
          finish_date: new Date('2024-10-11'),
          quest_thumbnail_image_url: 'quest-url',
          quest_title: 'Quest 1',
          quest_type: 'Type',
          quest_description: 'Quest 1 desc',
          quest_id: '1',
        },
      ];

      const rewards: RewardInfo[] = [
        {
          reward_id: '1',
          reward_description: 'Reward 1 desc',
          claim_date: new Date('2024-10-12'),
          reward_thumbnail_image_url: 'reward-url',
          reward_title: 'Reward 1',
          reward_usage_status: 'USED',
        },
      ];

      test('should return activity history response', async () => {
        mockAccountRepository.selectAccountById.mockResolvedValue(account);
        mockActionRepository.selectCompletedActions.mockResolvedValue(actions);
        mockQuestActivityRepository.selectCompletedQuests.mockResolvedValue(quests);
        mockClaimedRewardRepository.selectClaimedRewards.mockResolvedValue(rewards);

        const result = await accountService.getAccountActivityHistory(accountId, serviceId, languageCode.EN_US);

        expect(result).toEqual({
          completedActions: [
            {
              completedTime: '2024-10-10T00:00:00.000Z',
              thumbnailImageUrl: 'action-url',
              title: 'Action 1',
            },
          ],
          completedQuests: [
            {
              completedTime: '2024-10-11T00:00:00.000Z',
              thumbnailImageUrl: 'quest-url',
              title: 'Quest 1',
            },
          ],
          rewards: [
            {
              completedTime: '2024-10-12T00:00:00.000Z',
              thumbnailImageUrl: 'reward-url',
              title: 'Reward 1',
              actionType: 'USED',
            },
          ],
        });

        expect(mockAccountRepository.selectAccountById).toHaveBeenCalledWith(accountId, serviceId);
        expect(mockActionRepository.selectCompletedActions).toHaveBeenCalledWith(
          accountId,
          serviceId,
          languageCode.EN_US,
        );
        expect(mockQuestActivityRepository.selectCompletedQuests).toHaveBeenCalledWith(
          accountId,
          serviceId,
          languageCode.EN_US,
        );
        expect(mockClaimedRewardRepository.selectClaimedRewards).toHaveBeenCalledWith(
          accountId,
          serviceId,
          languageCode.EN_US,
        );
      });

      test('should throw NotFoundError if account does not exist', async () => {
        mockAccountRepository.selectAccountById.mockResolvedValue(undefined);

        await expect(
          accountService.getAccountActivityHistory(accountId, serviceId, languageCode.EN_US),
        ).rejects.toThrow(NotFoundError);
        expect(mockAccountRepository.selectAccountById).toHaveBeenCalledWith(accountId, serviceId);
      });

      test('should return empty arrays if no actions, quests, or rewards are found', async () => {
        mockAccountRepository.selectAccountById.mockResolvedValue(account);
        mockActionRepository.selectCompletedActions.mockResolvedValue([]);
        mockQuestActivityRepository.selectCompletedQuests.mockResolvedValue([]);
        mockClaimedRewardRepository.selectClaimedRewards.mockResolvedValue([]);

        const result = await accountService.getAccountActivityHistory(accountId, serviceId, languageCode.EN_US);

        expect(result).toEqual({
          completedActions: [],
          completedQuests: [],
          rewards: [],
        });

        expect(mockAccountRepository.selectAccountById).toHaveBeenCalledWith(accountId, serviceId);
        expect(mockActionRepository.selectCompletedActions).toHaveBeenCalledWith(
          accountId,
          serviceId,
          languageCode.EN_US,
        );
        expect(mockQuestActivityRepository.selectCompletedQuests).toHaveBeenCalledWith(
          accountId,
          serviceId,
          languageCode.EN_US,
        );
        expect(mockClaimedRewardRepository.selectClaimedRewards).toHaveBeenCalledWith(
          accountId,
          serviceId,
          languageCode.EN_US,
        );
      });

      test('should handle errors from repositories gracefully', async () => {
        mockAccountRepository.selectAccountById.mockResolvedValue(account);
        mockActionRepository.selectCompletedActions.mockRejectedValue(new Error('Action error'));
        mockQuestActivityRepository.selectCompletedQuests.mockRejectedValue(new Error('Quest error'));
        mockClaimedRewardRepository.selectClaimedRewards.mockRejectedValue(new Error('Reward error'));

        await expect(
          accountService.getAccountActivityHistory(accountId, serviceId, languageCode.EN_US),
        ).rejects.toThrow(new InternalServerError('Failed to retrieve activity history: Error: Action error'));

        expect(mockAccountRepository.selectAccountById).toHaveBeenCalledWith(accountId, serviceId);
        expect(mockActionRepository.selectCompletedActions).toHaveBeenCalledWith(
          accountId,
          serviceId,
          languageCode.EN_US,
        );
        expect(mockQuestActivityRepository.selectCompletedQuests).toHaveBeenCalledWith(
          accountId,
          serviceId,
          languageCode.EN_US,
        );
        expect(mockClaimedRewardRepository.selectClaimedRewards).toHaveBeenCalledWith(
          accountId,
          serviceId,
          languageCode.EN_US,
        );
      });
    });
  });

  // describe('getAccountNotifications', () => {
  //   const accountId = 'account_id';
  //   const serviceId = 'service_id';

  //   const account: AccountEntity = {
  //     account_id: accountId,
  //     service_id: serviceId,
  //     user_id: 'mockUserId',
  //     membership_id: 100,
  //     display_name: 'Test User',
  //     profile_image_url: 'http://example.com/image.png',
  //     token_bound_account_address: '0xBC4CA0EdA7647A8aB7C2061c2E118A18a936f13D',
  //     transaction_id: 'transaction-id',
  //     queue_id: 'queue-id',
  //     status: AccountStatus.ACTIVE,
  //     created_at: new Date('2020/01/01 00:00:00'),
  //     updated_at: new Date('2020/01/01 00:00:00'),
  //   };
  // describe('getAccountNotifications', () => {
  //   const accountId = 'account_id';
  //   const serviceId = 'service_id';
  //   const notificationId = 'notification_id';

  //   const account: AccountEntity = {
  //     account_id: accountId,
  //     service_id: serviceId,
  //     user_id: 'mockUserId',
  //     membership_id: 100,
  //     display_name: 'Test User',
  //     profile_image_url: 'http://example.com/image.png',
  //     token_bound_account_address: '0xBC4CA0EdA7647A8aB7C2061c2E118A18a936f13D',
  //     transaction_id: 'transaction-id',
  //     status: AccountStatus.ACTIVE,
  //     created_at: new Date('2020/01/01 00:00:00'),
  //     updated_at: new Date('2020/01/01 00:00:00'),
  //   };

  //   const accountNotification: AccountNotificationsEntity[] = [
  //     {
  //       account_id: accountId,
  //       service_id: serviceId,
  //       account_notification_id: 'account_notification_id_1',
  //       notification_id: notificationId,
  //       // notification_title: 'Notification 1',
  //       // notification_text: 'Notification 1 description',
  //       // broadcast_date: new Date('2024-10-10'),
  //     },
  //   ];

  //   const globalNotification: GlobalNotificationsEntity[] = [
  //     {
  //       global_notification_id: 'global_notification_id_1',
  //       service_id: serviceId,
  //       notification_id: notificationId,
  //       // notification_title: 'Global Notification 1',
  //       // notification_text: 'Global Notification 1 description',
  //       // broadcast_date: new Date('2024-10-11'),
  //     },
  //     {
  //       global_notification_id: 'global_notification_id_2',
  //       service_id: serviceId,
  //       notification_id: notificationId,
  //       // notification_title: 'Global Notification 2',
  //       // notification_text: 'Global Notification 2 description',
  //       // broadcast_date: new Date('2024-10-12'),
  //     },
  //   ];
  //   const notifications: AccountNotificationsResponse = {
  //     notifications: [
  //       {
  //         notificationId: 'account_notification_id_1',
  //         title: 'Notification 1',
  //         text: 'Notification 1 description',
  //         broadcastDate: new Date('2024-10-10').toISOString(),
  //       },
  //       {
  //         notificationId: 'global_notification_id_1',
  //         title: 'Global Notification 1',
  //         text: 'Global Notification 1 description',
  //         broadcastDate: new Date('2024-10-11').toISOString(),
  //       },
  //       {
  //         notificationId: 'global_notification_id_2',
  //         title: 'Global Notification 2',
  //         text: 'Global Notification 2 description',
  //         broadcastDate: new Date('2024-10-12').toISOString(),
  //       },
  //     ],
  //   };

  //   test('should return account notifications successfully', async () => {
  //     mockAccountRepository.selectAccountById.mockResolvedValue(account);
  //     mockNotificationRepository.selectGlobalNotification.mockResolvedValue(accountNotification);
  //     mockNotificationRepository.selectAccountNotification.mockResolvedValue(globalNotification);

  //     const result = await accountService.getAccountNotifications(accountId, serviceId);

  //     expect(result).toEqual(notifications);
  //     expect(mockAccountRepository.selectAccountById).toHaveBeenCalledWith(accountId, serviceId);
  //   });

  //   test('should throw NotFoundError if account does not exist', async () => {
  //     mockAccountRepository.selectAccountById.mockResolvedValue(undefined);

  //     await expect(accountService.getAccountNotifications(accountId, serviceId)).rejects.toThrow(NotFoundError);
  //     expect(mockAccountRepository.selectAccountById).toHaveBeenCalledWith(accountId, serviceId);
  //   });

  //   test('should return empty array if no notifications are found', async () => {
  //     mockAccountRepository.selectAccountById.mockResolvedValue(account);
  //     mockAccountRepository.getAccountNotifications.mockResolvedValue([]);
  //     mockAccountRepository.getGlobalNotifications.mockResolvedValue([]);

  //     const result = await accountService.getAccountNotifications(accountId, serviceId);

  //     expect(result).toEqual({ accountNotifications: [], globalNotifications: [] });
  //     expect(mockAccountRepository.selectAccountById).toHaveBeenCalledWith(accountId, serviceId);
  //     expect(mockAccountRepository.getAccountNotifications).toHaveBeenCalledWith(accountId, serviceId);
  //   });

  //   test('should handle errors from repository gracefully', async () => {
  //     mockAccountRepository.selectAccountById.mockResolvedValue(account);
  //     mockAccountRepository.getAccountNotifications.mockRejectedValue(new Error('Notification error'));

  //     await expect(accountService.getAccountNotifications(accountId, serviceId)).rejects.toThrow(
  //       new InternalServerError('Failed to retrieve notifications: Error: Notification error'),
  //     );

  //     expect(mockAccountRepository.selectAccountById).toHaveBeenCalledWith(accountId, serviceId);
  //     expect(mockAccountRepository.getAccountNotifications).toHaveBeenCalledWith(accountId, serviceId);
  //   });
  // });
});
