import { NftContractsRepository } from '../../src/repositories/nftContractsRepository';
import { UserOperationService } from '../../src/services/userOperationService';
import { NotFoundError } from '../../src/errors/notFoundError';
import { AccountRepository } from '../../src/repositories/accountRepository';
import { UserOperationQueuesRepository } from '../../src/repositories/userOperationQueuesRepository';
import { AccountEntity } from '../../src/tables/accountTable';
import { MetadataService } from '../../src/services/metadataService';
import { NftMetadatasRepository } from '../../src/repositories/nftMetadatasRepository';
import { ValidationError } from '../../src/errors/validationError';
import { TokenBoundAccountImplementationRepository } from '../../src/repositories/tokenBoundAccountImplementationsRepository';
import { NftBaseMetadatasRepository } from '../../src/repositories/nftBaseMetadatasRepository';
import { NftContractTypesRepository } from '../../src/repositories/nftContractTypesRepository';
import { NftType } from '../../src/enum/nftType';

jest.mock('../../src/repositories/serviceInfoRepository');

describe('UserOperationService', () => {
  let mockNftContractsRepository: jest.Mocked<NftContractsRepository>;
  let mockAccountRepository: jest.Mocked<AccountRepository>;
  let mockUserOperationQueuesRepository: jest.Mocked<UserOperationQueuesRepository>;
  let mockTokenBoundAccountImplementationRepository: jest.Mocked<TokenBoundAccountImplementationRepository>;
  let mockNftMetadatasRepository: jest.Mocked<NftMetadatasRepository>;
  let mockNftBaseMetadatasRepository: jest.Mocked<NftBaseMetadatasRepository>;
  let mockMetadataService: jest.Mocked<MetadataService>;
  let mockNftContractTypesRepository: jest.Mocked<NftContractTypesRepository>;
  let userOperationService: UserOperationService;

  beforeEach(() => {
    process.env.JSON_RPC_URL = 'http://127.0.0.1:8545';
    process.env.ALCHEMY_API_KEY = 'xxxxxsss';
    process.env.ALCHEMY_CHAIN_NAME = 'polygon-amoy';
    process.env.GAS_LIMIT_MULTIPLIER = '200';
    process.env.BASE_MAX_FEE_PER_GAS = '600';
    process.env.MAX_FEE_PER_GAS_MULTIPLIER = '150';
    process.env.MAX_PRIORITY_FEE_PER_GAS_MULTIPLIER = '150';
    process.env.GCP_PROJECT_ID = 'sample_gcp_project_id';

    mockNftContractsRepository = new NftContractsRepository() as jest.Mocked<NftContractsRepository>;
    mockTokenBoundAccountImplementationRepository =
      new TokenBoundAccountImplementationRepository() as jest.Mocked<TokenBoundAccountImplementationRepository>;
    mockNftMetadatasRepository = new NftMetadatasRepository() as jest.Mocked<NftMetadatasRepository>;
    mockNftBaseMetadatasRepository = new NftBaseMetadatasRepository() as jest.Mocked<NftBaseMetadatasRepository>;
    mockNftContractTypesRepository = new NftContractTypesRepository() as jest.Mocked<NftContractTypesRepository>;

    mockMetadataService = new MetadataService(
      mockNftMetadatasRepository,
      mockNftBaseMetadatasRepository,
      mockNftContractTypesRepository,
    ) as jest.Mocked<MetadataService>;

    mockAccountRepository = { selectAccountById: jest.fn() } as unknown as jest.Mocked<AccountRepository>;
    mockUserOperationQueuesRepository = {
      insertQueue: jest.fn(),
    } as unknown as jest.Mocked<UserOperationQueuesRepository>;
    mockNftContractsRepository = {
      selectNftContractAddressAndNftContractAbiUrl: jest.fn(),
    } as unknown as jest.Mocked<NftContractsRepository>;

    mockTokenBoundAccountImplementationRepository = {
      selectAbiByServiceId: jest.fn(),
    } as unknown as jest.Mocked<TokenBoundAccountImplementationRepository>;

    mockMetadataService = {
      getParsedMetadataFromBaseMetadata: jest.fn(),
    } as unknown as jest.Mocked<MetadataService>;

    userOperationService = new UserOperationService(
      mockNftContractsRepository,
      mockAccountRepository,
      mockUserOperationQueuesRepository,
      mockTokenBoundAccountImplementationRepository,
      mockMetadataService,
    );
  });

  describe('prepareForConsumption', () => {
    test('should return encoded ABI for useCoupon function', async () => {
      const serviceId = 'testServiceId';
      const nftContractId = 'testNftContractId';
      const nftContractAddress = '0x47eb2E73a00B86D82d0DF5A07f5FBF427e28dC77';
      const tokenId = 0;
      const expiredDate = new Date(Date.now() + 1000).toISOString();

      const nftContractEntity = {
        nft_contract_id: nftContractId,
        nft_contract_address: '0x47eb2E73a00B86D82d0DF5A07f5FBF427e28dC77',
        nft_contract_abi: [
          {
            type: 'function',
            name: 'useCoupon',
            inputs: [
              { name: 'tokenId', type: 'uint256' },
              { name: 'quantity', type: 'uint256' },
            ],
          },
        ] as object,
        service_id: 'testServiceId',
        nft_contract_type_id: 'sampleTypeId',
        nft_collection_name: 'sampleCollectionName',
        nft_contract_type_name: 'sampleTypeName',
        nft_contract_type_detail: 'sampleTypeDetail',
        nft_contract_binary: '0x1234567890',
        nft_type: NftType.CERTIFICATE,
      };

      const token_bound_account_registry_abi = [
        {
          type: 'function',
          name: 'execute',
          inputs: [
            { name: 'to', type: 'address' },
            { name: 'value', type: 'uint256' },
            { name: 'data', type: 'bytes' },
            { name: 'operation', type: 'uint256' },
          ],
        },
      ] as object;
      mockNftContractsRepository.selectNftContractAddressAndNftContractAbiUrl.mockResolvedValue(nftContractEntity);
      mockTokenBoundAccountImplementationRepository.selectAbiByServiceId.mockResolvedValue(
        token_bound_account_registry_abi,
      );
      mockMetadataService.getParsedMetadataFromBaseMetadata.mockResolvedValue({
        name: 'Sample NFT',
        description: 'Sample description',
        image: 'http://example.com/image.png',
        external_url: 'http://example.com',
        animation_url: null,
        background_color: null,
        youtube_url: null,
        attributes: [{ trait_type: 'to_date', value: expiredDate }],
      });

      const result = await userOperationService.prepareForConsumption(serviceId, nftContractAddress, tokenId);

      expect(result).toBeDefined();
      expect(mockNftContractsRepository.selectNftContractAddressAndNftContractAbiUrl).toHaveBeenCalledWith(
        nftContractAddress,
        serviceId,
      );
    });

    test('should throw ValidationError if coupon is expired', async () => {
      const serviceId = 'testServiceId';
      const nftContractId = 'testNftContractId';
      const tokenId = 1;
      const expiredDate = new Date(Date.now() - 1000).toISOString();

      const nftContractEntity = {
        service_id: 'testServiceId',
        nft_contract_type_id: 'testContractID',
        nft_collection_name: 'test',
        nft_contract_type_name: 'COUPON',
        nft_contract_type_detail: 'description test',
        nft_contract_binary: '0x1234567890abcdef',
        nft_contract_id: nftContractId,
        nft_contract_address: '0x1234567890abcdef',
        nft_contract_abi: [
          {
            type: 'function',
            name: 'useCoupon',
            inputs: [
              { name: 'tokenId', type: 'uint256' },
              { name: 'quantity', type: 'uint256' },
            ],
          },
        ] as unknown as object,
        nft_type: NftType.COUPON,
      };

      mockNftContractsRepository.selectNftContractAddressAndNftContractAbiUrl.mockResolvedValue(nftContractEntity);
      mockMetadataService.getParsedMetadataFromBaseMetadata.mockResolvedValue({
        name: 'Sample NFT',
        description: 'Sample description',
        image: 'http://example.com/image.png',
        external_url: 'http://example.com',
        animation_url: null,
        background_color: null,
        youtube_url: null,
        attributes: [{ trait_type: 'to_date', value: expiredDate }],
      });

      await expect(userOperationService.prepareForConsumption(serviceId, nftContractId, tokenId)).rejects.toThrow(
        ValidationError,
      );
    });
  });

  describe('UserOperationService - storeUserOperationInfo', () => {
    const mockServiceId = 'testServiceId';
    const mockAccountId = 'testAccountId';
    const uoHash = '0xuoHash123';

    const accountMock: AccountEntity = {
      account_id: mockAccountId,
      service_id: mockServiceId,
      user_id: '1',
      membership_id: 1,
      display_name: 'bang',
      profile_image_url: 'test',
      token_bound_account_address: '0xBC4CA0EdA7647A8aB7C2061c2E118A18a936f13D',
      transaction_id: 'transaction-id',
      queue_id: 'queue-id',
      status: 'ACTIVE',
      created_at: new Date('2020/01/01 00:00:00'),
      updated_at: new Date('2020/01/01 00:00:00'),
      last_login_at: new Date('2020/01/01 00:00:00'),
      membership_metadata_url: 'https://example.com/membership',
    }
    test('should store user operation info and return operation_id', async () => {
      mockAccountRepository.selectAccountById.mockResolvedValue(accountMock);
      mockUserOperationQueuesRepository.insertQueue.mockResolvedValue({
        operation_id: expect.any(String),
        from_address: accountMock.token_bound_account_address as string,
        hex_encoded_user_operation_data: undefined,
        signature: undefined,
        tx_hash: undefined,
        uo_hash: uoHash,
        status: 'EXECUTED',
      });

      await userOperationService.storeUserOperationInfo(mockServiceId, mockAccountId, uoHash);

      expect(mockAccountRepository.selectAccountById).toHaveBeenCalledWith(mockAccountId, mockServiceId);
      expect(mockUserOperationQueuesRepository.insertQueue).toHaveBeenCalledWith({
        operation_id: expect.any(String),
        from_address: accountMock.token_bound_account_address,
        hex_encoded_user_operation_data: undefined,
        signature: undefined,
        tx_hash: undefined,
        uo_hash: uoHash,
        status: 'EXECUTED',
      });
    });

    test('should throw NotFoundError if account is not found', async () => {
      mockAccountRepository.selectAccountById.mockResolvedValue(undefined);

      await expect(userOperationService.storeUserOperationInfo(mockServiceId, mockAccountId, uoHash)).rejects.toThrow(
        NotFoundError,
      );

      expect(mockAccountRepository.selectAccountById).toHaveBeenCalledWith(mockAccountId, mockServiceId);
      expect(mockUserOperationQueuesRepository.insertQueue).not.toHaveBeenCalled();
    });
  });
});
