import { AuthService } from '../../src/services/authService';
import { AuthProviderRepository } from '../../src/repositories/authProviderRepository';
import { UserService } from '../../src/services/userService';
import { ConflictError } from '../../src/errors/conflictError';
import { NotFoundError } from '../../src/errors/notFoundError';
import { FirebaseComponent } from '../../src/components/firebaseComponent';
import { LineComponent } from '../../src/components/lineComponent';
import { v4 as uuidv4 } from 'uuid';
import { UserResponse } from '../../src/responsedto/userResponse';
import { ServiceInfoRepository } from '../../src/repositories/serviceInfoRepository';
import { AccountRepository } from '../../src/repositories/accountRepository';

jest.mock('uuid', () => ({
  v4: jest.fn().mockReturnValue('mockUuid'),
}));
jest.mock('../../src/repositories/authProviderRepository', () => ({
  AuthProviderRepository: jest.fn().mockImplementation(() => {
    return {
      selectAuthProvider: jest.fn(),
      insertAuthProvider: jest.fn(),
    };
  }),
}));
jest.mock('../../src/services/userService', () => ({
  UserService: jest.fn().mockImplementation(() => {
    return {
      getUser: jest.fn(),
    };
  }),
}));
jest.mock('../../src/components/firebaseComponent', () => ({
  FirebaseComponent: jest.fn().mockImplementation(() => {
    return {
      verifyFirebaseIdToken: jest.fn(),
      createCustomToken: jest.fn(),
    };
  }),
}));
jest.mock('../../src/components/lineComponent', () => ({
  LineComponent: jest.fn().mockImplementation(() => {
    return {
      verifyLineIdToken: jest.fn(),
    };
  }),
}));
jest.mock('../../src/repositories/serviceInfoRepository', () => ({
  ServiceInfoRepository: jest.fn().mockImplementation(() => {
    return {
      selectLineChannelId: jest.fn(),
    };
  }),
}));

describe('AuthService', () => {
  let authService: AuthService;
  let mockAuthProviderRepository: jest.Mocked<AuthProviderRepository>;
  let mockUserService: jest.Mocked<UserService>;
  let mockFirebaseComponent: jest.Mocked<FirebaseComponent>;
  let mockLineComponent: jest.Mocked<LineComponent>;
  let mockServiceInfoRepository: jest.Mocked<ServiceInfoRepository>;
  let mockAccountRepository: jest.Mocked<AccountRepository>;

  beforeEach(() => {
    process.env.JSON_RPC_URL = 'http://127.0.0.1:8545';
    process.env.ALCHEMY_API_KEY = 'xxxxxsss';
    process.env.ALCHEMY_CHAIN_NAME = 'polygon-amoy';
    process.env.GAS_LIMIT_MULTIPLIER = '200';
    process.env.BASE_MAX_FEE_PER_GAS = '600';
    process.env.MAX_FEE_PER_GAS_MULTIPLIER = '150';
    process.env.MAX_PRIORITY_FEE_PER_GAS_MULTIPLIER = '150';
    process.env.GCP_PROJECT_ID = 'sample_gcp_project_id';

    mockAuthProviderRepository = {
      selectAuthProvider: jest.fn(),
      insertAuthProvider: jest.fn(),
    } as unknown as jest.Mocked<AuthProviderRepository>;

    mockUserService = {
      getUser: jest.fn(),
    } as unknown as jest.Mocked<UserService>;

    mockFirebaseComponent = {
      verifyFirebaseIdToken: jest.fn(),
      createCustomToken: jest.fn(),
    } as unknown as jest.Mocked<FirebaseComponent>;

    mockLineComponent = {
      verifyLineIdToken: jest.fn(),
    } as unknown as jest.Mocked<LineComponent>;

    mockServiceInfoRepository = {
      selectLineChannelId: jest.fn(),
    } as unknown as jest.Mocked<ServiceInfoRepository>;

    mockAccountRepository = {
      selectAccount: jest.fn(),
      insertAccount: jest.fn(),
      selectAccountByUserId: jest.fn(),
    } as unknown as jest.Mocked<AccountRepository>;

    authService = new AuthService(
      mockAuthProviderRepository,
      mockUserService,
      mockLineComponent,
      mockFirebaseComponent,
      mockServiceInfoRepository,
      mockAccountRepository,
    );
  });

  describe('linkLineIdToken', () => {
    test('should return custom token successfully', async () => {
      const mockClientId = 'clientId';
      const mockFirebaseIdToken = 'mockFirebaseIdToken';
      const mockLineIdToken = 'mockLineIdToken';
      const mockServiceId = 'mockServiceId';
      const mockFirebaseVerification = {
        uid: 'mockUid',
        aud: 'mockAud',
        auth_time: **********,
        exp: **********,
        firebase: {
          identities: {},
          sign_in_provider: 'mockProvider',
        },
        iat: **********,
        iss: 'mockIss',
        sub: 'mockSub',
      };
      const mockLineVerification = {
        sub: 'mockProviderId',
        name: 'Test User',
        picture: 'http://example.com/image.png',
      };
      const mockCustomToken = 'mockCustomToken';

      mockServiceInfoRepository.selectLineChannelId.mockResolvedValue(mockClientId);
      mockFirebaseComponent.verifyFirebaseIdToken.mockResolvedValue(mockFirebaseVerification);
      mockLineComponent.verifyLineIdToken.mockResolvedValue(mockLineVerification);
      mockAuthProviderRepository.selectAuthProvider.mockResolvedValue(undefined);
      const user = new UserResponse('mockUid', 'US', '**********', '0x**********abcdef', 'mock-mnemonic-backup-key');
      mockUserService.getUser.mockResolvedValue(user);
      mockFirebaseComponent.createCustomToken.mockResolvedValue(mockCustomToken);

      const result = await authService.linkLineIdToken(mockFirebaseIdToken, mockLineIdToken, mockServiceId);

      expect(result).toEqual({ token: mockCustomToken });
      expect(mockAuthProviderRepository.selectAuthProvider).toHaveBeenCalledWith(
        mockLineVerification.sub,
        mockServiceId,
      );
      expect(mockAuthProviderRepository.insertAuthProvider).toHaveBeenCalledWith({
        provider_id: uuidv4(),
        provider_uid: mockLineVerification.sub,
        user_id: mockFirebaseVerification.uid,
        service_id: mockServiceId,
        provider_name: 'line',
      });
      expect(mockFirebaseComponent.createCustomToken).toHaveBeenCalledWith(mockFirebaseVerification.uid, {
        access: {
          provider: 'line',
          providerUid: 'mockProviderId',
          serviceId: 'mockServiceId',
        },
      });
    });

    test('should throw ConflictError if auth provider is already linked', async () => {
      const mockClientId = 'clientId';
      const mockFirebaseIdToken = 'mockFirebaseIdToken';
      const mockLineIdToken = 'mockLineIdToken';
      const mockServiceId = 'mockServiceId';
      const mockFirebaseVerification = {
        uid: 'mockUid',
        aud: 'mockAud',
        auth_time: **********,
        exp: **********,
        firebase: {
          sign_in_provider: 'mockProvider',
          identities: {},
        },
        iat: **********,
        iss: 'mockIssuer',
        sub: 'mockUid',
      };
      const mockAuthProvider = {
        provider_id: 'mockProviderId',
        provider_uid: 'mockProviderId',
        user_id: 'mockUid',
        service_id: 'mockServiceId',
        provider_name: 'line',
      };
      const mockLineVerification = {
        sub: 'mockProviderId',
        name: 'Test User',
        picture: 'http://example.com/image.png',
      };
      mockServiceInfoRepository.selectLineChannelId.mockResolvedValue(mockClientId);
      mockFirebaseComponent.verifyFirebaseIdToken.mockResolvedValue(mockFirebaseVerification);
      mockLineComponent.verifyLineIdToken.mockResolvedValue(mockLineVerification);
      mockAuthProviderRepository.selectAuthProvider.mockResolvedValue(mockAuthProvider);

      await expect(authService.linkLineIdToken(mockFirebaseIdToken, mockLineIdToken, mockServiceId)).rejects.toThrow(
        ConflictError,
      );
    });

    test('should throw NotFoundError if user is not found', async () => {
      const mockClientId = 'clientId';
      const mockFirebaseIdToken = 'mockFirebaseIdToken';
      const mockLineIdToken = 'mockLineIdToken';
      const mockServiceId = 'mockServiceId';
      const mockFirebaseVerification = {
        uid: 'mockUid',
        aud: 'mockAud',
        auth_time: **********,
        exp: **********,
        firebase: {
          sign_in_provider: 'mockProvider',
          identities: {},
        },
        iat: **********,
        iss: 'mockIssuer',
        sub: 'mockUid',
      };
      const mockLineVerification = {
        sub: 'mockProviderId',
        name: 'Test User',
        picture: 'http://example.com/image.png',
      };

      mockServiceInfoRepository.selectLineChannelId.mockResolvedValue(mockClientId);
      mockFirebaseComponent.verifyFirebaseIdToken.mockResolvedValue(mockFirebaseVerification);
      mockLineComponent.verifyLineIdToken.mockResolvedValue(mockLineVerification);
      mockAuthProviderRepository.selectAuthProvider.mockResolvedValue(undefined);
      mockUserService.getUser.mockRejectedValue(new NotFoundError('User not found'));

      await expect(authService.linkLineIdToken(mockFirebaseIdToken, mockLineIdToken, mockServiceId)).rejects.toThrow(
        NotFoundError,
      );
    });
  });

  describe('getCustomToken', () => {
    test('should return custom token successfully', async () => {
      const mockClientId = 'clientId';
      const mockLineIdToken = 'mockLineIdToken';
      const mockServiceId = 'mockServiceId';
      const mockLineVerification = {
        sub: 'mockProviderId',
        name: 'Test User',
        picture: 'http://example.com/image.png',
      };
      const mockAuthProvider = {
        provider_id: 'mockProviderId',
        provider_uid: 'mockProviderId',
        user_id: 'mockUid',
        service_id: 'mockServiceId',
        provider_name: 'line',
      };
      const mockCustomToken = 'mockCustomToken';

      mockServiceInfoRepository.selectLineChannelId.mockResolvedValue(mockClientId);
      mockAccountRepository.selectAccountByUserId.mockResolvedValue({
        service_id: mockServiceId,
        account_id: 'mockAccountId',
        user_id: 'mockUid',
        queue_id: 'mockQueueId',
        transaction_id: 'mockTransactionId',
        status: 'active',
        membership_id: 1001,
        membership_metadata_url: 'https://example.com/metadata',
        display_name: 'Test User',
        profile_image_url: 'http://example.com/image.png',
        token_bound_account_address: '0x**********abcdef0123456789abcdef0123456789abcdef0123456789abcdef',
        created_at: new Date(),
        updated_at: new Date(),
        last_login_at: new Date(),
      });
      mockLineComponent.verifyLineIdToken.mockResolvedValue(mockLineVerification);
      mockAuthProviderRepository.selectAuthProvider.mockResolvedValue(mockAuthProvider);
      mockFirebaseComponent.createCustomToken.mockResolvedValue(mockCustomToken);

      const result = await authService.getCustomToken(mockLineIdToken, mockServiceId);

      expect(result).toEqual({ token: mockCustomToken });
      expect(mockAuthProviderRepository.selectAuthProvider).toHaveBeenCalledWith(
        mockLineVerification.sub,
        mockServiceId,
      );
      expect(mockFirebaseComponent.createCustomToken).toHaveBeenCalledWith(mockAuthProvider.user_id, {
        access: {
          account_id: 'mockAccountId',
          service_id: mockServiceId,
        },
        provider: {
          name: 'line',
          uid: 'mockProviderId',
        },
      });
    });

    test('should throw NotFoundError if auth provider is not found', async () => {
      const mockClientId = 'clientId';
      const mockLineIdToken = 'mockLineIdToken';
      const mockServiceId = 'mockServiceId';
      const mockLineVerification = {
        sub: 'mockProviderId',
        name: 'Test User',
        picture: 'http://example.com/image.png',
      };

      mockServiceInfoRepository.selectLineChannelId.mockResolvedValue(mockClientId);
      mockLineComponent.verifyLineIdToken.mockResolvedValue(mockLineVerification);
      mockAuthProviderRepository.selectAuthProvider.mockResolvedValue(undefined);

      await expect(authService.getCustomToken(mockLineIdToken, mockServiceId)).rejects.toThrow(NotFoundError);
    });
  });
});
