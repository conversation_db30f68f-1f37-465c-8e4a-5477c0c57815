import { ServiceInfoService } from '../../src/services/serviceInfoService';
import { RegisterServiceResponse, Service } from '../../src/dtos/services/schemas';
import { ServiceInfoRepository } from '../../src/repositories/serviceInfoRepository';
import { NotFoundError } from '../../src/errors/notFoundError';
import { ServiceEntity, ServiceWithTranslations } from '../../src/tables/servicesTable';
import { TenantRepository } from '../../src/repositories/tenantRepository';
import { TokenBoundAccountRegistryAddressRepository } from '../../src/repositories/tokenBoundAccountRegistryAddressRepository';
import { TokenBoundAccountImplementationRepository } from '../../src/repositories/tokenBoundAccountImplementationsRepository';
import { TokenBoundAccountRegistryEntity } from '../../src/tables/tokenBoundAccountRegistriesTable';
import { TokenBoundAccountImplementationEntity } from '../../src/tables/tokenBoundAccountImplementationTable';
import { RegisterServiceRequest } from '../../src/dtos/services/schemas';
import {
  ServiceTranslationEntity,
} from '../../src/tables/translations/serviceTranslationsTable';
import { languageCode } from '../../src/enum/languageCode';

jest.mock('../../src/repositories/tenantRepository');
jest.mock('../../src/repositories/serviceInfoRepository');
jest.mock('../../src/repositories/tokenBoundAccountRegistryAddressRepository');
jest.mock('../../src/repositories/tokenBoundAccountImplementationsRepository');
jest.mock('uuid', () => ({
  v4: jest.fn().mockReturnValue('mockServiceId'),
}));

describe('ServiceInfoService', () => {
  let serviceInfoService: ServiceInfoService;
  let mockServiceInfoRepository: jest.Mocked<ServiceInfoRepository>;
  let mockTenantRepository: jest.Mocked<TenantRepository>;
  let mockTokenBoundAccountRegistryAddressRepository: jest.Mocked<TokenBoundAccountRegistryAddressRepository>;
  let mockTokenBoundAccountImplementationRepository: jest.Mocked<TokenBoundAccountImplementationRepository>;

  beforeEach(() => {
    mockServiceInfoRepository = new ServiceInfoRepository() as jest.Mocked<ServiceInfoRepository>;
    mockTenantRepository = new TenantRepository() as jest.Mocked<TenantRepository>;
    mockTokenBoundAccountRegistryAddressRepository =
      new TokenBoundAccountRegistryAddressRepository() as jest.Mocked<TokenBoundAccountRegistryAddressRepository>;
    mockTokenBoundAccountImplementationRepository =
      new TokenBoundAccountImplementationRepository() as jest.Mocked<TokenBoundAccountImplementationRepository>;
    serviceInfoService = new ServiceInfoService(
      mockServiceInfoRepository,
      mockTenantRepository,
      mockTokenBoundAccountRegistryAddressRepository,
      mockTokenBoundAccountImplementationRepository,
    );
  });

  describe('getServiceInfo', () => {
    test('should return service info with correct data', async () => {
      const serviceId = '8552df83-cbd5-44db-b67e-0cbeb2785918';

      const mockServiceWithTranslations: ServiceWithTranslations = {
        service_id: serviceId,
        tenant_id: 'tenantId',
        service_name: 'Test Service',
        service_url: 'https://test-service.com',
        service_policy:
          'IyBIZWxsbyB3b3JsZCEKIyMgcG9saWN5IGRhdGEKVGhpcyBpcyB3aGVyZSB0aGUgdGV4dCBvZiB0aGUgcG9saWN5IGRhdGEgd2lsbCBiZSBsaXN0ZWQuCgotIGhpbnQgMQotIGhpbnQgMgoKW2ltYWdlXShodHRwczovL2ltYWdlLmNvbSkKCg==',
        service_logo_image_url: 'https://marbullx.com/logo',
        market_cover_image_url: 'https://example.com/market-cover.jpg',
        theme_primary_color_lowest: '0xFFFFFFFFFFFF',
        theme_primary_color_lower: '0xFFFFFFFFFFFF',
        theme_primary_color_higher: '0xFFFFFFFFFFFF',
        theme_primary_color_highest: '0xFFFFFFFFFFFF',
        is_market_enabled: true,
        service_pane: 'xxxxxx',
        membership_nft_contract_id: '0x1234567768',
        stripe_account_id: 'mockStripeAccountId',
        line_channel_id: 'line_channel_id',
        commission_rate: 0.1,
        modular_contract_id: '0x1234567768',
      };

      const expectedResponse: Service = {
        serviceId: serviceId,
        name: 'Test Service',
        policy:
          'IyBIZWxsbyB3b3JsZCEKIyMgcG9saWN5IGRhdGEKVGhpcyBpcyB3aGVyZSB0aGUgdGV4dCBvZiB0aGUgcG9saWN5IGRhdGEgd2lsbCBiZSBsaXN0ZWQuCgotIGhpbnQgMQotIGhpbnQgMgoKW2ltYWdlXShodHRwczovL2ltYWdlLmNvbSkKCg==',
        logoImageUrl: 'https://marbullx.com/logo',
        marketCoverImageUrl: 'https://example.com/market-cover.jpg', // Added missing property
        themePrimaryColorLowest: '0xFFFFFFFFFFFF',
        themePrimaryColorLower: '0xFFFFFFFFFFFF',
        themePrimaryColorHigher: '0xFFFFFFFFFFFF',
        themePrimaryColorHighest: '0xFFFFFFFFFFFF',
        isMarketEnabled: true,
      };

      mockServiceInfoRepository.getServiceWithTranslationsById.mockResolvedValue(mockServiceWithTranslations);

      const result = await serviceInfoService.getServiceById(serviceId, languageCode.EN_US);

      expect(result).toEqual(expectedResponse);
      expect(mockServiceInfoRepository.getServiceWithTranslationsById).toHaveBeenCalledWith(
        serviceId,
        languageCode.EN_US,
      );
    });

    test('should throw NotFoundError if service does not exist', async () => {
      const serviceId = 'nonExistentServiceId';

      mockServiceInfoRepository.getServiceWithTranslationsById.mockResolvedValue(undefined);

      await expect(serviceInfoService.getServiceById(serviceId, languageCode.EN_US)).rejects.toThrow(NotFoundError);
      expect(mockServiceInfoRepository.getServiceWithTranslationsById).toHaveBeenCalledWith(
        serviceId,
        languageCode.EN_US,
      );
    });
  });

  describe('registerService', () => {
    test('should successfully register a new service', async () => {
      const tenantId = 'validTenantId';
      const serviceId = 'bbc1499b-c47c-4c9b-befc-5717dff1c5ac';
      const registryId = 'eb24c15e-e9e8-4c59-81bf-8ac4e488970c';
      const implementationId = '8e585558-082e-4254-b140-cb1db5dca105';

      const registerRequest: RegisterServiceRequest = {
        serviceTranslations: [
          {
            language: languageCode.EN_US,
            serviceName: 'My New Service',
            servicePolicy: 'RXhhbXBsZSBzZXJ2aWNlIHBvbGljeSBjb250ZW50',
            servicePane: 'eyJrZXkiOiAiVmFsdWUifQ==',
          },
          {
            language: languageCode.JA,
            serviceName: '新しいサービス',
            servicePolicy: 'RXhhbXBsZSBzZXJ2aWNlIHBvbGljeSBjb250ZW50',
            servicePane: 'eyJrZXkiOiAiVmFsdWUifQ==',
          },
        ],
        serviceUrl: 'https://www.newservice.com',
        serviceLogoImageUrl: 'https://www.newservice.com/logo.png',
        themePrimaryColorLowest: '#FFFFFFF0',
        themePrimaryColorLower: '#FFFFFFF1',
        themePrimaryColorHigher: '#FFFFFFF2',
        themePrimaryColorHighest: '#FFFFFFF3',
        isMarketEnabled: true,
        stripeAccountId: 'acct_123ABC',
        lineChannelId: 'acct_123ABC',
        marketCoverImageUrl: undefined,
        modularContractId: 'acct_123ABC',
      };

      mockTenantRepository.selectTenantById.mockResolvedValue({
        tenant_id: tenantId,
        tenant_name: 'Tenant Name',
        plan_id: 'plan_id',
      });
      const mockService: ServiceEntity = {
        service_id: serviceId,
        tenant_id: tenantId,
        service_url: registerRequest.serviceUrl,
        service_logo_image_url: registerRequest.serviceLogoImageUrl,
        theme_primary_color_lowest: registerRequest.themePrimaryColorLowest || undefined,
        theme_primary_color_lower: registerRequest.themePrimaryColorLower || undefined,
        theme_primary_color_higher: registerRequest.themePrimaryColorHigher || undefined,
        theme_primary_color_highest: registerRequest.themePrimaryColorHighest || undefined,
        membership_nft_contract_id: undefined,
        is_market_enabled: registerRequest.isMarketEnabled,
        stripe_account_id: registerRequest.stripeAccountId || undefined,
        market_cover_image_url: undefined,
        line_channel_id: 'acct_123ABC',
        commission_rate: 0.064,
        modular_contract_id: 'acct_123ABC',
      };

      const mockServiceTranslations: ServiceTranslationEntity[] = [
        {
          service_id: serviceId,
          language: languageCode.EN_US,
          service_name: registerRequest.serviceTranslations[0].serviceName,
          service_policy: registerRequest.serviceTranslations[0].servicePolicy,
          service_pane: registerRequest.serviceTranslations[0].servicePane,
        },
        {
          service_id: serviceId,
          language: languageCode.JA,
          service_name: registerRequest.serviceTranslations[1].serviceName,
          service_policy: registerRequest.serviceTranslations[1].servicePolicy,
          service_pane: registerRequest.serviceTranslations[1].servicePane,
        },
      ];

      const mockResponse: RegisterServiceResponse = {
        serviceId: serviceId,
        serviceTranslations: [
          {
            language: languageCode.EN_US,
            serviceName: 'My New Service',
            servicePolicy: 'RXhhbXBsZSBzZXJ2aWNlIHBvbGljeSBjb250ZW50',
            servicePane: 'eyJrZXkiOiAiVmFsdWUifQ==',
          },
          {
            language: languageCode.JA,
            serviceName: '新しいサービス',
            servicePolicy: 'RXhhbXBsZSBzZXJ2aWNlIHBvbGljeSBjb250ZW50',
            servicePane: 'eyJrZXkiOiAiVmFsdWUifQ==',
          },
        ],
        logoImageUrl: 'https://www.newservice.com/logo.png',
        marketCoverImageUrl: undefined, // Added missing property
        themePrimaryColorLowest: '#FFFFFFF0',
        themePrimaryColorLower: '#FFFFFFF1',
        themePrimaryColorHigher: '#FFFFFFF2',
        themePrimaryColorHighest: '#FFFFFFF3',
        isMarketEnabled: true,
      };
      mockServiceInfoRepository.insertService.mockResolvedValue(mockService);
      mockServiceInfoRepository.insertServiceTranslations.mockResolvedValue(mockServiceTranslations);
      const mockRegistry: TokenBoundAccountRegistryEntity = {
        service_id: serviceId,
        token_bound_account_registry_id: registryId,
        token_bound_account_registry_address: '0x000',
        salt: '0x000',
        chain_id: '0',
        abi: {},
      };
      mockTokenBoundAccountRegistryAddressRepository.insertAbi.mockResolvedValue(mockRegistry);

      const mockImplementation: TokenBoundAccountImplementationEntity = {
        service_id: serviceId,
        token_bound_account_registry_id: registryId,
        token_bound_account_implementation_id: implementationId,
        token_bound_account_implementation_address: '0x000',
        token_bound_account_implementation_abi: {},
      };
      mockTokenBoundAccountImplementationRepository.insertAbi.mockResolvedValue(mockImplementation);
      mockServiceInfoRepository.getServiceById.mockResolvedValue(mockService);

      const result = await serviceInfoService.registerService(tenantId, registerRequest);

      expect(result).toEqual(mockResponse);
    });

    test('should throw NotFoundError when tenant does not exist', async () => {
      const tenantId = 'invalidTenantId';
      const registerRequest: RegisterServiceRequest = {
        serviceTranslations: [
          {
            language: languageCode.EN_US,
            serviceName: 'My New Service',
            servicePolicy: 'RXhhbXBsZSBzZXJ2aWNlIHBvbGljeSBjb250ZW50',
            servicePane: 'eyJrZXkiOiAiVmFsdWUifQ==',
          },
        ],
        serviceUrl: 'https://www.newservice.com',
        serviceLogoImageUrl: 'https://www.newservice.com/logo.png',
        themePrimaryColorLowest: '#FFFFFFF0',
        themePrimaryColorLower: '#FFFFFFF1',
        themePrimaryColorHigher: '#FFFFFFF2',
        themePrimaryColorHighest: '#FFFFFFF3',
        isMarketEnabled: true,
        stripeAccountId: 'acct_123ABC',
        lineChannelId: 'acct_123ABC',
        marketCoverImageUrl: undefined,
        modularContractId: 'acct_123ABC',
      };

      mockTenantRepository.selectTenantById.mockResolvedValue(undefined);

      await expect(serviceInfoService.registerService(tenantId, registerRequest)).rejects.toThrow(NotFoundError);
    });
  });
});
