import { ServiceInfoService } from '../../src/services/serviceInfoService';
import { RegisterServiceResponse, Service } from '../../src/dtos/services/schemas';
import { ServiceInfoRepository } from '../../src/repositories/serviceInfoRepository';
import { NotFoundError } from '../../src/errors/notFoundError';
import { ServiceEntity, ServiceWithTranslations } from '../../src/tables/servicesTable';
import { TenantRepository } from '../../src/repositories/tenantRepository';
import { TokenBoundAccountRegistryAddressRepository } from '../../src/repositories/tokenBoundAccountRegistryAddressRepository';
import { TokenBoundAccountImplementationRepository } from '../../src/repositories/tokenBoundAccountImplementationsRepository';
import { TokenBoundAccountRegistryEntity } from '../../src/tables/tokenBoundAccountRegistriesTable';
import { TokenBoundAccountImplementationEntity } from '../../src/tables/tokenBoundAccountImplementationTable';
import { RegisterServiceRequest } from '../../src/dtos/services/schemas';
import {
  ServiceTranslationEntity,
} from '../../src/tables/translations/serviceTranslationsTable';
import { languageCode } from '../../src/enum/languageCode';
import { ServicesCustomFieldsRepository } from '../../src/repositories/servicesCustomFieldsRepository';
import { CustomFieldType } from '../../src/enum/customFieldType';

jest.mock('../../src/repositories/tenantRepository');
jest.mock('../../src/repositories/serviceInfoRepository');
jest.mock('../../src/repositories/tokenBoundAccountRegistryAddressRepository');
jest.mock('../../src/repositories/tokenBoundAccountImplementationsRepository');
jest.mock('../../src/repositories/servicesCustomFieldsRepository');
jest.mock('uuid', () => ({
  v4: jest.fn().mockReturnValue('mockServiceId'),
}));

describe('ServiceInfoService', () => {
  let serviceInfoService: ServiceInfoService;
  let mockServiceInfoRepository: jest.Mocked<ServiceInfoRepository>;
  let mockTenantRepository: jest.Mocked<TenantRepository>;
  let mockTokenBoundAccountRegistryAddressRepository: jest.Mocked<TokenBoundAccountRegistryAddressRepository>;
  let mockTokenBoundAccountImplementationRepository: jest.Mocked<TokenBoundAccountImplementationRepository>;
  let mockServicesCustomFieldsRepository: jest.Mocked<ServicesCustomFieldsRepository>;

  beforeEach(() => {
    mockServiceInfoRepository = new ServiceInfoRepository() as jest.Mocked<ServiceInfoRepository>;
    mockTenantRepository = new TenantRepository() as jest.Mocked<TenantRepository>;
    mockTokenBoundAccountRegistryAddressRepository =
      new TokenBoundAccountRegistryAddressRepository() as jest.Mocked<TokenBoundAccountRegistryAddressRepository>;
    mockTokenBoundAccountImplementationRepository =
      new TokenBoundAccountImplementationRepository() as jest.Mocked<TokenBoundAccountImplementationRepository>;
    mockServicesCustomFieldsRepository = new ServicesCustomFieldsRepository() as jest.Mocked<ServicesCustomFieldsRepository>;
    serviceInfoService = new ServiceInfoService(
      mockServiceInfoRepository,
      mockTenantRepository,
      mockTokenBoundAccountRegistryAddressRepository,
      mockTokenBoundAccountImplementationRepository,
      mockServicesCustomFieldsRepository,
    );
  });

  describe('getServiceInfo', () => {
    test('should return service info with correct data', async () => {
      const serviceId = '8552df83-cbd5-44db-b67e-0cbeb2785918';

      const mockServiceWithTranslations: ServiceWithTranslations = {
        service_id: serviceId,
        tenant_id: 'tenantId',
        service_name: 'Test Service',
        service_url: 'https://test-service.com',
        service_policy:
          'IyBIZWxsbyB3b3JsZCEKIyMgcG9saWN5IGRhdGEKVGhpcyBpcyB3aGVyZSB0aGUgdGV4dCBvZiB0aGUgcG9saWN5IGRhdGEgd2lsbCBiZSBsaXN0ZWQuCgotIGhpbnQgMQotIGhpbnQgMgoKW2ltYWdlXShodHRwczovL2ltYWdlLmNvbSkKCg==',
        service_logo_image_url: 'https://marbullx.com/logo',
        market_cover_image_url: 'https://example.com/market-cover.jpg',
        theme_primary_color_lowest: '0xFFFFFFFFFFFF',
        theme_primary_color_lower: '0xFFFFFFFFFFFF',
        theme_primary_color_higher: '0xFFFFFFFFFFFF',
        theme_primary_color_highest: '0xFFFFFFFFFFFF',
        is_market_enabled: true,
        service_pane: 'xxxxxx',
        membership_nft_contract_id: '0x1234567768',
        stripe_account_id: 'mockStripeAccountId',
        line_channel_id: 'line_channel_id',
        commission_rate: 0.1,
        modular_contract_id: '0x1234567768',
      };

      const expectedResponse: Service = {
        serviceId: serviceId,
        name: 'Test Service',
        policy:
          'IyBIZWxsbyB3b3JsZCEKIyMgcG9saWN5IGRhdGEKVGhpcyBpcyB3aGVyZSB0aGUgdGV4dCBvZiB0aGUgcG9saWN5IGRhdGEgd2lsbCBiZSBsaXN0ZWQuCgotIGhpbnQgMQotIGhpbnQgMgoKW2ltYWdlXShodHRwczovL2ltYWdlLmNvbSkKCg==',
        logoImageUrl: 'https://marbullx.com/logo',
        marketCoverImageUrl: 'https://example.com/market-cover.jpg', // Added missing property
        themePrimaryColorLowest: '0xFFFFFFFFFFFF',
        themePrimaryColorLower: '0xFFFFFFFFFFFF',
        themePrimaryColorHigher: '0xFFFFFFFFFFFF',
        themePrimaryColorHighest: '0xFFFFFFFFFFFF',
        isMarketEnabled: true,
      };

      mockServiceInfoRepository.getServiceWithTranslationsById.mockResolvedValue(mockServiceWithTranslations);

      const result = await serviceInfoService.getServiceById(serviceId, languageCode.EN_US);

      expect(result).toEqual(expectedResponse);
      expect(mockServiceInfoRepository.getServiceWithTranslationsById).toHaveBeenCalledWith(
        serviceId,
        languageCode.EN_US,
      );
    });

    test('should throw NotFoundError if service does not exist', async () => {
      const serviceId = 'nonExistentServiceId';

      mockServiceInfoRepository.getServiceWithTranslationsById.mockResolvedValue(undefined);

      await expect(serviceInfoService.getServiceById(serviceId, languageCode.EN_US)).rejects.toThrow(NotFoundError);
      expect(mockServiceInfoRepository.getServiceWithTranslationsById).toHaveBeenCalledWith(
        serviceId,
        languageCode.EN_US,
      );
    });
  });

  describe('registerService', () => {
    test('should successfully register a new service', async () => {
      const tenantId = 'validTenantId';
      const serviceId = 'bbc1499b-c47c-4c9b-befc-5717dff1c5ac';
      const registryId = 'eb24c15e-e9e8-4c59-81bf-8ac4e488970c';
      const implementationId = '8e585558-082e-4254-b140-cb1db5dca105';

      const registerRequest: RegisterServiceRequest = {
        serviceTranslations: [
          {
            language: languageCode.EN_US,
            serviceName: 'My New Service',
            servicePolicy: 'RXhhbXBsZSBzZXJ2aWNlIHBvbGljeSBjb250ZW50',
            servicePane: 'eyJrZXkiOiAiVmFsdWUifQ==',
          },
          {
            language: languageCode.JA,
            serviceName: '新しいサービス',
            servicePolicy: 'RXhhbXBsZSBzZXJ2aWNlIHBvbGljeSBjb250ZW50',
            servicePane: 'eyJrZXkiOiAiVmFsdWUifQ==',
          },
        ],
        serviceUrl: 'https://www.newservice.com',
        serviceLogoImageUrl: 'https://www.newservice.com/logo.png',
        themePrimaryColorLowest: '#FFFFFFF0',
        themePrimaryColorLower: '#FFFFFFF1',
        themePrimaryColorHigher: '#FFFFFFF2',
        themePrimaryColorHighest: '#FFFFFFF3',
        isMarketEnabled: true,
        stripeAccountId: 'acct_123ABC',
        lineChannelId: 'acct_123ABC',
        marketCoverImageUrl: undefined,
        modularContractId: 'acct_123ABC',
      };

      mockTenantRepository.selectTenantById.mockResolvedValue({
        tenant_id: tenantId,
        tenant_name: 'Tenant Name',
        plan_id: 'plan_id',
      });
      const mockService: ServiceEntity = {
        service_id: serviceId,
        tenant_id: tenantId,
        service_url: registerRequest.serviceUrl,
        service_logo_image_url: registerRequest.serviceLogoImageUrl,
        theme_primary_color_lowest: registerRequest.themePrimaryColorLowest || undefined,
        theme_primary_color_lower: registerRequest.themePrimaryColorLower || undefined,
        theme_primary_color_higher: registerRequest.themePrimaryColorHigher || undefined,
        theme_primary_color_highest: registerRequest.themePrimaryColorHighest || undefined,
        membership_nft_contract_id: undefined,
        is_market_enabled: registerRequest.isMarketEnabled,
        stripe_account_id: registerRequest.stripeAccountId || undefined,
        market_cover_image_url: undefined,
        line_channel_id: 'acct_123ABC',
        commission_rate: 0.064,
        modular_contract_id: 'acct_123ABC',
      };

      const mockServiceTranslations: ServiceTranslationEntity[] = [
        {
          service_id: serviceId,
          language: languageCode.EN_US,
          service_name: registerRequest.serviceTranslations[0].serviceName,
          service_policy: registerRequest.serviceTranslations[0].servicePolicy,
          service_pane: registerRequest.serviceTranslations[0].servicePane,
        },
        {
          service_id: serviceId,
          language: languageCode.JA,
          service_name: registerRequest.serviceTranslations[1].serviceName,
          service_policy: registerRequest.serviceTranslations[1].servicePolicy,
          service_pane: registerRequest.serviceTranslations[1].servicePane,
        },
      ];

      const mockResponse: RegisterServiceResponse = {
        serviceId: serviceId,
        serviceTranslations: [
          {
            language: languageCode.EN_US,
            serviceName: 'My New Service',
            servicePolicy: 'RXhhbXBsZSBzZXJ2aWNlIHBvbGljeSBjb250ZW50',
            servicePane: 'eyJrZXkiOiAiVmFsdWUifQ==',
          },
          {
            language: languageCode.JA,
            serviceName: '新しいサービス',
            servicePolicy: 'RXhhbXBsZSBzZXJ2aWNlIHBvbGljeSBjb250ZW50',
            servicePane: 'eyJrZXkiOiAiVmFsdWUifQ==',
          },
        ],
        logoImageUrl: 'https://www.newservice.com/logo.png',
        marketCoverImageUrl: undefined, // Added missing property
        themePrimaryColorLowest: '#FFFFFFF0',
        themePrimaryColorLower: '#FFFFFFF1',
        themePrimaryColorHigher: '#FFFFFFF2',
        themePrimaryColorHighest: '#FFFFFFF3',
        isMarketEnabled: true,
      };
      mockServiceInfoRepository.insertService.mockResolvedValue(mockService);
      mockServiceInfoRepository.insertServiceTranslations.mockResolvedValue(mockServiceTranslations);
      const mockRegistry: TokenBoundAccountRegistryEntity = {
        service_id: serviceId,
        token_bound_account_registry_id: registryId,
        token_bound_account_registry_address: '0x000',
        salt: '0x000',
        chain_id: '0',
        abi: {},
      };
      mockTokenBoundAccountRegistryAddressRepository.insertAbi.mockResolvedValue(mockRegistry);

      const mockImplementation: TokenBoundAccountImplementationEntity = {
        service_id: serviceId,
        token_bound_account_registry_id: registryId,
        token_bound_account_implementation_id: implementationId,
        token_bound_account_implementation_address: '0x000',
        token_bound_account_implementation_abi: {},
      };
      mockTokenBoundAccountImplementationRepository.insertAbi.mockResolvedValue(mockImplementation);
      mockServiceInfoRepository.getServiceById.mockResolvedValue(mockService);

      const result = await serviceInfoService.registerService(tenantId, registerRequest);

      expect(result).toEqual(mockResponse);
    });

    test('should throw NotFoundError when tenant does not exist', async () => {
      const tenantId = 'invalidTenantId';
      const registerRequest: RegisterServiceRequest = {
        serviceTranslations: [
          {
            language: languageCode.EN_US,
            serviceName: 'My New Service',
            servicePolicy: 'RXhhbXBsZSBzZXJ2aWNlIHBvbGljeSBjb250ZW50',
            servicePane: 'eyJrZXkiOiAiVmFsdWUifQ==',
          },
        ],
        serviceUrl: 'https://www.newservice.com',
        serviceLogoImageUrl: 'https://www.newservice.com/logo.png',
        themePrimaryColorLowest: '#FFFFFFF0',
        themePrimaryColorLower: '#FFFFFFF1',
        themePrimaryColorHigher: '#FFFFFFF2',
        themePrimaryColorHighest: '#FFFFFFF3',
        isMarketEnabled: true,
        stripeAccountId: 'acct_123ABC',
        lineChannelId: 'acct_123ABC',
        marketCoverImageUrl: undefined,
        modularContractId: 'acct_123ABC',
      };

      mockTenantRepository.selectTenantById.mockResolvedValue(undefined);

      await expect(serviceInfoService.registerService(tenantId, registerRequest)).rejects.toThrow(NotFoundError);
    });
  });

  describe('createOrUpdateServiceCustomFields', () => {
    test('should successfully create custom fields', async () => {
      const serviceId = 'service-123';
      const definition = {
        fields: [
          {
            fieldKey: 'email',
            type: CustomFieldType.EMAIL,
            optional: false,
            unique: true,
            verify: false,
            sortOrder: 1,
            translations: [
              {
                locale: languageCode.EN_US,
                label: 'Email Address'
              }
            ]
          }
        ]
      };

      const mockResult = {
        id: 'definition-123',
        version: 2
      };

      mockServicesCustomFieldsRepository.getCurrentVersion.mockResolvedValue(1);
      mockServicesCustomFieldsRepository.createOrUpdate.mockResolvedValue(mockResult);

      const result = await serviceInfoService.createOrUpdateServiceCustomFields(serviceId, definition);

      expect(mockServicesCustomFieldsRepository.getCurrentVersion).toHaveBeenCalledWith(serviceId);
      expect(mockServicesCustomFieldsRepository.createOrUpdate).toHaveBeenCalledWith(
        serviceId,
        definition,
        2
      );
      expect(result).toEqual({
        customFieldsDefinitionId: 'definition-123',
        version: 2
      });
    });

    test('should throw ValidationError for empty fields array', async () => {
      const serviceId = 'service-123';
      const definition = {
        fields: []
      };

      await expect(
        serviceInfoService.createOrUpdateServiceCustomFields(serviceId, definition)
      ).rejects.toThrow('Fields must be a non-empty array');
    });

    test('should throw ValidationError for missing fieldKey', async () => {
      const serviceId = 'service-123';
      const definition = {
        fields: [
          {
            type: CustomFieldType.EMAIL,
            optional: false,
            unique: true,
            verify: false,
            sortOrder: 1,
            translations: [
              {
                locale: languageCode.EN_US,
                label: 'Email Address'
              }
            ]
          }
        ]
      } as any;

      await expect(
        serviceInfoService.createOrUpdateServiceCustomFields(serviceId, definition)
      ).rejects.toThrow('Each field must have a fieldKey');
    });

    test('should throw ValidationError for duplicate fieldKey', async () => {
      const serviceId = 'service-123';
      const definition = {
        fields: [
          {
            fieldKey: 'email',
            type: CustomFieldType.EMAIL,
            optional: false,
            unique: true,
            verify: false,
            sortOrder: 1,
            translations: [
              {
                locale: languageCode.EN_US,
                label: 'Email Address'
              }
            ]
          },
          {
            fieldKey: 'email',
            type: CustomFieldType.TEXT,
            optional: false,
            unique: false,
            verify: false,
            sortOrder: 2,
            translations: [
              {
                locale: languageCode.EN_US,
                label: 'Email Address 2'
              }
            ]
          }
        ]
      };

      await expect(
        serviceInfoService.createOrUpdateServiceCustomFields(serviceId, definition)
      ).rejects.toThrow('Duplicate field key: email');
    });

    test('should throw ValidationError for missing type', async () => {
      const serviceId = 'service-123';
      const definition = {
        fields: [
          {
            fieldKey: 'email',
            optional: false,
            unique: true,
            verify: false,
            sortOrder: 1,
            translations: [
              {
                locale: languageCode.EN_US,
                label: 'Email Address'
              }
            ]
          }
        ]
      } as any;

      await expect(
        serviceInfoService.createOrUpdateServiceCustomFields(serviceId, definition)
      ).rejects.toThrow('Field email must have a type');
    });

    test('should throw ValidationError for missing translations', async () => {
      const serviceId = 'service-123';
      const definition = {
        fields: [
          {
            fieldKey: 'email',
            type: CustomFieldType.EMAIL,
            optional: false,
            unique: true,
            verify: false,
            sortOrder: 1,
            translations: []
          }
        ]
      };

      await expect(
        serviceInfoService.createOrUpdateServiceCustomFields(serviceId, definition)
      ).rejects.toThrow('Field email must have at least one translation');
    });

    test('should throw ValidationError for SELECTION field without options', async () => {
      const serviceId = 'service-123';
      const definition = {
        fields: [
          {
            fieldKey: 'gender',
            type: CustomFieldType.SELECTION,
            optional: false,
            unique: false,
            verify: false,
            sortOrder: 1,
            translations: [
              {
                locale: languageCode.EN_US,
                label: 'Gender'
              }
            ]
          }
        ]
      };

      await expect(
        serviceInfoService.createOrUpdateServiceCustomFields(serviceId, definition)
      ).rejects.toThrow('SELECTION field gender must have at least one option');
    });

    test('should throw ValidationError for duplicate option values', async () => {
      const serviceId = 'service-123';
      const definition = {
        fields: [
          {
            fieldKey: 'gender',
            type: CustomFieldType.SELECTION,
            optional: false,
            unique: false,
            verify: false,
            sortOrder: 1,
            translations: [
              {
                locale: languageCode.EN_US,
                label: 'Gender'
              }
            ],
            options: [
              {
                value: 'male',
                translations: [
                  {
                    locale: languageCode.EN_US,
                    label: 'Male'
                  }
                ]
              },
              {
                value: 'male', // Duplicate value
                translations: [
                  {
                    locale: languageCode.EN_US,
                    label: 'Male 2'
                  }
                ]
              }
            ]
          }
        ]
      };

      await expect(
        serviceInfoService.createOrUpdateServiceCustomFields(serviceId, definition)
      ).rejects.toThrow('Duplicate option value male for field gender');
    });

    test('should throw ValidationError for validator without pattern', async () => {
      const serviceId = 'service-123';
      const definition = {
        fields: [
          {
            fieldKey: 'email',
            type: CustomFieldType.EMAIL,
            optional: false,
            unique: true,
            verify: false,
            sortOrder: 1,
            translations: [
              {
                locale: languageCode.EN_US,
                label: 'Email'
              }
            ],
            validator: {
              translations: [
                {
                  locale: languageCode.EN_US,
                  errorMessage: 'Invalid email'
                }
              ]
            }
          }
        ]
      };

      await expect(
        serviceInfoService.createOrUpdateServiceCustomFields(serviceId, definition)
      ).rejects.toThrow('Validator for field email must have a pattern');
    });
  });

  describe('getServiceCustomFields', () => {
    test('should return formatted custom fields', async () => {
      const serviceId = 'service-123';
      const lang = languageCode.EN_US

      const mockDefinition = {
        fields: [
          {
            custom_field_id: 'field-123',
            service_id: serviceId,
            field_key: 'email',
            version: 1,
            type: CustomFieldType.EMAIL,
            default_value: undefined,
            max_length: 255,
            min_length: 5,
            unique: true,
            verify: false,
            optional: false,
            sort_order: 1,
            created_at: new Date('2023-01-01T00:00:00Z'),
            label: 'Email Address',
            locale: languageCode.EN_US,
            validator: null,
            options: null
          }
        ]
      };

      mockServicesCustomFieldsRepository.getLatestCustomFieldsVersion.mockResolvedValue(mockDefinition);

      const result = await serviceInfoService.getServiceCustomFields(serviceId, lang);

      expect(mockServicesCustomFieldsRepository.getLatestCustomFieldsVersion).toHaveBeenCalledWith(serviceId, lang);
      expect(result.fields).toHaveLength(1);
      expect(result.fields[0].created_at).toBe('2023-01-01T00:00:00.000Z');
      expect(result.fields[0].field_key).toBe('email');
    });
  });
});
