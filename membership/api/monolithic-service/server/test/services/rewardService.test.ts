import { RewardService } from '../../src/services/rewardService';
import { ExtendedReward, RewardRepository } from '../../src/repositories/rewardRepository';
import { NotFoundError } from '../../src/errors/notFoundError';
import { AccountRepository } from '../../src/repositories/accountRepository';
import { AccountEntity } from '../../src/tables/accountTable';

import { CertificateRewardRepository } from '../../src/repositories/certificateRewardRepository';
import { CouponRewardRepository } from '../../src/repositories/couponRewardRepository';
import { DigitalContentRewardRepository } from '../../src/repositories/digitalContentRewardRepository';
import { ClaimedRewardRepository } from '../../src/repositories/claimedRewardRepository';
import { ConflictError } from '../../src/errors/conflictError';
import { RewardUsageStatus } from '../../src/enum/rewardUsageStatus';
import { NftsService } from '../../src/services/nftsService';
import { NftMintService } from '../../src/services/nftMintService';
import { AchievementActionRepository } from '../../src/repositories/achievementActionRepository';
import { QuestActivityRepository } from '../../src/repositories/questActivityRepository';
import { NftContractsRepository } from '../../src/repositories/nftContractsRepository';
import { NftBaseMetadatasRepository } from '../../src/repositories/nftBaseMetadatasRepository';
import { NftMetadatasRepository } from '../../src/repositories/nftMetadatasRepository';
import { VaultKeyRepository } from '../../src/repositories/vaultKeyRepository';
import { NftType } from '../../src/enum/nftType';
import { MetadataService } from '../../src/services/metadataService';
import { NftTag } from '../../src/enum/nftTag';
import { RewardType } from '../../src/enum/rewardType';
import { ServiceInfoRepository } from '../../src/repositories/serviceInfoRepository';
import { NftContractTypesRepository } from '../../src/repositories/nftContractTypesRepository';
import { CouponRewardEntity } from '../../src/tables/couponRewardTable';
import { RewardAcquirementType } from '../../src/enum/rewardAcquirementType';
import { InternalServerError } from '../../src/errors/internalServerError';
import { QuestionnaireResultAnswerRepository } from '../../src/repositories/questionnaireResultAnswerRepository';
import { QuestRewardPriorityType } from '../../src/enum/questRewardPriorityType';
import { RewardEntity } from '../../src/tables/rewardTable';
import { TransactionQueuesRepository } from '../../src/repositories/transactionQueuesRepository';
import { ClaimedReward } from '../../src/dtos/accounts/schemas';
import { TxType } from '../../src/enum/txType';
import { DeliveryNftsFirestoreRepository } from '../../src/repositories/deliveryNftsFirestoreRepository';
import { FirebaseComponent } from '../../src/components/firebaseComponent';

jest.mock('../../src/repositories/rewardRepository');
jest.mock('../../src/repositories/accountRepository');
jest.mock('../../src/repositories/certificateRewardRepository');
jest.mock('../../src/repositories/couponRewardRepository');
jest.mock('../../src/repositories/digitalContentRewardRepository');
jest.mock('../../src/repositories/claimedRewardRepository');
jest.mock('../../src/repositories/achievementActionRepository');
jest.mock('../../src/repositories/questActivityRepository');
jest.mock('../../src/repositories/questionnaireResultAnswerRepository');

jest.mock('../../src/services/nftMintService');
jest.mock('../../src/services/nftsService');
jest.mock('../../src/services/metadataService');
jest.mock('../../src/repositories/nftContractsRepository');
jest.mock('../../src/repositories/nftBaseMetadatasRepository');
jest.mock('../../src/repositories/nftMetadatasRepository');
jest.mock('../../src/repositories/vaultKeyRepository');
jest.mock('../../src/repositories/vaultTransactionQueuesRepository');
jest.mock('../../src/services/transactionService');
jest.mock('../../src/services/webhookService');

jest.mock('axios');

describe('RewardService', () => {
  let rewardService: RewardService;
  let mockRewardRepository: jest.Mocked<RewardRepository>;
  let mockAccountRepository: jest.Mocked<AccountRepository>;
  let mockCertificateRewardRepository: jest.Mocked<CertificateRewardRepository>;
  let mockCouponRewardRepository: jest.Mocked<CouponRewardRepository>;
  let mockDigitalContentRewardRepository: jest.Mocked<DigitalContentRewardRepository>;
  let mockClaimedRewardRepository: jest.Mocked<ClaimedRewardRepository>;
  let mockAchievementActionRepository: jest.Mocked<AchievementActionRepository>;
  let mockQuestActivityRepository: jest.Mocked<QuestActivityRepository>;
  let mockNftMintService: jest.Mocked<NftMintService>;
  let mockNftsService: jest.Mocked<NftsService>;
  let mockMetadataService: jest.Mocked<MetadataService>;

  let mockNftContractsRepository: jest.Mocked<NftContractsRepository>;
  let mockNftBaseMetadatasRepository: jest.Mocked<NftBaseMetadatasRepository>;
  let mockNftMetadatasRepository: jest.Mocked<NftMetadatasRepository>;
  let mockVaultKeyRepository: jest.Mocked<VaultKeyRepository>;
  let mockServiceInfoRepository: jest.Mocked<ServiceInfoRepository>;
  let mockNftContractTypesRepository: jest.Mocked<NftContractTypesRepository>;
  let mockQuestionnaireResultAnswerRepository: jest.Mocked<QuestionnaireResultAnswerRepository>;
  let mockTransactionQueuesRepository: jest.Mocked<TransactionQueuesRepository>;
  let mockDeliveryNftsFirestoreRepository: jest.Mocked<DeliveryNftsFirestoreRepository>;

  beforeEach(() => {
    process.env.CONTRACT_MS_BASE_URL = 'http://*********:8080';
    process.env.JSON_RPC_URL = 'http://127.0.0.1:8545';
    process.env.ALCHEMY_API_KEY = 'xxxxxsss';
    process.env.ALCHEMY_CHAIN_NAME = 'polygon-amoy';
    process.env.GAS_LIMIT_MULTIPLIER = '150';
    process.env.BASE_MAX_FEE_PER_GAS = '150';
    process.env.MAX_FEE_PER_GAS_MULTIPLIER = '150';
    process.env.MAX_PRIORITY_FEE_PER_GAS_MULTIPLIER = '150';

    mockRewardRepository = new RewardRepository() as jest.Mocked<RewardRepository>;
    mockAccountRepository = new AccountRepository() as jest.Mocked<AccountRepository>;
    mockCertificateRewardRepository = new CertificateRewardRepository() as jest.Mocked<CertificateRewardRepository>;
    mockCouponRewardRepository = new CouponRewardRepository() as jest.Mocked<CouponRewardRepository>;
    mockDigitalContentRewardRepository =
      new DigitalContentRewardRepository() as jest.Mocked<DigitalContentRewardRepository>;
    mockClaimedRewardRepository = new ClaimedRewardRepository() as jest.Mocked<ClaimedRewardRepository>;
    mockAchievementActionRepository = new AchievementActionRepository() as jest.Mocked<AchievementActionRepository>;
    mockQuestActivityRepository = new QuestActivityRepository() as jest.Mocked<QuestActivityRepository>;
    mockDeliveryNftsFirestoreRepository = new DeliveryNftsFirestoreRepository(
      {} as FirebaseComponent,
    ) as jest.Mocked<DeliveryNftsFirestoreRepository>;

    mockVaultKeyRepository = new VaultKeyRepository() as jest.Mocked<VaultKeyRepository>;
    mockServiceInfoRepository = new ServiceInfoRepository() as jest.Mocked<ServiceInfoRepository>;
    mockNftContractTypesRepository = new NftContractTypesRepository() as jest.Mocked<NftContractTypesRepository>;
    mockNftMetadatasRepository = new NftMetadatasRepository() as jest.Mocked<NftMetadatasRepository>;
    mockNftBaseMetadatasRepository = new NftBaseMetadatasRepository() as jest.Mocked<NftBaseMetadatasRepository>;
    mockTransactionQueuesRepository = new TransactionQueuesRepository() as jest.Mocked<TransactionQueuesRepository>;
    mockNftContractsRepository = new NftContractsRepository() as jest.Mocked<NftContractsRepository>;
    mockMetadataService = new MetadataService(
      mockNftMetadatasRepository,
      mockNftBaseMetadatasRepository,
      mockNftContractTypesRepository,
    ) as jest.Mocked<MetadataService>;
    mockNftMintService = new NftMintService(
      mockNftContractsRepository,
      mockTransactionQueuesRepository,
      mockVaultKeyRepository,
      mockServiceInfoRepository,
      mockDeliveryNftsFirestoreRepository,
    ) as jest.Mocked<NftMintService>;

    mockNftsService = new NftsService(
      mockNftContractsRepository,
      mockAccountRepository,
      mockRewardRepository,
    ) as jest.Mocked<NftsService>;

    mockQuestionnaireResultAnswerRepository =
      new QuestionnaireResultAnswerRepository() as jest.Mocked<QuestionnaireResultAnswerRepository>;

    rewardService = new RewardService(
      mockRewardRepository,
      mockAccountRepository,
      mockCertificateRewardRepository,
      mockCouponRewardRepository,
      mockDigitalContentRewardRepository,
      mockClaimedRewardRepository,
      mockAchievementActionRepository,
      mockQuestActivityRepository,
      mockNftMintService,
      mockNftsService,
      mockMetadataService,
      mockQuestionnaireResultAnswerRepository,
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getReward() method', () => {
    const rewardId = 'reward1';
    const serviceId = 'service1';
    const mockReward: ExtendedReward = {
      reward_id: rewardId,
      reward_title: 'Reward One',
      reward_cover_image_url: 'http://example.com/reward1_cover.jpg',
      reward_thumbnail_image_url: 'http://example.com/reward1_thumbnail.jpg',
      reward_description: 'Description of Reward One',
      reward_acquirement_type: RewardAcquirementType.DISTRIBUTION,
      reward_type: RewardType.CERTIFICATE,
      quest_reward_priority_type: QuestRewardPriorityType.MAIN,
    };

    test('should return a RewardResponseItem when reward is found', async () => {
      mockRewardRepository.selectRewardQuestById.mockResolvedValue(mockReward);

      const result = await rewardService.getReward(rewardId, serviceId, 'ja');

      expect(result).toEqual({
        rewardId: mockReward.reward_id,
        title: mockReward.reward_title,
        coverImageUrl: mockReward.reward_cover_image_url,
        description: mockReward.reward_description,
        acquirementType: mockReward.reward_acquirement_type,
        rewardType: mockReward.reward_type,
        rewardPriorityType: QuestRewardPriorityType.MAIN,
      });
      expect(mockRewardRepository.selectRewardQuestById).toHaveBeenCalledWith(rewardId, serviceId, 'ja');
    });

    test('should throw NotFoundError when reward is not found', async () => {
      mockRewardRepository.selectRewardQuestById.mockRejectedValue(new NotFoundError());

      await expect(rewardService.getReward(rewardId, serviceId, 'ja')).rejects.toThrow(NotFoundError);
      expect(mockRewardRepository.selectRewardQuestById).toHaveBeenCalledWith(rewardId, serviceId, 'ja');
    });
  });

  describe('claimReward() method', () => {
    const accountId = 'account1';
    const rewardId = 'reward1';
    const serviceId = 'service1';
    const tokenBoundAccountAddress = '0x1234567890abcdef';
    const nftContractId = 'test_contract_id';
    const tokenId = 123;

    const accountMock: AccountEntity = {
      account_id: accountId,
      service_id: serviceId,
      user_id: 'user1',
      membership_id: 1,
      display_name: 'John Doe',
      profile_image_url: 'http://example.com/profile.jpg',
      token_bound_account_address: tokenBoundAccountAddress,
      transaction_id: 'transaction-id',
      queue_id: 'queue-id',
      status: 'ACTIVE',
      created_at: new Date('2020/01/01 00:00:00'),
      updated_at: new Date('2020/01/01 00:00:00'),
      last_login_at: new Date('2020/01/01 00:00:00'),
      membership_metadata_url: 'http://example.com/membership_metadata.json',
    };

    const rewardMock: RewardEntity = {
      reward_id: rewardId,
      service_id: serviceId,
      reward_cover_image_url: 'http://example.com/reward.jpg',
      reward_thumbnail_image_url: 'http://example.com/reward_thumb.jpg',
      reward_type: RewardType.COUPON,
      order_index: 1,
    };

    const mintResponseMock = {
      queueId: 'tx123',
      contractAddress: '0xabc0123456',
      tokenBoundAccountAddress: tokenBoundAccountAddress,
    };

    const metadataMock = {
      name: 'NFT Name',
      description: 'this is test description',
      image: 'https://example.com/nft-image.png',
      external_url: null,
      animation_url: null,
      background_color: null,
      youtube_url: null,
      attributes: [
        { trait_type: 'to_date', value: '2024-12-31T00:00:00Z' },
        { trait_type: 'from_date', value: '2025-12-31T00:00:00Z' },
        { trait_type: 'tags', value: 'EXCHANGE-COUPON' },
        { trait_type: 'jan_code', value: '**********' },
        { trait_type: 'discount_amount', value: 10000 },
        { trait_type: 'discount_unit', value: '円' },
      ],
    };

    const mockCouponReward: CouponRewardEntity = {
      reward_id: rewardId,
      service_id: serviceId,
      nft_contract_id: nftContractId,
      token_id: tokenId,
    };

    beforeEach(() => {
      jest.clearAllMocks();
      mockAccountRepository.selectAccountById.mockResolvedValue(accountMock);
      mockRewardRepository.selectRewardById.mockResolvedValue(rewardMock);
      mockCertificateRewardRepository.checkStatusCertificateRewardById.mockResolvedValue(undefined);
      mockQuestActivityRepository.checkQuestCompletedWithRewardId.mockResolvedValue(true);
      mockClaimedRewardRepository.selectClaimedRewardById.mockResolvedValue(undefined);
      mockCouponRewardRepository.selectCouponRewardById.mockResolvedValue(mockCouponReward);
      mockNftMintService.mint.mockResolvedValue(mintResponseMock);
      mockMetadataService.getParsedMetadataFromBaseMetadata.mockResolvedValue(metadataMock);
    });

    test('should successfully claim a reward with QUIZ', async () => {
      const mockQuestId = 'mockQuestId';
      const mockRankId = 'mockRankId';
      const mockReward: RewardEntity = {
        reward_id: '',
        service_id: serviceId,
        reward_cover_image_url: '',
        reward_thumbnail_image_url: '',
        reward_type: RewardType.COUPON,
        order_index: 1,
      };

      mockRewardRepository.slectRewardAcquirementType.mockResolvedValue(RewardAcquirementType.QUIZ);
      mockRewardRepository.selectQuestIdByRewardId.mockResolvedValue(mockQuestId);
      mockQuestionnaireResultAnswerRepository.selectQuestionnaireResultRank.mockResolvedValue(mockRankId);
      mockRewardRepository.selectRewardsByRankId.mockResolvedValue(mockReward);
      const result = await rewardService.claimReward(accountId, rewardId, serviceId);

      const expectedResult: ClaimedReward = {
        rewardId,
        title: metadataMock.name,
        coverImageUrl: metadataMock.image,
        rewardType: NftTag.EXCHANGE_COUPON,
      };
      expect(result).toEqual(expectedResult);
      expect(mockAccountRepository.selectAccountById).toHaveBeenCalledWith(accountId, serviceId);
      expect(mockRewardRepository.slectRewardAcquirementType).toHaveBeenCalledWith(rewardId, serviceId);
      expect(mockRewardRepository.selectQuestIdByRewardId).toHaveBeenCalledWith(rewardId, serviceId);
      expect(mockQuestionnaireResultAnswerRepository.selectQuestionnaireResultRank).toHaveBeenCalledWith(
        serviceId,
        accountId,
        mockQuestId,
      );
      expect(mockRewardRepository.selectRewardsByRankId).toHaveBeenCalledWith(mockQuestId, mockRankId, serviceId);
      expect(mockQuestActivityRepository.checkQuestCompletedWithRewardId).not.toHaveBeenCalled();
      expect(mockNftMintService.mint).toHaveBeenCalledWith(
        serviceId,
        accountId,
        TxType.MINT_REWARD,
        tokenBoundAccountAddress,
        nftContractId,
        NftType.COUPON,
        tokenId,
      );
      expect(mockClaimedRewardRepository.insertClaimedReward).toHaveBeenCalledWith({
        account_id: accountId,
        reward_id: rewardId,
        service_id: serviceId,
        reward_usage_status: RewardUsageStatus.MINTING,
        queue_id: mintResponseMock.queueId,
        claim_date: expect.any(Date),
      });
    });

    test('should successfully claim a reward with DISTRIBUTION', async () => {
      mockRewardRepository.slectRewardAcquirementType.mockResolvedValue(RewardAcquirementType.DISTRIBUTION);
      const result = await rewardService.claimReward(accountId, rewardId, serviceId);

      const expectedResult: ClaimedReward = {
        rewardId,
        title: metadataMock.name,
        coverImageUrl: metadataMock.image,
        rewardType: NftTag.EXCHANGE_COUPON,
      };

      expect(result).toEqual(expectedResult);
      expect(mockAccountRepository.selectAccountById).toHaveBeenCalledWith(accountId, serviceId);
      expect(mockRewardRepository.slectRewardAcquirementType).toHaveBeenCalledWith(rewardId, serviceId);
      expect(mockRewardRepository.selectRewardById).toHaveBeenCalledWith(rewardId, serviceId);
      expect(mockQuestActivityRepository.checkQuestCompletedWithRewardId).not.toHaveBeenCalled();
      expect(mockNftMintService.mint).toHaveBeenCalledWith(
        serviceId,
        accountId,
        TxType.MINT_REWARD,
        tokenBoundAccountAddress,
        nftContractId,
        NftType.COUPON,
        tokenId,
      );
      expect(mockClaimedRewardRepository.insertClaimedReward).toHaveBeenCalledWith({
        account_id: accountId,
        reward_id: rewardId,
        service_id: serviceId,
        reward_usage_status: RewardUsageStatus.MINTING,
        queue_id: mintResponseMock.queueId,
        claim_date: expect.any(Date),
      });
    });

    test('should throw NotFoundError if account is not found', async () => {
      mockAccountRepository.selectAccountById.mockResolvedValue(undefined);

      await expect(rewardService.claimReward(accountId, rewardId, serviceId)).rejects.toThrow(NotFoundError);
    });

    test('should throw ValidationError if reward is not found', async () => {
      mockRewardRepository.slectRewardAcquirementType.mockResolvedValue(RewardAcquirementType.DISTRIBUTION);

      mockRewardRepository.selectRewardById.mockRejectedValue(new InternalServerError());

      await expect(rewardService.claimReward(accountId, rewardId, serviceId)).rejects.toThrow(NotFoundError);
    });

    test('should throw ConflictError if reward is already claimed', async () => {
      mockClaimedRewardRepository.selectClaimedRewardById.mockResolvedValue({
        account_id: accountId,
        reward_id: rewardId,
        service_id: serviceId,
        reward_usage_status: RewardUsageStatus.ACTIVE,
        claim_date: new Date(),
        transaction_id: 'txn123',
        queue_id: 'txn123',
        operation_id: undefined,
      });

      await expect(rewardService.claimReward(accountId, rewardId, serviceId)).rejects.toThrow(ConflictError);
    });

    test('should throw Error if NFT contract details are not found', async () => {
      mockCouponRewardRepository.selectCouponRewardById.mockResolvedValue(undefined);

      await expect(rewardService.claimReward(accountId, rewardId, serviceId)).rejects.toThrow(Error);
    });
  });
});
