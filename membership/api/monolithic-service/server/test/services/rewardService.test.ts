import { RewardService } from '../../src/services/rewardService';
import { ExtendedPointComponent, ExtendedReward, RewardRepository } from '../../src/repositories/rewardRepository';
import { NotFoundError } from '../../src/errors/notFoundError';
import { AccountRepository } from '../../src/repositories/accountRepository';
import { AccountEntity } from '../../src/tables/accountTable';
import { ClaimedRewardRepository } from '../../src/repositories/claimedRewardRepository';
import { RewardUsageStatus } from '../../src/enum/rewardUsageStatus';
import { NftMintService } from '../../src/services/nftMintService';
import { AchievementActionRepository } from '../../src/repositories/achievementActionRepository';
import { QuestActivityRepository } from '../../src/repositories/questActivityRepository';
import { NftContractsRepository } from '../../src/repositories/nftContractsRepository';
import { NftBaseMetadatasRepository } from '../../src/repositories/nftBaseMetadatasRepository';
import { NftMetadatasRepository } from '../../src/repositories/nftMetadatasRepository';
import { VaultKeyRepository } from '../../src/repositories/vaultKeyRepository';
import { NftType } from '../../src/enum/nftType';
import { MetadataService } from '../../src/services/metadataService';
import { NftTag } from '../../src/enum/nftTag';
import { ServiceInfoRepository } from '../../src/repositories/serviceInfoRepository';
import { NftContractTypesRepository } from '../../src/repositories/nftContractTypesRepository';
import { RewardAcquirementType } from '../../src/enum/rewardAcquirementType';
import { QuestionnaireResultAnswerRepository } from '../../src/repositories/questionnaireResultAnswerRepository';
import { QuestRewardPriorityType } from '../../src/enum/questRewardPriorityType';
import { RewardEntity } from '../../src/tables/rewardTable';
import { TransactionQueuesRepository } from '../../src/repositories/transactionQueuesRepository';
import { ClaimedReward } from '../../src/dtos/accounts/schemas';
import { TxType } from '../../src/enum/txType';
import { ExtendedNftComponent } from '../../src/repositories/rewardRepository';
import { RewardComponentType } from '../../src/enum/rewardComponentType';
import { CertificateType } from '../../src/enum/certificateType';
import { DeliveryNftsFirestoreRepository } from '../../src/repositories/deliveryNftsFirestoreRepository';
import { FirebaseComponent } from '../../src/components/firebaseComponent';
import { StatusPointService } from '../../src/services/statusPointService';
import { RewardPointService } from '../../src/services/rewardPointService';
import { RedisComponent } from '../../src/components/redisComponent';
import { RewardPointTxsRepository } from '../../src/repositories/rewardPointTxsRepository';
import { StatusPointTxsRepository } from '../../src/repositories/statusPointTxsRepository';
import { PointType } from '../../src/enum/pointType';
import { PointOpsActor } from '../../src/enum/pointOpsActor';
import { PointTxDetail } from '../../src/enum/pointTxDetail';
import { RewardType } from '../../src/enum/rewardType';

jest.mock('../../src/repositories/rewardRepository');
jest.mock('../../src/repositories/accountRepository');
jest.mock('../../src/repositories/claimedRewardRepository');
jest.mock('../../src/repositories/achievementActionRepository');
jest.mock('../../src/repositories/questActivityRepository');
jest.mock('../../src/repositories/questionnaireResultAnswerRepository');
jest.mock('../../src/services/nftMintService');
jest.mock('../../src/services/metadataService');
jest.mock('../../src/repositories/nftContractsRepository');
jest.mock('../../src/repositories/nftBaseMetadatasRepository');
jest.mock('../../src/repositories/nftMetadatasRepository');
jest.mock('../../src/repositories/vaultKeyRepository');
jest.mock('../../src/repositories/vaultTransactionQueuesRepository');
jest.mock('../../src/services/transactionService');
jest.mock('../../src/services/webhookService');
jest.mock('../../src/services/rewardPointService');
jest.mock('../../src/services/statusPointService');
jest.mock('../../src/components/redisComponent');

const mockTrx = {
  commit: jest.fn(),
  rollback: jest.fn(),
};
jest.mock('../../src/db/database', () => {
  return {
    db: {
      selectFrom: jest.fn(),
      transaction: jest.fn().mockReturnValue({
        execute: jest.fn().mockImplementation(async (callback) => {
          return await callback(mockTrx);
        }),
      }),
    },
  };
});

jest.mock('axios');

const MOCK_DEFAULT_DATE = new Date('2025-01-01T00:00:00Z');
jest.useFakeTimers();
jest.setSystemTime(MOCK_DEFAULT_DATE);

describe('RewardService', () => {
  let rewardService: RewardService;
  let mockRewardRepository: jest.Mocked<RewardRepository>;
  let mockAccountRepository: jest.Mocked<AccountRepository>;
  let mockClaimedRewardRepository: jest.Mocked<ClaimedRewardRepository>;
  let mockAchievementActionRepository: jest.Mocked<AchievementActionRepository>;
  let mockQuestActivityRepository: jest.Mocked<QuestActivityRepository>;
  let mockNftMintService: jest.Mocked<NftMintService>;
  let mockMetadataService: jest.Mocked<MetadataService>;
  let mockNftContractsRepository: jest.Mocked<NftContractsRepository>;
  let mockNftBaseMetadatasRepository: jest.Mocked<NftBaseMetadatasRepository>;
  let mockNftMetadatasRepository: jest.Mocked<NftMetadatasRepository>;
  let mockVaultKeyRepository: jest.Mocked<VaultKeyRepository>;
  let mockServiceInfoRepository: jest.Mocked<ServiceInfoRepository>;
  let mockNftContractTypesRepository: jest.Mocked<NftContractTypesRepository>;
  let mockQuestionnaireResultAnswerRepository: jest.Mocked<QuestionnaireResultAnswerRepository>;
  let mockTransactionQueuesRepository: jest.Mocked<TransactionQueuesRepository>;
  let mockDeliveryNftsFirestoreRepository: jest.Mocked<DeliveryNftsFirestoreRepository>;
  let mockRedisComponent: jest.Mocked<RedisComponent>;
  let mockRewardPointService: jest.Mocked<RewardPointService>;
  let mockStatusPointService: jest.Mocked<StatusPointService>;
  let mockRewardPointTxsRepository: jest.Mocked<RewardPointTxsRepository>;
  let mockStatusPointTxsRepository: jest.Mocked<StatusPointTxsRepository>;

  beforeEach(() => {
    process.env.CONTRACT_MS_BASE_URL = 'http://*********:8080';
    process.env.JSON_RPC_URL = 'http://127.0.0.1:8545';
    process.env.ALCHEMY_API_KEY = 'xxxxxsss';
    process.env.ALCHEMY_CHAIN_NAME = 'polygon-amoy';
    process.env.GAS_LIMIT_MULTIPLIER = '150';
    process.env.BASE_MAX_FEE_PER_GAS = '150';
    process.env.MAX_FEE_PER_GAS_MULTIPLIER = '150';
    process.env.MAX_PRIORITY_FEE_PER_GAS_MULTIPLIER = '150';

    mockRewardRepository = new RewardRepository() as jest.Mocked<RewardRepository>;
    mockAccountRepository = new AccountRepository() as jest.Mocked<AccountRepository>;
    mockClaimedRewardRepository = new ClaimedRewardRepository() as jest.Mocked<ClaimedRewardRepository>;
    mockAchievementActionRepository = new AchievementActionRepository() as jest.Mocked<AchievementActionRepository>;
    mockQuestActivityRepository = new QuestActivityRepository() as jest.Mocked<QuestActivityRepository>;
    mockDeliveryNftsFirestoreRepository = new DeliveryNftsFirestoreRepository(
      {} as FirebaseComponent,
    ) as jest.Mocked<DeliveryNftsFirestoreRepository>;

    mockVaultKeyRepository = new VaultKeyRepository() as jest.Mocked<VaultKeyRepository>;
    mockServiceInfoRepository = new ServiceInfoRepository() as jest.Mocked<ServiceInfoRepository>;
    mockNftContractTypesRepository = new NftContractTypesRepository() as jest.Mocked<NftContractTypesRepository>;
    mockNftMetadatasRepository = new NftMetadatasRepository() as jest.Mocked<NftMetadatasRepository>;
    mockNftBaseMetadatasRepository = new NftBaseMetadatasRepository() as jest.Mocked<NftBaseMetadatasRepository>;
    mockTransactionQueuesRepository = new TransactionQueuesRepository() as jest.Mocked<TransactionQueuesRepository>;
    mockNftContractsRepository = new NftContractsRepository() as jest.Mocked<NftContractsRepository>;
    mockMetadataService = new MetadataService(
      mockNftMetadatasRepository,
      mockNftBaseMetadatasRepository,
      mockNftContractTypesRepository,
    ) as jest.Mocked<MetadataService>;
    mockNftMintService = new NftMintService(
      mockNftContractsRepository,
      mockTransactionQueuesRepository,
      mockVaultKeyRepository,
      mockServiceInfoRepository,
      mockDeliveryNftsFirestoreRepository,
    ) as jest.Mocked<NftMintService>;
    mockQuestionnaireResultAnswerRepository =
      new QuestionnaireResultAnswerRepository() as jest.Mocked<QuestionnaireResultAnswerRepository>;
    mockRedisComponent = new RedisComponent() as jest.Mocked<RedisComponent>;
    mockRewardPointTxsRepository = new RewardPointTxsRepository() as jest.Mocked<RewardPointTxsRepository>;
    mockStatusPointTxsRepository = new StatusPointTxsRepository() as jest.Mocked<StatusPointTxsRepository>;
    mockRewardPointService = new RewardPointService(
      mockRedisComponent,
      mockServiceInfoRepository,
      mockRewardPointTxsRepository,
    ) as jest.Mocked<RewardPointService>;
    mockStatusPointService = new StatusPointService(
      mockStatusPointTxsRepository,
      mockRedisComponent,
    ) as jest.Mocked<StatusPointService>;

    rewardService = new RewardService(
      mockRewardRepository,
      mockAccountRepository,
      mockClaimedRewardRepository,
      mockAchievementActionRepository,
      mockQuestActivityRepository,
      mockNftMintService,
      mockMetadataService,
      mockQuestionnaireResultAnswerRepository,
      mockRewardPointService,
      mockStatusPointService,
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getReward() method', () => {
    const rewardId = 'reward1';
    const serviceId = 'service1';
    const mockReward: ExtendedReward = {
      reward_id: rewardId,
      reward_title: 'Reward One',
      reward_cover_image_url: 'http://example.com/reward1_cover.jpg',
      reward_thumbnail_image_url: 'http://example.com/reward1_thumbnail.jpg',
      reward_description: 'Description of Reward One',
      reward_acquirement_type: RewardAcquirementType.DISTRIBUTION,
      quest_reward_priority_type: QuestRewardPriorityType.MAIN,
    };
    const mockExtendedNftComponents: ExtendedNftComponent[] = [
      {
        reward_component_id: 'rewardComponentId',
        reward_component_type: RewardComponentType.NFT,
        nft_component_id: 'nftComponentId',
        service_id: serviceId,
        nft_contract_id: 'nftContractId',
        nft_contract_type: RewardType.CERTIFICATE,
        certificate_type: CertificateType.STANDARD,
        status_certificate_rank: null,
        token_id: null,
      },
    ];

    test('should return a RewardResponseItem when reward is found', async () => {
      mockRewardRepository.selectRewardQuestById.mockResolvedValue(mockReward);
      mockRewardRepository.findRewardComponents.mockResolvedValue({
        nftComponents: mockExtendedNftComponents,
        pointComponents: [],
      });

      const result = await rewardService.getReward(rewardId, serviceId, 'ja');

      expect(result).toEqual({
        rewardId: mockReward.reward_id,
        title: mockReward.reward_title,
        coverImageUrl: mockReward.reward_cover_image_url,
        description: mockReward.reward_description,
        acquirementType: mockReward.reward_acquirement_type,
        rewardPriorityType: QuestRewardPriorityType.MAIN,
        nfts: mockExtendedNftComponents.map((component) => ({
          nftContractId: component.nft_contract_id,
          nftContractType: component.nft_contract_type,
        })),
        points: [],
      });
      expect(mockRewardRepository.selectRewardQuestById).toHaveBeenCalledWith(rewardId, serviceId, 'ja');
    });

    test('should throw NotFoundError when reward is not found', async () => {
      mockRewardRepository.selectRewardQuestById.mockRejectedValue(new NotFoundError());

      await expect(rewardService.getReward(rewardId, serviceId, 'ja')).rejects.toThrow(NotFoundError);
      expect(mockRewardRepository.selectRewardQuestById).toHaveBeenCalledWith(rewardId, serviceId, 'ja');
    });

    test('should throw NotFoundError when reward has no components', async () => {
      mockRewardRepository.findRewardComponents.mockResolvedValue({ nftComponents: [], pointComponents: [] });
      await expect(rewardService.getReward(rewardId, serviceId, 'ja')).rejects.toThrow(NotFoundError);
      expect(mockRewardRepository.selectRewardQuestById).toHaveBeenCalledWith(rewardId, serviceId, 'ja');
    });
  });

  describe('claimReward() method', () => {
    const accountId = 'account1';
    const rewardId = 'reward1';
    const serviceId = 'service1';
    const tokenBoundAccountAddress = '0x1234567890abcdef';
    const nftContractId = 'test_contract_id';
    const tokenId = 123;

    const accountMock: AccountEntity = {
      account_id: accountId,
      service_id: serviceId,
      user_id: 'user1',
      membership_id: 1,
      display_name: 'John Doe',
      profile_image_url: 'http://example.com/profile.jpg',
      token_bound_account_address: tokenBoundAccountAddress,
      transaction_id: 'transaction-id',
      queue_id: 'queue-id',
      status: 'ACTIVE',
      created_at: new Date('2020/01/01 00:00:00'),
      updated_at: new Date('2020/01/01 00:00:00'),
      last_login_at: new Date('2020/01/01 00:00:00'),
      membership_metadata_url: 'http://example.com/membership_metadata.json',
    };

    const rewardMock: RewardEntity = {
      reward_id: rewardId,
      service_id: serviceId,
      reward_cover_image_url: 'http://example.com/reward.jpg',
      reward_thumbnail_image_url: 'http://example.com/reward_thumb.jpg',
      order_index: 1,
    };

    const mintResponseMock = {
      queueId: 'tx123',
      contractAddress: '0xabc0123456',
      tokenBoundAccountAddress: tokenBoundAccountAddress,
    };

    const metadataMock = {
      name: 'NFT Name',
      description: 'this is test description',
      image: 'https://example.com/nft-image.png',
      external_url: null,
      animation_url: null,
      background_color: null,
      youtube_url: null,
      attributes: [
        { trait_type: 'to_date', value: '2024-12-31T00:00:00Z' },
        { trait_type: 'from_date', value: '2025-12-31T00:00:00Z' },
        { trait_type: 'tags', value: 'STANDARD-CERTIFICATE' },
        { trait_type: 'jan_code', value: '**********' },
        { trait_type: 'discount_amount', value: 10000 },
        { trait_type: 'discount_unit', value: '円' },
      ],
    };

    const mockExtendedNftComponents: ExtendedNftComponent[] = [
      {
        reward_component_id: 'rewardComponentId',
        reward_component_type: RewardComponentType.NFT,
        nft_component_id: 'nftComponentId',
        service_id: serviceId,
        nft_contract_id: nftContractId,
        nft_contract_type: RewardType.CERTIFICATE,
        certificate_type: CertificateType.STANDARD,
        status_certificate_rank: null,
        token_id: null,
      },
    ];

    const mockExtendedPointComponents: ExtendedPointComponent[] = [
      {
        reward_component_id: 'rewardComponentId1',
        reward_component_type: RewardComponentType.POINT,
        service_id: serviceId,
        point_component_id: 'pointComponentId1',
        amount: 100,
        point_type: PointType.REWARD,
        expires_on: new Date('2025-01-01T00:00:00Z'),
      },
      {
        reward_component_id: 'rewardComponentId2',
        reward_component_type: RewardComponentType.POINT,
        service_id: serviceId,
        point_component_id: 'pointComponentId2',
        amount: 200,
        point_type: PointType.STATUS,
        expires_on: null,
      },
    ];

    beforeEach(() => {
      jest.clearAllMocks();
      mockAccountRepository.selectAccountById.mockResolvedValue(accountMock);
      mockRewardRepository.selectRewardById.mockResolvedValue(rewardMock);
      mockQuestActivityRepository.checkQuestCompletedWithRewardId.mockResolvedValue(true);
      mockClaimedRewardRepository.selectClaimedRewardById.mockResolvedValue(undefined);
      mockNftMintService.mint.mockResolvedValue(mintResponseMock);
      mockMetadataService.getParsedMetadataFromBaseMetadata.mockResolvedValue(metadataMock);
    });

    test('should successfully claim a reward with QUIZ', async () => {
      const mockQuestId = 'mockQuestId';
      const mockRankId = 'mockRankId';
      const mockReward: RewardEntity = {
        reward_id: '',
        service_id: serviceId,
        reward_cover_image_url: '',
        reward_thumbnail_image_url: '',
        order_index: 1,
      };

      mockRewardRepository.selectRewardAcquirementType.mockResolvedValue(RewardAcquirementType.QUIZ);
      mockRewardRepository.selectQuestIdByRewardId.mockResolvedValue(mockQuestId);
      mockQuestionnaireResultAnswerRepository.selectQuestionnaireResultRank.mockResolvedValue(mockRankId);
      mockRewardRepository.findRewardComponents.mockResolvedValue({
        nftComponents: mockExtendedNftComponents,
        pointComponents: mockExtendedPointComponents,
      });
      mockRewardRepository.selectRewardsByRankId.mockResolvedValue(mockReward);
      const result = await rewardService.claimReward(accountId, rewardId, serviceId);

      const expectedResult: ClaimedReward = {
        rewardId,
        nfts: [
          {
            title: metadataMock.name,
            coverImageUrl: metadataMock.image,
            rewardType: NftTag.STANDARD_CERTIFICATE,
          },
        ],
        points: mockExtendedPointComponents.map((component) => ({
          amount: component.amount,
          pointType: component.point_type,
        })),
      };
      expect(result).toEqual(expectedResult);
      expect(mockAccountRepository.selectAccountById).toHaveBeenCalledWith(accountId, serviceId);
      expect(mockRewardRepository.selectRewardAcquirementType).toHaveBeenCalledWith(rewardId, serviceId);
      expect(mockRewardRepository.selectQuestIdByRewardId).toHaveBeenCalledWith(rewardId, serviceId);
      expect(mockQuestionnaireResultAnswerRepository.selectQuestionnaireResultRank).toHaveBeenCalledWith(
        serviceId,
        accountId,
        mockQuestId,
      );
      expect(mockQuestActivityRepository.checkQuestCompletedWithRewardId).toHaveBeenCalledWith(
        accountId,
        rewardId,
        serviceId,
      );
      expect(mockNftMintService.mint).toHaveBeenCalledWith(
        serviceId,
        accountId,
        TxType.MINT_REWARD,
        tokenBoundAccountAddress,
        nftContractId,
        NftType.CERTIFICATE,
        undefined,
        mockTrx,
      );
      expect(mockNftMintService.mint).toHaveBeenCalledWith(
        serviceId,
        accountId,
        TxType.MINT_REWARD,
        tokenBoundAccountAddress,
        nftContractId,
        NftType.CERTIFICATE,
        undefined,
        mockTrx,
      );
      expect(mockRewardPointService.addRewardPoint).toHaveBeenCalledWith(
        serviceId,
        accountId,
        mockExtendedPointComponents[0].amount,
        PointOpsActor.USER,
        PointTxDetail.REWARD,
        '2025-01-01T00:00:00.000Z',
        mockTrx,
        rewardId,
      );
      expect(mockClaimedRewardRepository.insertClaimedReward).toHaveBeenCalledWith(
        {
          account_id: accountId,
          reward_component_id: 'rewardComponentId',
          service_id: serviceId,
          reward_usage_status: RewardUsageStatus.MINTING,
          queue_id: mintResponseMock.queueId,
          claim_date: MOCK_DEFAULT_DATE,
        },
        mockTrx,
      );
    });

    test('should successfully claim a reward with DISTRIBUTION', async () => {
      mockRewardRepository.selectRewardAcquirementType.mockResolvedValue(RewardAcquirementType.DISTRIBUTION);
      mockRewardRepository.findRewardComponents.mockResolvedValue({
        nftComponents: mockExtendedNftComponents,
        pointComponents: mockExtendedPointComponents,
      });
      const result = await rewardService.claimReward(accountId, rewardId, serviceId);

      const expectedResult: ClaimedReward = {
        rewardId,
        nfts: [
          {
            title: metadataMock.name,
            coverImageUrl: metadataMock.image,
            rewardType: NftTag.STANDARD_CERTIFICATE,
          },
        ],
        points: mockExtendedPointComponents.map((component) => ({
          amount: component.amount,
          pointType: component.point_type,
        })),
      };

      expect(result).toEqual(expectedResult);
      expect(mockAccountRepository.selectAccountById).toHaveBeenCalledWith(accountId, serviceId);
      expect(mockRewardRepository.selectRewardAcquirementType).toHaveBeenCalledWith(rewardId, serviceId);
      expect(mockQuestActivityRepository.checkQuestCompletedWithRewardId).toHaveBeenCalledWith(
        accountId,
        rewardId,
        serviceId,
      );
      expect(mockNftMintService.mint).toHaveBeenCalledWith(
        serviceId,
        accountId,
        TxType.MINT_REWARD,
        tokenBoundAccountAddress,
        nftContractId,
        NftType.CERTIFICATE,
        undefined,
        mockTrx,
      );
      expect(mockRewardPointService.addRewardPoint).toHaveBeenCalledWith(
        serviceId,
        accountId,
        mockExtendedPointComponents[0].amount,
        PointOpsActor.USER,
        PointTxDetail.REWARD,
        '2025-01-01T00:00:00.000Z',
        mockTrx,
        rewardId,
      );
      expect(mockStatusPointService.addStatusPoint).toHaveBeenCalledWith(
        serviceId,
        accountId,
        mockExtendedPointComponents[1].amount,
        PointOpsActor.USER,
        PointTxDetail.REWARD,
        mockTrx,
        rewardId,
      );
      expect(mockClaimedRewardRepository.insertClaimedReward).toHaveBeenCalledWith(
        {
          account_id: accountId,
          reward_component_id: 'rewardComponentId',
          service_id: serviceId,
          reward_usage_status: RewardUsageStatus.MINTING,
          queue_id: mintResponseMock.queueId,
          claim_date: MOCK_DEFAULT_DATE,
        },
        mockTrx,
      );
    });

    test('should throw NotFoundError if account is not found', async () => {
      mockAccountRepository.selectAccountById.mockResolvedValue(undefined);
      mockRewardRepository.findRewardComponents.mockResolvedValue({
        nftComponents: mockExtendedNftComponents,
        pointComponents: mockExtendedPointComponents,
      });

      await expect(rewardService.claimReward(accountId, rewardId, serviceId)).rejects.toThrow(NotFoundError);
    });

    test('should throw ValidationError if achievement action is not completed in case of STATUS certificate', async () => {
      mockRewardRepository.selectRewardAcquirementType.mockResolvedValue(RewardAcquirementType.DISTRIBUTION);
      mockRewardRepository.findRewardComponents.mockResolvedValue({
        nftComponents: [
          {
            reward_component_id: 'rewardComponentId',
            reward_component_type: RewardComponentType.NFT,
            nft_component_id: 'nftComponentId',
            service_id: serviceId,
            nft_contract_id: nftContractId,
            nft_contract_type: RewardType.CERTIFICATE,
            certificate_type: CertificateType.STATUS,
            status_certificate_rank: null,
            token_id: null,
          },
        ],
        pointComponents: [],
      });
      mockAchievementActionRepository.checkAchievementActionCompletedWithRewardId.mockResolvedValue(false);
      const expectedErrorResult = {
        failedIds: [{ error: 'Achievement action did not completed', rewardComponentId: 'rewardComponentId' }],
        nfts: [],
        points: [],
        rewardId,
      };

      await expect(rewardService.claimReward(accountId, rewardId, serviceId)).resolves.toEqual(expectedErrorResult);
    });

    test('should throw ValidationError if quest is not completed in case of STANDARD certificate', async () => {
      mockRewardRepository.selectRewardAcquirementType.mockResolvedValue(RewardAcquirementType.DISTRIBUTION);
      mockRewardRepository.findRewardComponents.mockResolvedValue({
        nftComponents: mockExtendedNftComponents,
        pointComponents: [],
      });
      mockQuestActivityRepository.checkQuestCompletedWithRewardId.mockResolvedValue(false);
      const expectedErrorResult = {
        failedIds: [{ error: 'Achievement action did not completed', rewardComponentId: 'rewardComponentId' }],
        nfts: [],
        points: [],
        rewardId,
      };

      await expect(rewardService.claimReward(accountId, rewardId, serviceId)).resolves.toEqual(expectedErrorResult);
    });

    test('should throw ConflictError if reward is already claimed', async () => {
      mockRewardRepository.selectRewardAcquirementType.mockResolvedValue(RewardAcquirementType.DISTRIBUTION);
      mockRewardRepository.findRewardComponents.mockResolvedValue({
        nftComponents: mockExtendedNftComponents,
        pointComponents: [],
      });
      mockClaimedRewardRepository.selectClaimedRewardById.mockResolvedValue({
        account_id: accountId,
        reward_component_id: 'rewardComponentId',
        service_id: serviceId,
        reward_usage_status: RewardUsageStatus.ACTIVE,
        claim_date: new Date(),
        transaction_id: 'txn123',
        queue_id: 'txn123',
        operation_id: undefined,
      });

      const expectedErrorResult = {
        failedIds: [{ error: 'Reward has already been claimed.', rewardComponentId: 'rewardComponentId' }],
        nfts: [],
        points: [],
        rewardId,
      };

      await expect(rewardService.claimReward(accountId, rewardId, serviceId)).resolves.toEqual(expectedErrorResult);
    });
  });

  afterAll(() => {
    jest.useRealTimers();
  });
});
