import { OtpService } from '../../src/services/otpService';
import { AccountRepository } from '../../src/repositories/accountRepository';
import { EmailDeliveryService } from '../../src/services/emailDeliveryService';
import { RedisComponent } from '../../src/components/redisComponent';
import { NotFoundError } from '../../src/errors/notFoundError';
import { ValidationError } from '../../src/errors/validationError';
import { EmailTemplateType } from '../../src/constants/emailTemplates';
import { languageCode } from '../../src/enum/languageCode';
import { authenticator } from 'otplib';

jest.mock('../../src/repositories/accountRepository');
jest.mock('../../src/services/emailDeliveryService');
jest.mock('../../src/components/redisComponent');
jest.mock('otplib', () => ({
  authenticator: {
    options: {},
    generateSecret: jest.fn().mockReturnValue('secret'),
    generate: jest.fn().mockReturnValue('123456')
  }
}));
jest.mock('crypto', () => ({
  createHash: jest.fn().mockReturnValue({
    update: jest.fn().mockReturnValue({
      digest: jest.fn().mockReturnValue('hashed-otp')
    })
  })
}));

jest.mock('resend', () => {
  return {
    Resend: class {
      sendEmail = jest.fn().mockResolvedValue({ id: 'fake-id' });
    }
  };
});

describe('OtpService', () => {
  let otpService: OtpService;
  let mockAccountRepository: jest.Mocked<AccountRepository>;
  let mockEmailDeliveryService: jest.Mocked<EmailDeliveryService>;
  let mockRedisComponent: jest.Mocked<RedisComponent>;

  beforeEach(() => {
    jest.clearAllMocks();
    mockAccountRepository = new AccountRepository() as jest.Mocked<AccountRepository>;
    mockEmailDeliveryService = new EmailDeliveryService() as jest.Mocked<EmailDeliveryService>;
    mockRedisComponent = new RedisComponent() as jest.Mocked<RedisComponent>;

    otpService = new OtpService(
      mockAccountRepository,
      mockEmailDeliveryService,
      mockRedisComponent
    );
  });

  describe('generateEmailOtp', () => {
    const accountId = 'account-123';
    const serviceId = 'service-123';
    const email = '<EMAIL>';
    const lang = languageCode.EN_US;

    test('should generate and send OTP successfully', async () => {
      mockAccountRepository.findAccountCustomFieldByEmail.mockResolvedValue({
        account_id: accountId,
        service_id: serviceId,
        created_at: new Date(),
        custom_field_id: 'field-123',
        custom_field_value_id: 'value-123',
        value: email,
        verified: false
      });
      mockRedisComponent.set.mockResolvedValue(undefined);
      mockEmailDeliveryService.sendTemplatedEmail.mockResolvedValue(true);

      const result = await otpService.generateEmailOtp(accountId, serviceId, lang, email);

      expect(result).toEqual({ email });
      expect(mockAccountRepository.findAccountCustomFieldByEmail).toHaveBeenCalledWith(accountId, serviceId, email);
      expect(authenticator.generateSecret).toHaveBeenCalled();
      expect(authenticator.generate).toHaveBeenCalledWith('secret');
      expect(mockRedisComponent.set).toHaveBeenCalledWith(
        `otp:${accountId}:${email}`,
        { otpHash: 'hashed-otp', attempts: 0 },
        300
      );
      expect(mockEmailDeliveryService.sendTemplatedEmail).toHaveBeenCalledWith(
        email,
        EmailTemplateType.OTP_VERIFICATION,
        lang,
        { otp: '123456' }
      );
    });

    test('should throw NotFoundError if email not found', async () => {
      mockAccountRepository.findAccountCustomFieldByEmail.mockResolvedValue(undefined);

      await expect(otpService.generateEmailOtp(accountId, serviceId, lang, email))
        .rejects.toThrow(NotFoundError);
      expect(mockAccountRepository.findAccountCustomFieldByEmail).toHaveBeenCalledWith(accountId, serviceId, email);
    });

    test('should throw ValidationError if email already verified', async () => {
      mockAccountRepository.findAccountCustomFieldByEmail.mockResolvedValue({
        account_id: accountId,
        service_id: serviceId,
        created_at: new Date(),
        custom_field_id: 'field-123',
        custom_field_value_id: 'value-123',
        value: email,
        verified: true
      });

      await expect(otpService.generateEmailOtp(accountId, serviceId, lang, email))
        .rejects.toThrow(ValidationError);
    });
  });

  describe('verifyOtp', () => {
    const accountId = 'account-123';
    const serviceId = 'service-123';
    const email = '<EMAIL>';
    const otp = '123456';

    test('should verify OTP successfully', async () => {
      mockAccountRepository.findAccountCustomFieldByEmail.mockResolvedValue({
        account_id: accountId,
        service_id: serviceId,
        created_at: new Date(),
        custom_field_id: 'field-123',
        custom_field_value_id: 'value-123',
        value: email,
        verified: false
      });
      mockRedisComponent.get.mockResolvedValue({
        otpHash: 'hashed-otp',
        attempts: 0
      });
      mockRedisComponent.set.mockResolvedValue(undefined);
      mockAccountRepository.updateCustomFieldVerificationStatus.mockResolvedValue(undefined);

      const result = await otpService.verifyOtp(accountId, serviceId, email, otp);

      expect(result).toEqual({ email });
      expect(mockAccountRepository.findAccountCustomFieldByEmail).toHaveBeenCalledWith(accountId, serviceId, email);
      expect(mockRedisComponent.get).toHaveBeenCalledWith(`otp:${accountId}:${email}`);
      expect(mockRedisComponent.set).toHaveBeenCalledWith(
        `otp:${accountId}:${email}`,
        { otpHash: 'hashed-otp', attempts: 1 },
        300
      );
      expect(mockAccountRepository.updateCustomFieldVerificationStatus).toHaveBeenCalledWith(
        accountId,
        serviceId,
        'field-123',
        email,
        true
      );
    });

    test('should throw NotFoundError if email not found', async () => {
      mockAccountRepository.findAccountCustomFieldByEmail.mockResolvedValue(undefined);

      await expect(otpService.verifyOtp(accountId, serviceId, email, otp))
        .rejects.toThrow(NotFoundError);
    });

    test('should throw NotFoundError if OTP data not found', async () => {
      mockAccountRepository.findAccountCustomFieldByEmail.mockResolvedValue({
        account_id: accountId,
        service_id: serviceId,
        created_at: new Date(),
        custom_field_id: 'field-123',
        custom_field_value_id: 'value-123',
        value: email,
        verified: false
      });
      mockRedisComponent.get.mockResolvedValue(null);

      await expect(otpService.verifyOtp(accountId, serviceId, email, otp))
        .rejects.toThrow(NotFoundError);
    });

    test('should throw ValidationError if max attempts reached', async () => {
      mockAccountRepository.findAccountCustomFieldByEmail.mockResolvedValue({
        account_id: accountId,
        service_id: serviceId,
        created_at: new Date(),
        custom_field_id: 'field-123',
        custom_field_value_id: 'value-123',
        value: email,
        verified: false
      });
      mockRedisComponent.get.mockResolvedValue({
        otpHash: 'hashed-otp',
        attempts: 3
      });

      await expect(otpService.verifyOtp(accountId, serviceId, email, otp))
        .rejects.toThrow(ValidationError);
    });

    test('should throw ValidationError if OTP is invalid', async () => {
      mockAccountRepository.findAccountCustomFieldByEmail.mockResolvedValue({
        account_id: accountId,
        service_id: serviceId,
        created_at: new Date(),
        custom_field_id: 'field-123',
        custom_field_value_id: 'value-123',
        value: email,
        verified: false
      });
      mockRedisComponent.get.mockResolvedValue({
        otpHash: 'different-hash',
        attempts: 0
      });
      mockRedisComponent.set.mockResolvedValue(undefined);

      await expect(otpService.verifyOtp(accountId, serviceId, email, otp))
        .rejects.toThrow(ValidationError);
      expect(mockRedisComponent.set).toHaveBeenCalledWith(
        `otp:${accountId}:${email}`,
        { otpHash: 'different-hash', attempts: 1 },
        300
      );
    });
  });
});
