// import { AccountCustomFieldService } from '../../src/services/accountCustomFieldService';
// import { AccountRepository } from '../../src/repositories/accountRepository';
// import { ServicesCustomFieldsRepository } from '../../src/repositories/servicesCustomFieldsRepository';
// import { NotFoundError, ValidationError } from '../../src/errors';
// import { LanguageCode } from '../../src/enum/languageCode';

// describe('AccountCustomFieldService', () => {
//   let service: AccountCustomFieldService;
//   let mockAccountRepository: jest.Mocked<AccountRepository>;
//   let mockServicesCustomFieldsRepository: jest.Mocked<ServicesCustomFieldsRepository>;

//   const mockAccount = {
//     id: 'account-123',
//     service_id: 'service-456',
//     // Add other required account fields
//   };

//   const mockCustomFieldDefinition = {
//     id: 'def-123',
//     service_id: 'service-456',
//     version: 1,
//     fields: [
//       {
//         custom_field_id: 'field-1',
//         field_key: 'username',
//         type: 'TEXT',
//         label: 'Username',
//         description: 'User username',
//         optional: false,
//         unique: true,
//         min_length: 3,
//         max_length: 50,
//         is_multi_select: false,
//         options: [],
//         created_at: new Date(),
//         updated_at: new Date(),
//       },
//       {
//         custom_field_id: 'field-2',
//         field_key: 'age',
//         type: 'NUMERIC',
//         label: 'Age',
//         description: 'User age',
//         optional: true,
//         created_at: new Date(),
//         updated_at: new Date(),
//       },
//     ],
//   };

//   beforeEach(() => {
//     // Create mock implementations
//     mockAccountRepository = {
//       selectAccountById: jest.fn(),
//       getAccountCustomFieldValues: jest.fn(),
//       createOrUpdateAccountCustomField: jest.fn(),
//       isCustomFieldValueUnique: jest.fn(),
//     } as any;

//     mockServicesCustomFieldsRepository = {
//       getLatestCustomFieldsVersion: jest.fn(),
//     } as any;

//     // Initialize service with mocked dependencies
//     service = new AccountCustomFieldService(
//       mockAccountRepository,
//       mockServicesCustomFieldsRepository
//     );
//   });

//   afterEach(() => {
//     jest.clearAllMocks();
//   });

//   describe('getAccountCustomField', () => {
//     it('should return custom fields for an account', async () => {
//       // Arrange
//       const accountId = 'account-123';
//       const serviceId = 'service-456';
//       const lang = LanguageCode.EN;
      
//       const mockCustomFields = [
//         {
//           id: 'cf-123',
//           account_id: accountId,
//           service_id: serviceId,
//           custom_field_id: 'field-1',
//           field_key: 'username',
//           value: 'testuser',
//           created_at: new Date(),
//         }
//       ];

//       mockAccountRepository.selectAccountById.mockResolvedValue(mockAccount);
//       mockAccountRepository.getAccountCustomFieldValues.mockResolvedValue(mockCustomFields);

//       // Act
//       const result = await service.getAccountCustomField(accountId, serviceId, lang);

//       // Assert
//       expect(mockAccountRepository.selectAccountById).toHaveBeenCalledWith(accountId, serviceId);
//       expect(mockAccountRepository.getAccountCustomFieldValues).toHaveBeenCalledWith(
//         accountId,
//         serviceId,
//         lang
//       );
//       expect(Array.isArray(result)).toBe(true);
//       expect(result[0].field_key).toBe('username');
//       expect(result[0].value).toBe('testuser');
//       expect(typeof result[0].created_at).toBe('string'); // Should be ISO string
//     });

//     it('should throw NotFoundError when account does not exist', async () => {
//       // Arrange
//       const accountId = 'non-existent';
//       const serviceId = 'service-456';
//       const lang = LanguageCode.EN;

//       mockAccountRepository.selectAccountById.mockResolvedValue(null);

//       // Act & Assert
//       await expect(
//         service.getAccountCustomField(accountId, serviceId, lang)
//       ).rejects.toThrow(NotFoundError);
//     });
//   });

//   describe('updateAccountCustomField', () => {
//     it('should update custom fields for an account', async () => {
//       // Arrange
//       const accountId = 'account-123';
//       const serviceId = 'service-456';
//       const lang = LanguageCode.EN;
//       const updateData = {
//         username: 'newusername',
//         age: '25'
//       };

//       mockAccountRepository.selectAccountById.mockResolvedValue(mockAccount);
//       mockServicesCustomFieldsRepository.getLatestCustomFieldsVersion.mockResolvedValue(mockCustomFieldDefinition);
//       mockAccountRepository.isCustomFieldValueUnique.mockResolvedValue(true);
//       mockAccountRepository.getAccountCustomFieldValues.mockResolvedValue([]);
//       mockAccountRepository.createOrUpdateAccountCustomField.mockResolvedValue(undefined);

//       // Act
//       const result = await service.updateAccountCustomField(
//         accountId,
//         serviceId,
//         updateData,
//         lang
//       );

//       // Assert
//       expect(mockAccountRepository.selectAccountById).toHaveBeenCalledWith(accountId, serviceId);
//       expect(mockServicesCustomFieldsRepository.getLatestCustomFieldsVersion).toHaveBeenCalledWith(
//         serviceId,
//         lang
//       );
//       expect(mockAccountRepository.createOrUpdateAccountCustomField).toHaveBeenCalled();
//       expect(Array.isArray(result)).toBe(true);
//       expect(result.length).toBe(2);
//     });

//     it('should validate required fields', async () => {
//       // Arrange
//       const accountId = 'account-123';
//       const serviceId = 'service-456';
//       const lang = LanguageCode.EN;
//       const updateData = {
//         // Missing required 'username' field
//         age: '25'
//       };

//       mockAccountRepository.selectAccountById.mockResolvedValue(mockAccount);
//       mockServicesCustomFieldsRepository.getLatestCustomFieldsVersion.mockResolvedValue(mockCustomFieldDefinition);

//       // Act & Assert
//       await expect(
//         service.updateAccountCustomField(accountId, serviceId, updateData, lang)
//       ).rejects.toThrow(ValidationError);
//     });

//     it('should validate unique fields', async () => {
//       // Arrange
//       const accountId = 'account-123';
//       const serviceId = 'service-456';
//       const lang = LanguageCode.EN;
//       const updateData = {
//         username: 'existinguser',
//         age: '25'
//       };

//       mockAccountRepository.selectAccountById.mockResolvedValue(mockAccount);
//       mockServicesCustomFieldsRepository.getLatestCustomFieldsVersion.mockResolvedValue(mockCustomFieldDefinition);
//       mockAccountRepository.isCustomFieldValueUnique.mockResolvedValue(false); // Not unique
//       mockAccountRepository.getAccountCustomFieldValues.mockResolvedValue([]);

//       // Act & Assert
//       await expect(
//         service.updateAccountCustomField(accountId, serviceId, updateData, lang)
//       ).rejects.toThrow(ValidationError);
//     });
//   });

//   describe('validateCustomData', () => {
//     it('should validate field types correctly', async () => {
//       // Arrange
//       const customData = {
//         username: 'test', // Should be at least 3 chars
//         age: 'not-a-number' // Should be a number
//       };

//       // Act
//       const errors = await (service as any).validateCustomData(
//         customData,
//         mockCustomFieldDefinition.fields,
//         'account-123',
//         'service-456'
//       );

//       // Assert
//       expect(errors).toHaveLength(1);
//       expect(errors[0].field).toBe('age');
//       expect(errors[0].message).toContain('must be a number');
//     });

//     it('should validate field lengths', async () => {
//       // Arrange
//       const customData = {
//         username: 'ab', // Less than min_length (3)
//       };

//       // Act
//       const errors = await (service as any).validateCustomData(
//         customData,
//         [mockCustomFieldDefinition.fields[0]], // Just test the username field
//         'account-123',
//         'service-456'
//       );

//       // Assert
//       expect(errors).toHaveLength(1);
//       expect(errors[0].field).toBe('username');
//       expect(errors[0].message).toContain('must be at least 3 characters');
//     });
//   });
// });
