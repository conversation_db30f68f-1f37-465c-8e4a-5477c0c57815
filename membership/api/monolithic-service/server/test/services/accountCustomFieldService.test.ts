import { AccountCustomFieldService } from '../../src/services/accountCustomFieldService';
import { AccountRepository } from '../../src/repositories/accountRepository';
import { ServicesCustomFieldsRepository } from '../../src/repositories/servicesCustomFieldsRepository';
import { NotFoundError } from '../../src/errors/notFoundError';
import { ValidationError } from '../../src/errors/validationError';
import { LanguageCode, languageCode } from '../../src/enum/languageCode';
import { CustomFieldType } from '../../src/enum/customFieldType';

jest.mock('../../src/repositories/accountRepository');
jest.mock('../../src/repositories/servicesCustomFieldsRepository');

describe('AccountCustomFieldService', () => {
  let service: AccountCustomFieldService;
  let mockAccountRepository: jest.Mocked<AccountRepository>;
  let mockServicesCustomFieldsRepository: jest.Mocked<ServicesCustomFieldsRepository>;

  const mockAccount = {
    account_id: 'account-123',
    service_id: 'service-456',
    user_id: 'user-123',
    membership_id: 1,
    membership_metadata_url: 'https://example.com/metadata',
    display_name: 'Test User',
    profile_image_url: 'https://example.com/profile.jpg',
    membership_contract_address: '0x123',
    membership_token_id: 1,
    token_bound_account_address: '0x456',
    status: 'ACTIVE',
    transaction_id: 'txn-789',
    queue_id: 'queue-abc',
    created_at: new Date(),
    updated_at: new Date(),
    last_login_at: new Date(),
  };

  const mockCustomFieldDefinition = {
    id: 'definition-123',
    service_id: 'service-456',
    version: 1,
    created_at: new Date(),
    updated_at: new Date(),
    fields: [
      {
        custom_field_id: 'field-1',
        service_id: 'service-456',
        field_key: 'username',
        version: 1,
        type: CustomFieldType.TEXT,
        default_value: undefined,
        max_length: 50,
        min_length: 3,
        unique: true,
        verify: false,
        optional: false,
        sort_order: 1,
        created_at: new Date(),
        updated_at: new Date(),
        label: 'Username',
        locale: languageCode.EN_US,
        validator: null,
        options: null
      },
      {
        custom_field_id: 'field-2',
        service_id: 'service-456',
        field_key: 'age',
        version: 1,
        type: CustomFieldType.NUMERIC,
        default_value: undefined,
        max_length: undefined,
        min_length: undefined,
        unique: false,
        verify: false,
        optional: true,
        sort_order: 2,
        created_at: new Date(),
        updated_at: new Date(),
        label: 'Age',
        locale: languageCode.EN_US,
        validator: null,
        options: null
      },
      {
        custom_field_id: 'field-3',
        service_id: 'service-456',
        field_key: 'gender',
        version: 1,
        type: CustomFieldType.MULTIPLE_SELECTION,
        default_value: undefined,
        max_length: undefined,
        min_length: undefined,
        unique: false,
        verify: false,
        optional: false,
        sort_order: 3,
        created_at: new Date(),
        updated_at: new Date(),
        label: 'Gender',
        locale: languageCode.EN_US,
        validator: null,
        options: [
          {
            custom_field_option_id: 'opt-1',
            service_id: 'service-456',
            custom_field_id: 'field-3',
            value: 'male',
            sort_order: 1,
            locale: languageCode.EN_US,
            label: 'Male'
          },
          {
            custom_field_option_id: 'opt-2',
            service_id: 'service-456',
            custom_field_id: 'field-3',
            value: 'female',
            sort_order: 2,
            locale: languageCode.EN_US,
            label: 'Female'
          },
          {
            custom_field_option_id: 'opt-3',
            service_id: 'service-456',
            custom_field_id: 'field-3',
            value: 'other',
            sort_order: 3,
            locale: languageCode.EN_US,
            label: 'Other'
          }
        ]
      },
    ],
  };

  beforeEach(() => {
    mockAccountRepository = {
      selectAccountById: jest.fn(),
      getAccountCustomFieldValues: jest.fn(),
      createOrUpdateAccountCustomField: jest.fn(),
      isCustomFieldValueUnique: jest.fn(),
    } as any;

    mockServicesCustomFieldsRepository = {
      getLatestCustomFieldsVersion: jest.fn(),
    } as any;

    service = new AccountCustomFieldService(
      mockAccountRepository,
      mockServicesCustomFieldsRepository
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getAccountCustomField', () => {
    it('should return custom fields for an account', async () => {
      const accountId = 'account-123';
      const serviceId = 'service-456';
      const lang = languageCode.EN_US;

      const mockCustomFields = [
        {
          custom_field_id: 'field-1',
          service_id: serviceId,
          field_key: 'username',
          version: 1,
          type: CustomFieldType.TEXT,
          default_value: undefined,
          max_length: 50,
          min_length: 3,
          unique: true,
          verify: false,
          optional: false,
          sort_order: 1,
          created_at: new Date('2023-01-01T00:00:00Z'),
          label: 'Username',
          locale: languageCode.EN_US,
          validator: null,
          options: null,
          values: ['testuser']
        }
      ];

      mockAccountRepository.selectAccountById.mockResolvedValue(mockAccount);
      mockAccountRepository.getAccountCustomFieldValues.mockResolvedValue(mockCustomFields);

      const result = await service.getAccountCustomField(accountId, serviceId, lang);

      expect(mockAccountRepository.selectAccountById).toHaveBeenCalledWith(accountId, serviceId);
      expect(mockAccountRepository.getAccountCustomFieldValues).toHaveBeenCalledWith(
        accountId,
        serviceId,
        lang
      );
      expect(Array.isArray(result)).toBe(true);
      expect(result[0].field_key).toBe('username');
      expect(result[0].values).toEqual(['testuser']);
      expect(typeof result[0].created_at).toBe('string');
      expect(result[0].created_at).toBe('2023-01-01T00:00:00.000Z');
    });

    it('should throw NotFoundError when account does not exist', async () => {
      const accountId = 'non-existent';
      const serviceId = 'service-456';
      const lang = languageCode.EN_US;

      mockAccountRepository.selectAccountById.mockResolvedValue(undefined);

      await expect(
        service.getAccountCustomField(accountId, serviceId, lang)
      ).rejects.toThrow(NotFoundError);

      expect(mockAccountRepository.selectAccountById).toHaveBeenCalledWith(accountId, serviceId);
    });
  });

  describe('updateAccountCustomField', () => {
    it('should update custom fields for an account', async () => {
      const accountId = 'account-123';
      const serviceId = 'service-456';
      const lang = languageCode.EN_US;
      const updateData: Record<string, string[]> = {
        username: ['newusername'],
        age: ['25'],
        gender: ['male', 'other']
      };

     const mockExistingFields = [
      {
        custom_field_id: 'field-1',
        service_id: 'service-456',
        field_key: 'username',
        version: 1,
        type: CustomFieldType.TEXT,
        default_value: undefined,
        max_length: 50,
        min_length: 3,
        unique: true,
        verify: false,
        optional: false,
        sort_order: 1,
        created_at: new Date(),
        updated_at: new Date(),
        label: 'Username',
        locale: languageCode.EN_US,
        validator: null,
        options: null,
        values: ['oldusername'],
      },
      {
        custom_field_id: 'field-2',
        service_id: 'service-456',
        field_key: 'age',
        version: 1,
        type: CustomFieldType.NUMERIC,
        default_value: undefined,
        max_length: undefined,
        min_length: undefined,
        unique: false,
        verify: false,
        optional: true,
        sort_order: 2,
        created_at: new Date(),
        updated_at: new Date(),
        label: 'Age',
        locale: languageCode.EN_US,
        validator: null,
        options: null,
        values: ['20'],
      },
      {
        custom_field_id: 'field-3',
        service_id: 'service-456',
        field_key: 'gender',
        version: 1,
        type: CustomFieldType.MULTIPLE_SELECTION,
        default_value: undefined,
        max_length: undefined,
        min_length: undefined,
        unique: false,
        verify: false,
        optional: false,
        sort_order: 3,
        created_at: new Date(),
        updated_at: new Date(),
        label: 'Gender',
        locale: languageCode.EN_US,
        validator: null,

        options: [
          {
            custom_field_option_id: 'opt-1',
            service_id: 'service-456',
            custom_field_id: 'field-3',
            value: 'male',
            label: 'Male',
            sort_order: 1,
            locale: languageCode.EN_US,
          },
          {
            custom_field_option_id: 'opt-2',
            service_id: 'service-456',
            custom_field_id: 'field-3',
            value: 'female',
            label: 'Female',
            sort_order: 2,
            locale: languageCode.EN_US,
          },
          {
            custom_field_option_id: 'opt-3',
            service_id: 'service-456',
            custom_field_id: 'field-3',
            value: 'other',
            label: 'Other',
            sort_order: 3,
            locale: languageCode.EN_US,
          },
        ],

        values: ['female'],
        },
      ];

      const mockUpdatedFields = [
        {
          customFieldId: 'field-1',
          fieldKey: 'username',
          value: 'newusername'
        },
        {
          customFieldId: 'field-2',
          fieldKey: 'age',
          value: '25'
        },
        {
          customFieldId: 'field-3',
          fieldKey: 'gender',
          value: 'male,other'
        }
      ];

      mockAccountRepository.selectAccountById.mockResolvedValue(mockAccount);
      mockServicesCustomFieldsRepository.getLatestCustomFieldsVersion.mockResolvedValue(mockCustomFieldDefinition);
      mockAccountRepository.isCustomFieldValueUnique.mockResolvedValue(true);
      mockAccountRepository.getAccountCustomFieldValues.mockResolvedValue(mockExistingFields);
      mockAccountRepository.createOrUpdateAccountCustomField.mockResolvedValue(undefined);

      const result = await service.updateAccountCustomField(
        accountId,
        serviceId,
        updateData,
        lang
      );

      expect(mockAccountRepository.selectAccountById).toHaveBeenCalledWith(accountId, serviceId);
      expect(mockServicesCustomFieldsRepository.getLatestCustomFieldsVersion).toHaveBeenCalledWith(
        serviceId,
        lang
      );
      expect(mockAccountRepository.createOrUpdateAccountCustomField).toHaveBeenCalled();
      expect(Array.isArray(result)).toBe(true);
    });

    it('should validate required fields', async () => {
      const accountId = 'account-123';
      const serviceId = 'service-456';
      const lang = languageCode.EN_US;
      const updateData: Record<string, string[]> = {
        age: ['25']
      };

      mockAccountRepository.selectAccountById.mockResolvedValue(mockAccount);
      mockServicesCustomFieldsRepository.getLatestCustomFieldsVersion.mockResolvedValue(mockCustomFieldDefinition);
      mockAccountRepository.getAccountCustomFieldValues.mockResolvedValue([]);

      await expect(
        service.updateAccountCustomField(accountId, serviceId, updateData, lang)
      ).rejects.toThrow(ValidationError);
    });

    it('should validate unique fields', async () => {
      const accountId = 'account-123';
      const serviceId = 'service-456';
      const lang = languageCode.EN_US;
      const updateData: Record<string, string[]> = {
        username: ['existinguser'],
        age: ['25'],
        gender: ['male']
      };

      mockAccountRepository.selectAccountById.mockResolvedValue(mockAccount);
      mockServicesCustomFieldsRepository.getLatestCustomFieldsVersion.mockResolvedValue(mockCustomFieldDefinition);
      mockAccountRepository.isCustomFieldValueUnique.mockResolvedValue(false); // Not unique
      mockAccountRepository.getAccountCustomFieldValues.mockResolvedValue([]);

      await expect(
        service.updateAccountCustomField(accountId, serviceId, updateData, lang)
      ).rejects.toThrow(ValidationError);
    });

    it('should throw NotFoundError when account does not exist', async () => {
      const accountId = 'non-existent';
      const serviceId = 'service-456';
      const lang = languageCode.EN_US;
      const updateData: Record<string, string[]> = { username: ['test'] };

      mockAccountRepository.selectAccountById.mockResolvedValue(undefined);

      await expect(
        service.updateAccountCustomField(accountId, serviceId, updateData, lang)
      ).rejects.toThrow(NotFoundError);
    });

    it('should validate field types correctly', async () => {
      const accountId = 'account-123';
      const serviceId = 'service-456';
      const lang = languageCode.EN_US;
      const updateData: Record<string, string[]> = {
        username: ['ab'],
        age: ['not-a-number'],
        gender: ['invalid-option']
      };

      mockAccountRepository.selectAccountById.mockResolvedValue(mockAccount);
      mockServicesCustomFieldsRepository.getLatestCustomFieldsVersion.mockResolvedValue(mockCustomFieldDefinition);
      mockAccountRepository.getAccountCustomFieldValues.mockResolvedValue([]);
      mockAccountRepository.isCustomFieldValueUnique.mockResolvedValue(true);

      await expect(
        service.updateAccountCustomField(accountId, serviceId, updateData, lang)
      ).rejects.toThrow(ValidationError);
    });
  });

  describe('validateCustomData', () => {
    it('should validate field types correctly', async () => {
      const customData = [
        {
          field_key: 'username',
          values: ['test']
        },
        {
          field_key: 'age',
          values: ['not-a-number']
        },
        {
          field_key: 'gender',
          values: ['male']
        }
      ];

      mockAccountRepository.isCustomFieldValueUnique.mockResolvedValue(true);

      const errors = await (service as any).validateCustomData(
        customData,
        mockCustomFieldDefinition.fields,
        'account-123',
        'service-456'
      );

      expect(errors).toHaveLength(1);
      expect(errors[0].field).toBe('age');
      expect(errors[0].message).toContain('must be a number');
    });

    it('should validate field lengths', async () => {
      const customData = [
        {
          field_key: 'username',
          values: ['ab']
        }
      ];

      mockAccountRepository.isCustomFieldValueUnique.mockResolvedValue(true);

      const errors = await (service as any).validateCustomData(
        customData,
        [mockCustomFieldDefinition.fields[0]],
        'account-123',
        'service-456'
      );

      expect(errors).toHaveLength(1);
      expect(errors[0].field).toBe('username');
      expect(errors[0].message).toContain('must be at least 3 characters');
    });

    it('should validate multiple selection options', async () => {
      const customData = [
        {
          field_key: 'gender',
          values: ['invalid-option']
        }
      ];

      const errors = await (service as any).validateCustomData(
        customData,
        [mockCustomFieldDefinition.fields[2]],
        'account-123',
        'service-456'
      );

      expect(errors).toHaveLength(1);
      expect(errors[0].field).toBe('gender');
      expect(errors[0].message).toContain('not a valid option');
    });

    it('should validate required fields', async () => {
      const customData: Array<{ field_key: string, values: string[] }> = [];

      const errors = await (service as any).validateCustomData(
        customData,
        [mockCustomFieldDefinition.fields[0]],
        'account-123',
        'service-456'
      );

      expect(errors).toHaveLength(1);
      expect(errors[0].field).toBe('username');
      expect(errors[0].message).toContain('is required');
    });

    it('should pass validation for valid data', async () => {
      const customData = [
        {
          field_key: 'username',
          values: ['validuser']
        },
        {
          field_key: 'age',
          values: ['25']
        },
        {
          field_key: 'gender',
          values: ['male', 'other']
        }
      ];

      mockAccountRepository.isCustomFieldValueUnique.mockResolvedValue(true);

      const errors = await (service as any).validateCustomData(
        customData,
        mockCustomFieldDefinition.fields,
        'account-123',
        'service-456'
      );

      expect(errors).toHaveLength(0);
    });
  });
});
