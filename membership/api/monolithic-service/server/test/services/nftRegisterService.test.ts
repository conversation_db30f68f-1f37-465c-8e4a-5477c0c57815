import { NftRegisterService } from '../../src/services/nftRegisterService';
import { NftContractTypesRepository } from '../../src/repositories/nftContractTypesRepository';
import { NftContractsRepository } from '../../src/repositories/nftContractsRepository';
import { NftMetadatasRepository } from '../../src/repositories/nftMetadatasRepository';
import { VaultKeyRepository } from '../../src/repositories/vaultKeyRepository';
import { VaultTransactionQueuesRepository } from '../../src/repositories/vaultTransactionQueuesRepository';
import { ServiceInfoRepository } from '../../src/repositories/serviceInfoRepository';
import { NftBaseMetadatasRepository } from '../../src/repositories/nftBaseMetadatasRepository';
import { TransactionService } from '../../src/services/transactionService';
import { WebhookService } from '../../src/services/webhookService';
import { ValidationError } from '../../src/errors/validationError';
import { NftRegister } from '../../src/dtos/nfts/schemas';
import { NftType } from '../../src/enum/nftType';
import { TokenBoundAccountRegistryAddressRepository } from '../../src/repositories/tokenBoundAccountRegistryAddressRepository';
import { AccountRepository } from '../../src/repositories/accountRepository';
import { MetadataService } from '../../src/services/metadataService';
import { NftTransactionUpdateService } from '../../src/services/transactionUpdateService';
import { FirebaseComponent } from '../../src/components/firebaseComponent';
import { NftsFirestoreRepository } from '../../src/repositories/nftsFirestoreRepository';
import { AlchemyComponent } from '../../src/components/alchemyComponent';
import { db } from '../../src/db/database';
import { TransactionResponse } from 'ethers';
import { TransactionsRepository } from '../../src/repositories/transactionsRepository';
import { TransactionQueuesRepository } from '../../src/repositories/transactionQueuesRepository';
import { AttemptTransactionsRepository } from '../../src/repositories/attemptTransactionsRepository';
import { UserRepository } from '../../src/repositories/userRepository';
import { BulkMintService } from '../../src/services/bulkMintService';
import { NftMintService } from '../../src/services/nftMintService';
import { TokenBoundAccountImplementationRepository } from '../../src/repositories/tokenBoundAccountImplementationsRepository';
import { DeliveryNftsFirestoreRepository } from '../../src/repositories/deliveryNftsFirestoreRepository';
import { GcpKmsSigner } from '@cuonghx.gu-tech/ethers-gcp-kms-signer';
import { createMockTransactionComponent, createMockViemComponent } from '../../src/utils/mockFactories';

jest.mock('../../src/repositories/nftContractTypesRepository');
jest.mock('../../src/repositories/nftContractsRepository');
jest.mock('../../src/repositories/nftMetadatasRepository');
jest.mock('../../src/repositories/vaultKeyRepository');
jest.mock('../../src/repositories/vaultTransactionQueuesRepository');
jest.mock('../../src/repositories/serviceInfoRepository');
jest.mock('../../src/repositories/nftBaseMetadatasRepository');
jest.mock('../../src/services/transactionService');
jest.mock('../../src/services/webhookService');
jest.mock('../../src/db/database', () => {
  return {
    db: {
      transaction: jest.fn(),
    },
  };
});

describe('NftRegisterService', () => {
  let nftRegisterService: NftRegisterService;
  let mockNftContractTypesRepository: jest.Mocked<NftContractTypesRepository>;
  let mockNftContractsRepository: jest.Mocked<NftContractsRepository>;
  let mockVaultKeyRepository: jest.Mocked<VaultKeyRepository>;
  let mockVaultTransactionQueuesRepository: jest.Mocked<VaultTransactionQueuesRepository>;
  let mockServiceInfoRepository: jest.Mocked<ServiceInfoRepository>;
  let mockNftBaseMetadatasRepository: jest.Mocked<NftBaseMetadatasRepository>;
  let mockNftMetadatasRepository: jest.Mocked<NftMetadatasRepository>;
  let mockTransactionService: jest.Mocked<TransactionService>;
  let mockWebhookService: jest.Mocked<WebhookService>;
  let mockAccountRepository: jest.Mocked<AccountRepository>;
  let mockTokenBoundAccountRegistryAddressRepository: jest.Mocked<TokenBoundAccountRegistryAddressRepository>;
  let mockMetadataService: jest.Mocked<MetadataService>;
  let mockNftTransactionUpdateService: jest.Mocked<NftTransactionUpdateService>;
  let mockNftsFirestoreRepository: jest.Mocked<NftsFirestoreRepository>;
  let mockFirebaseComponent: jest.Mocked<FirebaseComponent>;
  let mockAlchemyComponent: jest.Mocked<AlchemyComponent>;
  let mockAttemptTransactionsRepository: jest.Mocked<AttemptTransactionsRepository>;
  let mockTransactionsRepository: jest.Mocked<TransactionsRepository>;
  let mockTransactionQueuesRepository: jest.Mocked<TransactionQueuesRepository>;
  let mockBulkMintService: jest.Mocked<BulkMintService>;
  let mockUserRepository: jest.Mocked<UserRepository>;
  let mockNftMintService: jest.Mocked<NftMintService>;
  let mockTokenBoundAccountImplementationRepository: jest.Mocked<TokenBoundAccountImplementationRepository>;
  let mockDeliveryNftsFirestoreRepository: jest.Mocked<DeliveryNftsFirestoreRepository>;

  beforeEach(() => {
    process.env.JSON_RPC_URL = 'http://127.0.0.1:8545';
    process.env.ALCHEMY_API_KEY = 'xxxxxsss';
    process.env.ALCHEMY_CHAIN_NAME = 'polygon-amoy';
    process.env.GAS_LIMIT_MULTIPLIER = '150';
    process.env.BASE_MAX_FEE_PER_GAS = '150';
    process.env.MAX_FEE_PER_GAS_MULTIPLIER = '150';
    process.env.MAX_PRIORITY_FEE_PER_GAS_MULTIPLIER = '150';

    mockNftContractTypesRepository = new NftContractTypesRepository() as jest.Mocked<NftContractTypesRepository>;
    mockNftContractsRepository = new NftContractsRepository() as jest.Mocked<NftContractsRepository>;
    mockNftMetadatasRepository = new NftMetadatasRepository() as jest.Mocked<NftMetadatasRepository>;
    mockVaultKeyRepository = new VaultKeyRepository() as jest.Mocked<VaultKeyRepository>;
    mockVaultTransactionQueuesRepository =
      new VaultTransactionQueuesRepository() as jest.Mocked<VaultTransactionQueuesRepository>;
    mockServiceInfoRepository = new ServiceInfoRepository() as jest.Mocked<ServiceInfoRepository>;
    mockNftBaseMetadatasRepository = new NftBaseMetadatasRepository() as jest.Mocked<NftBaseMetadatasRepository>;
    mockFirebaseComponent = new FirebaseComponent() as jest.Mocked<FirebaseComponent>;
    mockNftsFirestoreRepository = new NftsFirestoreRepository(
      mockFirebaseComponent,
    ) as jest.Mocked<NftsFirestoreRepository>;
    mockAttemptTransactionsRepository =
      new AttemptTransactionsRepository() as jest.Mocked<AttemptTransactionsRepository>;
    mockTransactionQueuesRepository = new TransactionQueuesRepository() as jest.Mocked<TransactionQueuesRepository>;
    mockTransactionsRepository = new TransactionsRepository() as jest.Mocked<TransactionsRepository>;
    mockTransactionService = new TransactionService(
      createMockTransactionComponent(),
      mockAttemptTransactionsRepository,
      mockTransactionsRepository,
      mockTransactionQueuesRepository,
      mockServiceInfoRepository,
      mockNftContractsRepository,
    ) as jest.Mocked<TransactionService>;
    mockMetadataService = new MetadataService(
      mockNftMetadatasRepository,
      mockNftBaseMetadatasRepository,
      mockNftContractTypesRepository,
    ) as jest.Mocked<MetadataService>;
    mockAccountRepository = new AccountRepository() as jest.Mocked<AccountRepository>;
    mockTokenBoundAccountRegistryAddressRepository =
      new TokenBoundAccountRegistryAddressRepository() as jest.Mocked<TokenBoundAccountRegistryAddressRepository>;
    mockDeliveryNftsFirestoreRepository = new DeliveryNftsFirestoreRepository(
      mockFirebaseComponent,
    ) as jest.Mocked<DeliveryNftsFirestoreRepository>;
    mockNftTransactionUpdateService = new NftTransactionUpdateService(
      mockAccountRepository,
      mockNftsFirestoreRepository,
      mockNftContractsRepository,
      mockNftBaseMetadatasRepository,
      mockDeliveryNftsFirestoreRepository,
      mockServiceInfoRepository,
      createMockViemComponent(),
      createMockTransactionComponent(),
      mockTransactionsRepository,
      mockTransactionQueuesRepository,
    ) as jest.Mocked<NftTransactionUpdateService>;
    mockAlchemyComponent = new AlchemyComponent() as jest.Mocked<AlchemyComponent>;
    mockNftMintService = new NftMintService(
      mockNftContractsRepository,
      mockTransactionQueuesRepository,
      mockVaultKeyRepository,
      mockServiceInfoRepository,
      mockDeliveryNftsFirestoreRepository,
    ) as jest.Mocked<NftMintService>;
    mockTokenBoundAccountImplementationRepository =
      new TokenBoundAccountImplementationRepository() as jest.Mocked<TokenBoundAccountImplementationRepository>;

    mockBulkMintService = new BulkMintService(
      mockTransactionService,
      mockNftMintService,
      mockNftContractsRepository,
      mockTransactionQueuesRepository,
      mockTransactionsRepository,
      mockVaultKeyRepository,
      mockServiceInfoRepository,
      mockTokenBoundAccountImplementationRepository,
      mockTokenBoundAccountRegistryAddressRepository,
      mockAccountRepository,
      mockDeliveryNftsFirestoreRepository,
      mockNftBaseMetadatasRepository,
    ) as jest.Mocked<BulkMintService>;
    mockUserRepository = new UserRepository() as jest.Mocked<UserRepository>;

    mockWebhookService = new WebhookService(
      mockTransactionsRepository,
      mockTransactionQueuesRepository,
      mockTokenBoundAccountRegistryAddressRepository,
      mockMetadataService,
      mockNftTransactionUpdateService,
      mockAlchemyComponent,
      mockNftContractsRepository,
      mockBulkMintService,
      mockServiceInfoRepository,
      mockUserRepository,
    ) as jest.Mocked<WebhookService>;

    nftRegisterService = new NftRegisterService(
      mockTransactionService,
      mockWebhookService,
      mockNftContractTypesRepository,
      mockNftContractsRepository,
      mockVaultKeyRepository,
      mockServiceInfoRepository,
      mockMetadataService,
      mockNftBaseMetadatasRepository,
      mockTransactionQueuesRepository,
      mockTransactionsRepository,
    );

    jest.spyOn(global, 'fetch').mockImplementation((url) => {
      if (url === 'http://abi.url') {
        return Promise.resolve({
          ok: true,
          json: async () => [
            {
              constant: false,
              inputs: [
                { name: 'to', type: 'address' },
                { name: 'tokenId', type: 'uint256' },
                { name: 'amount', type: 'uint256' },
                { name: 'data', type: 'string' },
              ],
              name: 'mint',
              outputs: [],
              payable: false,
              stateMutability: 'nonpayable',
              type: 'function',
            },
          ],
        } as Response);
      }
      if (url === 'http://binary.url') {
        return Promise.resolve({
          ok: true,
          json: async () => new ArrayBuffer(8),
        } as Response);
      }
      return Promise.reject(new Error('Invalid URL'));
    });

    mockNftContractsRepository.selectNftContractById.mockImplementation((contractId: string) => {
      if (contractId === 'modular_contract_id') {
        return Promise.resolve({
          nft_contract_id: 'modular_contract_id',
          service_id: 'serviceId',
          nft_contract_type_id: 'mockTypeId',
          nft_collection_name: 'Modular Contract',
          nft_contract_address: '0xModularContractAddress',
          nft_contract_implementation_address: '0xModularImplementationAddress',
        });
      }
      return Promise.resolve(undefined);
    });

    jest.spyOn(NftRegisterService.prototype, 'grantRole').mockImplementation(async () => {
      return Promise.resolve();
    });

    (db.transaction as jest.Mock).mockImplementation(() => {
      return {
        execute: async (callback: (trx: unknown) => Promise<unknown>) => {
          const mockInsertChain = {
            values: jest.fn().mockReturnThis(),
            returningAll: jest.fn().mockReturnThis(),
            executeTakeFirstOrThrow: jest.fn().mockResolvedValue({}),
            execute: jest.fn().mockResolvedValue([]),
          };

          const mockExecutor = {
            insertInto: jest.fn().mockReturnValue(mockInsertChain),
          };
          return callback(mockExecutor);
        },
      };
    });

    mockNftContractsRepository.selectNftContractById.mockImplementation((contractId: string) => {
      if (contractId === 'modular_contract_id') {
        return Promise.resolve({
          nft_contract_id: 'modular_contract_id',
          service_id: 'serviceId',
          nft_contract_type_id: 'mockTypeId',
          nft_collection_name: 'Modular Contract',
          nft_contract_address: '0xModularContractAddress',
          nft_contract_implementation_address: '0xModularImplementationAddress',
        });
      }
      return Promise.resolve(undefined);
    });

    (db.transaction as jest.Mock).mockImplementation(() => {
      return {
        execute: async (callback: (trx: unknown) => Promise<unknown>) => {
          const mockInsertChain = {
            values: jest.fn().mockReturnThis(),
            returningAll: jest.fn().mockReturnThis(),
            executeTakeFirstOrThrow: jest.fn().mockResolvedValue({}),
            execute: jest.fn().mockResolvedValue([]),
          };

          const mockExecutor = {
            insertInto: jest.fn().mockReturnValue(mockInsertChain),
          };
          return callback(mockExecutor);
        },
      };
    });
  });

  describe('register', () => {
    test('should register NFT successfully', async () => {
      const mockTransactionResponse = {
        hash: '0x123',
        signature:
          '0xf86c808504a817c800825208945aae4f4c86c3a7f0eaa2c399c6e37d5e3fa12e418872386f26fc100008025a05e6d3f9a3b2aef203ca4f547ee2b362e264489d39f5bffbc9f2e5834b1d24b2ea0741a4d155774a92f71882d58b3a3941d7f8c24d11e2ea10fd419359f91b1e7ae',
        confirmations: jest.fn().mockResolvedValue(0),
        from: '0x53d284357ec70cE289D6D64134DfAc8E511c8a3D',
        nonce: 1,
        gasLimit: BigInt(21000),
        gasPrice: BigInt(1000000000),
        data: '0x',
        value: BigInt(0),
        chainId: BigInt(1),
        blockNumber: null,
        blockHash: null,
        index: 0,
        type: 0,
        wait: jest.fn().mockResolvedValue({
          to: '0x53d284357ec70cE289D6D64134DfAc8E511c8a3D',
          from: '0x742d35Cc6634C0532925a3b844Bc454e4438f44e',
          contractAddress: '0x32Be343B94f860124dC4fEe278FDCBD38C102D88',
          transactionIndex: 1,
          gasUsed: BigInt(21000),
          blockHash: '0x999',
          blockNumber: 1,
          confirmations: 1,
          status: 1,
          hash: '0x123',
        }),
      };

      const request: NftRegister = {
        deliveryImageUrl: 'https://aa.example.com',
        nftContractTypeId: 'contractTypeId',
        nftName: 'NFT Name',
        nftSymbol: 'NFT Symbol',
        nftCollectionName: 'NFT Collection',
        metadata: Buffer.from(
          JSON.stringify({
            image: 'http://image.url',
            external_url: 'http://external.url',
            description: 'NFT Description',
            attributes: [
              { trait_type: 'Background', value: 'Blue' },
              { trait_type: 'Eyes', value: 'Green' },
            ],
            name: 'NFT Name',
            animation_url: 'http://animation.url',
            youtube_url: 'http://youtube.url',
          }),
        ).toString('base64'),
      };

      mockNftContractTypesRepository.selectNftContractTypeById.mockResolvedValue({
        nft_contract_type_id: 'contractTypeId',
        nft_contract_type_name: NftType.CERTIFICATE,
        nft_contract_type_detail: 'Detailed description of the NFT contract type',
        nft_contract_abi: {} as object,
        nft_contract_binary: '0x1234567890',
        nft_type: NftType.CERTIFICATE,
        nft_contract_address: '0x123',
      });
      mockServiceInfoRepository.getServiceById.mockResolvedValue({
        service_id: 'serviceId',
        tenant_id: 'tenantId',
        service_url: 'http://service.url',
        service_logo_image_url: 'http://logo.url',
        market_cover_image_url: 'mockMarketCoverImageUrl',
        theme_primary_color_lowest: '#FFFFFF',
        theme_primary_color_lower: '#CCCCCC',
        theme_primary_color_higher: '#999999',
        theme_primary_color_highest: '#666666',
        membership_nft_contract_id: 'membershipContractId',
        is_market_enabled: true,
        stripe_account_id: 'mockStripeAccountId',
        line_channel_id: 'line_channel_id',
        commission_rate: 0.1,
        modular_contract_id: 'modular_contract_id',
      });
      mockVaultKeyRepository.getVaultKeyId.mockResolvedValue({
        vault_key_id: 'someKeyId',
        tenant_id: 'tenantId',
        key_ring_project: 'someProject',
        key_ring_location: 'someLocation',
        key_ring_name: 'someKeyRingName',
        key_version: 'someKeyVersion',
        vault_wallet_address: '0xabc',
        nonce: 0,
      });
      mockVaultKeyRepository.getVaultKeyIdWithLock.mockResolvedValue({
        vault_key_id: 'mockVaultKeyId',
        tenant_id: 'mockTenantId',
        key_ring_project: 'mockKeyRingProject',
        key_ring_location: 'mockKeyRingLocation',
        key_ring_name: 'mockKeyRingName',
        key_version: 'mockKeyVersion',
        vault_wallet_address: '******************************************',
        nonce: 0,
      });

      mockTransactionService.createTransactionRequest.mockResolvedValue({
        transactionRequest: {
          from: '0x1234',
          data: '0xabcd',
        },
        signer: {} as unknown as GcpKmsSigner,
        fallbackSigner: {} as unknown as GcpKmsSigner,
      });

      mockTransactionService.calculateTxHash.mockResolvedValue(mockTransactionResponse.hash);
      mockTransactionService.createTransactionDatabases.mockResolvedValue({
        transactionId: 'transactionId',
        attemptTransactionIds: ['attemptTransactionId'],
      });
      mockTransactionService.sendTransactionFromSignedTransaction.mockResolvedValue(
        mockTransactionResponse as unknown as TransactionResponse,
      );

      const result = await nftRegisterService.register('serviceId', request);

      expect(result).toEqual({
        nftContractId: expect.any(String),
        contractAddress: '0x32Be343B94f860124dC4fEe278FDCBD38C102D88',
      });
    });

    test('should deploy implementation contract if coupon contract address not available', async () => {
      mockNftContractTypesRepository.selectNftContractTypeById.mockResolvedValue({
        nft_contract_type_id: 'contractTypeId',
        nft_contract_type_name: NftType.COUPON,
        nft_contract_type_detail: 'Detailed description of coupon contract type',
        nft_contract_abi: [
          {
            inputs: [
              { internalType: 'bytes32', name: 'role', type: 'bytes32' },
              { internalType: 'address', name: 'account', type: 'address' },
            ],
            name: 'grantRole',
            outputs: [],
            stateMutability: 'nonpayable',
            type: 'function',
          },
        ] as object,
        nft_contract_binary: '0x1234567890',
        nft_type: NftType.COUPON,
        nft_contract_address: undefined,
      });
      mockServiceInfoRepository.getServiceById.mockResolvedValue({
        service_id: 'serviceId',
        tenant_id: 'tenantId',
        service_url: 'http://service.url',
        service_logo_image_url: 'http://logo.url',
        market_cover_image_url: 'mockMarketCoverImageUrl',
        theme_primary_color_lowest: '#FFFFFF',
        theme_primary_color_lower: '#CCCCCC',
        theme_primary_color_higher: '#999999',
        theme_primary_color_highest: '#666666',
        membership_nft_contract_id: 'membershipContractId',
        is_market_enabled: true,
        stripe_account_id: 'mockStripeAccountId',
        line_channel_id: 'line_channel_id',
        commission_rate: 0.1,
        modular_contract_id: 'modular_contract_id',
      });
      mockMetadataService.insertBaseMetadataAndGenerateBaseUrl = jest.fn().mockResolvedValue({
        metadtaUrl: 'http://metadata.url',
        baseNftId: 'baseNftId',
      });
      mockVaultKeyRepository.getVaultKeyId.mockResolvedValue({
        vault_key_id: 'someKeyId',
        tenant_id: 'tenantId',
        key_ring_project: 'someProject',
        key_ring_location: 'someLocation',
        key_ring_name: 'someKeyRingName',
        key_version: 'someKeyVersion',
        vault_wallet_address: '0xabc',
        nonce: 0,
      });
      mockVaultKeyRepository.getVaultKeyIdWithLock.mockResolvedValue({
        vault_key_id: 'someKeyId',
        tenant_id: 'tenantId',
        key_ring_project: 'someProject',
        key_ring_location: 'someLocation',
        key_ring_name: 'someKeyRingName',
        key_version: 'someKeyVersion',
        vault_wallet_address: '0xabc',
        nonce: 0,
      });

      mockTransactionService.prepareDeployContract.mockResolvedValueOnce('implementationCallData');
      const fakeImplementationResponse = {
        hash: '0ximp',
        wait: jest.fn().mockResolvedValue({
          contractAddress: '0xImpAddress',
        }),
      };
      mockTransactionService.sendRawTransaction.mockResolvedValueOnce(
        fakeImplementationResponse as unknown as TransactionResponse,
      );
      mockTransactionService.calculateTxHash.mockResolvedValueOnce(fakeImplementationResponse.hash);
      mockTransactionService.sendTransactionFromSignedTransaction.mockResolvedValueOnce(
        fakeImplementationResponse as unknown as TransactionResponse,
      );

      mockTransactionService.prepareDeployUpgradeableContract.mockResolvedValueOnce('proxyCallData');
      const fakeFinalResponse = {
        hash: '0xfinal',
        wait: jest.fn().mockResolvedValue({
          contractAddress: '0xFinalAddress',
          implementAddress: '0xImpAddress',
        }),
      };

      mockTransactionService.sendRawTransaction.mockResolvedValueOnce(
        fakeFinalResponse as unknown as TransactionResponse,
      );
      mockTransactionService.calculateTxHash.mockResolvedValueOnce(fakeFinalResponse.hash);
      mockTransactionService.sendTransactionFromSignedTransaction.mockResolvedValueOnce(
        fakeFinalResponse as unknown as TransactionResponse,
      );

      mockTransactionService.createTransactionRequest.mockResolvedValue({
        transactionRequest: {
          from: '0x1234',
          data: '0xabcd',
        },
        signer: {} as unknown as GcpKmsSigner,
        fallbackSigner: {} as unknown as GcpKmsSigner,
      });
      mockTransactionService.createTransactionDatabases.mockResolvedValue({
        transactionId: 'transactionId',
        attemptTransactionIds: ['attemptTransactionId'],
      });
      mockVaultTransactionQueuesRepository.updateVaultTransactionHash.mockResolvedValue(undefined);
      mockNftContractsRepository.insertNftContract.mockResolvedValue(undefined);

      const request: NftRegister = {
        nftContractTypeId: 'contractTypeId',
        nftName: 'Coupon NFT',
        nftSymbol: 'CPN',
        nftCollectionName: 'Coupon Collection',
        metadata: JSON.stringify({ key: 'value' }),
        deliveryImageUrl: 'https://aa.example.com',
      };
      const result = await nftRegisterService.register('serviceId', request);

      expect(result).toEqual({
        nftContractId: expect.any(String),
        contractAddress: '0xFinalAddress',
      });
      expect(mockNftContractTypesRepository.updateNftContractAddress).toHaveBeenCalledWith(
        'contractTypeId',
        '0xImpAddress',
      );
    });

    test('should throw ValidationError if NFT contract type is not found', async () => {
      const request: NftRegister = {
        nftContractTypeId: 'invalidContractTypeId',
        nftName: 'NFT Name',
        nftSymbol: 'NFT Symbol',
        nftCollectionName: 'NFT Collection',
        metadata: '{}',
        deliveryImageUrl: 'https://aa.example.com',
      };

      mockNftContractTypesRepository.selectNftContractTypeById.mockResolvedValue(undefined);

      await expect(nftRegisterService.register('serviceId', request)).rejects.toThrow(ValidationError);
    });

    test('should throw NotFoundError if service is not found', async () => {
      const request: NftRegister = {
        nftContractTypeId: 'contractTypeId',
        nftName: 'NFT Name',
        nftSymbol: 'NFT Symbol',
        nftCollectionName: 'NFT Collection',
        metadata: '{}',
        deliveryImageUrl: 'https://aa.example.com',
      };

      mockNftContractTypesRepository.selectNftContractTypeById.mockResolvedValue({
        nft_contract_type_id: 'contractTypeId',
        nft_contract_type_name: NftType.COUPON,
        nft_contract_type_detail: 'Detailed description of the NFT contract type',
        nft_contract_abi: {} as object,
        nft_contract_binary: '0x1234567890',
        nft_type: NftType.COUPON,
        nft_contract_address: '0x123',
      });
      mockServiceInfoRepository.getServiceById.mockResolvedValue(undefined);

      await expect(nftRegisterService.register('serviceId', request)).rejects.toThrow(ValidationError);
    });
  });
});
