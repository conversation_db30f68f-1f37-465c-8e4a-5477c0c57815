jest.mock('../../src/db/database', () => {
  const actual = jest.requireActual('../../src/db/database');
  return {
    ...actual,
    db: {
      ...actual.db,
      transaction: () => ({
        execute: async (cb: any) => cb(),
      }),
    },
  };
});

import { SerialCodeProjectStatus } from '../../src/enum/serialCodeProjectStatus';

// DBトランザクションのmock

import { AccountRepository } from '../../src/repositories/accountRepository';
import { AccountSerialCodesRepository } from '../../src/repositories/accountSerialCodesRepository';
import { AchievementActionRepository } from '../../src/repositories/achievementActionRepository';
import { CertificateRewardRepository } from '../../src/repositories/certificateRewardRepository';
import { ClaimedRewardRepository } from '../../src/repositories/claimedRewardRepository';
import { CouponRewardRepository } from '../../src/repositories/couponRewardRepository';
import { DigitalContentRewardRepository } from '../../src/repositories/digitalContentRewardRepository';
import { NftBaseMetadatasRepository } from '../../src/repositories/nftBaseMetadatasRepository';
import { NftContractsRepository } from '../../src/repositories/nftContractsRepository';
import { NftContractTypesRepository } from '../../src/repositories/nftContractTypesRepository';
import { NftMetadatasRepository } from '../../src/repositories/nftMetadatasRepository';
import { QuestActivityRepository } from '../../src/repositories/questActivityRepository';
import { QuestionnaireResultAnswerRepository } from '../../src/repositories/questionnaireResultAnswerRepository';
import { RewardRepository } from '../../src/repositories/rewardRepository';
import { SerialCodeProjectsRepository } from '../../src/repositories/serialCodeProjectsRepository';
import { InsertSerialCode, SerialCodesRepository } from '../../src/repositories/serialCodesRepository';
import { ServiceInfoRepository } from '../../src/repositories/serviceInfoRepository';
import { VaultKeyRepository } from '../../src/repositories/vaultKeyRepository';
import { TransactionQueuesRepository } from '../../src/repositories/transactionQueuesRepository';
import { MetadataService } from '../../src/services/metadataService';
import { NftMintService } from '../../src/services/nftMintService';
import { NftsService } from '../../src/services/nftsService';
import { RewardService } from '../../src/services/rewardService';
import { SerialCodeService } from '../../src/services/serialCodeService';
import { languageCode } from '../../src/enum/languageCode';
import {
  SerialCodeGenerationResponse,
  SerialCodes,
  SerialCodesProcedure,
  SerialCodeTranslation,
} from '../../src/dtos/serial_codes/schemas';
import { DeliveryNftsFirestoreRepository } from '../../src/repositories/deliveryNftsFirestoreRepository';
import { FirebaseComponent } from '../../src/components/firebaseComponent';
import { Transaction } from 'kysely';

jest.mock('../../src/db/database', () => ({
  db: {
    selectFrom: jest.fn(),
    transaction: jest.fn().mockReturnValue({
      execute: jest.fn().mockImplementation(async (callback) => {
        const mockTransaction = {} as Transaction<unknown>;
        return await callback(mockTransaction);
      }),
    } as unknown as jest.Mocked<Transaction<unknown>>),
  },
}));

describe('SerialCodeService', () => {
  let serialCodeProjectsRepository: jest.Mocked<SerialCodeProjectsRepository>;
  let serialCodesRepository: jest.Mocked<SerialCodesRepository>;
  let accountSerialCodesRepository: jest.Mocked<AccountSerialCodesRepository>;
  let rewardRepository: jest.Mocked<RewardRepository>;
  let accountRepository: jest.Mocked<AccountRepository>;
  let certificateRewardRepository: jest.Mocked<CertificateRewardRepository>;
  let couponRewardRepository: jest.Mocked<CouponRewardRepository>;
  let digitalContentRewardRepository: jest.Mocked<DigitalContentRewardRepository>;
  let claimedRewardRepository: jest.Mocked<ClaimedRewardRepository>;
  let achievementActionRepository: jest.Mocked<AchievementActionRepository>;
  let questActivityRepository: jest.Mocked<QuestActivityRepository>;
  let questionnaireResultAnswerRepository: jest.Mocked<QuestionnaireResultAnswerRepository>;
  let nftContractsRepository: jest.Mocked<NftContractsRepository>;
  let vaultKeyRepository: jest.Mocked<VaultKeyRepository>;
  let serviceInfoRepository: jest.Mocked<ServiceInfoRepository>;
  let nftMetadatasRepository: jest.Mocked<NftMetadatasRepository>;
  let nftBaseMetadatasRepository: jest.Mocked<NftBaseMetadatasRepository>;
  let nftContractTypesRepository: jest.Mocked<NftContractTypesRepository>;
  let transactionQueuesRepository: jest.Mocked<TransactionQueuesRepository>;
  let deliveryNftsFirestoreRepository: jest.Mocked<DeliveryNftsFirestoreRepository>;

  let metadataService: jest.Mocked<MetadataService>;
  let nftsService: jest.Mocked<NftsService>;
  let nftMintService: jest.Mocked<NftMintService>;
  let rewardService: jest.Mocked<RewardService>;
  let serialCodeService: jest.Mocked<SerialCodeService>;

  beforeEach(() => {
    process.env.JSON_RPC_URL = 'http://localhost:8545'; // Use a valid URL format
    process.env.ALCHEMY_API_KEY = 'xxxxxsss';
    process.env.ALCHEMY_CHAIN_NAME = 'polygon-amoy';
    process.env.GAS_LIMIT_MULTIPLIER = '100';
    process.env.BASE_MAX_FEE_PER_GAS = '100';
    process.env.MAX_FEE_PER_GAS_MULTIPLIER = '100';
    process.env.MAX_PRIORITY_FEE_PER_GAS_MULTIPLIER = '100';
    process.env.GCP_PROJECT_ID = 'sample_gcp_project_id'; // Added GCP_PROJECT_ID
    process.env.GCS_BUCKET_NAME = 'mock-bucket-name'; // Added GCS_BUCKET_NAME
    process.env.ALCHEMY_ADDRESS_ACTIVITY_WEBHOOK_ID = 'mockAddressActivityWebhookId'; // Added
    process.env.ALCHEMY_NFT_ACTIVITY_WEBHOOK_ID = 'mockNftActivityWebhookId'; // Added
    process.env.ALCHEMY_ADDRESS_ACTIVITY_WEBHOOK_SIGNING_KEY = 'mockSigningKey'; // Added
    process.env.ALCHEMY_NFT_ACTIVITY_WEBHOOK_SIGNING_KEY = 'mockSigningKey'; // Added
    process.env.ALCHEMY_AUTH_TOKEN = 'mockAuthToken'; // Added

    serialCodeProjectsRepository = new SerialCodeProjectsRepository() as jest.Mocked<SerialCodeProjectsRepository>;
    serialCodesRepository = new SerialCodesRepository() as jest.Mocked<SerialCodesRepository>;
    accountSerialCodesRepository = new AccountSerialCodesRepository() as jest.Mocked<AccountSerialCodesRepository>;
    rewardRepository = new RewardRepository() as jest.Mocked<RewardRepository>;
    accountRepository = new AccountRepository() as jest.Mocked<AccountRepository>;
    certificateRewardRepository = new CertificateRewardRepository() as jest.Mocked<CertificateRewardRepository>;
    couponRewardRepository = new CouponRewardRepository() as jest.Mocked<CouponRewardRepository>;
    digitalContentRewardRepository =
      new DigitalContentRewardRepository() as jest.Mocked<DigitalContentRewardRepository>;
    claimedRewardRepository = new ClaimedRewardRepository() as jest.Mocked<ClaimedRewardRepository>;
    achievementActionRepository = new AchievementActionRepository() as jest.Mocked<AchievementActionRepository>;
    questActivityRepository = new QuestActivityRepository() as jest.Mocked<QuestActivityRepository>;
    questionnaireResultAnswerRepository =
      new QuestionnaireResultAnswerRepository() as jest.Mocked<QuestionnaireResultAnswerRepository>;
    nftContractsRepository = new NftContractsRepository() as jest.Mocked<NftContractsRepository>;
    transactionQueuesRepository = new TransactionQueuesRepository() as jest.Mocked<TransactionQueuesRepository>;
    vaultKeyRepository = new VaultKeyRepository() as jest.Mocked<VaultKeyRepository>;
    serviceInfoRepository = new ServiceInfoRepository() as jest.Mocked<ServiceInfoRepository>;
    nftMetadatasRepository = new NftMetadatasRepository() as jest.Mocked<NftMetadatasRepository>;
    nftBaseMetadatasRepository = new NftBaseMetadatasRepository() as jest.Mocked<NftBaseMetadatasRepository>;
    nftContractTypesRepository = new NftContractTypesRepository() as jest.Mocked<NftContractTypesRepository>;
    deliveryNftsFirestoreRepository = new DeliveryNftsFirestoreRepository(
      {} as FirebaseComponent,
    ) as jest.Mocked<DeliveryNftsFirestoreRepository>;

    metadataService = new MetadataService(
      nftMetadatasRepository,
      nftBaseMetadatasRepository,
      nftContractTypesRepository,
    ) as jest.Mocked<
    MetadataService>;
    nftsService = new NftsService(
      nftContractsRepository,
      accountRepository,
      rewardRepository,
    ) as jest.Mocked<NftsService>;
    nftMintService = new NftMintService(
      nftContractsRepository,
      transactionQueuesRepository,
      vaultKeyRepository,
      serviceInfoRepository,
      deliveryNftsFirestoreRepository,
    ) as jest.Mocked<NftMintService>;
    rewardService = new RewardService(
      rewardRepository,
      accountRepository,
      certificateRewardRepository,
      couponRewardRepository,
      digitalContentRewardRepository,
      claimedRewardRepository,
      achievementActionRepository,
      questActivityRepository,
      nftMintService,
      nftsService,
      metadataService,
      questionnaireResultAnswerRepository,
    ) as jest.Mocked<RewardService>;
    serialCodeService = new SerialCodeService(
      serialCodeProjectsRepository,
      serialCodesRepository,
      accountSerialCodesRepository,
      rewardService,
    ) as jest.Mocked<SerialCodeService>;

    jest.mock('../../src/db/database', () => {
      const actual = jest.requireActual('../../src/db/database');
      return {
        ...actual,
        db: {
          ...actual.db,
          transaction: () => ({
            execute: async (cb: any) => cb(),
          }),
        },
      };
    });
  });

  describe('import', () => {
    test('should import serial-code and return a response with status 200', async () => {
      const hashKey = 'hash_key';
      const serialCodes: SerialCodes[] = [
        { code: 'AAAA-BBBB-CCCC-DDDD', maxUseNum: 1 },
        { code: '2222-3333-4444-5555', maxUseNum: 1 },
      ];
      const serialCodeHashs = [
        serialCodeService.serialCodeToHash(serialCodes[0].code, hashKey),
        serialCodeService.serialCodeToHash(serialCodes[1].code, hashKey),
      ];
      const serialCodeIds = ['serial_code_id_1', 'serial_code_id_2'];
      const serialCodeSlug = 'serial-code-slug';
      const serialCodeProjectId = 'serial_code_project_id';
      const serviceId = 'service_id';
      const projectName = 'name';
      const projectDescription = 'description';
      const rewardId = 'reward_id';
      const startAt = new Date('2025-01-01T00:00:00Z');
      const endAt = new Date('2025-12-31T00:00:00Z');
      const createdAt = new Date('2025-01-01T00:00:00Z');
      const updatedAt = new Date('2025-01-01T00:00:00Z');
      const serialCodeTranslations: SerialCodeTranslation[] = [
        {
          language: languageCode.EN_US,
          name: 'name',
          description: 'description',
        },
        {
          language: languageCode.JA,
          name: '名前',
          description: '説明',
        },
      ];

      serialCodeProjectsRepository.insert = jest.fn().mockResolvedValue({
        serial_code_project_id: serialCodeProjectId,
        slug: serialCodeSlug,
        service_id: serviceId,
        name: projectName,
        description: projectDescription,
        reward_id: rewardId,
        hash_key: hashKey,
        status: 'ENABLE',
        start_at: startAt,
        end_at: endAt,
        created_at: createdAt,
        updated_at: updatedAt,
      });
      serialCodeProjectsRepository.insertTranslation = jest.fn().mockResolvedValue([
        {
          serial_code_project_id: serialCodeProjectId,
          service_id: serviceId,
          language: languageCode.EN_US,
          name: 'name',
          description: 'description',
        },
        {
          serial_code_project_id: serialCodeProjectId,
          service_id: serviceId,
          language: languageCode.JA,
          name: '名前',
          description: '説明',
        },
      ]);
      serialCodesRepository.insertSerialCodes = jest.fn().mockResolvedValue([
        {
          serial_code_id: serialCodeIds[0],
          code_hash: serialCodeHashs[0],
          serial_code_project_id: serialCodeProjectId,
          max_use_num: 1,
          remaining_use_num: 1,
          created_at: createdAt,
          updated_at: updatedAt,
        },
        {
          serial_code_id: serialCodeIds[1],
          code_hash: serialCodeHashs[1],
          serial_code_project_id: serialCodeProjectId,
          max_use_num: 1,
          remaining_use_num: 1,
          created_at: createdAt,
          updated_at: updatedAt,
        },
      ]);

      const result = await serialCodeService.import(
        serviceId,
        serialCodeSlug,
        serialCodeTranslations,
        serialCodes,
        rewardId,
        hashKey,
        startAt,
        endAt,
      );

      const expectedRes: SerialCodeGenerationResponse = {
        serialCodeProjectId: serialCodeProjectId,
        slug: serialCodeSlug,
        serialCodeProjectTranslations: [
          {
            language: languageCode.EN_US,
            name: 'name',
            description: 'description',
          },
          {
            language: languageCode.JA,
            name: '名前',
            description: '説明',
          },
        ],
        rewardId: rewardId,
        startAt: startAt.toISOString(),
        endAt: endAt.toISOString(),
        serialCodes: [
          {
            serialCodeId: serialCodeIds[0],
            code: serialCodes[0].code,
            codeHash: serialCodeHashs[0],
            maxUseNum: 1,
          },
          {
            serialCodeId: serialCodeIds[1],
            code: serialCodes[1].code,
            codeHash: serialCodeHashs[1],
            maxUseNum: 1,
          },
        ],
      };

      expect(result).toEqual(expectedRes);
    });
  });

  describe('create', () => {
    test('should create serial-code and return a response with status 200', async () => {
      const hashKey = 'hash_key';
      const serialCodeIds = ['serial_code_id_1', 'serial_code_id_2'];
      const serialCodeSlug = 'serial-code-slug';
      const serialCodeProjectId = 'serial_code_project_id';
      const serviceId = 'service_id';
      const serialCodesProcedure: SerialCodesProcedure[] = [{ createCodeNum: 2, maxUseNum: 2 }];
      const rewardId = 'reward_id';
      const startAt = new Date('2025-01-01T00:00:00Z');
      const endAt = new Date('2025-12-31T00:00:00Z');
      const createdAt = new Date('2025-01-01T00:00:00Z');
      const updatedAt = new Date('2025-01-01T00:00:00Z');
      const serialCodeMap: { serialCode: string; codeHash: string }[] = [];
      const serialCodeTranslations: SerialCodeTranslation[] = [
        {
          language: languageCode.EN_US,
          name: 'name',
          description: 'description',
        },
        {
          language: languageCode.JA,
          name: '名前',
          description: '説明',
        },
      ];
      serialCodeProjectsRepository.insert = jest.fn().mockResolvedValue({
        serial_code_project_id: serialCodeProjectId,
        slug: serialCodeSlug,
        service_id: serviceId,
        reward_id: rewardId,
        hash_key: hashKey,
        status: SerialCodeProjectStatus.ENABLE,
        start_at: startAt,
        end_at: endAt,
        created_at: createdAt,
        updated_at: updatedAt,
      });

      serialCodeProjectsRepository.insertTranslation = jest.fn().mockResolvedValue([
        {
          serial_code_project_id: serialCodeProjectId,
          service_id: serviceId,
          language: languageCode.EN_US,
          name: 'name',
          description: 'description',
        },
        {
          serial_code_project_id: serialCodeProjectId,
          service_id: serviceId,
          language: languageCode.JA,
          name: '名前',
          description: '説明',
        },
      ]);

      serialCodesRepository.insertSerialCodes = jest
        .fn()
        .mockImplementation(async (serviceId: string, serialCodes: InsertSerialCode[], trx: any) => {
          return serialCodes.map((code, i) => {
            return {
              serial_code_id: serialCodeIds[i],
              code_hash: code.codeHash,
              serial_code_project_id: code.serialCodeProjectId,
              max_use_num: code.maxUseNum,
              remaining_use_num: code.maxUseNum,
              created_at: createdAt,
              updated_at: updatedAt,
            };
          });
        });

      serialCodeService.serialCodeToHash = jest.fn().mockImplementation((serialCode: string, hashKey: string) => {
        serialCodeMap.push({
          serialCode,
          codeHash: `${serialCode}_${hashKey}`,
        });
        return `${serialCode}_${hashKey}`;
      });

      const result = await serialCodeService.create(
        serviceId,
        serialCodeSlug,
        serialCodeTranslations,
        serialCodesProcedure,
        rewardId,
        startAt,
        endAt,
      );

      expect(result).toEqual({
        serialCodeProjectId: serialCodeProjectId,
        slug: serialCodeSlug,
        serialCodeProjectTranslations: [
          {
            language: languageCode.EN_US,
            name: 'name',
            description: 'description',
          },
          {
            language: languageCode.JA,
            name: '名前',
            description: '説明',
          },
        ],
        rewardId: rewardId,
        startAt: startAt.toISOString(),
        endAt: endAt.toISOString(),
        serialCodes: [
          {
            serialCodeId: serialCodeIds[0],
            code: serialCodeMap[0].serialCode,
            codeHash: serialCodeMap[0].codeHash,
            maxUseNum: 2,
          },
          {
            serialCodeId: serialCodeIds[1],
            code: serialCodeMap[1].serialCode,
            codeHash: serialCodeMap[1].codeHash,
            maxUseNum: 2,
          },
        ],
      });
    });
  });

  describe('redeem', () => {
    test('should redeem serial-code and return a response with status 200', async () => {
      const serviceId = 'service_id';
      const accountId = 'account_id';
      const serialCode = 'serial_code';
      const serialCodeProjectId = 'serial_code_project_id';
      const hashKey = 'hash_key';
      const startAt = new Date('2025-01-01T00:00:00Z');
      const endAt = new Date('2025-12-31T00:00:00Z');
      const serialCodeId = 'serial_code_id';
      const createdAt = new Date('2025-01-01T00:00:00Z');
      const updatedAt = new Date('2025-01-01T00:00:00Z');
      const accountSerialCodeId = 'account_serial_code_id';

      serialCodeProjectsRepository.selectSerialCodeProject = jest.fn().mockImplementation(async () => {
        return {
          service_id: serviceId,
          reward_id: undefined,
          hash_key: hashKey,
          start_at: startAt,
          end_at: endAt,
          status: SerialCodeProjectStatus.ENABLE,
        };
      });

      serialCodesRepository.selectSerialCodeWithLock = jest.fn().mockImplementation(async (codeHash: string) => {
        return {
          serial_code_id: serialCodeId,
          code_hash: codeHash,
          serial_code_project_id: serialCodeProjectId,
          max_use_num: 3,
          remaining_use_num: 3,
          created_at: createdAt,
          updated_at: updatedAt,
        };
      });

      accountSerialCodesRepository.insert = jest
        .fn()
        .mockImplementation(async (accountId: string, serialCodeId: string) => {
          return {
            account_serial_code_id: accountSerialCodeId,
            account_id: accountId,
            serial_code_id: serialCodeId,
            created_at: createdAt,
          };
        });
      accountSerialCodesRepository.countBySerialCodeId = jest.fn().mockImplementation(async () => {
        return 1;
      });

      serialCodesRepository.updateRemainingUseNum = jest.fn().mockImplementation();

      const result = await serialCodeService.redeem(serviceId, accountId, serialCode, serialCodeProjectId);

      expect(result).toEqual({
        accountSerialCodeId: accountSerialCodeId,
        serialCodeId: serialCodeId,
        remainingUseNum: 2,
      });
    });

    test('should redeem serial-code. invalid project status.', async () => {
      const serviceId = 'service_id';
      const accountId = 'account_id';
      const serialCode = 'serial_code';
      const serialCodeProjectId = 'serial_code_project_id';

      serialCodeProjectsRepository.selectSerialCodeProject = jest.fn().mockImplementation(async () => {
        return undefined;
      });

      const result = serialCodeService.redeem(serviceId, accountId, serialCode, serialCodeProjectId);
      expect(result).rejects.toThrow('serial code project does not exist. [serial_code_project_id]');
    });

    test('should redeem serial-code. serial code does not exist.', async () => {
      const serviceId = 'service_id';
      const accountId = 'account_id';
      const serialCode = 'serial_code';
      const serialCodeProjectId = 'serial_code_project_id';
      const hashKey = 'hash_key';
      const startAt = new Date('2025-01-01T00:00:00Z');
      const endAt = new Date('2025-12-31T00:00:00Z');

      serialCodeProjectsRepository.selectSerialCodeProject = jest.fn().mockImplementation(async () => {
        return {
          service_id: serviceId,
          reward_id: undefined,
          hash_key: hashKey,
          start_at: startAt,
          end_at: endAt,
          status: SerialCodeProjectStatus.ENABLE,
        };
      });

      serialCodesRepository.selectSerialCodeWithLock = jest.fn().mockImplementation(async () => {
        return undefined;
      });

      const result = serialCodeService.redeem(serviceId, accountId, serialCode, serialCodeProjectId);
      expect(result).rejects.toThrow('serial code does not exist.');
    });

    test('should redeem serial-code. not enough serial codes used.', async () => {
      const serviceId = 'service_id';
      const accountId = 'account_id';
      const serialCode = 'serial_code';
      const serialCodeProjectId = 'serial_code_project_id';
      const hashKey = 'hash_key';
      const startAt = new Date('2025-01-01T00:00:00Z');
      const endAt = new Date('2025-12-31T00:00:00Z');
      const serialCodeId = 'serial_code_id';
      const createdAt = new Date('2025-01-01T00:00:00Z');
      const updatedAt = new Date('2025-01-01T00:00:00Z');

      serialCodeProjectsRepository.selectSerialCodeProject = jest.fn().mockImplementation(async () => {
        return {
          service_id: serviceId,
          reward_id: undefined,
          hash_key: hashKey,
          start_at: startAt,
          end_at: endAt,
          status: SerialCodeProjectStatus.ENABLE,
        };
      });

      serialCodesRepository.selectSerialCodeWithLock = jest.fn().mockImplementation(async (codeHash: string) => {
        return {
          serial_code_id: serialCodeId,
          code_hash: codeHash,
          serial_code_project_id: serialCodeProjectId,
          max_use_num: 3,
          remaining_use_num: 0,
          created_at: createdAt,
          updated_at: updatedAt,
        };
      });

      const result = serialCodeService.redeem(serviceId, accountId, serialCode, serialCodeProjectId);
      expect(result).rejects.toThrow('not enough serial codes used.');
    });
  });
});
