import 'reflect-metadata';
import Stripe from 'stripe';
import { ProductService } from '../../src/services/productService';
import { CheckoutRepository } from '../../src/repositories/checkoutRepository';
import { NftMintService } from '../../src/services/nftMintService';
import { NftContractsRepository } from '../../src/repositories/nftContractsRepository';
import { NotFoundError } from '../../src/errors/notFoundError';
import { InternalServerError } from '../../src/errors/internalServerError';
import { ServiceInfoRepository } from '../../src/repositories/serviceInfoRepository';
import { AccountRepository } from '../../src/repositories/accountRepository';
import { ProductRepository } from '../../src/repositories/productRepository';
import { ServiceEntity } from '../../src/tables/servicesTable';
import { CustomFieldsRepository } from '../../src/repositories/customFieldsRepository';
import { MetadataService } from '../../src/services/metadataService';
import { NftType } from '../../src/enum/nftType';
import { ProductEntity, ProductWithTranslationsEntity } from '../../src/tables/productsTable';
import { AccountEntity } from '../../src/tables/accountTable';
import { TransactionData } from '../../src/dtos/nfts/schemas';
import { StripeCheckoutInfo, StripeProduct } from '../../src/dtos/products/schemas';
import { languageCode } from '../../src/enum/languageCode';
import { TxType } from '../../src/enum/txType';

const mockStripeConstructEvent = jest.fn();
const mockStripeRetrieveSession = jest.fn();
const mockStripeListLineItems = jest.fn();
const mockStripeCreateSession = jest.fn();
const mockStripeRetrievePrice = jest.fn();
const mockStripeListProductItems = jest.fn();

jest.mock('stripe', () => {
  const StripeMock = jest.fn(() => ({
    webhooks: {
      constructEvent: mockStripeConstructEvent,
    },
    checkout: {
      sessions: {
        retrieve: mockStripeRetrieveSession,
        listLineItems: mockStripeListLineItems,
        create: mockStripeCreateSession,
      },
    },
    prices: {
      retrieve: mockStripeRetrievePrice,
    },
    products: {
      list: mockStripeListProductItems,
    },
  }));
  return StripeMock;
});

describe('ProductService', () => {
  let productService: ProductService;
  let mockCheckoutRepository: jest.Mocked<CheckoutRepository>;
  let mockNftMintService: jest.Mocked<NftMintService>;
  let mockNftContractsRepository: jest.Mocked<NftContractsRepository>;
  let mockServiceInfoRepository: jest.Mocked<ServiceInfoRepository>;
  let mockAccountRepository: jest.Mocked<AccountRepository>;
  let mockProductRepository: jest.Mocked<ProductRepository>;
  let mockCustomFieldsRepository: jest.Mocked<CustomFieldsRepository>;
  let mockMetadataService: jest.Mocked<MetadataService>;
  let mockStripe: jest.Mocked<Stripe>;

  beforeEach(() => {
    process.env.JSON_RPC_URL = 'http://127.0.0.1:8545';
    process.env.ALCHEMY_API_KEY = 'xxxxxsss';
    process.env.ALCHEMY_CHAIN_NAME = 'polygon-amoy';
    process.env.GAS_LIMIT_MULTIPLIER = '200';
    process.env.BASE_MAX_FEE_PER_GAS = '600';
    process.env.MAX_FEE_PER_GAS_MULTIPLIER = '150';
    process.env.MAX_PRIORITY_FEE_PER_GAS_MULTIPLIER = '150';
    process.env.GCP_PROJECT_ID = 'sample_gcp_project_id';

    mockStripe = new (jest.requireMock('stripe'))();
    mockCheckoutRepository = {
      selectCheckoutSession: jest.fn(),
      updateCheckoutSession: jest.fn(),
      handleCheckoutCreationTransaction: jest.fn(),
      handleCompletionTransaction: jest.fn(),
      selectAvailableCheckoutSession: jest.fn(),
      accountAlreadyCheckoutedWithProductId: jest.fn(),
      accountAlreadyCheckoutedWithProductIds: jest.fn(),
      countBlockSessionWithProductId: jest.fn(),
    } as unknown as jest.Mocked<CheckoutRepository>;

    mockNftMintService = {
      mint: jest.fn(),
    } as unknown as jest.Mocked<NftMintService>;

    mockNftContractsRepository = {
      selectNftContractIdAndNftContractAbiUrl: jest.fn(),
    } as unknown as jest.Mocked<NftContractsRepository>;

    mockServiceInfoRepository = {
      getServiceById: jest.fn(),
    } as unknown as jest.Mocked<ServiceInfoRepository>;

    mockAccountRepository = {
      selectAccountById: jest.fn(),
      selectAccountByTokenBoundAddress: jest.fn(),
    } as unknown as jest.Mocked<AccountRepository>;

    mockProductRepository = {
      selectProductByServiceId: jest.fn(),
      selectProductByStripeId: jest.fn(),
    } as unknown as jest.Mocked<ProductRepository>;

    mockCustomFieldsRepository = {
      selectCustomFieldsByProductId: jest.fn(),
    } as unknown as jest.Mocked<CustomFieldsRepository>;

    mockMetadataService = {} as unknown as jest.Mocked<MetadataService>;

    productService = new ProductService(
      mockNftMintService,
      mockCheckoutRepository,
      mockNftContractsRepository,
      mockServiceInfoRepository,
      mockAccountRepository,
      mockProductRepository,
      mockCustomFieldsRepository,
      mockMetadataService,
    );
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });
  describe('method handleStripeEvents', () => {
    const event = {
      type: 'checkout.session.completed',
      data: { object: { id: 'session123' } },
      account: 'mockStripeAccount',
    };

    const sessionDetails = {
      id: 'session123',
      metadata: { service_id: 'service123', to_address: '0xAddress' },
      payment_intent: {
        status: 'succeeded',
      },
    };

    const mockServiceEntity: ServiceEntity = {
      service_id: 'service123',
      tenant_id: '',
      service_url: '',
      service_logo_image_url: '',
      market_cover_image_url: '',
      theme_primary_color_lowest: '',
      theme_primary_color_lower: '',
      theme_primary_color_higher: '',
      theme_primary_color_highest: '',
      membership_nft_contract_id: '',
      is_market_enabled: false,
      stripe_account_id: 'mockStripeAccount',
      line_channel_id: '',
      commission_rate: 0,
      modular_contract_id: '',
    };

    const lineItems = {
      data: [
        {
          price: {
            product: {
              metadata: {
                thumbnail_image_url: 'https://thumbnail_image_url',
                cover_image_url: 'https://cover_image_url',
                category_name: 'category_name',
                available_at: '2024-01-01T00:00:00Z',
                expire_at: '2024-01-01T00:00:00Z',
                nft_contract_id: '5bc4277a-87f6-4b56-9601-6cb76e907faa',
              },
            },
          },
        },
      ],
    };

    const nftContract = {
      nft_contract_id: 'contract123',
      service_id: 'service123',
      nft_contract_type_id: 'type123',
      nft_collection_name: 'Test Collection',
      nft_contract_address: '0x123456789',
      nft_contract_type_name: 'ERC721',
      nft_contract_type_detail: 'NFT Contract',
      nft_contract_abi: {},
      nft_contract_binary: 'binarydata',
      nft_type: NftType.CONTENT,
    };

    const mockProductData: ProductEntity = {
      service_id: 'service123',
      stripe_product_id: 'prod_RKpE4R0v574MmF',
      quantity: 100,
      perchase_limit_per_person: 10,
      is_phone_number_collection: false,
      is_billing_address_collection: false,
      is_shipping_address_collection: false,
      order_index: 1,
    };

    const mockMintData: TransactionData = {
      queueId: '',
      contractAddress: '0x123456789',
      tokenBoundAccountAddress: '0xAddress',
    };
    test('should process checkout.session.completed event successfully', async () => {
      const mockAccount: AccountEntity = {
        account_id: 'account123',
        token_bound_account_address: '0xAddress',
        service_id: 'service123',
        user_id: 'user123',
        membership_id: 456,
        display_name: 'Test User',
        profile_image_url: 'https://example.com/profile.png',
        status: 'active',
        transaction_id: '0x123456789abcdef123456789abcdef123456789a',
        queue_id: '0x123456789abcdef123456789abcdef123456789a',
        created_at: new Date('2020/01/01 00:00:00'),
        updated_at: new Date('2020/01/01 00:00:00'),
        last_login_at: new Date('2020/01/01 00:00:00'),
        membership_metadata_url: 'http://example.com/membership_metadata.json',
      };
      mockStripeConstructEvent.mockReturnValue(event);
      mockStripeRetrieveSession.mockReturnValue(sessionDetails);
      mockServiceInfoRepository.getServiceById.mockResolvedValue(mockServiceEntity);
      mockStripeListLineItems.mockReturnValue(lineItems);
      mockProductRepository.selectProductByStripeId.mockResolvedValue(mockProductData);
      mockCheckoutRepository.countBlockSessionWithProductId.mockResolvedValue(0);
      mockCheckoutRepository.handleCompletionTransaction.mockResolvedValue(true);
      mockNftContractsRepository.selectNftContractIdAndNftContractAbiUrl.mockResolvedValue(nftContract);
      mockAccountRepository.selectAccountByTokenBoundAddress.mockResolvedValue(mockAccount);
      mockNftMintService.mint.mockResolvedValue(mockMintData);

      await productService.handleStripeEvents(JSON.stringify(event), 'signature');
      expect(mockCheckoutRepository.updateCheckoutSession).toHaveBeenCalledTimes(0);
      // expect(mockNftMintService.mint).toHaveBeenCalledWith(
      //   'service123',
      //   'account123',
      //   TxType.MINT_REWARD,
      //   '0xAddress',
      //   '5bc4277a-87f6-4b56-9601-6cb76e907faa',
      //   NftType.CONTENT,
      //   undefined,
      // );
    });

    test('should handle unsupported Stripe event types gracefully', async () => {
      const event = {
        type: 'unknown.event.type',
        data: {},
      };

      mockStripeConstructEvent.mockReturnValue(event);

      await productService.handleStripeEvents(JSON.stringify(event), 'signature');

      expect(mockCheckoutRepository.handleCheckoutCreationTransaction).not.toHaveBeenCalled();
      expect(mockNftMintService.mint).not.toHaveBeenCalled();
    });

    test('should throw InternalServerError when session metadata is missing', async () => {
      const sessionDetails = { id: 'session123', metadata: null };

      mockStripeConstructEvent.mockReturnValue(event);
      mockStripeRetrieveSession.mockReturnValue(sessionDetails);

      mockStripeConstructEvent.mockReturnValue(event);
      mockStripeRetrieveSession.mockReturnValue(sessionDetails);

      await expect(productService.handleStripeEvents(JSON.stringify(event), 'signature')).rejects.toThrow(
        InternalServerError,
      );

      expect(mockCheckoutRepository.handleCheckoutCreationTransaction).not.toHaveBeenCalled();
    });
  });

  describe('method createCheckoutSession', () => {
    const mockService: ServiceEntity = {
      service_id: 'service123',
      tenant_id: 'tenant123',
      service_url: 'example.com',
      service_logo_image_url: 'https://example.com/logo.png',
      theme_primary_color_lowest: '#F0F8FF',
      theme_primary_color_lower: '#ADD8E6',
      theme_primary_color_higher: '#4682B4',
      theme_primary_color_highest: '#00008B',
      membership_nft_contract_id: 'contract123',
      is_market_enabled: true,
      stripe_account_id: 'acct_123',
      market_cover_image_url: 'https://example.com/market-cover.jpg',
      line_channel_id: 'line_channel_id',
      commission_rate: 0.1,
      modular_contract_id: 'acct_123',
    };

    const mockAccount: AccountEntity = {
      account_id: 'account123',
      service_id: 'service123',
      user_id: 'user123',
      membership_id: 456,
      display_name: 'Test User',
      profile_image_url: 'https://example.com/profile.png',
      token_bound_account_address: '0x123456789abcdef123456789abcdef123456789a',
      status: 'active',
      transaction_id: '0x123456789abcdef123456789abcdef123456789a',
      queue_id: '0x123456789abcdef123456789abcdef123456789a',
      created_at: new Date('2020/01/01 00:00:00'),
      updated_at: new Date('2020/01/01 00:00:00'),
      last_login_at: new Date('2020/01/01 00:00:00'),
      membership_metadata_url: 'http://example.com/membership_metadata.json',
    };

    const mockProduct = {
      priceId: 'price_1MoBy5LkdIwHu7ixZhnattbh',
      quantity: 10,
    };
    const mockProductData: ProductEntity = {
      service_id: 'service123',
      stripe_product_id: 'prod_RKpE4R0v574MmF',
      quantity: 100,
      perchase_limit_per_person: 10,
      is_phone_number_collection: false,
      is_billing_address_collection: false,
      is_shipping_address_collection: false,
      order_index: 1,
    };

    const mockCheckout = {
      id: 'session-id',
      client_secret: 'client-secret',
    };
    const mockPrice = {
      id: 'price_1QS9aGKSAJYIA7etaXculHk4',
      product: {
        id: 'prod_RKpE4R0v574MmF',
        metadata: {
          thumbnail_image_url: 'https://thumbnail_image_url',
          cover_image_url: 'https://cover_image_url',
          category_name: 'category_name',
          available_at: '2024-01-01T00:00:00Z',
          expire_at: '3000-01-01T00:00:00Z',
          nft_contract_id: '5bc4277a-87f6-4b56-9601-6cb76e907faa',
        },
      },
    };
    test('should create a checkout session and return the session URL', async () => {
      const mockSession: StripeCheckoutInfo = {
        sessionId: 'session-id',
        credential: 'client-secret',
        account: 'acct_123',
      };
      // Mock dependencies
      mockServiceInfoRepository.getServiceById.mockResolvedValue(mockService);
      mockAccountRepository.selectAccountById.mockResolvedValue(mockAccount);
      mockStripeRetrievePrice.mockResolvedValue(mockPrice);
      mockCheckoutRepository.selectAvailableCheckoutSession.mockResolvedValue(undefined);
      mockStripeCreateSession.mockResolvedValue(mockSession);
      mockProductRepository.selectProductByStripeId.mockResolvedValue(mockProductData);
      mockCheckoutRepository.countBlockSessionWithProductId.mockResolvedValue(0);
      mockCheckoutRepository.accountAlreadyCheckoutedWithProductId.mockResolvedValue(0);
      mockCheckoutRepository.handleCheckoutCreationTransaction.mockResolvedValue(true);
      mockCustomFieldsRepository.selectCustomFieldsByProductId.mockResolvedValue([]);
      mockStripeCreateSession.mockResolvedValue(mockCheckout);

      const session = await productService.createCheckoutSession('service123', mockAccount.account_id, mockProduct);

      // Assertions
      expect(mockServiceInfoRepository.getServiceById).toHaveBeenCalledWith('service123');
      expect(mockAccountRepository.selectAccountById).toHaveBeenCalledWith(mockAccount.account_id, 'service123');
      expect(session).toEqual(mockSession);
    });

    test('should handle Stripe session creation errors gracefully', async () => {
      mockServiceInfoRepository.getServiceById.mockResolvedValue(mockService);
      mockAccountRepository.selectAccountById.mockResolvedValue(mockAccount);
      mockStripeRetrievePrice.mockResolvedValue(mockPrice);
      mockCheckoutRepository.selectAvailableCheckoutSession.mockResolvedValue(undefined);
      mockStripeCreateSession.mockRejectedValue(new Error('Stripe API Error'));

      await expect(
        productService.createCheckoutSession('service123', mockAccount.account_id, {
          priceId: 'price_1MoBy5LkdIwHu7ixZhnattbh',
          quantity: 10,
        }),
      ).rejects.toThrow(Error);
    });
  });

  describe('ProductService - getProducts', () => {
    const accountId = 'accountId';
    test('should throw NotFoundError when service is not found or stripe_account_id is missing', async () => {
      const serviceId = 'nonexistentServiceId';

      mockServiceInfoRepository.getServiceById.mockReturnValue(Promise.resolve(undefined));

      await expect(productService.getProducts(serviceId, accountId, languageCode.JA)).rejects.toThrow(NotFoundError);

      expect(mockServiceInfoRepository.getServiceById).toHaveBeenCalledWith(serviceId);
    });

    test('should return formatted product list from Stripe and database', async () => {
      const serviceId = 'service123';
      const stripeAccountId = 'acct_123';
      const stripeProducts: Stripe.Response<Stripe.ApiList<Stripe.Product>> = {
        lastResponse: {
          headers: {},
          requestId: '',
          statusCode: 200,
        },
        object: 'list',
        has_more: false,
        url: 'https://url',
        data: [
          {
            object: 'product',
            id: 'prod_1',
            name: 'Product 1',
            images: ['image1.jpg'],
            metadata: {
              thumbnail_image_url: 'thumb1.jpg',
              cover_image_url: 'cover1.jpg',
              available_at: '2023-01-01T00:00:00Z',
              expire_at: '3000-12-31T00:00:00Z',
              category_name: 'category1',
              nft_contract_id: 'dc0ff9aa-c1cd-4da5-95ba-78695fadc99e',
            },
            default_price: {
              object: 'price',
              id: 'price_1',
              active: true,
              billing_scheme: 'per_unit',
              unit_amount: 1000,
              currency: 'usd',
            } as unknown as Stripe.Price,
            url: '/v1/products',
            active: false,
            created: 0,
            description: null,
            livemode: false,
            marketing_features: [],
            package_dimensions: null,
            shippable: null,
            tax_code: null,
            type: 'good',
            updated: 0,
          },
          {
            object: 'product',
            id: 'prod_2',
            name: 'Product 2',
            images: ['image2.jpg'],
            metadata: {
              thumbnail_image_url: 'thumb2.jpg',
              cover_image_url: 'cover1.jpg',
              available_at: '2023-01-01T00:00:00Z',
              expire_at: '3000-12-31T00:00:00Z',
              category_name: 'category1',
              nft_contract_id: '5189a390-c4e3-4b86-a4a7-1c08a7f08764',
            },
            default_price: {
              object: 'price',
              id: 'price_2',
              active: true,
              billing_scheme: 'per_unit',
              unit_amount: 1000,
              currency: 'usd',
            } as unknown as Stripe.Price,
            url: '/v1/products',
            active: false,
            created: 0,
            description: null,
            livemode: false,
            marketing_features: [],
            package_dimensions: null,
            shippable: null,
            tax_code: null,
            type: 'good',
            updated: 0,
          },
        ],
      };

      const service: ServiceEntity = {
        service_id: serviceId,
        tenant_id: 'tenantId',
        service_url: 'https://test-service.com',
        service_logo_image_url: 'https://marbullx.com/logo',
        theme_primary_color_lowest: '0xFFFFFFFFFFFF',
        theme_primary_color_lower: '0xFFFFFFFFFFFF',
        theme_primary_color_higher: '0xFFFFFFFFFFFF',
        theme_primary_color_highest: '0xFFFFFFFFFFFF',
        is_market_enabled: true,
        membership_nft_contract_id: '0x1234567890abcdef',
        stripe_account_id: stripeAccountId,
        market_cover_image_url: 'https://marbullx.com/cover',
        line_channel_id: 'line_channel_id',
        commission_rate: 0.1,
        modular_contract_id: '0x1234567890abcdef',
      };
      const productsFromDb: ProductWithTranslationsEntity[] = [
        {
          stripe_product_id: 'prod_1',
          service_id: 'service123',
          quantity: 10,
          perchase_limit_per_person: 1,
          is_phone_number_collection: false,
          is_billing_address_collection: false,
          is_shipping_address_collection: false,
          order_index: 1,
          description: 'Product 1 Description',
        },
        {
          stripe_product_id: 'prod_2',
          service_id: 'service123',
          quantity: 500,
          perchase_limit_per_person: 100,
          is_phone_number_collection: false,
          is_billing_address_collection: false,
          is_shipping_address_collection: false,
          order_index: 2,
          description: 'Product 2 Description',
        },
      ];

      const mockCheckoutState = new Map<
        string,
        {
          total_checkout_count: number;
          account_checkout_count: number;
        }
      >();
      mockCheckoutState.set('prod_1', { total_checkout_count: 10, account_checkout_count: 0 });
      mockCheckoutState.set('prod_2', { total_checkout_count: 100, account_checkout_count: 10 });

      const mockProductResponse: StripeProduct[] = [
        {
          productId: 'prod_1',
          productName: 'Product 1',
          imageUrl: 'image1.jpg',
          thumbnailImageUrl: 'thumb1.jpg',
          coverImageUrl: 'cover1.jpg',
          availableAt: '2023-01-01T00:00:00Z',
          expireAt: '3000-12-31T00:00:00Z',
          priceId: 'price_1',
          unitPrice: 1000,
          currency: 'usd',
          quantity: 0,
          category: 'category1',
          description: 'Product 1 Description',
          orderIndex: 1,
        },
        {
          productId: 'prod_2',
          productName: 'Product 2',
          imageUrl: 'image2.jpg',
          thumbnailImageUrl: 'thumb2.jpg',
          coverImageUrl: 'cover1.jpg',
          availableAt: '2023-01-01T00:00:00Z',
          expireAt: '3000-12-31T00:00:00Z',
          priceId: 'price_2',
          unitPrice: 1000,
          currency: 'usd',
          quantity: 400,
          category: 'category1',
          description: 'Product 2 Description',
          orderIndex: 2,
        },
      ];

      mockServiceInfoRepository.getServiceById.mockResolvedValue(service);
      mockStripeListProductItems.mockReturnValue(stripeProducts);

      mockProductRepository.selectProductByServiceId.mockResolvedValue(productsFromDb);
      mockCheckoutRepository.accountAlreadyCheckoutedWithProductIds.mockResolvedValue(mockCheckoutState);

      const result = await productService.getProducts(serviceId, accountId, languageCode.JA);

      expect(result).toEqual(mockProductResponse);

      expect(mockServiceInfoRepository.getServiceById).toHaveBeenCalledWith(serviceId);
      expect(mockStripe.products.list).toHaveBeenCalledWith(
        { limit: 100, expand: ['data.default_price'], active: true },
        { stripeAccount: stripeAccountId },
      );
      expect(mockProductRepository.selectProductByServiceId).toHaveBeenCalledWith(serviceId, languageCode.JA);
    });
  });
});
