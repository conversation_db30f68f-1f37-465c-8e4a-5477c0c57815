import { UserService } from '../../src/services/userService';
import { UserEntity } from '../../src/tables/userTable';
import { BaseError } from '../../src/errors/baseError';
import { InternalServerError } from '../../src/errors/internalServerError';
import { UserStatus } from '../../src/enum/userStatus';
import { NotFoundError } from '../../src/errors/notFoundError';
import { userEntity } from '../data/userTestData';
import { CreatedUser, CreateUser, RecoveryShare } from '../../src/dtos/users/schemas';
import {createMockUserRepository, createMockAccountRepository, createMockShareBackupRepository} from '../../src/utils/mockFactories';
import { ShareBackupEntity } from '../../src/tables/shareBackups';

jest.mock('../../src/repositories/userRepository');

describe('UserService', () => {
  let userService: UserService;
  const mockUserRepository = createMockUserRepository();
  const mockAccountRepository = createMockAccountRepository();
  const mockShareBackupRepository = createMockShareBackupRepository();

  beforeEach(() => {
    userService = new UserService(
      mockUserRepository,
      mockAccountRepository,
      mockShareBackupRepository,
    );
    jest.clearAllMocks();
  });

  describe('method createUser()', () => {
    test('should return user response on successful user creation', async () => {
      const userCreateRequest: CreateUser = {
        userId: 'newUserId',
        countryCode: 'JP+81',
        phoneNumber: '***********',
        mnemonicBackupKey: 'n9DK7fpekroiMiBsEkpZbPOJNlGZnLVKzi7AMQfkir9',
      };

      const newUserEntity: UserEntity = {
        user_id: 'newUserId',
        country_code: 'JP+81',
        phone_number: '***********',
        contract_account_address: '0xContractAccountAddressSample',
        status: UserStatus.ACTIVE,
        mnemonic_backup_key: 'someKey',
        created_at: new Date('2020/01/01 00:00:00'),
        updated_at: new Date('2020/01/01 00:00:00'),
      };

      mockUserRepository.insertUser.mockResolvedValueOnce(newUserEntity);

      const expectedResponse: CreatedUser = {
        userId: newUserEntity.user_id,
        phone: {
          countryCode: newUserEntity.country_code,
          phoneNumber: newUserEntity.phone_number,
        },
        contractAccountAddress: newUserEntity.contract_account_address,
        account: {
          serviceId: newUserEntity.mnemonic_backup_key,
        },
      };

      const result = await userService.createUser(userCreateRequest);

      expect(result).toEqual(expectedResponse);
      expect(mockUserRepository.insertUser).toHaveBeenCalledWith({
        ...userCreateRequest,
        status: UserStatus.ACTIVE,
      });
    });

    test('should throw BaseError when repository throws BaseError', async () => {
      const userCreateRequest: CreateUser = {
        userId: 'newUserId',
        countryCode: 'JP+81',
        phoneNumber: '***********',
        mnemonicBackupKey: 'n9DK7fpekroiMiBsEkpZbPOJNlGZnLVKzi7AMQfkir9',
      };

      const baseError = new BaseError(400, 'TEST_ERROR', 'Test Error');

      mockUserRepository.insertUser.mockRejectedValueOnce(baseError);

      await expect(userService.createUser(userCreateRequest)).rejects.toThrow(baseError);
    });

    test('should throw InternalServerError on unexpected errors', async () => {
      const userCreateRequest: CreateUser = {
        userId: 'newUserId',
        countryCode: 'JP+81',
        phoneNumber: '***********',
        mnemonicBackupKey: 'n9DK7fpekroiMiBsEkpZbPOJNlGZnLVKzi7AMQfkir9',
      };

      mockUserRepository.insertUser.mockRejectedValueOnce(new Error('Something unexpected'));

      await expect(userService.createUser(userCreateRequest)).rejects.toThrow(InternalServerError);
    });
  });

  describe('method getUser()', () => {
    test('success case - get user by ID', async () => {
      const userEntity: UserEntity = {
        user_id: 'newUserId',
        country_code: 'JP+81',
        phone_number: '***********',
        contract_account_address: '0xContractAccountAddressSample',
        mnemonic_backup_key: 'someEncryptedKey',
        status: UserStatus.ACTIVE,
        created_at: new Date('2020/01/01 00:00:00'),
        updated_at: new Date('2020/01/01 00:00:00'),
      };

      mockUserRepository.selectUserById.mockResolvedValue(userEntity);

      const result = await userService.getUser('newUserId');
      expect(result).toHaveProperty('userId', 'newUserId');
      expect(result).toHaveProperty('phone.countryCode', 'JP+81');
      expect(result).toHaveProperty('phone.phoneNumber', '***********');
      expect(result).toHaveProperty('contractAccountAddress', '0xContractAccountAddressSample');
    });

    test('failure case - user not found', async () => {
      mockUserRepository.selectUserById.mockResolvedValue(null);

      await expect(userService.getUser('nonexistentUserId')).rejects.toThrow(NotFoundError);
    });
  });

  describe('method deleteUser()', () => {
    test('success case - user is successfully deleted', async () => {
      mockUserRepository.selectUserById.mockResolvedValue({
        user_id: 'user123',
        country_code: 'JP',
        phone_number: '**********',
        contract_account_address: '0x123456789',
        mnemonic_backup_key: 'n9DK7fpekroiMiBsEkpZbPOJNlGZnLVKzi7AMQfkir9',
        status: UserStatus.ACTIVE,
        created_at: new Date('2020/01/01 00:00:00'),
        updated_at: new Date('2020/01/01 00:00:00'),
      });

      mockUserRepository.updateUserState.mockResolvedValueOnce(undefined);

      await expect(userService.deleteUser('user123')).resolves.toBeUndefined();
      expect(mockUserRepository.updateUserState).toHaveBeenCalledWith('user123', UserStatus.DELETED);
    });

    test('failure case - user not found', async () => {
      mockUserRepository.selectUserById.mockResolvedValue(null);

      await expect(userService.deleteUser('nonexistentUserId')).rejects.toThrow(NotFoundError);
    });
  });

  describe('method updateUserPhoneNumber()', () => {
    test('success case - phone number is successfully updated', async () => {
      mockUserRepository.selectUserById.mockResolvedValue({
        user_id: 'user123',
        country_code: 'JP',
        phone_number: '**********',
        contract_account_address: '0x123456789',
        mnemonic_backup_key: 'n9DK7fpekroiMiBsEkpZbPOJNlGZnLVKzi7AMQfkir9',
        status: UserStatus.ACTIVE,
        created_at: new Date('2020/01/01 00:00:00'),
        updated_at: new Date('2020/01/01 00:00:00'),
      });

      mockUserRepository.updateUserPhoneNumber.mockResolvedValueOnce(undefined);

      const phoneCountryCode = 'JP';
      const phone = '+**********';
      await expect(userService.updateUserPhoneNumber('user123', phoneCountryCode, phone)).resolves.toBeUndefined();
      expect(mockUserRepository.updateUserPhoneNumber).toHaveBeenCalledWith('user123', phoneCountryCode, phone);
    });

    test('failure case - user not found', async () => {
      mockUserRepository.selectUserById.mockResolvedValue(null);

      const phoneCountryCode = 'JP';
      const phone = '+**********';
      await expect(userService.updateUserPhoneNumber('nonexistentUserId', phoneCountryCode, phone)).rejects.toThrow(
        NotFoundError,
      );
    });
  });

  describe('method getBackupKey()', () => {
    test('success case - backup key is successfully retrieved', async () => {
      mockUserRepository.selectUserById.mockResolvedValue({
        user_id: 'user123',
        country_code: 'JP',
        phone_number: '**********',
        contract_account_address: '0x123456789',
        mnemonic_backup_key: 'n9DK7fpekroiMiBsEkpZbPOJNlGZnLVKzi7AMQfkir9',
        status: UserStatus.ACTIVE,
        created_at: new Date('2020/01/01 00:00:00'),
        updated_at: new Date('2020/01/01 00:00:00'),
      });

      const result = await userService.getBackupKey('user123');
      expect(result).toEqual({
        mnemonicBackupKey: 'n9DK7fpekroiMiBsEkpZbPOJNlGZnLVKzi7AMQfkir9',
        contractAccountAddress: '0x123456789',
      });
      expect(mockUserRepository.selectUserById).toHaveBeenCalledWith('user123');
    });

    test('failure case - user not found', async () => {
      mockUserRepository.selectUserById.mockResolvedValue(null);

      await expect(userService.getBackupKey('nonexistentUserId')).rejects.toThrow(NotFoundError);
    });
  });

  describe('method checkUser()', () => {
    test('return isUserExist = true when user exist and status = ACTIVE', async () => {
      mockUserRepository.checkUser.mockResolvedValue(userEntity);

      const result = await userService.checkUser('newUserId');
      expect(result).toHaveProperty('isUserExist', true);
    });

    test('return isUserExist = false when user not found', async () => {
      mockUserRepository.checkUser.mockResolvedValue(undefined);

      const result = await userService.checkUser('notExistUserId');
      expect(result).toHaveProperty('isUserExist', false);
    });
  });

  describe('method getRecoveryShare()', () => {
    test('success case - recovery share is successfully retrieved', async () => {
      const userId = 'user123';
      const mockUser: UserEntity = {
        user_id: userId,
        country_code: 'JP',
        phone_number: '**********',
        contract_account_address: '0x123456789',
        mnemonic_backup_key: 'someKey',
        status: UserStatus.ACTIVE,
        created_at: new Date('2020/01/01 00:00:00'),
        updated_at: new Date('2020/01/01 00:00:00'),
      };

      const mockShareBackup: ShareBackupEntity = {
        share_backup_id: 'backup123',
        user_id: userId,
        polynomial_id: '0x123polynomial',
        share_index: '0x456index',
        share: '0x789share',
        created_at: '2020-01-01T00:00:00.000Z',
        is_active: true,
      };

      mockUserRepository.selectUserById.mockResolvedValue(mockUser);
      mockShareBackupRepository.getShareBackupsByUserId.mockResolvedValue(mockShareBackup);

      const result = await userService.getRecoveryShare(userId);

      expect(result).toEqual({
        shareIndex: '0x456index',
        share: '0x789share',
        polynomialID: '0x123polynomial',
      });
      expect(mockUserRepository.selectUserById).toHaveBeenCalledWith(userId);
      expect(mockShareBackupRepository.getShareBackupsByUserId).toHaveBeenCalledWith(userId);
    });

    test('failure case - user not found', async () => {
      mockUserRepository.selectUserById.mockResolvedValue(null);

      await expect(userService.getRecoveryShare('nonexistentUserId')).rejects.toThrow(NotFoundError);
      expect(mockShareBackupRepository.getShareBackupsByUserId).not.toHaveBeenCalled();
    });

    test('failure case - share backup not found throws error', async () => {
      const userId = 'user123';
      const mockUser: UserEntity = {
        user_id: userId,
        country_code: 'JP',
        phone_number: '**********',
        contract_account_address: '0x123456789',
        mnemonic_backup_key: 'someKey',
        status: UserStatus.ACTIVE,
        created_at: new Date('2020/01/01 00:00:00'),
        updated_at: new Date('2020/01/01 00:00:00'),
      };

      mockUserRepository.selectUserById.mockResolvedValue(mockUser);
      mockShareBackupRepository.getShareBackupsByUserId.mockRejectedValue(new Error('Share backup not found'));

      await expect(userService.getRecoveryShare(userId)).rejects.toThrow('Share backup not found');
    });
  });

  describe('method storeRecoveryShare()', () => {
    test('success case - recovery share is successfully stored', async () => {
      const userId = 'user123';
      const mockUser: UserEntity = {
        user_id: userId,
        country_code: 'JP',
        phone_number: '**********',
        contract_account_address: '0x123456789',
        mnemonic_backup_key: 'someKey',
        status: UserStatus.ACTIVE,
        created_at: new Date('2020/01/01 00:00:00'),
        updated_at: new Date('2020/01/01 00:00:00'),
      };

      const recoveryShare: RecoveryShare = {
        share: '0x789share',
        shareIndex: '0x456index',
        polynomialID: '0x123polynomial',
      };

      mockUserRepository.selectUserById.mockResolvedValue(mockUser);
      mockShareBackupRepository.addNewShareBackup.mockResolvedValue({} as ShareBackupEntity);

      // Mock crypto.randomUUID
      const mockUUID = '550e8400-e29b-41d4-a716-************';
      jest.spyOn(crypto, 'randomUUID').mockReturnValue(mockUUID);

      await userService.storeRecoveryShare(userId, recoveryShare);

      expect(mockUserRepository.selectUserById).toHaveBeenCalledWith(userId);
      expect(mockShareBackupRepository.addNewShareBackup).toHaveBeenCalledWith({
        share_backup_id: mockUUID,
        user_id: userId,
        share: recoveryShare.share,
        polynomial_id: recoveryShare.polynomialID,
        share_index: recoveryShare.shareIndex,
        created_at: expect.any(String),
        is_active: true,
      });

      // Verify the created_at is in ISO format
      const callArgs = mockShareBackupRepository.addNewShareBackup.mock.calls[0][0];
      expect(new Date(callArgs.created_at).toISOString()).toBe(callArgs.created_at);
    });

    test('failure case - user not found', async () => {
      const userId = 'nonexistentUserId';
      const recoveryShare: RecoveryShare = {
        share: '0x789share',
        shareIndex: '0x456index',
        polynomialID: '0x123polynomial',
      };

      mockUserRepository.selectUserById.mockResolvedValue(null);

      await expect(userService.storeRecoveryShare(userId, recoveryShare)).rejects.toThrow(NotFoundError);
      expect(mockShareBackupRepository.addNewShareBackup).not.toHaveBeenCalled();
    });

    test('failure case - repository throws error', async () => {
      const userId = 'user123';
      const mockUser: UserEntity = {
        user_id: userId,
        country_code: 'JP',
        phone_number: '**********',
        contract_account_address: '0x123456789',
        mnemonic_backup_key: 'someKey',
        status: UserStatus.ACTIVE,
        created_at: new Date('2020/01/01 00:00:00'),
        updated_at: new Date('2020/01/01 00:00:00'),
      };

      const recoveryShare: RecoveryShare = {
        share: '0x789share',
        shareIndex: '0x456index',
        polynomialID: '0x123polynomial',
      };

      mockUserRepository.selectUserById.mockResolvedValue(mockUser);
      mockShareBackupRepository.addNewShareBackup.mockRejectedValue(new Error('Database error'));

      await expect(userService.storeRecoveryShare(userId, recoveryShare)).rejects.toThrow('Database error');
    });
  });
});
