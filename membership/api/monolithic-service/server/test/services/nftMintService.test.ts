/* eslint-disable @typescript-eslint/no-explicit-any */
import { TransactionResponse } from 'ethers';
import { v4 as uuidv4 } from 'uuid';
import { AlchemyComponent } from '../../src/components/alchemyComponent';
import { FirebaseComponent } from '../../src/components/firebaseComponent';
import { TransactionComponent } from '../../src/components/transactionComponent';
import { db } from '../../src/db/database';
import { TransactionData } from '../../src/dtos/nfts/schemas';
import { AccountStatus } from '../../src/enum/accoutStatus';
import { NftType } from '../../src/enum/nftType';
import { TransactionQueueStatus } from '../../src/enum/transactionQueueStatus';
import { TxType } from '../../src/enum/txType';
import { VaultTransactionStatus } from '../../src/enum/vaultTransactionStatus';
import { NotFoundError } from '../../src/errors/notFoundError';
import { AccountRepository } from '../../src/repositories/accountRepository';
import { AttemptTransactionsRepository } from '../../src/repositories/attemptTransactionsRepository';
import { DeliveryNftsFirestoreRepository } from '../../src/repositories/deliveryNftsFirestoreRepository';
import { NftBaseMetadatasRepository } from '../../src/repositories/nftBaseMetadatasRepository';
import { NftContract, NftContractsRepository } from '../../src/repositories/nftContractsRepository';
import { NftContractTypesRepository } from '../../src/repositories/nftContractTypesRepository';
import { NftMetadatasRepository } from '../../src/repositories/nftMetadatasRepository';
import { NftsFirestoreRepository } from '../../src/repositories/nftsFirestoreRepository';
import { ServiceInfoRepository } from '../../src/repositories/serviceInfoRepository';
import { TokenBoundAccountImplementationRepository } from '../../src/repositories/tokenBoundAccountImplementationsRepository';
import { TokenBoundAccountRegistryAddressRepository } from '../../src/repositories/tokenBoundAccountRegistryAddressRepository';
import { TransactionQueuesRepository } from '../../src/repositories/transactionQueuesRepository';
import { TransactionsRepository } from '../../src/repositories/transactionsRepository';
import { UserRepository } from '../../src/repositories/userRepository';
import { VaultKeyRepository } from '../../src/repositories/vaultKeyRepository';
import { BulkMintService } from '../../src/services/bulkMintService';
import { MetadataService } from '../../src/services/metadataService';
import { NftMintService } from '../../src/services/nftMintService';
import { TransactionService } from '../../src/services/transactionService';
import { NftTransactionUpdateService } from '../../src/services/transactionUpdateService';
import { WebhookService } from '../../src/services/webhookService';
import { AccountEntity } from '../../src/tables/accountTable';
import { ServiceEntity } from '../../src/tables/servicesTable';
import { createMockTransactionComponent, createMockViemComponent } from '../../src/utils/mockFactories';

jest.mock('ethers', () => {
  const actualEthers = jest.requireActual('ethers');
  return {
    ...actualEthers,
    providers: {
      JsonRpcProvider: jest.fn(() => {
        return {
          getTransactionCount: jest.fn().mockResolvedValue(0),
          estimateGas: jest.fn().mockResolvedValue({}),
          getGasPrice: jest.fn().mockResolvedValue({}),
        };
      }),
    },
    Interface: jest.fn().mockImplementation((abi) => {
      return new actualEthers.Interface(abi);
    }),
  };
});

jest.mock('../../src/repositories/nftContractsRepository');
jest.mock('../../src/repositories/nftBaseMetadatasRepository');
jest.mock('../../src/repositories/nftMetadatasRepository');
jest.mock('../../src/repositories/vaultKeyRepository');
jest.mock('../../src/repositories/vaultTransactionQueuesRepository');
jest.mock('../../src/repositories/serviceInfoRepository');
jest.mock('../../src/services/transactionService');
jest.mock('../../src/services/webhookService');
jest.mock('../../src/repositories/accountRepository');
jest.mock('uuid');
jest.mock('../../src/db/database', () => ({
  db: {
    transaction: jest.fn(() => ({
      execute: jest.fn(),
    })),
    execute: jest.fn(),
  },
}));
jest.mock('../../src/components/cloudTaskComponent', () => ({
  CloudTaskComponent: jest.fn().mockImplementation(() => ({
    enqueueMintTask: jest.fn().mockResolvedValue(undefined),
  })),
}));

describe('NftMintService', () => {
  let nftMintService: NftMintService;
  let mockNftContractsRepository: jest.Mocked<NftContractsRepository>;
  let mockNftBaseMetadatasRepository: jest.Mocked<NftBaseMetadatasRepository>;
  let mockNftMetadatasRepository: jest.Mocked<NftMetadatasRepository>;
  let mockVaultKeyRepository: jest.Mocked<VaultKeyRepository>;
  let mockTransactionComponent: jest.Mocked<TransactionComponent>;
  let mockTransactionService: jest.Mocked<TransactionService>;
  let mockWebhookService: jest.Mocked<WebhookService>;
  let mockServiceInfoRepository: jest.Mocked<ServiceInfoRepository>;
  let mockAccountRepository: jest.Mocked<AccountRepository>;
  let mockNftContractTypesRepository: jest.Mocked<NftContractTypesRepository>;
  let mockTokenBoundAccountRegistryAddressRepository: jest.Mocked<TokenBoundAccountRegistryAddressRepository>;
  let mockMetadataService: jest.Mocked<MetadataService>;
  let mockNftTransactionUpdateService: jest.Mocked<NftTransactionUpdateService>;
  let mockFirebaseComponent: jest.Mocked<FirebaseComponent>;
  let mockAlchemyComponent: jest.Mocked<AlchemyComponent>;
  let mockAttemptTransactionsRepository: jest.Mocked<AttemptTransactionsRepository>;
  let mockTransactionsRepository: jest.Mocked<TransactionsRepository>;
  let mockTransactionQueuesRepository: jest.Mocked<TransactionQueuesRepository>;
  let mockBulkMintService: jest.Mocked<BulkMintService>;
  let mockUserRepository: jest.Mocked<UserRepository>;
  let mockNftMintService: jest.Mocked<NftMintService>;
  let mockTokenBoundAccountImplementationRepository: jest.Mocked<TokenBoundAccountImplementationRepository>;
  let mockDeliveryNftsFirestoreRepository: jest.Mocked<DeliveryNftsFirestoreRepository>;
  let mockNftsFirestoreRepository: jest.Mocked<NftsFirestoreRepository>;
  beforeEach(() => {
    process.env.JSON_RPC_URL = 'http://localhost:8545'; // Use a valid URL format
    process.env.ALCHEMY_API_KEY = 'xxxxxsss';
    process.env.ALCHEMY_CHAIN_NAME = 'polygon-amoy';
    process.env.GAS_LIMIT_MULTIPLIER = '100';
    process.env.BASE_MAX_FEE_PER_GAS = '100';
    process.env.MAX_FEE_PER_GAS_MULTIPLIER = '100';
    process.env.MAX_PRIORITY_FEE_PER_GAS_MULTIPLIER = '100';
    process.env.GCP_PROJECT_ID = 'sample_gcp_project_id'; // Added GCP_PROJECT_ID
    process.env.GCS_BUCKET_NAME = 'mock-bucket-name'; // Added GCS_BUCKET_NAME
    process.env.ALCHEMY_ADDRESS_ACTIVITY_WEBHOOK_ID = 'mockAddressActivityWebhookId'; // Added
    process.env.ALCHEMY_NFT_ACTIVITY_WEBHOOK_ID = 'mockNftActivityWebhookId'; // Added
    process.env.ALCHEMY_ADDRESS_ACTIVITY_WEBHOOK_SIGNING_KEY = 'mockSigningKey'; // Added
    process.env.ALCHEMY_NFT_ACTIVITY_WEBHOOK_SIGNING_KEY = 'mockSigningKey'; // Added
    process.env.ALCHEMY_AUTH_TOKEN = 'mockAuthToken'; // Added

    mockNftContractsRepository = new NftContractsRepository() as jest.Mocked<NftContractsRepository>;
    mockNftBaseMetadatasRepository = new NftBaseMetadatasRepository() as jest.Mocked<NftBaseMetadatasRepository>;
    mockNftMetadatasRepository = new NftMetadatasRepository() as jest.Mocked<NftMetadatasRepository>;
    mockVaultKeyRepository = new VaultKeyRepository() as jest.Mocked<VaultKeyRepository>;
    mockTransactionComponent = new TransactionComponent(mockVaultKeyRepository) as jest.Mocked<TransactionComponent>;
    mockAttemptTransactionsRepository =
      new AttemptTransactionsRepository() as jest.Mocked<AttemptTransactionsRepository>;
    mockTransactionQueuesRepository = new TransactionQueuesRepository() as jest.Mocked<TransactionQueuesRepository>;
    mockTransactionsRepository = new TransactionsRepository() as jest.Mocked<TransactionsRepository>;
    mockTransactionService = new TransactionService(
      mockTransactionComponent,
      mockAttemptTransactionsRepository,
      mockTransactionsRepository,
      mockTransactionQueuesRepository,
      mockServiceInfoRepository,
      mockNftContractsRepository,
    ) as jest.Mocked<TransactionService>;
    mockAccountRepository = new AccountRepository() as jest.Mocked<AccountRepository>;
    mockNftContractTypesRepository = new NftContractTypesRepository() as jest.Mocked<NftContractTypesRepository>;
    mockTokenBoundAccountRegistryAddressRepository =
      new TokenBoundAccountRegistryAddressRepository() as jest.Mocked<TokenBoundAccountRegistryAddressRepository>;
    mockMetadataService = new MetadataService(
      mockNftMetadatasRepository,
      mockNftBaseMetadatasRepository,
      mockNftContractTypesRepository,
    ) as jest.Mocked<MetadataService>;
    mockFirebaseComponent = new FirebaseComponent() as jest.Mocked<FirebaseComponent>;
    mockDeliveryNftsFirestoreRepository = new DeliveryNftsFirestoreRepository(
      mockFirebaseComponent,
    ) as jest.Mocked<DeliveryNftsFirestoreRepository>;
    mockAlchemyComponent = new AlchemyComponent() as jest.Mocked<AlchemyComponent>;
    mockNftMintService = new NftMintService(
      mockNftContractsRepository,
      mockTransactionQueuesRepository,
      mockVaultKeyRepository,
      mockServiceInfoRepository,
      mockDeliveryNftsFirestoreRepository,
    ) as jest.Mocked<NftMintService>;
    mockTokenBoundAccountImplementationRepository =
      new TokenBoundAccountImplementationRepository() as jest.Mocked<TokenBoundAccountImplementationRepository>;

    mockBulkMintService = new BulkMintService(
      mockTransactionService,
      mockNftMintService,
      mockNftContractsRepository,
      mockTransactionQueuesRepository,
      mockTransactionsRepository,
      mockVaultKeyRepository,
      mockServiceInfoRepository,
      mockTokenBoundAccountImplementationRepository,
      mockTokenBoundAccountRegistryAddressRepository,
      mockAccountRepository,
      mockDeliveryNftsFirestoreRepository,
      mockNftBaseMetadatasRepository,
    ) as jest.Mocked<BulkMintService>;
    mockUserRepository = new UserRepository() as jest.Mocked<UserRepository>;
    mockNftsFirestoreRepository = new NftsFirestoreRepository(
      mockFirebaseComponent,
    ) as jest.Mocked<NftsFirestoreRepository>;
    mockNftTransactionUpdateService = new NftTransactionUpdateService(
      mockAccountRepository,
      mockNftsFirestoreRepository,
      mockNftContractsRepository,
      mockNftBaseMetadatasRepository,
      mockDeliveryNftsFirestoreRepository,
      mockServiceInfoRepository,
      createMockViemComponent(),
      createMockTransactionComponent(),
      mockTransactionsRepository,
      mockTransactionQueuesRepository,
    ) as jest.Mocked<NftTransactionUpdateService>;

    mockWebhookService = new WebhookService(
      mockTransactionsRepository,
      mockTransactionQueuesRepository,
      mockTokenBoundAccountRegistryAddressRepository,
      mockMetadataService,
      mockNftTransactionUpdateService,
      mockAlchemyComponent,
      mockNftContractsRepository,
      mockBulkMintService,
      mockServiceInfoRepository,
      mockUserRepository,
    ) as jest.Mocked<WebhookService>;
    mockServiceInfoRepository = new ServiceInfoRepository() as jest.Mocked<ServiceInfoRepository>;

    nftMintService = new NftMintService(
      mockNftContractsRepository,
      mockTransactionQueuesRepository,
      mockVaultKeyRepository,
      mockServiceInfoRepository,
      mockDeliveryNftsFirestoreRepository,
    ) as jest.Mocked<NftMintService>;

    const mockTrx = {};

    (db.transaction as jest.Mock).mockImplementation(() => {
      return {
        execute: async (callback: (trx: typeof mockTrx) => Promise<any>) => {
          return callback(mockTrx);
        },
      };
    });
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('mint', () => {
    test('should mint NFT successfully for COUPON type', async () => {
      const mockServiceId = 'sample-service-id';
      const mockAccountId = 'sample-account-id';
      const mockToAddress = '0xf39fd6e51aad88f6f4ce6ab8827279cfffb92266';
      const mockNftContractId = 'contractId';
      const mockNftTokenId = 1;
      const mockNftType = NftType.COUPON;
      const mockContractDetails: NftContract = {
        nft_contract_id: 'mockContractId',
        service_id: 'mockServiceId',
        nft_contract_type_id: 'mockTypeId',
        nft_contract_address: '******************************************',
        nft_contract_implementation_address: '0x32Be343B94f860124dC4fEe278FDCBD38C102D89',
        nft_contract_abi: [
          {
            constant: false,
            inputs: [
              { name: 'to', type: 'address' },
              { name: 'id', type: 'uint256' },
              { name: 'amount', type: 'uint256' },
            ],
            name: 'additinalMint',
            outputs: [],
            payable: false,
            stateMutability: 'nonpayable',
            type: 'function',
          },
        ] as unknown as object,
        next_token_id: undefined,
      };
      const mockVaultKey = {
        vault_key_id: 'mockVaultKeyId',
        tenant_id: 'mockTenantId',
        key_ring_project: 'mockKeyRingProject',
        key_ring_location: 'mockKeyRingLocation',
        key_ring_name: 'mockKeyRingName',
        key_version: 'mockKeyVersion',
        vault_wallet_address: '******************************************',
        nonce: 0,
      };
      const mockPrepareTransaction = {
        from: '******************************************',
        to: '******************************************',
        gasLimit: BigInt(21000),
        gasPrice: BigInt(1000000000),
        data: 'mintData',
        nonce: 0,
        retries: 0,
      };
      const mockVaultTransactionQueue = {
        transaction_id: 'txnId',
        service_id: 'mockServiceId',
        hex_encoded_transaction_data: '0x789',
        from_address: '******************************************',
        to_address: '******************************************',
        status: VaultTransactionStatus.EXECUTED,
        signature: undefined,
        tx_hash: 'txnHash',
        retry_count: 0,
        nonce: 0,
        nft_type: mockNftType,
        tx_type: TxType.MINT_MEMBERSHIP,
        created_date: new Date(),
        token_id: null,
      };
      const mockTransactionQueue = {
        queue_id: 'txnId',
        service_id: 'mockServiceId',
        from_address: '******************************************',
        to_address: '******************************************',
        status: TransactionQueueStatus.PROCESSING,
        nft_type: mockNftType,
        tx_type: TxType.MINT_MEMBERSHIP,
        nft_contract_address: '0x66f820a414680B5bcda5eECA5dea238543F42055',
        created_date: new Date(),
        token_id: undefined,
      };
      const mockTransactionResponse = {
        hash: '0x123',
        signature:
          '0xf86c808504a817c800825208945aae4f4c86c3a7f0eaa2c399c6e37d5e3fa12e418872386f26fc100008025a05e6d3f9a3b2aef203ca4f547ee2b362e264489d39f5bffbc9f2e5834b1d24b2ea0741a4d155774a92f71882d58b3a3941d7f8c24d11e2ea10fd419359f91b1e7ae',
        confirmations: jest.fn().mockResolvedValue(0),
        from: '******************************************',
        nonce: 1,
        gasLimit: BigInt(21000),
        gasPrice: BigInt(1000000000),
        data: '0x',
        value: BigInt(0),
        chainId: BigInt(1),
        blockNumber: null,
        blockHash: null,
        index: 0,
        type: 0,
        wait: jest.fn().mockResolvedValue({
          to: '******************************************',
          from: '0x742d35Cc6634C0532925a3b844Bc454e4438f44e',
          contractAddress: '******************************************',
          transactionIndex: 1,
          gasUsed: BigInt(21000),
          blockHash: '0x999',
          blockNumber: 1,
          confirmations: 1,
          status: 1,
          hash: '0x123',
        }),
      };

      const mockServiceInfo: ServiceEntity = {
        service_id: 'mockServiceId',
        tenant_id: 'mockTenantId',
        service_url: 'mockServiceUrl',
        service_logo_image_url: 'mockLogoUrl',
        market_cover_image_url: 'https://example.com/market-cover.jpg',
        theme_primary_color_lowest: 'mockLowestColor',
        theme_primary_color_lower: 'mockLowerColor',
        theme_primary_color_higher: 'mockHigherColor',
        theme_primary_color_highest: 'mockHighestColor',
        membership_nft_contract_id: 'mockMembershipNftContractId',
        is_market_enabled: true,
        stripe_account_id: 'stripe_account_id',
        line_channel_id: 'line_channel_id',
        commission_rate: 0.1,
        modular_contract_id: 'modular_contract_id',
      };
      const mockAccount: AccountEntity = {
        account_id: 'mock-uuid',
        service_id: 'mockServiceId',
        user_id: 'mock-uuid',
        membership_id: 0,
        display_name: 'mock-name',
        profile_image_url: 'mock-image-url',
        token_bound_account_address: '0xBC4CA0EdA7647A8aB7C2061c2E118A18a936f13D',
        status: AccountStatus.ACTIVE,
        transaction_id: 'mock-uuid',
        queue_id: 'mock-uuid',
        created_at: new Date(),
        updated_at: new Date(),
        last_login_at: new Date('2020/01/01 00:00:00'),
        membership_metadata_url: 'http://example.com/membership_metadata.json',
      };
      mockAccountRepository.selectAccountByTokenBoundAddress.mockResolvedValue(mockAccount);
      mockNftContractsRepository.selectNftContractById.mockResolvedValue(mockContractDetails);
      mockNftContractsRepository.getNftContractsByIdWithLock.mockResolvedValue(mockContractDetails);
      mockServiceInfoRepository.getServiceById.mockResolvedValue(mockServiceInfo);
      mockVaultKeyRepository.getVaultKeyIdWithLock.mockResolvedValue(mockVaultKey);
      mockVaultKeyRepository.getVaultKeyId.mockResolvedValue(mockVaultKey);
      mockTransactionService.prepareTransaction.mockResolvedValue(mockPrepareTransaction);
      mockTransactionService.sendRawTransaction.mockResolvedValue(
        mockTransactionResponse as unknown as TransactionResponse,
      );
      mockWebhookService.addNftContractAddressToWebhook.mockResolvedValue();
      (uuidv4 as jest.Mock).mockReturnValue(mockVaultTransactionQueue.transaction_id);
      mockTransactionQueuesRepository.insertQueue = jest.fn();
      mockTransactionQueuesRepository.insertQueue.mockResolvedValue(mockTransactionQueue);
      mockFirebaseComponent.createDocument = jest.fn().mockResolvedValue({
        id: 'mockDocId',
        set: jest.fn(),
      } as any);

      const result = await nftMintService.mint(
        mockServiceId,
        mockAccountId,
        TxType.MINT_REWARD,
        mockToAddress,
        mockNftContractId,
        mockNftType,
        mockNftTokenId,
      );
      const expectedResult: TransactionData = {
        queueId: String(mockVaultTransactionQueue.transaction_id),
        contractAddress: '******************************************',
        tokenBoundAccountAddress: mockToAddress,
      };
      expect(result).toEqual(expectedResult);
    });

    test('should mint NFT successfully for non-COUPON type', async () => {
      const mockServiceId = 'sample-service-id';
      const mockAccountId = 'sample-account-id';
      const mockToAddress = '0xf39fd6e51aad88f6f4ce6ab8827279cfffb92266';
      const mockNftContractId = 'contractId';
      const mockNftType = NftType.MEMBERSHIP;
      const mockContractDetails: NftContract = {
        nft_contract_id: 'mockContractId',
        service_id: 'mockServiceId',
        nft_contract_type_id: 'mockTypeId',
        nft_contract_address: '******************************************',
        nft_contract_implementation_address: '0x32Be343B94f860124dC4fEe278FDCBD38C102D89',
        nft_contract_abi: [
          {
            constant: false,
            inputs: [{ name: 'to', type: 'address' }],
            name: 'safeMint',
            outputs: [],
            payable: false,
            stateMutability: 'nonpayable',
            type: 'function',
          },
        ] as unknown as object,
        next_token_id: 0,
      };
      const mockVaultKey = {
        vault_key_id: 'mockVaultKeyId',
        tenant_id: 'mockTenantId',
        key_ring_project: 'mockKeyRingProject',
        key_ring_location: 'mockKeyRingLocation',
        key_ring_name: 'mockKeyRingName',
        key_version: 'mockKeyVersion',
        vault_wallet_address: '******************************************',
        nonce: 0,
      };
      const mockPrepareTransaction = {
        from: '******************************************',
        to: '******************************************',
        gasLimit: BigInt(21000),
        gasPrice: BigInt(1000000000),
        data: 'mintData',
        nonce: 0,
        retries: 0,
      };
      const mockTransactionResponse = {
        hash: '0x123',
        signature:
          '0xf86c808504a817c800825208945aae4f4c86c3a7f0eaa2c399c6e37d5e3fa12e418872386f26fc100008025a05e6d3f9a3b2aef203ca4f547ee2b362e264489d39f5bffbc9f2e5834b1d24b2ea0741a4d155774a92f71882d58b3a3941d7f8c24d11e2ea10fd419359f91b1e7ae',
        confirmations: jest.fn().mockResolvedValue(0),
        from: '******************************************',
        nonce: 1,
        gasLimit: BigInt(21000),
        gasPrice: BigInt(1000000000),
        data: '0x',
        value: BigInt(0),
        chainId: BigInt(1),
        blockNumber: null,
        blockHash: null,
        index: 0,
        type: 0,
        wait: jest.fn().mockResolvedValue({
          to: '******************************************',
          from: '0x742d35Cc6634C0532925a3b844Bc454e4438f44e',
          contractAddress: '******************************************',
          transactionIndex: 1,
          gasUsed: BigInt(21000),
          blockHash: '0x999',
          blockNumber: 1,
          confirmations: 1,
          status: 1,
          hash: '0x123',
        }),
      };

      const mockServiceInfo: ServiceEntity = {
        service_id: 'mockServiceId',
        tenant_id: 'mockTenantId',
        service_url: 'mockServiceUrl',
        service_logo_image_url: 'mockLogoUrl',
        market_cover_image_url: 'mockMarketCoverUrl', // Added missing property
        theme_primary_color_lowest: 'mockLowestColor',
        theme_primary_color_lower: 'mockLowerColor',
        theme_primary_color_higher: 'mockHigherColor',
        theme_primary_color_highest: 'mockHighestColor',
        membership_nft_contract_id: 'mockMembershipNftContractId',
        is_market_enabled: true,
        stripe_account_id: 'stripe_account_id',
        line_channel_id: 'line_channel_id',
        commission_rate: 0.1,
        modular_contract_id: 'modular_contract_id',
      };
      const mockTransactionQueue = {
        queue_id: 'txnId',
        service_id: 'mockServiceId',
        from_address: '******************************************',
        to_address: '******************************************',
        status: TransactionQueueStatus.PROCESSING,
        nft_type: mockNftType,
        tx_type: TxType.MINT_MEMBERSHIP,
        nft_contract_address: '0x66f820a414680B5bcda5eECA5dea238543F42055',
        created_date: new Date(),
        token_id: undefined,
      };
      mockNftContractsRepository.selectNftContractById.mockResolvedValue(mockContractDetails);
      mockNftContractsRepository.getNftContractsByIdWithLock.mockResolvedValue(mockContractDetails);
      mockServiceInfoRepository.getServiceById.mockResolvedValue(mockServiceInfo);
      mockVaultKeyRepository.getVaultKeyIdWithLock.mockResolvedValue(mockVaultKey);
      mockVaultKeyRepository.getVaultKeyId.mockResolvedValue(mockVaultKey);
      mockTransactionService.prepareTransaction.mockResolvedValue(mockPrepareTransaction);
      mockTransactionService.callContractFunction.mockResolvedValue(
        mockTransactionResponse as unknown as TransactionResponse,
      );
      mockWebhookService.addNftContractAddressToWebhook.mockResolvedValue();
      mockTransactionQueuesRepository.insertQueue = jest.fn();
      mockTransactionQueuesRepository.insertQueue.mockResolvedValue(mockTransactionQueue);

      // Mock createDocument to avoid real Firestore call causing error
      mockFirebaseComponent.createDocument = jest.fn().mockResolvedValue({
        id: 'mockDocId',
        set: jest.fn(),
      } as any);

      const result = await nftMintService.mint(
        mockServiceId,
        mockAccountId,
        TxType.MINT_REWARD,
        mockToAddress,
        mockNftContractId,
        mockNftType,
      );
      const expectedResult: TransactionData = {
        queueId: String('txnId'),
        contractAddress: '******************************************',
        tokenBoundAccountAddress: mockToAddress,
      };
      expect(result).toEqual(expectedResult);
    });

    test('should throw NotFoundError if vault key is not found', async () => {
      const mockServiceId = 'sample-service-id';
      const mockAccountId = 'sample-account-id';
      const mockToAddress = '0xf39fd6e51aad88f6f4ce6ab8827279cfffb92266';
      const mockNftContractId = 'contractId';
      const mockNftTokenId = undefined;
      const mockNftType = NftType.CONTENT;

      mockNftContractsRepository.selectNftContractById.mockResolvedValue({
        nft_contract_id: 'mockContractId',
        service_id: 'mockServiceId',
        nft_contract_type_id: 'mockTypeId',
        nft_collection_name: 'mockCollectionName',
        nft_contract_address: '0x456',
        nft_contract_abi: [
          {
            constant: false,
            inputs: [{ name: 'to', type: 'address' }],
            name: 'safeMint',
            outputs: [],
            payable: false,
            stateMutability: 'nonpayable',
            type: 'function',
          },
        ] as unknown as object,
      });
      mockServiceInfoRepository.getServiceById.mockResolvedValue({
        service_id: 'mockServiceId',
        tenant_id: 'mockTenantId',
        service_url: 'mockServiceUrl',
        service_logo_image_url: 'mockLogoUrl',
        market_cover_image_url: 'mockMarketCoverUrl',
        theme_primary_color_lowest: 'mockLowestColor',
        theme_primary_color_lower: 'mockLowerColor',
        theme_primary_color_higher: 'mockHigherColor',
        theme_primary_color_highest: 'mockHighestColor',
        membership_nft_contract_id: 'mockMembershipNftContractId',
        is_market_enabled: true,
        stripe_account_id: 'stripe_account_id',
        line_channel_id: 'line_channel_id',
        commission_rate: 0.1,
        modular_contract_id: 'modular_contract_id',
      });
      const mockTransactionQueue = {
        queue_id: 'txnId',
        service_id: 'mockServiceId',
        from_address: '******************************************',
        to_address: '******************************************',
        status: TransactionQueueStatus.PROCESSING,
        nft_type: mockNftType,
        tx_type: TxType.MINT_MEMBERSHIP,
        nft_contract_address: '0x66f820a414680B5bcda5eECA5dea238543F42055',
        created_date: new Date(),
        token_id: undefined,
      };
      mockVaultKeyRepository.getVaultKeyIdWithLock.mockResolvedValue(undefined);
      mockVaultKeyRepository.getVaultKeyId.mockResolvedValue(undefined);
      mockTransactionQueuesRepository.insertQueue = jest.fn();
      mockTransactionQueuesRepository.insertQueue.mockResolvedValue(mockTransactionQueue);

      await expect(
        nftMintService.mint(
          mockServiceId,
          mockAccountId,
          TxType.MINT_REWARD,
          mockToAddress,
          mockNftContractId,
          mockNftType,
          mockNftTokenId,
        ),
      ).rejects.toThrow(NotFoundError);
    });
  });
});
