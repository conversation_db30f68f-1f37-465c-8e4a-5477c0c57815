// geofenceService.test.ts
import { GeofenceService } from '../../src/services/geofenceService';
import { GeofenceRepository } from '../../src/repositories/geofenceRepository';
import { ValidationError } from '../../src/errors/validationError';
import { NotFoundError } from '../../src/errors/notFoundError';
import { GeofencesTable } from '../../src/tables/geofencesTable';
import {
  LocationGeofence,
  LocationGeofenceId,
  GetLocationGeofences,
  GeofenceType,
} from '../../src/dtos/locations/schemas';
import { v4 as uuidv4 } from 'uuid';

jest.mock('../../src/repositories/geofenceRepository');
jest.mock('uuid', () => ({
  v4: jest.fn(() => 'mocked-uuid'),
}));

describe('GeofenceService', () => {
  let geofenceService: GeofenceService;
  let mockGeofenceRepository: jest.Mocked<GeofenceRepository>;


  beforeEach(() => {
    mockGeofenceRepository = new GeofenceRepository() as jest.Mocked<GeofenceRepository>;
    geofenceService = new GeofenceService(mockGeofenceRepository);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createGeofence', () => {
    test('should create a circle geofence successfully', async () => {
      const mockServiceId = 'service-123';
      const mockGeofenceId = 'geofence-001';
      const mockRequest: LocationGeofence = {
        name: 'Tokyo Station Area',
        geofence: {
          geofenceType: GeofenceType.CIRCLE,
          center: {
            latitude: 35.6812,
            longitude: 139.7671,
          },
          radiusMeters: 50,
        },
      };

      const mockCreatedGeofence: GeofencesTable = {
        geofence_id: mockGeofenceId,
        service_id: 'service-123',
        center_pin_name: 'Tokyo Station Area',
        geofence_type: 'CIRCLE',
        circle_radius: '50',
        circle_geometry: { type: 'Point', coordinates: [139.7671, 35.6812] },
        polygon_geometry: undefined,
      };

      const expectedResponse: LocationGeofenceId = {
        geofenceId: 'mocked-uuid',
        name: 'Tokyo Station Area',
        geofence: {
          geofenceType: GeofenceType.CIRCLE,
          center: {
            latitude: 35.6812,
            longitude: 139.7671,
          },
          radiusMeters: 50,
        },
      };

      // mockUuidv4.mockReturnValue(mockGeofenceId);
      mockGeofenceRepository.createGeofence.mockResolvedValue(mockCreatedGeofence);

      const result = await geofenceService.createGeofence(mockServiceId, mockRequest);

      expect(result).toEqual(expectedResponse);
      expect(mockGeofenceRepository.createGeofence).toHaveBeenCalledWith(
        mockServiceId,
        expect.objectContaining({
          service_id: mockServiceId,
          center_pin_name: 'Tokyo Station Area',
          geofence_type: 'CIRCLE',
          circle_radius: '50',
        })
      );
    });

    test('should create a polygon geofence successfully', async () => {
      const mockServiceId = 'service-123';
      const mockGeofenceId = 'geofence-002';
      const mockRequest: LocationGeofence = {
        name: 'Campus Area',
        geofence: {
          geofenceType: GeofenceType.POLYGON,
          coordinates: [
            { latitude: 35.6812, longitude: 139.7671 },
            { latitude: 35.6820, longitude: 139.7680 },
            { latitude: 35.6800, longitude: 139.7690 },
            { latitude: 35.6812, longitude: 139.7671 }, // Closed polygon
          ],
        },
      };

      const mockCreatedGeofence: GeofencesTable = {
        geofence_id: mockGeofenceId,
        service_id: 'service-123',
        center_pin_name: 'Campus Area',
        geofence_type: 'POLYGON',
        circle_radius: undefined,
        circle_geometry: undefined,
        polygon_geometry: {
          type: 'Polygon',
          coordinates: [[[139.7671, 35.6812], [139.7680, 35.6820], [139.7690, 35.6800], [139.7671, 35.6812]]]
        },
      };

      const expectedResponse: LocationGeofenceId = {
        geofenceId: 'mocked-uuid',
        name: 'Campus Area',
        geofence: {
          geofenceType: GeofenceType.POLYGON,
          coordinates: [
            { latitude: 35.6812, longitude: 139.7671 },
            { latitude: 35.6820, longitude: 139.7680 },
            { latitude: 35.6800, longitude: 139.7690 },
            { latitude: 35.6812, longitude: 139.7671 },
          ],
        },
      };

      // mockUuidv4.mockReturnValue(mockGeofenceId);
      mockGeofenceRepository.createGeofence.mockResolvedValue(mockCreatedGeofence);

      const result = await geofenceService.createGeofence(mockServiceId, mockRequest);

      expect(result).toEqual(expectedResponse);
      expect(mockGeofenceRepository.createGeofence).toHaveBeenCalledWith(
        mockServiceId,
        expect.objectContaining({
          service_id: mockServiceId,
          center_pin_name: 'Campus Area',
          geofence_type: 'POLYGON',
        })
      );
    });

    test('should throw ValidationError for invalid geofence type', async () => {
      const mockServiceId = 'service-123';
      const mockRequest = {
        name: 'Invalid Geofence',
        geofence: {
          geofenceType: 'INVALID_TYPE' as any,
          // Don't include center/radiusMeters or coordinates to avoid the createPolygon error
        },
      } as any;

      await expect(geofenceService.createGeofence(mockServiceId, mockRequest)).rejects.toThrow(ValidationError);
    });

    test('should throw ValidationError for polygon with insufficient coordinates', async () => {
      const mockServiceId = 'service-123';
      const mockRequest: LocationGeofence = {
        name: 'Invalid Polygon',
        geofence: {
          geofenceType: GeofenceType.POLYGON,
          coordinates: [
            { latitude: 35.6812, longitude: 139.7671 },
            { latitude: 35.6820, longitude: 139.7680 },
            // Missing third coordinate for valid polygon
          ],
        },
      };

      await expect(geofenceService.createGeofence(mockServiceId, mockRequest)).rejects.toThrow(ValidationError);
    });

    test('should auto-close polygon if not closed', async () => {
      const mockServiceId = 'service-123';
      const mockGeofenceId = 'geofence-003';
      const mockRequest: LocationGeofence = {
        name: 'Auto-close Polygon',
        geofence: {
          geofenceType: GeofenceType.POLYGON,
          coordinates: [
            { latitude: 35.6812, longitude: 139.7671 },
            { latitude: 35.6820, longitude: 139.7680 },
            { latitude: 35.6800, longitude: 139.7690 },
            { latitude: 35.6812, longitude: 139.7671 }, // Manually close for test
          ],
        },
      };

      const mockCreatedGeofence: GeofencesTable = {
        geofence_id: mockGeofenceId,
        service_id: 'service-123',
        center_pin_name: 'Auto-close Polygon',
        geofence_type: 'POLYGON',
        circle_radius: undefined,
        circle_geometry: undefined,
        polygon_geometry: {
          type: 'Polygon',
          coordinates: [[[139.7671, 35.6812], [139.7680, 35.6820], [139.7690, 35.6800], [139.7671, 35.6812]]]
        },
      };

      // mockUuidv4.mockReturnValue(mockGeofenceId);
      mockGeofenceRepository.createGeofence.mockResolvedValue(mockCreatedGeofence);

      const result = await geofenceService.createGeofence(mockServiceId, mockRequest);

      expect(result.geofence.geofenceType).toBe(GeofenceType.POLYGON);
      expect(mockGeofenceRepository.createGeofence).toHaveBeenCalledWith(
        mockServiceId,
        expect.objectContaining({
          geofence_type: 'POLYGON',
          polygon_geometry: expect.objectContaining({
            type: 'Polygon',
            coordinates: expect.any(Array),
          }),
        })
      );
    });
  });

  describe('updateGeofence', () => {
    test('should update a geofence successfully', async () => {
      const mockServiceId = 'service-123';
      const mockGeofenceId = 'geofence-001';
      const mockRequest: LocationGeofence = {
        name: 'Updated Tokyo Station Area',
        geofence: {
          geofenceType: GeofenceType.CIRCLE,
          center: {
            latitude: 35.6812,
            longitude: 139.7671,
          },
          radiusMeters: 100,
        },
      };

      const mockExistingGeofence: GeofencesTable = {
        geofence_id: 'geofence-001',
        service_id: 'service-123',
        center_pin_name: 'Tokyo Station Area',
        geofence_type: 'CIRCLE',
        circle_radius: '50',
        circle_geometry: { type: 'Point', coordinates: [139.7671, 35.6812] },
        polygon_geometry: undefined,
      };

      const mockUpdatedGeofence: GeofencesTable = {
        geofence_id: 'geofence-001',
        service_id: 'service-123',
        center_pin_name: 'Updated Tokyo Station Area',
        geofence_type: 'CIRCLE',
        circle_radius: '100',
        circle_geometry: 'POINT(139.7671 35.6812)',
        polygon_geometry: undefined,
      };

      const expectedResponse: LocationGeofenceId = {
        geofenceId: 'geofence-001',
        name: 'Updated Tokyo Station Area',
        geofence: {
          geofenceType: GeofenceType.CIRCLE,
          center: {
            latitude: 35.6812,
            longitude: 139.7671,
          },
          radiusMeters: 100,
        },
      };

      mockGeofenceRepository.getGeofenceById.mockResolvedValue(mockExistingGeofence);
      mockGeofenceRepository.updateGeofence.mockResolvedValue(mockUpdatedGeofence);

      const result = await geofenceService.updateGeofence(mockServiceId, mockGeofenceId, mockRequest);

      expect(result).toEqual(expectedResponse);
      expect(mockGeofenceRepository.getGeofenceById).toHaveBeenCalledWith(mockServiceId, mockGeofenceId);
      expect(mockGeofenceRepository.updateGeofence).toHaveBeenCalledWith(
        mockServiceId,
        mockGeofenceId,
        expect.objectContaining({
          center_pin_name: 'Updated Tokyo Station Area',
          geofence_type: 'CIRCLE',
          circle_radius: '100',
        })
      );
    });

    test('should return null when geofence does not exist', async () => {
      const mockServiceId = 'service-123';
      const mockGeofenceId = 'non-existent-geofence';
      const mockRequest: LocationGeofence = {
        name: 'Updated Geofence',
        geofence: {
          geofenceType: GeofenceType.CIRCLE,
          center: {
            latitude: 35.6812,
            longitude: 139.7671,
          },
          radiusMeters: 100,
        },
      };

      mockGeofenceRepository.getGeofenceById.mockResolvedValue(undefined);

      const result = await geofenceService.updateGeofence(mockServiceId, mockGeofenceId, mockRequest);

      expect(result).toBeNull();
      expect(mockGeofenceRepository.getGeofenceById).toHaveBeenCalledWith(mockServiceId, mockGeofenceId);
    });
  });

  describe('getGeofences', () => {
    test('should return all geofences for a service', async () => {
      const mockServiceId = 'service-123';
      const mockGeofences: GeofencesTable[] = [
        {
          geofence_id: 'geofence-001',
          service_id: 'service-123',
          center_pin_name: 'Location 1',
          geofence_type: 'CIRCLE',
          circle_radius: '50',
          circle_geometry: { type: 'Point', coordinates: [139.7671, 35.6812] },
          polygon_geometry: undefined,
        },
        {
          geofence_id: 'geofence-002',
          service_id: 'service-123',
          center_pin_name: 'Location 2',
          geofence_type: 'POLYGON',
          circle_radius: undefined,
          circle_geometry: undefined,
          polygon_geometry: {
            type: 'Polygon',
            coordinates: [[[139.7, 35.6], [139.8, 35.6], [139.8, 35.7], [139.7, 35.7], [139.7, 35.6]]]
          },
        },
      ];

      const expectedResponse: LocationGeofenceId[] = [
        {
          geofenceId: 'geofence-001',
          name: 'Location 1',
          geofence: {
            geofenceType: GeofenceType.CIRCLE,
            center: { latitude: 35.6812, longitude: 139.7671 },
            radiusMeters: 50,
          },
        },
        {
          geofenceId: 'geofence-002',
          name: 'Location 2',
          geofence: {
            geofenceType: GeofenceType.POLYGON,
            coordinates: [
              { latitude: 35.6, longitude: 139.7 },
              { latitude: 35.6, longitude: 139.8 },
              { latitude: 35.7, longitude: 139.8 },
              { latitude: 35.7, longitude: 139.7 },
              { latitude: 35.6, longitude: 139.7 },
            ],
          },
        },
      ];

      mockGeofenceRepository.getGeofences.mockResolvedValue(mockGeofences);

      const result = await geofenceService.getGeofences(mockServiceId);

      expect(result).toEqual(expectedResponse);
      expect(mockGeofenceRepository.getGeofences).toHaveBeenCalledWith(mockServiceId);
    });

    test('should return empty array when no geofences exist', async () => {
      const mockServiceId = 'service-123';

      mockGeofenceRepository.getGeofences.mockResolvedValue([]);

      const result = await geofenceService.getGeofences(mockServiceId);

      expect(result).toEqual([]);
      expect(mockGeofenceRepository.getGeofences).toHaveBeenCalledWith(mockServiceId);
    });
  });

  describe('getGeofenceById', () => {
    test('should return a geofence by ID', async () => {
      const mockServiceId = 'service-123';
      const mockGeofenceId = 'geofence-001';
      const mockGeofence: GeofencesTable = {
        geofence_id: 'geofence-001',
        service_id: 'service-123',
        center_pin_name: 'Tokyo Station',
        geofence_type: 'CIRCLE',
        circle_radius: '50',
        circle_geometry: { type: 'Point', coordinates: [139.7671, 35.6812] },
        polygon_geometry: undefined,
      };

      const expectedResponse: LocationGeofenceId = {
        geofenceId: 'geofence-001',
        name: 'Tokyo Station',
        geofence: {
          geofenceType: GeofenceType.CIRCLE,
          center: {
            latitude: 35.6812,
            longitude: 139.7671,
          },
          radiusMeters: 50,
        },
      };

      mockGeofenceRepository.getGeofenceById.mockResolvedValue(mockGeofence);

      const result = await geofenceService.getGeofenceById(mockServiceId, mockGeofenceId);

      expect(result).toEqual(expectedResponse);
      expect(mockGeofenceRepository.getGeofenceById).toHaveBeenCalledWith(mockServiceId, mockGeofenceId);
    });

    test('should return null when geofence does not exist', async () => {
      const mockServiceId = 'service-123';
      const mockGeofenceId = 'non-existent-geofence';

      mockGeofenceRepository.getGeofenceById.mockResolvedValue(undefined);

      const result = await geofenceService.getGeofenceById(mockServiceId, mockGeofenceId);

      expect(result).toBeNull();
      expect(mockGeofenceRepository.getGeofenceById).toHaveBeenCalledWith(mockServiceId, mockGeofenceId);
    });
  });
});
