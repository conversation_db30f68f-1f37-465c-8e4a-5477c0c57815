import { QuestionnaireCreateRequest, QuestionnaireUpdateRequest } from '../../src/dtos/questionnaire/schemas';
import { languageCode } from '../../src/enum/languageCode';
import { QuestionType } from '../../src/enum/questionTypeEnum';
import { QuestionnaireType } from '../../src/enum/questionnaireTypeEnum';
import { NotFoundError } from '../../src/errors/notFoundError';
import { QuestionnaireQuestionRepository } from '../../src/repositories/questionnaireQuestionRepository';
import { QuestionnaireRepository } from '../../src/repositories/questionnaireRepository';
import { QuestionnaireResultAnswerRepository } from '../../src/repositories/questionnaireResultAnswerRepository';
import { QuestionnaireThemeRepository } from '../../src/repositories/questionnaireThemeRepository';
import { ServiceInfoRepository } from '../../src/repositories/serviceInfoRepository';
import { QuestionnaireService } from '../../src/services/questionnaireService';
import { ServiceEntity } from '../../src/tables/servicesTable';

jest.mock('../../src/repositories/serviceInfoRepository');
jest.mock('../../src/repositories/geofenceRepository');
jest.mock('../../src/repositories/questionnaireRepository');
jest.mock('../../src/repositories/questionnaireResultAnswerRepository');
jest.mock('../../src/repositories/questionnaireQuestionRepository');
jest.mock('../../src/repositories/questionnaireThemeRepository');

jest.mock('uuid', () => ({
  v4: jest.fn().mockReturnValue('mock-uuid'),
}));

describe('QuestionnaireService', () => {
  let questionnaireService: QuestionnaireService;
  let mockServiceInfoRepository: jest.Mocked<ServiceInfoRepository>;
  let mockQuestionnaireRepository: jest.Mocked<QuestionnaireRepository>;
  let mockQuestionnaireThemeRepository: jest.Mocked<QuestionnaireThemeRepository>;
  let mockQuestionnaireResultAnswerRepository: jest.Mocked<QuestionnaireResultAnswerRepository>;
  let mockQuestionnaireQuestionRepository: jest.Mocked<QuestionnaireQuestionRepository>;

  beforeEach(() => {
    mockServiceInfoRepository = new ServiceInfoRepository() as jest.Mocked<ServiceInfoRepository>;
    mockQuestionnaireRepository = new QuestionnaireRepository() as jest.Mocked<QuestionnaireRepository>;
    mockQuestionnaireThemeRepository = new QuestionnaireThemeRepository() as jest.Mocked<QuestionnaireThemeRepository>;
    mockQuestionnaireResultAnswerRepository =
      new QuestionnaireResultAnswerRepository() as jest.Mocked<QuestionnaireResultAnswerRepository>;
    mockQuestionnaireQuestionRepository =
      new QuestionnaireQuestionRepository() as jest.Mocked<QuestionnaireQuestionRepository>;

    questionnaireService = new QuestionnaireService(
      mockServiceInfoRepository,
      mockQuestionnaireRepository,
      mockQuestionnaireThemeRepository,
      mockQuestionnaireResultAnswerRepository,
      mockQuestionnaireQuestionRepository,
    );
  });

  describe('createQuestionnaire', () => {
    const mockServiceId = 'mock-service-id';

    const requestData: QuestionnaireCreateRequest = {
      questionnaireType: QuestionnaireType.QUIZ,
      themes: [
        {
          themeThumbnailImageUrl: 'https://example.com/images/theme_thumbnail.jpg',
          themeCoverImageUrl: 'https://example.com/cover1.jpg',
          themeNumber: 1,
          themeTimeLimitSeconds: 600,
          themeTranslations: [
            {
              language: languageCode.EN_US,
              themeTitle: 'General Knowledge',
              themeDescription: 'This theme covers a broad range of general knowledge questions.',
            },
            {
              language: languageCode.JA,
              themeTitle: '一般常識',
              themeDescription: '一般常識の問題です。',
            },
          ],
          questions: [
            {
              questionNumber: 1,
              questionType: QuestionType.SINGLE_CHOICE,
              answerPoint: 10,
              isRequired: true,
              questionImageUrl: undefined,
              questionTranslations: [
                {
                  language: languageCode.JA,
                  questionTitle: 'What is the capital of France?',
                  questionDetail: 'Select the correct answer.',
                  questionExtra: {
                    type: QuestionType.SINGLE_CHOICE,
                    selections: [
                      {
                        order: 0,
                        selectionText: 'Paris',
                      },
                      {
                        order: 1,
                        selectionText: 'London',
                      },
                      {
                        order: 2,
                        selectionText: 'Berlin',
                      },
                    ],
                  },
                  correctData: '1',
                  correctDataValidation: '^*$',
                },
              ],
            },
          ],
        },
      ],
      ranks: [
        {
          rankHeaderAnimationUrl: 'https://example.com/animations/header1.riv',
          rank: 1,
          lowerLimitPoints: 0,
          upperLimitPoints: 50,
          isPassed: true,
          rankTranslations: [
            {
              language: languageCode.JA,
              rankName: '初級者',
            },
            {
              language: languageCode.EN_US,
              rankName: 'Beginner',
            },
          ],
        },
      ],
    };
    const mockService: ServiceEntity = {
      service_id: mockServiceId,
      tenant_id: 'mock-tenant-id',
      service_url: 'https://test-service.com',
      service_logo_image_url: 'https://marbullx.com/logo',
      market_cover_image_url: 'https://example.com/market-cover.jpg',
      theme_primary_color_lowest: '0xFFFFFFFFFFFF',
      theme_primary_color_lower: '0xFFFFFFFFFFFF',
      theme_primary_color_higher: '0xFFFFFFFFFFFF',
      theme_primary_color_highest: '0xFFFFFFFFFFFF',
      is_market_enabled: true,
      membership_nft_contract_id: '0x1234567768',
      stripe_account_id: 'mockStripeAccountId',
      line_channel_id: 'line_channel_id',
      commission_rate: 1,
      modular_contract_id: 'modular_contract_id',
    };

    test('should successfully create a questionnaire', async () => {
      mockServiceInfoRepository.getServiceById.mockResolvedValue(mockService);
      // mockQuestionnaireActionRepository.insertQuestionnaireAction.mockResolvedValue(undefined);

      const result = await questionnaireService.createQuestionnaire(mockServiceId, requestData);

      expect(mockServiceInfoRepository.getServiceById).toHaveBeenCalledWith(mockServiceId);

      expect(result).toHaveProperty('ranks');
      expect(result).toHaveProperty('themes');
    });

    test('should throw NotFoundError if service is not found', async () => {
      mockServiceInfoRepository.getServiceById.mockResolvedValue(undefined);

      await expect(questionnaireService.createQuestionnaire('invalid-service-id', requestData)).rejects.toThrow(
        NotFoundError,
      );

      expect(mockServiceInfoRepository.getServiceById).toHaveBeenCalledWith('invalid-service-id');
    });

    test('should successfully create a questionnaire type MESSAGE', async () => {
      mockServiceInfoRepository.getServiceById.mockResolvedValue(mockService);

      const requestData: QuestionnaireCreateRequest = {
        questionnaireType: QuestionnaireType.MESSAGE,
        themes: [
          {
            themeThumbnailImageUrl: 'https://example.com/images/theme_thumbnail.jpg',
            themeCoverImageUrl: 'https://example.com/cover1.jpg',
            themeNumber: 1,
            themeTimeLimitSeconds: 600,
            themeTranslations: [
              {
                language: languageCode.EN_US,
                themeTitle: 'General Knowledge',
                themeDescription: 'This theme covers a broad range of general knowledge questions.',
              },
              {
                language: languageCode.JA,
                themeTitle: '一般常識',
                themeDescription: '一般常識の問題です。',
              },
            ],
            questions: [
              {
                questionNumber: 1,
                questionType: QuestionType.TEXT_LINES,
                isRequired: true,
                questionImageUrl: undefined,
                questionTranslations: [
                  {
                    language: languageCode.JA,
                    questionTitle: 'What is the capital of France?',
                    questionDetail: 'Select the correct answer.',
                    questionExtra: {
                      type: QuestionType.TEXT_LINES,
                      validations: [],
                    },
                  },
                ],
              },
            ],
          },
        ],
      };
      const result = await questionnaireService.createQuestionnaire(mockServiceId, requestData);

      expect(mockServiceInfoRepository.getServiceById).toHaveBeenCalledWith(mockServiceId);

      expect(result.ranks).toBeUndefined();
      expect(result).toHaveProperty('themes');
    });
  });
  describe('updateQuestionnaire', () => {
    const mockServiceId = 'mock-service-id';
    const mockTenantId = 'mock-tenant-id';
    const mockQuestionnaireId = 'mock-questionnaire-id';

    const mockService: ServiceEntity = {
      service_id: mockServiceId,
      tenant_id: mockTenantId,
      service_url: 'https://test-service.com',
      service_logo_image_url: 'https://marbullx.com/logo',
      market_cover_image_url: 'https://example.com/market-cover.jpg',
      theme_primary_color_lowest: '0xFFFFFFFFFFFF',
      theme_primary_color_lower: '0xFFFFFFFFFFFF',
      theme_primary_color_higher: '0xFFFFFFFFFFFF',
      theme_primary_color_highest: '0xFFFFFFFFFFFF',
      is_market_enabled: true,
      membership_nft_contract_id: '0x1234567768',
      stripe_account_id: 'mockStripeAccountId',
      line_channel_id: 'line_channel_id',
      commission_rate: 1,
      modular_contract_id: 'modular_contract_id',
    };

    const createValidUpdateRequest = (): QuestionnaireUpdateRequest => ({
      questionnaireType: QuestionnaireType.QUIZ,
      themes: [
        {
          themeId: 'theme-1',
          themeThumbnailImageUrl: 'https://example.com/theme-thumbnail.jpg',
          themeCoverImageUrl: 'https://example.com/cover1.jpg',
          themeNumber: 1,
          themeTimeLimitSeconds: 900,
          themeTranslations: [
            {
              language: languageCode.EN_US,
              themeTitle: 'Comprehensive Questions Theme',
              themeDescription: 'Theme with various question types',
            },
            {
              language: languageCode.JA,
              themeTitle: '総合問題',
              themeDescription: '総合問題の問題です。',
            },
          ],
          questions: [
            {
              questionId: 'question-single-choice',
              questionNumber: 1,
              questionType: QuestionType.SINGLE_CHOICE,
              answerPoint: 10,
              isRequired: true,
              questionImageUrl: undefined,
              questionTranslations: [
                {
                  language: languageCode.JA,
                  questionTitle: 'Single Choice Question',
                  questionDetail: 'Select the correct answer',
                  questionExtra: {
                    type: QuestionType.SINGLE_CHOICE,
                    selections: [
                      { order: 0, selectionText: 'Option A' },
                      { order: 1, selectionText: 'Option B' },
                      { order: 2, selectionText: 'Option C' },
                    ],
                  },
                  correctData: '1',
                  correctDataValidation: '^*$',
                },
              ],
            },
          ],
        },
      ],
      ranks: [
        {
          rankId: 'rank-1',
          rankHeaderAnimationUrl: 'https://example.com/animations/beginner.gif',
          rank: 1,
          lowerLimitPoints: 0,
          upperLimitPoints: 50,
          isPassed: true,
          rankTranslations: [
            {
              language: languageCode.JA,
              rankName: '初級者',
            },
            {
              language: languageCode.EN_US,
              rankName: 'Beginner',
            },
          ],
        },
      ],
    });

    test('should successfully update questionnaire with comprehensive questions', async () => {
      mockServiceInfoRepository.getServiceById.mockResolvedValue(mockService);
      mockQuestionnaireRepository.selectQuestionnaireByQuestionnaireId.mockResolvedValue({
        questionnaire_id: mockQuestionnaireId,
        questionnaire_type: QuestionnaireType.QUIZ,
        service_id: mockServiceId,
      });
      mockQuestionnaireRepository.updateQuestionnaireActionData.mockResolvedValue(undefined);
      mockQuestionnaireRepository.getQuestionnaireActionDetail.mockResolvedValue({
        themes: [
          {
            service_id: mockServiceId,
            questionnaire_id: mockQuestionnaireId,
            theme_id: 'theme-1',
            theme_thumbnail_image_url: 'https://example.com/theme-thumbnail.jpg',
            theme_cover_image_url: 'https://example.com/cover1.jpg',
            theme_number: 1,
            theme_time_limit_seconds: 900,
            theme_translations: [
              {
                theme_id: 'theme-1',
                service_id: mockServiceId,
                language: languageCode.EN_US,
                theme_title: 'Comprehensive Questions Theme',
                theme_description: 'Theme with various question types',
              },
              {
                theme_id: 'theme-1',
                service_id: mockServiceId,
                language: languageCode.JA,
                theme_title: '総合問題',
                theme_description: '総合問題の問題です。',
              },
            ],
            questions: [
              {
                theme_id: 'theme-1',
                service_id: mockServiceId,
                question_id: 'question-single-choice',
                question_number: 1,
                is_required: true,
                question_image_url: undefined,
                question_type: QuestionType.SINGLE_CHOICE,
                answer_point: 10,
                question_translations: [
                  {
                    question_id: 'question-single-choice',
                    service_id: mockServiceId,
                    language: languageCode.JA,
                    question_title: 'Single Choice Question',
                    question_detail: 'Select the correct answer',
                    question_extra: {
                      type: QuestionType.SINGLE_CHOICE,
                      // jsonfr保存されるため、DBでもこの形
                      selections: [
                        { order: 0, selectionId: '1', selectionText: 'Option A' },
                        { order: 1, selectionId: '2', selectionText: 'Option B' },
                        { order: 2, selectionId: '3', selectionText: 'Option C' },
                      ],
                    },
                    correct_data: '1',
                    correct_data_validation: '^*$',
                  },
                ],
              },
            ],
          },
        ],
        resultRanks: [
          {
            rank_id: 'rank-1',
            service_id: mockServiceId,
            questionnaire_id: mockQuestionnaireId,
            rank_header_animation_url: 'https://example.com/animations/beginner.gif',
            rank: 1,
            lower_limit_points: 0,
            upper_limit_points: 50,
            is_passed: true,
            rank_translations: [
              {
                rank_id: 'rank-1',
                service_id: mockServiceId,
                language: languageCode.JA,
                rank_name: '初級者',
              },
              {
                rank_id: 'rank-1',
                service_id: mockServiceId,
                language: languageCode.EN_US,
                rank_name: 'Beginner',
              },
            ],
          },
        ],
      });

      const result = await questionnaireService.updateQuestionnaire(
        mockServiceId,
        mockQuestionnaireId,
        createValidUpdateRequest(),
      );

      expect(result.questionnaireId).toBe(mockQuestionnaireId);
    });

    test('should throw NotFoundError if service is not found', async () => {
      mockServiceInfoRepository.getServiceById.mockResolvedValue(undefined);

      await expect(
        questionnaireService.updateQuestionnaire('invalid-service-id', mockQuestionnaireId, createValidUpdateRequest()),
      ).rejects.toThrow(NotFoundError);
    });

    test('should throw NotFoundError if questionnaire is not found', async () => {
      mockServiceInfoRepository.getServiceById.mockResolvedValue(mockService);
      mockQuestionnaireRepository.selectQuestionnaireByQuestionnaireId.mockResolvedValue(undefined);

      await expect(
        questionnaireService.updateQuestionnaire(mockServiceId, 'invalid-questionnaire-id', createValidUpdateRequest()),
      ).rejects.toThrow(NotFoundError);
    });
  });

  describe('getAnsweredQuestionnaire', () => {
    const mockServiceId = 'mock-service-id';
    const mockAccountId = 'mock-account-id';
    const mockQuestionnaireId = 'mock-questionnaire-id';

    const mockService = {
      service_id: mockServiceId,
      tenant_id: 'tenantId',
      service_name: 'Test Service',
      service_url: 'https://test-service.com',
      service_policy:
        'IyBIZWxsbyB3b3JsZCEKIyMgcG9saWN5IGRhdGEKVGhpcyBpcyB3aGVyZSB0aGUgdGV4dCBvZiB0aGUgcG9saWN5IGRhdGEgd2lsbCBiZSBsaXN0ZWQuCgotIGhpbnQgMQotIGhpbnQgMgoKW2ltYWdlXShodHRwczovL2ltYWdlLmNvbSkKCg==',
      service_logo_image_url: 'https://marbullx.com/logo',
      market_cover_image_url: 'https://example.com/market-cover.jpg',
      theme_primary_color_lowest: '0xFFFFFFFFFFFF',
      theme_primary_color_lower: '0xFFFFFFFFFFFF',
      theme_primary_color_higher: '0xFFFFFFFFFFFF',
      theme_primary_color_highest: '0xFFFFFFFFFFFF',
      is_market_enabled: true,
      service_pane: 'xxxxxx',
      membership_nft_contract_id: '0x1234567768',
      stripe_account_id: 'mockStripeAccountId',
      line_channel_id: 'line_channel_id',
      commission_rate: 0.1,
      modular_contract_id: 'modular_contract_id',
    };

    test('should return AnsweredQuestionnaireResponse when data is available', async () => {
      mockServiceInfoRepository.getServiceById.mockResolvedValue(mockService);

      mockQuestionnaireThemeRepository.selectForQuestionnaireAnswers.mockResolvedValue([
        {
          themeId: 'theme-1',
          themeThumbnailImageUrl: 'https://example.com/theme1.jpg',
          themeCoverImageUrl: 'https://example.com/cover1.jpg',
          themeTitle: 'Theme 1',
          themeNumber: 1,
        },
      ]);

      mockQuestionnaireRepository.selectQuestionnaireByQuestionnaireId.mockResolvedValue({
        questionnaire_id: mockQuestionnaireId,
        questionnaire_type: QuestionnaireType.QUIZ,
        service_id: mockServiceId,
      });

      mockQuestionnaireResultAnswerRepository.selectQuestionnaireAnswers.mockResolvedValue([
        {
          questionId: 'q1',
          themeId: 'theme-1',
          questionNumber: 1,
          questionTitle: 'Question 1',
          isRequired: true,
          postedAnswer: 'User Answer',
          correctAnswer: 'Correct Answer',
          obtainedAnswerPoint: 10,
          isCorrect: true,
        },
      ]);

      mockQuestionnaireResultAnswerRepository.selectAnsweredResultRank.mockResolvedValue({
        rankId: 'rank-1',
        rankName: 'Beginner',
        rank: 1,
        rankHeaderAnimation: 'https://example.com/rank.gif',
        questionnairePoint: 45,
        isPassed: true,
      });

      mockQuestionnaireQuestionRepository.sumQuestionnaireAnswerPoints.mockResolvedValue(50);

      const result = await questionnaireService.getAnsweredQuestionnaire(
        mockServiceId,
        mockAccountId,
        mockQuestionnaireId,
        languageCode.JA,
      );

      expect(result).toEqual({
        questionnaireId: mockQuestionnaireId,
        result: {
          rankId: 'rank-1',
          rankName: 'Beginner',
          rank: 1,
          rankHeaderAnimation: 'https://example.com/rank.gif',
          currentPoint: 45,
          isPassed: true,
          maxPoint: 50,
        },
        questionnaireThemes: [
          {
            themeId: 'theme-1',
            correctCount: 1,
            failedCount: 0,
            questions: [
              {
                questionId: 'q1',
                postedAnswer: 'User Answer',
                correctAnswer: 'Correct Answer',
                isRequired: true,
                obtainedAnswerPoint: 10,
                isCorrect: true,
              },
            ],
          },
        ],
      });

      expect(mockServiceInfoRepository.getServiceById).toHaveBeenCalledWith(mockServiceId);
      expect(mockQuestionnaireThemeRepository.selectForQuestionnaireAnswers).toHaveBeenCalledWith(
        mockServiceId,
        mockQuestionnaireId,
        languageCode.JA,
      );
      expect(mockQuestionnaireResultAnswerRepository.selectQuestionnaireAnswers).toHaveBeenCalledWith(
        mockServiceId,
        mockAccountId,
        mockQuestionnaireId,
        languageCode.JA,
      );
      expect(mockQuestionnaireResultAnswerRepository.selectAnsweredResultRank).toHaveBeenCalledWith(
        mockServiceId,
        mockAccountId,
        mockQuestionnaireId,
        languageCode.JA,
      );
      expect(mockQuestionnaireQuestionRepository.sumQuestionnaireAnswerPoints).toHaveBeenCalledWith(
        mockServiceId,
        mockQuestionnaireId,
      );
    });

    test('should throw NotFoundError if service is not found', async () => {
      mockServiceInfoRepository.getServiceById.mockResolvedValue(undefined);

      await expect(
        questionnaireService.getAnsweredQuestionnaire(
          mockServiceId,
          mockAccountId,
          mockQuestionnaireId,
          languageCode.JA,
        ),
      ).rejects.toThrow(NotFoundError);

      expect(mockServiceInfoRepository.getServiceById).toHaveBeenCalledWith(mockServiceId);
    });

    test('should return empty questionnaireThemes if no themes are found', async () => {
      mockServiceInfoRepository.getServiceById.mockResolvedValue(mockService);
      mockQuestionnaireRepository.selectQuestionnaireByQuestionnaireId.mockResolvedValue({
        questionnaire_id: mockQuestionnaireId,
        questionnaire_type: QuestionnaireType.QUIZ,
        service_id: mockServiceId,
      });

      mockQuestionnaireThemeRepository.selectForQuestionnaireAnswers.mockResolvedValue([]);
      mockQuestionnaireResultAnswerRepository.selectQuestionnaireAnswers.mockResolvedValue([]);
      mockQuestionnaireResultAnswerRepository.selectAnsweredResultRank.mockResolvedValue({
        rankId: 'rank-1',
        rankName: 'Beginner',
        rank: 1,
        rankHeaderAnimation: 'https://example.com/rank.gif',
        questionnairePoint: 45,
        isPassed: true,
      });
      mockQuestionnaireQuestionRepository.sumQuestionnaireAnswerPoints.mockResolvedValue(50);

      const result = await questionnaireService.getAnsweredQuestionnaire(
        mockServiceId,
        mockAccountId,
        mockQuestionnaireId,
        languageCode.JA,
      );

      expect(result.questionnaireThemes).toEqual([]);
    });

    test('should return empty questions if no answers are found', async () => {
      mockServiceInfoRepository.getServiceById.mockResolvedValue(mockService);
      mockQuestionnaireRepository.selectQuestionnaireByQuestionnaireId.mockResolvedValue({
        questionnaire_id: mockQuestionnaireId,
        questionnaire_type: QuestionnaireType.QUIZ,
        service_id: mockServiceId,
      });

      mockQuestionnaireThemeRepository.selectForQuestionnaireAnswers.mockResolvedValue([
        {
          themeId: 'theme-1',
          themeThumbnailImageUrl: 'https://example.com/theme1.jpg',
          themeCoverImageUrl: 'https://example.com/cover1.jpg',
          themeTitle: 'Theme 1',
          themeNumber: 1,
        },
      ]);
      mockQuestionnaireResultAnswerRepository.selectQuestionnaireAnswers.mockResolvedValue([]);
      mockQuestionnaireResultAnswerRepository.selectAnsweredResultRank.mockResolvedValue({
        rankId: 'rank-1',
        rankName: 'Beginner',
        rank: 1,
        rankHeaderAnimation: 'https://example.com/rank.gif',
        questionnairePoint: 45,
        isPassed: true,
      });
      mockQuestionnaireQuestionRepository.sumQuestionnaireAnswerPoints.mockResolvedValue(50);

      const result = await questionnaireService.getAnsweredQuestionnaire(
        mockServiceId,
        mockAccountId,
        mockQuestionnaireId,
        languageCode.JA,
      );

      expect(result.questionnaireThemes[0].questions).toEqual([]);
    });
  });
});
