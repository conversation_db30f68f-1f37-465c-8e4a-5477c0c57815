import { LocationService } from '../../src/services/locationService';
import { GeofenceRepository } from '../../src/repositories/geofenceRepository';
import { ActionRepository } from '../../src/repositories/actionRepository';
import { AccountRepository } from '../../src/repositories/accountRepository';
import { CreateGeofenceRequest, LocationCheckinRequest } from '../../src/dtos/location/schemas';
import { NotFoundError } from '../../src/errors/notFoundError';
import { ValidationError } from '../../src/errors/validationError';
import { RewardActionType } from '../../src/enum/actionType';

// Mock dependencies
jest.mock('../../src/repositories/geofenceRepository');
jest.mock('../../src/repositories/actionRepository');
jest.mock('../../src/repositories/accountRepository');

describe('LocationService', () => {
  let locationService: LocationService;
  let mockGeofenceRepository: jest.Mocked<GeofenceRepository>;
  let mockActionRepository: jest.Mocked<ActionRepository>;
  let mockAccountRepository: jest.Mocked<AccountRepository>;

  const mockServiceId = 'service-123';
  const mockAccountId = 'account-456';
  const mockActionId = 'action-789';
  const mockGeofenceId = 'geofence-abc';

  const mockGeofence = {
    geofence_id: mockGeofenceId,
    service_id: mockServiceId,
    name: 'Tokyo Station',
    description: 'Check-in location for Tokyo Station quest',
    latitude: 35.6812,
    longitude: 139.7671,
    radius_meters: 100,
    is_active: true,
    created_at: new Date('2023-01-01T00:00:00Z'),
    updated_at: new Date('2023-01-01T00:00:00Z')
  };

  const mockAccount = {
    account_id: mockAccountId,
    service_id: mockServiceId,
    line_uid: 'line-123',
    created_at: new Date(),
    updated_at: new Date()
  };

  const mockAction = {
    action_id: mockActionId,
    service_id: mockServiceId,
    action_type: RewardActionType.LOCATION_CHECKIN,
    name: 'Check in at Tokyo Station',
    description: 'Visit Tokyo Station to complete this action',
    created_at: new Date(),
    updated_at: new Date()
  };

  beforeEach(() => {
    mockGeofenceRepository = new GeofenceRepository() as jest.Mocked<GeofenceRepository>;
    mockActionRepository = new ActionRepository() as jest.Mocked<ActionRepository>;
    mockAccountRepository = new AccountRepository() as jest.Mocked<AccountRepository>;

    locationService = new LocationService(
      mockGeofenceRepository,
      mockActionRepository,
      mockAccountRepository
    );

    jest.clearAllMocks();
  });

  describe('createGeofence', () => {
    it('should create a geofence successfully', async () => {
      const geofenceData: CreateGeofenceRequest = {
        name: 'Tokyo Station',
        description: 'Check-in location for Tokyo Station quest',
        latitude: 35.6812,
        longitude: 139.7671,
        radiusMeters: 100
      };

      mockGeofenceRepository.createGeofence.mockResolvedValue(mockGeofence);

      const result = await locationService.createGeofence(mockServiceId, geofenceData);

      expect(mockGeofenceRepository.createGeofence).toHaveBeenCalledWith(mockServiceId, geofenceData);
      expect(result).toEqual({
        geofenceId: mockGeofenceId,
        serviceId: mockServiceId,
        name: 'Tokyo Station',
        description: 'Check-in location for Tokyo Station quest',
        latitude: 35.6812,
        longitude: 139.7671,
        radiusMeters: 100,
        isActive: true,
        createdAt: '2023-01-01T00:00:00.000Z',
        updatedAt: '2023-01-01T00:00:00.000Z'
      });
    });
  });

  describe('getGeofenceById', () => {
    it('should return geofence when found', async () => {
      mockGeofenceRepository.selectGeofenceById.mockResolvedValue(mockGeofence);

      const result = await locationService.getGeofenceById(mockServiceId, mockGeofenceId);

      expect(mockGeofenceRepository.selectGeofenceById).toHaveBeenCalledWith(mockServiceId, mockGeofenceId);
      expect(result.geofenceId).toBe(mockGeofenceId);
    });

    it('should throw NotFoundError when geofence not found', async () => {
      mockGeofenceRepository.selectGeofenceById.mockResolvedValue(undefined);

      await expect(
        locationService.getGeofenceById(mockServiceId, mockGeofenceId)
      ).rejects.toThrow(NotFoundError);
    });
  });

  describe('performLocationCheckin', () => {
    const checkinRequest: LocationCheckinRequest = {
      actionId: mockActionId,
      location: {
        latitude: 35.6812, // Exact center of geofence
        longitude: 139.7671,
        accuracy: 5.0
      }
    };

    const mockLocationAction = {
      ...mockGeofence,
      location_action_id: 'location-action-123',
      action_id: mockActionId,
      geofence_id: mockGeofenceId,
      required_duration_seconds: 0,
      created_at: new Date(),
      updated_at: new Date()
    };

    beforeEach(() => {
      mockAccountRepository.selectAccountById.mockResolvedValue(mockAccount);
      mockActionRepository.selectActionById.mockResolvedValue(mockAction);
      mockGeofenceRepository.getSuccessfulLocationCheckin.mockResolvedValue(undefined);
      mockGeofenceRepository.getLocationCheckinActionWithGeofence.mockResolvedValue(mockLocationAction);
    });

    it('should complete location check-in when within geofence', async () => {
      mockGeofenceRepository.isLocationWithinGeofence.mockResolvedValue({
        withinGeofence: true,
        distance: 0
      });
      mockGeofenceRepository.createLocationCheckinAttempt.mockResolvedValue({
        attempt_id: 'attempt-123',
        account_id: mockAccountId,
        action_id: mockActionId,
        latitude: 35.6812,
        longitude: 139.7671,
        accuracy_meters: 5.0,
        is_successful: true,
        distance_from_target: 0,
        attempted_at: new Date()
      });

      const result = await locationService.performLocationCheckin(
        mockServiceId,
        mockAccountId,
        checkinRequest
      );

      expect(result.success).toBe(true);
      expect(result.withinGeofence).toBe(true);
      expect(result.distanceFromTarget).toBe(0);
      expect(result.message).toContain('Successfully checked in');
      expect(mockActionRepository.completeAction).toHaveBeenCalledWith(mockAccountId, mockActionId);
    });

    it('should fail location check-in when outside geofence', async () => {
      const outsideCheckinRequest: LocationCheckinRequest = {
        actionId: mockActionId,
        location: {
          latitude: 35.6900, // Outside the 100m radius
          longitude: 139.7671,
          accuracy: 5.0
        }
      };

      mockGeofenceRepository.isLocationWithinGeofence.mockResolvedValue({
        withinGeofence: false,
        distance: 150
      });
      mockGeofenceRepository.createLocationCheckinAttempt.mockResolvedValue({
        attempt_id: 'attempt-123',
        account_id: mockAccountId,
        action_id: mockActionId,
        latitude: 35.6900,
        longitude: 139.7671,
        accuracy_meters: 5.0,
        is_successful: false,
        distance_from_target: 150,
        attempted_at: new Date()
      });

      const result = await locationService.performLocationCheckin(
        mockServiceId,
        mockAccountId,
        outsideCheckinRequest
      );

      expect(result.success).toBe(false);
      expect(result.withinGeofence).toBe(false);
      expect(result.distanceFromTarget).toBe(150);
      expect(result.message).toContain('50m away from');
      expect(mockActionRepository.completeAction).not.toHaveBeenCalled();
    });

    it('should return already completed when action was previously completed', async () => {
      const existingCompletion = {
        attempt_id: 'attempt-existing',
        account_id: mockAccountId,
        action_id: mockActionId,
        latitude: 35.6812,
        longitude: 139.7671,
        accuracy_meters: 5.0,
        is_successful: true,
        distance_from_target: 0,
        attempted_at: new Date('2023-01-01T12:00:00Z')
      };

      mockGeofenceRepository.getSuccessfulLocationCheckin.mockResolvedValue(existingCompletion);

      const result = await locationService.performLocationCheckin(
        mockServiceId,
        mockAccountId,
        checkinRequest
      );

      expect(result.success).toBe(false);
      expect(result.message).toBe('Action already completed');
      expect(result.completedAt).toBe('2023-01-01T12:00:00.000Z');
    });

    it('should throw NotFoundError when account not found', async () => {
      mockAccountRepository.selectAccountById.mockResolvedValue(undefined);

      await expect(
        locationService.performLocationCheckin(mockServiceId, mockAccountId, checkinRequest)
      ).rejects.toThrow(NotFoundError);
    });

    it('should throw NotFoundError when action not found', async () => {
      mockActionRepository.selectActionById.mockResolvedValue(undefined);

      await expect(
        locationService.performLocationCheckin(mockServiceId, mockAccountId, checkinRequest)
      ).rejects.toThrow(NotFoundError);
    });

    it('should throw ValidationError when action is not location check-in type', async () => {
      const nonLocationAction = {
        ...mockAction,
        action_type: 'QR_CHECKIN' as any
      };
      mockActionRepository.selectActionById.mockResolvedValue(nonLocationAction);

      await expect(
        locationService.performLocationCheckin(mockServiceId, mockAccountId, checkinRequest)
      ).rejects.toThrow(ValidationError);
    });

    it('should throw NotFoundError when location action configuration not found', async () => {
      mockGeofenceRepository.getLocationCheckinActionWithGeofence.mockResolvedValue(undefined);

      await expect(
        locationService.performLocationCheckin(mockServiceId, mockAccountId, checkinRequest)
      ).rejects.toThrow(NotFoundError);
    });
  });

  describe('checkLocationInGeofence', () => {
    it('should return correct distance and within status', async () => {
      const location = {
        latitude: 35.6812,
        longitude: 139.7671,
        accuracy: 5.0
      };

      mockGeofenceRepository.selectGeofenceById.mockResolvedValue(mockGeofence);
      mockGeofenceRepository.isLocationWithinGeofence.mockResolvedValue({
        withinGeofence: true,
        distance: 0
      });

      const result = await locationService.checkLocationInGeofence(
        mockServiceId,
        mockGeofenceId,
        location
      );

      expect(result.withinGeofence).toBe(true);
      expect(result.distance).toBe(0);
      expect(result.geofence.geofenceId).toBe(mockGeofenceId);
    });

    it('should throw NotFoundError when geofence not found', async () => {
      mockGeofenceRepository.selectGeofenceById.mockResolvedValue(undefined);

      await expect(
        locationService.checkLocationInGeofence(mockServiceId, mockGeofenceId, {
          latitude: 35.6812,
          longitude: 139.7671
        })
      ).rejects.toThrow(NotFoundError);
    });
  });
});
