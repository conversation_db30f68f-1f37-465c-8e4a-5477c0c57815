import { FirebaseComponent } from '../../src/components/firebaseComponent';
import { RedisComponent } from '../../src/components/redisComponent';
import { FirestoreNftsDocument, NftsFirestoreRepository } from '../../src/repositories/nftsFirestoreRepository';
import { TokenGateService } from '../../src/services/tokenGateService';
import { ViemComponent } from '../../src/components/viemComponent';
import { AccountRepository } from '../../src/repositories/accountRepository';
import {
  CheckTokenGateAccessRequest,
  CheckTokenGateAccessResponse,
  RegisterTokenGateRequest,
  RegisterTokenGateResponse,
  VerifySigRequest,
} from '../../src/dtos/nfts/schemas';
import { UnauthorizedError } from '../../src/errors/unauthorizedError';
import { NotFoundError } from '../../src/errors/notFoundError';
import { InternalServerError } from '../../src/errors/internalServerError';
import { TokenGateRepository } from '../../src/repositories/tokenGateRepository';
import { NftType } from '../../src/enum/nftType';

jest.mock('uuid', () => ({
  v4: jest.fn().mockReturnValue('mock-uuid'),
}));

jest.mock('../../src/components/gptComponent');
jest.mock('../../src/components/redisComponent');
jest.mock('../../src/repositories/nftsFirestoreRepository');
jest.mock('../../src/components/viemComponent');
jest.mock('../../src/repositories/accountRepository');
jest.mock('../../src/repositories/tokenGateRepository');

describe('TokenGateService', () => {
  let tokenGateService: TokenGateService;
  let mockRedisComponent: jest.Mocked<RedisComponent>;
  let mockAccountRepository: jest.Mocked<AccountRepository>;
  let mockNftsFirestoreRepository: jest.Mocked<NftsFirestoreRepository>;
  let mockFirebaseComponent: jest.Mocked<FirebaseComponent>;
  let mockViemComponent: jest.Mocked<ViemComponent>;
  let mockTokenGateRepository: jest.Mocked<TokenGateRepository>;

  beforeEach(() => {
    mockFirebaseComponent = new FirebaseComponent() as jest.Mocked<FirebaseComponent>;
    mockNftsFirestoreRepository = new NftsFirestoreRepository(
      mockFirebaseComponent,
    ) as jest.Mocked<NftsFirestoreRepository>;
    mockRedisComponent = new RedisComponent() as jest.Mocked<RedisComponent>;
    mockAccountRepository = new AccountRepository() as jest.Mocked<AccountRepository>;
    mockViemComponent = new ViemComponent() as jest.Mocked<ViemComponent>;
    mockTokenGateRepository = new TokenGateRepository() as jest.Mocked<TokenGateRepository>;

    tokenGateService = new TokenGateService(
      mockRedisComponent,
      mockAccountRepository,
      mockNftsFirestoreRepository,
      mockViemComponent,
      mockTokenGateRepository,
    );
  });

  describe('verifySigAndFetchNfts', () => {
    const serviceId = 'F8DAFCE9-C629-4E39-9F75-7C93D8BC3AF';
    const accountId = '55DAFCE9-C629-4E39-9F75-37C93D8BC3AF';
    const mockRequestBody: VerifySigRequest = {
      sig: '0x143538a788799ff8f30ddfe0691fb997ebf3d3cc509491436201ac90713b521e1407c5e413e5648a206ec16dec0a18dfa443309c9ad185faec1746ed32fa903c1b',
      msg: 'Nonce: 4db9e045d4527024302b84e202c2431b501bb051d466004970b9bd7aaa59118d\nIssued At: 2025-06-05T14:00:00Z\nExpiration: 2025-08-05T14:00:00Z\nDomain: marbullx.com',
    };
    const mockNfts: FirestoreNftsDocument[] = [
      {
        chainId: '1',
        contractAddress: '0xB33F706B1aC2543A108F40497a14f540778798fd',
        tokenId: '1',
        amount: 1,
        accountId: '55DAFCE9-C629-4E39-9F75-37C93D8BC3AF',
        serviceId: 'F8DAFCE9-C629-4E39-9F75-7C93D8BC3AF',
        contractType: NftType.CONTENT,
        metadataJson: {},
        metadataUri: 'https://example.com',
        metadataCachedAt: '2025-06-05T14:00:00Z',
        updatedAt: '2025-06-05T14:00:00Z',
        createdAt: '2025-06-05T14:00:00Z',
        transactions: {},
      },
    ];

    it('should verify a signature and fetch nfts', async () => {
      const mockNftsResponse = {
        ownedNfts: mockNfts.map((nft) => ({
          chainId: '1',
          contractAddress: nft.contractAddress,
          tokenId: nft.tokenId,
        })),
      };

      mockRedisComponent.getDel.mockResolvedValue('1');
      mockAccountRepository.selectTokenBoundAccountAddress.mockResolvedValue(
        '0xB33F706B1aC2543A108F40497a14f540778798fd',
      );
      mockViemComponent.callViewFunction
        .mockResolvedValueOnce('0x0F0aBF68B001f29ACb907f78B43FCA084eA4700A')
        .mockResolvedValueOnce('0x0F0aBF68B001f29ACb907f78B43FCA084eA4700A');
      mockNftsFirestoreRepository.selectNftsByAccountId.mockResolvedValue(mockNfts);

      const result = await tokenGateService.verifySigAndFetchNfts(serviceId, accountId, mockRequestBody);

      expect(mockRedisComponent.getDel).toHaveBeenCalledWith(
        'challenge:4db9e045d4527024302b84e202c2431b501bb051d466004970b9bd7aaa59118d',
      );
      expect(result).toEqual(mockNftsResponse);
    });

    it('should throw an error if the signature is invalid', async () => {
      const invalidSigReqBody = {
        sig: 'foobar',
        msg: 'Nonce: 4db9e045d4527024302b84e202c2431b501bb051d466004970b9bd7aaa59118d\nIssued At: 2025-06-05T14:00:00Z\nExpiration: 2025-08-05T14:00:00Z\nDomain: marbullx.com',
      };

      await expect(tokenGateService.verifySigAndFetchNfts(serviceId, accountId, invalidSigReqBody)).rejects.toThrow(
        new UnauthorizedError('Signature is invalid'),
      );
      expect(mockRedisComponent.getDel).not.toHaveBeenCalled();
    });

    it('should throw an error if the expiration date is in the past', async () => {
      const expiredSigReqBody = {
        sig: '0x143538a788799ff8f30ddfe0691fb997ebf3d3cc509491436201ac90713b521e1407c5e413e5648a206ec16dec0a18dfa443309c9ad185faec1746ed32fa903c1b',
        msg: 'Nonce: 4db9e045d4527024302b84e202c2431b501bb051d466004970b9bd7aaa59118d\nIssued At: 2025-06-05T14:00:00Z\nExpiration: 2025-06-05T14:00:00Z\nDomain: marbullx.com',
      };

      await expect(tokenGateService.verifySigAndFetchNfts(serviceId, accountId, expiredSigReqBody)).rejects.toThrow(
        new UnauthorizedError('The expiration date is in the past'),
      );
      expect(mockRedisComponent.getDel).not.toHaveBeenCalled();
    });

    it('should throw an error if the nonce is invalid', async () => {
      mockRedisComponent.get.mockResolvedValue('0');
      await expect(tokenGateService.verifySigAndFetchNfts(serviceId, accountId, mockRequestBody)).rejects.toThrow(
        new UnauthorizedError('Nonce is invalid or not found'),
      );
    });

    it('should throw an error if the token bound account address is not found', async () => {
      mockRedisComponent.getDel.mockResolvedValue('1');
      mockAccountRepository.selectTokenBoundAccountAddress.mockResolvedValue('');
      await expect(tokenGateService.verifySigAndFetchNfts(serviceId, accountId, mockRequestBody)).rejects.toThrow(
        new NotFoundError('Token bound account address is not found'),
      );
    });

    it('should throw an error if the token bound account address is invalid', async () => {
      mockRedisComponent.getDel.mockResolvedValue('1');
      mockAccountRepository.selectTokenBoundAccountAddress.mockResolvedValue('foobar');
      await expect(tokenGateService.verifySigAndFetchNfts(serviceId, accountId, mockRequestBody)).rejects.toThrow(
        new InternalServerError('Token bound account address is invalid'),
      );
    });

    it('should throw an error if the token bound account owner is not found', async () => {
      mockRedisComponent.getDel.mockResolvedValue('1');
      mockAccountRepository.selectTokenBoundAccountAddress.mockResolvedValue(
        '0xB33F706B1aC2543A108F40497a14f540778798fd',
      );
      mockViemComponent.callViewFunction.mockResolvedValueOnce('');
      await expect(tokenGateService.verifySigAndFetchNfts(serviceId, accountId, mockRequestBody)).rejects.toThrow(
        new InternalServerError('Token bound account owner is not found'),
      );
    });

    it('should throw an error if the contract account address cannot be generated', async () => {
      mockRedisComponent.getDel.mockResolvedValue('1');
      mockAccountRepository.selectTokenBoundAccountAddress.mockResolvedValue(
        '0xB33F706B1aC2543A108F40497a14f540778798fd',
      );
      mockViemComponent.callViewFunction
        .mockResolvedValueOnce('0x0F0aBF68B001f29ACb907f78B43FCA084eA4700A')
        .mockResolvedValueOnce('');
      await expect(tokenGateService.verifySigAndFetchNfts(serviceId, accountId, mockRequestBody)).rejects.toThrow(
        new InternalServerError('Contract account address cannot be generated'),
      );
    });

    it('should throw an error if the nfts are not found', async () => {
      mockRedisComponent.getDel.mockResolvedValue('1');
      mockAccountRepository.selectTokenBoundAccountAddress.mockResolvedValue(
        '0xB33F706B1aC2543A108F40497a14f540778798fd',
      );
      mockViemComponent.callViewFunction
        .mockResolvedValueOnce('0x0F0aBF68B001f29ACb907f78B43FCA084eA4700A')
        .mockResolvedValueOnce('0x0F0aBF68B001f29ACb907f78B43FCA084eA4700A');
      mockNftsFirestoreRepository.selectNftsByAccountId.mockRejectedValue(new NotFoundError('Nfts not found'));
      await expect(tokenGateService.verifySigAndFetchNfts(serviceId, accountId, mockRequestBody)).rejects.toThrow(
        new InternalServerError('Failed to fetch nfts from Firestore'),
      );
    });
  });

  describe('checkTokenGateAccess', () => {
    const serviceId = 'F8DAFCE9-C629-4E39-9F75-7C93D8BC3AF';
    const mockTokenGateConditions = new Map([
      [
        '205c21f7-5e68-41d7-95d7-7650f16dfc40',
        [
          {
            set_number: 1,
            hashes: ['5a01a36181ad5d837ab5ef42f6732825d0ae25b0e3851895f8f7081f311fc40d'],
          },
          {
            set_number: 2,
            hashes: [
              'd2ccfe4997d3c52f72aee03706859c102758d0076e0301ae8b6ae8161f4fa639',
              '34894dd2e2392f3fc0d5fcfd2508bb8fe359a4bb4016b79a95b35a969a0a9656',
            ],
          },
        ],
      ],
      [
        'e4c6022f-5921-41e7-a9d5-e83dbb1fe0ef',
        [
          {
            set_number: 1,
            hashes: ['15abf94557175b18cb364d27cfa14c568687a411a242756a68efe4534fb01d72'],
          },
          {
            set_number: 2,
            hashes: [
              'e919f066c8bae72ad08d0b78fb8fbdfc523097d8b34e5e47b736c17994f8abd0',
              '2e4805986f22c778186eb0b0641afcddfad9dfb87f455d1d5e75ffe583c0edf9',
            ],
          },
        ],
      ],
    ]);
    it('should pass the all token gates in case of the provided nfts are all valid', async () => {
      const mockTokenGateRequest: CheckTokenGateAccessRequest = {
        tokenGateIds: ['205c21f7-5e68-41d7-95d7-7650f16dfc40', 'e4c6022f-5921-41e7-a9d5-e83dbb1fe0ef'],
        nfts: [
          {
            chainId: '137',
            contractAddress: '0xDDDD4444DDDD4444DDDD4444DDDD4444DDDD4444',
            tokenId: '77',
          },
          {
            chainId: '137',
            contractAddress: '0xBBBB2222BBBB2222BBBB2222BBBB2222BBBB2222',
            tokenId: '11',
          },
          {
            chainId: '1',
            contractAddress: '0xCCCC3333CCCC3333CCCC3333CCCC3333CCCC3333',
            tokenId: '125',
          },
        ],
      };
      const mockTokenGateResponse: CheckTokenGateAccessResponse = {
        passed: ['205c21f7-5e68-41d7-95d7-7650f16dfc40', 'e4c6022f-5921-41e7-a9d5-e83dbb1fe0ef'],
        failed: [],
      };
      mockTokenGateRepository.fetchConditions.mockResolvedValue(mockTokenGateConditions);
      const result = await tokenGateService.checkTokenGateAccess(serviceId, mockTokenGateRequest);
      expect(result).toEqual(mockTokenGateResponse);
    });

    it('should fail the token gate in case of the provided nfts are not valid', async () => {
      const mockTokenGateRequest: CheckTokenGateAccessRequest = {
        tokenGateIds: ['205c21f7-5e68-41d7-95d7-7650f16dfc40'],
        nfts: [
          {
            chainId: '137',
            contractAddress: '0xEEEE5555EEEE5555EEEE5555EEEE5555EEEE5555',
            tokenId: '77',
          },
        ],
      };
      const mockTokenGateResponse: CheckTokenGateAccessResponse = {
        passed: [],
        failed: [
          {
            tokenGateId: '205c21f7-5e68-41d7-95d7-7650f16dfc40',
            reason: 'CONDITION_NOT_MATCHED',
          },
        ],
      };
      mockTokenGateRepository.fetchConditions.mockResolvedValue(mockTokenGateConditions);
      const result = await tokenGateService.checkTokenGateAccess(serviceId, mockTokenGateRequest);
      expect(result).toEqual(mockTokenGateResponse);
    });

    it('should partially pass the token gate in case of the provided nfts are partially valid', async () => {
      const mockTokenGateRequest: CheckTokenGateAccessRequest = {
        tokenGateIds: ['e4c6022f-5921-41e7-a9d5-e83dbb1fe0ef', '205c21f7-5e68-41d7-95d7-7650f16dfc40'],
        nfts: [
          {
            chainId: '137',
            contractAddress: '0xDDDD4444DDDD4444DDDD4444DDDD4444DDDD4444',
            tokenId: '42',
          },
        ],
      };
      const mockTokenGateResponse: CheckTokenGateAccessResponse = {
        passed: ['205c21f7-5e68-41d7-95d7-7650f16dfc40'],
        failed: [
          {
            tokenGateId: 'e4c6022f-5921-41e7-a9d5-e83dbb1fe0ef',
            reason: 'CONDITION_NOT_MATCHED',
          },
        ],
      };
      mockTokenGateRepository.fetchConditions.mockResolvedValue(mockTokenGateConditions);
      const result = await tokenGateService.checkTokenGateAccess(serviceId, mockTokenGateRequest);
      expect(result).toEqual(mockTokenGateResponse);
    });
  });

  describe('registerTokenGate', () => {
    const serviceId = 'F8DAFCE9-C629-4E39-9F75-7C93D8BC3AF';
    it('should register a token gate', async () => {
      const mockTokenGateRequest: RegisterTokenGateRequest = {
        tokenGateTranslations: [
          {
            language: 'ja',
            gateName: '限定記事35号用TokenGate',
            gateDescription: 'この記事は、限定NFT Aと限定NFT Bの両方を所持しているユーザーのみが閲覧可能です。',
          },
          {
            language: 'en-US',
            gateName: 'TokenGate for Premium Article #35',
            gateDescription: 'Only users who own both limited NFT A and limited NFT B can view this article.',
          },
        ],
        tokenGateConditions: [
          {
            setNumber: 1,
            nft: {
              chainId: '137',
              contractAddress: '0xAAAA1111AAAA1111AAAA1111AAAA1111AAAA1111',
            },
          },
          {
            setNumber: 2,
            nft: {
              chainId: '137',
              contractAddress: '0xBBBB2222BBBB2222BBBB2222BBBB2222BBBB2222',
              tokenId: '11',
            },
          },
          {
            setNumber: 2,
            nft: {
              chainId: '1',
              contractAddress: '0xCCCC3333CCCC3333CCCC3333CCCC3333CCCC3333',
              tokenId: '125',
            },
          },
        ],
      };
      const mockTokenGateResponse: RegisterTokenGateResponse = {
        tokenGateId: '123e4567-e89b-12d3-a456-************',
      };
      mockTokenGateRepository.createTokenGate.mockResolvedValue('123e4567-e89b-12d3-a456-************');
      const result = await tokenGateService.registerTokenGate(serviceId, mockTokenGateRequest);
      expect(mockTokenGateRepository.createTokenGate).toHaveBeenCalledWith(
        serviceId,
        'mock-uuid',
        mockTokenGateRequest.tokenGateTranslations,
        [
          {
            setNumber: 1,
            tokenHash: '15abf94557175b18cb364d27cfa14c568687a411a242756a68efe4534fb01d72',
          },
          {
            setNumber: 2,
            tokenHash: 'e919f066c8bae72ad08d0b78fb8fbdfc523097d8b34e5e47b736c17994f8abd0',
          },
          {
            setNumber: 2,
            tokenHash: '2e4805986f22c778186eb0b0641afcddfad9dfb87f455d1d5e75ffe583c0edf9',
          },
        ],
      );
      expect(result).toEqual(mockTokenGateResponse);
    });
  });
});
