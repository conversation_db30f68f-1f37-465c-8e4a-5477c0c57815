/* eslint-disable @typescript-eslint/no-explicit-any */
// import '@types/jest';
import { NftTransactionUpdateService } from '../../src/services/transactionUpdateService';
import { AccountRepository } from '../../src/repositories/accountRepository';
import { NftContractJoinNftContractType, NftContractsRepository } from '../../src/repositories/nftContractsRepository';
import { NftBaseMetadatasRepository } from '../../src/repositories/nftBaseMetadatasRepository';
import { NftType } from '../../src/enum/nftType';
import { FirestoreNftsUpdateInfo } from '../../src/services/transactionUpdateService';
import { TxDetail, WebhookTxType } from '../../src/services/webhookService';
import { FirebaseComponent } from '../../src/components/firebaseComponent';

import { AccountEntity } from '../../src/tables/accountTable';
import { NftBaseMetadatasEntity } from '../../src/tables/nftBaseMetadatasTable';
import { NftsFirestoreRepository } from '../../src/repositories/nftsFirestoreRepository';
import {
  createMockAccountRepository,
  createMockDeliveryNftsFirestoreRepository,
  createMockNftBaseMetadatasRepository,
  createMockNftContractsRepository,
  createMockNftsFirestoreRepository,
  createMockViemComponent,
  createMockServiceInfoRepository,
  createMockTransactionComponent,
  createMockTransactionsRepository,
  createMockTransactionQueuesRepository,
} from '../../src/utils/mockFactories';
import { DeliveryNftsFirestoreRepository } from '../../src/repositories/deliveryNftsFirestoreRepository';
import { TxType } from '../../src/enum/txType';
import { ViemComponent } from '../../src/components/viemComponent';
import { ServiceInfoRepository } from '../../src/repositories/serviceInfoRepository';
import { mockErc721MintTx, mockBlock } from '../data/viemTestData';
import { TransactionComponent } from '../../src/components/transactionComponent';
import { ServiceEntity } from '../../src/tables/servicesTable';
import type FirebaseFirestore from '@google-cloud/firestore';
import { TransactionQueuesRepository } from '../../src/repositories/transactionQueuesRepository';
import { TransactionsRepository } from '../../src/repositories/transactionsRepository';
import { TransactionsEntity } from '../../src/tables/transactionsTable';
import { TransactionStatus } from '../../src/enum/transactionStatus';
import { TransactionQueuesEntity } from '../../src/tables/transactionQueuesTable';
import { TransactionQueueStatus } from '../../src/enum/transactionQueueStatus';
import { AccountStatus } from '../../src/enum/accoutStatus';

jest.mock('../../src/components/firebaseComponent');
jest.mock('../../src/repositories/accountRepository');
jest.mock('../../src/repositories/nftsFirestoreRepository');
jest.mock('../../src/repositories/deliveryNftsFirestoreRepository');
jest.mock('../../src/repositories/nftContractsRepository');
jest.mock('../../src/repositories/nftBaseMetadatasRepository');
jest.mock('../../src/repositories/serviceInfoRepository');
jest.mock('../../src/components/viemComponent');
jest.mock('../../src/components/transactionComponent');
jest.mock('../../src/services/transactionService');
jest.mock('../../src/repositories/transactionsRepository');
jest.mock('../../src/repositories/transactionQueuesRepository');
jest.mock('../../src/db/database', () => {
  return {
    db: {
      transaction: jest.fn(() => ({
        execute: jest.fn(async (callback) => callback()),
      })),
    },
  };
});

describe('NftTransactionUpdateService', () => {
  let service: NftTransactionUpdateService;
  let mockAccountRepository: jest.Mocked<AccountRepository>;
  let mockNftsFirestoreRepository: jest.Mocked<NftsFirestoreRepository>;
  let mockNftContractsRepository: jest.Mocked<NftContractsRepository>;
  let mockNftBaseMetadatasRepository: jest.Mocked<NftBaseMetadatasRepository>;
  let mockDeliveryNftsFirestoreRepository: jest.Mocked<DeliveryNftsFirestoreRepository>;
  let mockServiceInfoRepository: jest.Mocked<ServiceInfoRepository>;
  let mockViemComponent: jest.Mocked<ViemComponent>;
  let mockTransactionComponent: jest.Mocked<TransactionComponent>;
  let mockTransactionsRepository: jest.Mocked<TransactionsRepository>;
  let mockTransactionQueuesRepository: jest.Mocked<TransactionQueuesRepository>;

  beforeEach(() => {
    process.env.ALCHEMY_ADDRESS_ACTIVITY_WEBHOOK_ID = 'mockAddressActivityWebhookId';
    process.env.ALCHEMY_NFT_ACTIVITY_WEBHOOK_ID = 'mockNftActivityWebhookId';
    process.env.ALCHEMY_ADDRESS_ACTIVITY_WEBHOOK_SIGNING_KEY = 'mockSigningKey';
    process.env.ALCHEMY_NFT_ACTIVITY_WEBHOOK_SIGNING_KEY = 'mockSigningKey';
    process.env.ALCHEMY_CHAIN_NAME = 'polygon-amoy';
    process.env.ALCHEMY_API_KEY = 'xxxxxsss';
    process.env.ALCHEMY_AUTH_TOKEN = 'mockAuthToken';
    process.env.GCP_PROJECT_ID = 'sample_gcp_project_id';
    process.env.GCS_BUCKET_NAME = 'mock-bucket-name';

    mockAccountRepository = createMockAccountRepository();
    mockNftsFirestoreRepository = createMockNftsFirestoreRepository();
    mockNftContractsRepository = createMockNftContractsRepository();
    mockNftBaseMetadatasRepository = createMockNftBaseMetadatasRepository();
    mockServiceInfoRepository = createMockServiceInfoRepository();
    mockViemComponent = createMockViemComponent();
    mockDeliveryNftsFirestoreRepository = createMockDeliveryNftsFirestoreRepository();
    mockTransactionComponent = createMockTransactionComponent();
    mockTransactionsRepository = createMockTransactionsRepository();
    mockTransactionQueuesRepository = createMockTransactionQueuesRepository();

    service = new NftTransactionUpdateService(
      mockAccountRepository,
      mockNftsFirestoreRepository,
      mockNftContractsRepository,
      mockNftBaseMetadatasRepository,
      mockDeliveryNftsFirestoreRepository,
      mockServiceInfoRepository,
      mockViemComponent,
      mockTransactionComponent,
      mockTransactionsRepository,
      mockTransactionQueuesRepository,
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('updateFirestoreNftsCollection', () => {
    const txData: TxDetail = {
      nftTokenId: '1',
      nftAmount: '1',
      nftContractAddress: '0x123',
      transactionHash: '0xabc',
      timestamp: '**********',
      isConfirmed: true,
      type: WebhookTxType.NFT_TRANSFER,
    };
    const FirestoreNftsUpdateInfo: FirestoreNftsUpdateInfo = {
      serviceId: 'service1',
      tokenId: '1',
      from: '0x0000',
      to: '0x1111',
      amount: '1',
      contractAddress: '0x123',
      transactionHash: '0xabc',
      nftType: NftType.CERTIFICATE,
      timestamp: '**********',
      isConfirmed: true,
    };

    test('should update transaction confirmation if NFT documents exist', async () => {
      mockNftsFirestoreRepository.selectNftsByTxHash.mockResolvedValue({
        empty: false,
        docs: [
          {
            exists: true,
            ref: { id: 'mockDocRef' },
          },
        ],
      } as unknown as FirebaseFirestore.QuerySnapshot<FirebaseFirestore.DocumentData, FirebaseFirestore.DocumentData>);

      await service.updateFirestoreNftsCollection(txData);

      expect(mockNftsFirestoreRepository.selectNftsByTxHash).toHaveBeenCalledWith(
        FirestoreNftsUpdateInfo.transactionHash,
      );
    });

    test('should handle transaction type if no NFT documents exist', async () => {
      mockNftsFirestoreRepository.selectNftsByTxHash.mockResolvedValue({
        empty: true,
        docs: [],
      } as unknown as FirebaseFirestore.QuerySnapshot<FirebaseFirestore.DocumentData, FirebaseFirestore.DocumentData>);
      jest.spyOn(service as any, 'handleTransactionType').mockResolvedValue(undefined);

      await service.updateFirestoreNftsCollection(txData);

      expect(mockNftsFirestoreRepository.selectNftsByTxHash).toHaveBeenCalledWith(
        FirestoreNftsUpdateInfo.transactionHash,
      );
      expect(service['handleTransactionType']).toHaveBeenCalledWith(txData);
    });
  });

  describe('processMint', () => {
    const FirestoreNftsUpdateInfo: FirestoreNftsUpdateInfo = {
      serviceId: 'service1',
      tokenId: '1',
      from: '0x0000',
      to: '0x1111',
      amount: '1',
      contractAddress: '0x123',
      transactionHash: '0xabc',
      nftType: NftType.CERTIFICATE,
      timestamp: '**********',
      isConfirmed: true,
    };

    test('should create NFT in Firestore if account and metadata exist', async () => {
      const account: AccountEntity = {
        account_id: 'account1',
        service_id: 'service',
        user_id: 'user',
        membership_id: 0,
        display_name: undefined,
        profile_image_url: undefined,
        token_bound_account_address: undefined,
        status: '',
        transaction_id: '',
        queue_id: '',
        created_at: new Date(),
        updated_at: new Date(),
        last_login_at: new Date('2020/01/01 00:00:00'),
        membership_metadata_url: 'http://example.com/membership_metadata.json',
      };
      const nftMetadata: NftBaseMetadatasEntity = {
        metadata: {},
        base_metadata_id: 'metadataUri',
        service_id: '',
        contract_address: undefined,
        token_id: undefined,
      };

      mockAccountRepository.selectAccountByTokenBoundAddress.mockResolvedValue(account);
      mockNftBaseMetadatasRepository.selectDataByContractAddress.mockResolvedValue(nftMetadata);

      jest.spyOn(service as any, 'buildNftDocument').mockReturnValue({
        contractAddress: FirestoreNftsUpdateInfo.contractAddress,
        tokenId: FirestoreNftsUpdateInfo.tokenId,
        accountId: account.account_id,
        serviceId: FirestoreNftsUpdateInfo.serviceId,
        contractType: NftType.CONTENT,
        metadataJson: {},
      });

      await service['processMint'](FirestoreNftsUpdateInfo, NftType.CONTENT);

      expect(mockAccountRepository.selectAccountByTokenBoundAddress).toHaveBeenCalledWith(
        FirestoreNftsUpdateInfo.to,
        FirestoreNftsUpdateInfo.serviceId,
      );
      expect(mockNftsFirestoreRepository.insertNft).toHaveBeenCalled();
    });

    test('should not insert metadata if account is not found', async () => {
      mockAccountRepository.selectAccountByTokenBoundAddress.mockResolvedValue(undefined);

      await service['processMint'](FirestoreNftsUpdateInfo, NftType.CONTENT);

      expect(mockNftsFirestoreRepository.insertNft).not.toHaveBeenCalled();
    });
  });

  describe('processBurn', () => {
    const FirestoreNftsUpdateInfo: FirestoreNftsUpdateInfo = {
      serviceId: 'service1',
      tokenId: '1',
      from: '0x1234',
      to: '0x0000000000000000000000000000000000000000',
      amount: '1',
      contractAddress: '0x123',
      transactionHash: '0xabc',
      nftType: NftType.COUPON,
      timestamp: new Date().toISOString(),
      isConfirmed: true,
    };

    test('should delete NFT document if it exists and is not COUPON type', async () => {
      const nftDoc = {
        empty: false,
        docs: [
          {
            data: () => ({
              contractType: NftType.CERTIFICATE,
            }),
            ref: {
              delete: jest.fn(),
            },
          },
        ],
      } as unknown as FirebaseFirestore.QuerySnapshot<FirebaseFirestore.DocumentData, FirebaseFirestore.DocumentData>;
      const mockAccount: AccountEntity = {
        account_id: 'account1',
        service_id: '',
        user_id: '',
        membership_id: 0,
        display_name: undefined,
        profile_image_url: undefined,
        token_bound_account_address: undefined,
        status: '',
        transaction_id: '',
        queue_id: '',
        created_at: new Date(),
        updated_at: new Date(),
        last_login_at: new Date('2020/01/01 00:00:00'),
        membership_metadata_url: 'http://example.com/membership_metadata.json',
      };
      mockNftsFirestoreRepository.selectNftByAccountId.mockResolvedValue(nftDoc);
      mockAccountRepository.selectAccountByTokenBoundAddress.mockResolvedValue(mockAccount);

      await service['processBurn'](FirestoreNftsUpdateInfo);

      expect(mockNftsFirestoreRepository.selectNftByAccountId).toHaveBeenCalledWith(
        'account1',
        FirestoreNftsUpdateInfo.contractAddress,
        FirestoreNftsUpdateInfo.tokenId,
      );
      expect(nftDoc.docs[0].ref.delete).toHaveBeenCalled();
    });

    test('should delete NFT COUPON document if amount reaches 0', async () => {
      const nftDoc = {
        empty: false,
        docs: [
          {
            data: jest.fn(() => ({
              contractType: NftType.COUPON,
              amount: 1, // current amount is 1
            })),
            ref: {
              update: jest.fn(),
              delete: jest.fn(),
            },
          },
        ],
      } as unknown as FirebaseFirestore.QuerySnapshot<FirebaseFirestore.DocumentData, FirebaseFirestore.DocumentData>;
      const mockAccount: AccountEntity = {
        account_id: 'account1',
        service_id: '',
        user_id: '',
        membership_id: 0,
        display_name: undefined,
        profile_image_url: undefined,
        token_bound_account_address: undefined,
        status: '',
        transaction_id: '',
        queue_id: '',
        created_at: new Date(),
        updated_at: new Date(),
        last_login_at: new Date('2020/01/01 00:00:00'),
        membership_metadata_url: 'http://example.com/membership_metadata.json',
      };
      mockNftsFirestoreRepository.selectNftByAccountId.mockResolvedValue(nftDoc);
      mockAccountRepository.selectAccountByTokenBoundAddress.mockResolvedValue(mockAccount);

      await service['processBurn'](FirestoreNftsUpdateInfo);

      expect(nftDoc.docs[0].ref.delete).toHaveBeenCalled();
      expect(nftDoc.docs[0].ref.update).not.toHaveBeenCalled();
    });

    test('should not process if no account is found', async () => {
      mockAccountRepository.selectAccountByTokenBoundAddress.mockResolvedValue(undefined);

      await service['processBurn'](FirestoreNftsUpdateInfo);

      expect(mockNftsFirestoreRepository.selectNftByAccountId).not.toHaveBeenCalled();
    });
  });

  describe('processTransfer', () => {
    const FirestoreNftsUpdateInfo: FirestoreNftsUpdateInfo = {
      serviceId: 'service1',
      tokenId: '1',
      from: '0x1234',
      to: '0x1111',
      amount: '2',
      contractAddress: '0x123',
      transactionHash: '0xabc',
      nftType: NftType.CERTIFICATE,
      timestamp: new Date().toISOString(),
      isConfirmed: true,
    };

    test('should handle ERC721 transfer when both accounts exist', async () => {
      const mockAccountFrom = { account_id: 'accountFrom' } as unknown as AccountEntity;
      const mockAccountTo = { account_id: 'accountTo' } as unknown as AccountEntity;

      mockAccountRepository.selectAccountByTokenBoundAddress.mockResolvedValueOnce(mockAccountFrom);
      mockAccountRepository.selectAccountByTokenBoundAddress.mockResolvedValueOnce(mockAccountTo);

      jest.spyOn(service as any, 'handleErc721Transfer').mockResolvedValue(undefined);

      await service['processTransfer'](FirestoreNftsUpdateInfo, NftType.CERTIFICATE);

      expect(service['handleErc721Transfer']).toHaveBeenCalledWith(FirestoreNftsUpdateInfo, 'accountFrom', 'accountTo');
    });

    test('should create NFT for recipient if sender account does not exist', async () => {
      const mockAccountTo = { account_id: 'accountTo' } as unknown as AccountEntity;

      mockAccountRepository.selectAccountByTokenBoundAddress.mockResolvedValueOnce(undefined);
      mockAccountRepository.selectAccountByTokenBoundAddress.mockResolvedValueOnce(mockAccountTo);

      jest.spyOn(service as any, 'createNftForAccount').mockResolvedValue(undefined);

      await service['processTransfer'](FirestoreNftsUpdateInfo, NftType.CERTIFICATE);

      expect(service['createNftForAccount']).toHaveBeenCalledWith(
        FirestoreNftsUpdateInfo,
        NftType.CERTIFICATE,
        'accountTo',
      );
    });

    test('should delete NFT from sender if recipient account does not exist', async () => {
      const mockAccountFrom = { account_id: 'accountFrom' } as unknown as AccountEntity;

      mockAccountRepository.selectAccountByTokenBoundAddress.mockResolvedValueOnce(mockAccountFrom);
      mockAccountRepository.selectAccountByTokenBoundAddress.mockResolvedValueOnce(undefined);

      jest.spyOn(service as any, 'deleteNftFromAccount').mockResolvedValue(undefined);

      await service['processTransfer'](FirestoreNftsUpdateInfo, NftType.CERTIFICATE);

      expect(service['deleteNftFromAccount']).toHaveBeenCalledWith(FirestoreNftsUpdateInfo, 'accountFrom');
    });

    test('should handle ERC1155 transfer for COUPON type', async () => {
      const txUpdateInfoErc1155 = {
        ...FirestoreNftsUpdateInfo,
        nftType: NftType.COUPON,
      };
      const mockAccountFrom = { account_id: 'accountFrom' } as unknown as AccountEntity;
      const mockAccountTo = { account_id: 'accountTo' } as unknown as AccountEntity;

      mockAccountRepository.selectAccountByTokenBoundAddress.mockResolvedValueOnce(mockAccountFrom);
      mockAccountRepository.selectAccountByTokenBoundAddress.mockResolvedValueOnce(mockAccountTo);

      jest.spyOn(service as any, 'handleErc1155Transfer').mockResolvedValue(undefined);

      await service['processTransfer'](txUpdateInfoErc1155, NftType.COUPON);

      expect(service['handleErc1155Transfer']).toHaveBeenCalledWith(txUpdateInfoErc1155, 'accountFrom', 'accountTo');
    });
  });

  describe('handleTransactionType', () => {
    test('should process minting if from address is zero', async () => {
      const FirestoreNftsUpdateInfo: FirestoreNftsUpdateInfo = {
        serviceId: 'service1',
        tokenId: '1',
        from: '0x0000000000000000000000000000000000000000',
        to: '0x1111',
        amount: '1',
        contractAddress: '0x123',
        transactionHash: '0xabc',
        nftType: NftType.CONTENT,
        timestamp: '**********',
        isConfirmed: true,
      };
      const txData: TxDetail = {
        nftTokenId: '1',
        nftFrom: '0x0000000000000000000000000000000000000000',
        nftTo: '0x1111',
        nftAmount: '1',
        nftContractAddress: '0x123',
        transactionHash: '0xabc',
        timestamp: '**********',
        isConfirmed: true,
        type: WebhookTxType.NFT_TRANSFER,
      };
      const mockContractType: NftContractJoinNftContractType = {
        nft_type: NftType.CONTENT,
        service_id: 'service1',
        nft_contract_id: '',
        nft_contract_type_id: undefined,
        nft_collection_name: undefined,
        nft_contract_address: undefined,
        nft_contract_type_name: '',
        nft_contract_type_detail: '',
        nft_contract_abi: {},
        nft_contract_binary: '',
      };

      mockNftContractsRepository.selectNftContractAndTypeByAddress.mockResolvedValue(mockContractType);
      jest.spyOn(service as any, 'processMint').mockResolvedValue(undefined);

      await service['handleTransactionType'](txData);

      expect(mockNftContractsRepository.selectNftContractAndTypeByAddress).toHaveBeenCalledWith(
        FirestoreNftsUpdateInfo.contractAddress,
      );
      expect(service['processMint']).toHaveBeenCalledWith(FirestoreNftsUpdateInfo, NftType.CONTENT);
    });

    test('should process transfer if not minting or burning', async () => {
      const FirestoreNftsUpdateInfo: FirestoreNftsUpdateInfo = {
        serviceId: 'service2',
        tokenId: '1',
        from: '0x1234',
        to: '0x1111',
        amount: '2',
        contractAddress: '0x123',
        transactionHash: '0xabc',
        nftType: NftType.CERTIFICATE,
        timestamp: new Date('2025-01-01T00:00:00Z').toISOString(),
        isConfirmed: true,
      };
      const txData: TxDetail = {
        nftTokenId: '1',
        nftFrom: '0x1234',
        nftTo: '0x1111',
        nftAmount: '2',
        nftContractAddress: '0x123',
        transactionHash: '0xabc',
        timestamp: new Date('2025-01-01T00:00:00Z').toISOString(),
        isConfirmed: true,
        type: WebhookTxType.NFT_TRANSFER,
      };
      const mockContractType: NftContractJoinNftContractType = {
        nft_type: NftType.CERTIFICATE,
        service_id: 'service2',
        nft_contract_id: '',
        nft_contract_type_id: undefined,
        nft_collection_name: undefined,
        nft_contract_address: undefined,
        nft_contract_type_name: '',
        nft_contract_type_detail: '',
        nft_contract_abi: {},
        nft_contract_binary: '',
      };

      jest.spyOn(service as any, 'processTransfer').mockResolvedValue(undefined);
      mockNftContractsRepository.selectNftContractAndTypeByAddress.mockResolvedValue(mockContractType);
      await service['handleTransactionType'](txData);

      expect(mockNftContractsRepository.selectNftContractAndTypeByAddress).toHaveBeenCalledWith(
        FirestoreNftsUpdateInfo.contractAddress,
      );
      expect(service['processTransfer']).toHaveBeenCalledWith(FirestoreNftsUpdateInfo, NftType.CERTIFICATE);
    });
  });

  describe('updateTxFinalityStatus', () => {
    const minedTx: TransactionsEntity = {
      transaction_id: 'mined_tx_id',
      service_id: 'sample_service_id',
      encoded_data: '0xabcdef',
      nonce: 5,
      tx_hash: '0xabc',
      status: TransactionStatus.MINED,
      created_date: new Date(),
    };

    it('should update tx status if tx is finalized', async () => {
      mockTransactionsRepository.selectTransactionsByStatus.mockResolvedValue([minedTx]);
      mockViemComponent.getBlock.mockResolvedValue(mockBlock);
      mockViemComponent.getTransaction.mockResolvedValue({ success: true, value: mockErc721MintTx });

      await service['updateTxFinalityStatus']();
      expect(mockTransactionsRepository.updateTransactionStatus).toHaveBeenCalledWith(
        'mined_tx_id',
        TransactionStatus.CONFIRMED,
        undefined,
      );
      expect(mockNftsFirestoreRepository.updateTransactionConfirmation).toHaveBeenCalledWith(minedTx.tx_hash, true);
    });

    it('should retry transaction if tx is reorged', async () => {
      const mockTxQueue: TransactionQueuesEntity = {
        queue_id: 'queue_id',
        service_id: 'sample_service_id',
        from_address: '0x123MockFrom',
        to_address: '0x456MockTo',
        status: TransactionQueueStatus.PENDING,
        tx_type: TxType.MINT_REWARD,
        nft_type: NftType.CONTENT,
        nft_contract_address: 'nft_contract_address',
        token_id: 1,
        created_date: new Date(),
      };
      const serviceData: ServiceEntity = {
        service_id: 'serviceId',
        tenant_id: 'sample_tenant_id',
        service_url: 'https://test-service.com',
        service_logo_image_url: 'https://marbullx.com/logo',
        market_cover_image_url: 'https://example.com/market-cover.jpg',
        theme_primary_color_lowest: '0xFFFFFFFFFFFF',
        theme_primary_color_lower: '0xFFFFFFFFFFFF',
        theme_primary_color_higher: '0xFFFFFFFFFFFF',
        theme_primary_color_highest: '0xFFFFFFFFFFFF',
        is_market_enabled: true,
        membership_nft_contract_id: '0x1234567890abcdef',
        stripe_account_id: 'stripe_account_id',
        line_channel_id: 'lineChannelId',
        commission_rate: 0.1,
        modular_contract_id: 'modular_contract_id',
      };
      const mockNftContract: NftContractJoinNftContractType = {
        nft_type: NftType.CONTENT,
        service_id: 'serviceId',
        nft_contract_id: '0x1234567890abcdef',
        nft_contract_type_id: '0x1234567890abcdef',
        nft_contract_address: '0x1234567890abcdef',
        nft_contract_abi: {},
        nft_contract_binary: '',
        nft_contract_type_name: '',
        nft_contract_type_detail: '',
        nft_collection_name: '',
      };
      const mockAccount: AccountEntity = {
        account_id: 'accountId',
        service_id: 'serviceId',
        user_id: 'userId',
        membership_id: 11,
        display_name: 'displayName',
        profile_image_url: 'https://example.com/profile-image.jpg',
        token_bound_account_address: '0x1234567890abcdef',
        status: AccountStatus.ACTIVE,
        transaction_id: 'transactionId',
        queue_id: 'queueId',
        created_at: new Date(),
        updated_at: new Date(),
        last_login_at: new Date('2020/01/01 00:00:00'),
        membership_metadata_url: 'http://example.com/membership_metadata.json',
      };
      mockTransactionsRepository.selectTransactionsByStatus.mockResolvedValue([minedTx]);
      mockViemComponent.getBlock.mockResolvedValue(mockBlock);
      mockViemComponent.getTransaction.mockResolvedValue({ success: false, error: 'TransactionNotFoundError' });
      mockTransactionQueuesRepository.selectQueueByTransactionId.mockResolvedValue([mockTxQueue]);
      mockServiceInfoRepository.getServiceById.mockResolvedValue({
        ...serviceData,
        tenant_id: 'sample_tenant_id',
      });
      mockNftContractsRepository.selectNftContractById.mockResolvedValue(mockNftContract);
      mockNftContractsRepository.selectNftContractAndTypeByAddress.mockResolvedValue(mockNftContract);
      mockTransactionComponent.sendRawTransaction.mockResolvedValue({
        to: '0xa69326Fcf86aD4Af9CE4d04Ff416d413d9B80D6f',
        from: '0x3c9767abe08DE3DeFE7D7ee38779864B35B3ff53',
        hash: '0x4f2728408d42dd630ef96c7e2e3a6253bb9402e8ddc381ecb84ba4e8118dd13c',
        nonce: 2,
        wait: jest.fn().mockResolvedValue({ status: 1 }), // success
      } as any);
      mockNftContractsRepository.selectDeliveryImageUrl.mockResolvedValue('https://example.com/delivery-image.jpg');

      mockAccountRepository.selectAccountByTokenBoundAddress.mockResolvedValue(mockAccount);
      await service.updateTxFinalityStatus();
      expect(mockTransactionComponent.sendRawTransaction).toHaveBeenCalledWith(
        'sample_tenant_id',
        '0x1234567890abcdef',
        '0xabcdef',
        5,
      );
      expect(mockTransactionsRepository.updateTransactionHash).toHaveBeenCalledWith(
        'mined_tx_id',
        '0x4f2728408d42dd630ef96c7e2e3a6253bb9402e8ddc381ecb84ba4e8118dd13c',
        undefined,
      );
      expect(mockTransactionsRepository.updateTransactionStatus).toHaveBeenCalledWith(
        'mined_tx_id',
        TransactionStatus.EXECUTED,
        undefined,
      );
      expect(mockDeliveryNftsFirestoreRepository.insertDeliveryNft).toHaveBeenCalledWith(
        NftType.CONTENT,
        'nft_contract_address',
        mockTxQueue.token_id?.toString(),
        '0x456MockTo',
        'sample_service_id',
        'accountId',
        'queue_id',
        'https://example.com/delivery-image.jpg',
        '0x4f2728408d42dd630ef96c7e2e3a6253bb9402e8ddc381ecb84ba4e8118dd13c',
      );
    });
  });
});
