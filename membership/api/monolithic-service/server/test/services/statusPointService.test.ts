
import { PointOps } from '../../src/enum/pointOpsType';
import { pointOpsActor } from '../../src/enum/pointOpsActor';
import { pointTxDetail } from '../../src/enum/pointTxDetail';
import { StatusPointService } from '../../src/services/statusPointService';
import { StatusPointTxsRepository } from '../../src/repositories/statusPointTxsRepository';
import { StatusPointTxEntity } from '../../src/tables/statusPointTxs';
import { RedisComponent } from '../../src/components/redisComponent';

jest.mock('../../src/repositories/statusPointTxsRepository');
jest.mock('uuid', () => ({
  v4: jest.fn().mockReturnValue('status_point_tx_id'),
}));
jest.useFakeTimers();
jest.setSystemTime(new Date('2025-01-02T00:00:00.000Z'));
jest.mock('../../src/components/redisComponent');

describe('StatusPointService', () => {
  let statusPointService: StatusPointService;
  let mockStatusPointTxsRepository: jest.Mocked<StatusPointTxsRepository>;
  let mockRedisComponent: jest.Mocked<RedisComponent>;

  beforeEach(() => {
    mockStatusPointTxsRepository = new StatusPointTxsRepository() as jest.Mocked<StatusPointTxsRepository>;
    mockRedisComponent = new RedisComponent() as jest.Mocked<RedisComponent>;
    statusPointService = new StatusPointService(mockStatusPointTxsRepository, mockRedisComponent);
  });

  describe('adjustRewardPoints', () => {
    const serviceId = 'F8DAFCE9-C629-4E39-9F75-37C93D8BC3AF';
    const accountId = 'bc6650a9-57e4-4940-b5dd-2b37b7347b06';
    const amount = 100;
    const pointOpsType = PointOps.ADD;

    const mockStatusPointTx: StatusPointTxEntity = {
      status_point_tx_id: 'status_point_tx_id',
      service_id: serviceId,
      account_id: accountId,
      amount: amount,
      ops_type: pointOpsType,
      tx_by: pointOpsActor.ADMIN,
      tx_detail: pointTxDetail.ADJUST,
      tx_extra: undefined,
      created_at: new Date(),
    };

    it('should add reward points', async () => {
      mockStatusPointTxsRepository.insertStatusPointTx.mockResolvedValue(mockStatusPointTx);
      const result = await statusPointService.adjustStatusPoints(serviceId, accountId, amount, pointOpsType);
      expect(mockStatusPointTxsRepository.insertStatusPointTx).toHaveBeenCalledWith(serviceId, mockStatusPointTx);
      expect(result).toEqual({
        ...mockStatusPointTx,
        created_at: mockStatusPointTx.created_at.toISOString(),
      });
    });
  });

  afterAll(() => {
    jest.useRealTimers();
  });
});
