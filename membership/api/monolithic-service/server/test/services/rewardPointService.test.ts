import { RedisComponent } from '../../src/components/redisComponent';
import { RewardPointService } from '../../src/services/rewardPointService';
import { ServiceInfoRepository } from '../../src/repositories/serviceInfoRepository';
import { RewardPointTxsRepository } from '../../src/repositories/rewardPointTxsRepository';
import { PointOpsType } from '../../src/enum/pointOpsType';
import { RewardPointTxEntity } from '../../src/tables/rewardPointTxs';
import { PointOpsActor } from '../../src/enum/pointOpsActor';
import { PointTxDetail } from '../../src/enum/pointTxDetail';
import { Transaction } from 'kysely';
import { OperationStatus } from '../../src/enum/operationStatus';

jest.mock('../../src/db/database', () => ({
  db: {
    selectFrom: jest.fn(),
    transaction: jest.fn().mockReturnValue({
      execute: jest.fn().mockImplementation(async (callback) => {
        const mockTransaction = {} as Transaction<unknown>;
        return await callback(mockTransaction);
      }),
    } as unknown as jest.Mocked<Transaction<unknown>>),
  },
}));
jest.mock('kysely', () => ({
  sql: jest.fn(() => ({
    execute: jest.fn().mockResolvedValue({ rows: [{ pg_try_advisory_xact_lock: true }] }),
  })),
}));
jest.mock('../../src/repositories/rewardPointTxsRepository');
jest.mock('../../src/repositories/serviceInfoRepository');
jest.mock('../../src/components/redisComponent');
jest.mock('uuid', () => ({
  v4: jest.fn().mockReturnValue('reward_point_tx_id'),
}));
jest.useFakeTimers();
jest.setSystemTime(new Date('2025-01-02T00:00:00.000Z'));

describe('RewardPointService', () => {
  let rewardPointService: RewardPointService;
  let mockRedisComponent: jest.Mocked<RedisComponent>;
  let mockServiceInfoRepository: jest.Mocked<ServiceInfoRepository>;
  let mockRewardPointTxsRepository: jest.Mocked<RewardPointTxsRepository>;

  beforeEach(() => {
    mockRedisComponent = new RedisComponent() as jest.Mocked<RedisComponent>;
    mockServiceInfoRepository = new ServiceInfoRepository() as jest.Mocked<ServiceInfoRepository>;
    mockRewardPointTxsRepository = new RewardPointTxsRepository() as jest.Mocked<RewardPointTxsRepository>;

    rewardPointService = new RewardPointService(
      mockRedisComponent,
      mockServiceInfoRepository,
      mockRewardPointTxsRepository,
    );
  });

  describe('adjustRewardPoints', () => {
    const serviceId = 'F8DAFCE9-C629-4E39-9F75-37C93D8BC3AF';
    const accountId = 'bc6650a9-57e4-4940-b5dd-2b37b7347b06';
    const amount = 100;
    const pointOpsType = PointOpsType.ADD;
    const expiresOn = '2025-01-01T00:00:00Z';

    const mockRewardPointTx: RewardPointTxEntity = {
      reward_point_tx_id: 'reward_point_tx_id',
      service_id: serviceId,
      account_id: accountId,
      amount: amount,
      ops_type: PointOpsType.ADD,
      tx_by: PointOpsActor.ADMIN,
      tx_detail: PointTxDetail.ADJUST,
      tx_extra: undefined,
      expires_on: new Date(expiresOn),
      created_at: new Date(),
    };

    it('should add reward points', async () => {
      mockRewardPointTxsRepository.insertRewardPointTx.mockResolvedValue(mockRewardPointTx);
      const result = await rewardPointService.adjustRewardPoints(serviceId, accountId, amount, pointOpsType, expiresOn);
      expect(mockRewardPointTxsRepository.insertRewardPointTx).toHaveBeenCalledWith(serviceId, mockRewardPointTx, {});
      expect(result).toEqual({
        ...mockRewardPointTx,
        expires_on: mockRewardPointTx.expires_on?.toISOString(),
        created_at: mockRewardPointTx.created_at.toISOString(),
      });
    });
  });

  describe('expireRewardPoints', () => {
    const serviceId = 'F8DAFCE9-C629-4E39-9F75-37C93D8BC3AF';
    const mockServiceList = [
      {
        service_id: 'F8DAFCE9-C629-4E39-9F75-37C93D8BC3AF',
        tenant_id: '123',
        service_url: 'https://test.com',
        service_logo_image_url: 'https://test.com/logo.png',
        market_cover_image_url: 'https://test.com/market.png',
        theme_primary_color_lowest: '#000000',
        theme_primary_color_highest: '#FFFFFF',
        theme_primary_color_lower: '#000000',
        theme_primary_color_higher: '#FFFFFF',
        membership_nft_contract_id: '123',
        is_market_enabled: true,
        modular_contract_id: '123',
        stripe_account_id: '123',
        line_channel_id: '123',
        commission_rate: 10,
      },
    ];
    const mockExpirePointsTxs = [
      {
        reward_point_tx_id: 'reward_point_tx_id',
        service_id: 'F8DAFCE9-C629-4E39-9F75-37C93D8BC3AF',
        account_id: '123',
        amount: 100,
        ops_type: PointOpsType.ADD,
        tx_by: PointOpsActor.ADMIN,
        tx_detail: PointTxDetail.ADJUST,
        tx_extra: undefined,
        expires_on: new Date('2025-01-01T00:00:00Z'),
        created_at: new Date('2024-01-01T00:00:00Z'),
      },
      {
        reward_point_tx_id: 'reward_point_tx_id',
        service_id: 'F8DAFCE9-C629-4E39-9F75-37C93D8BC3AF',
        account_id: '456',
        amount: 100,
        ops_type: PointOpsType.ADD,
        tx_by: PointOpsActor.USER,
        tx_detail: PointTxDetail.REWARD,
        tx_extra: undefined,
        expires_on: new Date('2025-01-01T00:00:00Z'),
        created_at: new Date('2024-01-01T00:00:00Z'),
      },
    ];
    it('should expire reward points', async () => {
      mockRedisComponent.get.mockResolvedValueOnce('2025-01-01T00:00:00Z').mockResolvedValueOnce(null);
      mockServiceInfoRepository.getServiceList.mockResolvedValue(mockServiceList);
      mockRewardPointTxsRepository.findExpirePointsTxs.mockResolvedValue(mockExpirePointsTxs);
      mockRewardPointTxsRepository.getTotalPointsByAccountId.mockResolvedValueOnce(100).mockResolvedValueOnce(300);
      mockRewardPointTxsRepository.getUnexpiredPointsByAccountId.mockResolvedValueOnce(70).mockResolvedValueOnce(120);
      mockRewardPointTxsRepository.insertRewardPointTx
        .mockResolvedValueOnce({
          reward_point_tx_id: 'reward_point_tx_id',
          service_id: 'F8DAFCE9-C629-4E39-9F75-37C93D8BC3AF',
          account_id: '123',
          amount: -30,
          ops_type: PointOpsType.SUB,
          tx_by: PointOpsActor.SCHEDULER,
          tx_detail: PointTxDetail.EXPIRE,
          tx_extra: undefined,
          expires_on: new Date('2999-12-31T00:00:00Z'),
          created_at: new Date(),
        })
        .mockResolvedValueOnce({
          reward_point_tx_id: 'reward_point_tx_id',
          service_id: 'F8DAFCE9-C629-4E39-9F75-37C93D8BC3AF',
          account_id: '456',
          amount: -180,
          ops_type: PointOpsType.SUB,
          tx_by: PointOpsActor.SCHEDULER,
          tx_detail: PointTxDetail.EXPIRE,
          tx_extra: undefined,
          expires_on: new Date('2999-12-31T00:00:00Z'),
          created_at: new Date(),
        });
      const result = await rewardPointService.expireRewardPoints();
      expect(mockRewardPointTxsRepository.insertRewardPointTx).toHaveBeenNthCalledWith(
        1,
        serviceId,
        {
          reward_point_tx_id: 'reward_point_tx_id',
          service_id: 'F8DAFCE9-C629-4E39-9F75-37C93D8BC3AF',
          account_id: '123',
          amount: -30,
          ops_type: PointOpsType.SUB,
          tx_by: PointOpsActor.SCHEDULER,
          tx_detail: PointTxDetail.EXPIRE,
          expires_on: new Date('2999-12-31T00:00:00Z'),
          created_at: new Date(),
        },
        {},
      );
      expect(mockRewardPointTxsRepository.insertRewardPointTx).toHaveBeenNthCalledWith(
        2,
        serviceId,
        {
          reward_point_tx_id: 'reward_point_tx_id',
          service_id: 'F8DAFCE9-C629-4E39-9F75-37C93D8BC3AF',
          account_id: '456',
          amount: -180,
          ops_type: PointOpsType.SUB,
          tx_by: PointOpsActor.SCHEDULER,
          tx_detail: PointTxDetail.EXPIRE,
          expires_on: new Date('2999-12-31T00:00:00Z'),
          created_at: new Date(),
        },
        {},
      );
      expect(result).toEqual({
        status: OperationStatus.SUCCESS,
        startedAt: new Date('2025-01-02T00:00:00Z').toISOString(),
        endedAt: new Date('2025-01-02T00:00:00Z').toISOString(),
        processedCount: 2,
        failedAccounts: [],
      });
    });

    it('should do nothing if there are no expire points txs', async () => {
      mockRedisComponent.get.mockResolvedValue('2025-01-01T00:00:00Z');
      mockServiceInfoRepository.getServiceList.mockResolvedValue(mockServiceList);
      mockRewardPointTxsRepository.findExpirePointsTxs.mockResolvedValue([]);
      const result = await rewardPointService.expireRewardPoints();
      expect(result).toEqual({
        status: OperationStatus.SUCCESS,
        startedAt: new Date('2025-01-02T00:00:00Z').toISOString(),
        endedAt: new Date('2025-01-02T00:00:00Z').toISOString(),
        processedCount: 0,
        failedAccounts: [],
      });
      expect(mockRewardPointTxsRepository.getTotalPointsByAccountId).not.toHaveBeenCalled();
      expect(mockRewardPointTxsRepository.getUnexpiredPointsByAccountId).not.toHaveBeenCalled();
      expect(mockRewardPointTxsRepository.insertRewardPointTx).not.toHaveBeenCalled();
    });
  });

  afterAll(() => {
    jest.useRealTimers();
  });
});
