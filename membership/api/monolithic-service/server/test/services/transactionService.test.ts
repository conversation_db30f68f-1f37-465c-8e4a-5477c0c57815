/* eslint-disable @typescript-eslint/no-explicit-any */
import { TransactionService } from '../../src/services/transactionService';
import { TransactionComponent } from '../../src/components/transactionComponent';
import { ethers } from 'ethers';
import { ServiceInfoRepository } from '../../src/repositories/serviceInfoRepository';
import { AttemptTransactionsRepository } from '../../src/repositories/attemptTransactionsRepository';
import { TransactionsRepository } from '../../src/repositories/transactionsRepository';
import { TransactionQueuesRepository } from '../../src/repositories/transactionQueuesRepository';
import { NftContractsRepository } from '../../src/repositories/nftContractsRepository';

const CONTRACT_CALL_TX_RESPONSE_MOCK = {
  provider: {},
  blockNumber: null,
  blockHash: null,
  index: undefined,
  hash: '0x4f2728408d42dd630ef96c7e2e3a6253bb9402e8ddc381ecb84ba4e8118dd13c',
  type: 2,
  to: '******************************************',
  from: '******************************************',
  nonce: 2,
  gasLimit: 124842n,
  gasPrice: undefined,
  maxPriorityFeePerGas: 1500000n,
  maxFeePerGas: 1500894n,
  maxFeePerBlobGas: null,
  data: '0x40d097c30000000000000000000000003c9767abe08de3defe7d7ee38779864b35b3ff53',
  value: 0n,
  chainId: 84532n,
  signature: {
    r: '0x24f5f56caa7c3f1caa46ebaf74a61eb176571de7a1e1b65abf630798b414b992',
    s: '0x6ecefa804cf8633f7a11ce4629e376da502479a53129c1ad29e0745138716301',
    yParity: 1,
    networkV: null,
  },
  accessList: [],
  blobVersionedHashes: null,
};

const CONTRACT_CALL_TX_RECEIPT_MOCK = {
  _type: 'TransactionReceipt',
  blockHash: '0xbf2c404282e0d6274f1ea5d77f8ec0b8d68ab342706400d340f4723d3d22fcdf',
  blockNumber: 16610145,
  contractAddress: null,
  cumulativeGasUsed: '82446',
  from: '0x7882740d30b40eFA4EbeCc0BeFe5Ba41d750aE67',
  gasPrice: '30676250000',
  blobGasUsed: null,
  blobGasPrice: null,
  gasUsed: '61446',
  hash: '0x95c38480f684f2186405311bd8479bf6203148c6c716b3579107b1fb2f521701',
  index: 1,
  logs: [
    {
      _type: 'log',
      address: '0xC673Bb7FfBa099C9f0ebFe59a6beC251C3309000',
      blockHash: '0xbf2c404282e0d6274f1ea5d77f8ec0b8d68ab342706400d340f4723d3d22fcdf',
      blockNumber: 16610145,
      data: '0x',
      index: 2,
      topics: [
        '0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef',
        '0x0000000000000000000000000000000000000000000000000000000000000000',
        '0x0000000000000000000000007882740d30b40efa4ebecc0befe5ba41d750ae67',
        '0x000000000000000000000000000000000000000000000000000000000000000d',
      ],
      transactionHash: '0x95c38480f684f2186405311bd8479bf6203148c6c716b3579107b1fb2f521701',
      transactionIndex: 1,
    },
    {
      _type: 'log',
      address: '0x0000000000000000000000000000000000001010',
      blockHash: '0xbf2c404282e0d6274f1ea5d77f8ec0b8d68ab342706400d340f4723d3d22fcdf',
      blockNumber: 16610145,
      data: '0x0000000000000000000000000000000000000000000000000006b2562157810600000000000000000000000000000000000000000000000092b35cbde67e5e1c0000000000000000000000000000000000000000000004be0edb6faa9ca35a5e00000000000000000000000000000000000000000000000092acaa67c526dd160000000000000000000000000000000000000000000004be0ee22200bdfadb64',
      index: 3,
      topics: [
        '0x4dfe1bbbcf077ddc3e01291eea2d5c70c2b422b415d95645b9adcfd678cb1d63',
        '0x0000000000000000000000000000000000000000000000000000000000001010',
        '0x0000000000000000000000007882740d30b40efa4ebecc0befe5ba41d750ae67',
        '0x0000000000000000000000004ad84f7014b7b44f723f284a85b1662337971439',
      ],
      transactionHash: '0x95c38480f684f2186405311bd8479bf6203148c6c716b3579107b1fb2f521701',
      transactionIndex: 1,
    },
  ],
  logsBloom:
    '0x00000000000000000040000000000000000000000000000000000000000000000000000000000000000000000000000000008000000000000000000000400000000000000000000000000008000000800002000000000000000100001000000000000000020001000000000000000800000000000000000080000010000000000000000000000000000000000000000000000000000000000000000000000000208000000000000000000000000000000000000000400000000000000000004000000002100000000001000000000000000000000000000000100000000020020000000200200000000000000000008000000000000000000000000000100000',
  status: 1,
  to: '0xC673Bb7FfBa099C9f0ebFe59a6beC251C3309000',
};

const CONTRACT_DEPLOY_TX_RECEIPT_MOCK = {
  provider: {},
  to: null,
  from: '******************************************',
  contractAddress: '0x38e30e3b0B7C2ea0A7E406EEEA589FBDD2e557aB',
  hash: '0x4687e37d4f42b38cf9f4d3196a8d0fb3bbb43dea1128c8da1b9e387f19f89262',
  index: 3,
  blockHash: '0x716663de760550909ed90f5b343b50afbf0ef5831346868b712e0b407fc62a78',
  blockNumber: 17601897,
  logsBloom:
    '0x000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000',
  gasUsed: 2165440n,
  blobGasUsed: null,
  cumulativeGasUsed: 2287045n,
  gasPrice: 1500548n,
  blobGasPrice: null,
  type: 2,
  status: 1,
  root: undefined,
};

jest.mock('ethers', () => ({
  ethers: {
    Contract: jest.fn().mockImplementation(() => ({
      mint: Object.assign(jest.fn().mockResolvedValue(CONTRACT_CALL_TX_RESPONSE_MOCK), {
        estimateGas: jest.fn().mockResolvedValue(BigInt(1000000)), // Mock estimateGas for mint function
      }),
      interface: {
        encodeFunctionData: jest.fn().mockReturnValue('encoded_data'),
      },
    })),
    ContractFactory: jest.fn().mockImplementation(() => ({
      getDeployTransaction: jest.fn().mockReturnValue({
        data: 'deploy_data',
      }),
      deploy: jest.fn().mockResolvedValue({
        deploymentTransaction: jest.fn().mockReturnValue({
          wait: jest.fn().mockResolvedValue(CONTRACT_DEPLOY_TX_RECEIPT_MOCK),
        }),
      }),
      interface: {
        encodeFunctionData: jest.fn().mockReturnValue('encoded_data'),
      },
    })),
    parseUnits: jest.fn((value, unit) => {
      if (unit === 'ether') {
        return BigInt(Number(value) * 1e18);
      }
      return BigInt(value);
    }),
    formatUnits: jest.fn((value, unit) => {
      if (unit === 'ether') {
        return (Number(value) / 1e18).toString();
      }
      return value.toString();
    }),
    formatEther: jest.fn((wei) => {
      return (Number(wei) / 1e18).toString();
    }),
  },
}));
jest.mock('../../src/components/cloudTaskComponent', () => ({
  CloudTaskComponent: jest.fn().mockImplementation(() => ({
    enqueueMintTask: jest.fn().mockResolvedValue(undefined)
  }))
}));

const mockTransactionComponentImpl = {
  getSigner: jest.fn().mockResolvedValue({
    connect: jest.fn().mockReturnThis(),
    getAddress: jest.fn().mockResolvedValue('0x123'),
    signTransaction: jest.fn().mockResolvedValue('signed_transaction'),
    signMessage: jest.fn().mockResolvedValue('signed_message'),
    sendTransaction: jest.fn().mockResolvedValue(CONTRACT_CALL_TX_RESPONSE_MOCK),
  }),
  getTransactionInfo: jest.fn().mockResolvedValue({
    gasPrice: BigInt(2000000),
    info: {
      nonce: 5,
      maxFeePerGas: BigInt(20000000000),
      maxPriorityFeePerGas: BigInt(1000000000),
    },
  }),
  sendRawTransaction: jest.fn().mockImplementation(() => ({
    ...CONTRACT_CALL_TX_RESPONSE_MOCK,
    wait: jest.fn().mockResolvedValue(CONTRACT_CALL_TX_RECEIPT_MOCK),
  })),
  estimateFunctionCallGas: jest.fn().mockResolvedValue(BigInt(1000000)),
  estimateDeployContractGas: jest.fn().mockResolvedValue(BigInt(1000000)),
  sendTransaction: jest.fn().mockResolvedValue(CONTRACT_CALL_TX_RESPONSE_MOCK),
};

jest.mock('../../src/components/transactionComponent', () => ({
  TransactionComponent: jest.fn().mockImplementation(() => mockTransactionComponentImpl),
}));

jest.mock('../../src/repositories/serviceInfoRepository', () => ({
  ServiceInfoRepository: jest.fn().mockImplementation(() => ({
    getServiceById: jest.fn(),
  })),
}));

jest.mock('../../src/components/viemComponent', () => ({
  ViemComponent: jest.fn().mockImplementation(() => ({
    getBlock: jest.fn(),
    getTransaction: jest.fn(),
  })),
}));

describe('TransactionService', () => {
  let transactionService: TransactionService;
  let mockTransactionComponent: TransactionComponent;
  let mockServiceInfoRepository: jest.Mocked<ServiceInfoRepository>;
  let mockAttemptTransactionsRepository: jest.Mocked<AttemptTransactionsRepository>;
  let mockTransactionsRepository: jest.Mocked<TransactionsRepository>;
  let mockTransactionQueuesRepository: jest.Mocked<TransactionQueuesRepository>;
  let mockNftContractsRepository: jest.Mocked<NftContractsRepository>;

  const dummySigner = {
    getAddress: jest.fn().mockResolvedValue('0x123'),
  };

  beforeEach(() => {
    process.env.JSON_RPC_URL = 'http://127.0.0.1:8545';
    process.env.ALCHEMY_API_KEY = 'xxxxxsss';
    process.env.ALCHEMY_CHAIN_NAME = 'polygon-amoy';
    process.env.GAS_LIMIT_MULTIPLIER = '200';
    process.env.BASE_MAX_FEE_PER_GAS = '600';
    process.env.MAX_FEE_PER_GAS_MULTIPLIER = '150';
    process.env.MAX_PRIORITY_FEE_PER_GAS_MULTIPLIER = '150';
    process.env.GCP_PROJECT_ID = 'sample_gcp_project_id';
    process.env.DESTINATION_ADDRESS = '0x7882740d30b40eFA4EbeCc0BeFe5Ba41d750aE67';

    mockTransactionComponent = mockTransactionComponentImpl as unknown as TransactionComponent;
    mockServiceInfoRepository = new ServiceInfoRepository() as jest.Mocked<ServiceInfoRepository>;
    mockAttemptTransactionsRepository = new AttemptTransactionsRepository() as jest.Mocked<AttemptTransactionsRepository>;
    mockTransactionsRepository = new TransactionsRepository() as jest.Mocked<TransactionsRepository>;
    mockTransactionQueuesRepository = new TransactionQueuesRepository() as jest.Mocked<TransactionQueuesRepository>;
    mockNftContractsRepository = new NftContractsRepository() as jest.Mocked<NftContractsRepository>;
    transactionService = new TransactionService(
      mockTransactionComponent,
      mockAttemptTransactionsRepository,
      mockTransactionsRepository,
      mockTransactionQueuesRepository,
      mockServiceInfoRepository,
      mockNftContractsRepository,
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('callContractFunction() method', () => {
    it('should return a transaction response', async () => {
      const result = await transactionService.callContractFunction({
        tenantId: 'sample_tenant_id',
        contractAddress: 'sample_contract_address',
        contractABI: 'sample_abi',
        functionName: 'mint',
        args: ['sample_arg1', 'sample_arg2'],
      });
      expect(result).toEqual(CONTRACT_CALL_TX_RESPONSE_MOCK);
    });
  });

  describe('prepareDeployContract()', () => {
    mockTransactionComponent = {
      getSigner: jest.fn().mockResolvedValue(dummySigner),
    } as unknown as TransactionComponent;

    it('should return deploy transaction data', async () => {
      const tenantId = 'test_tenant';
      const contractBytecode = '0xcontractBytecode';
      const contractABI = 'sample_abi';
      const constructorArgs = ['arg1', 'arg2'];
      const transaction = {};
      const mockDeployTx = { data: 'deploy_data' };

      const mockFactory = {
        getDeployTransaction: jest.fn().mockResolvedValue(mockDeployTx),
      };
      const factorySpy = jest.spyOn(ethers, 'ContractFactory').mockImplementation(() => mockFactory as any);

      (mockTransactionComponent.getSigner as jest.Mock).mockResolvedValue(dummySigner);

      const result = await transactionService.prepareDeployContract({
        tenantId,
        contractBytecode,
        contractABI,
        constructorArgs,
        transaction,
      });

      expect(result).toBe('deploy_data');
      expect(mockFactory.getDeployTransaction).toHaveBeenCalledWith(...constructorArgs);

      factorySpy.mockRestore();
    });

    it('should throw InternalServerError if getSigner fails', async () => {
      const tenantId = 'test_tenant';
      const contractBytecode = '0xcontractBytecode';
      const contractABI = 'sample_abi';
      const constructorArgs = ['arg1'];
      const transaction = {};

      (mockTransactionComponent.getSigner as jest.Mock).mockRejectedValue(new Error('Signer error'));
      await expect(
        transactionService.prepareDeployContract({
          tenantId,
          contractBytecode,
          contractABI,
          constructorArgs,
          transaction,
        }),
      ).rejects.toThrow('Error generating call data for contract deployment: Error: Signer error');
    });
  });

  describe('prepareDeployUpgradeableContract()', () => {
    mockTransactionComponent = {
      getSigner: jest.fn().mockResolvedValue(dummySigner),
    } as unknown as TransactionComponent;

    it('should return upgradeable deploy call data', async () => {
      const tenantId = 'test_tenant';
      const implementationContractAddress = '0ximpl';
      const implementationByteCode = '0ximplBytecode';
      const implementationABI = 'impl_abi';
      const proxyByteCode = '0xproxyBytecode';
      const proxyABI = 'proxy_abi';
      const initializeArgs = ['initArg1', 'initArg2'];
      const dummyInitializeData = 'initialize_data';
      const mockImplementationFactory = {
        interface: {
          encodeFunctionData: jest.fn().mockReturnValue(dummyInitializeData),
        },
      };
      const mockProxyFactory = {
        getDeployTransaction: jest.fn().mockResolvedValue({ data: 'upgradeable_deploy_data' }),
      };
      (mockTransactionComponent.getSigner as jest.Mock).mockResolvedValue(dummySigner);

      const factorySpy = jest.spyOn(ethers, 'ContractFactory');
      factorySpy
        .mockImplementationOnce(() => mockImplementationFactory as any)
        .mockImplementationOnce(() => mockProxyFactory as any);

      const result = await transactionService.prepareDeployUpgradeableContract({
        tenantId,
        implementationContractAddress,
        implementationByteCode,
        implementationABI,
        proxyByteCode,
        proxyABI,
        initializeArgs,
      });

      expect(mockImplementationFactory.interface.encodeFunctionData).toHaveBeenCalledWith('initialize', initializeArgs);
      expect(mockProxyFactory.getDeployTransaction).toHaveBeenCalledWith(
        implementationContractAddress,
        dummyInitializeData,
      );
      expect(result).toBe('upgradeable_deploy_data');

      factorySpy.mockRestore();
    });

    it('should throw InternalServerError if getSigner fails in upgradeable deployment', async () => {
      const tenantId = 'test_tenant';
      const implementationContractAddress = '0ximpl';
      const implementationByteCode = '0ximplBytecode';
      const implementationABI = 'impl_abi';
      const proxyByteCode = '0xproxyBytecode';
      const proxyABI = 'proxy_abi';
      const initializeArgs = ['initArg1'];

      (mockTransactionComponent.getSigner as jest.Mock).mockRejectedValue(new Error('Signer error'));

      await expect(
        transactionService.prepareDeployUpgradeableContract({
          tenantId,
          implementationContractAddress,
          implementationByteCode,
          implementationABI,
          proxyByteCode,
          proxyABI,
          initializeArgs,
        }),
      ).rejects.toThrow('Error generating call data for upgradeable contract deployment: Error: Signer error');
    });
  });
});
