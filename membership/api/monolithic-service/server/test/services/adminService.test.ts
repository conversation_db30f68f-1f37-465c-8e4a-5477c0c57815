/* eslint-disable @typescript-eslint/no-explicit-any */
// import '@types/jest';
import { AdminService } from '../../src/services/adminService';
import { NftContractsRepository, NftContractJoinNftContractType } from '../../src/repositories/nftContractsRepository';
import { TransactionComponent } from '../../src/components/transactionComponent';
import { FirebaseComponent } from '../../src/components/firebaseComponent';
import { NotFoundError } from '../../src/errors/notFoundError';
import { InternalServerError } from '../../src/errors/internalServerError';
import { NftType } from '../../src/enum/nftType';
import axios from 'axios';
import { MetadataFetchService } from '../../src/services/metadataFetchService';
import { NftsFirestoreRepository } from '../../src/repositories/nftsFirestoreRepository';
import { RewardPointService } from '../../src/services/rewardPointService';
import { StatusPointService } from '../../src/services/statusPointService';
import { StatusPointTxsRepository } from '../../src/repositories/statusPointTxsRepository';
import { RewardPointTxsRepository } from '../../src/repositories/rewardPointTxsRepository';
import { RedisComponent } from '../../src/components/redisComponent';
import { ServiceInfoRepository } from '../../src/repositories/serviceInfoRepository';

jest.mock('axios');
jest.mock('../../src/repositories/nftsFirestoreRepository');
jest.mock('../../src/repositories/nftContractsRepository');
jest.mock('../../src/components/transactionComponent');
jest.mock('../../src/components/firebaseComponent');
process.env.ALCHEMY_CHAIN_NAME = 'eth-mainnet';
process.env.ALCHEMY_API_KEY = 'test_alchemy_api_key';

describe('AdminService', () => {
  let adminService: AdminService;
  let mockNftsFirestoreRepository: jest.Mocked<NftsFirestoreRepository>;
  let mockNftContractsRepository: jest.Mocked<NftContractsRepository>;
  let mockTransactionComponent: jest.Mocked<TransactionComponent>;
  let mockFirebaseComponent: jest.Mocked<FirebaseComponent>;
  let mockMetadataFetchService: jest.Mocked<MetadataFetchService>;
  let mockRewardPointService: jest.Mocked<RewardPointService>;
  let mockStatusPointService: jest.Mocked<StatusPointService>;
  let mockStatusPointTxsRepository: jest.Mocked<StatusPointTxsRepository>;
  let mockRedisComponent: jest.Mocked<RedisComponent>;
  let mockServiceInfoRepository: jest.Mocked<ServiceInfoRepository>;
  let mockRewardPointTxsRepository: jest.Mocked<RewardPointTxsRepository>;
  const mockContractAddress = '0x1234567890abcdef';
  const mockTokenId = '1';
  const mockTokenURI = 'ipfs://QmExample';
  const mockMetadata = { name: 'Test NFT' };


  // Common mock for a contract + type record
  const mockNftContractJoinNftContractType: NftContractJoinNftContractType = {
    nft_contract_id: 'contract_12345',
    service_id: 'service_67890',
    nft_contract_type_id: 'type_001',
    nft_collection_name: 'My NFT Collection',
    nft_contract_address: '0x1234567890abcdef1234567890abcdef12345678',
    nft_contract_type_name: 'ERC721',
    nft_contract_type_detail: 'Standard NFT contract for unique assets',
    nft_contract_abi: {
      functions: [
        {
          name: 'balanceOf',
          inputs: [{ name: 'owner', type: 'address' }],
          outputs: [{ name: 'balance', type: 'uint256' }],
        },
        {
          name: 'ownerOf',
          inputs: [{ name: 'tokenId', type: 'uint256' }],
          outputs: [{ name: 'owner', type: 'address' }],
        },
      ],
    },
    nft_contract_binary:
      '0x608060405234801561001057600080fd5b506040516020806101008339810180604052810190808051820192919060200180518201929190805190602001909291905050508060008190555050',
    nft_type: NftType.COUPON, // We will override this in tests as needed
  };

  beforeEach(() => {
    mockFirebaseComponent = new FirebaseComponent() as jest.Mocked<FirebaseComponent>;
    mockNftsFirestoreRepository = new NftsFirestoreRepository(
      mockFirebaseComponent,
    ) as jest.Mocked<NftsFirestoreRepository>;
    mockNftContractsRepository = new NftContractsRepository() as jest.Mocked<NftContractsRepository>;
    mockTransactionComponent = new TransactionComponent(null as any) as jest.Mocked<TransactionComponent>;
    mockMetadataFetchService = new MetadataFetchService(mockTransactionComponent) as jest.Mocked<MetadataFetchService>;
    mockRedisComponent = {} as unknown as jest.Mocked<RedisComponent>;
    mockServiceInfoRepository = new ServiceInfoRepository() as jest.Mocked<ServiceInfoRepository>;
    mockRewardPointTxsRepository = new RewardPointTxsRepository() as jest.Mocked<RewardPointTxsRepository>;
    mockRewardPointService = new RewardPointService(mockRedisComponent, mockServiceInfoRepository, mockRewardPointTxsRepository) as jest.Mocked<RewardPointService>;
    mockStatusPointTxsRepository = new StatusPointTxsRepository() as jest.Mocked<StatusPointTxsRepository>;
    mockStatusPointService = new StatusPointService(mockStatusPointTxsRepository, mockRedisComponent) as jest.Mocked<StatusPointService>;
    adminService = new AdminService(mockNftsFirestoreRepository, mockNftContractsRepository, mockMetadataFetchService, mockRewardPointService, mockStatusPointService);

    (axios.get as jest.Mock).mockReset();
    jest.clearAllMocks();
  });

  describe('updateFirestoreMetadata', () => {
    describe('when tokenIds are specified', () => {
      test('should update metadata for a COUPON-type NFT', async () => {
        const contractInfo = { ...mockNftContractJoinNftContractType, nft_type: NftType.COUPON };
        mockNftContractsRepository.selectNftContractAndTypeByAddress.mockResolvedValue(contractInfo);
        mockTransactionComponent.callViewFunction.mockResolvedValueOnce('uri1');
        mockTransactionComponent.callViewFunction.mockResolvedValueOnce('uri2');
        (axios.get as jest.Mock).mockResolvedValue({ data: mockMetadata });

        const result = await adminService.updateFirestoreMetadata(mockContractAddress, ['1', '2']);

        expect(mockNftContractsRepository.selectNftContractAndTypeByAddress).toHaveBeenCalledWith(mockContractAddress);
        expect(mockTransactionComponent.callViewFunction).toHaveBeenCalledWith(
          mockContractAddress,
          contractInfo.nft_contract_abi,
          'uri',
          ['1'],
        );
        expect(mockTransactionComponent.callViewFunction).toHaveBeenCalledWith(
          mockContractAddress,
          contractInfo.nft_contract_abi,
          'uri',
          ['2'],
        );
        expect(mockNftsFirestoreRepository.updateNftsMetadata).toHaveBeenCalledWith(mockContractAddress, {
          tokenId: '1',
          metadataJson: mockMetadata,
          metadataUri: 'uri1',
        });
        expect(mockNftsFirestoreRepository.updateNftsMetadata).toHaveBeenCalledWith(mockContractAddress, {
          tokenId: '2',
          metadataJson: mockMetadata,
          metadataUri: 'uri2',
        });
        expect(result).toEqual({
          updatedTokenUris: [
            { tokenId: '1', tokenUri: 'uri1' },
            { tokenId: '2', tokenUri: 'uri2' },
          ],
          failedIds: [],
        });
      });

      test('should update metadata for a non-COUPON (e.g. CONTENT) type NFT', async () => {
        const contractInfo = { ...mockNftContractJoinNftContractType, nft_type: NftType.CONTENT };
        mockNftContractsRepository.selectNftContractAndTypeByAddress.mockResolvedValue(contractInfo);
        mockTransactionComponent.callViewFunction.mockResolvedValue(mockTokenURI); // "tokenUri" call
        (axios.get as jest.Mock).mockResolvedValue({ data: mockMetadata });

        const result = await adminService.updateFirestoreMetadata(mockContractAddress, [mockTokenId]);

        expect(mockTransactionComponent.callViewFunction).toHaveBeenCalledWith(
          mockContractAddress,
          contractInfo.nft_contract_abi,
          'tokenURI', //
          [mockTokenId],
        );
        expect(mockNftsFirestoreRepository.updateNftsMetadata).toHaveBeenCalledWith(mockContractAddress, {
          tokenId: mockTokenId,
          metadataJson: mockMetadata,
          metadataUri: mockTokenURI,
        });
        expect(result.updatedTokenUris).toHaveLength(1);
        expect(result.failedIds).toHaveLength(0);
      });

      test('should handle failures to fetch tokenUri for some tokens', async () => {
        const contractInfo = { ...mockNftContractJoinNftContractType, nft_type: NftType.CONTENT };
        mockNftContractsRepository.selectNftContractAndTypeByAddress.mockResolvedValue(contractInfo);

        mockTransactionComponent.callViewFunction
          .mockResolvedValueOnce(mockTokenURI)
          .mockRejectedValueOnce(new Error('Failed to call tokenUri'));

        (axios.get as jest.Mock).mockResolvedValueOnce({ data: mockMetadata });

        const result = await adminService.updateFirestoreMetadata(mockContractAddress, ['1', '2']);

        expect(result.updatedTokenUris).toEqual([{ tokenId: '1', tokenUri: mockTokenURI }]);
        expect(result.failedIds).toEqual([{ tokenId: '2', errorMsg: 'Failed to fetch tokenUri' }]);
      });

      test('should handle failures to fetch metadata from tokenUri for some tokens', async () => {
        const contractInfo = { ...mockNftContractJoinNftContractType, nft_type: NftType.CONTENT };
        mockNftContractsRepository.selectNftContractAndTypeByAddress.mockResolvedValue(contractInfo);

        mockTransactionComponent.callViewFunction.mockResolvedValue(mockTokenURI);
        (axios.get as jest.Mock)
          .mockResolvedValueOnce({ data: mockMetadata })
          .mockRejectedValueOnce(new Error('Metadata fetch failed'));

        const result = await adminService.updateFirestoreMetadata(mockContractAddress, ['1', '2']);

        expect(result.updatedTokenUris).toEqual([{ tokenId: '1', tokenUri: mockTokenURI }]);
        expect(result.failedIds).toEqual([{ tokenId: '2', errorMsg: 'Failed to fetch metadataJson' }]);
        expect(mockNftsFirestoreRepository.updateNftsMetadata).toHaveBeenCalledTimes(1);
      });

      test('should handle firestore update failures for some tokens', async () => {
        const contractInfo = { ...mockNftContractJoinNftContractType, nft_type: NftType.CONTENT };
        mockNftContractsRepository.selectNftContractAndTypeByAddress.mockResolvedValue(contractInfo);

        mockTransactionComponent.callViewFunction.mockResolvedValue(mockTokenURI);
        (axios.get as jest.Mock).mockResolvedValue({ data: mockMetadata });

        mockNftsFirestoreRepository.updateNftsMetadata
          .mockResolvedValueOnce([])
          .mockRejectedValueOnce(new Error('Firestore update failed'));

        const result = await adminService.updateFirestoreMetadata(mockContractAddress, ['1', '2']);

        expect(result.updatedTokenUris).toEqual([{ tokenId: '1', tokenUri: mockTokenURI }]);
        expect(result.failedIds).toEqual([{ tokenId: '2', errorMsg: 'Failed to update Firestore' }]);
      });
    });

    describe('when tokenIds are NOT specified (use Alchemy API)', () => {
      const alchemyResponsePage1 = {
        nfts: Array.from({ length: 100 }, (_, index) => ({
          tokenId: (index + 1).toString(),
          raw: { tokenUri: `uri${index + 1}` },
        })),
      };
      const alchemyResponsePage2 = {
        nfts: [{ tokenId: '101', raw: { tokenUri: 'uri101' } }],
      };

      beforeEach(() => {
      });

      test('should fetch all NFTs from Alchemy, then batch update metadata', async () => {
        const contractInfo = { ...mockNftContractJoinNftContractType, nft_type: NftType.CONTENT };
        mockNftContractsRepository.selectNftContractAndTypeByAddress.mockResolvedValue(contractInfo);

        (axios.get as jest.Mock)
          .mockResolvedValueOnce({ data: alchemyResponsePage1 })
          .mockResolvedValueOnce({ data: alchemyResponsePage2 })
          .mockResolvedValue({ data: mockMetadata });

        const result = await adminService.updateFirestoreMetadata(mockContractAddress);

        expect((axios.get as jest.Mock).mock.calls[0][0]).toMatch(/getNFTsForCollection/);
        expect((axios.get as jest.Mock).mock.calls[1][0]).toMatch(/getNFTsForCollection/);
        expect((axios.get as jest.Mock).mock.calls[2][0]).toMatch('uri1');

        const expectedMetadatas = Array.from({ length: 101 }, (_, index) => ({
          tokenId: (index + 1).toString(),
          metadataJson: mockMetadata,
          metadataUri: `uri${index + 1}`,
        }));
        expect(mockNftsFirestoreRepository.batchUpdateNftsMetadata).toHaveBeenCalledWith(
          mockContractAddress,
          expectedMetadatas,
          expect.any(Array),
        );
        expect(result.updatedTokenUris).toHaveLength(101);
        expect(result.failedIds).toHaveLength(0);
      });

      test('should handle partial failures in metadata fetch from Alchemy-based list', async () => {
        const contractInfo = { ...mockNftContractJoinNftContractType, nft_type: NftType.CONTENT };
        mockNftContractsRepository.selectNftContractAndTypeByAddress.mockResolvedValue(contractInfo);

        const alchemySinglePage = {
          nfts: [
            { tokenId: '1', raw: { tokenUri: 'uri1' } },
            { tokenId: '2', raw: { tokenUri: 'uri2' } },
          ],
        };

        (axios.get as jest.Mock)
          .mockResolvedValueOnce({ data: alchemySinglePage })
          .mockResolvedValueOnce({ data: mockMetadata })
          .mockRejectedValueOnce(new Error('Metadata fetch failed'));

        const result = await adminService.updateFirestoreMetadata(mockContractAddress);

        expect(result.updatedTokenUris).toEqual([{ tokenId: '1', tokenUri: 'uri1' }]);
        expect(result.failedIds).toEqual([{ tokenId: '2', errorMsg: 'Failed to fetch metadataJson' }]);
        expect(mockNftsFirestoreRepository.batchUpdateNftsMetadata).toHaveBeenCalledWith(
          mockContractAddress,
          [{ tokenId: '1', metadataJson: mockMetadata, metadataUri: 'uri1' }],
          [{ tokenId: '2', errorMsg: 'Failed to fetch metadataJson' }],
        );
      });

      test('should throw InternalServerError if Alchemy API fails after retries', async () => {
        const contractInfo = { ...mockNftContractJoinNftContractType, nft_type: NftType.CONTENT };
        mockNftContractsRepository.selectNftContractAndTypeByAddress.mockResolvedValue(contractInfo);

        (axios.get as jest.Mock).mockRejectedValue(new Error('Alchemy call failed'));

        await expect(adminService.updateFirestoreMetadata(mockContractAddress)).rejects.toThrow(InternalServerError);
      });

      test('should throw InternalServerError if batchUpdateNftsMetadata fails', async () => {
        const contractInfo = { ...mockNftContractJoinNftContractType, nft_type: NftType.CONTENT };
        mockNftContractsRepository.selectNftContractAndTypeByAddress.mockResolvedValue(contractInfo);

        const alchemySinglePage = {
          nfts: [{ tokenId: '1', raw: { tokenUri: 'uri1' } }],
        };
        (axios.get as jest.Mock)
          .mockResolvedValueOnce({ data: alchemySinglePage }) // Alchemy
          .mockResolvedValueOnce({ data: mockMetadata }); // metadata

        mockNftsFirestoreRepository.batchUpdateNftsMetadata.mockRejectedValue(new Error('DB error'));

        await expect(adminService.updateFirestoreMetadata(mockContractAddress)).rejects.toThrow(InternalServerError);
      });
    });

    describe('error handling for contract lookup', () => {
      test('should throw NotFoundError if contract is not found in DB', async () => {
        mockNftContractsRepository.selectNftContractAndTypeByAddress.mockRejectedValue(new Error('not found'));
        await expect(adminService.updateFirestoreMetadata(mockContractAddress)).rejects.toThrow(NotFoundError);
      });

      test('should throw NotFoundError if no nftType or abi in the DB record', async () => {
        const missingDataContractInfo = {
          ...mockNftContractJoinNftContractType,
          nft_type: null,
          nft_contract_abi: null,
        };
        mockNftContractsRepository.selectNftContractAndTypeByAddress.mockResolvedValue(
          missingDataContractInfo as unknown as NftContractJoinNftContractType,
        );

        await expect(adminService.updateFirestoreMetadata(mockContractAddress)).rejects.toThrow(NotFoundError);
      });
    });
  });
});