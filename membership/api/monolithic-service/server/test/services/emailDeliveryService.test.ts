import { EmailDeliveryService } from '../../src/services/emailDeliveryService';
import { Resend } from 'resend';
import { EmailTemplateType, EMAIL_TEMPLATES } from '../../src/constants/emailTemplates';
import { languageCode } from '../../src/enum/languageCode';

jest.mock('resend', () => {
  const mockSend = jest.fn();
  return {
    Resend: jest.fn().mockImplementation(() => {
      return {
        emails: {
          send: mockSend
        }
      };
    })
  };
});

describe('EmailDeliveryService', () => {
  let emailDeliveryService: EmailDeliveryService;
  let mockSend: jest.Mock;

  beforeEach(() => {
    jest.clearAllMocks();
    mockSend = (new Resend('').emails.send as jest.Mock);
    emailDeliveryService = new EmailDeliveryService();
  });

  describe('sendEmail', () => {
    test('should return true when email is sent successfully', async () => {
      mockSend.mockResolvedValue({
        data: { id: 'email-id' },
        error: null
      });

      const result = await emailDeliveryService.sendEmail('<EMAIL>', {
        subject: 'Test Subject',
        html: '<p>Test Content</p>'
      });

      expect(result).toBe(true);
      expect(mockSend).toHaveBeenCalledWith(expect.objectContaining({
        to: ['<EMAIL>'],
        subject: 'Test Subject',
        html: '<p>Test Content</p>'
      }));
    });

    test('should return false when Resend returns an error', async () => {
      mockSend.mockResolvedValue({
        data: null,
        error: { message: 'Failed to send email' }
      });

      const result = await emailDeliveryService.sendEmail('<EMAIL>', {
        subject: 'Test Subject',
        html: '<p>Test Content</p>'
      });

      expect(result).toBe(false);
    });

    test('should return false when an exception occurs', async () => {
      mockSend.mockRejectedValue(new Error('Network error'));

      const result = await emailDeliveryService.sendEmail('<EMAIL>', {
        subject: 'Test Subject',
        html: '<p>Test Content</p>'
      });

      expect(result).toBe(false);
    });
  });

  describe('sendTemplatedEmail', () => {
    test('should use the correct template for English language', async () => {
      mockSend.mockResolvedValue({
        data: { id: 'email-id' },
        error: null
      });

      const result = await emailDeliveryService.sendTemplatedEmail(
        '<EMAIL>',
        EmailTemplateType.OTP_VERIFICATION,
        languageCode.EN_US,
        { otp: '123456' }
      );

      expect(result).toBe(true);
      expect(mockSend).toHaveBeenCalledWith(expect.objectContaining({
        to: ['<EMAIL>'],
        html: expect.stringContaining('123456')
      }));
    });

    test('should fall back to Japanese when language is not supported', async () => {
      mockSend.mockResolvedValue({
        data: { id: 'email-id' },
        error: null
      });

      const result = await emailDeliveryService.sendTemplatedEmail(
        '<EMAIL>',
        EmailTemplateType.OTP_VERIFICATION,
        'fr' as any,
        { otp: '123456' }
      );

      expect(result).toBe(true);
      expect(mockSend).toHaveBeenCalledWith(expect.objectContaining({
        to: ['<EMAIL>'],
        subject: EMAIL_TEMPLATES[EmailTemplateType.OTP_VERIFICATION][languageCode.JA]({ otp: '' }).subject,
        html: expect.stringContaining('123456')
      }));
    });
  });
});
