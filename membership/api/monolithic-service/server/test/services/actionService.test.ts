import { ActionService } from '../../src/services/actionService';
import { ActionRepository } from '../../src/repositories/actionRepository';
import { AchievementActionRepository } from '../../src/repositories/achievementActionRepository';
import { OnlineCheckinActionRepository } from '../../src/repositories/onlineCheckinActionRepository';
import { NotFoundError } from '../../src/errors/notFoundError';
import { ActionActivitiesStatus, ActionType } from '../../src/enum/actionType';
import { ActionEntity, ActionWithTranslations } from '../../src/tables/actionTable';
import { ActionActivityRepository } from '../../src/repositories/actionActivityRepository';
import { QuestRepository } from '../../src/repositories/questRepository';
import { QuestActivityRepository } from '../../src/repositories/questActivityRepository';
import { QrCheckinActionRepository } from '../../src/repositories/qrCheckinActionRepository';
import { ValidationError } from '../../src/errors/validationError';
import { QuestActivityStatus } from '../../src/enum/questActivityStatus';
import { QuestType } from '../../src/enum/questType';
import { QuestEntity, QuestWithTranslations } from '../../src/tables/questTable';
import { ConflictError } from '../../src/errors/conflictError';
import { ActionActivityEntity } from '../../src/tables/actionActivitesTable';
import { InternalServerError } from '../../src/errors/internalServerError';
import { QuestionnaireType } from '../../src/enum/questionnaireTypeEnum';
import { QuestionnaireQuestionRepository } from '../../src/repositories/questionnaireQuestionRepository';
import { QuestionnaireResultAnswerRepository } from '../../src/repositories/questionnaireResultAnswerRepository';
import { QuestionnaireResultRankRepository } from '../../src/repositories/questionnaireResultRankRepository';
import { QuestionnaireQuestionAnswerRepository } from '../../src/repositories/questionnaireQuestionAnswerRepository';
import { QuestionnaireStatus } from '../../src/enum/questionnaireStatusEnum';
import { QuestionType } from '../../src/enum/questionTypeEnum';
import { QuestionnaireRepository } from '../../src/repositories/questionnaireRepository';
import { QuestionnaireActionRepository } from '../../src/repositories/questionnaireActionRepository';
import { QuestionnaireAnswerEntity } from '../../src/tables/questionnaireResultAnswersTable';

import { SerialCodeProjectsRepository } from '../../src/repositories/serialCodeProjectsRepository';
import { SerialCodesRepository } from '../../src/repositories/serialCodesRepository';
import { AccountSerialCodesRepository } from '../../src/repositories/accountSerialCodesRepository';
import { RewardRepository } from '../../src/repositories/rewardRepository';
import { AccountRepository } from '../../src/repositories/accountRepository';
import { CertificateRewardRepository } from '../../src/repositories/certificateRewardRepository';
import { CouponRewardRepository } from '../../src/repositories/couponRewardRepository';
import { DigitalContentRewardRepository } from '../../src/repositories/digitalContentRewardRepository';
import { ClaimedRewardRepository } from '../../src/repositories/claimedRewardRepository';
import { NftContractsRepository } from '../../src/repositories/nftContractsRepository';
import { VaultKeyRepository } from '../../src/repositories/vaultKeyRepository';
import { ServiceInfoRepository } from '../../src/repositories/serviceInfoRepository';
import { NftMetadatasRepository } from '../../src/repositories/nftMetadatasRepository';
import { NftBaseMetadatasRepository } from '../../src/repositories/nftBaseMetadatasRepository';
import { NftContractTypesRepository } from '../../src/repositories/nftContractTypesRepository';
import { MetadataService } from '../../src/services/metadataService';
import { NftsService } from '../../src/services/nftsService';
import { NftMintService } from '../../src/services/nftMintService';
import { RewardService } from '../../src/services/rewardService';
import { SerialCodeService } from '../../src/services/serialCodeService';
import { TransactionQueuesRepository } from '../../src/repositories/transactionQueuesRepository';
import { languageCode } from '../../src/enum/languageCode';
import { ActionComplete, QuestionnaireAnswerRequest } from '../../src/dtos/accounts/schemas';
import { Transaction } from 'kysely';
import { SerialCodeActionRepository } from '../../src/repositories/serialCodeActionRepository';
import { DeliveryNftsFirestoreRepository } from '../../src/repositories/deliveryNftsFirestoreRepository';
import { FirebaseComponent } from '../../src/components/firebaseComponent';

jest.mock('../../src/repositories/actionRepository');
jest.mock('../../src/repositories/achievementActionRepository');
jest.mock('../../src/repositories/onlineCheckinActionRepository');
jest.mock('../../src/repositories/questionnaireRepository');
jest.mock('../../src/repositories/actionActivityRepository');
jest.mock('../../src/repositories/questRepository');
jest.mock('../../src/repositories/questActivityRepository');
jest.mock('../../src/repositories/qrCheckinActionRepository');
jest.mock('../../src/repositories/questionnaireQuestionRepository');
jest.mock('../../src/repositories/questionnaireResultAnswerRepository');
jest.mock('../../src/repositories/questionnaireResultRankRepository');
jest.mock('../../src/repositories/questionnaireQuestionAnswerRepository');
jest.mock('../../src/repositories/questionnaireActionRepository');
jest.mock('../../src/repositories/transactionQueuesRepository');

describe('ActionService', () => {
  let actionService: ActionService;
  let mockActionRepository: jest.Mocked<ActionRepository>;
  let mockActionActivityRepository: jest.Mocked<ActionActivityRepository>;
  let mockQuestRepository: jest.Mocked<QuestRepository>;
  let mockQuestActivityRepository: jest.Mocked<QuestActivityRepository>;
  let mockQrCheckinActionRepository: jest.Mocked<QrCheckinActionRepository>;
  let mockAchievementActionRepository: jest.Mocked<AchievementActionRepository>;
  let mockOnlineCheckinActionRepository: jest.Mocked<OnlineCheckinActionRepository>;
  let mockQuestionnaireRepository: jest.Mocked<QuestionnaireRepository>;
  let mockQuestionnaireActionRepository: jest.Mocked<QuestionnaireActionRepository>;
  let mockQuestionnaireQuestionRepository: jest.Mocked<QuestionnaireQuestionRepository>;
  let mockQuestionnaireQuestionAnswerRepository: jest.Mocked<QuestionnaireQuestionAnswerRepository>;
  let mockQuestionnaireResultAnswerRepository: jest.Mocked<QuestionnaireResultAnswerRepository>;
  let mockQuestionnaireResultRankRepository: jest.Mocked<QuestionnaireResultRankRepository>;

  let mockSerialCodeProjectsRepository: jest.Mocked<SerialCodeProjectsRepository>;
  let mockSerialCodesRepository: jest.Mocked<SerialCodesRepository>;
  let mockAccountSerialCodesRepository: jest.Mocked<AccountSerialCodesRepository>;
  let mockRewardRepository: jest.Mocked<RewardRepository>;
  let mockAccountRepository: jest.Mocked<AccountRepository>;
  let mockCertificateRewardRepository: jest.Mocked<CertificateRewardRepository>;
  let mockCouponRewardRepository: jest.Mocked<CouponRewardRepository>;
  let mockDigitalContentRewardRepository: jest.Mocked<DigitalContentRewardRepository>;
  let mockClaimedRewardRepository: jest.Mocked<ClaimedRewardRepository>;
  let mockNftContractsRepository: jest.Mocked<NftContractsRepository>;
  let mockVaultKeyRepository: jest.Mocked<VaultKeyRepository>;
  let mockServiceInfoRepository: jest.Mocked<ServiceInfoRepository>;
  let mockNftMetadatasRepository: jest.Mocked<NftMetadatasRepository>;
  let mockNftBaseMetadatasRepository: jest.Mocked<NftBaseMetadatasRepository>;
  let mockNftContractTypesRepository: jest.Mocked<NftContractTypesRepository>;
  let mockTransactionQueuesRepository: jest.Mocked<TransactionQueuesRepository>;
  let mockSerialCodeActionRepository: jest.Mocked<SerialCodeActionRepository>;
  let mockDeliveryNftsFirestoreRepository: jest.Mocked<DeliveryNftsFirestoreRepository>;

  let mockMetadataService: jest.Mocked<MetadataService>;
  let mockNftsService: jest.Mocked<NftsService>;
  let mockNftMintService: jest.Mocked<NftMintService>;
  let mockRewardService: jest.Mocked<RewardService>;
  let mockSerialCodeService: jest.Mocked<SerialCodeService>;

  beforeEach(() => {
    process.env.JSON_RPC_URL = 'http://127.0.0.1:8545';
    process.env.ALCHEMY_API_KEY = 'xxxxxsss';
    process.env.ALCHEMY_CHAIN_NAME = 'polygon-amoy';
    process.env.GAS_LIMIT_MULTIPLIER = '200';
    process.env.BASE_MAX_FEE_PER_GAS = '600';
    process.env.MAX_FEE_PER_GAS_MULTIPLIER = '150';
    process.env.MAX_PRIORITY_FEE_PER_GAS_MULTIPLIER = '150';
    process.env.GCP_PROJECT_ID = 'sample_gcp_project_id';

    mockActionRepository = new ActionRepository() as jest.Mocked<ActionRepository>;
    mockActionActivityRepository = new ActionActivityRepository() as jest.Mocked<ActionActivityRepository>;
    mockQuestRepository = new QuestRepository() as jest.Mocked<QuestRepository>;
    mockQuestActivityRepository = new QuestActivityRepository() as jest.Mocked<QuestActivityRepository>;
    mockQrCheckinActionRepository = new QrCheckinActionRepository() as jest.Mocked<QrCheckinActionRepository>;
    mockAchievementActionRepository = new AchievementActionRepository() as jest.Mocked<AchievementActionRepository>;
    mockOnlineCheckinActionRepository =
      new OnlineCheckinActionRepository() as jest.Mocked<OnlineCheckinActionRepository>;
    mockQuestionnaireActionRepository =
      new QuestionnaireActionRepository() as jest.Mocked<QuestionnaireActionRepository>;
    mockQuestionnaireRepository = new QuestionnaireRepository() as jest.Mocked<QuestionnaireRepository>;
    mockQuestionnaireQuestionRepository =
      new QuestionnaireQuestionRepository() as jest.Mocked<QuestionnaireQuestionRepository>;
    mockQuestionnaireQuestionAnswerRepository =
      new QuestionnaireQuestionAnswerRepository() as jest.Mocked<QuestionnaireQuestionAnswerRepository>;
    mockQuestionnaireResultAnswerRepository =
      new QuestionnaireResultAnswerRepository() as jest.Mocked<QuestionnaireResultAnswerRepository>;
    mockQuestionnaireResultRankRepository =
      new QuestionnaireResultRankRepository() as jest.Mocked<QuestionnaireResultRankRepository>;
    mockTransactionQueuesRepository = new TransactionQueuesRepository() as jest.Mocked<TransactionQueuesRepository>;

    mockSerialCodeProjectsRepository = new SerialCodeProjectsRepository() as jest.Mocked<SerialCodeProjectsRepository>;
    mockSerialCodesRepository = new SerialCodesRepository() as jest.Mocked<SerialCodesRepository>;
    mockAccountSerialCodesRepository = new AccountSerialCodesRepository() as jest.Mocked<AccountSerialCodesRepository>;
    mockRewardRepository = new RewardRepository() as jest.Mocked<RewardRepository>;
    mockAccountRepository = new AccountRepository() as jest.Mocked<AccountRepository>;
    mockCertificateRewardRepository = new CertificateRewardRepository() as jest.Mocked<CertificateRewardRepository>;
    mockCouponRewardRepository = new CouponRewardRepository() as jest.Mocked<CouponRewardRepository>;
    mockDigitalContentRewardRepository =
      new DigitalContentRewardRepository() as jest.Mocked<DigitalContentRewardRepository>;
    mockClaimedRewardRepository = new ClaimedRewardRepository() as jest.Mocked<ClaimedRewardRepository>;
    mockAchievementActionRepository = new AchievementActionRepository() as jest.Mocked<AchievementActionRepository>;
    mockQuestActivityRepository = new QuestActivityRepository() as jest.Mocked<QuestActivityRepository>;
    mockNftContractsRepository = new NftContractsRepository() as jest.Mocked<NftContractsRepository>;
    mockVaultKeyRepository = new VaultKeyRepository() as jest.Mocked<VaultKeyRepository>;
    mockServiceInfoRepository = new ServiceInfoRepository() as jest.Mocked<ServiceInfoRepository>;
    mockNftMetadatasRepository = new NftMetadatasRepository() as jest.Mocked<NftMetadatasRepository>;
    mockNftBaseMetadatasRepository = new NftBaseMetadatasRepository() as jest.Mocked<NftBaseMetadatasRepository>;
    mockNftContractTypesRepository = new NftContractTypesRepository() as jest.Mocked<NftContractTypesRepository>;
    mockSerialCodeActionRepository = new SerialCodeActionRepository() as jest.Mocked<SerialCodeActionRepository>;
    mockDeliveryNftsFirestoreRepository = new DeliveryNftsFirestoreRepository(
      {} as FirebaseComponent,
    ) as jest.Mocked<DeliveryNftsFirestoreRepository>;

    mockMetadataService = new MetadataService(
      mockNftMetadatasRepository,
      mockNftBaseMetadatasRepository,
      mockNftContractTypesRepository,
    ) as jest.Mocked<MetadataService>;
    mockNftsService = new NftsService(
      mockNftContractsRepository,
      mockAccountRepository,
      mockRewardRepository,
    ) as jest.Mocked<NftsService>;
    mockNftMintService = new NftMintService(
      mockNftContractsRepository,
      mockTransactionQueuesRepository,
      mockVaultKeyRepository,
      mockServiceInfoRepository,
      mockDeliveryNftsFirestoreRepository,
    ) as jest.Mocked<NftMintService>;
    mockRewardService = new RewardService(
      mockRewardRepository,
      mockAccountRepository,
      mockCertificateRewardRepository,
      mockCouponRewardRepository,
      mockDigitalContentRewardRepository,
      mockClaimedRewardRepository,
      mockAchievementActionRepository,
      mockQuestActivityRepository,
      mockNftMintService,
      mockNftsService,
      mockMetadataService,
      mockQuestionnaireResultAnswerRepository,
    ) as jest.Mocked<RewardService>;

    mockSerialCodeService = new SerialCodeService(
      mockSerialCodeProjectsRepository,
      mockSerialCodesRepository,
      mockAccountSerialCodesRepository,
      mockRewardService,
    ) as jest.Mocked<SerialCodeService>;

    actionService = new ActionService(
      mockActionRepository,
      mockActionActivityRepository,
      mockAchievementActionRepository,
      mockOnlineCheckinActionRepository,
      mockQuestRepository,
      mockQuestActivityRepository,
      mockQrCheckinActionRepository,
      mockQuestionnaireActionRepository,
      mockQuestionnaireRepository,
      mockQuestionnaireQuestionRepository,
      mockQuestionnaireQuestionAnswerRepository,
      mockQuestionnaireResultAnswerRepository,
      mockQuestionnaireResultRankRepository,
      mockSerialCodeActionRepository,
      mockSerialCodeService,
    );

    // Mock db.transaction().execute to avoid real DB connection errors in tests
    const db = require('../../src/db/database');
    jest.spyOn(db.db.transaction(), 'execute').mockImplementation(async (...args: any[]) => {
      const callback = args[0];
      return callback({
        // Mock transaction object
        commit: jest.fn(),
        rollback: jest.fn(),
      });
    });
  });

  describe('getAction', () => {
    test('should return ActionResponse when action is found', async () => {
      const mockActionWithTranslations: ActionWithTranslations = {
        action_id: 'action1',
        service_id: 'service1',
        action_title: 'Action Title',
        action_description: 'Action Description',
        action_cover_image_url: 'http://example.com/cover.jpg',
        action_thumbnail_image_url: 'http://example.com/thumb.jpg',
        action_label: 'label',
        action_available_start_date: new Date(),
        action_available_end_date: new Date(),
        action_type: ActionType.ACHIEVEMENT,
        geofence_id: 'geofence1',
        order_index: 1,
      };

      mockActionRepository.selectActionById.mockResolvedValue(mockActionWithTranslations);
      mockAchievementActionRepository.selectAchievementActionById.mockResolvedValue({
        action_id: 'action1',
        reward_id: 'reward1',
        service_id: 'service1',
        status_rank: 1,
        milestone: 10,
      });

      const result = await actionService.getAction('action1', 'service1', languageCode.EN_US);
      expect(result.actionId).toBe('action1');
      expect(result.title).toBe('Action Title');
      expect(result.coverImageUrl).toBe('http://example.com/cover.jpg');
      expect(result.description).toBe('Action Description');
      expect(result.actionType).toBe(ActionType.ACHIEVEMENT);
      expect(result.actionLabel).toBe('label');
      expect(result.actionDetailInfo).toEqual({
        type: ActionType.ACHIEVEMENT,
        milestone: 10,
        priority: 1,
      });
    });

    test('should throw NotFoundError when action is not found', async () => {
      mockActionRepository.selectActionById.mockResolvedValue(undefined);

      await expect(actionService.getAction('invalidActionId', 'service1', languageCode.EN_US)).rejects.toThrow(
        NotFoundError,
      );
    });
  });

  describe('handleActionInfo', () => {
    test('should return questionnaire action info', async () => {
      const mockAction: ActionEntity = {
        action_id: 'action3',
        service_id: 'service3',
        action_cover_image_url: 'http://example.com/cover.jpg',
        action_thumbnail_image_url: 'http://example.com/thumb.jpg',
        action_available_start_date: new Date(),
        action_available_end_date: new Date(),
        action_type: ActionType.QUESTIONNAIRE,
        geofence_id: 'geofence3',
        order_index: 1,
      };

      mockQuestionnaireActionRepository.selectQuestionnaireByIdAction.mockResolvedValue({
        action_id: 'account_id',
        service_id: 'service3',
        questionnaire_id: 'questionnaire1',
      });

      const result = await actionService['handleActionInfo'](mockAction);

      expect(result).toEqual({
        type: ActionType.QUESTIONNAIRE,
        questionnaireId: 'questionnaire1',
      });
    });
    test('should return achievement action info', async () => {
      const mockAction: ActionEntity = {
        action_id: 'action1',
        service_id: 'service1',
        action_cover_image_url: 'http://example.com/cover.jpg',
        action_thumbnail_image_url: 'http://example.com/thumb.jpg',
        action_available_start_date: new Date(),
        action_available_end_date: new Date(),
        action_type: ActionType.ACHIEVEMENT,
        geofence_id: 'geofence1',
        order_index: 1,
      };

      mockAchievementActionRepository.selectAchievementActionById.mockResolvedValue({
        action_id: 'action1',
        reward_id: 'reward1',
        service_id: 'service1',
        status_rank: 1,
        milestone: 10,
      });

      const result = await actionService['handleActionInfo'](mockAction);

      expect(result).toEqual({
        type: ActionType.ACHIEVEMENT,
        priority: 1,
        milestone: 10,
      });
    });

    test('should return online checkin action info', async () => {
      const mockAction: ActionEntity = {
        action_id: 'action2',
        service_id: 'service2',
        action_cover_image_url: 'http://example.com/cover.jpg',
        action_thumbnail_image_url: 'http://example.com/thumb.jpg',
        action_available_start_date: new Date(),
        action_available_end_date: new Date(),
        action_type: ActionType.ONLINE_CHECKIN,
        geofence_id: 'geofence2',
        order_index: 1,
      };

      mockOnlineCheckinActionRepository.selectOnlineCheckinActionById.mockResolvedValue({
        action_id: 'action2',
        service_id: 'service2',
        online_checkin_url: 'http://example.com/checkin',
      });

      const result = await actionService['handleActionInfo'](mockAction);

      expect(result).toEqual({
        type: ActionType.ONLINE_CHECKIN,
        targetUrl: 'http://example.com/checkin',
      });
    });
  });

  describe('completeAction', () => {
    describe('findAction', () => {
      test('should return action when action is found', async () => {
        const mockActionWithTranslations: ActionWithTranslations = {
          action_id: 'action1',
          service_id: 'service1',
          action_title: 'Action Title',
          action_description: 'Action Description',
          action_cover_image_url: 'http://example.com/cover.jpg',
          action_thumbnail_image_url: 'http://example.com/thumb.jpg',
          action_label: 'label',
          action_available_start_date: new Date(),
          action_available_end_date: new Date(),
          action_type: ActionType.ACHIEVEMENT,
          geofence_id: 'geofence1',
          order_index: 1,
        };

        mockActionRepository.selectActionById.mockResolvedValue(mockActionWithTranslations);

        const result = await actionService['findAction']('action1', 'service1');

        expect(result).toEqual(mockActionWithTranslations);
        expect(mockActionRepository.selectActionById).toHaveBeenCalledWith('action1', 'service1');
      });

      test('should throw NotFoundError when action is not found', async () => {
        mockActionRepository.selectActionById.mockResolvedValue(undefined);

        await expect(actionService['findAction']('invalidActionId', 'service1')).rejects.toThrow(NotFoundError);
        expect(mockActionRepository.selectActionById).toHaveBeenCalledWith('invalidActionId', 'service1');
      });
    });
  });

  describe('validateActionAvailability', () => {
    let mockAction: ActionEntity;

    beforeEach(() => {
      mockAction = {
        action_id: 'action1',
        service_id: 'service1',
        action_cover_image_url: 'http://example.com/cover.jpg',
        action_thumbnail_image_url: 'http://example.com/thumb.jpg',
        action_available_start_date: new Date(),
        action_available_end_date: new Date(),
        action_type: ActionType.ACHIEVEMENT,
        geofence_id: 'geofence1',
        order_index: 1,
      };
    });

    test('should throw ValidationError if action is not available yet', () => {
      mockAction.action_available_start_date = new Date(Date.now() + 10000); // future date
      mockAction.action_available_end_date = new Date(Date.now() + 20000);

      expect(() => actionService['validateActionAvailability'](mockAction)).toThrow(ValidationError);
    });

    test('should throw ValidationError if action is expired', () => {
      mockAction.action_available_start_date = new Date(Date.now() - 20000);
      mockAction.action_available_end_date = new Date(Date.now() - 10000); // past date

      expect(() => actionService['validateActionAvailability'](mockAction)).toThrow(ValidationError);
    });

    test('should not throw any error if action is currently available', () => {
      mockAction.action_available_start_date = new Date(Date.now() - 10000); // past date
      mockAction.action_available_end_date = new Date(Date.now() + 10000); // future date

      expect(() => actionService['validateActionAvailability'](mockAction)).not.toThrow();
    });
  });

  describe('checkActionCompletion', () => {
    test('should throw ConflictError if action is already completed', async () => {
      mockActionActivityRepository.selectActionActivity.mockResolvedValue({
        account_id: 'account1',
        action_id: 'action1',
        service_id: 'service1',
        action_activity_status: ActionActivitiesStatus.DONE,
        finish_date: new Date(),
      });

      await expect(actionService['checkActionCompletion']('action1', 'account1', 'service1')).rejects.toThrow(
        ConflictError,
      );
      expect(mockActionActivityRepository.selectActionActivity).toHaveBeenCalledWith('action1', 'account1', 'service1');
    });

    test('should not throw any error if action is not completed', async () => {
      mockActionActivityRepository.selectActionActivity.mockResolvedValue(undefined);

      await expect(actionService['checkActionCompletion']('action1', 'account1', 'service1')).resolves.not.toThrow();
      expect(mockActionActivityRepository.selectActionActivity).toHaveBeenCalledWith('action1', 'account1', 'service1');
    });
  });

  describe('validateActionTypeAndInput', () => {
    let mockAction: ActionEntity;

    beforeEach(() => {
      mockAction = {
        action_id: 'action1',
        service_id: 'service1',
        action_cover_image_url: 'http://example.com/cover.jpg',
        action_thumbnail_image_url: 'http://example.com/thumb.jpg',
        action_available_start_date: new Date(),
        action_available_end_date: new Date(),
        action_type: ActionType.QR_CHECKIN,
        geofence_id: 'geofence1',
        order_index: 1,
      };
    });

    test('should throw ValidationError if qrVerificationData is missing for QR_CHECKIN action', async () => {
      await expect(
        actionService['validateActionTypeAndInput'](mockAction, 'service1', 'account1', languageCode.JA),
      ).rejects.toThrow(ValidationError);
    });

    test('should throw ValidationError if qrVerificationData does not match for QR_CHECKIN action', async () => {
      mockQrCheckinActionRepository.selectQrVerification.mockResolvedValue({
        action_id: 'action1',
        service_id: 'service1',
        qr_verification_data: 'validQrData',
      });

      await expect(
        actionService['validateActionTypeAndInput'](
          mockAction,
          'service1',
          'account1',
          languageCode.JA,
          'invalidQrData',
        ),
      ).rejects.toThrow(ValidationError);
    });

    test('should not throw any error if qrVerificationData matches for QR_CHECKIN action', async () => {
      mockQrCheckinActionRepository.selectQrVerification.mockResolvedValue({
        action_id: 'action1',
        service_id: 'service1',
        qr_verification_data: 'validQrData',
      });

      await expect(
        actionService['validateActionTypeAndInput'](mockAction, 'service1', 'account1', languageCode.JA, 'validQrData'),
      ).resolves.not.toThrow();
    });

    test('should not throw any error for ONLINE_CHECKIN action', async () => {
      mockAction.action_id = 'action2';
      mockAction.service_id = 'service2';
      mockAction.action_type = ActionType.ONLINE_CHECKIN;
      mockAction.geofence_id = 'geofence2';

      await expect(
        actionService['validateActionTypeAndInput'](mockAction, 'service2', 'account1', languageCode.JA),
      ).resolves.not.toThrow();
    });
  });

  describe('storeActionCompletion', () => {
    test('should store action completion successfully', async () => {
      await actionService['storeActionCompletion']('action1', 'account1', 'service1');

      expect(mockActionActivityRepository.insertActionActivity).toHaveBeenCalledWith({
        account_id: 'account1',
        action_id: 'action1',
        service_id: 'service1',
        action_activity_status: ActionActivitiesStatus.DONE,
        finish_date: expect.any(Date),
      });
    });

    test('should throw error if insertActionActivity fails', async () => {
      mockActionActivityRepository.insertActionActivity.mockRejectedValue(new Error('Insert failed'));

      await expect(actionService['storeActionCompletion']('action1', 'account1', 'service1')).rejects.toThrow(
        'Insert failed',
      );
    });
  });

  describe('updateQuestActivities', () => {
    let mockAction: ActionWithTranslations;
    let mockActionActivities: ActionActivityEntity[];

    beforeEach(() => {
      mockAction = {
        action_id: 'action1',
        service_id: 'service1',
        action_title: 'Action Title',
        action_description: 'Action Description',
        action_cover_image_url: 'http://example.com/cover.jpg',
        action_thumbnail_image_url: 'http://example.com/thumb.jpg',
        action_label: 'label',
        action_available_start_date: new Date(),
        action_available_end_date: new Date(),
        action_type: ActionType.ACHIEVEMENT,
        geofence_id: 'geofence1',
        order_index: 1,
      };

      const mockQuest: QuestWithTranslations = {
        quest_id: 'quest1',
        service_id: 'service1',
        quest_title: 'Quest Title',
        quest_description: 'Quest Description',
        quest_cover_image_url: 'http://example.com/cover.jpg',
        quest_thumbnail_image_url: 'http://example.com/thumb.jpg',
        quest_available_start_date: new Date(),
        quest_available_end_date: new Date(),
        quest_type: QuestType.STATUS,
        order_index: 1,
      };

      mockActionActivities = [
        {
          action_id: 'action1',
          account_id: 'account1',
          service_id: 'service1',
          action_activity_status: ActionActivitiesStatus.DONE,
          finish_date: new Date(),
        },
      ];

      mockQuestRepository.selectQuestByActionId.mockResolvedValue(mockQuest);
      mockActionRepository.selectActionsByQuestId.mockResolvedValue([mockAction]);
      mockActionActivityRepository.selectActionActivityByAccountId.mockResolvedValue(mockActionActivities);
    });

    test('should update quest activity to DONE if all actions are completed', async () => {
      await actionService['updateQuestActivities']('action1', 'account1', 'service1');

      expect(mockQuestActivityRepository.upsertQuestActivity).toHaveBeenCalledWith({
        account_id: 'account1',
        quest_id: 'quest1',
        service_id: 'service1',
        quest_activity_status: QuestActivityStatus.DONE,
        finish_date: expect.any(Date),
      });
    });

    test('should update quest activity to PROCEEDING if not all actions are completed', async () => {
      mockActionRepository.selectActionsByQuestId.mockResolvedValue([
        mockAction,
        {
          ...mockAction,
          action_id: 'action2',
        },
      ]);

      await actionService['updateQuestActivities']('action1', 'account1', 'service1');

      expect(mockQuestActivityRepository.upsertQuestActivity).toHaveBeenCalledWith({
        account_id: 'account1',
        quest_id: 'quest1',
        service_id: 'service1',
        quest_activity_status: QuestActivityStatus.PROCEEDING,
      });
    });

    test('should throw InternalServerError if related quest is not found', async () => {
      mockQuestRepository.selectQuestByActionId.mockResolvedValue(undefined);

      await expect(actionService['updateQuestActivities']('action1', 'account1', 'service1')).rejects.toThrow(
        InternalServerError,
      );
    });
  });
  describe('updateStatusQuests', () => {
    let mockQuest: QuestEntity;

    beforeEach(() => {
      mockQuest = {
        quest_id: 'quest1',
        service_id: 'service1',
        quest_cover_image_url: 'http://example.com/quest.jpg',
        quest_thumbnail_image_url: 'http://example.com/quest_thumb.jpg',
        quest_available_start_date: new Date(),
        quest_available_end_date: new Date(),
        quest_type: QuestType.STATUS,
        order_index: 1,
      };

      mockQuestRepository.selectAvailableQuest.mockResolvedValue([mockQuest]);
      mockQuestActivityRepository.countQuestActivitiesByAccountIdAndPeriod.mockResolvedValue(5);
      mockActionRepository.selectAchievementActionsByQuestId.mockResolvedValue([
        {
          action_id: 'action1',
          service_id: 'service1',
          action_title: 'Achievement 1',
          action_description: 'Achievement Description',
          action_cover_image_url: 'http://example.com/achievement1.jpg',
          action_thumbnail_image_url: 'http://example.com/thumb1.jpg',
          action_label: 'label1',
          action_available_start_date: new Date(),
          action_available_end_date: new Date(),
          action_type: ActionType.ACHIEVEMENT,
          geofence_id: 'geofence1',
          milestone: 5,
          reward_id: 'reward1',
          status_rank: 1,
          order_index: 1,
        },
      ]);
    });

    test('should update status quest activity to DONE if all actions are completed', async () => {
      mockActionActivityRepository.selectActionActivity.mockResolvedValue({
        account_id: 'account1',
        action_id: 'action1',
        service_id: 'service1',
        action_activity_status: ActionActivitiesStatus.DONE,
        finish_date: new Date(),
      });

      await actionService['updateStatusQuests']('account1', 'service1');

      expect(mockQuestActivityRepository.upsertQuestActivity).toHaveBeenCalledWith({
        account_id: 'account1',
        quest_id: 'quest1',
        service_id: 'service1',
        quest_activity_status: QuestActivityStatus.DONE,
        finish_date: expect.any(Date),
      });
    });

    test('should throw InternalServerError if more than one available status quest is found', async () => {
      mockQuestRepository.selectAvailableQuest.mockResolvedValue([mockQuest, mockQuest]);

      await expect(actionService['updateStatusQuests']('account1', 'service1')).rejects.toThrow(InternalServerError);
    });

    test('should not update status quest activity if no available status quest is found', async () => {
      mockQuestRepository.selectAvailableQuest.mockResolvedValue([]);

      await expect(actionService['updateStatusQuests']('account1', 'service1')).resolves.not.toThrow();
      expect(mockQuestActivityRepository.upsertQuestActivity).not.toHaveBeenCalled();
    });
  });

  describe('completeAction', () => {
    let mockAction: ActionWithTranslations;
    let mockCompleteActionRequest: ActionComplete;

    beforeEach(() => {
      mockAction = {
        action_id: 'action1',
        service_id: 'service1',
        action_title: 'Action Title',
        action_description: 'Action Description',
        action_cover_image_url: 'http://example.com/cover.jpg',
        action_thumbnail_image_url: 'http://example.com/thumb.jpg',
        action_label: 'label',
        action_available_start_date: new Date(Date.now() - 10000), // past date
        action_available_end_date: new Date(Date.now() + 10000), // future date
        action_type: ActionType.QR_CHECKIN,
        geofence_id: 'geofence1',
        order_index: 1,
      };

      mockCompleteActionRequest = {
        actionType: ActionType.QR_CHECKIN,
        qrVerificationData: 'validQrData',
      };

      mockActionRepository.selectActionById.mockResolvedValue(mockAction);
      mockActionActivityRepository.selectActionActivity.mockResolvedValue(undefined);
      mockQrCheckinActionRepository.selectQrVerification.mockResolvedValue({
        action_id: 'action1',
        service_id: 'service1',
        qr_verification_data: 'validQrData',
      });
      mockQuestRepository.selectQuestByActionId.mockResolvedValue({
        quest_id: 'quest1',
        service_id: 'service1',
        quest_cover_image_url: 'http://example.com/cover.jpg',
        quest_thumbnail_image_url: 'http://example.com/thumb.jpg',
        quest_available_start_date: new Date(),
        quest_available_end_date: new Date(),
        quest_type: QuestType.STATUS,
        order_index: 1,
      });
      mockActionRepository.selectActionsByQuestId.mockResolvedValue([mockAction]);
      mockActionActivityRepository.selectActionActivityByAccountId.mockResolvedValue([]);
      mockQuestActivityRepository.upsertQuestActivity.mockResolvedValue(undefined);
      mockQuestRepository.selectAvailableQuest.mockResolvedValue([]);
    });

    test('should complete action successfully', async () => {
      await expect(
        actionService.completeAction('account1', 'action1', 'service1', mockCompleteActionRequest, languageCode.JA),
      ).resolves.not.toThrow();

      expect(mockActionRepository.selectActionById).toHaveBeenCalledWith('action1', 'service1');
      expect(mockActionActivityRepository.selectActionActivity).toHaveBeenCalledWith('action1', 'account1', 'service1');
      expect(mockQrCheckinActionRepository.selectQrVerification).toHaveBeenCalledWith('action1', 'service1');
      expect(mockActionActivityRepository.insertActionActivity).toHaveBeenCalledWith({
        account_id: 'account1',
        action_id: 'action1',
        service_id: 'service1',
        action_activity_status: ActionActivitiesStatus.DONE,
        finish_date: expect.any(Date),
      });
      expect(mockQuestActivityRepository.upsertQuestActivity).toHaveBeenCalled();
    });

    test('should throw NotFoundError if action is not found', async () => {
      mockActionRepository.selectActionById.mockResolvedValue(undefined);

      await expect(
        actionService.completeAction(
          'account1',
          'invalidActionId',
          'service1',
          mockCompleteActionRequest,
          languageCode.JA,
        ),
      ).rejects.toThrow(NotFoundError);
    });

    test('should throw ValidationError if action is not available', async () => {
      mockAction.action_available_start_date = new Date(Date.now() + 10000); // future date

      await expect(
        actionService.completeAction('account1', 'action1', 'service1', mockCompleteActionRequest, languageCode.JA),
      ).rejects.toThrow(ValidationError);
    });

    test('should throw ConflictError if action is already completed', async () => {
      mockActionActivityRepository.selectActionActivity.mockResolvedValue({
        account_id: 'account1',
        action_id: 'action1',
        service_id: 'service1',
        action_activity_status: ActionActivitiesStatus.DONE,
        finish_date: new Date(),
      });

      await expect(
        actionService.completeAction('account1', 'action1', 'service1', mockCompleteActionRequest, languageCode.JA),
      ).rejects.toThrow(ConflictError);
    });

    test('should throw ValidationError if qrVerificationData is invalid', async () => {
      mockQrCheckinActionRepository.selectQrVerification.mockResolvedValue({
        action_id: 'action1',
        service_id: 'service1',
        qr_verification_data: 'invalidQrData',
      });

      await expect(
        actionService.completeAction('account1', 'action1', 'service1', mockCompleteActionRequest, languageCode.JA),
      ).rejects.toThrow(ValidationError);
    });

    test('should throw ValidationError by complete action QUESTIONNAIRE and MESSAGE', async () => {
      mockAction.action_type = ActionType.QUESTIONNAIRE;
      mockQuestionnaireQuestionRepository.selectQuestionnaireQuestions.mockResolvedValue([
        { questionId: 'q1', questionType: QuestionType.IMAGE, isRequired: true },
        { questionId: 'q2', questionType: QuestionType.TEXT, isRequired: true },
      ]);
      mockQuestionnaireRepository.getQuestionnaireType.mockResolvedValue(QuestionnaireType.MESSAGE);

      mockQuestionnaireResultAnswerRepository.insertResultAnswers.mockResolvedValue({
        questionnaire_result_id: 'dummy_result_id',
        service_id: 'service1',
        account_id: 'account1',
        rank_id: 'rank1',
        questionnaire_status: QuestionnaireStatus.UNDEFINED,
        questionnaire_points: 10,
        is_available_result: true,
        created_at: new Date(),
      });
      mockQuestionnaireResultRankRepository.selectResultRanks.mockResolvedValue([
        {
          rank_id: 'rank_id',
          service_id: 'service_id',
          questionnaire_id: 'questionnaire_id',
          rank_header_animation_url: 'rank_header_animation_url',
          rank: 1,
          lower_limit_points: 0,
          upper_limit_points: 1,
          is_passed: true,
        },
      ]);

      await expect(
        actionService.completeAction(
          'account1',
          'action1',
          'service1',
          {
            actionType: ActionType.QUESTIONNAIRE,
            answerData: {
              questionnaireId: 'questionnaire1',
              questionAnswers: [
                {
                  questionId: 'q1',
                  answer: 'user/abc123/firebaseStragePath.png',
                },
                {
                  questionId: 'q2',
                  answer: null,
                },
              ],
            },
          },
          languageCode.JA,
        ),
      ).rejects.toThrow(InternalServerError);
    });
  });

  describe('storeAndValidateQuestionnaireAnswers', () => {
    let mockQuestionnaireAnswerRequest: QuestionnaireAnswerRequest;

    beforeEach(() => {
      mockQuestionnaireAnswerRequest = {
        questionnaireId: 'questionnaire1',
        questionAnswers: [
          {
            questionId: 'q1',
            answer: 'correct_answer_1',
          },
          {
            questionId: 'q2',
            answer: 'incorrect_answer_2',
          },
        ],
      };

      mockQuestionnaireRepository.getQuestionnaireType.mockResolvedValue(QuestionnaireType.QUIZ);
      mockQuestionnaireQuestionRepository.selectQuestionnaireQuestions.mockResolvedValue([
        {
          questionId: 'q1',
          correctData: 'correct_answer_1',
          answerPoint: 10,
          isRequired: true,
          questionType: QuestionType.TEXT,
          validation: '^.*$',
        },
        {
          questionId: 'q2',
          correctData: 'correct_answer_2',
          answerPoint: 5,
          isRequired: true,
          questionType: QuestionType.TEXT,
          validation: '^correct_answer_2$',
        },
      ]);
      mockQuestionnaireResultRankRepository.selectResultRanks.mockResolvedValue([
        {
          service_id: 'service1',
          rank_id: 'rank1',
          questionnaire_id: 'questionnaire1',
          rank_header_animation_url: 'http://example.com/gold.png',
          rank: 1,
          lower_limit_points: 0,
          upper_limit_points: 20,
          is_passed: true,
        },
      ]);
    });

    test('should throw error if no ranks are defined for QUIZ', async () => {
      mockQuestionnaireResultRankRepository.selectResultRanks.mockResolvedValue([]);

      await expect(
        actionService['storeAndValidateQuestionnaireAnswers'](
          'service1',
          'account1',
          mockQuestionnaireAnswerRequest,
          languageCode.JA,
        ),
      ).rejects.toThrow(InternalServerError);
    });
  });
});
