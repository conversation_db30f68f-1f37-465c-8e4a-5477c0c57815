import { FirebaseComponent } from '../../src/components/firebaseComponent';
import { UnauthorizedError } from '../../src/errors/unauthorizedError';
import * as admin from 'firebase-admin';
import { getFirestore } from 'firebase-admin/firestore';

jest.mock('firebase-admin', () => ({
  credential: {
    cert: jest.fn(),
  },
  auth: jest.fn().mockReturnValue({
    verifyIdToken: jest.fn(),
    createCustomToken: jest.fn(),
  }),
  initializeApp: jest.fn(),
  apps: {
    length: 0,
  },
}));

jest.mock('firebase-admin/firestore', () => ({
  getFirestore: jest.fn().mockReturnValue({
    collection: jest.fn().mockReturnValue({
      doc: jest.fn().mockReturnValue({
        get: jest.fn(),
      }),
    }),
  }),
}));

describe('FirebaseComponent', () => {
  let firebaseComponent: FirebaseComponent;

  beforeEach(() => {
    process.env.FIREBASE_PROJECT_ID = 'test-project-id';
    process.env.FIREBASE_CLIENT_EMAIL = 'test-client-email';
    process.env.FIREBASE_PRIVATE_KEY = 'test-private-key';
    process.env.FIRESTORE_DB = 'test-firestore-db';
    firebaseComponent = new FirebaseComponent();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('verifyFirebaseIdToken', () => {
    test('should verify a valid Firebase ID token', async () => {
      const mockDecodedIdToken = { uid: 'test-uid' };
      (admin.auth().verifyIdToken as jest.Mock).mockResolvedValue(mockDecodedIdToken);

      const result = await firebaseComponent.verifyFirebaseIdToken('valid-token');

      expect(admin.auth().verifyIdToken).toHaveBeenCalledWith('valid-token');
      expect(result).toEqual(mockDecodedIdToken);
    });

    test('should throw UnauthorizedError for an invalid Firebase ID token', async () => {
      (admin.auth().verifyIdToken as jest.Mock).mockRejectedValue(new Error('Invalid token'));

      await expect(firebaseComponent.verifyFirebaseIdToken('invalid-token')).rejects.toThrow(UnauthorizedError);
    });
  });

  describe('createCustomToken', () => {
    test('should create a custom token for a valid UID', async () => {
      const mockCustomToken = 'custom-token';
      (admin.auth().createCustomToken as jest.Mock).mockResolvedValue(mockCustomToken);

      const result = await firebaseComponent.createCustomToken('test-uid');

      expect(admin.auth().createCustomToken).toHaveBeenCalledWith('test-uid', undefined);
      expect(result).toBe(mockCustomToken);
    });

    test('should create a custom token with additional claims', async () => {
      const mockCustomToken = 'custom-token';
      const additionalClaims = { role: 'admin' };
      (admin.auth().createCustomToken as jest.Mock).mockResolvedValue(mockCustomToken);

      const result = await firebaseComponent.createCustomToken('test-uid', additionalClaims);

      expect(admin.auth().createCustomToken).toHaveBeenCalledWith('test-uid', additionalClaims);
      expect(result).toBe(mockCustomToken);
    });

    test('should throw UnauthorizedError when failing to create a custom token', async () => {
      (admin.auth().createCustomToken as jest.Mock).mockRejectedValue(new Error('Creation failed'));

      await expect(firebaseComponent.createCustomToken('test-uid')).rejects.toThrow(UnauthorizedError);
    });
  });

  describe('getDocumentFromFirestore', () => {
    test('should return document data if document exists', async () => {
      const mockDocumentData = { field: 'value' };
      const mockDoc = {
        exists: true,
        data: jest.fn().mockReturnValue(mockDocumentData),
      };
      (getFirestore().collection('collection').doc('documentId').get as jest.Mock).mockResolvedValue(mockDoc);

      const result = await firebaseComponent.getDocumentFromFirestore('collection', 'documentId');

      expect(getFirestore().collection).toHaveBeenCalledWith('collection');
      expect(getFirestore().collection('collection').doc).toHaveBeenCalledWith('documentId');
      expect(result).toEqual(mockDocumentData);
    });

    test('should return undefined if document does not exist', async () => {
      const mockDoc = {
        exists: false,
        data: jest.fn(),
      };
      (getFirestore().collection('collection').doc('documentId').get as jest.Mock).mockResolvedValue(mockDoc);

      const result = await firebaseComponent.getDocumentFromFirestore('collection', 'documentId');

      expect(getFirestore().collection).toHaveBeenCalledWith('collection');
      expect(getFirestore().collection('collection').doc).toHaveBeenCalledWith('documentId');
      expect(result).toBeUndefined();
    });

    test('should throw an error if getting document fails', async () => {
      (getFirestore().collection('collection').doc('documentId').get as jest.Mock).mockRejectedValue(new Error('Firestore error'));

      await expect(firebaseComponent.getDocumentFromFirestore('collection', 'documentId')).rejects.toThrow('Failed to get document from Firestore: Firestore error');
    });
  });
});
