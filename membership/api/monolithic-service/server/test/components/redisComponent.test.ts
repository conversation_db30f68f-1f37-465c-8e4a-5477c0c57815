// test/components/RedisComponent.test.ts
import { RedisComponent } from '../../src/components/redisComponent';
import { createClient, RedisClientType } from 'redis';
import { config } from '../../src/configs/config';

const mockClient: jest.Mocked<RedisClientType> = {
  on: jest.fn(),
  connect: jest.fn().mockResolvedValue(undefined),
  set: jest.fn().mockResolvedValue(undefined),
  get: jest.fn().mockResolvedValue(null),
  del: jest.fn().mockResolvedValue(undefined),
  quit: jest.fn().mockResolvedValue(undefined),
} as unknown as jest.Mocked<RedisClientType>;

jest.mock('redis', () => ({
  createClient: jest.fn(() => mockClient),
}));

describe('RedisComponent', () => {
  let redisComponent: RedisComponent;

  beforeEach(() => {
    jest.clearAllMocks();
    jest.spyOn(process, 'once').mockImplementation(() => process as unknown as NodeJS.Process);
    redisComponent = new RedisComponent();
  });

  describe('constructor', () => {
    it('should initialize client and register event handlers', () => {
      expect(createClient).toHaveBeenCalledWith({ url: config.redisUrl });
      expect(mockClient.on).toHaveBeenCalledWith('error', expect.any(Function));
      expect(mockClient.on).toHaveBeenCalledWith('reconnecting', expect.any(Function));
      expect(mockClient.connect).toHaveBeenCalled();
      expect(process.once).toHaveBeenCalledWith('SIGTERM', expect.any(Function));
    });
  });

  describe('set()', () => {
    it('should call set with default TTL when ttlSeconds is omitted', async () => {
      const key = 'test:key';
      const value = { foo: 'bar' };
      await redisComponent.set(key, value);
      expect(mockClient.set).toHaveBeenCalledWith(key, JSON.stringify(value), { EX: expect.any(Number) });
    });

    it('should call set without EX option when ttlSeconds is zero', async () => {
      const key = 'test:key';
      const value = { baz: 1 };
      await redisComponent.set(key, value, 0);
      expect(mockClient.set).toHaveBeenCalledWith(key, JSON.stringify(value));
    });
  });

  describe('get()', () => {
    it('should return parsed JSON object when value exists', async () => {
      const key = 'test:key';
      const stored = { hello: 'world' };
      mockClient.get.mockResolvedValueOnce(JSON.stringify(stored));
      const result = await redisComponent.get<typeof stored>(key);
      expect(mockClient.get).toHaveBeenCalledWith(key);
      expect(result).toEqual(stored);
    });

    it('should return null when no value exists', async () => {
      const key = 'test:key';
      mockClient.get.mockResolvedValueOnce(null);
      const result = await redisComponent.get<any>(key);
      expect(mockClient.get).toHaveBeenCalledWith(key);
      expect(result).toBeNull();
    });
  });

  describe('delete()', () => {
    it('should call del with the given key', async () => {
      const key = 'test:key';
      await redisComponent.delete(key);
      expect(mockClient.del).toHaveBeenCalledWith(key);
    });
  });
});
