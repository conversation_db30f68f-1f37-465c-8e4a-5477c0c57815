import { Context } from 'hono';
import { UserOperationController } from '../../src/controllers/userOperationController';
import { UserOperationService } from '../../src/services/userOperationService';
import { SendUserOperationId } from '../../src/dtos/user_operations/schemas';
import { parseCustomToken } from '../../src/utils/middleware/firebaseAuthAccessMiddleware';

jest.mock('../../src/utils/middleware/firebaseAuthAccessMiddleware', () => ({
  parseCustomToken: jest.fn(),
}));
describe('UserOperationController - storeUserOperationData', () => {
  let userOperationController: UserOperationController;
  let mockUserOperationService: jest.Mocked<UserOperationService>;

  beforeEach(() => {
    mockUserOperationService = {
      storeUserOperationInfo: jest.fn(),
    } as unknown as jest.Mocked<UserOperationService>;

    userOperationController = new UserOperationController(mockUserOperationService);

    jest.clearAllMocks();
  });

  const mockServiceId = 'testServiceId';
  const mockActionId = 'testActionId';
  (parseCustomToken as jest.Mock).mockReturnValue({
    service_id: mockServiceId,
    account_id: mockActionId,
  });

  const mockOperationId = 'operation-uuid';

  const mockContext = {
    req: {
      json: jest.fn().mockResolvedValue({
        uoHash: '0x7c6a180b36896a0a8c58c715db5edfdc15e80e982bf30d1deae8d51d416a6c7a',
      }),
      param: jest.fn().mockReturnValue(mockActionId),
      header: jest.fn().mockReturnValue(mockServiceId),
    },
    json: jest.fn(),
  } as unknown as Context;

  const mockResponse: SendUserOperationId = {
    operationId: mockOperationId,
  };

  test('should store user operation data and return operationId', async () => {
    // Mock the service method to return an operation ID
    mockUserOperationService.storeUserOperationInfo.mockResolvedValue(mockResponse);

    // Call the controller method
    await userOperationController.storeUserOperationData(mockContext);

    // Await the resolved value of the request JSON
    const requestData = await mockContext.req.json();

    // Check that the service was called with the correct arguments
    expect(mockUserOperationService.storeUserOperationInfo).toHaveBeenCalledWith(
      mockContext.req.header(),
      mockContext.req.param(),
      requestData.uoHash,
    );

    // Check that the response is sent with status 200 and the correct operation ID
    expect(mockContext.json).toHaveBeenCalledWith(mockResponse, 200);
  });

  test('should handle errors if storeUserOperationInfo throws an error', async () => {
    // Mock the service to throw an error
    mockUserOperationService.storeUserOperationInfo.mockRejectedValue(new Error('Service error'));

    // Expect the controller method to throw an error
    await expect(userOperationController.storeUserOperationData(mockContext)).rejects.toThrow('Service error');
  });
});
