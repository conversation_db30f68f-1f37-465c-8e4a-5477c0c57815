import { AccountRepository } from '../../src/repositories/accountRepository';
import { AccountSerialCodesRepository } from '../../src/repositories/accountSerialCodesRepository';
import { AchievementActionRepository } from '../../src/repositories/achievementActionRepository';
import { ClaimedRewardRepository } from '../../src/repositories/claimedRewardRepository';
import { NftBaseMetadatasRepository } from '../../src/repositories/nftBaseMetadatasRepository';
import { NftContractsRepository } from '../../src/repositories/nftContractsRepository';
import { NftContractTypesRepository } from '../../src/repositories/nftContractTypesRepository';
import { NftMetadatasRepository } from '../../src/repositories/nftMetadatasRepository';
import { QuestActivityRepository } from '../../src/repositories/questActivityRepository';
import { QuestionnaireResultAnswerRepository } from '../../src/repositories/questionnaireResultAnswerRepository';
import { RewardRepository } from '../../src/repositories/rewardRepository';
import { SerialCodeProjectsRepository } from '../../src/repositories/serialCodeProjectsRepository';
import { SerialCodesRepository } from '../../src/repositories/serialCodesRepository';
import { ServiceInfoRepository } from '../../src/repositories/serviceInfoRepository';
import { VaultKeyRepository } from '../../src/repositories/vaultKeyRepository';
import { MetadataService } from '../../src/services/metadataService';
import { NftMintService } from '../../src/services/nftMintService';
import { RewardService } from '../../src/services/rewardService';
import { SerialCodeService } from '../../src/services/serialCodeService';
import { SerialCodeController } from '../../src/controllers/serialCodeController';
import { Context } from 'hono';
import { TransactionQueuesRepository } from '../../src/repositories/transactionQueuesRepository';
import { DeliveryNftsFirestoreRepository } from '../../src/repositories/deliveryNftsFirestoreRepository';
import { FirebaseComponent } from '../../src/components/firebaseComponent';
import { StatusPointService } from '../../src/services/statusPointService';
import { RewardPointService } from '../../src/services/rewardPointService';
import { RedisComponent } from '../../src/components/redisComponent';
import { RewardPointTxsRepository } from '../../src/repositories/rewardPointTxsRepository';
import { StatusPointTxsRepository } from '../../src/repositories/statusPointTxsRepository';
import { parseCustomToken } from '../../src/utils/middleware/firebaseAuthAccessMiddleware';

jest.mock('../../src/components/redisComponent');
jest.mock('../../src/utils/middleware/firebaseAuthAccessMiddleware', () => ({
  parseCustomToken: jest.fn(),
}));

describe('SerialCodeController', () => {
  let context: Context;
  let serialCodeProjectsRepository: jest.Mocked<SerialCodeProjectsRepository>;
  let serialCodesRepository: jest.Mocked<SerialCodesRepository>;
  let accountSerialCodesRepository: jest.Mocked<AccountSerialCodesRepository>;
  let rewardRepository: jest.Mocked<RewardRepository>;
  let accountRepository: jest.Mocked<AccountRepository>;
  let claimedRewardRepository: jest.Mocked<ClaimedRewardRepository>;
  let achievementActionRepository: jest.Mocked<AchievementActionRepository>;
  let questActivityRepository: jest.Mocked<QuestActivityRepository>;
  let questionnaireResultAnswerRepository: jest.Mocked<QuestionnaireResultAnswerRepository>;
  let nftContractsRepository: jest.Mocked<NftContractsRepository>;
  let vaultKeyRepository: jest.Mocked<VaultKeyRepository>;
  let serviceInfoRepository: jest.Mocked<ServiceInfoRepository>;
  let nftMetadatasRepository: jest.Mocked<NftMetadatasRepository>;
  let nftBaseMetadatasRepository: jest.Mocked<NftBaseMetadatasRepository>;
  let nftContractTypesRepository: jest.Mocked<NftContractTypesRepository>;
  let transactionQueuesRepository: jest.Mocked<TransactionQueuesRepository>;
  let deliveryNftsFirestoreRepository: jest.Mocked<DeliveryNftsFirestoreRepository>;

  let metadataService: jest.Mocked<MetadataService>;
  let nftMintService: jest.Mocked<NftMintService>;
  let rewardService: jest.Mocked<RewardService>;
  let serialCodeService: jest.Mocked<SerialCodeService>;

  let serialCodeController: jest.Mocked<SerialCodeController>;
  let mockRedisComponent: jest.Mocked<RedisComponent>;
  let mockRewardPointService: jest.Mocked<RewardPointService>;
  let mockStatusPointService: jest.Mocked<StatusPointService>;
  let mockRewardPointTxsRepository: jest.Mocked<RewardPointTxsRepository>;
  let mockStatusPointTxsRepository: jest.Mocked<StatusPointTxsRepository>;
  let mockServiceInfoRepository: jest.Mocked<ServiceInfoRepository>;

  beforeEach(() => {
    process.env.JSON_RPC_URL = 'http://localhost:8545';
    process.env.ALCHEMY_API_KEY = 'xxxxxsss';
    process.env.ALCHEMY_CHAIN_NAME = 'polygon-amoy';
    process.env.GAS_LIMIT_MULTIPLIER = '100';
    process.env.BASE_MAX_FEE_PER_GAS = '100';
    process.env.MAX_FEE_PER_GAS_MULTIPLIER = '100';
    process.env.MAX_PRIORITY_FEE_PER_GAS_MULTIPLIER = '100';
    process.env.GCP_PROJECT_ID = 'sample_gcp_project_id';
    process.env.GCS_BUCKET_NAME = 'mock-bucket-name';
    process.env.ALCHEMY_ADDRESS_ACTIVITY_WEBHOOK_ID = 'mockAddressActivityWebhookId';
    process.env.ALCHEMY_NFT_ACTIVITY_WEBHOOK_ID = 'mockNftActivityWebhookId';
    process.env.ALCHEMY_ADDRESS_ACTIVITY_WEBHOOK_SIGNING_KEY = 'mockSigningKey';
    process.env.ALCHEMY_NFT_ACTIVITY_WEBHOOK_SIGNING_KEY = 'mockSigningKey';
    process.env.ALCHEMY_AUTH_TOKEN = 'mockAuthToken';

    serialCodeProjectsRepository = new SerialCodeProjectsRepository() as jest.Mocked<SerialCodeProjectsRepository>;
    serialCodesRepository = new SerialCodesRepository() as jest.Mocked<SerialCodesRepository>;
    accountSerialCodesRepository = new AccountSerialCodesRepository() as jest.Mocked<AccountSerialCodesRepository>;
    rewardRepository = new RewardRepository() as jest.Mocked<RewardRepository>;
    accountRepository = new AccountRepository() as jest.Mocked<AccountRepository>;
    claimedRewardRepository = new ClaimedRewardRepository() as jest.Mocked<ClaimedRewardRepository>;
    achievementActionRepository = new AchievementActionRepository() as jest.Mocked<AchievementActionRepository>;
    questActivityRepository = new QuestActivityRepository() as jest.Mocked<QuestActivityRepository>;
    questionnaireResultAnswerRepository =
      new QuestionnaireResultAnswerRepository() as jest.Mocked<QuestionnaireResultAnswerRepository>;
    nftContractsRepository = new NftContractsRepository() as jest.Mocked<NftContractsRepository>;

    vaultKeyRepository = new VaultKeyRepository() as jest.Mocked<VaultKeyRepository>;
    serviceInfoRepository = new ServiceInfoRepository() as jest.Mocked<ServiceInfoRepository>;
    nftMetadatasRepository = new NftMetadatasRepository() as jest.Mocked<NftMetadatasRepository>;
    nftBaseMetadatasRepository = new NftBaseMetadatasRepository() as jest.Mocked<NftBaseMetadatasRepository>;
    nftContractTypesRepository = new NftContractTypesRepository() as jest.Mocked<NftContractTypesRepository>;
    transactionQueuesRepository = new TransactionQueuesRepository() as jest.Mocked<TransactionQueuesRepository>;
    deliveryNftsFirestoreRepository = new DeliveryNftsFirestoreRepository(
      {} as FirebaseComponent,
    ) as jest.Mocked<DeliveryNftsFirestoreRepository>;

    metadataService = new MetadataService(
      nftMetadatasRepository,
      nftBaseMetadatasRepository,
      nftContractTypesRepository,
    ) as jest.Mocked<MetadataService>;
    nftMintService = new NftMintService(
      nftContractsRepository,
      transactionQueuesRepository,
      vaultKeyRepository,
      serviceInfoRepository,
      deliveryNftsFirestoreRepository,
    ) as jest.Mocked<NftMintService>;
    mockServiceInfoRepository = new ServiceInfoRepository() as jest.Mocked<ServiceInfoRepository>;
    mockRedisComponent = new RedisComponent() as jest.Mocked<RedisComponent>;
    mockRewardPointTxsRepository = new RewardPointTxsRepository() as jest.Mocked<RewardPointTxsRepository>;
    mockStatusPointTxsRepository = new StatusPointTxsRepository() as jest.Mocked<StatusPointTxsRepository>;
    mockRewardPointService = new RewardPointService(
      mockRedisComponent,
      mockServiceInfoRepository,
      mockRewardPointTxsRepository,
    ) as jest.Mocked<RewardPointService>;
    mockStatusPointService = new StatusPointService(
      mockStatusPointTxsRepository,
      mockRedisComponent,
    ) as jest.Mocked<StatusPointService>;
    rewardService = new RewardService(
      rewardRepository,
      accountRepository,
      claimedRewardRepository,
      achievementActionRepository,
      questActivityRepository,
      nftMintService,
      metadataService,
      questionnaireResultAnswerRepository,
      mockRewardPointService,
      mockStatusPointService,
    ) as jest.Mocked<RewardService>;
    serialCodeService = new SerialCodeService(
      serialCodeProjectsRepository,
      serialCodesRepository,
      accountSerialCodesRepository,
      rewardService,
    ) as jest.Mocked<SerialCodeService>;

    serialCodeController = new SerialCodeController(serialCodeService) as jest.Mocked<SerialCodeController>;

    context = {
      req: {
        header: jest.fn(),
        param: jest.fn(),
        json: jest.fn(),
      },
      json: jest.fn(),
    } as unknown as Context;
  });

  describe('import', () => {
    test('should import serial-code and return a response with status 200', async () => {
      context.req.json = jest.fn().mockResolvedValue({
        projectName: 'ProjectName',
        projectDescription: 'ProjectDescription',
        codes: [],
        createCodeNum: 1,
        maxUseNum: 1,
        rewardId: null,
        hashKey: '',
      });
      serialCodeService.import = jest.fn().mockResolvedValue({
        serialCodeProjectId: '9293485f-a63a-49c1-917b-4834c9498dc8',
        name: 'ProjectName',
        description: 'ProjectDescription',
        rewardId: null,
        startAt: '2025-01-01T00:00:00Z',
        endAt: '2025-12-31T00:00:00Z',
        serialCodes: [
          {
            serialCodeId: '9293485f-a63a-49c1-917b-4834c9498dc8',
            code: 'AAAA-BBBB-CCCC-DDDD',
            codeHash: '6146ccf6a66d994f7c363db875e31ca35581450a4bf6d3be6cc9ac79233a69d0',
            maxUseNum: 1,
          },
          {
            serialCodeId: '9293485f-a63a-49c1-917b-4834c9498dc8',
            code: '1111-2222-3333-4444',
            codeHash: '6146ccf6a66d994f7c363db875e31ca35581450a4bf6d3be6cc9ac79233a69d0',
            maxUseNum: 1,
          },
        ],
      });
      await serialCodeController.import(context);
    });

    test('should throw an error if serial-code import fails', async () => {
      context.req.json = jest.fn().mockResolvedValue({
        projectName: 'ProjectName',
        projectDescription: 'ProjectDescription',
        createCodeNum: 1,
        maxUseNum: 1,
        rewardId: null,
      });
      serialCodeService.import = jest.fn().mockRejectedValue(new Error('Failed to serial-code import'));

      await expect(serialCodeController.import(context)).rejects.toThrow('Failed to serial-code import');
    });
  });

  describe('create', () => {
    test('should create serial-code and return a response with status 200', async () => {
      context.req.json = jest.fn().mockResolvedValue({
        projectName: 'ProjectName',
        projectDescription: 'ProjectDescription',
        codes: [],
        createCodeNum: 1,
        maxUseNum: 1,
        rewardId: null,
        hashKey: '',
      });
      serialCodeService.create = jest.fn().mockResolvedValue({
        serialCodeProjectId: '9293485f-a63a-49c1-917b-4834c9498dc8',
        name: 'ProjectName',
        description: 'ProjectDescription',
        rewardId: null,
        startAt: '2025-01-01T00:00:00Z',
        endAt: '2025-12-31T00:00:00Z',
        serialCodes: [
          {
            serialCodeId: '9293485f-a63a-49c1-917b-4834c9498dc8',
            code: 'AAAA-BBBB-CCCC-DDDD',
            codeHash: '6146ccf6a66d994f7c363db875e31ca35581450a4bf6d3be6cc9ac79233a69d0',
            maxUseNum: 1,
          },
          {
            serialCodeId: '9293485f-a63a-49c1-917b-4834c9498dc8',
            code: '1111-2222-3333-4444',
            codeHash: '6146ccf6a66d994f7c363db875e31ca35581450a4bf6d3be6cc9ac79233a69d0',
            maxUseNum: 1,
          },
        ],
      });
      await serialCodeController.create(context);
    });

    test('should throw an error if serial-code create fails', async () => {
      context.req.json = jest.fn().mockResolvedValue({
        projectName: 'ProjectName',
        projectDescription: 'ProjectDescription',
        createCodeNum: 1,
        maxUseNum: 1,
        rewardId: null,
      });
      serialCodeService.create = jest.fn().mockRejectedValue(new Error('Failed to serial-code create'));

      await expect(serialCodeController.create(context)).rejects.toThrow('Failed to serial-code create');
    });
  });

  describe('redeem', () => {
    (parseCustomToken as jest.Mock).mockReturnValue({
      service_id: 'mockServiceId',
      account_id: 'mockAccountId',
    });
    test('should redeem serial-code and return a response with status 200', async () => {
      context.req.json = jest.fn().mockResolvedValue({
        projectName: 'ProjectName',
        projectDescription: 'ProjectDescription',
      });
      serialCodeService.redeemAndClaimReward = jest.fn().mockRejectedValue(new Error('Failed to serial-code redeem'));

      await expect(serialCodeController.redeem(context)).rejects.toThrow('Failed to serial-code redeem');
    });

    test('should throw an error if serial-code redeem fails', async () => {
      context.req.json = jest.fn().mockResolvedValue({
        projectName: 'ProjectName',
        projectDescription: 'ProjectDescription',
        createCodeNum: 1,
        maxUseNum: 1,
        rewardId: null,
      });
      serialCodeService.redeemAndClaimReward = jest.fn().mockRejectedValue(new Error('Failed to serial-code redeem'));

      await expect(serialCodeController.redeem(context)).rejects.toThrow('Failed to serial-code redeem');
    });
  });
});
