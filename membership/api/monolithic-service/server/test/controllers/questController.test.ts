import { Context } from 'hono';
import { QuestController } from '../../src/controllers/questController';
import { QuestRepository } from '../../src/repositories/questRepository';
import { QuestService } from '../../src/services/questService';
import { ActionRepository } from '../../src/repositories/actionRepository';
import { RewardRepository } from '../../src/repositories/rewardRepository';
import { QuestActionRepository } from '../../src/repositories/questActionRepository';
import { ActionActivityRepository } from '../../src/repositories/actionActivityRepository';
import { ClaimedRewardRepository } from '../../src/repositories/claimedRewardRepository';
import { QuestType } from '../../src/enum/questType';
import { ServiceInfoRepository } from '../../src/repositories/serviceInfoRepository';
import { QrCheckinActionRepository } from '../../src/repositories/qrCheckinActionRepository';
import { OnlineCheckinActionRepository } from '../../src/repositories/onlineCheckinActionRepository';
import { AchievementActionRepository } from '../../src/repositories/achievementActionRepository';
import { NftContractTypesRepository } from '../../src/repositories/nftContractTypesRepository';
import { NftRegisterService } from '../../src/services/nftRegisterService';
import { TransactionService } from '../../src/services/transactionService';
import { WebhookService } from '../../src/services/webhookService';
import { NftContractsRepository } from '../../src/repositories/nftContractsRepository';
import { VaultKeyRepository } from '../../src/repositories/vaultKeyRepository';
import { MetadataService } from '../../src/services/metadataService';
import { NftBaseMetadatasRepository } from '../../src/repositories/nftBaseMetadatasRepository';
import { QuestionnaireService } from '../../src/services/questionnaireService';
import { QuestionnaireThemeRepository } from '../../src/repositories/questionnaireThemeRepository';
import { QuestionnaireResultAnswerRepository } from '../../src/repositories/questionnaireResultAnswerRepository';
import { QuestionnaireQuestionRepository } from '../../src/repositories/questionnaireQuestionRepository';
import { QuestionnaireResultRankRepository } from '../../src/repositories/questionnaireResultRankRepository';
import { QuestionnaireRepository } from '../../src/repositories/questionnaireRepository';
import { QuestionnaireActionRepository } from '../../src/repositories/questionnaireActionRepository';
import { QuestDetailResponse } from '../../src/dtos/quests/response/questDetailResponse';
import { ActionType } from '../../src/enum/actionType';
import { QuestRewardPriorityType } from '../../src/enum/questRewardPriorityType';
import { QuestListItemResponse } from '../../src/dtos/quests/response/questResponseItem';
import { StatusQuest } from '../../src/dtos/services/schemas';
import { TransactionQueuesRepository } from '../../src/repositories/transactionQueuesRepository';
import { TransactionsRepository } from '../../src/repositories/transactionsRepository';
import { languageCode } from '../../src/enum/languageCode';
import { SerialCodeActionRepository } from '../../src/repositories/serialCodeActionRepository';
import { NftComponentRepository } from '../../src/repositories/nftComponentRepository';
import { RewardComponentRepository } from '../../src/repositories/rewardComponentRepository';
import { PointComponentRepository } from '../../src/repositories/pointComponentRepository';
import { parseCustomToken } from '../../src/utils/middleware/firebaseAuthAccessMiddleware';


jest.mock('../../src/utils/middleware/firebaseAuthAccessMiddleware', () => ({
  parseCustomToken: jest.fn(),
}));
jest.mock('../../src/repositories/questionnaireRepository');
jest.mock('../../src/repositories/questionnaireResultAnswerRepository');
jest.mock('../../src/repositories/questionnaireQuestionRepository');
jest.mock('../../src/repositories/questionnaireThemeRepository');
jest.mock('../../src/repositories/questionnaireResultRankRepository');
jest.mock('../../src/repositories/nftComponentRepository');
jest.mock('../../src/repositories/rewardComponentRepository');
jest.mock('../../src/repositories/pointComponentRepository');
jest.mock('../../src/components/redisComponent');

jest.mock('../../src/services/questService', () => ({
  QuestService: jest.fn().mockImplementation(() => {
    return {
      getQuests: jest.fn(),
      getQuest: jest.fn(),
      getAccountQuests: jest.fn(),
      getAccountQuest: jest.fn(),
    };
  }),
}));

describe('QuestController', () => {
  let questService: jest.Mocked<QuestService>;
  let questionnaireService: jest.Mocked<QuestionnaireService>;

  let questController: QuestController;
  let context: Context;
  let mockQuestRepository: jest.Mocked<QuestRepository>;
  let mockActionRepository: jest.Mocked<ActionRepository>;
  let mockRewardRepository: jest.Mocked<RewardRepository>;
  let mockQuestActionRepository: jest.Mocked<QuestActionRepository>;
  let mockActionActivityRepository: jest.Mocked<ActionActivityRepository>;
  let mockClaimedRewardRepository: jest.Mocked<ClaimedRewardRepository>;
  let mockServiceInfoRepository: jest.Mocked<ServiceInfoRepository>;
  let mockQrCheckinActionRepository: jest.Mocked<QrCheckinActionRepository>;
  let mockOnlineCheckinActionRepository: jest.Mocked<OnlineCheckinActionRepository>;
  let mockAchievementActionRepository: jest.Mocked<AchievementActionRepository>;
  let mockQuestionnaireActionRepository: jest.Mocked<QuestionnaireActionRepository>;
  let mockNftContractTypesRepository: jest.Mocked<NftContractTypesRepository>;
  let mockSerialCodeActionRepository: jest.Mocked<SerialCodeActionRepository>;
  let mockNftRegisterService: jest.Mocked<NftRegisterService>;
  let mockQuestionnaireThemeRepository: jest.Mocked<QuestionnaireThemeRepository>;
  let mockQuestionnaireResultAnswerRepository: jest.Mocked<QuestionnaireResultAnswerRepository>;
  let mockQuestionnaireQuestionRepository: jest.Mocked<QuestionnaireQuestionRepository>;
  let mockQuestionnaireResultRankRepository: jest.Mocked<QuestionnaireResultRankRepository>;
  let mockQuestionnaireRepository: jest.Mocked<QuestionnaireRepository>;
  let mockNftComponentRepository: jest.Mocked<NftComponentRepository>;
  let mockRewardComponentRepository: jest.Mocked<RewardComponentRepository>;
  let mockPointComponentRepository: jest.Mocked<PointComponentRepository>;

  beforeEach(() => {
    process.env.ALCHEMY_ADDRESS_ACTIVITY_WEBHOOK_ID = 'mockAddressActivityWebhookId';
    process.env.ALCHEMY_NFT_ACTIVITY_WEBHOOK_ID = 'mockNftActivityWebhookId';
    process.env.ALCHEMY_ADDRESS_ACTIVITY_WEBHOOK_SIGNING_KEY = 'mockSigningKey';
    process.env.ALCHEMY_NFT_ACTIVITY_WEBHOOK_SIGNING_KEY = 'mockSigningKey';
    process.env.ALCHEMY_CHAIN_NAME = 'polygon-amoy';
    process.env.ALCHEMY_API_KEY = 'xxxxxsss';
    process.env.ALCHEMY_AUTH_TOKEN = 'mockAuthToken';
    process.env.GCP_PROJECT_ID = 'sample_gcp_project_id';
    process.env.GCS_BUCKET_NAME = 'mock-bucket-name';

    mockQuestRepository = {} as jest.Mocked<QuestRepository>;
    mockActionRepository = {} as jest.Mocked<ActionRepository>;
    mockRewardRepository = {} as jest.Mocked<RewardRepository>;
    mockQuestActionRepository = {} as jest.Mocked<QuestActionRepository>;
    mockActionActivityRepository = {} as jest.Mocked<ActionActivityRepository>;
    mockClaimedRewardRepository = {} as jest.Mocked<ClaimedRewardRepository>;
    mockServiceInfoRepository = {} as jest.Mocked<ServiceInfoRepository>;
    mockQrCheckinActionRepository = {} as jest.Mocked<QrCheckinActionRepository>;
    mockOnlineCheckinActionRepository = {} as jest.Mocked<OnlineCheckinActionRepository>;
    mockAchievementActionRepository = {} as jest.Mocked<AchievementActionRepository>;
    mockQuestionnaireActionRepository = {} as jest.Mocked<QuestionnaireActionRepository>;
    mockSerialCodeActionRepository = {} as jest.Mocked<SerialCodeActionRepository>;
    mockNftContractTypesRepository = {} as jest.Mocked<NftContractTypesRepository>;
    mockQuestionnaireThemeRepository = new QuestionnaireThemeRepository() as jest.Mocked<QuestionnaireThemeRepository>;
    mockQuestionnaireResultAnswerRepository =
      new QuestionnaireResultAnswerRepository() as jest.Mocked<QuestionnaireResultAnswerRepository>;
    mockQuestionnaireQuestionRepository =
      new QuestionnaireQuestionRepository() as jest.Mocked<QuestionnaireQuestionRepository>;
    mockQuestionnaireResultRankRepository =
      new QuestionnaireResultRankRepository() as jest.Mocked<QuestionnaireResultRankRepository>;
    mockQuestionnaireRepository = new QuestionnaireRepository() as jest.Mocked<QuestionnaireRepository>;
    mockNftComponentRepository = new NftComponentRepository() as jest.Mocked<NftComponentRepository>;
    mockRewardComponentRepository = new RewardComponentRepository() as jest.Mocked<RewardComponentRepository>;

    mockNftRegisterService = new NftRegisterService(
      {} as TransactionService,
      {} as WebhookService,
      mockNftContractTypesRepository,
      {} as NftContractsRepository,
      {} as VaultKeyRepository,
      mockServiceInfoRepository,
      {} as MetadataService,
      {} as NftBaseMetadatasRepository,
      {} as TransactionQueuesRepository,
      {} as TransactionsRepository,
    ) as jest.Mocked<NftRegisterService>;
    mockPointComponentRepository = new PointComponentRepository() as jest.Mocked<PointComponentRepository>;

    questService = new QuestService(
      mockQuestRepository,
      mockActionRepository,
      mockRewardRepository,
      mockQuestActionRepository,
      mockActionActivityRepository,
      mockClaimedRewardRepository,
      mockServiceInfoRepository,
      mockNftRegisterService,
      mockQrCheckinActionRepository,
      mockOnlineCheckinActionRepository,
      mockAchievementActionRepository,
      mockQuestionnaireActionRepository,
      mockSerialCodeActionRepository,
      mockQuestionnaireResultRankRepository,
      mockNftComponentRepository,
      mockRewardComponentRepository,
      mockPointComponentRepository,
    ) as jest.Mocked<QuestService>;

    questionnaireService = new QuestionnaireService(
      mockServiceInfoRepository,
      mockQuestionnaireRepository,
      mockQuestionnaireThemeRepository,
      mockQuestionnaireResultAnswerRepository,
      mockQuestionnaireQuestionRepository,
    ) as jest.Mocked<QuestionnaireService>;

    questController = new QuestController(questService, questionnaireService);

    context = {
      req: {
        header: jest.fn(),
        param: jest.fn(),
        query: jest.fn(),
        json: jest.fn(),
      },
      json: jest.fn(),
      get: jest.fn().mockReturnValue(languageCode.EN_US),
    } as unknown as Context;
  });

  describe('getQuests', () => {
    const mockServiceId = 'test-service-id';

    (parseCustomToken as jest.Mock).mockReturnValue({
      service_id: mockServiceId,
      account_id: 'mockAccountId',
    });
    test('should return quests with status 200', async () => {
      const mockResponse: QuestListItemResponse[] = [
        {
          questId: 'quest1',
          title: 'Quest 1',
          questType: QuestType.CUSTOM,
          mainRewardTitle: 'Reward 1',
          thumbnailImageUrl: 'http://example.com/thumb1.jpg',
          mainRewardThumbnailImageUrl: 'http://example.com/thumb1.jpg',
          orderIndex: 1,
          startedAt: '2022-12-31T17:00:00',
          expiredAt: '2023-12-31T16:59:59',
        },
        {
          questId: 'quest2',
          title: 'Quest 2',
          questType: QuestType.CUSTOM,
          mainRewardTitle: 'Reward 1',
          thumbnailImageUrl: 'http://example.com/thumb2.jpg',

          mainRewardThumbnailImageUrl: 'http://example.com/thumb1.jpg',
          orderIndex: 2,
          startedAt: '2022-12-31T17:00:00',
          expiredAt: '2023-12-31T16:59:59',
        },
      ];

      (context.req.header as jest.Mock).mockReturnValue(mockServiceId);
      (context.get as jest.Mock).mockReturnValue(languageCode.EN_US);
      questService.getQuests.mockResolvedValue(mockResponse);

      await questController.getQuests(context);

      expect(questService.getQuests).toHaveBeenCalledWith(mockServiceId, languageCode.EN_US);
      expect(context.json).toHaveBeenCalledWith(mockResponse, 200);
    });

    test('should handle errors gracefully', async () => {
      const mockError = new Error('Test error');

      (context.req.header as jest.Mock).mockReturnValue(mockServiceId);
      questService.getQuests.mockRejectedValue(mockError);

      await expect(questController.getQuests(context)).rejects.toThrow('Test error');
    });
  });

  describe('getQuest', () => {
    const mockServiceId = 'test-service-id';
    (parseCustomToken as jest.Mock).mockReturnValue({
      service_id: mockServiceId,
      account_id: 'account-id',
    });
    test('should return quest details with status 200', async () => {
      const mockQuestId = 'quest1';
      const mockQuestDetail: QuestDetailResponse = {
        questId: 'quest1',
        title: 'Quest 1',
        description: 'Description of Quest 1',
        coverImageUrl: 'http://example.com/cover1.jpg',
        startedAt: new Date('2022-12-31T17:00:00.000Z').toISOString(),
        expiredAt: new Date('2023-12-31T16:59:59.000Z').toISOString(),
        questType: QuestType.CUSTOM,
        rewards: [
          {
            rewardId: 'reward1',
            title: 'Reward One',
            thumbnailImageUrl: 'http://example.com/reward_thumb1.jpg',
            rewardPriorityType: QuestRewardPriorityType.MAIN,
            orderIndex: 1,
          },
        ],
        actions: [
          {
            actionId: 'action1',
            title: 'Action One',
            availableStartDate: '2020-01-01T00:00:00Z',
            availableEndDate: '2030-01-01T00:00:00Z',
            thumbnailImageUrl: 'http://example.com/action_thumb1.jpg',
            actionType: ActionType.ONLINE_CHECKIN,
            orderIndex: 2,
          },
        ],
      };

      (context.req.param as jest.Mock).mockReturnValue(mockQuestId);
      (context.req.header as jest.Mock).mockReturnValue(mockServiceId);
      (context.get as jest.Mock).mockReturnValue(languageCode.EN_US);
      questService.getQuest.mockResolvedValue(mockQuestDetail);

      await questController.getQuest(context);

      expect(context.req.param).toHaveBeenCalledWith('questId');
      expect(questService.getQuest).toHaveBeenCalledWith(mockQuestId, mockServiceId, languageCode.EN_US);
      expect(context.json).toHaveBeenCalledWith(mockQuestDetail, 200);
    });

    test('should handle errors gracefully in getQuest', async () => {
      const mockQuestId = 'quest1';
      const mockServiceId = 'test-service-id';
      const mockError = new Error('Test error');

      (context.req.param as jest.Mock).mockReturnValue(mockQuestId);
      (context.req.header as jest.Mock).mockReturnValue(mockServiceId);
      questService.getQuest.mockRejectedValue(mockError);

      await expect(questController.getQuest(context)).rejects.toThrow('Test error');
    });
  });

  describe('getAccountQuests', () => {
    const mockServiceId = 'test-service-id';
    const mockAccountId = 'account-id';

    test('should return account quests for a valid accountId and serviceId', async () => {
      // parseCustomTokenのモックを各テストケース内で設定
      (parseCustomToken as jest.Mock).mockReturnValue({
        service_id: mockServiceId,
        account_id: mockAccountId,
      });
      const mockAccountQuests = {
        questIds: ['quest1', 'quest2', 'quest3'],
      };

      (context.req.param as jest.Mock).mockReturnValue(mockAccountId);
      (context.req.header as jest.Mock).mockReturnValue(mockServiceId);
      (context.req.query as jest.Mock).mockImplementation((queryName: string) => {
        switch (queryName) {
          case 'rewardStatus':
            return 'NOT_ACQUIRED';
          case 'questStatus':
            return 'ACTIVE';
          case 'startAtFrom':
            return '2023-01-01T00:00:00Z';
          case 'startAtTo':
            return '2023-12-31T23:59:59Z';
          case 'expireAtFrom':
            return '2023-06-01T00:00:00Z';
          case 'expireAtTo':
            return '2023-12-31T23:59:59Z';
          default:
            return undefined;
        }
      });
      questService.getAccountQuests.mockResolvedValue(mockAccountQuests);

      await questController.getAccountQuests(context);

      expect(questService.getAccountQuests).toHaveBeenCalledWith(
        mockAccountId,
        mockServiceId,
        'NOT_ACQUIRED', // rewardStatus
        'ACTIVE', // questStatus
        '2023-01-01T00:00:00Z', // startAtFrom
        '2023-12-31T23:59:59Z', // startAtTo
        '2023-06-01T00:00:00Z', // expireAtFrom
        '2023-12-31T23:59:59Z', // expireAtTo
      );
      expect(context.json).toHaveBeenCalledWith(mockAccountQuests, 200);
    });

    test('should handle errors gracefully in getAccountQuests', async () => {
      const mockError = new Error('Test error');

      questService.getAccountQuests.mockRejectedValue(mockError);


      await expect(questController.getAccountQuests(context)).rejects.toThrow('Test error');
    });
  });

  describe('getQuestsByStatus', () => {
    const mockServiceId = 'test-service-id';
    (parseCustomToken as jest.Mock).mockReturnValue({
      service_id: mockServiceId,
      account_id: 'mockAccountId',
    });
    test('should return quests by status with status 200', async () => {
      const mockQuestsStatus: StatusQuest = {
        questId: '123e4567-e89b-12d3-a456-************',
        title: 'The Great Adventure',
        description: 'Embark on a journey through the Enchanted Forest to uncover hidden treasures.',
        startedAt: new Date('2023-01-01T00:00:00.000Z').toISOString(),
        expiredAt: new Date('2023-01-31T23:59:59.999Z').toISOString(),
        actions: [
          {
            actionId: 'action-001',
            title: 'Discover the Hidden Valley',
            rewardId: 'reward-201',
            statusRank: 1,
            milestone: 5,
          },
        ],
      };
      questService.getActiveStatusQuest = jest.fn().mockResolvedValue(mockQuestsStatus);

      (context.req.header as jest.Mock).mockReturnValue(mockServiceId);

      await questController.getQuestsByStatus(context);

      expect(questService.getActiveStatusQuest).toHaveBeenCalledWith(mockServiceId, languageCode.EN_US);
      expect(context.json).toHaveBeenCalledWith(mockQuestsStatus, 200);
    });

    test('should return an empty array when no quests are found', async () => {
      questService.getActiveStatusQuest = jest.fn().mockResolvedValue([]);

      (context.req.header as jest.Mock).mockReturnValue(mockServiceId);

      await questController.getQuestsByStatus(context);

      expect(questService.getActiveStatusQuest).toHaveBeenCalledWith(mockServiceId, languageCode.EN_US);
      expect(context.json).toHaveBeenCalledWith([], 200);
    });

    test('should handle errors when encountered in service', async () => {
      const mockError = new Error('Internal Server Error');

      questService.getActiveStatusQuest = jest.fn().mockRejectedValue(mockError);

      (context.req.header as jest.Mock).mockReturnValue(mockServiceId);

      await expect(questController.getQuestsByStatus(context)).rejects.toThrow(mockError);
      expect(questService.getActiveStatusQuest).toHaveBeenCalledWith(mockServiceId, languageCode.EN_US);
    });
  });
});
