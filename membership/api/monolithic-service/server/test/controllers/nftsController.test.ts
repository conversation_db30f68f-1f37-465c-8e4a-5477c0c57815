import { Context } from 'hono';
import { NftsController } from '../../src/controllers/nftsController';
import { NftsService } from '../../src/services/nftsService';
import { NftMetadatasRepository } from '../../src/repositories/nftMetadatasRepository';
import { NftContractsRepository } from '../../src/repositories/nftContractsRepository';
import { AccountRepository } from '../../src/repositories/accountRepository';
import { RewardRepository } from '../../src/repositories/rewardRepository';
import { TransactionService } from '../../src/services/transactionService';
import { WebhookService } from '../../src/services/webhookService';
import { NftBaseMetadatasRepository } from '../../src/repositories/nftBaseMetadatasRepository';
import { VaultKeyRepository } from '../../src/repositories/vaultKeyRepository';
import { NftRegisterService } from '../../src/services/nftRegisterService';
import { NftContractTypesRepository } from '../../src/repositories/nftContractTypesRepository';
import { ServiceInfoRepository } from '../../src/repositories/serviceInfoRepository';
import { MetadataService } from '../../src/services/metadataService';
import { NftTag } from '../../src/enum/nftTag';
import { NotFoundError } from '../../src/errors/notFoundError';
import { NftMintService } from '../../src/services/nftMintService';
import { TransactionComponent } from '../../src/components/transactionComponent';
import {
  GetI18nNFTMetadataResponse,
  NftRegister,
  RegisterTokenGateRequest,
  VerifySigRequest,
  VerifySigResponse,
  RegisterTokenGateResponse,
  CheckTokenGateAccessResponse,
  CheckTokenGateAccessRequest,
} from '../../src/dtos/nfts/schemas';
import { NftDeploy } from '../../src/dtos/nfts/schemas';
import { AttemptTransactionsRepository } from '../../src/repositories/attemptTransactionsRepository';
import { TransactionsRepository } from '../../src/repositories/transactionsRepository';
import { TransactionQueuesRepository } from '../../src/repositories/transactionQueuesRepository';
import { BulkMintService } from '../../src/services/bulkMintService';
import { TokenBoundAccountImplementationRepository } from '../../src/repositories/tokenBoundAccountImplementationsRepository';
import { TokenBoundAccountRegistryAddressRepository } from '../../src/repositories/tokenBoundAccountRegistryAddressRepository';
import { DeliveryNftsFirestoreRepository } from '../../src/repositories/deliveryNftsFirestoreRepository';
import { I18nService } from '../../src/services/i18nService';
import { GptComponent } from '../../src/components/gptComponent';
import { RedisComponent } from '../../src/components/redisComponent';
import { NftsFirestoreRepository } from '../../src/repositories/nftsFirestoreRepository';
import { NftTransactionUpdateService } from '../../src/services/transactionUpdateService';
import { ViemComponent } from '../../src/components/viemComponent';
import { TokenGateService } from '../../src/services/tokenGateService';
import { TokenGateRepository } from '../../src/repositories/tokenGateRepository';
import { parseCustomToken } from '../../src/utils/middleware/firebaseAuthAccessMiddleware';

jest.mock('../../src/utils/middleware/firebaseAuthAccessMiddleware', () => ({
  parseCustomToken: jest.fn(),
}));
jest.mock('../../src/services/nftsService');
jest.mock('../../src/services/metadataService');
jest.mock('../../src/services/nftMintService');
jest.mock('../../src/services/nftRegisterService');
jest.mock('../../src/services/transactionService');
jest.mock('../../src/services/i18nService');
jest.mock('../../src/services/tokenGateService');

describe('NftsController', () => {
  let nftsController: NftsController;

  let mockNftsService: jest.Mocked<NftsService>;
  let mockMetadataService: jest.Mocked<MetadataService>;
  let mockNftRegisterService: jest.Mocked<NftRegisterService>;
  let mockTransactionService: jest.Mocked<TransactionService>;
  let mockNftMintService: jest.Mocked<NftMintService>;
  let mockI18nService: jest.Mocked<I18nService>;
  let mockBulkMintService: jest.Mocked<BulkMintService>;
  let mockTokenGateService: jest.Mocked<TokenGateService>;

  beforeEach(() => {
    process.env.ALCHEMY_ADDRESS_ACTIVITY_WEBHOOK_ID = 'mockAddressActivityWebhookId';
    process.env.ALCHEMY_NFT_ACTIVITY_WEBHOOK_ID = 'mockNftActivityWebhookId';
    process.env.ALCHEMY_ADDRESS_ACTIVITY_WEBHOOK_SIGNING_KEY = 'mockSigningKey';
    process.env.ALCHEMY_NFT_ACTIVITY_WEBHOOK_SIGNING_KEY = 'mockSigningKey';
    process.env.ALCHEMY_CHAIN_NAME = 'polygon-amoy';
    process.env.ALCHEMY_API_KEY = 'xxxxxsss';
    process.env.ALCHEMY_AUTH_TOKEN = 'mockAuthToken';
    process.env.GCP_PROJECT_ID = 'sample_gcp_project_id';
    process.env.GCS_BUCKET_NAME = 'mock-bucket-name';

    mockNftsService = new NftsService(
      {} as NftContractsRepository,
      {} as AccountRepository,
      {} as RewardRepository,
    ) as jest.Mocked<NftsService>;

    mockMetadataService = new MetadataService(
      {} as NftMetadatasRepository,
      {} as NftBaseMetadatasRepository,
      {} as NftContractTypesRepository,
    ) as jest.Mocked<MetadataService>;

    mockNftRegisterService = new NftRegisterService(
      {} as TransactionService,
      {} as WebhookService,
      {} as NftContractTypesRepository,
      {} as NftContractsRepository,
      {} as VaultKeyRepository,
      {} as ServiceInfoRepository,
      {} as MetadataService,
      {} as NftBaseMetadatasRepository,
      {} as TransactionQueuesRepository,
      {} as TransactionsRepository,
    ) as jest.Mocked<NftRegisterService>;

    mockTransactionService = new TransactionService(
      {} as TransactionComponent,
      {} as AttemptTransactionsRepository,
      {} as TransactionsRepository,
      {} as TransactionQueuesRepository,
      {} as ServiceInfoRepository,
      {} as NftContractsRepository,
    ) as jest.Mocked<TransactionService>;

    mockNftMintService = new NftMintService(
      {} as NftContractsRepository,
      {} as TransactionQueuesRepository,
      {} as VaultKeyRepository,
      {} as ServiceInfoRepository,
      {} as DeliveryNftsFirestoreRepository,
    ) as jest.Mocked<NftMintService>;
    mockBulkMintService = new BulkMintService(
      {} as TransactionService,
      {} as NftMintService,
      {} as NftContractsRepository,
      {} as TransactionQueuesRepository,
      {} as TransactionsRepository,
      {} as VaultKeyRepository,
      {} as ServiceInfoRepository,
      {} as TokenBoundAccountImplementationRepository,
      {} as TokenBoundAccountRegistryAddressRepository,
      {} as AccountRepository,
      {} as DeliveryNftsFirestoreRepository,
      {} as NftBaseMetadatasRepository,
      {} as MetadataService,
    ) as jest.Mocked<BulkMintService>;

    mockI18nService = new I18nService(
      {} as NftsFirestoreRepository,
      {} as GptComponent,
      {} as RedisComponent,
    ) as jest.Mocked<I18nService>;

    mockTokenGateService = new TokenGateService(
      {} as RedisComponent,
      {} as AccountRepository,
      {} as NftsFirestoreRepository,
      {} as ViemComponent,
      {} as TokenGateRepository,
    ) as jest.Mocked<TokenGateService>;

    nftsController = new NftsController(
      mockNftsService,
      mockMetadataService,
      mockNftRegisterService,
      mockTransactionService,
      mockNftMintService,
      {} as NftTransactionUpdateService,
      mockBulkMintService,
      mockI18nService,
      mockTokenGateService,
    );
  });

  describe('searchMetadataByWallet() method', () => {
    test('should return metadata list and status 200', async () => {
      const mockContext = {
        req: {
          header: jest.fn().mockReturnValue('sample-service-id'),
          json: jest.fn().mockResolvedValue({ walletAddress: '0xWalletAddress' }),
        },
        json: jest.fn(),
      } as unknown as Context;

      const mockMetadataList = mockNftsService.createContentResponse([
        {
          rewardId: 'reward1',
          nftContractId: 'contract1',
          contractAddress: '0xContractAddress1',
          tokenId: 1,
          amount: 1,
          title: 'NFT #1',
          description: 'Description for NFT #1',
          imageUrl: 'http://example.com/nft1.png',
          tags: NftTag.DIGITAL_CONTENT,
        },
        {
          rewardId: 'reward2',
          nftContractId: 'contract2',
          contractAddress: '0xContractAddress2',
          tokenId: 2,
          amount: 1,
          title: 'NFT #2',
          description: 'Description for NFT #2',
          imageUrl: 'http://example.com/nft2.png',
          tags: NftTag.DIGITAL_CONTENT,
        },
      ]);

      mockNftsService.getMetadataByWallet.mockResolvedValueOnce(mockMetadataList);

      await nftsController.searchMetadataByWallet(mockContext);

      expect(mockNftsService.getMetadataByWallet).toHaveBeenCalledWith('sample-service-id', {
        walletAddress: '0xWalletAddress',
      });
      expect(mockContext.json).toHaveBeenCalledWith(mockMetadataList, 200);
    });

    test('should throw NotFoundError if metadata is not found', async () => {
      const mockContext = {
        req: {
          param: jest.fn().mockReturnValueOnce('0x123456789abcdef').mockReturnValueOnce('42'),
        },
        json: jest.fn(),
      } as unknown as Context;

      mockMetadataService.getNftMetadata.mockRejectedValueOnce(new NotFoundError());

      await expect(nftsController.getMetadata(mockContext)).rejects.toThrow(NotFoundError);
    });
  });

  describe('register', () => {
    test('should register NFT and return result with status 200', async () => {
      // const serviceId = '8552df83-cbd5-44db-b67e-0cbeb2785918';
      const mockNftRegisterRequest: NftRegister = {
        nftContractTypeId: '8552df83-cbd5-44db-b67e-0cbeb2785918',
        nftName: 'marbull NFT',
        nftSymbol: 'MBL1',
        nftCollectionName: 'Marbull NFT collection',
        metadata: 'e30=',
        deliveryImageUrl: 'https://aa.example.com',
      };
      const mockNftRegisterResponse: NftDeploy = {
        nftContractId: '8552df83-cbd5-44db-b67e-0cbeb2785918',
        contractAddress: '******************************************',
      };
      const mockContext = {
        req: {
          header: jest.fn().mockReturnValue('sample-service-id'),
          json: jest.fn().mockResolvedValue(mockNftRegisterRequest),
        },
        json: jest.fn(),
      } as unknown as Context;

      mockNftRegisterService.register.mockResolvedValueOnce(mockNftRegisterResponse);

      await nftsController.register(mockContext);

      expect(mockContext.json).toHaveBeenCalledWith(mockNftRegisterResponse, 200);
    });
  });

  describe('getI18nNftMetadata() method', () => {
        (parseCustomToken as jest.Mock).mockReturnValue({
          service_id: 'mockServiceId',
          account_id: 'mockAccountId',
        });
    test('should return i18n nft metadata and status 200', async () => {
      const mockContext = {
        req: {
          param: jest.fn().mockReturnValueOnce('******************************************').mockReturnValueOnce('42'),
          header: jest.fn().mockReturnValue('sample-account-id'),
        },
        get: jest.fn().mockReturnValue('en-US'),
        json: jest.fn(),
      } as unknown as Context;

      const mockI18nNftMetadata: GetI18nNFTMetadataResponse = {
        name: 'test',
        description: 'test',
      };

      mockI18nService.getI18nNftMetadata.mockResolvedValueOnce(mockI18nNftMetadata);
      await nftsController.getI18nMetadata(mockContext);

      expect(mockI18nService.getI18nNftMetadata).toHaveBeenCalledWith(
        '******************************************',
        '42',
        'mockAccountId',
        'en-US',
      );
      expect(mockContext.json).toHaveBeenCalledWith(mockI18nNftMetadata, 200);
    });
  });

  describe('verifySigAndFetchNfts() method', () => {
    test('should return nfts and status 200', async () => {
      const mockRequestBody: VerifySigRequest = {
        sig: '0x143538a788799ff8f30ddfe0691fb997ebf3d3cc509491436201ac90713b521e1407c5e413e5648a206ec16dec0a18dfa443309c9ad185faec1746ed32fa903c1b',
        msg: 'Nonce: 4db9e045d4527024302b84e202c2431b501bb051d466004970b9bd7aaa59118d\nIssued At: 2025-06-05T14:00:00Z\nExpiration: 2025-08-05T14:00:00Z\nDomain: marbullx.com',
      };
      const mockContext = {
        req: {
          header: jest.fn().mockReturnValue('F8DAFCE9-C629-4E39-9F75-7C93D8BC3AF'),
          param: jest.fn().mockReturnValueOnce('55DAFCE9-C629-4E39-9F75-37C93D8BC3AF'),
          json: jest.fn().mockResolvedValue(mockRequestBody),
        },
        json: jest.fn(),
      } as unknown as Context;

      const mockNfts: VerifySigResponse = {
        ownedNfts: [
          {
            chainId: '137',
            contractAddress: '******************************************',
            tokenId: '1',
          },
        ],
      };
      mockTokenGateService.verifySigAndFetchNfts.mockResolvedValueOnce(mockNfts);
      await nftsController.verifySigAndFetchNfts(mockContext);
      expect(mockContext.json).toHaveBeenCalledWith(mockNfts, 200);
    });
  });

  describe('checkTokenGateAccess() method', () => {
    test('should check token gate access and return result with status 200', async () => {
      const mockTokenGateRequest: CheckTokenGateAccessRequest = {
        tokenGateIds: ['123e4567-e89b-12d3-a456-426614174000'],
        nfts: [
          {
            contractAddress: '******************************************',
            chainId: '137',
            tokenId: '1',
          },
        ],
      };
      const mockTokenGateResponse: CheckTokenGateAccessResponse = {
        passed: [],
        failed: [],
      };
      const mockContext = {
        req: {
          header: jest.fn().mockReturnValue('F8DAFCE9-C629-4E39-9F75-7C93D8BC3AF'),
          json: jest.fn().mockResolvedValue(mockTokenGateRequest),
        },
        json: jest.fn(),
      } as unknown as Context;

      mockTokenGateService.checkTokenGateAccess.mockResolvedValueOnce(mockTokenGateResponse);
      await nftsController.checkTokenGateAccess(mockContext);
      expect(mockContext.json).toHaveBeenCalledWith(mockTokenGateResponse, 200);
    });
  });

  describe('registerTokenGate() method', () => {
    test('should register token gate and return result with status 201', async () => {
      const mockTokenGateRequest: RegisterTokenGateRequest = {
        tokenGateTranslations: [
          {
            language: 'ja',
            gateName: '限定記事42号用TokenGate',
            gateDescription:
              'この記事は、スペシャルNFT X とスペシャルNFT Y（またはZ）の両方を所持しているユーザーのみが閲覧可能です。',
          },
          {
            language: 'en-US',
            gateName: 'TokenGate for Exclusive Article #42',
            gateDescription:
              'Only users who own Special NFT X plus either Special NFT Y or Special NFT Z can view this article.',
          },
        ],
        tokenGateConditions: [
          {
            setNumber: 1,
            nft: {
              chainId: '137',
              contractAddress: '******************************************',
            },
          },
          {
            setNumber: 2,
            nft: {
              chainId: '137',
              contractAddress: '0xEEEE5555EEEE5555EEEE5555EEEE5555EEEE5555',
              tokenId: '77',
            },
          },
          {
            setNumber: 2,
            nft: {
              chainId: '137',
              contractAddress: '0xFFFF6666FFFF6666FFFF6666FFFF6666FFFF6666',
              tokenId: '999',
            },
          },
        ],
      };
      const mockTokenGateResponse: RegisterTokenGateResponse = {
        tokenGateId: '123e4567-e89b-12d3-a456-426614174000',
      };
      const mockContext = {
        req: {
          header: jest.fn().mockReturnValue('sample-service-id'),
          json: jest.fn().mockResolvedValue(mockTokenGateRequest),
        },
        json: jest.fn(),
      } as unknown as Context;

      mockTokenGateService.registerTokenGate.mockResolvedValueOnce(mockTokenGateResponse);
      await nftsController.registerTokenGate(mockContext);
      expect(mockContext.json).toHaveBeenCalledWith(mockTokenGateResponse, 201);
    });
  });
});
