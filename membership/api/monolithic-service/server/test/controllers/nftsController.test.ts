import { Context } from 'hono';
import { NftsController } from '../../src/controllers/nftsController';
import { NftsService } from '../../src/services/nftsService';
import { NftMetadatasRepository } from '../../src/repositories/nftMetadatasRepository';
import { NftContractsRepository } from '../../src/repositories/nftContractsRepository';
import { AccountRepository } from '../../src/repositories/accountRepository';
import { RewardRepository } from '../../src/repositories/rewardRepository';
import { TransactionService } from '../../src/services/transactionService';
import { WebhookService } from '../../src/services/webhookService';
import { NftBaseMetadatasRepository } from '../../src/repositories/nftBaseMetadatasRepository';
import { VaultKeyRepository } from '../../src/repositories/vaultKeyRepository';
import { NftRegisterService } from '../../src/services/nftRegisterService';
import { NftContractTypesRepository } from '../../src/repositories/nftContractTypesRepository';
import { ServiceInfoRepository } from '../../src/repositories/serviceInfoRepository';
import { MetadataService } from '../../src/services/metadataService';
import { NftTag } from '../../src/enum/nftTag';
import { NotFoundError } from '../../src/errors/notFoundError';
import { NftMintService } from '../../src/services/nftMintService';
import { TransactionComponent } from '../../src/components/transactionComponent';
import { GetI18nNFTMetadataResponse, NftRegister } from '../../src/dtos/nfts/schemas';
import { NftDeploy } from '../../src/dtos/nfts/schemas';
import { AttemptTransactionsRepository } from '../../src/repositories/attemptTransactionsRepository';
import { TransactionsRepository } from '../../src/repositories/transactionsRepository';
import { TransactionQueuesRepository } from '../../src/repositories/transactionQueuesRepository';
import { BulkMintService } from '../../src/services/bulkMintService';
import { TokenBoundAccountImplementationRepository } from '../../src/repositories/tokenBoundAccountImplementationsRepository';
import { TokenBoundAccountRegistryAddressRepository } from '../../src/repositories/tokenBoundAccountRegistryAddressRepository';
import { DeliveryNftsFirestoreRepository } from '../../src/repositories/deliveryNftsFirestoreRepository';
import { I18nService } from '../../src/services/i18nService';
import { GptComponent } from '../../src/components/gptComponent';
import { RedisComponent } from '../../src/components/redisComponent';
import { NftsFirestoreRepository } from '../../src/repositories/nftsFirestoreRepository';
import { NftTransactionUpdateService } from '../../src/services/transactionUpdateService';

jest.mock('../../src/services/nftsService');
jest.mock('../../src/services/metadataService');
jest.mock('../../src/services/nftMintService');
jest.mock('../../src/services/nftRegisterService');
jest.mock('../../src/services/transactionService');
jest.mock('../../src/services/i18nService');
describe('NftsController', () => {
  let nftsController: NftsController;

  let mockNftsService: jest.Mocked<NftsService>;
  let mockMetadataService: jest.Mocked<MetadataService>;
  let mockNftRegisterService: jest.Mocked<NftRegisterService>;
  let mockTransactionService: jest.Mocked<TransactionService>;
  let mockNftMintService: jest.Mocked<NftMintService>;
  let mockI18nService: jest.Mocked<I18nService>;
  let mockBulkMintService: jest.Mocked<BulkMintService>;
  beforeEach(() => {
    process.env.ALCHEMY_ADDRESS_ACTIVITY_WEBHOOK_ID = 'mockAddressActivityWebhookId';
    process.env.ALCHEMY_NFT_ACTIVITY_WEBHOOK_ID = 'mockNftActivityWebhookId';
    process.env.ALCHEMY_ADDRESS_ACTIVITY_WEBHOOK_SIGNING_KEY = 'mockSigningKey';
    process.env.ALCHEMY_NFT_ACTIVITY_WEBHOOK_SIGNING_KEY = 'mockSigningKey';
    process.env.ALCHEMY_CHAIN_NAME = 'polygon-amoy';
    process.env.ALCHEMY_API_KEY = 'xxxxxsss';
    process.env.ALCHEMY_AUTH_TOKEN = 'mockAuthToken';
    process.env.GCP_PROJECT_ID = 'sample_gcp_project_id';
    process.env.GCS_BUCKET_NAME = 'mock-bucket-name';

    mockNftsService = new NftsService(
      {} as NftContractsRepository,
      {} as AccountRepository,
      {} as RewardRepository,
    ) as jest.Mocked<NftsService>;

    mockMetadataService = new MetadataService(
      {} as NftMetadatasRepository,
      {} as NftBaseMetadatasRepository,
      {} as NftContractTypesRepository,
    ) as jest.Mocked<MetadataService>;

    mockNftRegisterService = new NftRegisterService(
      {} as TransactionService,
      {} as WebhookService,
      {} as NftContractTypesRepository,
      {} as NftContractsRepository,
      {} as VaultKeyRepository,
      {} as ServiceInfoRepository,
      {} as MetadataService,
      {} as NftBaseMetadatasRepository,
      {} as TransactionQueuesRepository,
      {} as TransactionsRepository,
    ) as jest.Mocked<NftRegisterService>;

    mockTransactionService = new TransactionService(
      {} as TransactionComponent,
      {} as AttemptTransactionsRepository,
      {} as TransactionsRepository,
      {} as TransactionQueuesRepository,
      {} as ServiceInfoRepository,
      {} as NftContractsRepository,
    ) as jest.Mocked<TransactionService>;

    mockNftMintService = new NftMintService(
      {} as NftContractsRepository,
      {} as TransactionQueuesRepository,
      {} as VaultKeyRepository,
      {} as ServiceInfoRepository,
      {} as DeliveryNftsFirestoreRepository,
    ) as jest.Mocked<NftMintService>;
    mockBulkMintService = new BulkMintService(
      {} as TransactionService,
      {} as NftMintService,
      {} as NftContractsRepository,
      {} as TransactionQueuesRepository,
      {} as TransactionsRepository,
      {} as VaultKeyRepository,
      {} as ServiceInfoRepository,
      {} as TokenBoundAccountImplementationRepository,
      {} as TokenBoundAccountRegistryAddressRepository,
      {} as AccountRepository,
      {} as DeliveryNftsFirestoreRepository,
      {} as NftBaseMetadatasRepository,

    ) as jest.Mocked<BulkMintService>;

    mockI18nService = new I18nService(
      {} as NftsFirestoreRepository,
      {} as GptComponent,
      {} as RedisComponent,
    ) as jest.Mocked<I18nService>;

    nftsController = new NftsController(
      mockNftsService,
      mockMetadataService,
      mockNftRegisterService,
      mockTransactionService,
      mockNftMintService,
      {} as NftTransactionUpdateService,
      mockBulkMintService,
      mockI18nService,
    );
  });

  describe('searchMetadataByWallet() method', () => {
    test('should return metadata list and status 200', async () => {
      const mockContext = {
        req: {
          header: jest.fn().mockReturnValue('sample-service-id'),
          json: jest.fn().mockResolvedValue({ walletAddress: '0xWalletAddress' }),
        },
        json: jest.fn(),
      } as unknown as Context;

      const mockMetadataList = mockNftsService.createContentResponse([
        {
          rewardId: 'reward1',
          nftContractId: 'contract1',
          contractAddress: '0xContractAddress1',
          tokenId: 1,
          amount: 1,
          title: 'NFT #1',
          description: 'Description for NFT #1',
          imageUrl: 'http://example.com/nft1.png',
          tags: NftTag.DIGITAL_CONTENT,
        },
        {
          rewardId: 'reward2',
          nftContractId: 'contract2',
          contractAddress: '0xContractAddress2',
          tokenId: 2,
          amount: 1,
          title: 'NFT #2',
          description: 'Description for NFT #2',
          imageUrl: 'http://example.com/nft2.png',
          tags: NftTag.DIGITAL_CONTENT,
        },
      ]);

      mockNftsService.getMetadataByWallet.mockResolvedValueOnce(mockMetadataList);

      await nftsController.searchMetadataByWallet(mockContext);

      expect(mockNftsService.getMetadataByWallet).toHaveBeenCalledWith('sample-service-id', {
        walletAddress: '0xWalletAddress',
      });
      expect(mockContext.json).toHaveBeenCalledWith(mockMetadataList, 200);
    });

    test('should throw NotFoundError if metadata is not found', async () => {
      const mockContext = {
        req: {
          param: jest.fn().mockReturnValueOnce('0x123456789abcdef').mockReturnValueOnce('42'),
        },
        json: jest.fn(),
      } as unknown as Context;

      mockMetadataService.getNftMetadata.mockRejectedValueOnce(new NotFoundError());

      await expect(nftsController.getMetadata(mockContext)).rejects.toThrow(NotFoundError);
    });
  });

  describe('register() method', () => {
    test('should register NFT and return result with status 200', async () => {
      // const serviceId = '8552df83-cbd5-44db-b67e-0cbeb2785918';
      const mockNftRegisterRequest: NftRegister = {
        nftContractTypeId: '8552df83-cbd5-44db-b67e-0cbeb2785918',
        nftName: 'marbull NFT',
        nftSymbol: 'MBL1',
        nftCollectionName: 'Marbull NFT collection',
        metadata: 'e30=',
        deliveryImageUrl: 'https://aa.example.com',
      };
      const mockNftRegisterResponse: NftDeploy = {
        nftContractId: '8552df83-cbd5-44db-b67e-0cbeb2785918',
        contractAddress: '******************************************',
      };
      const mockContext = {
        req: {
          header: jest.fn().mockReturnValue('sample-service-id'),
          json: jest.fn().mockResolvedValue(mockNftRegisterRequest),
        },
        json: jest.fn(),
      } as unknown as Context;

      mockNftRegisterService.register.mockResolvedValueOnce(mockNftRegisterResponse);

      await nftsController.register(mockContext);

      expect(mockContext.json).toHaveBeenCalledWith(mockNftRegisterResponse, 200);
    });
  });

  describe('getI18nNftMetadata() method', () => {
    test('should return i18n nft metadata and status 200', async () => {
      const mockContext = {
        req: {
          param: jest.fn().mockReturnValueOnce('******************************************').mockReturnValueOnce('42'),
          header: jest.fn().mockReturnValue('sample-account-id'),
        },
        get: jest.fn().mockReturnValue('en-US'),
        json: jest.fn(),
      } as unknown as Context;

      const mockI18nNftMetadata: GetI18nNFTMetadataResponse = {
        name: 'test',
        description: 'test',
      };

      mockI18nService.getI18nNftMetadata.mockResolvedValueOnce(mockI18nNftMetadata);
      await nftsController.getI18nMetadata(mockContext);

      expect(mockI18nService.getI18nNftMetadata).toHaveBeenCalledWith(
        '******************************************',
        '42',
        'sample-account-id',
        'en-US',
      );
      expect(mockContext.json).toHaveBeenCalledWith(mockI18nNftMetadata, 200);
    });
  });
});
