import { AccountController } from '../../src/controllers/accountController';
import { AccountService } from '../../src/services/accountService';
import { AccountCustomFieldService } from '../../src/services/accountCustomFieldService';
import { Context, HonoRequest } from 'hono';
import { AccountResponse } from '../../src/responsedto/accountResponse';
import { AccountRepository } from '../../src/repositories/accountRepository';
import { ActionRepository } from '../../src/repositories/actionRepository';
import { QuestRepository } from '../../src/repositories/questRepository';
import { QuestActivityRepository } from '../../src/repositories/questActivityRepository';
import { ClaimedRewardRepository } from '../../src/repositories/claimedRewardRepository';
import { UserRepository } from '../../src/repositories/userRepository';
import { UserService } from '../../src/services/userService';
import { ServiceInfoRepository } from '../../src/repositories/serviceInfoRepository';
import { VaultKeyRepository } from '../../src/repositories/vaultKeyRepository';
import { RewardRepository } from '../../src/repositories/rewardRepository';
import { MetadataService } from '../../src/services/metadataService';
import { NftMetadatasRepository } from '../../src/repositories/nftMetadatasRepository';
import { NftBaseMetadatasRepository } from '../../src/repositories/nftBaseMetadatasRepository';
import { NftContractTypesRepository } from '../../src/repositories/nftContractTypesRepository';
import { FirebaseComponent } from '../../src/components/firebaseComponent';
import { LineComponent } from '../../src/components/lineComponent';
import { NftsService } from '../../src/services/nftsService';
import { NftContractsRepository } from '../../src/repositories/nftContractsRepository';
import { TransactionQueuesRepository } from '../../src/repositories/transactionQueuesRepository';
import { RewardActionType } from '../../src/enum/actionType';
import { languageCode } from '../../src/enum/languageCode';
import { NotificationRepository } from '../../src/repositories/notificationRepository';
import { NotificationService } from '../../src/services/notificationService';
import { Account } from '../../src/dtos/accounts/schemas';


jest.mock('../../src/services/accountService', () => ({
  AccountService: jest.fn().mockImplementation(() => {
    return {
      createAccount: jest.fn(),
      updateUserLineProfile: jest.fn(),
      getAccount: jest.fn(),
      getAccountStatus: jest.fn(),
      deleteAccount: jest.fn(),
      getAccountActivityHistory: jest.fn(),
      getAccountNotifications: jest.fn(),
    };
  }),
}));

describe('AccountController', () => {
  let accountService: jest.Mocked<AccountService>;
  let accountCustomFieldService: jest.Mocked<AccountCustomFieldService>;
  let accountController: AccountController;
  let context: Context;
  let mockAccountRepository: jest.Mocked<AccountRepository>;
  let mockActionRepository: jest.Mocked<ActionRepository>;
  let mockQuestRepository: jest.Mocked<QuestRepository>;
  let mockQuestActivityRepository: jest.Mocked<QuestActivityRepository>;
  let mockClaimedRewardRepository: jest.Mocked<ClaimedRewardRepository>;
  let mockUserRepository: jest.Mocked<UserRepository>;
  let mockUserService: jest.Mocked<UserService>;
  let mockServiceInfoRepository: jest.Mocked<ServiceInfoRepository>;
  let mockVaultKeyRepository: jest.Mocked<VaultKeyRepository>;
  let mockRewardRepository: jest.Mocked<RewardRepository>;
  let mockFirebaseComponent: jest.Mocked<FirebaseComponent>;
  let mockLineComponent: jest.Mocked<LineComponent>;
  let mockNftsService: jest.Mocked<NftsService>;
  let mockNftContractsRepository: jest.Mocked<NftContractsRepository>;
  let mockMetadataService: jest.Mocked<MetadataService>;
  let mockNotificationRepository: jest.Mocked<NotificationRepository>;
  let mockNotificationService: jest.Mocked<NotificationService>;
  let mockTransactionQueuesRepository: jest.Mocked<TransactionQueuesRepository>;

  beforeEach(() => {
    process.env.JSON_RPC_URL = 'http://127.0.0.1:8545';
    process.env.ALCHEMY_API_KEY = 'xxxxxsss';
    process.env.ALCHEMY_CHAIN_NAME = 'polygon-amoy';
    process.env.GAS_LIMIT_MULTIPLIER = '200';
    process.env.BASE_MAX_FEE_PER_GAS = '600';
    process.env.MAX_FEE_PER_GAS_MULTIPLIER = '150';
    process.env.MAX_PRIORITY_FEE_PER_GAS_MULTIPLIER = '150';
    process.env.GCP_PROJECT_ID = 'sample_gcp_project_id';
    process.env.MORALIS_MEMBERSHIP_STREAM_ID = 'mockMembershipStreamId';
    process.env.MORALIS_REWARD_STREAM_ID = 'mockRewardStreamId';

    mockAccountRepository = new AccountRepository() as jest.Mocked<AccountRepository>;
    mockActionRepository = new ActionRepository() as jest.Mocked<ActionRepository>;
    mockQuestRepository = new QuestRepository() as jest.Mocked<QuestRepository>;
    mockQuestActivityRepository = new QuestActivityRepository() as jest.Mocked<QuestActivityRepository>;
    mockClaimedRewardRepository = new ClaimedRewardRepository() as jest.Mocked<ClaimedRewardRepository>;
    mockNftContractsRepository = new NftContractsRepository() as jest.Mocked<NftContractsRepository>;
    mockServiceInfoRepository = new ServiceInfoRepository() as jest.Mocked<ServiceInfoRepository>;
    mockUserRepository = new UserRepository() as jest.Mocked<UserRepository>;
    mockVaultKeyRepository = new VaultKeyRepository() as jest.Mocked<VaultKeyRepository>;
    mockRewardRepository = new RewardRepository() as jest.Mocked<RewardRepository>;
    mockLineComponent = new LineComponent() as jest.Mocked<LineComponent>;
    mockFirebaseComponent = new FirebaseComponent() as jest.Mocked<FirebaseComponent>;
    mockUserService = new UserService(mockUserRepository, mockAccountRepository) as jest.Mocked<UserService>;
    mockTransactionQueuesRepository = new TransactionQueuesRepository() as jest.Mocked<TransactionQueuesRepository>;
    mockAccountRepository = new AccountRepository() as jest.Mocked<AccountRepository>;
    mockNftsService = new NftsService(
      mockNftContractsRepository,
      mockAccountRepository,
      mockRewardRepository,
    ) as jest.Mocked<NftsService>;
    mockMetadataService = new MetadataService(
      {} as NftMetadatasRepository,
      {} as NftBaseMetadatasRepository,
      {} as NftContractTypesRepository,
    ) as jest.Mocked<MetadataService>;
    mockNotificationRepository = new NotificationRepository() as jest.Mocked<NotificationRepository>;
    mockNotificationService = new NotificationService(mockAccountRepository, mockNotificationRepository) as jest.Mocked<NotificationService>;

    accountService = new AccountService(
      mockAccountRepository,
      mockActionRepository,
      mockQuestRepository,
      mockQuestActivityRepository,
      mockClaimedRewardRepository,
      mockUserService,
      mockServiceInfoRepository,
      mockLineComponent,
      mockFirebaseComponent,
      mockNftsService,
      mockNftContractsRepository,
      mockVaultKeyRepository,
      mockTransactionQueuesRepository,
      mockMetadataService,
      mockNotificationService,
    ) as jest.Mocked<AccountService>;
    accountController = new AccountController(accountService);

    context = {
      req: {
        header: jest.fn(),
        param: jest.fn(),
      },
      json: jest.fn(),
      get: jest.fn().mockReturnValue(''),
    } as unknown as Context;
  });

  describe('createAccount', () => {
    test('should create an account and return a response with status 200', async () => {
      const mockAccountResponse = {
        accountId: '9293485f-a63a-49c1-917b-4834c9498dc8',
        displayName: 'DisplayName',
        profileImage: 'https://mockurl.com/mockProfileImage.png',
        membership: {
          contractAddress: '0xBC4CA0EdA7647A8aB7C2061c2E118A18a936f13D',
          tokenId: 100,
        },
        tokenBoundAccountAddress: undefined,
        transactionHash: undefined,
      };

      accountService.createAccount.mockResolvedValue(mockAccountResponse);
      (context.req.header as jest.Mock).mockImplementation((headerName: string) => {
        switch (headerName) {
          case 'X-Forwarded-Authorization':
            return 'Bearer mockFirebaseIdToken';
          case 'line-id-token-header':
            return 'mockLineIdToken';
          case 'service-id-header':
            return 'mockServiceId';
          default:
            return '';
        }
      });
      await accountController.createAccount(context);

      expect(accountService.createAccount).toHaveBeenCalledWith(
        'mockFirebaseIdToken',
        'mockLineIdToken',
        'mockServiceId',
      );
      expect(context.json).toHaveBeenCalledWith(mockAccountResponse, 200);
    });

    test('should throw an error if account creation fails', async () => {
      const mockError = new Error('Failed to create account');
      accountService.createAccount.mockRejectedValue(mockError);

      (context.req.header as jest.Mock).mockImplementation((headerName: string) => {
        switch (headerName) {
          case 'authorization':
            return 'Bearer mockFirebaseIdToken';
          case 'line-id-token-header':
            return 'mockLineIdToken';
          case 'service-id-header':
            return 'mockServiceId';
          default:
            return '';
        }
      });

      await expect(accountController.createAccount(context)).rejects.toThrow('Failed to create account');
    });
  });

  describe('delete() method', () => {
    test('should return 200 when account is successfully deleted', async () => {
      const mockRequest = {
        param: jest.fn().mockReturnValue('1'),
        header: jest.fn().mockReturnValue('sampleServiceId'),
      } as Partial<HonoRequest>;

      const mockContext = {
        req: mockRequest,
        text: jest.fn(),
      } as unknown as Context;

      accountService.deleteAccount.mockResolvedValueOnce(undefined);

      await accountController.delete(mockContext);

      expect(mockContext.text).toHaveBeenCalledWith('', 200);
    });

    test('should throw an error when account deletion fails', async () => {
      const mockRequest = {
        param: jest.fn().mockReturnValue('account123'),
        header: jest.fn().mockReturnValue('sampleServiceId'),
      } as Partial<HonoRequest>;

      const mockContext = {
        req: mockRequest,
        text: jest.fn(),
      } as unknown as Context;

      accountService.deleteAccount.mockRejectedValueOnce(new Error('Deletion failed'));

      await expect(accountController.delete(mockContext)).rejects.toThrow('Deletion failed');
    });
  });

  describe('getAccount', () => {
    test('should return account details for a valid accountId and serviceId', async () => {
      const mockAccountResponse = {
        accountId: 'mockAccountId',
        displayName: 'mockDisplayName',
        profileImage: 'mockProfileImage',
        membership: {
          contractAddress: '0xMockContractAddress',
          tokenId: 100,
        },
        tokenBoundAccountAddress: '0xMockTokenBoundAccountAddress',
      };

      accountService.getAccount.mockResolvedValue(mockAccountResponse);

      (context.req.param as jest.Mock).mockReturnValue('mockAccountId');
      (context.req.header as jest.Mock).mockImplementation((headerName: string) => {
        if (headerName === 'service-id-header') {
          return 'mockServiceId';
        }
        return '';
      });

      await accountController.getAccount(context);

      expect(accountService.getAccount).toHaveBeenCalledWith('mockAccountId', 'mockServiceId');
      expect(context.json).toHaveBeenCalledWith(mockAccountResponse, 200);
    });

    test('should throw an error if account retrieval fails', async () => {
      const mockError = new Error('Account not found');
      accountService.getAccount.mockRejectedValue(mockError);

      (context.req.param as jest.Mock).mockReturnValue('mockAccountId');
      (context.req.header as jest.Mock).mockImplementation((headerName: string) => {
        if (headerName === 'service-id-header') {
          return 'mockServiceId';
        }
        return '';
      });

      await expect(accountController.getAccount(context)).rejects.toThrow('Account not found');
    });
  });

  describe('getAccountStatus', () => {
    test('should return account status successfully', async () => {
      const mockAccountStatusResponse = {
        completedQuests: 5,
        obtainedRewards: 3,
        unusedCoupon: 1,
        currentPeriodCompletedQuests: 2,
        badges: [
          {
            rewardId: '8552df83-cbd5-44db-b67e-0cbeb2785918',
            title: '10% Off Discount Coupon',
            description: 'IyBBcHBsaWVzIHRvIGFsbCBwdXJjaGFzZXMgb3ZlciAkNTAu',
            imageUrl: 'https://example.com/images/coupon.png',
            rank: 3,
          },
        ],
      };

      accountService.getAccountStatus.mockResolvedValue(mockAccountStatusResponse);
      (context.req.header as jest.Mock).mockReturnValue('mockServiceId');
      (context.req.param as jest.Mock).mockReturnValue('mockAccountId');

      await accountController.getAccountStatus(context);

      expect(accountService.getAccountStatus).toHaveBeenCalledWith('mockAccountId', 'mockServiceId');
      expect(context.json).toHaveBeenCalledWith(mockAccountStatusResponse, 200);
    });

    test('should throw an error if account status retrieval fails', async () => {
      const mockError = new Error('Failed to get account status');
      accountService.getAccountStatus.mockRejectedValue(mockError);

      (context.req.header as jest.Mock).mockReturnValue('mockServiceId');
      (context.req.param as jest.Mock).mockReturnValue('mockAccountId');

      await expect(accountController.getAccountStatus(context)).rejects.toThrow('Failed to get account status');
    });
  });

  describe('updateUserLineProfile', () => {
    test('should call updateUserLineProfile on AccountService and return a 200 response', async () => {
      // Mocking the context and headers
      const mockAccountResponse: Account = {
        accountId: 'account1',
        displayName: 'Updated User',
        profileImage: 'https://example.com',
        membership: {
          contractAddress: '0xBC4CA0EdA7647A8aB7C2061c2E118A18a936f13D',
          tokenId: 100,
        },
        tokenBoundAccountAddress: '0xBC4CA0EdA7647A8aB7C2061c2E118A18a936f13D',
      };
      accountService.updateUserLineProfile.mockResolvedValueOnce(mockAccountResponse);

      const context = {
        req: {
          param: jest.fn().mockReturnValue('account1'),
          header: jest.fn().mockImplementation((headerName) => {
            switch (headerName) {
              case 'line-id-token-header':
                return 'some-line-id-token';
              case 'service-id-header':
                return 'service1';
              default:
                return undefined;
            }
          }),
        },
        json: jest.fn(),
      } as unknown as Context;

      // Call the controller method
      await accountController.updateUserLineProfile(context);

      // Assertions
      expect(accountService.updateUserLineProfile).toHaveBeenCalledWith('account1', 'some-line-id-token', 'service1');
      expect(context.json).toHaveBeenCalledWith(mockAccountResponse, 200);
    });

    test('should throw an error if account not found', async () => {
      const mockError = new Error('Account not found');
      accountService.updateUserLineProfile.mockRejectedValue(mockError);

      (context.req.param as jest.Mock).mockReturnValue('mockAccountId');
      (context.req.header as jest.Mock).mockImplementation((headerName: string) => {
        if (headerName === 'service-id-header') {
          return 'mockServiceId';
        }
        return '';
      });

      await expect(accountController.updateUserLineProfile(context)).rejects.toThrow('Account not found');
    });

    test('should throw Invalid token error for an invalid LINE ID token', async () => {
      const mockError = new Error('Invalid token');
      accountService.updateUserLineProfile.mockRejectedValue(mockError);

      (context.req.param as jest.Mock).mockReturnValue('mockAccountId');
      (context.req.header as jest.Mock).mockImplementation((headerName: string) => {
        if (headerName === 'service-id-header') {
          return 'mockServiceId';
        }
        if (headerName === 'line-id-token-header') {
          return 'mockLineToken';
        }
        return '';
      });

      await expect(accountController.updateUserLineProfile(context)).rejects.toThrow('Invalid token');
    });
  });

  describe('getAccountActivityHistory', () => {
    test('should return account activity history for valid accountId and serviceId', async () => {
      (context.get as jest.Mock).mockReturnValue(languageCode.EN_US);
      const mockAccountActivityResponse = {
        completedActions: [
          {
            completedTime: '2023-12-31T17:59:59.000Z',
            thumbnailImageUrl: 'https://action1.thumbnail.url',
            title: 'action 1',
          },
        ],
        completedQuests: [
          {
            completedTime: '2024-10-17T03:41:55.000Z',
            thumbnailImageUrl: 'https://quest2thumbnail.url',
            title: 'quest 1',
          },
        ],
        rewards: [
          {
            completedTime: '2024-10-17T03:47:16.000Z',
            thumbnailImageUrl: 'https://reward1thumbnail.url',
            title: 'reward 1',
            actionType: RewardActionType.USED,
          },
        ],
      };

      accountService.getAccountActivityHistory.mockResolvedValue(mockAccountActivityResponse);

      (context.req.param as jest.Mock).mockReturnValue('mockAccountId');
      (context.req.header as jest.Mock).mockImplementation((headerName: string) => {
        if (headerName === 'service-id-header') {
          return 'mockServiceId';
        }
        return '';
      });

      await accountController.getAccountActivityHistory(context);

      expect(accountService.getAccountActivityHistory).toHaveBeenCalledWith('mockAccountId', 'mockServiceId', languageCode.EN_US);
      expect(context.json).toHaveBeenCalledWith(mockAccountActivityResponse, 200);
    });

    test('should throw an error if account activity retrieval fails', async () => {
      const mockError = new Error('Account activity not found');
      accountService.getAccountActivityHistory.mockRejectedValue(mockError);

      (context.req.param as jest.Mock).mockReturnValue('mockAccountId');
      (context.req.header as jest.Mock).mockImplementation((headerName: string) => {
        if (headerName === 'service-id-header') {
          return 'mockServiceId';
        }
        return '';
      });

      await expect(accountController.getAccountActivityHistory(context)).rejects.toThrow(
        'Account activity not found',
      );
    });
  });

  describe('getAccountNotifications', () => {
    test('should return account notifications for valid accountId and serviceId', async () => {
      const mockAccountNotificationsResponse = {
        notifications: [
          {
            notificationId: '1',
            title: 'global notification 1',
            text: 'global notification content 1',
            broadcastDate: '2024-10-21T08:58:25.000Z',
          },
          {
            notificationId: '1',
            title: 'account notification 1',
            text: 'account notification content 1',
            broadcastDate: '2024-10-21T08:58:25.000Z',
          },
        ],
      };

      accountService.getAccountNotifications.mockResolvedValue(mockAccountNotificationsResponse);

      (context.req.param as jest.Mock).mockReturnValue('mockAccountId');
      (context.req.header as jest.Mock).mockImplementation((headerName: string) => {
        if (headerName === 'service-id-header') {
          return 'mockServiceId';
        }
        return '';
      });

      await accountController.getAccountNotifications(context);

      expect(accountService.getAccountNotifications).toHaveBeenCalledWith('mockAccountId', 'mockServiceId', '');
      expect(context.json).toHaveBeenCalledWith(mockAccountNotificationsResponse, 200);
    });

    test('should throw an error if account notifications retrieval fails', async () => {
      const mockError = new Error('Account notifications not found');
      accountService.getAccountNotifications.mockRejectedValue(mockError);

      (context.req.param as jest.Mock).mockReturnValue('mockAccountId');
      (context.req.header as jest.Mock).mockImplementation((headerName: string) => {
        if (headerName === 'service-id-header') {
          return 'mockServiceId';
        }
        return '';
      });

      await expect(accountController.getAccountNotifications(context)).rejects.toThrow(
        'Account notifications not found',
      );
    });
  });
});
