// locationController.test.ts
import { Context } from 'hono';
import { LocationController } from '../../src/controllers/locationController';
import { GeofenceService } from '../../src/services/geofenceService';
import { GeofenceRepository } from '../../src/repositories/geofenceRepository';
import {
  LocationGeofence,
  LocationGeofenceId,
  GetLocationGeofences,
  GeofenceType,
} from '../../src/dtos/locations/schemas';

jest.mock('../../src/services/geofenceService', () => ({
  GeofenceService: jest.fn().mockImplementation(() => {
    return {
      createGeofence: jest.fn(),
      updateGeofence: jest.fn(),
      getGeofences: jest.fn(),
      getGeofenceById: jest.fn(),
    };
  }),
}));

describe('LocationController', () => {
  let geofenceService: jest.Mocked<GeofenceService>;
  let locationController: LocationController;
  let context: Context;
  let mockGeofenceRepository: jest.Mocked<GeofenceRepository>;

  beforeEach(() => {
    mockGeofenceRepository = new GeofenceRepository() as jest.Mocked<GeofenceRepository>;
    geofenceService = new GeofenceService(mockGeofenceRepository) as jest.Mocked<GeofenceService>;
    locationController = new LocationController(geofenceService);

    context = {
      req: {
        header: jest.fn(),
        param: jest.fn(),
        json: jest.fn(),
      },
      json: jest.fn(),
      status: jest.fn().mockReturnThis(),
    } as any;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createGeofence', () => {
    test('should create a new geofence successfully', async () => {
      const mockServiceId = 'service-123';
      const mockRequest: LocationGeofence = {
        name: 'Tokyo Station Area',
        geofence: {
          geofenceType: GeofenceType.CIRCLE,
          center: {
            latitude: 35.6812,
            longitude: 139.7671,
          },
          radiusMeters: 50,
        },
      };

      const mockResponse: LocationGeofenceId = {
        geofenceId: 'geofence-001',
        name: 'Tokyo Station Area',
        geofence: {
          geofenceType: GeofenceType.CIRCLE,
          center: {
            latitude: 35.6812,
            longitude: 139.7671,
          },
          radiusMeters: 50,
        },
      };

      context.req.header = jest.fn().mockReturnValue(mockServiceId);
      context.req.json = jest.fn().mockResolvedValue(mockRequest);
      geofenceService.createGeofence.mockResolvedValue(mockResponse);

      await locationController.createGeofence(context);

      expect(context.req.header).toHaveBeenCalledWith('service-id-header');
      expect(context.req.json).toHaveBeenCalled();
      expect(geofenceService.createGeofence).toHaveBeenCalledWith(mockServiceId, mockRequest);
      expect(context.json).toHaveBeenCalledWith(mockResponse, 201);
    });

    test('should handle missing service ID header', async () => {
      context.req.header = jest.fn().mockReturnValue(undefined);
      context.req.json = jest.fn().mockResolvedValue({});

      // The controller doesn't validate headers, so it will pass undefined to the service
      // This test should pass without throwing
      await locationController.createGeofence(context);

      expect(context.req.header).toHaveBeenCalledWith('service-id-header');
    });
  });

  describe('updateGeofence', () => {
    test('should update an existing geofence successfully', async () => {
      const mockServiceId = 'service-123';
      const mockGeofenceId = 'geofence-001';
      const mockRequest: LocationGeofence = {
        name: 'Updated Tokyo Station Area',
        geofence: {
          geofenceType: GeofenceType.CIRCLE,
          center: {
            latitude: 35.6812,
            longitude: 139.7671,
          },
          radiusMeters: 100,
        },
      };

      const mockResponse: LocationGeofenceId = {
        geofenceId: 'geofence-001',
        name: 'Updated Tokyo Station Area',
        geofence: {
          geofenceType: GeofenceType.CIRCLE,
          center: {
            latitude: 35.6812,
            longitude: 139.7671,
          },
          radiusMeters: 100,
        },
      };

      context.req.header = jest.fn().mockReturnValue(mockServiceId);
      context.req.param = jest.fn().mockReturnValue({ geofenceId: mockGeofenceId });
      context.req.json = jest.fn().mockResolvedValue(mockRequest);
      geofenceService.updateGeofence.mockResolvedValue(mockResponse);

      await locationController.updateGeofence(context);

      expect(context.req.header).toHaveBeenCalledWith('service-id-header');
      expect(context.req.param).toHaveBeenCalledWith();
      expect(context.req.json).toHaveBeenCalled();
      expect(geofenceService.updateGeofence).toHaveBeenCalledWith(mockServiceId, mockGeofenceId, mockRequest);
      expect(context.json).toHaveBeenCalledWith(mockResponse, 200);
    });

    test('should handle missing geofence ID parameter', async () => {
      const mockServiceId = 'service-123';

      context.req.header = jest.fn().mockReturnValue(mockServiceId);
      context.req.param = jest.fn().mockReturnValue({});
      context.req.json = jest.fn().mockResolvedValue({});

      // The controller doesn't validate parameters, so it will pass undefined to the service
      await locationController.updateGeofence(context);

      expect(context.req.param).toHaveBeenCalledWith();
    });
  });

  describe('getGeofences', () => {
    test('should return all geofences for a service', async () => {
      const mockServiceId = 'service-123';
      const mockResponse: LocationGeofenceId[] = [
        {
          geofenceId: 'geofence-001',
          name: 'Location 1',
          geofence: {
            geofenceType: GeofenceType.CIRCLE,
            center: { latitude: 35.6812, longitude: 139.7671 },
            radiusMeters: 50,
          },
        },
        {
          geofenceId: 'geofence-002',
          name: 'Location 2',
          geofence: {
            geofenceType: GeofenceType.POLYGON,
            coordinates: [
              { latitude: 35.6, longitude: 139.7 },
              { latitude: 35.6, longitude: 139.8 },
              { latitude: 35.7, longitude: 139.8 },
              { latitude: 35.7, longitude: 139.7 },
              { latitude: 35.6, longitude: 139.7 },
            ],
          },
        },
      ];

      context.req.header = jest.fn().mockReturnValue(mockServiceId);
      geofenceService.getGeofences.mockResolvedValue(mockResponse);

      await locationController.getGeofences(context);

      expect(context.req.header).toHaveBeenCalledWith('service-id-header');
      expect(geofenceService.getGeofences).toHaveBeenCalledWith(mockServiceId);
      expect(context.json).toHaveBeenCalledWith(mockResponse, 200);
    });

    test('should return empty array when no geofences exist', async () => {
      const mockServiceId = 'service-123';
      const mockResponse: LocationGeofenceId[] = [];

      context.req.header = jest.fn().mockReturnValue(mockServiceId);
      geofenceService.getGeofences.mockResolvedValue(mockResponse);

      await locationController.getGeofences(context);

      expect(context.req.header).toHaveBeenCalledWith('service-id-header');
      expect(geofenceService.getGeofences).toHaveBeenCalledWith(mockServiceId);
      expect(context.json).toHaveBeenCalledWith(mockResponse, 200);
    });
  });

  describe('getGeofence', () => {
    test('should return a specific geofence by ID', async () => {
      const mockServiceId = 'service-123';
      const mockGeofenceId = 'geofence-001';
      const mockResponse: LocationGeofenceId = {
        geofenceId: 'geofence-001',
        name: 'Tokyo Station',
        geofence: {
          geofenceType: GeofenceType.CIRCLE,
          center: {
            latitude: 35.6812,
            longitude: 139.7671,
          },
          radiusMeters: 50,
        },
      };

      context.req.header = jest.fn().mockReturnValue(mockServiceId);
      context.req.param = jest.fn().mockReturnValue({ geofenceId: mockGeofenceId });
      geofenceService.getGeofenceById.mockResolvedValue(mockResponse);

      await locationController.getGeofence(context);

      expect(context.req.header).toHaveBeenCalledWith('service-id-header');
      expect(context.req.param).toHaveBeenCalledWith();
      expect(geofenceService.getGeofenceById).toHaveBeenCalledWith(mockServiceId, mockGeofenceId);
      expect(context.json).toHaveBeenCalledWith(mockResponse, 200);
    });

    test('should handle geofence not found', async () => {
      const mockServiceId = 'service-123';
      const mockGeofenceId = 'non-existent-geofence';

      context.req.header = jest.fn().mockReturnValue(mockServiceId);
      context.req.param = jest.fn().mockReturnValue({ geofenceId: mockGeofenceId });
      geofenceService.getGeofenceById.mockRejectedValue(new Error('Geofence not found'));

      await expect(locationController.getGeofence(context)).rejects.toThrow('Geofence not found');

      expect(context.req.header).toHaveBeenCalledWith('service-id-header');
      expect(context.req.param).toHaveBeenCalledWith();
      expect(geofenceService.getGeofenceById).toHaveBeenCalledWith(mockServiceId, mockGeofenceId);
    });
  });
});
