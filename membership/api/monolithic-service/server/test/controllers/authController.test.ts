import { AuthController } from '../../src/controllers/authController';
import { AuthService } from '../../src/services/authService';
import { Context } from 'hono';
import { AuthProviderRepository } from '../../src/repositories/authProviderRepository';
import { UserService } from '../../src/services/userService';
import { LineComponent } from '../../src/components/lineComponent';
import { FirebaseComponent } from '../../src/components/firebaseComponent';
import { ServiceInfoRepository } from '../../src/repositories/serviceInfoRepository';
import { UserRepository } from '../../src/repositories/userRepository';
import { AccountRepository } from '../../src/repositories/accountRepository';
import { createMockShareBackupRepository } from '../../src/utils/mockFactories';

jest.mock('../../src/services/authService', () => ({
  AuthService: jest.fn().mockImplementation(() => {
    return {
      linkLineIdToken: jest.fn(),
      getCustomToken: jest.fn(),
    };
  }),
}));

describe('AuthController', () => {
  let authService: jest.Mocked<AuthService>;
  let authController: AuthController;
  let context: Context;

  let mockAuthProviderRepository: jest.Mocked<AuthProviderRepository>;
  let mockServiceInfoRepository: jest.Mocked<ServiceInfoRepository>;
  let mockUserRepository: jest.Mocked<UserRepository>;
  let mockAccountRepository: jest.Mocked<AccountRepository>;

  let mockUserService: jest.Mocked<UserService>;
  let mockLineComponent: jest.Mocked<LineComponent>;
  let mockFirebaseComponent: jest.Mocked<FirebaseComponent>;

  beforeEach(() => {
    process.env.JSON_RPC_URL = 'http://127.0.0.1:8545';
    process.env.ALCHEMY_API_KEY = 'xxxxxsss';
    process.env.ALCHEMY_CHAIN_NAME = 'polygon-amoy';
    process.env.GAS_LIMIT_MULTIPLIER = '200';
    process.env.BASE_MAX_FEE_PER_GAS = '600';
    process.env.MAX_FEE_PER_GAS_MULTIPLIER = '150';
    process.env.MAX_PRIORITY_FEE_PER_GAS_MULTIPLIER = '150';
    process.env.GCP_PROJECT_ID = 'sample_gcp_project_id';
    process.env.MORALIS_MEMBERSHIP_STREAM_ID = 'mockMembershipStreamId';
    process.env.MORALIS_REWARD_STREAM_ID = 'mockRewardStreamId';

    mockAuthProviderRepository = new AuthProviderRepository() as jest.Mocked<AuthProviderRepository>;
    mockServiceInfoRepository = new ServiceInfoRepository() as jest.Mocked<ServiceInfoRepository>;
    mockUserRepository = new UserRepository() as jest.Mocked<UserRepository>;
    mockAccountRepository = new AccountRepository() as jest.Mocked<AccountRepository>;

    mockLineComponent = new LineComponent() as jest.Mocked<LineComponent>;
    mockFirebaseComponent = {} as jest.Mocked<FirebaseComponent>;

    mockUserService = new UserService(
      mockUserRepository,
      mockAccountRepository,
      createMockShareBackupRepository(),
    ) as jest.Mocked<UserService>;

    authService = new AuthService(
      mockAuthProviderRepository,
      mockUserService,
      mockLineComponent,
      mockFirebaseComponent,
      mockServiceInfoRepository,
      mockAccountRepository,
    ) as jest.Mocked<AuthService>;
    authController = new AuthController(authService);
    context = {
      req: {
        header: jest.fn(),
      },
      json: jest.fn(),
    } as unknown as Context;
  });

  test('linkLineIdToken - success case', async () => {
    const mockToken = { token: 'mockCustomToken' };
    authService.linkLineIdToken.mockResolvedValue(mockToken);
    (context.req.header as jest.Mock).mockImplementation((headerName: string) => {
      switch (headerName) {
        case 'Authorization':
          return 'Bearer mockFirebaseIdToken';
        case 'Line-Id-Token-Header':
          return 'mockLineIdToken';
        case 'Service-Id-Header':
          return 'mockServiceId';
        default:
          return null;
      }
    });

    await authController.linkLineIdToken(context);

    expect(authService.linkLineIdToken).toHaveBeenCalledWith('mockFirebaseIdToken', 'mockLineIdToken', 'mockServiceId');
    expect(context.json).toHaveBeenCalledWith(mockToken, 200);
  });

  test('linkLineIdToken - failure case', async () => {
    const mockError = new Error('Failed to get custom token');
    authService.linkLineIdToken.mockRejectedValue(mockError);

    (context.req.header as jest.Mock).mockImplementation((headerName: string) => {
      switch (headerName) {
        case 'Authorization':
          return 'Bearer mockFirebaseIdToken';
        case 'Line-Id-Token-Header':
          return 'mockLineIdToken';
        case 'Service-Id-Header':
          return 'mockServiceId';
        default:
          return null;
      }
    });

    await expect(authController.linkLineIdToken(context)).rejects.toThrow('Failed to get custom token');
  });

  test('getCustomToken - success case', async () => {
    const mockToken = { token: 'mockCustomToken' };
    authService.getCustomToken.mockResolvedValue(mockToken);
    (context.req.header as jest.Mock).mockImplementation((headerName: string) => {
      switch (headerName) {
        case 'Line-Id-Token-Header':
          return 'mockLineIdToken';
        case 'Service-Id-Header':
          return 'mockServiceId';
        default:
          return null;
      }
    });

    await authController.getCustomToken(context);

    expect(authService.getCustomToken).toHaveBeenCalledWith('mockLineIdToken', 'mockServiceId');
    expect(context.json).toHaveBeenCalledWith(mockToken, 200);
  });

  test('getCustomToken - failure case', async () => {
    const mockError = new Error('Failed to get custom token');
    authService.getCustomToken.mockRejectedValue(mockError);

    (context.req.header as jest.Mock).mockImplementation((headerName: string) => {
      switch (headerName) {
        case 'Line-Id-Token-Header':
          return 'mockLineIdToken';
        case 'Service-Id-Header':
          return 'mockServiceId';
        default:
          return null;
      }
    });

    await expect(authController.getCustomToken(context)).rejects.toThrow('Failed to get custom token');
  });
});
