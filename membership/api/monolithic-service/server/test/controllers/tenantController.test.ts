import { Context } from 'hono';
import { TenantsController } from '../../src/controllers/tenantController';
import { TenantsService } from '../../src/services/tenantService';
import { TenantRepository } from '../../src/repositories/tenantRepository';
import { PlanRepository } from '../../src/repositories/planRepository';
import { VaultKeyService } from '../../src/services/vaultKeyService';
import { Tenant } from '../../src/dtos/tenants_td/schemas';
import { TenantInput } from '../../src/dtos/tenants_td/schemas';
import { languageCode } from '../../src/enum/languageCode';

jest.mock('../../src/services/tenantService', () => ({
  TenantsService: jest.fn().mockImplementation(() => ({
    registerTenant: jest.fn(),
  })),
}));


describe('TenantsController', () => {
  let tenantsController: TenantsController;
  let mockTenantsService: jest.Mocked<TenantsService>;
  let mockVaultKeyService: jest.Mocked<VaultKeyService>;

  let mockTenantRepository: jest.Mocked<TenantRepository>;
  let mockPlanRepository: jest.Mocked<PlanRepository>;

  beforeEach(() => {
    mockTenantRepository = {} as jest.Mocked<TenantRepository>;
    mockPlanRepository = {} as jest.Mocked<PlanRepository>;
    mockVaultKeyService = {} as jest.Mocked<VaultKeyService>;
    mockTenantsService = new TenantsService(
      mockTenantRepository,
      mockPlanRepository,
      mockVaultKeyService,
    ) as jest.Mocked<TenantsService>;

    tenantsController = new TenantsController(mockTenantsService);
  });

  describe('registerTenant', () => {
    const mockRequestBody: TenantInput = {
      tenantTranslations: [
        {
          tenantName: 'test-tenant',
          language: languageCode.JA,
        }
      ],
      planId: 'mock-plan-id',
    };

    const mockContext = {
      req: {
        json: jest.fn().mockResolvedValue(mockRequestBody),
      },
      json: jest.fn(),
    } as unknown as Context;

    test('should call tenantsService.registerTenant and return a successful response', async () => {
      const mockTenantResponse: Tenant = {
        tenantId: 'mock-tenant-id',
        tenantTranslations: [
          {
            tenantName: 'test-tenant',
            language: languageCode.JA,
          },
        ],
        planId: 'mock-plan-id',
        keyRingProject: 'keyRingProject',
        keyRingLocation: 'keyRingLocation',
        vaultKeyName: 'vaultKeyName',
        vaultWalletAddress: '0x0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef',
      };

      mockTenantsService.registerTenant.mockResolvedValue(mockTenantResponse);

      await tenantsController.registerTenant(mockContext);

      expect(mockContext.req.json).toHaveBeenCalled();
      expect(mockTenantsService.registerTenant).toHaveBeenCalledWith(mockRequestBody);
      expect(mockContext.json).toHaveBeenCalledWith(mockTenantResponse, 200);
    });

    test('should handle errors when registering a tenant', async () => {
      const error = new Error('Failed to register tenant');

      mockTenantsService.registerTenant.mockRejectedValue(error);

      await expect(tenantsController.registerTenant(mockContext)).rejects.toThrow(error);

      expect(mockContext.req.json).toHaveBeenCalled();
      expect(mockTenantsService.registerTenant).toHaveBeenCalledWith(mockRequestBody);
    });
  });
});
