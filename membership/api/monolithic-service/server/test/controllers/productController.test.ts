import { Context } from 'hono';
import { ProductService } from '../../src/services/productService';
import { NftMintService } from '../../src/services/nftMintService';

import { ProductsController } from '../../src/controllers/productController';
import { NftContractsRepository } from '../../src/repositories/nftContractsRepository';
import { CheckoutRepository } from '../../src/repositories/checkoutRepository';
import { ServiceInfoRepository } from '../../src/repositories/serviceInfoRepository';
import { AccountRepository } from '../../src/repositories/accountRepository';
import { ProductRepository } from '../../src/repositories/productRepository';
import { CustomFieldsRepository } from '../../src/repositories/customFieldsRepository';
import { MetadataService } from '../../src/services/metadataService';
import { NftMetadatasRepository } from '../../src/repositories/nftMetadatasRepository';
import { NftBaseMetadatasRepository } from '../../src/repositories/nftBaseMetadatasRepository';
import { NftContractTypesRepository } from '../../src/repositories/nftContractTypesRepository';
import { VaultKeyRepository } from '../../src/repositories/vaultKeyRepository';
import { TransactionQueuesRepository } from '../../src/repositories/transactionQueuesRepository';
import { StripeCheckoutInfo } from '../../src/dtos/products/schemas';
import { languageCode } from '../../src/enum/languageCode';
import { DeliveryNftsFirestoreRepository } from '../../src/repositories/deliveryNftsFirestoreRepository';
import { FirebaseComponent } from '../../src/components/firebaseComponent';

jest.mock('../../src/services/productService', () => ({
  ProductService: jest.fn().mockImplementation(() => ({
    handleStripeEvents: jest.fn(),
  })),
}));

describe('ProductsController', () => {
  process.env.JSON_RPC_URL = 'http://127.0.0.1:8545';
  process.env.ALCHEMY_API_KEY = 'xxxxxsss';
  process.env.ALCHEMY_CHAIN_NAME = 'polygon-amoy';
  process.env.GAS_LIMIT_MULTIPLIER = '200';
  process.env.BASE_MAX_FEE_PER_GAS = '600';
  process.env.MAX_FEE_PER_GAS_MULTIPLIER = '150';
  process.env.MAX_PRIORITY_FEE_PER_GAS_MULTIPLIER = '150';
  process.env.GCP_PROJECT_ID = 'sample_gcp_project_id';

  let productsController: ProductsController;
  let productService: jest.Mocked<ProductService>;
  let mockCheckoutRepository: jest.Mocked<CheckoutRepository>;
  let mockNftContractsRepository: jest.Mocked<NftContractsRepository>;
  let mockNftMetadatasRepository: jest.Mocked<NftMetadatasRepository>;
  let mockNftBaseMetadatasRepository: jest.Mocked<NftBaseMetadatasRepository>;
  let mockNftContractTypesRepository: jest.Mocked<NftContractTypesRepository>;
  let mockVaultKeyRepository: jest.Mocked<VaultKeyRepository>;
  let mockDeliveryNftsFirestoreRepository: jest.Mocked<DeliveryNftsFirestoreRepository>;

  let mockNftMintService: jest.Mocked<NftMintService>;
  let mockServiceInfoRepository: jest.Mocked<ServiceInfoRepository>;
  let mockAccountRepository: jest.Mocked<AccountRepository>;
  let mockProductRepository: jest.Mocked<ProductRepository>;
  let mockCustomFieldsRepository: jest.Mocked<CustomFieldsRepository>;
  let mockMetadataService: jest.Mocked<MetadataService>;
  let mockTransactionQueuesRepository: jest.Mocked<TransactionQueuesRepository>;

  beforeEach(() => {
    mockCheckoutRepository = new CheckoutRepository() as jest.Mocked<CheckoutRepository>;
    mockNftContractsRepository = new NftContractsRepository() as jest.Mocked<NftContractsRepository>;
    mockServiceInfoRepository = new ServiceInfoRepository() as jest.Mocked<ServiceInfoRepository>;
    mockAccountRepository = new AccountRepository() as jest.Mocked<AccountRepository>;
    mockVaultKeyRepository = new VaultKeyRepository() as jest.Mocked<VaultKeyRepository>;
    mockTransactionQueuesRepository = new TransactionQueuesRepository() as jest.Mocked<TransactionQueuesRepository>;
    mockDeliveryNftsFirestoreRepository = new DeliveryNftsFirestoreRepository(
      {} as FirebaseComponent
    ) as jest.Mocked<DeliveryNftsFirestoreRepository>;
    mockNftMintService = new NftMintService(
      mockNftContractsRepository,
      mockTransactionQueuesRepository,
      mockVaultKeyRepository,
      mockServiceInfoRepository,
      mockDeliveryNftsFirestoreRepository,
    ) as jest.Mocked<NftMintService>;
    mockServiceInfoRepository = {} as jest.Mocked<ServiceInfoRepository>;
    mockProductRepository = {} as jest.Mocked<ProductRepository>;
    mockCustomFieldsRepository = new CustomFieldsRepository() as jest.Mocked<CustomFieldsRepository>;
    mockNftMetadatasRepository = new NftMetadatasRepository() as jest.Mocked<NftMetadatasRepository>;
    mockNftBaseMetadatasRepository = new NftBaseMetadatasRepository() as jest.Mocked<NftBaseMetadatasRepository>;
    mockNftContractTypesRepository = new NftContractTypesRepository() as jest.Mocked<NftContractTypesRepository>;
    mockMetadataService = new MetadataService(
      mockNftMetadatasRepository,
      mockNftBaseMetadatasRepository,
      mockNftContractTypesRepository,
    ) as jest.Mocked<MetadataService>;
    productService = new ProductService(
      mockNftMintService,
      mockCheckoutRepository,
      mockNftContractsRepository,
      mockServiceInfoRepository,
      mockAccountRepository,
      mockProductRepository,
      mockCustomFieldsRepository,
      mockMetadataService,
    ) as jest.Mocked<ProductService>;

    productsController = new ProductsController(productService);
  });
  describe('method handleStripeEvents', () => {
    test('should process Stripe events and return a successful response', async () => {
      const mockSignature = 'mock-signature';
      const mockBody = '{"type":"checkout.session.completed"}';
      const mockContext = {
        req: {
          header: jest.fn().mockReturnValue(mockSignature),
          text: jest.fn().mockResolvedValue(mockBody),
        },
        json: jest.fn(),
        get: jest.fn(),
      } as unknown as Context;

      await productsController.handleStripeEvents(mockContext);

      expect(productService.handleStripeEvents).toHaveBeenCalledWith(mockBody, mockSignature);
      expect(mockContext.json).toHaveBeenCalledWith('success', 200);
    });
    test('should handle errors when processing Stripe events', async () => {
      const mockSignature = 'mock-signature';
      const error = new Error('Failed to handle stripe event');

      const mockContext = {
        req: {
          header: jest.fn().mockReturnValue(mockSignature),
          text: jest.fn(() => Promise.resolve('request body')),
        },
        get: jest.fn(),
      } as unknown as Context;

      productService.handleStripeEvents.mockRejectedValue(error);
      mockContext.req.text = jest.fn(() => Promise.resolve('request body'));

      await expect(productsController.handleStripeEvents(mockContext)).rejects.toThrow(error);
    });
  });

  describe('method createCheckoutSession', () => {
    const mockServiceId = 'service123';
    const mockAccountId = 'account123';
    const mockProducts = [{ priceId: 'price_1A2B3C', quantity: 2 }];
    beforeEach(() => {
      productService = {
        handleStripeEvents: jest.fn(),
        createCheckoutSession: jest.fn(),
      } as unknown as jest.Mocked<ProductService>;

      productsController = new ProductsController(productService);
    });

    test('should create a checkout session and redirect to the checkout URL', async () => {
      const mockCheckoutResponse: StripeCheckoutInfo = {
        sessionId: 'sessionId',
        credential: 'credential',
        account: 'account',
      }

      const mockContext = {
        req: {
          header: jest.fn().mockReturnValue(mockServiceId),
          param: jest.fn().mockReturnValue(mockAccountId),
          json: jest.fn().mockResolvedValue(mockProducts),
        },
        json: jest.fn(),
        get: jest.fn().mockReturnValue(languageCode.JA),
      } as unknown as Context;

      productService.createCheckoutSession.mockResolvedValue(mockCheckoutResponse);

      await productsController.createCheckoutSession(mockContext);

      expect(mockContext.req.header).toHaveBeenCalledWith('Service-Id-Header');
      expect(mockContext.req.param).toHaveBeenCalledWith('accountId');
      expect(mockContext.req.json).toHaveBeenCalled();
      expect(productService.createCheckoutSession).toHaveBeenCalledWith(mockServiceId, mockAccountId, mockProducts);
    });

    test('should throw an error if creating a checkout session fails', async () => {
      const error = new Error('Failed to create checkout session');

      const mockContext = {
        req: {
          header: jest.fn().mockReturnValue(mockServiceId),
          param: jest.fn().mockReturnValue(mockAccountId),
          json: jest.fn().mockResolvedValue(mockProducts),
        },
        json: jest.fn(),
        get: jest.fn().mockReturnValue(languageCode.JA),
      } as unknown as Context;

      productService.createCheckoutSession.mockRejectedValue(error);

      await expect(productsController.createCheckoutSession(mockContext)).rejects.toThrow(error);

      expect(mockContext.req.header).toHaveBeenCalledWith('Service-Id-Header');
      expect(mockContext.req.param).toHaveBeenCalledWith('accountId');
      expect(mockContext.req.json).toHaveBeenCalled();
      expect(productService.createCheckoutSession).toHaveBeenCalledWith(mockServiceId, mockAccountId, mockProducts);
    });
  });
});
