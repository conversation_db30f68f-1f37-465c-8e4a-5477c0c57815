import { Context } from 'hono';
import { UserController } from '../../src/controllers/userController';
import { UserService } from '../../src/services/userService';
import { BaseError } from '../../src/errors/baseError';
import { NotFoundError } from '../../src/errors/notFoundError';
import { UserResponse } from '../../src/responsedto/userResponse';
import { User, UserCheck } from '../../src/dtos/users/schemas';
import { createMockUserService } from '../../src/utils/mockFactories';
jest.mock('../../src/repositories/userRepository');
jest.mock('../../src/repositories/accountRepository');
jest.mock('../../src/services/userService');

describe('UserController', () => {
  let userController: UserController;
  let mockUserService: jest.Mocked<UserService>;

  beforeEach(() => {
    mockUserService = createMockUserService();
    userController = new UserController(mockUserService);
    jest.clearAllMocks();
  });

  describe('create() method', () => {
    test('should successfully create a user', async () => {
      const mockContext = {
        req: {
          json: jest.fn().mockResolvedValue({
            id: 'newUserId',
            phoneCountryCode: 'JP+81',
            phone: '***********',
            contractAccountAddress: '0xContractAccountAddressSample',
          }),
        },
        json: jest.fn(),
      } as unknown as Context;

      const newUser = new UserResponse(
        'newUserId',
        'JP+81',
        '***********',
        '0xContractAccountAddressSample',
        'n9DK7fpekroiMiBsEkpZbPOJNlGZnLVKzi7AMQfkir9'
      );

      mockUserService.createUser.mockResolvedValue(newUser);

      await userController.create(mockContext);

      expect(mockContext.json).toHaveBeenCalledWith(newUser, 201);
      expect(mockUserService.createUser).toHaveBeenCalledWith({
        id: 'newUserId',
        phoneCountryCode: 'JP+81',
        phone: '***********',
        contractAccountAddress: '0xContractAccountAddressSample',
      });
    });

    test('should return error response when service throws error', async () => {
      const mockContext = {
        req: {
          json: jest.fn().mockResolvedValue({
            id: 'newUserId',
            phoneCountryCode: 'JP+81',
            phone: '***********',
            contractAccountAddress: '0xContractAccountAddressSample',
          }),
        },
        json: jest.fn(),
      } as unknown as Context;

      const error = new BaseError(400, 'TEST_ERROR', 'Test Error');
      mockUserService.createUser.mockRejectedValue(error);

      await expect(userController.create(mockContext)).rejects.toThrow('Test Error');
    });
  });

  describe('getSanitized() method', () => {
    test('should return sanitized user response when user exists', async () => {
      const mockContext = {
        req: {
          param: jest.fn().mockReturnValue('user123'),
          header: jest.fn().mockReturnValue('sample-service-id-header'),
        },
        json: jest.fn(),
      } as unknown as Context;

      const sanitizedUserResponse: User = {
        userId: 'user123',
        phone: {
          countryCode: 'US',
          phoneNumber: '**********',
        },
        account: {
          serviceId: 'n9DK7fpekroiMiBsEkpZbPOJNlGZnLVKzi7AMQfkir9',
          accountId: 'accountId',
        },
        contractAccountAddress: '0x123456789',
      };

      mockUserService.getSanitizedUserWithAccount.mockResolvedValueOnce(sanitizedUserResponse);

      await userController.getSanitized(mockContext);

      expect(mockContext.json).toHaveBeenCalledWith(sanitizedUserResponse, 200);
    });

    test('should return 404 when user does not exist', async () => {
      const mockContext = {
        req: {
          param: jest.fn().mockReturnValue('nonexistentuser'),
          header: jest.fn().mockReturnValue('sample-service-id-header'),
        },
        json: jest.fn(),
      } as unknown as Context;

      mockUserService.getSanitizedUserWithAccount.mockRejectedValueOnce(new NotFoundError('User not found'));

      await expect(userController.getSanitized(mockContext)).rejects.toThrow(NotFoundError);
    });
  });

  describe('updateContractAccount() method', () => {
    test('should update contract account successfully', async () => {
      const mockContext = {
        req: {
          param: jest.fn().mockReturnValue('user123'),
          json: jest.fn().mockResolvedValue({
            contractAccountAddress: '0xContractAccountAddressSample',
          }),
        },
        text: jest.fn(),
      } as unknown as Context;

      mockUserService.updateUserContractAccount.mockResolvedValueOnce(undefined);

      await userController.updateContractAccount(mockContext);

      expect(mockUserService.updateUserContractAccount).toHaveBeenCalledWith(
        'user123',
        '0xContractAccountAddressSample'
      );
      expect(mockContext.text).toHaveBeenCalledWith('', 200);
    });

    test('should throw an error when contract account update fails', async () => {
      const mockContext = {
        req: {
          param: jest.fn().mockReturnValue('user123'),
          json: jest.fn().mockResolvedValue({
            contractAccountAddress: '0xContractAccountAddressSample',
          }),
        },
        text: jest.fn(),
      } as unknown as Context;

      mockUserService.updateUserContractAccount.mockRejectedValueOnce(new Error('Update failed'));

      await expect(userController.updateContractAccount(mockContext)).rejects.toThrow('Update failed');
    });
  });

  describe('checkUser() method', () => {
    test('should return 200 and isUserExist = true when user exists', async () => {
      const mockContext = {
        req: {
          param: jest.fn().mockReturnValue('user123'),
        },
        json: jest.fn(),
      } as unknown as Context;

      const userCheckResponse: UserCheck = {
        isUserExist: true,
      };

      mockUserService.checkUser.mockResolvedValueOnce(userCheckResponse);

      await userController.checkUser(mockContext);

      expect(mockContext.json).toHaveBeenCalledWith(userCheckResponse, 200);
    });

    test('should return 200 and isUserExist = false when user does not exist', async () => {
      const mockContext = {
        req: {
          param: jest.fn().mockReturnValue('nonexistentuser'),
        },
        json: jest.fn(),
      } as unknown as Context;

      const userCheckResponse: UserCheck = {
        isUserExist: false,
      };

      mockUserService.checkUser.mockResolvedValueOnce(userCheckResponse);

      await userController.checkUser(mockContext);

      expect(mockContext.json).toHaveBeenCalledWith(userCheckResponse, 200);
    });
  });
});