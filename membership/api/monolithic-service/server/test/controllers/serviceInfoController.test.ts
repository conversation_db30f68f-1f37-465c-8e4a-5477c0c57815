import { Context } from 'hono';
import { ServiceInfoController } from '../../src/controllers/serviceInfoController';
import { ServiceInfoService } from '../../src/services/serviceInfoService';
import { ServiceInfoRepository } from '../../src/repositories/serviceInfoRepository';
import { TenantRepository } from '../../src/repositories/tenantRepository';
import { TokenBoundAccountRegistryAddressRepository } from '../../src/repositories/tokenBoundAccountRegistryAddressRepository';
import { TokenBoundAccountImplementationRepository } from '../../src/repositories/tokenBoundAccountImplementationsRepository';
import { RegisterServiceResponse, RegisterServiceRequest } from '../../src/dtos/services/schemas';
import { languageCode } from '../../src/enum/languageCode';

jest.mock('../../src/repositories/tenantRepository');
jest.mock('../../src/repositories/serviceInfoRepository');
jest.mock('../../src/repositories/tokenBoundAccountRegistryAddressRepository');
jest.mock('../../src/repositories/tokenBoundAccountImplementationsRepository');
jest.mock('../../src/services/serviceInfoService', () => ({
  ServiceInfoService: jest.fn().mockImplementation(() => {
    return {
      getServiceInfo: jest.fn(),
      registerService: jest.fn(),
    };
  }),
}));

describe('ServiceInfoController', () => {
  let serviceInfoService: jest.Mocked<ServiceInfoService>;
  let serviceInfoController: ServiceInfoController;
  let mockServiceInfoRepository: jest.Mocked<ServiceInfoRepository>;
  let mockTenantRepository: jest.Mocked<TenantRepository>;
  let mockTokenBoundAccountRegistryAddressRepository: jest.Mocked<TokenBoundAccountRegistryAddressRepository>;
  let mockTokenBoundAccountImplementationRepository: jest.Mocked<TokenBoundAccountImplementationRepository>;

  let context: Context;

  beforeEach(() => {
    mockServiceInfoRepository = new ServiceInfoRepository() as jest.Mocked<ServiceInfoRepository>;
    mockTenantRepository = new TenantRepository() as jest.Mocked<TenantRepository>;
    mockTokenBoundAccountRegistryAddressRepository =
      new TokenBoundAccountRegistryAddressRepository() as jest.Mocked<TokenBoundAccountRegistryAddressRepository>;
    mockTokenBoundAccountImplementationRepository =
      new TokenBoundAccountImplementationRepository() as jest.Mocked<TokenBoundAccountImplementationRepository>;

    serviceInfoService = new ServiceInfoService(
      mockServiceInfoRepository,
      mockTenantRepository,
      mockTokenBoundAccountRegistryAddressRepository,
      mockTokenBoundAccountImplementationRepository,
    ) as jest.Mocked<ServiceInfoService>;
    serviceInfoService.getServiceById = jest.fn();

    serviceInfoController = new ServiceInfoController(serviceInfoService);

    context = {
      req: {
        header: jest.fn(),
        json: jest.fn(),
      },
      json: jest.fn(),
    } as unknown as Context;
  });
  describe('getServiceById', () => {
    test('should register a service and return status 200', async () => {
      const mockTenantId = 'eb997fe3-a7a3-434b-86d1-e2fc28f7cf3e';
      const mockServiceId = 'bbc1499b-c47c-4c9b-befc-5717dff1c5ac';

      const mockRequestBody: RegisterServiceRequest = {
        serviceTranslations: [
          {
            language: languageCode.EN_US,
            serviceName: 'My New Service',
            servicePolicy: 'RXhhbXBsZSBzZXJ2aWNlIHBvbGljeSBjb250ZW50',
            servicePane: 'RXhhbXBsZSBzZXJ2aWNlIHBhbmUgY29udGVudA==',
          },
        ],
        serviceUrl: 'https://www.newservice.com',
        serviceLogoImageUrl: 'https://www.newservice.com/logo.png',
        themePrimaryColorLowest: '#FFFFFFF0',
        themePrimaryColorLower: '#FFFFFFF1',
        themePrimaryColorHigher: '#FFFFFFF2',
        themePrimaryColorHighest: '#FFFFFFF3',
        isMarketEnabled: true,
        stripeAccountId: 'acct_123ABC',
        lineChannelId: 'acct_123ABC',
        marketCoverImageUrl: undefined,
        modularContractId: 'acct_123ABC',
      };

      const mockResponse: RegisterServiceResponse = {
        serviceId: mockServiceId,
        serviceTranslations: mockRequestBody.serviceTranslations,
        logoImageUrl: mockRequestBody.serviceLogoImageUrl,
        marketCoverImageUrl: undefined,
        themePrimaryColorLowest: mockRequestBody.themePrimaryColorLowest || undefined,
        themePrimaryColorLower: mockRequestBody.themePrimaryColorLower || undefined,
        themePrimaryColorHigher: mockRequestBody.themePrimaryColorHigher || undefined,
        themePrimaryColorHighest: mockRequestBody.themePrimaryColorHighest || undefined,
        isMarketEnabled: mockRequestBody.isMarketEnabled,
      };
      (context.req.header as jest.Mock).mockReturnValue(mockTenantId);
      (context.req.json as jest.Mock).mockResolvedValue(mockRequestBody);
      serviceInfoService.registerService.mockResolvedValue(mockResponse);

      await serviceInfoController.registerService(context);

      expect(context.req.header).toHaveBeenCalledWith('tenant-id-header');
      expect(context.req.json).toHaveBeenCalled();
      expect(serviceInfoService.registerService).toHaveBeenCalledWith(mockTenantId, mockRequestBody);
      expect(context.json).toHaveBeenCalledWith(mockResponse, 200);
    });

    test('should handle errors during service registration', async () => {
      const mockTenantId = 'test-tenant-id';
      const mockRequestBody: RegisterServiceRequest = {
        serviceTranslations: [
          {
            language: languageCode.EN_US,
            serviceName: 'My New Service',
            servicePolicy: 'RXhhbXBsZSBzZXJ2aWNlIHBvbGljeSBjb250ZW50',
            servicePane: 'RXhhbXBsZSBzZXJ2aWNlIHBhbmUgY29udGVudA==',
          },
        ],
        serviceUrl: 'https://www.newservice.com',
        serviceLogoImageUrl: 'https://www.newservice.com/logo.png',
        themePrimaryColorLowest: '#FFFFFFF0',
        themePrimaryColorLower: '#FFFFFFF1',
        themePrimaryColorHigher: '#FFFFFFF2',
        themePrimaryColorHighest: '#FFFFFFF3',
        isMarketEnabled: true,
        stripeAccountId: 'acct_123ABC',
        lineChannelId: 'acct_123ABC',
        marketCoverImageUrl: undefined,
        modularContractId: 'acct_123ABC',
      };

      const mockError = new Error('Registration failed');

      (context.req.header as jest.Mock).mockReturnValue(mockTenantId);
      (context.req.json as jest.Mock).mockResolvedValue(mockRequestBody);
      serviceInfoService.registerService.mockRejectedValue(mockError);

      await expect(serviceInfoController.registerService(context)).rejects.toThrow('Registration failed');

      expect(context.req.header).toHaveBeenCalledWith('tenant-id-header');
      expect(context.req.json).toHaveBeenCalled();
      expect(serviceInfoService.registerService).toHaveBeenCalledWith(mockTenantId, mockRequestBody);
    });
  });
});
