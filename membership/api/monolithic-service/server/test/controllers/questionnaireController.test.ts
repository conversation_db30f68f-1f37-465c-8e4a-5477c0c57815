import { Context } from 'hono';
import { QuestionnaireController } from '../../src/controllers/questionnaireController';
import { QuestionnaireService } from '../../src/services/questionnaireService';
import { ServiceInfoRepository } from '../../src/repositories/serviceInfoRepository';
import { QuestionType } from '../../src/enum/questionTypeEnum';
import { QuestionnaireThemeRepository } from '../../src/repositories/questionnaireThemeRepository';
import { QuestionnaireType } from '../../src/enum/questionnaireTypeEnum';
import { QuestionnaireQuestionRepository } from '../../src/repositories/questionnaireQuestionRepository';
import { QuestionnaireResultAnswerRepository } from '../../src/repositories/questionnaireResultAnswerRepository';
import { QuestionnaireRepository } from '../../src/repositories/questionnaireRepository';
import { languageCode } from '../../src/enum/languageCode';
import {
  QuestionnaireCreateRequest,
  QuestionnaireDetailResponse,
  QuestionnaireUpdateRequest,
} from '../../src/dtos/questionnaire/schemas';

jest.mock('../../src/utils/middleware/firebaseAuthAccessMiddleware', () => ({
  parseCustomToken: jest.fn(),
}));

jest.mock('../../src/services/questionnaireService', () => ({
  QuestionnaireService: jest.fn().mockImplementation(() => ({
    createQuestionnaire: jest.fn(),
    updateQuestionnaire: jest.fn(),
  })),
}));

describe('QuestionnaireController', () => {
  let questionnaireService: jest.Mocked<QuestionnaireService>;
  let questionnaireController: QuestionnaireController;
  let context: Context;
  let serviceInfoRepository: jest.Mocked<ServiceInfoRepository>;
  let questionnaireActionRepository: jest.Mocked<QuestionnaireRepository>;
  let questionnaireThemeRepository: jest.Mocked<QuestionnaireThemeRepository>;
  let questionnaireResultAnswerRepository: jest.Mocked<QuestionnaireResultAnswerRepository>;
  let questionnaireQuestionRepository: jest.Mocked<QuestionnaireQuestionRepository>;

  beforeEach(() => {
    serviceInfoRepository = new ServiceInfoRepository() as jest.Mocked<ServiceInfoRepository>;
    questionnaireActionRepository = new QuestionnaireRepository() as jest.Mocked<QuestionnaireRepository>;
    questionnaireThemeRepository = new QuestionnaireThemeRepository() as jest.Mocked<QuestionnaireThemeRepository>;
    questionnaireResultAnswerRepository =
      new QuestionnaireResultAnswerRepository() as jest.Mocked<QuestionnaireResultAnswerRepository>;
    questionnaireQuestionRepository =
      new QuestionnaireQuestionRepository() as jest.Mocked<QuestionnaireQuestionRepository>;

    questionnaireService = new QuestionnaireService(
      serviceInfoRepository,
      questionnaireActionRepository,
      questionnaireThemeRepository,
      questionnaireResultAnswerRepository,
      questionnaireQuestionRepository,
    ) as jest.Mocked<QuestionnaireService>;

    questionnaireController = new QuestionnaireController(questionnaireService);

    context = {
      req: {
        header: jest.fn(),
        json: jest.fn(),
        param: jest.fn(),
      },
      json: jest.fn(),
    } as unknown as Context;
  });

  describe('updateQuestionnaire', () => {
    test('should call updateQuestionnaire on the service and return 200', async () => {
      const mockServiceId = 'test-service-id';
      const mockQuestionnaireId = 'test-questionnaire-id';
      const mockRequestBody: QuestionnaireUpdateRequest = {
        questionnaireType: QuestionnaireType.QUIZ,
        themes: [
          {
            themeId: 'theme-1',
            themeThumbnailImageUrl: 'https://example.com/updated-theme.jpg',
            themeCoverImageUrl: 'https://example.com/cover1.jpg',
            themeNumber: 1,
            themeTimeLimitSeconds: 300,
            themeTranslations: [
              {
                language: languageCode.EN_US,
                themeTitle: 'Updated General Knowledge',
                themeDescription: 'Updated general knowledge questions.',
              },
            ],
            questions: [
              {
                questionId: 'question-1',
                questionNumber: 1,
                questionType: QuestionType.NUMBER,
                answerPoint: 15,
                questionImageUrl: 'https://example.com/question-image.jpg',
                isRequired: true,
                questionTranslations: [
                  {
                    questionDetail: 'Select the correct city.',
                    language: languageCode.JA,
                    questionTitle: 'What is the capital of Italy?',
                    correctData: '10',
                    questionExtra: {
                      type: QuestionType.NUMBER,
                      validations: [],
                    },
                    correctDataValidation: '',
                  },
                ],
              },
            ],
          },
        ],
        ranks: [
            {
              rankId: 'rank-1',
              rankHeaderAnimationUrl: 'https://example.com/updated-animations.riv',
              rank: 2,
              lowerLimitPoints: 51,
              upperLimitPoints: 100,
              isPassed: true,
              rankTranslations: [
                {
                  language: languageCode.JA,
                  rankName: '中級者',
                },
                {
                  language: languageCode.EN_US,
                  rankName: 'Intermediate',
                },
              ],
            },
          ],
      };

      const mockResult: QuestionnaireDetailResponse = {
        questionnaireId: mockQuestionnaireId,
        questionnaireType: QuestionnaireType.QUIZ,
        ranks: {
          maxRank: 2,
          passingPoints: 51,
          rankDetails: [
            {
              rankId: 'rank-1',
              rank: 2,
              lowerLimitPoints: 51,
              upperLimitPoints: 100,
              rankHeaderAnimationUrl: 'https://example.com/updated-animations.riv',
              isPassed: true,
              rankTranslations: [
                {
                  language: languageCode.JA,
                  rankName: '中級者',
                },
                {
                  language: languageCode.EN_US,
                  rankName: 'Intermediate',
                },
              ],
            },
          ],
        },
        themes: [
          {
            themeId: 'theme-1',
            themeThumbnailImageUrl: 'https://example.com/updated-theme.jpg',
            themeCoverImageUrl: 'https://example.com/cover1.jpg',
            themeNumber: 1,
            themeTimeLimitSeconds: 300,
            themeTranslations: [
              {
                language: languageCode.EN_US,
                themeTitle: 'Updated General Knowledge',
                themeDescription: 'Updated general knowledge questions.',
              },
            ],
          },
        ],
        questions: [
          {
            questionId: 'question-1',
            questionNumber: 1,
            questionType: QuestionType.NUMBER,
            answerPoint: 15,
            questionImageUrl: 'https://example.com/question-image.jpg',
            isRequired: true,
            questionTranslations: [
              {
                questionDetail: 'Select the correct city.',
                language: languageCode.JA,
                questionTitle: 'What is the capital of Italy?',
                questionExtra: {
                  type: QuestionType.NUMBER,
                  validations: [],
                },
                correctData: '10',
                correctDataValidation: '',
              },
            ],
          },
        ],
      };
      (context.req.param as jest.Mock).mockReturnValue(mockQuestionnaireId);
      (context.req.header as jest.Mock).mockImplementation((headerName: string) => {
        if (headerName === 'Service-Id-Header') return mockServiceId;
      });
      (context.req.json as jest.Mock).mockResolvedValue(mockRequestBody);

      questionnaireService.updateQuestionnaire.mockResolvedValue(mockResult);

      await questionnaireController.updateQuestionnaire(context);

      expect(context.req.param).toHaveBeenCalledWith('questionnaireId');
      expect(context.req.header).toHaveBeenCalledWith('Service-Id-Header');
      expect(context.req.json).toHaveBeenCalled();
      expect(questionnaireService.updateQuestionnaire).toHaveBeenCalledWith(
        mockServiceId,
        mockQuestionnaireId,
        mockRequestBody,
      );
      expect(context.json).toHaveBeenCalledWith(mockResult, 200);
    });

    test('should handle errors gracefully when service throws an error', async () => {
      const mockServiceId = 'test-service-id';
      const mockQuestionnaireId = 'test-questionnaire-id';
      const mockRequestBody = {};
      const mockError = new Error('Failed to update questionnaire');

      (context.req.param as jest.Mock).mockReturnValue(mockQuestionnaireId);
      (context.req.header as jest.Mock).mockImplementation((headerName: string) => {
        if (headerName === 'Service-Id-Header') return mockServiceId;
      });
      (context.req.json as jest.Mock).mockResolvedValue(mockRequestBody);
      questionnaireService.updateQuestionnaire.mockRejectedValue(mockError);

      await expect(questionnaireController.updateQuestionnaire(context)).rejects.toThrow(
        'Failed to update questionnaire',
      );

      expect(context.req.param).toHaveBeenCalledWith('questionnaireId');
      expect(context.req.header).toHaveBeenCalledWith('Service-Id-Header');
      expect(context.req.param).toHaveBeenCalled();
      expect(context.req.json).toHaveBeenCalled();
      expect(questionnaireService.updateQuestionnaire).toHaveBeenCalledWith(
        mockServiceId,
        mockQuestionnaireId,
        mockRequestBody,
      );
    });
  });

  describe('createQuestionnaire', () => {
      const mockServiceId = 'test-service-id';
    test('should call createQuestionnaire on the service and return 200', async () => {
      const mockRequestBody: QuestionnaireCreateRequest = {
        questionnaireType: QuestionnaireType.QUIZ,
        themes: [
          {
            themeThumbnailImageUrl: 'https://example.com/theme.jpg',
            themeCoverImageUrl: 'https://example.com/cover1.jpg',
            themeNumber: 1,
            themeTimeLimitSeconds: 600,
            themeTranslations: [
              {
                language: languageCode.EN_US,
                themeTitle: 'General Knowledge',
                themeDescription: 'General knowledge questions.',
              },
            ],
            questions: [
              {
                questionNumber: 1,
                questionType: QuestionType.SINGLE_CHOICE,
                answerPoint: 10,
                questionImageUrl: 'https://example.com/question-image.jpg',
                isRequired: true,
                questionTranslations: [
                  {
                    questionDetail: 'Select the correct city.',
                    language: languageCode.JA,
                    questionTitle: 'What is the capital of France?',
                    questionExtra: {
                      type: QuestionType.SINGLE_CHOICE,
                      selections: [
                        {
                          order: 0,
                          selectionText: 'Paris',
                        },
                        {
                          order: 1,
                          selectionText: 'London',
                        },
                        {
                          order: 2,
                          selectionText: 'Berlin',
                        },
                      ],
                    },
                    correctData: 'Paris',
                    correctDataValidation: '',
                  },
                ],
              },
            ],
          },
        ],
        ranks: [
          {
            rankHeaderAnimationUrl: 'https://example.com/animations.riv',
            rank: 1,
            lowerLimitPoints: 0,
            upperLimitPoints: 50,
            isPassed: false,
            rankTranslations: [
              {
                language: languageCode.JA,
                rankName: '初級者',
              },
              {
                language: languageCode.EN_US,
                rankName: 'Beginner',
              },
            ],
          },
        ],
      };

      const mockResult: QuestionnaireDetailResponse = {
        questionnaireId: 'generated-id',
        questionnaireType: QuestionnaireType.QUIZ,
        ranks: {
          maxRank: 1,
          passingPoints: 0,
          rankDetails: [
            {
              rankId: 'rank-id-1',
              rank: 1,
              lowerLimitPoints: 0,
              upperLimitPoints: 50,
              rankHeaderAnimationUrl: 'https://example.com/animations.riv',
              isPassed: false,
              rankTranslations: [
                {
                  language: languageCode.JA,
                  rankName: '初級者',
                },
                {
                  language: languageCode.EN_US,
                  rankName: 'Beginner',
                },
              ],
            },
          ],
        },
        themes: [
          {
            themeId: 'theme-id-1',
            themeThumbnailImageUrl: 'https://example.com/theme.jpg',
            themeCoverImageUrl: 'https://example.com/cover1.jpg',
            themeNumber: 1,
            themeTimeLimitSeconds: 600,
            themeTranslations: [
              {
                language: languageCode.EN_US,
                themeTitle: 'General Knowledge',
                themeDescription: 'General knowledge questions.',
              },
              {
                language: languageCode.JA,
                themeTitle: '一般常識',
                themeDescription: '一般常識の問題です。',
              },
            ],
          },
        ],
        questions: [
          {
            questionId: 'question-id-1',
            questionNumber: 1,
            questionType: QuestionType.NUMBER,
            answerPoint: 10,
            questionImageUrl: 'https://example.com/question-image.jpg',
            isRequired: true,
            questionTranslations: [
              {
                questionDetail: 'Select the correct city.',
                language: languageCode.JA,
                questionTitle: 'What is the capital of France?',
                questionExtra: {
                  type: QuestionType.NUMBER,
                  validations: [],
                },
                correctData: '',
                correctDataValidation: '',
              },
            ],
          },
        ],
      };

      (context.req.header as jest.Mock).mockImplementation((headerName: string) => {
        if (headerName === 'Service-Id-Header') return mockServiceId;
      });
      (context.req.json as jest.Mock).mockResolvedValue(mockRequestBody);
      questionnaireService.createQuestionnaire.mockResolvedValue(mockResult);

      await questionnaireController.createQuestionnaire(context);

      expect(context.req.header).toHaveBeenCalledWith('Service-Id-Header');
      expect(context.req.json).toHaveBeenCalled();
      expect(questionnaireService.createQuestionnaire).toHaveBeenCalledWith(mockServiceId, mockRequestBody);
      expect(context.json).toHaveBeenCalledWith(mockResult, 200);
    });

    test('should handle errors gracefully when service throws an error', async () => {
      const mockRequestBody = {};
      const mockError = new Error('Failed to create questionnaire');

      (context.req.header as jest.Mock).mockImplementation((headerName: string) => {
        if (headerName === 'Service-Id-Header') return mockServiceId;
      });
      (context.req.json as jest.Mock).mockResolvedValue(mockRequestBody);
      questionnaireService.createQuestionnaire.mockRejectedValue(mockError);

      await expect(questionnaireController.createQuestionnaire(context)).rejects.toThrow(
        'Failed to create questionnaire',
      );

      expect(context.req.header).toHaveBeenCalledWith('Service-Id-Header');
      expect(context.req.json).toHaveBeenCalled();
      expect(questionnaireService.createQuestionnaire).toHaveBeenCalledWith(mockServiceId, mockRequestBody);
    });
  });
});
