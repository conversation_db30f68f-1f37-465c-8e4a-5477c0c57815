import { Context } from 'hono';
import { AdminController } from '../../src/controllers/adminController';
import { AdminService } from '../../src/services/adminService';
import { NotFoundError } from '../../src/errors/notFoundError';
import { UpdateMetadataResponse } from '../../src/dtos/admins/schemas';
import { RewardPointService } from '../../src/services/rewardPointService';
import { StatusPointService } from '../../src/services/statusPointService';
import { NftContractsRepository } from '../../src/repositories/nftContractsRepository';
import { MetadataFetchService } from '../../src/services/metadataFetchService';
import { NftsFirestoreRepository } from '../../src/repositories/nftsFirestoreRepository';

jest.mock('../../src/services/adminService');

describe('AdminController', () => {
  let adminController: AdminController;
  let mockAdminService: jest.Mocked<AdminService>;

  beforeEach(() => {
    mockAdminService = new AdminService({} as unknown as NftsFirestoreRepository, {} as unknown as NftContractsRepository, {} as unknown as MetadataFetchService, {} as unknown as RewardPointService, {} as unknown as StatusPointService) as jest.Mocked<AdminService>;
    adminController = new AdminController(mockAdminService);
    jest.clearAllMocks();
  });

  describe('updateNftMetadata', () => {
    test('should return 200 and include updatedTokenUris and failedIds in the response when the request is valid', async () => {
      const mockContext = {
        req: {
          json: jest.fn().mockResolvedValue({
            contractAddress: '******************************************',
            tokenId: ['1', '2'],
          }),
        },
        json: jest.fn(),
      } as unknown as Context;

      const mockResponse: UpdateMetadataResponse = {
        updatedTokenUris: [
          { tokenId: '1', tokenUri: 'uri1' },
          { tokenId: '2', tokenUri: 'uri2' },
        ],
        failedIds: [],
      };
      mockAdminService.updateFirestoreMetadata.mockResolvedValue(mockResponse);
      await adminController.updateNftMetadata(mockContext);
      expect(mockAdminService.updateFirestoreMetadata).toHaveBeenCalledWith(
        '******************************************',
        ['1', '2'],
      );
      expect(mockContext.json).toHaveBeenCalledWith(mockResponse, 200);
    });

    test('should return 404 when AdminService throws a NotFoundError', async () => {
      const mockContext = {
        req: {
          json: jest.fn().mockResolvedValue({
            contractAddress: '******************************************',
            tokenIds: ['999'],
          }),
        },
        json: jest.fn(),
      } as unknown as Context;

      const notFoundError = new NotFoundError('Contract not found');
      mockAdminService.updateFirestoreMetadata.mockRejectedValue(notFoundError);
      await adminController.updateNftMetadata(mockContext);

      expect(mockContext.json).toHaveBeenCalledWith({ code: 'NOT_FOUND', message: 'Resource not found', status: 404 }, 404);
    });

    test('should throw an error when AdminService throws an unexpected error', async () => {
      const mockContext = {
        req: {
          json: jest.fn().mockResolvedValue({
            contractAddress: '******************************************',
            tokenIds: ['999'],
          }),
        },
        json: jest.fn(),
      } as unknown as Context;

      const mockError = new Error('Something went wrong in the service');
      mockAdminService.updateFirestoreMetadata.mockRejectedValue(mockError);

      await adminController.updateNftMetadata(mockContext);

      expect(mockContext.json).toHaveBeenCalledWith({ code: 'INTERNAL_SERVER_ERROR', message: 'Internal server error', status: 500 }, 500);
    });
  });
});
