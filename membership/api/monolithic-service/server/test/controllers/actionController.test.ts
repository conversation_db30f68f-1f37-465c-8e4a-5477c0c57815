import { ActionController } from '../../src/controllers/actionController';
import { ActionType } from '../../src/enum/actionType';
import { ActionDetail } from '../../src/dtos/services/schemas';

import { AchievementActionRepository } from '../../src/repositories/achievementActionRepository';
import { ActionActivityRepository } from '../../src/repositories/actionActivityRepository';
import { ActionRepository } from '../../src/repositories/actionRepository';
import { OnlineCheckinActionRepository } from '../../src/repositories/onlineCheckinActionRepository';
import { QrCheckinActionRepository } from '../../src/repositories/qrCheckinActionRepository';
import { QuestActivityRepository } from '../../src/repositories/questActivityRepository';
import { QuestionnaireActionRepository } from '../../src/repositories/questionnaireActionRepository';
import { QuestionnaireQuestionAnswerRepository } from '../../src/repositories/questionnaireQuestionAnswerRepository';
import { QuestionnaireQuestionRepository } from '../../src/repositories/questionnaireQuestionRepository';
import { QuestionnaireRepository } from '../../src/repositories/questionnaireRepository';
import { QuestionnaireResultAnswerRepository } from '../../src/repositories/questionnaireResultAnswerRepository';
import { QuestionnaireResultRankRepository } from '../../src/repositories/questionnaireResultRankRepository';
import { QuestRepository } from '../../src/repositories/questRepository';
import { ActionService } from '../../src/services/actionService';
import { Context } from 'hono';
import { SerialCodeService } from '../../src/services/serialCodeService';
import { SerialCodeProjectsRepository } from '../../src/repositories/serialCodeProjectsRepository';
import { SerialCodesRepository } from '../../src/repositories/serialCodesRepository';
import { AccountSerialCodesRepository } from '../../src/repositories/accountSerialCodesRepository';
import { RewardService } from '../../src/services/rewardService';
import { RewardRepository } from '../../src/repositories/rewardRepository';
import { AccountRepository } from '../../src/repositories/accountRepository';
import { CertificateRewardRepository } from '../../src/repositories/certificateRewardRepository';
import { CouponRewardRepository } from '../../src/repositories/couponRewardRepository';
import { DigitalContentRewardRepository } from '../../src/repositories/digitalContentRewardRepository';
import { ClaimedRewardRepository } from '../../src/repositories/claimedRewardRepository';
import { MetadataService } from '../../src/services/metadataService';
import { NftsService } from '../../src/services/nftsService';
import { NftMintService } from '../../src/services/nftMintService';
import { NftContractsRepository } from '../../src/repositories/nftContractsRepository';
import { VaultKeyRepository } from '../../src/repositories/vaultKeyRepository';
import { ServiceInfoRepository } from '../../src/repositories/serviceInfoRepository';
import { NftMetadatasRepository } from '../../src/repositories/nftMetadatasRepository';
import { NftBaseMetadatasRepository } from '../../src/repositories/nftBaseMetadatasRepository';
import { NftContractTypesRepository } from '../../src/repositories/nftContractTypesRepository';
import { TransactionQueuesRepository } from '../../src/repositories/transactionQueuesRepository';
import { languageCode } from '../../src/enum/languageCode';
import { SerialCodeActionRepository } from '../../src/repositories/serialCodeActionRepository';
import { LocationCheckinActionRepository } from '../../src/repositories/locationCheckinActionRepository';
import { GeofenceRepository } from '../../src/repositories/geofenceRepository';
import { DeliveryNftsFirestoreRepository } from '../../src/repositories/deliveryNftsFirestoreRepository';
import { FirebaseComponent } from '../../src/components/firebaseComponent';

jest.mock('../../src/services/actionService');
jest.mock('../../src/repositories/questionnaireActionRepository');

describe('ActionController', () => {
  let actionService: jest.Mocked<ActionService>;
  let actionController: ActionController;
  let context: Context;
  let mockActionRepository: jest.Mocked<ActionRepository>;
  let mockAchievementActionRepository: jest.Mocked<AchievementActionRepository>;
  let mockOnlineCheckinActionRepository: jest.Mocked<OnlineCheckinActionRepository>;
  let mockQuestionnaireActionRepository: jest.Mocked<QuestionnaireActionRepository>;
  let mockActionActivityRepository: jest.Mocked<ActionActivityRepository>;
  let mockQuestRepository: jest.Mocked<QuestRepository>;
  let mockQuestActivityRepository: jest.Mocked<QuestActivityRepository>;
  let mockQrCheckinActionRepository: jest.Mocked<QrCheckinActionRepository>;
  let mockQuestionnaireQuestionRepository: jest.Mocked<QuestionnaireQuestionRepository>;
  let mockQuestionnaireQuestionAnswerRepository: jest.Mocked<QuestionnaireQuestionAnswerRepository>;
  let mockQuestionnaireResultAnswerRepository: jest.Mocked<QuestionnaireResultAnswerRepository>;
  let mockQuestionnaireResultRankRepository: jest.Mocked<QuestionnaireResultRankRepository>;
  let mockQuestionnaireRepository: jest.Mocked<QuestionnaireRepository>;
  let mockSerialCodeProjectsRepository: jest.Mocked<SerialCodeProjectsRepository>;
  let mockSerialCodesRepository: jest.Mocked<SerialCodesRepository>;
  let mockAccountSerialCodesRepository: jest.Mocked<AccountSerialCodesRepository>;
  let mockRewardRepository: jest.Mocked<RewardRepository>;
  let mockAccountRepository: jest.Mocked<AccountRepository>;
  let mockCertificateRewardRepository: jest.Mocked<CertificateRewardRepository>;
  let mockCouponRewardRepository: jest.Mocked<CouponRewardRepository>;
  let mockDigitalContentRewardRepository: jest.Mocked<DigitalContentRewardRepository>;
  let mockClaimedRewardRepository: jest.Mocked<ClaimedRewardRepository>;
  let mockNftContractsRepository: jest.Mocked<NftContractsRepository>;
  let mockVaultKeyRepository: jest.Mocked<VaultKeyRepository>;
  let mockServiceInfoRepository: jest.Mocked<ServiceInfoRepository>;
  let mockNftMetadatasRepository: jest.Mocked<NftMetadatasRepository>;
  let mockNftBaseMetadatasRepository: jest.Mocked<NftBaseMetadatasRepository>;
  let mockNftContractTypesRepository: jest.Mocked<NftContractTypesRepository>;
  let mockTransactionQueuesRepository: jest.Mocked<TransactionQueuesRepository>;
  let mockMetadataService: jest.Mocked<MetadataService>;
  let mockNftsService: jest.Mocked<NftsService>;
  let mockNftMintService: jest.Mocked<NftMintService>;
  let mockRewardService: jest.Mocked<RewardService>;
  let mockSerialCodeService: jest.Mocked<SerialCodeService>;
  let mockSerialCodeActionRepository: jest.Mocked<SerialCodeActionRepository>;
  let mockLocationCheckinActionRepository: jest.Mocked<LocationCheckinActionRepository>;
  let mockGeofenceRepository: jest.Mocked<GeofenceRepository>;
  let mockDeliveryNftsFirestoreRepository: jest.Mocked<DeliveryNftsFirestoreRepository>;

  beforeEach(() => {
    process.env.JSON_RPC_URL = 'http://127.0.0.1:8545';
    process.env.ALCHEMY_API_KEY = 'xxxxxsss';
    process.env.ALCHEMY_CHAIN_NAME = 'polygon-amoy';
    process.env.GAS_LIMIT_MULTIPLIER = '200';
    process.env.BASE_MAX_FEE_PER_GAS = '600';
    process.env.MAX_FEE_PER_GAS_MULTIPLIER = '150';
    process.env.MAX_PRIORITY_FEE_PER_GAS_MULTIPLIER = '150';
    process.env.GCP_PROJECT_ID = 'sample_gcp_project_id';
    process.env.MORALIS_MEMBERSHIP_STREAM_ID = 'mockMembershipStreamId';
    process.env.MORALIS_REWARD_STREAM_ID = 'mockRewardStreamId';

    mockActionRepository = new ActionRepository() as jest.Mocked<ActionRepository>;
    mockActionActivityRepository = new ActionActivityRepository() as jest.Mocked<ActionActivityRepository>;
    mockAchievementActionRepository = new AchievementActionRepository() as jest.Mocked<AchievementActionRepository>;
    mockOnlineCheckinActionRepository =
      new OnlineCheckinActionRepository() as jest.Mocked<OnlineCheckinActionRepository>;
    mockQuestRepository = new QuestRepository() as jest.Mocked<QuestRepository>;
    mockQuestActivityRepository = new QuestActivityRepository() as jest.Mocked<QuestActivityRepository>;
    mockQrCheckinActionRepository = new QrCheckinActionRepository() as jest.Mocked<QrCheckinActionRepository>;
    mockQuestionnaireRepository = new QuestionnaireRepository() as jest.Mocked<QuestionnaireRepository>;
    mockQuestionnaireActionRepository =
      new QuestionnaireActionRepository() as jest.Mocked<QuestionnaireActionRepository>;

    mockQuestionnaireQuestionRepository =
      new QuestionnaireQuestionRepository() as jest.Mocked<QuestionnaireQuestionRepository>;
    mockQuestionnaireQuestionAnswerRepository =
      new QuestionnaireQuestionAnswerRepository() as jest.Mocked<QuestionnaireQuestionAnswerRepository>;
    mockQuestionnaireResultAnswerRepository =
      new QuestionnaireResultAnswerRepository() as jest.Mocked<QuestionnaireResultAnswerRepository>;
    mockQuestionnaireResultRankRepository =
      new QuestionnaireResultRankRepository() as jest.Mocked<QuestionnaireResultRankRepository>;
    mockQuestionnaireRepository = new QuestionnaireRepository() as jest.Mocked<QuestionnaireRepository>;
    mockTransactionQueuesRepository = new TransactionQueuesRepository() as jest.Mocked<TransactionQueuesRepository>;

    mockSerialCodeProjectsRepository = new SerialCodeProjectsRepository() as jest.Mocked<SerialCodeProjectsRepository>;
    mockSerialCodesRepository = new SerialCodesRepository() as jest.Mocked<SerialCodesRepository>;
    mockAccountSerialCodesRepository = new AccountSerialCodesRepository() as jest.Mocked<AccountSerialCodesRepository>;
    mockRewardRepository = new RewardRepository() as jest.Mocked<RewardRepository>;
    mockAccountRepository = new AccountRepository() as jest.Mocked<AccountRepository>;
    mockCertificateRewardRepository = new CertificateRewardRepository() as jest.Mocked<CertificateRewardRepository>;
    mockCouponRewardRepository = new CouponRewardRepository() as jest.Mocked<CouponRewardRepository>;
    mockDigitalContentRewardRepository =
      new DigitalContentRewardRepository() as jest.Mocked<DigitalContentRewardRepository>;
    mockClaimedRewardRepository = new ClaimedRewardRepository() as jest.Mocked<ClaimedRewardRepository>;
    mockAchievementActionRepository = new AchievementActionRepository() as jest.Mocked<AchievementActionRepository>;
    mockQuestActivityRepository = new QuestActivityRepository() as jest.Mocked<QuestActivityRepository>;
    mockNftContractsRepository = new NftContractsRepository() as jest.Mocked<NftContractsRepository>;
    mockVaultKeyRepository = new VaultKeyRepository() as jest.Mocked<VaultKeyRepository>;
    mockServiceInfoRepository = new ServiceInfoRepository() as jest.Mocked<ServiceInfoRepository>;
    mockNftMetadatasRepository = new NftMetadatasRepository() as jest.Mocked<NftMetadatasRepository>;
    mockNftBaseMetadatasRepository = new NftBaseMetadatasRepository() as jest.Mocked<NftBaseMetadatasRepository>;
    mockNftContractTypesRepository = new NftContractTypesRepository() as jest.Mocked<NftContractTypesRepository>;
    mockSerialCodeActionRepository = new SerialCodeActionRepository() as jest.Mocked<SerialCodeActionRepository>;
    mockLocationCheckinActionRepository = new LocationCheckinActionRepository() as jest.Mocked<LocationCheckinActionRepository>;
    mockGeofenceRepository = new GeofenceRepository() as jest.Mocked<GeofenceRepository>;
    mockDeliveryNftsFirestoreRepository = new DeliveryNftsFirestoreRepository(
      {} as FirebaseComponent,
    ) as jest.Mocked<DeliveryNftsFirestoreRepository>;

    mockMetadataService = new MetadataService(
      mockNftMetadatasRepository,
      mockNftBaseMetadatasRepository,
      mockNftContractTypesRepository,
    ) as jest.Mocked<MetadataService>;
    mockNftsService = new NftsService(
      mockNftContractsRepository,
      mockAccountRepository,
      mockRewardRepository,
    ) as jest.Mocked<NftsService>;
    mockNftMintService = new NftMintService(
      mockNftContractsRepository,
      mockTransactionQueuesRepository,
      mockVaultKeyRepository,
      mockServiceInfoRepository,
      mockDeliveryNftsFirestoreRepository,
    ) as jest.Mocked<NftMintService>;
    mockRewardService = new RewardService(
      mockRewardRepository,
      mockAccountRepository,
      mockCertificateRewardRepository,
      mockCouponRewardRepository,
      mockDigitalContentRewardRepository,
      mockClaimedRewardRepository,
      mockAchievementActionRepository,
      mockQuestActivityRepository,
      mockNftMintService,
      mockNftsService,
      mockMetadataService,
      mockQuestionnaireResultAnswerRepository,
    ) as jest.Mocked<RewardService>;

    mockSerialCodeService = new SerialCodeService(
      mockSerialCodeProjectsRepository,
      mockSerialCodesRepository,
      mockAccountSerialCodesRepository,
      mockRewardService,
    ) as jest.Mocked<SerialCodeService>;

    actionService = new ActionService(
      mockActionRepository,
      mockActionActivityRepository,
      mockAchievementActionRepository,
      mockOnlineCheckinActionRepository,
      mockQuestRepository,
      mockQuestActivityRepository,
      mockQrCheckinActionRepository,
      mockQuestionnaireActionRepository,
      mockQuestionnaireRepository,
      mockQuestionnaireQuestionRepository,
      mockQuestionnaireQuestionAnswerRepository,
      mockQuestionnaireResultAnswerRepository,
      mockQuestionnaireResultRankRepository,
      mockSerialCodeActionRepository,
      mockSerialCodeService,
      mockLocationCheckinActionRepository,
      mockGeofenceRepository,
    ) as jest.Mocked<ActionService>;
    actionController = new ActionController(actionService);
    context = {
      req: {
        header: jest.fn(),
        param: jest.fn(),
        json: jest.fn(),
      },
      json: jest.fn(),
      get: jest.fn().mockReturnValue(languageCode.EN_US),
    } as unknown as Context;
  });

  describe('getAction', () => {
    test('should get an action and return a response with status 200', async () => {
      const mockActionResponse: ActionDetail = {
        actionId: '12345',
        title: 'Sample Action',
        coverImageUrl: 'https://example.com/cover.jpg',
        description: 'This is a sample action description.',
        actionType: ActionType.ACHIEVEMENT,
        actionLabel: 'SampleLabel',
        availableStartDate: new Date('2100-01-01T00:00:00Z').toISOString(),
        availableEndDate: new Date('2100-01-01T00:00:00Z').toISOString(),
      };

      actionService.getAction.mockResolvedValue(mockActionResponse);
      (context.req.header as jest.Mock).mockReturnValue('mockServiceId');
      (context.req.param as jest.Mock).mockReturnValue('12345');

      await actionController.getAction(context);

      expect(actionService.getAction).toHaveBeenCalledWith('12345', 'mockServiceId', languageCode.EN_US);
      expect(context.json).toHaveBeenCalledWith(mockActionResponse, 200);
    });

    test('should throw an error if action retrieval fails', async () => {
      const mockError = new Error('Failed to get action');
      actionService.getAction.mockRejectedValue(mockError);

      (context.req.header as jest.Mock).mockReturnValue('mockServiceId');
      (context.req.param as jest.Mock).mockReturnValue('12345');

      await expect(actionController.getAction(context)).rejects.toThrow('Failed to get action');
    });
  });
});
