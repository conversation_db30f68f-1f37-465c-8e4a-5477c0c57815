import { Context } from 'hono';
import { VaultKeyController } from '../../src/controllers/vaultKeyController';
import { VaultKeyService } from '../../src/services/vaultKeyService';
import { VaultKeyRepository } from '../../src/repositories/vaultKeyRepository';
import { WebhookService } from '../../src/services/webhookService';
jest.mock('../../src/services/vaultKeyService');

describe('VaultKeyController', () => {
  let vaultKeyController: VaultKeyController;
  let mockVaultKeyService: jest.Mocked<VaultKeyService>;
	let mockVaultKeyRepository: jest.Mocked<VaultKeyRepository>;
  let mockWebhookService: jest.Mocked<WebhookService>;
  beforeEach(() => {
    process.env.JSON_RPC_URL = 'http://127.0.0.1:8545';
    process.env.ALCHEMY_API_KEY = 'xxxxxsss';
    process.env.ALCHEMY_CHAIN_NAME = 'polygon-amoy';
    process.env.GAS_LIMIT_MULTIPLIER = '200';
    process.env.BASE_MAX_FEE_PER_GAS = '600';
    process.env.MAX_FEE_PER_GAS_MULTIPLIER = '150';
    process.env.MAX_PRIORITY_FEE_PER_GAS_MULTIPLIER = '150';
    process.env.GCP_PROJECT_ID = 'sample_gcp_project_id';
    process.env.GCS_BUCKET_NAME = 'mock-bucket-name'; // Added GCS_BUCKET_NAME
    process.env.ALCHEMY_ADDRESS_ACTIVITY_WEBHOOK_ID = 'mockAddressActivityWebhookId'; // Added
    process.env.ALCHEMY_NFT_ACTIVITY_WEBHOOK_ID = 'mockNftActivityWebhookId'; // Added
    process.env.ALCHEMY_ADDRESS_ACTIVITY_WEBHOOK_SIGNING_KEY = 'mockSigningKey'; // Added
    process.env.ALCHEMY_NFT_ACTIVITY_WEBHOOK_SIGNING_KEY = 'mockSigningKey'; // Added
    process.env.ALCHEMY_AUTH_TOKEN = 'mockAuthToken'; // Added

    mockVaultKeyRepository = new VaultKeyRepository() as jest.Mocked<VaultKeyRepository>;
    mockWebhookService = jest.fn() as unknown as jest.Mocked<WebhookService>;
    mockVaultKeyService = new VaultKeyService(
      mockVaultKeyRepository,
      mockWebhookService,
    ) as jest.Mocked<VaultKeyService>;
    vaultKeyController = new VaultKeyController(mockVaultKeyService);
    jest.clearAllMocks();
  });

  describe('createVaultKey() method', () => {
    test('should successfully create a vault key', async () => {
      const mockContext = {
        req: {
          header: jest.fn().mockReturnValue('tenant123'),
          get: jest.fn(),
        },
        json: jest.fn(),
      } as unknown as Context;

      const mockResponse = {
        keyRingProject: 'sample_project_id',
        keyRingLocation: 'global',
        keyRingName: 'sample_tenant_id',
        vaultKeyName: 'projects/sample_project_id/locations/global/keyRings/sample_tenant_id/cryptoKeys/sample_tenant_id',
        vaultWalletAddress: '0x123',
      };
      mockVaultKeyService.addVaultKey.mockResolvedValue(mockResponse);

      await vaultKeyController.createVaultKey(mockContext);

      expect(mockContext.json).toHaveBeenCalledWith(mockResponse, 200);
      expect(mockVaultKeyService.addVaultKey).toHaveBeenCalledWith('tenant123');
    });

    test('should return error response when service throws error', async () => {
      const mockContext = {
        req: {
          header: jest.fn().mockReturnValue('tenant123'),
          get: jest.fn(),
        },
        json: jest.fn(),
      } as unknown as Context;

      const error = new Error('Service Error');
      mockVaultKeyService.addVaultKey.mockRejectedValue(error);

      await vaultKeyController.createVaultKey(mockContext);

      expect(mockContext.json).toHaveBeenCalledWith({ error }, 500);
    });
  });

  describe('getVaultKey() method', () => {
    test('should successfully retrieve vault keys', async () => {
      const mockContext = {
        json: jest.fn(),
        get: jest.fn(),
      } as unknown as Context;

      const mockResponse = [
        {
          tenantName: 'Tenant A',
          vaultWalletAddress: '******************************************',
          chain: 'amoy',
          currentBalance: '2.0',
          consumptionData: {
            lastUpdatedAt: '2024-12-24T23:00:00Z',
            last1h: 0.1,
            last6h: 0.5,
            last12h: 1.0,
            last24h: 2.0,
          },
        },
      ];
      mockVaultKeyService.getVaultKeys.mockResolvedValue(mockResponse);

      await vaultKeyController.getVaultKey(mockContext);

      expect(mockContext.json).toHaveBeenCalledWith(mockResponse, 200);
      expect(mockVaultKeyService.getVaultKeys).toHaveBeenCalled();
    });

    test('should return error response when service throws error', async () => {
      const mockContext = {
        json: jest.fn(),
        get: jest.fn(),
      } as unknown as Context;

      const error = new Error('Service Error');
      mockVaultKeyService.getVaultKeys.mockRejectedValue(error);

      await vaultKeyController.getVaultKey(mockContext);

      expect(mockContext.json).toHaveBeenCalledWith({ error }, 500);
    });
  });

  // describe('transferToken() method', () => {
  //   test('should successfully transfer tokens', async () => {
  //     const mockContext = {
  //       req: {
  //         header: jest.fn().mockReturnValue('tenant123'),
  //         json: jest.fn().mockResolvedValue({ amount: 100, transferAll: false }),
  //       },
  //       json: jest.fn(),
  //     } as unknown as Context;

  //     const mockResponse: VaultTransferResponse = {
  //       success: true,
  //       transactionHash: '0x...',
  //       spentAmount: 100,
  //       remainingAmount: 900,
  //     };
  //     mockVaultKeyService.transferToken.mockResolvedValue(mockResponse);

  //     await vaultKeyController.transferToken(mockContext);

  //     expect(mockContext.json).toHaveBeenCalledWith(mockResponse, 200);
  //     expect(mockVaultKeyService.transferToken).toHaveBeenCalledWith('tenant123', 100, false);
  //   });
  // });
});
