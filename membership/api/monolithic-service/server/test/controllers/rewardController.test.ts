import { Context } from 'hono';
import { RewardController } from '../../src/controllers/rewardController';
import { RewardService } from '../../src/services/rewardService';
import { RewardRepository } from '../../src/repositories/rewardRepository';
import { AccountRepository } from '../../src/repositories/accountRepository';
import { NotFoundError } from '../../src/errors/notFoundError';
import { ClaimedRewardRepository } from '../../src/repositories/claimedRewardRepository';
import { ConflictError } from '../../src/errors/conflictError';
import { AchievementActionRepository } from '../../src/repositories/achievementActionRepository';
import { QuestActivityRepository } from '../../src/repositories/questActivityRepository';
import { NftMintService } from '../../src/services/nftMintService';
import { NftTag } from '../../src/enum/nftTag';
import { MetadataService } from '../../src/services/metadataService';
import { ServiceInfoRepository } from '../../src/repositories/serviceInfoRepository';
import { VaultKeyRepository } from '../../src/repositories/vaultKeyRepository';
import { NftContractsRepository } from '../../src/repositories/nftContractsRepository';
import { NftMetadatasRepository } from '../../src/repositories/nftMetadatasRepository';
import { NftBaseMetadatasRepository } from '../../src/repositories/nftBaseMetadatasRepository';
import { NftContractTypesRepository } from '../../src/repositories/nftContractTypesRepository';
import { QuestionnaireResultAnswerRepository } from '../../src/repositories/questionnaireResultAnswerRepository';
import { RewardDetail } from '../../src/dtos/services/schemas';
import { RewardAcquirementType } from '../../src/enum/rewardAcquirementType';
import { QuestRewardPriorityType } from '../../src/enum/questRewardPriorityType';
import { TransactionQueuesRepository } from '../../src/repositories/transactionQueuesRepository';
import { ClaimedReward } from '../../src/dtos/accounts/schemas';
import { NftType } from '../../src/enum/nftType';
import { DeliveryNftsFirestoreRepository } from '../../src/repositories/deliveryNftsFirestoreRepository';
import { FirebaseComponent } from '../../src/components/firebaseComponent';
import { StatusPointService } from '../../src/services/statusPointService';
import { RewardPointService } from '../../src/services/rewardPointService';
import { RedisComponent } from '../../src/components/redisComponent';
import { RewardPointTxsRepository } from '../../src/repositories/rewardPointTxsRepository';
import { StatusPointTxsRepository } from '../../src/repositories/statusPointTxsRepository';
import { RewardType } from '../../src/enum/rewardType';
import { parseCustomToken } from '../../src/utils/middleware/firebaseAuthAccessMiddleware';


jest.mock('../../src/utils/middleware/firebaseAuthAccessMiddleware', () => ({
  parseCustomToken: jest.fn(),
}));
jest.mock('../../src/services/rewardService', () => ({
  RewardService: jest.fn().mockImplementation(() => {
    return {
      getReward: jest.fn(),
    };
  }),
}));
jest.mock('../../src/repositories/questionnaireResultAnswerRepository');
jest.mock('../../src/components/redisComponent');

describe('RewardController', () => {
  let context: Context;
  let rewardController: RewardController;
  let mockRewardRepository: jest.Mocked<RewardRepository>;
  let mockAccountRepository: jest.Mocked<AccountRepository>;
  let mockClaimedRewardRepository: jest.Mocked<ClaimedRewardRepository>;
  let mockAchievementActionRepository: jest.Mocked<AchievementActionRepository>;
  let mockQuestActivityRepository: jest.Mocked<QuestActivityRepository>;
  let mockServiceInfoRepository: jest.Mocked<ServiceInfoRepository>;
  let mockVaultKeyRepository: jest.Mocked<VaultKeyRepository>;
  let mockNftContractsRepository: jest.Mocked<NftContractsRepository>;
  let mockNftMetadatasRepository: jest.Mocked<NftMetadatasRepository>;
  let mockNftBaseMetadatasRepository: jest.Mocked<NftBaseMetadatasRepository>;
  let mockNftContractTypesRepository: jest.Mocked<NftContractTypesRepository>;
  let mockQuestionnaireResultAnswerRepository: jest.Mocked<QuestionnaireResultAnswerRepository>;
  let rewardService: jest.Mocked<RewardService>;
  let mockNftMintService: jest.Mocked<NftMintService>;
  let mockMetadataService: jest.Mocked<MetadataService>;
  let mockTransactionQueuesRepository: jest.Mocked<TransactionQueuesRepository>;
  let mockDeliveryNftsFirestoreRepository: jest.Mocked<DeliveryNftsFirestoreRepository>;
  let mockRedisComponent: jest.Mocked<RedisComponent>;
  let mockRewardPointService: jest.Mocked<RewardPointService>;
  let mockStatusPointService: jest.Mocked<StatusPointService>;
  let mockRewardPointTxsRepository: jest.Mocked<RewardPointTxsRepository>;
  let mockStatusPointTxsRepository: jest.Mocked<StatusPointTxsRepository>;

  beforeEach(() => {
    process.env.JSON_RPC_URL = 'http://127.0.0.1:8545';
    process.env.ALCHEMY_API_KEY = 'xxxxxsss';
    process.env.ALCHEMY_CHAIN_NAME = 'polygon-amoy';
    process.env.GAS_LIMIT_MULTIPLIER = '200';
    process.env.BASE_MAX_FEE_PER_GAS = '600';
    process.env.MAX_FEE_PER_GAS_MULTIPLIER = '150';
    process.env.MAX_PRIORITY_FEE_PER_GAS_MULTIPLIER = '150';
    process.env.GCP_PROJECT_ID = 'sample_gcp_project_id';

    mockRewardRepository = new RewardRepository() as jest.Mocked<RewardRepository>;
    mockAccountRepository = new AccountRepository() as jest.Mocked<AccountRepository>;
    mockClaimedRewardRepository = new ClaimedRewardRepository() as jest.Mocked<ClaimedRewardRepository>;
    mockAchievementActionRepository = new AchievementActionRepository() as jest.Mocked<AchievementActionRepository>;
    mockQuestActivityRepository = new QuestActivityRepository() as jest.Mocked<QuestActivityRepository>;
    mockVaultKeyRepository = new VaultKeyRepository() as jest.Mocked<VaultKeyRepository>;
    mockServiceInfoRepository = new ServiceInfoRepository() as jest.Mocked<ServiceInfoRepository>;
    mockNftContractsRepository = new NftContractsRepository() as jest.Mocked<NftContractsRepository>;
    mockNftMetadatasRepository = new NftMetadatasRepository() as jest.Mocked<NftMetadatasRepository>;
    mockNftBaseMetadatasRepository = new NftBaseMetadatasRepository() as jest.Mocked<NftBaseMetadatasRepository>;
    mockNftContractTypesRepository = new NftContractTypesRepository() as jest.Mocked<NftContractTypesRepository>;
    mockTransactionQueuesRepository = new TransactionQueuesRepository() as jest.Mocked<TransactionQueuesRepository>;
    mockDeliveryNftsFirestoreRepository = new DeliveryNftsFirestoreRepository(
      {} as FirebaseComponent,
    ) as jest.Mocked<DeliveryNftsFirestoreRepository>;
    mockNftMintService = new NftMintService(
      mockNftContractsRepository,
      mockTransactionQueuesRepository,
      mockVaultKeyRepository,
      mockServiceInfoRepository,
      mockDeliveryNftsFirestoreRepository,
    ) as jest.Mocked<NftMintService>;
    mockMetadataService = new MetadataService(
      mockNftMetadatasRepository,
      mockNftBaseMetadatasRepository,
      mockNftContractTypesRepository,
    ) as jest.Mocked<MetadataService>;
    mockQuestionnaireResultAnswerRepository =
      new QuestionnaireResultAnswerRepository() as jest.Mocked<QuestionnaireResultAnswerRepository>;
    mockRedisComponent = new RedisComponent() as jest.Mocked<RedisComponent>;
    mockRewardPointTxsRepository = new RewardPointTxsRepository() as jest.Mocked<RewardPointTxsRepository>;
    mockStatusPointTxsRepository = new StatusPointTxsRepository() as jest.Mocked<StatusPointTxsRepository>;
    mockRewardPointService = new RewardPointService(
      mockRedisComponent,
      mockServiceInfoRepository,
      mockRewardPointTxsRepository,
    ) as jest.Mocked<RewardPointService>;
    mockStatusPointService = new StatusPointService(
      mockStatusPointTxsRepository,
      mockRedisComponent,
    ) as jest.Mocked<StatusPointService>;
    rewardService = new RewardService(
      mockRewardRepository,
      mockAccountRepository,
      mockClaimedRewardRepository,
      mockAchievementActionRepository,
      mockQuestActivityRepository,
      mockNftMintService,
      mockMetadataService,
      mockQuestionnaireResultAnswerRepository,
      mockRewardPointService,
      mockStatusPointService,
    ) as jest.Mocked<RewardService>;
    rewardService.getReward = jest.fn();
    rewardService.claimReward = jest.fn();

    rewardController = new RewardController(rewardService);

    context = {
      req: {
        header: jest.fn(),
        param: jest.fn(),
      },
      json: jest.fn(),
      get: jest.fn(),
    } as unknown as Context;
  });

  describe('getReward method', () => {
    const mockServiceId = 'test-service-id';
    (parseCustomToken as jest.Mock).mockReturnValue({
      service_id: mockServiceId,
      account_id: 'mockAccountId',
    });
    test('should return reward with status 200', async () => {
      const mockRewardId = 'reward123';
      const mockReward: RewardDetail = {
        rewardId: '8552df83-cbd5-44db-b67e-0cbeb2785918',
        title: 'Test Reward',
        coverImageUrl: 'https://marbullx.com/reward',
        description:
          'IyBIZWxsbyB3b3JsZCEKIyMgcG9saWN5IGRhdGEKVGhpcyBpcyB3aGVyZSB0aGUgdGV4dCBvZiB0aGUgcG9saWN5IGRhdGEgd2lsbCBiZSBsaXN0ZWQuCgotIGhpbnQgMQotIGhpbnQgMgoKW2ltYWdlXShodHRwczovL2ltYWdlLmNvbSkKCg==',
        acquirementType: RewardAcquirementType.DISTRIBUTION,
        rewardPriorityType: QuestRewardPriorityType.MAIN,
        nfts: [
          {
            nftContractId: '8552df83-cbd5-44db-b67e-0cbeb2785918',
            nftContractType: RewardType.CERTIFICATE,
          },
        ],
      };

      (context.req.header as jest.Mock).mockReturnValue(mockServiceId);
      (context.req.param as jest.Mock).mockReturnValue(mockRewardId);
      (context.get as jest.Mock).mockReturnValue('ja');
      rewardService.getReward.mockResolvedValue(mockReward);

      await rewardController.getReward(context);
      expect(context.req.param).toHaveBeenCalledWith('rewardId');
      expect(rewardService.getReward).toHaveBeenCalledWith(mockRewardId, mockServiceId, 'ja');
      expect(context.json).toHaveBeenCalledWith(mockReward, 200);
    });

    test('should handle errors gracefully', async () => {
      const mockServiceId = 'test-service-id';
      const mockRewardId = 'reward123';
      const mockError = new Error('Test error');

      (context.req.header as jest.Mock).mockReturnValue(mockServiceId);
      (context.req.param as jest.Mock).mockReturnValue(mockRewardId);
      rewardService.getReward.mockRejectedValue(mockError);

      await expect(rewardController.getReward(context)).rejects.toThrow('Test error');
    });
  });

  describe('claimReward() method', () => {
    const mockAccountId = 'account456';
    const mockServiceId = 'test-service-id';

    (parseCustomToken as jest.Mock).mockReturnValue({
      service_id: mockServiceId,
      account_id: mockAccountId,
    });
    test('should claim reward and return status 200', async () => {
      const mockRewardId = 'reward123';
      const mockClaimRewardResponse: ClaimedReward = {
        rewardId: mockRewardId,
        nfts: [
          {
            title: 'Claimed Reward',
            rewardType: NftTag.EXCHANGE_COUPON,
            coverImageUrl: 'https://example.com/claimed_reward.jpg',
          },
        ],
      };

      (context.req.header as jest.Mock).mockReturnValue(mockServiceId);
      (context.req.param as jest.Mock).mockImplementation((param) => {
        if (param === 'rewardId') return mockRewardId;
        if (param === 'accountId') return mockAccountId;
      });
      rewardService.claimReward.mockResolvedValue(mockClaimRewardResponse);

      await rewardController.claimReward(context);

      expect(context.req.param).toHaveBeenCalledWith('rewardId');
      expect(rewardService.claimReward).toHaveBeenCalledWith(mockAccountId, mockRewardId, mockServiceId);
      expect(context.json).toHaveBeenCalledWith(mockClaimRewardResponse, 200);
    });

    test('should handle NotFoundError when account or reward is not found', async () => {
      const mockServiceId = 'test-service-id';
      const mockRewardId = 'reward123';
      const mockAccountId = 'account456';

      (context.req.header as jest.Mock).mockReturnValue(mockServiceId);
      (context.req.param as jest.Mock).mockImplementation((param) => {
        if (param === 'rewardId') return mockRewardId;
        if (param === 'accountId') return mockAccountId;
      });
      rewardService.claimReward.mockRejectedValue(new NotFoundError('Not found'));

      await expect(rewardController.claimReward(context)).rejects.toThrow(NotFoundError);
    });

    test('should handle ConflictError when reward is already claimed', async () => {
      const mockServiceId = 'test-service-id';
      const mockRewardId = 'reward123';
      const mockAccountId = 'account456';

      (context.req.header as jest.Mock).mockReturnValue(mockServiceId);
      (context.req.param as jest.Mock).mockImplementation((param) => {
        if (param === 'rewardId') return mockRewardId;
        if (param === 'accountId') return mockAccountId;
      });
      rewardService.claimReward.mockRejectedValue(new ConflictError('Reward has already been claimed.'));

      await expect(rewardController.claimReward(context)).rejects.toThrow(ConflictError);
    });
  });
});
