import { Context } from 'hono';
import { PointController } from '../../src/controllers/pointController';
import { RewardPointService } from '../../src/services/rewardPointService';
import { RedisComponent } from '../../src/components/redisComponent';
import { ServiceInfoRepository } from '../../src/repositories/serviceInfoRepository';
import { RewardPointTxsRepository } from '../../src/repositories/rewardPointTxsRepository';
import { ExpireRewardPointsResponse } from '../../src/dtos/points/schemas';
import { StatusPointService } from '../../src/services/statusPointService';
import { StatusPointTxsRepository } from '../../src/repositories/statusPointTxsRepository';
import { OperationStatus } from '../../src/enum/operationStatus';

jest.mock('../../src/services/rewardPointService');

describe('PointController', () => {
  let pointController: PointController;
  let mockRewardPointService: jest.Mocked<RewardPointService>;
  let mockRedisComponent: jest.Mocked<RedisComponent>;
  let mockServiceInfoRepository: jest.Mocked<ServiceInfoRepository>;
  let mockRewardPointTxsRepository: jest.Mocked<RewardPointTxsRepository>;
  let mockStatusPointService: jest.Mocked<StatusPointService>;
  let mockStatusPointTxsRepository: jest.Mocked<StatusPointTxsRepository>;

  beforeEach(() => {
    mockRedisComponent = {} as unknown as jest.Mocked<RedisComponent>;
    mockServiceInfoRepository = new ServiceInfoRepository() as jest.Mocked<ServiceInfoRepository>;
    mockRewardPointTxsRepository = new RewardPointTxsRepository() as jest.Mocked<RewardPointTxsRepository>;
    mockStatusPointTxsRepository = new StatusPointTxsRepository() as jest.Mocked<StatusPointTxsRepository>;
    mockRewardPointService = new RewardPointService(mockRedisComponent, mockServiceInfoRepository, mockRewardPointTxsRepository) as jest.Mocked<RewardPointService>;
    mockStatusPointService = new StatusPointService(mockStatusPointTxsRepository, mockRedisComponent) as jest.Mocked<StatusPointService>;
    pointController = new PointController(mockRewardPointService, mockStatusPointService);
    jest.clearAllMocks();
  });

  describe('expireRewardPoints', () => {
    test('should return 200 and expire reward points', async () => {
      const mockContext = {
        json: jest.fn(),
      } as unknown as Context;

      const mockResponse: ExpireRewardPointsResponse = {
        status: OperationStatus.SUCCESS,
        startedAt: new Date('2025-01-02T00:00:00Z').toISOString(),
        endedAt: new Date('2025-01-02T00:00:00Z').toISOString(),
        processedCount: 5,
        failedAccounts: [],
      };
      mockRewardPointService.expireRewardPoints.mockResolvedValue(mockResponse);
      await pointController.expireRewardPoints(mockContext);
      expect(mockRewardPointService.expireRewardPoints).toHaveBeenCalled();
      expect(mockContext.json).toHaveBeenCalledWith(mockResponse, 200);
    });
  });
});
