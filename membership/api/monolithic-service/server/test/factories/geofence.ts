import { faker } from '@faker-js/faker';
import { GeofencesTableEntity } from '../../src/tables/geofencesTable';

export class GeofenceFactory {
  static create(overrides: Partial<GeofencesTableEntity> = {}): GeofencesTableEntity {
    return {
      geofence_id: faker.string.uuid(),
      service_id: faker.string.uuid(),
      geofence_slug: faker.location.city(),
      geofence_type: 'CIRCLE',
      circle_radius: '50',
      circle_geometry: {
        type: 'Point',
        coordinates: [
          faker.location.longitude(),
          faker.location.latitude()
        ]
      },
      polygon_geometry: undefined,
      ...overrides,
    };
  }

  static createPolygon(overrides: Partial<GeofencesTableEntity> = {}): GeofencesTableEntity {
    const lat = faker.location.latitude();
    const lng = faker.location.longitude();
    
    return {
      geofence_id: faker.string.uuid(),
      service_id: faker.string.uuid(),
      geofence_slug: faker.location.city(),
      geofence_type: 'POLYGON',
      circle_radius: undefined,
      circle_geometry: undefined,
      polygon_geometry: {
        type: 'Polygon',
        coordinates: [[
          [lng, lat],
          [lng + 0.001, lat],
          [lng + 0.001, lat + 0.001],
          [lng, lat + 0.001],
          [lng, lat] // Close the polygon
        ]]
      },
      ...overrides,
    };
  }
}
