import { faker } from '@faker-js/faker';
import { LocationCheckinActionEntity } from '../../src/tables/locationCheckinActionTable';

export class LocationCheckinActionFactory {
  static create(overrides: Partial<LocationCheckinActionEntity> = {}): LocationCheckinActionEntity {
    return {
      action_id: faker.string.uuid(),
      service_id: faker.string.uuid(),
      geofence_id: faker.string.uuid(),
      ...overrides,
    };
  }
}
