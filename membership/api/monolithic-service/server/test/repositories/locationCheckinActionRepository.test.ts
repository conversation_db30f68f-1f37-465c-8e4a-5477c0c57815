// locationCheckinActionRepository.test.ts
import { LocationCheckinActionRepository } from '../../src/repositories/locationCheckinActionRepository';
import { db } from '../../src/db/database';
import { LocationCheckinActionsTable, InsertableLocationCheckinActionRow } from '../../src/tables/locationCheckinActionTable';

describe('LocationCheckinActionRepository', () => {
  let locationCheckinActionRepository: LocationCheckinActionRepository;

  beforeAll(() => {
    locationCheckinActionRepository = new LocationCheckinActionRepository();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getLocationCheckinActionByActionId', () => {
    test('should return the location checkin action for the given actionId and serviceId', async () => {
      const mockActionId = 'action-123';
      const mockServiceId = 'service-123';
      const mockLocationCheckinAction: LocationCheckinActionsTable = {
        action_id: 'action-123',
        service_id: 'service-123',
        geofence_id: 'geofence-001',
      };

      // Mocking the db selectFrom flow
      jest.spyOn(locationCheckinActionRepository, 'setServiceId').mockResolvedValue();
      const mockWhere = jest.fn().mockReturnThis();
      const mockSelectAll = jest.fn().mockReturnThis();
      const mockExecuteTakeFirst = jest.fn().mockResolvedValue(mockLocationCheckinAction);

      jest.spyOn(db, 'selectFrom').mockReturnValue({
        where: mockWhere,
        selectAll: mockSelectAll,
        executeTakeFirst: mockExecuteTakeFirst,
      } as any);

      const result = await locationCheckinActionRepository.getLocationCheckinActionByActionId(mockActionId, mockServiceId);

      expect(result).toEqual(mockLocationCheckinAction);
      expect(locationCheckinActionRepository.setServiceId).toHaveBeenCalledWith(mockServiceId);
      expect(db.selectFrom).toHaveBeenCalledWith('location_checkin_actions');
      expect(mockWhere).toHaveBeenCalledWith('action_id', '=', mockActionId);
      expect(mockExecuteTakeFirst).toHaveBeenCalled();
    });

    test('should return undefined if no location checkin action is found for the given actionId and serviceId', async () => {
      const mockActionId = 'non-existent-action-id';
      const mockServiceId = 'service-123';

      // Mocking setServiceId and the query flow for no result
      jest.spyOn(locationCheckinActionRepository, 'setServiceId').mockResolvedValue();
      const mockWhere = jest.fn().mockReturnThis();
      const mockSelectAll = jest.fn().mockReturnThis();
      const mockExecuteTakeFirst = jest.fn().mockResolvedValue(undefined);

      jest.spyOn(db, 'selectFrom').mockReturnValue({
        where: mockWhere,
        selectAll: mockSelectAll,
        executeTakeFirst: mockExecuteTakeFirst,
      } as any);

      const result = await locationCheckinActionRepository.getLocationCheckinActionByActionId(mockActionId, mockServiceId);

      expect(result).toBeUndefined();
      expect(locationCheckinActionRepository.setServiceId).toHaveBeenCalledWith(mockServiceId);
      expect(db.selectFrom).toHaveBeenCalledWith('location_checkin_actions');
      expect(mockWhere).toHaveBeenCalledWith('action_id', '=', mockActionId);
      expect(mockExecuteTakeFirst).toHaveBeenCalled();
    });
  });

  describe('insertLocationCheckinAction', () => {
    test('should insert a new location checkin action successfully', async () => {
      const mockServiceId = 'service-123';
      const mockLocationCheckinActionData: InsertableLocationCheckinActionRow = {
        action_id: 'action-123',
        service_id: 'service-123',
        geofence_id: 'geofence-001',
      };

      const mockInsertedAction: LocationCheckinActionsTable = {
        ...mockLocationCheckinActionData,
      };

      // Mocking the db insertInto flow
      jest.spyOn(locationCheckinActionRepository, 'setServiceId').mockResolvedValue();
      const mockValues = jest.fn().mockReturnThis();
      const mockReturningAll = jest.fn().mockReturnThis();
      const mockExecuteTakeFirstOrThrow = jest.fn().mockResolvedValue(mockInsertedAction);

      jest.spyOn(db, 'insertInto').mockReturnValue({
        values: mockValues,
        returningAll: mockReturningAll,
        executeTakeFirstOrThrow: mockExecuteTakeFirstOrThrow,
      } as any);

      const result = await locationCheckinActionRepository.insertLocationCheckinAction(mockServiceId, mockLocationCheckinActionData);

      expect(result).toEqual(mockInsertedAction);
      expect(locationCheckinActionRepository.setServiceId).toHaveBeenCalledWith(mockServiceId);
      expect(db.insertInto).toHaveBeenCalledWith('location_checkin_actions');
      expect(mockValues).toHaveBeenCalledWith(mockLocationCheckinActionData);
      expect(mockExecuteTakeFirstOrThrow).toHaveBeenCalled();
    });

    test('should throw error when insertion fails', async () => {
      const mockServiceId = 'service-123';
      const mockLocationCheckinActionData: InsertableLocationCheckinActionRow = {
        action_id: 'action-123',
        service_id: 'service-123',
        geofence_id: 'geofence-001',
      };

      const mockError = new Error('Database insertion failed');

      jest.spyOn(locationCheckinActionRepository, 'setServiceId').mockResolvedValue();
      const mockValues = jest.fn().mockReturnThis();
      const mockReturningAll = jest.fn().mockReturnThis();
      const mockExecuteTakeFirstOrThrow = jest.fn().mockRejectedValue(mockError);

      jest.spyOn(db, 'insertInto').mockReturnValue({
        values: mockValues,
        returningAll: mockReturningAll,
        executeTakeFirstOrThrow: mockExecuteTakeFirstOrThrow,
      } as any);

      await expect(
        locationCheckinActionRepository.insertLocationCheckinAction(mockServiceId, mockLocationCheckinActionData)
      ).rejects.toThrow('Database insertion failed');

      expect(locationCheckinActionRepository.setServiceId).toHaveBeenCalledWith(mockServiceId);
      expect(db.insertInto).toHaveBeenCalledWith('location_checkin_actions');
      expect(mockValues).toHaveBeenCalledWith(mockLocationCheckinActionData);
      expect(mockExecuteTakeFirstOrThrow).toHaveBeenCalled();
    });
  });

  describe('getGeofenceIdByActionId', () => {
    test('should return the geofence ID for the given actionId and serviceId', async () => {
      const mockActionId = 'action-123';
      const mockServiceId = 'service-123';
      const mockGeofenceId = 'geofence-001';

      const mockLocationCheckinAction: LocationCheckinActionsTable = {
        action_id: 'action-123',
        service_id: 'service-123',
        geofence_id: 'geofence-001',
      };

      jest.spyOn(locationCheckinActionRepository, 'setServiceId').mockResolvedValue();
      const mockWhere = jest.fn().mockReturnThis();
      const mockSelect = jest.fn().mockReturnThis();
      const mockExecuteTakeFirst = jest.fn().mockResolvedValue(mockLocationCheckinAction);

      jest.spyOn(db, 'selectFrom').mockReturnValue({
        where: mockWhere,
        select: mockSelect,
        executeTakeFirst: mockExecuteTakeFirst,
      } as any);

      const result = await locationCheckinActionRepository.getGeofenceIdByActionId(mockActionId, mockServiceId);

      expect(result).toBe(mockGeofenceId);
      expect(locationCheckinActionRepository.setServiceId).toHaveBeenCalledWith(mockServiceId);
      expect(db.selectFrom).toHaveBeenCalledWith('location_checkin_actions');
      expect(mockWhere).toHaveBeenCalledWith('action_id', '=', mockActionId);
      expect(mockSelect).toHaveBeenCalledWith('geofence_id');
      expect(mockExecuteTakeFirst).toHaveBeenCalled();
    });

    test('should return null if no location checkin action is found', async () => {
      const mockActionId = 'non-existent-action-id';
      const mockServiceId = 'service-123';

      jest.spyOn(locationCheckinActionRepository, 'setServiceId').mockResolvedValue();
      const mockWhere = jest.fn().mockReturnThis();
      const mockSelect = jest.fn().mockReturnThis();
      const mockExecuteTakeFirst = jest.fn().mockResolvedValue(undefined);

      jest.spyOn(db, 'selectFrom').mockReturnValue({
        where: mockWhere,
        select: mockSelect,
        executeTakeFirst: mockExecuteTakeFirst,
      } as any);

      const result = await locationCheckinActionRepository.getGeofenceIdByActionId(mockActionId, mockServiceId);

      expect(result).toBeNull();
      expect(locationCheckinActionRepository.setServiceId).toHaveBeenCalledWith(mockServiceId);
      expect(db.selectFrom).toHaveBeenCalledWith('location_checkin_actions');
      expect(mockWhere).toHaveBeenCalledWith('action_id', '=', mockActionId);
      expect(mockSelect).toHaveBeenCalledWith('geofence_id');
      expect(mockExecuteTakeFirst).toHaveBeenCalled();
    });
  });
});
