import { LocationCheckinActionRepository } from '../../src/repositories/locationCheckinActionRepository';
import { LocationCheckinActionEntity } from '../../src/tables/locationCheckinActionTable';
import { LocationCheckinActionFactory } from '../factories/locationCheckinAction';

describe('LocationCheckinActionRepository', () => {
  let repo: LocationCheckinActionRepository;
  const serviceId = 'test-service-id';

  let baseLocationCheckinAction: LocationCheckinActionEntity;

  beforeEach(async () => {
    repo = new LocationCheckinActionRepository();

    baseLocationCheckinAction = LocationCheckinActionFactory.create({
      service_id: serviceId,
    });
  });

  afterAll(() => {
    jest.restoreAllMocks();
  });

  describe('insert & select methods', () => {
    it('insertLocationCheckinAction should insert and return full entity', async () => {
      const inserted = await repo.insertLocationCheckinAction(serviceId, baseLocationCheckinAction);
      expect(inserted.action_id).toBe(baseLocationCheckinAction.action_id);
      expect(inserted.service_id).toBe(baseLocationCheckinAction.service_id);
      expect(inserted.geofence_id).toBe(baseLocationCheckinAction.geofence_id);
    });

    it('getLocationCheckinAction returns existing action', async () => {
      await repo.insertLocationCheckinAction(serviceId, baseLocationCheckinAction);
      const action = await repo.getLocationCheckinAction(baseLocationCheckinAction.action_id, serviceId);
      expect(action).toBeDefined();
      expect(action!.action_id).toBe(baseLocationCheckinAction.action_id);
      expect(action!.geofence_id).toBe(baseLocationCheckinAction.geofence_id);
    });

    it('getLocationCheckinAction returns undefined when not found', async () => {
      const action = await repo.getLocationCheckinAction('non-existent-action', serviceId);
      expect(action).toBeUndefined();
    });

    it('getGeofenceIdByActionId returns geofence ID for existing action', async () => {
      await repo.insertLocationCheckinAction(serviceId, baseLocationCheckinAction);
      const geofenceId = await repo.getGeofenceIdByActionId(baseLocationCheckinAction.action_id, serviceId);
      expect(geofenceId).toBe(baseLocationCheckinAction.geofence_id);
    });

    it('getGeofenceIdByActionId returns undefined when action not found', async () => {
      const geofenceId = await repo.getGeofenceIdByActionId('non-existent-action', serviceId);
      expect(geofenceId).toBeUndefined();
    });
  });

});
