
import { FirebaseComponent } from '../../src/components/firebaseComponent';
import { NftsFirestoreRepository, FirestoreNftsDocument } from '../../src/repositories/nftsFirestoreRepository';

import { DocumentData } from 'firebase-admin/firestore';
import { TransferTxType } from '../../src/enum/transferTxType';
import { NftType } from '../../src/enum/nftType';
import type FirebaseFirestore from '@google-cloud/firestore';

describe('NftsFirestoreRepository (using Firestore Emulator)', () => {
  let firebaseComponent: FirebaseComponent;
  let nftsFirestoreRepository: NftsFirestoreRepository;

  beforeAll(async () => {
    firebaseComponent = new FirebaseComponent();
    nftsFirestoreRepository = new NftsFirestoreRepository(firebaseComponent);
    await clearCollection();
  });

  afterAll(async () => {
    await clearCollection();
  });

  afterEach(async () => {
    await clearCollection();
  });

  describe('selectNfts', () => {
    test('should return NFTs matching the contract address and token ID', async () => {
      const nftData: FirestoreNftsDocument = {
        chainId: '137',
        contractAddress: '0xABC',
        tokenId: '123',
        amount: 1,
        accountId: 'account123',
        serviceId: 'service123',
        contractType: NftType.CONTENT,
        metadataJson: { name: 'Sample NFT', description: 'This is a sample NFT' },
        metadataUri: 'https://example.com/metadata/123',
        metadataCachedAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        createdAt: new Date().toISOString(),
        transactions: {
          'txHash123': {
            from: '0x0000000000000000000000000000000000000000',
            to: '0x70997970c51812dc3a010c7d01b50e0d17dc79c8',
            transactionDate: new Date().toISOString(),
            transactionType: TransferTxType.MINT,
            isConfirmed: true,
          },
        },
      };

      await nftsFirestoreRepository.insertNft(nftData);

      const snapshot = await nftsFirestoreRepository.selectNfts(nftData.contractAddress, nftData.tokenId);

      expect(snapshot.empty).toBe(false);
      const selectedDoc = snapshot.docs[0].data();
      expect(selectedDoc.contract_address).toBe(nftData.contractAddress);
      expect(selectedDoc.token_id).toBe(nftData.tokenId);
    });
  });

  describe('selectNftByAccountId', () => {
    test('should return NFTs matching the account ID, contract address, and token ID', async () => {
      const nftData = {
        chainId: '137',
        contractAddress: '0xDEF',
        tokenId: '456',
        amount: 1,
        accountId: 'account456',
        serviceId: 'service456',
        contractType: NftType.CONTENT,
        metadataJson: { name: 'Test NFT', description: 'A test NFT' },
        metadataUri: 'https://example.com/metadata/456',
        metadataCachedAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        createdAt: new Date().toISOString(),
        transactions: {
          'txHash456': {
            from: '0x0000000000000000000000000000000000000000',
            to: '0x70997970c51812dc3a010c7d01b50e0d17dc79c8',
            transactionDate: new Date().toISOString(),
            transactionType: TransferTxType.MINT,
            isConfirmed: true
          }
        }
      };

      await nftsFirestoreRepository.insertNft(nftData);

      const snapshot = await nftsFirestoreRepository.selectNftByAccountId(nftData.accountId, nftData.contractAddress, nftData.tokenId);

      expect(snapshot.empty).toBe(false);
      const selectedDoc = snapshot.docs[0].data();
      expect(selectedDoc.account_id).toBe(nftData.accountId);
      expect(selectedDoc.contract_address).toBe(nftData.contractAddress);
      expect(selectedDoc.token_id).toBe(nftData.tokenId);
    });
  });

  describe('selectNftsByTxHash', () => {
    test('should return NFT documents that contain the specified transaction hash in their transactions', async () => {
      const txHash = 'txHash999';

      const nftDataIsNotConfirmed = {
        chainId: '137',
        contractAddress: '0xAAA',
        tokenId: '999',
        amount: 1,
        accountId: 'account999',
        serviceId: 'service999',
        contractType: NftType.CONTENT,
        metadataJson: { name: 'NFT with txHash999' },
        metadataUri: 'https://example.com/metadata/999',
        metadataCachedAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        createdAt: new Date().toISOString(),
        transactions: {
          [txHash]: {
            from: '0x0000000000000000000000000000000000000000',
            to: '0x70997970c51812dc3a010c7d01b50e0d17dc79c8',
            transactionDate: new Date().toISOString(),
            transactionType: TransferTxType.MINT,
            isConfirmed: false,
            exists: true,
          },
        },
      };

      const nftDataIsConfirmed = {
        chainId: '137',
        contractAddress: '0xBBB',
        tokenId: '555',
        amount: 1,
        accountId: 'account555',
        serviceId: 'service555',
        contractType: NftType.CONTENT,
        metadataJson: { name: 'NFT without txHash999' },
        metadataUri: 'https://example.com/metadata/555',
        metadataCachedAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        createdAt: new Date().toISOString(),
        transactions: {
          'txHashAAA': {
            from: '0x0000000000000000000000000000000000000000',
            to: '0x123456789abcdef',
            transactionDate: new Date().toISOString(),
            transactionType: TransferTxType.MINT,
            isConfirmed: true,
            exists: true,
          },
        },
      };

      await nftsFirestoreRepository.insertNft(nftDataIsNotConfirmed);
      await nftsFirestoreRepository.insertNft(nftDataIsConfirmed);

      const snapshot = await nftsFirestoreRepository.selectNftsByTxHash(txHash);

      expect(snapshot.empty).toBe(false);
      expect(snapshot.docs).toHaveLength(1);

      const returnedDoc = snapshot.docs[0].data();
      expect(returnedDoc.contract_address).toBe(nftDataIsNotConfirmed.contractAddress);
      expect(returnedDoc.token_id).toBe(nftDataIsNotConfirmed.tokenId);
      expect(returnedDoc.transactions[txHash]).toBeDefined();
    });

    test('should return an empty snapshot if no NFT documents contain the specified transaction hash', async () => {
      const txHash = 'nonExistentTxHash';

      const nftData = {
        chainId: '137',
        contractAddress: '0xCCC',
        tokenId: '222',
        amount: 1,
        accountId: 'account222',
        serviceId: 'service222',
        contractType: NftType.CONTENT,
        metadataJson: { name: 'NFT without nonExistentTxHash' },
        metadataUri: 'https://example.com/metadata/222',
        metadataCachedAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        createdAt: new Date().toISOString(),
        transactions: {
          'txHashXYZ': {
            from: '0x0000000000000000000000000000000000000000',
            to: '0xABCDEFabcdef12345',
            transactionDate: new Date().toISOString(),
            transactionType: TransferTxType.MINT,
            isConfirmed: true,
            exists: true,
          },
        },
      };

      await nftsFirestoreRepository.insertNft(nftData);
      const snapshot = await nftsFirestoreRepository.selectNftsByTxHash(txHash);
      expect(snapshot.empty).toBe(true);
      expect(snapshot.docs).toHaveLength(0);
    });
  });


  describe('insertNft', () => {
    test('should create a new NFT document in Firestore', async () => {
      const nftData = {
        chainId: '137',
        contractAddress: '0xDEF',
        tokenId: '456',
        amount: 1,
        accountId: 'account456',
        serviceId: 'service456',
        contractType: NftType.CONTENT,
        metadataJson: { name: 'Test NFT', description: 'A test NFT' },
        metadataUri: 'https://example.com/metadata/456',
        metadataCachedAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        createdAt: new Date().toISOString(),
        transactions: {
          'txHash123': {
            from: '0x0000000000000000000000000000000000000000',
            to: '0x70997970c51812dc3a010c7d01b50e0d17dc79c8',
            transactionDate: new Date().toISOString(),
            transactionType: TransferTxType.MINT,
            isConfirmed: true
          }
        }
      };

      await nftsFirestoreRepository.insertNft(nftData);

      const db = firebaseComponent.getFirestoreInstance();
      const snapshot = await db.collection('nfts')
        .where('contract_address', '==', nftData.contractAddress)
        .where('token_id', '==', nftData.tokenId)
        .get();

      expect(snapshot.empty).toBe(false);
      const createdDoc = snapshot.docs[0].data();
      expect(createdDoc.contract_address).toBe(nftData.contractAddress);
      expect(createdDoc.token_id).toBe(nftData.tokenId);
      expect(createdDoc.amount).toBe(nftData.amount);
      expect(createdDoc.account_id).toBe(nftData.accountId);
      expect(createdDoc.service_id).toBe(nftData.serviceId);
      expect(createdDoc.contract_type).toBe(nftData.contractType);
      expect(createdDoc.metadata_json).toEqual(nftData.metadataJson);
      expect(createdDoc.metadata_uri).toBe(nftData.metadataUri);
      expect(createdDoc.transactions).toEqual(nftData.transactions);
    });
  });

  describe('deleteNft', () => {
    const nftData = {
      chainId: '137',
      contractAddress: '0xDEF',
      tokenId: '456',
      amount: 1,
      accountId: 'account456',
      serviceId: 'service456',
      contractType: NftType.CONTENT,
      metadataJson: { name: 'Test NFT', description: 'A test NFT' },
      metadataUri: 'https://example.com/metadata/456',
      metadataCachedAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      createdAt: new Date().toISOString(),
      transactions: {
        'txHash123': {
          from: '0x0000000000000000000000000000000000000000',
          to: '0x70997970c51812dc3a010c7d01b50e0d17dc79c8',
          transactionDate: new Date().toISOString(),
          transactionType: TransferTxType.MINT,
          isConfirmed: true
        }
      }
    };

    beforeEach(async () => {
      await nftsFirestoreRepository.insertNft(nftData);
      const db = firebaseComponent.getFirestoreInstance();
      const snapshot = await db.collection('nfts')
        .where('contract_address', '==', nftData.contractAddress)
        .where('token_id', '==', nftData.tokenId)
        .get();

      expect(snapshot.docs).toHaveLength(1);
    });

    test('should delete NFT document in Firestore', async () => {
      const db = firebaseComponent.getFirestoreInstance();
      const snapshot = await db.collection('nfts')
        .where('contract_address', '==', nftData.contractAddress)
        .where('token_id', '==', nftData.tokenId)
        .get();

      expect(snapshot.empty).toBe(false);
      const createdDoc = snapshot.docs[0].data();
      expect(createdDoc.contract_address).toBe(nftData.contractAddress);
      expect(createdDoc.token_id).toBe(nftData.tokenId);

      await nftsFirestoreRepository.deleteNft(nftData.contractAddress, nftData.tokenId);

      const deletedSnapshot = await db.collection('nfts')
        .where('contract_address', '==', nftData.contractAddress)
        .where('token_id', '==', nftData.tokenId)
        .get();

      expect(deletedSnapshot.empty).toBe(true);
    });

    test('should do nothing if NFT document not found', async () => {
      await nftsFirestoreRepository.deleteNft('0xNO_MATCH', '999');
      const db = firebaseComponent.getFirestoreInstance();
      const snapshot = await db.collection('nfts').get();
      expect(snapshot.empty).toBe(false);
      expect(snapshot.docs).toHaveLength(1);
    });
  });

  describe('updateNftsMetadata', () => {
    test('should update metadata for matching docs', async () => {
      const contractAddress = '0xABC';
      const tokenId = '123';
      const metadataUri = 'https://updated-uri';
      const metadataJson = { key: 'value' };

      const db = firebaseComponent.getFirestoreInstance();
      const docRef = db.collection('nfts').doc('doc-ref-abc123');
      await docRef.set({
        contract_address: contractAddress,
        token_id: tokenId
      });

      await nftsFirestoreRepository.updateNftsMetadata(contractAddress, { tokenId, metadataUri, metadataJson });

      const updatedDoc = await docRef.get();
      expect(updatedDoc.exists).toBe(true);
      const updatedData = updatedDoc.data() as DocumentData;
      expect(updatedData.metadata_uri).toBe(metadataUri);
      expect(updatedData.metadata_json).toEqual(metadataJson);
      expect(updatedData.metadata_cached_at).toBeDefined();
      expect(updatedData.updated_at).toBeDefined();
    });

    test('should do nothing if no docs found', async () => {
      await nftsFirestoreRepository.updateNftsMetadata('0xXYZ', { tokenId: '999', metadataUri: 'someUri', metadataJson: {} });
    });
  });

  describe('batchUpdateNftsMetadata', () => {
    test('updates matching token_id and returns the correct totalUpdated', async () => {
      const contractAddress = '0xABC';
      const db = firebaseComponent.getFirestoreInstance();

      await createDoc(db, 'doc-ref-100', {
        contract_address: contractAddress,
        token_id: '100',
        other: 'something',
      });
      await createDoc(db, 'doc-ref-200', {
        contract_address: contractAddress,
        token_id: '200',
        other: 'something',
      });
      await createDoc(db, 'doc-ref-999', {
        contract_address: contractAddress,
        token_id: '999',
        other: 'not-target',
      });

      const metadatas = [
        { tokenId: '100', metadataJson: { name: 'json-100' }, metadataUri: 'uri-100' },
        { tokenId: '200', metadataJson: { name: 'json-200' }, metadataUri: 'uri-200' },
        { tokenId: '300', metadataJson: { name: 'json-300' }, metadataUri: 'uri-300' },
      ];

      const result = await nftsFirestoreRepository.batchUpdateNftsMetadata(contractAddress, metadatas);

      const doc100 = await db.collection('nfts').doc('doc-ref-100').get();
      expect(doc100.data()).toMatchObject({
        token_id: '100',
        metadata_json: { name: 'json-100' },
        metadata_uri: 'uri-100',
      });

      const doc200 = await db.collection('nfts').doc('doc-ref-200').get();
      expect(doc200.data()).toMatchObject({
        token_id: '200',
        metadata_json: { name: 'json-200' },
        metadata_uri: 'uri-200',
      });

      const doc999 = await db.collection('nfts').doc('doc-ref-999').get();
      expect(doc999.data()?.metadata_uri).toBeUndefined();
      expect(result).toEqual({ totalUpdated: 2 });
    });

    test('returns totalUpdated=0 if no docs match the contractAddress', async () => {
      const contractAddress = '0xNO_MATCH';
      const metadatas = [{ tokenId: '111', metadataJson: {}, metadataUri: 'uri-111' }];
      const result = await nftsFirestoreRepository.batchUpdateNftsMetadata(contractAddress, metadatas);

      expect(result).toEqual({ totalUpdated: 0 });
    });

    test('retrieves all documents split across multiple pages when exceeding the maximum limit', async () => {
      const contractAddress = '0xABC';
      const db = firebaseComponent.getFirestoreInstance();

      for (let i = 0; i < 600; i++) {
        const token_id = i.toString();
        await createDoc(db, `doc-ref-${i}`, {
          contract_address: contractAddress,
          token_id,
          metadataJson: { neme: `old-json-${i}` }
        });
      }

      const metadatas = Array.from({ length: 556 }, (_, i) => ({
        tokenId: i.toString(),
        metadataJson: { name: `json-${i}` },
        metadataUri: `uri-${i}`,
      }));

      const result = await nftsFirestoreRepository.batchUpdateNftsMetadata(contractAddress, metadatas);

      const doc100 = await db.collection('nfts').doc('doc-ref-100').get();
      expect(doc100.data()).toMatchObject({
        token_id: '100',
        metadata_json: { name: 'json-100' },
        metadata_uri: 'uri-100',
      });

      const doc555 = await db.collection('nfts').doc('doc-ref-555').get();
      expect(doc555.data()).toMatchObject({
        token_id: '555',
        metadata_json: { name: 'json-555' },
        metadata_uri: 'uri-555',
      });

      expect(result).toEqual({ totalUpdated: 556 });
    }, 30000);
  });

  async function clearCollection() {
    const db = firebaseComponent.getFirestoreInstance();
    const snapshot = await db.collection('nfts').get();
    if (snapshot.empty) return;

    const batch = db.batch();
    snapshot.docs.forEach((doc) => batch.delete(doc.ref));
    await batch.commit();
  }

  async function createDoc(db: FirebaseFirestore.Firestore, docId: string, data: object) {
    return db.collection('nfts').doc(docId).set(data);
  }
});
