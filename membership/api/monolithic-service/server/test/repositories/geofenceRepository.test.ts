// geofenceRepository.test.ts
import { GeofenceRepository } from '../../src/repositories/geofenceRepository';
import { GeofencesTableEntity } from '../../src/tables/geofencesTable';
import { GeofenceFactory } from '../factories/geofence';
import { LocationCoordinates } from '../../src/dtos/locations/schemas';

describe('GeofenceRepository', () => {
  let repo: GeofenceRepository;
  const serviceId = 'test-service-id';

  let baseGeofence: GeofencesTableEntity;

  beforeEach(async () => {
    repo = new GeofenceRepository();

    baseGeofence = GeofenceFactory.create({
      service_id: serviceId,
    });
  });

  afterAll(() => {
    jest.restoreAllMocks();
  });

  describe('insert & select methods', () => {
    it('createGeofence should insert and return full entity', async () => {
      const inserted = await repo.createGeofence(serviceId, baseGeofence);
      expect(inserted.geofence_id).toBe(baseGeofence.geofence_id);
      expect(inserted.service_id).toBe(baseGeofence.service_id);
      expect(inserted.geofence_slug).toBe(baseGeofence.geofence_slug);
      expect(inserted.geofence_type).toBe(baseGeofence.geofence_type);
    });

    it('getGeofenceById returns existing geofence', async () => {
      await repo.createGeofence(serviceId, baseGeofence);
      const geofence = await repo.getGeofenceById(serviceId, baseGeofence.geofence_id);
      expect(geofence).toBeDefined();
      expect(geofence!.geofence_id).toBe(baseGeofence.geofence_id);
      expect(geofence!.geofence_slug).toBe(baseGeofence.geofence_slug);
    });

    it('getGeofenceById returns undefined when not found', async () => {
      const geofence = await repo.getGeofenceById(serviceId, 'non-existent-geofence');
      expect(geofence).toBeUndefined();
    });

    it('getGeofences returns all geofences for service', async () => {
      const geofence1 = GeofenceFactory.create({ service_id: serviceId });
      const geofence2 = GeofenceFactory.create({ service_id: serviceId });

      await repo.createGeofence(serviceId, geofence1);
      await repo.createGeofence(serviceId, geofence2);

      const geofences = await repo.getGeofences(serviceId);
      expect(geofences).toHaveLength(2);
      expect(geofences.map(g => g.geofence_id)).toContain(geofence1.geofence_id);
      expect(geofences.map(g => g.geofence_id)).toContain(geofence2.geofence_id);
    });

    it('getGeofences returns empty array when no geofences exist', async () => {
      const geofences = await repo.getGeofences(serviceId);
      expect(geofences).toHaveLength(0);
    });
  });

  describe('update methods', () => {
    beforeEach(async () => {
      await repo.createGeofence(serviceId, baseGeofence);
    });

    it('updateGeofence changes geofence properties', async () => {
      const updateData = {
        geofence_slug: 'Updated Location Name',
        circle_radius: '100',
      };

      await repo.updateGeofence(serviceId, baseGeofence.geofence_id, updateData);

      const row = await global.testDb
        .selectFrom('geofences')
        .select(['geofence_slug', 'circle_radius'])
        .where('geofence_id', '=', baseGeofence.geofence_id)
        .executeTakeFirst();

      expect(row!.geofence_slug).toBe('Updated Location Name');
      expect(row!.circle_radius).toBe('100');
    });
  });

  describe('spatial queries', () => {
    beforeEach(async () => {
      await repo.createGeofence(serviceId, baseGeofence);
    });

    it('isWithinGeofence returns true when location is within geofence', async () => {
      // Use the same coordinates as the geofence center
      const location: LocationCoordinates = {
        latitude: baseGeofence.circle_geometry!.coordinates[1],
        longitude: baseGeofence.circle_geometry!.coordinates[0],
      };

      const isWithin = await repo.isWithinGeofence(serviceId, baseGeofence.geofence_id, location);
      expect(isWithin).toBe(true);
    });

    it('isWithinGeofence returns false when location is outside geofence', async () => {
      // Use coordinates far from the geofence
      const location: LocationCoordinates = {
        latitude: 90.0, // North pole
        longitude: 0.0,
      };

      const isWithin = await repo.isWithinGeofence(serviceId, baseGeofence.geofence_id, location);
      expect(isWithin).toBe(false);
    });
  });
});
