// geofenceRepository.test.ts
import { GeofenceRepository } from '../../src/repositories/geofenceRepository';
import { db } from '../../src/db/database';
import { GeofencesTable, InsertableGeofencesTableRow } from '../../src/tables/geofencesTable';
import { LocationCoordinates } from '../../src/dtos/locations/schemas';

describe('GeofenceRepository', () => {
  let geofenceRepository: GeofenceRepository;

  beforeAll(() => {
    geofenceRepository = new GeofenceRepository();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getGeofenceById', () => {
    test('should return the geofence for the given geofenceId and serviceId', async () => {
      const mockServiceId = 'service-123';
      const mockGeofenceId = 'geofence-001';
      const mockGeofence: GeofencesTable = {
        geofence_id: 'geofence-001',
        service_id: 'service-123',
        center_pin_name: 'New York City',
        geofence_type: 'CIRCLE',
        circle_radius: '1000',
        circle_geometry: 'POINT(-74.0060 40.7128)',
        polygon_geometry: null,
      };

      // Mocking setServiceId and the query flow
      jest.spyOn(geofenceRepository, 'setServiceId').mockResolvedValue();
      const mockWhere = jest.fn().mockReturnThis();
      const mockSelectAll = jest.fn().mockReturnThis();
      const mockExecuteTakeFirst = jest.fn().mockResolvedValue(mockGeofence);

      jest.spyOn(db, 'selectFrom').mockReturnValue({
        where: mockWhere,
        selectAll: mockSelectAll,
        executeTakeFirst: mockExecuteTakeFirst,
      } as any); // Cast to 'any' for simplifying the mock type

      const result = await geofenceRepository.getGeofenceById(mockServiceId, mockGeofenceId);

      expect(result).toEqual(mockGeofence);
      expect(geofenceRepository.setServiceId).toHaveBeenCalledWith(mockServiceId);
      expect(db.selectFrom).toHaveBeenCalledWith('geofences');
      expect(mockWhere).toHaveBeenCalledWith('geofence_id', '=', mockGeofenceId);
      expect(mockExecuteTakeFirst).toHaveBeenCalled();
    });

    test('should return undefined if no geofence is found for the given geofenceId and serviceId', async () => {
      const mockServiceId = 'service-123';
      const mockGeofenceId = 'non-existent-geofence-id';

      // Mocking setServiceId and the query flow for no result
      jest.spyOn(geofenceRepository, 'setServiceId').mockResolvedValue();
      const mockWhere = jest.fn().mockReturnThis();
      const mockSelectAll = jest.fn().mockReturnThis();
      const mockExecuteTakeFirst = jest.fn().mockResolvedValue(undefined); // Simulating no result

      jest.spyOn(db, 'selectFrom').mockReturnValue({
        where: mockWhere,
        selectAll: mockSelectAll,
        executeTakeFirst: mockExecuteTakeFirst,
      } as any); // Cast to 'any' for simplifying the mock type

      const result = await geofenceRepository.getGeofenceById(mockServiceId, mockGeofenceId);

      expect(result).toBeUndefined();
      expect(geofenceRepository.setServiceId).toHaveBeenCalledWith(mockServiceId);
      expect(db.selectFrom).toHaveBeenCalledWith('geofences');
      expect(mockWhere).toHaveBeenCalledWith('geofence_id', '=', mockGeofenceId);
      expect(mockExecuteTakeFirst).toHaveBeenCalled();
    });
  });

  describe('createGeofence', () => {
    test('should create a new geofence successfully', async () => {
      const mockServiceId = 'service-123';
      const mockGeofenceData: InsertableGeofencesTableRow = {
        geofence_id: 'geofence-001',
        service_id: 'service-123',
        center_pin_name: 'Tokyo Station',
        geofence_type: 'CIRCLE',
        circle_radius: '50',
        circle_geometry: 'POINT(139.7671 35.6812)',
      };

      const mockCreatedGeofence: GeofencesTable = {
        ...mockGeofenceData,
        polygon_geometry: null,
      };

      jest.spyOn(geofenceRepository, 'setServiceId').mockResolvedValue();
      const mockValues = jest.fn().mockReturnThis();
      const mockReturningAll = jest.fn().mockReturnThis();
      const mockExecuteTakeFirstOrThrow = jest.fn().mockResolvedValue(mockCreatedGeofence);

      jest.spyOn(db, 'insertInto').mockReturnValue({
        values: mockValues,
        returningAll: mockReturningAll,
        executeTakeFirstOrThrow: mockExecuteTakeFirstOrThrow,
      } as any);

      const result = await geofenceRepository.createGeofence(mockServiceId, mockGeofenceData);

      expect(result).toEqual(mockCreatedGeofence);
      expect(geofenceRepository.setServiceId).toHaveBeenCalledWith(mockServiceId);
      expect(db.insertInto).toHaveBeenCalledWith('geofences');
      expect(mockValues).toHaveBeenCalledWith(mockGeofenceData);
      expect(mockExecuteTakeFirstOrThrow).toHaveBeenCalled();
    });
  });

  describe('updateGeofence', () => {
    test('should update an existing geofence successfully', async () => {
      const mockServiceId = 'service-123';
      const mockGeofenceId = 'geofence-001';
      const mockUpdateData = {
        center_pin_name: 'Updated Location Name',
        circle_radius: '100',
      };

      const mockUpdatedGeofence: GeofencesTable = {
        geofence_id: 'geofence-001',
        service_id: 'service-123',
        center_pin_name: 'Updated Location Name',
        geofence_type: 'CIRCLE',
        circle_radius: '100',
        circle_geometry: 'POINT(139.7671 35.6812)',
        polygon_geometry: null,
      };

      jest.spyOn(geofenceRepository, 'setServiceId').mockResolvedValue();
      const mockSet = jest.fn().mockReturnThis();
      const mockWhere = jest.fn().mockReturnThis();
      const mockReturningAll = jest.fn().mockReturnThis();
      const mockExecuteTakeFirstOrThrow = jest.fn().mockResolvedValue(mockUpdatedGeofence);

      jest.spyOn(db, 'updateTable').mockReturnValue({
        set: mockSet,
        where: mockWhere,
        returningAll: mockReturningAll,
        executeTakeFirstOrThrow: mockExecuteTakeFirstOrThrow,
      } as any);

      const result = await geofenceRepository.updateGeofence(mockServiceId, mockGeofenceId, mockUpdateData);

      expect(result).toEqual(mockUpdatedGeofence);
      expect(geofenceRepository.setServiceId).toHaveBeenCalledWith(mockServiceId);
      expect(db.updateTable).toHaveBeenCalledWith('geofences');
      expect(mockSet).toHaveBeenCalledWith(mockUpdateData);
      expect(mockWhere).toHaveBeenCalledWith('geofence_id', '=', mockGeofenceId);
      expect(mockExecuteTakeFirstOrThrow).toHaveBeenCalled();
    });
  });

  describe('getGeofences', () => {
    test('should return all geofences for a service', async () => {
      const mockServiceId = 'service-123';
      const mockGeofences: GeofencesTable[] = [
        {
          geofence_id: 'geofence-001',
          service_id: 'service-123',
          center_pin_name: 'Location 1',
          geofence_type: 'CIRCLE',
          circle_radius: '50',
          circle_geometry: 'POINT(139.7671 35.6812)',
          polygon_geometry: null,
        },
        {
          geofence_id: 'geofence-002',
          service_id: 'service-123',
          center_pin_name: 'Location 2',
          geofence_type: 'POLYGON',
          circle_radius: null,
          circle_geometry: null,
          polygon_geometry: 'POLYGON((139.7 35.6, 139.8 35.6, 139.8 35.7, 139.7 35.7, 139.7 35.6))',
        },
      ];

      jest.spyOn(geofenceRepository, 'setServiceId').mockResolvedValue();
      const mockSelectAll = jest.fn().mockReturnThis();
      const mockSelect = jest.fn().mockReturnThis();
      const mockExecute = jest.fn().mockResolvedValue(mockGeofences);

      jest.spyOn(db, 'selectFrom').mockReturnValue({
        selectAll: mockSelectAll,
        select: mockSelect,
        execute: mockExecute,
      } as any);

      const result = await geofenceRepository.getGeofences(mockServiceId);

      expect(result).toEqual(mockGeofences);
      expect(geofenceRepository.setServiceId).toHaveBeenCalledWith(mockServiceId);
      expect(db.selectFrom).toHaveBeenCalledWith('geofences');
      expect(mockExecute).toHaveBeenCalled();
    });
  });

  describe('isWithinGeofence', () => {
    test('should return true when location is within circle geofence', async () => {
      const mockServiceId = 'service-123';
      const mockGeofenceId = 'geofence-001';
      const mockLocation: LocationCoordinates = {
        latitude: 35.6812,
        longitude: 139.7671,
      };

      const mockGeofence: GeofencesTable = {
        geofence_id: 'geofence-001',
        service_id: 'service-123',
        center_pin_name: 'Tokyo Station',
        geofence_type: 'CIRCLE',
        circle_radius: '50',
        circle_geometry: 'POINT(139.7671 35.6812)',
        polygon_geometry: null,
      };

      jest.spyOn(geofenceRepository, 'setServiceId').mockResolvedValue();

      // Mock the first query to get geofence details
      const mockWhere1 = jest.fn().mockReturnThis();
      const mockSelectAll = jest.fn().mockReturnThis();
      const mockExecuteTakeFirst1 = jest.fn().mockResolvedValue(mockGeofence);

      // Mock the second query to check if within geofence
      const mockSelect = jest.fn().mockReturnThis();
      const mockWhere2 = jest.fn().mockReturnThis();
      const mockExecuteTakeFirst2 = jest.fn().mockResolvedValue({ is_within: true });

      jest.spyOn(db, 'selectFrom')
        .mockReturnValueOnce({
          selectAll: mockSelectAll,
          where: mockWhere1,
          executeTakeFirst: mockExecuteTakeFirst1,
        } as any)
        .mockReturnValueOnce({
          select: mockSelect,
          where: mockWhere2,
          executeTakeFirst: mockExecuteTakeFirst2,
        } as any);

      const result = await geofenceRepository.isWithinGeofence(mockServiceId, mockGeofenceId, mockLocation);

      expect(result).toBe(true);
      expect(geofenceRepository.setServiceId).toHaveBeenCalledWith(mockServiceId);
    });

    test('should return false when location is outside geofence', async () => {
      const mockServiceId = 'service-123';
      const mockGeofenceId = 'geofence-001';
      const mockLocation: LocationCoordinates = {
        latitude: 35.7000, // Far from the geofence
        longitude: 139.8000,
      };

      const mockGeofence: GeofencesTable = {
        geofence_id: 'geofence-001',
        service_id: 'service-123',
        center_pin_name: 'Tokyo Station',
        geofence_type: 'CIRCLE',
        circle_radius: '50',
        circle_geometry: 'POINT(139.7671 35.6812)',
        polygon_geometry: null,
      };

      jest.spyOn(geofenceRepository, 'setServiceId').mockResolvedValue();

      const mockWhere1 = jest.fn().mockReturnThis();
      const mockSelectAll = jest.fn().mockReturnThis();
      const mockExecuteTakeFirst1 = jest.fn().mockResolvedValue(mockGeofence);

      const mockSelect = jest.fn().mockReturnThis();
      const mockWhere2 = jest.fn().mockReturnThis();
      const mockExecuteTakeFirst2 = jest.fn().mockResolvedValue({ is_within: false });

      jest.spyOn(db, 'selectFrom')
        .mockReturnValueOnce({
          selectAll: mockSelectAll,
          where: mockWhere1,
          executeTakeFirst: mockExecuteTakeFirst1,
        } as any)
        .mockReturnValueOnce({
          select: mockSelect,
          where: mockWhere2,
          executeTakeFirst: mockExecuteTakeFirst2,
        } as any);

      const result = await geofenceRepository.isWithinGeofence(mockServiceId, mockGeofenceId, mockLocation);

      expect(result).toBe(false);
    });

    test('should return false when geofence is not found', async () => {
      const mockServiceId = 'service-123';
      const mockGeofenceId = 'non-existent-geofence';
      const mockLocation: LocationCoordinates = {
        latitude: 35.6812,
        longitude: 139.7671,
      };

      jest.spyOn(geofenceRepository, 'setServiceId').mockResolvedValue();

      const mockWhere = jest.fn().mockReturnThis();
      const mockSelectAll = jest.fn().mockReturnThis();
      const mockExecuteTakeFirst = jest.fn().mockResolvedValue(undefined);

      jest.spyOn(db, 'selectFrom').mockReturnValue({
        selectAll: mockSelectAll,
        where: mockWhere,
        executeTakeFirst: mockExecuteTakeFirst,
      } as any);

      const result = await geofenceRepository.isWithinGeofence(mockServiceId, mockGeofenceId, mockLocation);

      expect(result).toBe(false);
    });

    test('should return false when database error occurs', async () => {
      const mockServiceId = 'service-123';
      const mockGeofenceId = 'geofence-001';
      const mockLocation: LocationCoordinates = {
        latitude: 35.6812,
        longitude: 139.7671,
      };

      jest.spyOn(geofenceRepository, 'setServiceId').mockRejectedValue(new Error('Database error'));
      jest.spyOn(console, 'error').mockImplementation(() => { });

      const result = await geofenceRepository.isWithinGeofence(mockServiceId, mockGeofenceId, mockLocation);

      expect(result).toBe(false);
      expect(console.error).toHaveBeenCalledWith('Error checking geofence:', expect.any(Error));
    });
  });
});
