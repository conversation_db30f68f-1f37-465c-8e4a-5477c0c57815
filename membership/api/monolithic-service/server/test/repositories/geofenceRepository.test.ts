// geofenceRepository.test.ts
import { GeofenceRepository } from '../../src/repositories/geofenceRepository';
import { db } from '../../src/db/database';
import { GeofencesTable } from '../../src/tables/geofencesTable';

describe('GeofenceRepository', () => {
  let geofenceRepository: GeofenceRepository;

  beforeAll(() => {
    geofenceRepository = new GeofenceRepository();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('selectGeofenceById', () => {
    test('should return the geofence for the given geofenceId and serviceId', async () => {
      const mockServiceId = 'service-123';
      const mockGeofenceId = 'geofence-001';
      const mockGeofence: GeofencesTable = {
        geofence_id: 'geofence-001',
        service_id: 'service-123',
        center_coordinate_latitude: '40.7128',
        center_coordinate_longitude: '-74.0060',
        center_pin_name: 'New York City',
        geofence_radius: '1000', // Radius in meters
      };

      // Mocking setServiceId and the query flow
      jest.spyOn(geofenceRepository, 'setServiceId').mockResolvedValue();
      const mockWhere = jest.fn().mockReturnThis();
      const mockSelectAll = jest.fn().mockReturnThis();
      const mockExecuteTakeFirst = jest.fn().mockResolvedValue(mockGeofence);

      jest.spyOn(db, 'selectFrom').mockReturnValue({
        where: mockWhere,
        selectAll: mockSelectAll,
        executeTakeFirst: mockExecuteTakeFirst,
      } as any); // Cast to 'any' for simplifying the mock type

      const result = await geofenceRepository.selectGeofenceById(mockServiceId, mockGeofenceId);

      expect(result).toEqual(mockGeofence);
      expect(geofenceRepository.setServiceId).toHaveBeenCalledWith(mockServiceId);
      expect(db.selectFrom).toHaveBeenCalledWith('geofences');
      expect(mockWhere).toHaveBeenCalledWith('geofence_id', '=', mockGeofenceId);
      expect(mockExecuteTakeFirst).toHaveBeenCalled();
    });

    test('should return undefined if no geofence is found for the given geofenceId and serviceId', async () => {
      const mockServiceId = 'service-123';
      const mockGeofenceId = 'non-existent-geofence-id';

      // Mocking setServiceId and the query flow for no result
      jest.spyOn(geofenceRepository, 'setServiceId').mockResolvedValue();
      const mockWhere = jest.fn().mockReturnThis();
      const mockSelectAll = jest.fn().mockReturnThis();
      const mockExecuteTakeFirst = jest.fn().mockResolvedValue(undefined); // Simulating no result

      jest.spyOn(db, 'selectFrom').mockReturnValue({
        where: mockWhere,
        selectAll: mockSelectAll,
        executeTakeFirst: mockExecuteTakeFirst,
      } as any); // Cast to 'any' for simplifying the mock type

      const result = await geofenceRepository.selectGeofenceById(mockServiceId, mockGeofenceId);

      expect(result).toBeUndefined();
      expect(geofenceRepository.setServiceId).toHaveBeenCalledWith(mockServiceId);
      expect(db.selectFrom).toHaveBeenCalledWith('geofences');
      expect(mockWhere).toHaveBeenCalledWith('geofence_id', '=', mockGeofenceId);
      expect(mockExecuteTakeFirst).toHaveBeenCalled();
    });
  });
});
