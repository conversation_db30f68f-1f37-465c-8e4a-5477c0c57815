import { MetadataSyncBatchExecutor } from '../../src/batch/executor/metadataSyncBatchExecutor';
import { NotFoundError } from '../../src/errors/notFoundError';
import { NftContractsRepository } from '../../src/repositories/nftContractsRepository';
import { AccountRepository } from '../../src/repositories/accountRepository';
import * as retry from '../../src/utils/retry';
import { AlchemyTransfer, MetadataFetchService } from '../../src/services/metadataFetchService';
import { NftsFirestoreRepository } from '../../src/repositories/nftsFirestoreRepository';

const mockNfts = Array.from({ length: 250 }, (_, i) => ({
  tokenId: i.toString(),
  tokenUri: `https://example.com/api/coupon/${i + 1}.json`,
  raw: {
    tokenUri: `https://example.com/api/coupon/${i + 1}.json`,
  },
}));

const mockTokenIdAndOwnerBalances = mockNfts.reduce(
  (acc: Record<string, { ownerAddress: string; balance: number }[]>, nft) => {
    acc[nft.tokenId] = [{ ownerAddress: '0xOwnerAddress', balance: 1 }];
    return acc;
  },
  {},
);

const mockTransferTransaction: AlchemyTransfer = {
  blockNum: '0x14ccce1',
  uniqueId: '0x644e757ecb0cab8df44cd31419bbceadba25c404f2d147adf2187426a643bd44:log:465',
  hash: '0x2',
  from: '0x187b61bb2d393b090557e477c67cdd13b5b09b6a',
  to: '0x6b26eda810e5de599121e56e22667b19a2a64b9b',
  value: null,
  // 4724
  erc721TokenId: '0x0000000000000000000000000000000000000000000000000000000000001274',
  erc1155Metadata: null,
  tokenId: '0x0000000000000000000000000000000000000000000000000000000000001273',
  asset: 'BAYC',
  category: 'erc721',
  rawContract: {
    value: null,
    address: '0xbc4ca0eda7647a8ab7c2061c2e118a18a936f13d',
    decimal: null,
  },
  metadata: {
    blockTimestamp: '2021-05-09T00:29:20.000Z',
  },
};
process.env.ALCHEMY_CHAIN_NAME = 'polygon-amoy';
process.env.ALCHEMY_API_KEY = 'testApiKey';
process.env.JSON_RPC_URL = 'https://mockJsonRpcUrl';

jest.mock('../../src/utils/retry', () => ({
  retryExecution: jest.fn(),
}));


jest.spyOn(retry, 'retryExecution').mockImplementation((primaryFunction) => primaryFunction());

describe('MetadataSyncBatchExecutor', () => {
  let executor: MetadataSyncBatchExecutor;
  let nftContractsRepository: jest.Mocked<NftContractsRepository>;
  let accountRepository: jest.Mocked<AccountRepository>;
  let metadataFetchService: jest.Mocked<MetadataFetchService>;
  let nftsFirestoreRepository: jest.Mocked<NftsFirestoreRepository>;

  beforeEach(() => {
    nftContractsRepository = {
      selectNftContractByContractAddress: jest.fn().mockResolvedValue({ nft_type: 'CONTENT' }),
    } as unknown as jest.Mocked<NftContractsRepository>;
    accountRepository = {
      selectAccountByTokenBoundAddress: jest.fn(),
    } as unknown as jest.Mocked<AccountRepository>;
    metadataFetchService = {
      fetchTokenUri: jest.fn(),
      fetchMetadataFromTokenURI: jest.fn(),
      fetchNftTokens: jest.fn(),
      fetchTokenIdAndOwnerBalances: jest.fn(),
      fetchLatestTokenTransaction: jest.fn(),
    } as unknown as jest.Mocked<MetadataFetchService>;
    nftsFirestoreRepository = {
      insertNft: jest.fn(),
      updateTransactionConfirmation: jest.fn(),
      updateNftsMetadata: jest.fn(),
      deleteNft: jest.fn(),
    } as unknown as jest.Mocked<NftsFirestoreRepository>;
    executor = new MetadataSyncBatchExecutor(
      nftContractsRepository,
      accountRepository,
      metadataFetchService,
      nftsFirestoreRepository,
    );

    jest
      .spyOn(metadataFetchService, 'fetchNftTokens')
      .mockResolvedValue(mockNfts.map((nft) => ({ tokenId: nft.tokenId, tokenUri: nft.tokenUri })));
    jest.spyOn(metadataFetchService, 'fetchTokenUri').mockResolvedValue('https://example.com/token/1');
    jest
      .spyOn(metadataFetchService, 'fetchMetadataFromTokenURI')
      .mockResolvedValue({ name: 'Test NFT', description: 'A test NFT' });
    jest.spyOn(metadataFetchService, 'fetchTokenIdAndOwnerBalances').mockResolvedValue(mockTokenIdAndOwnerBalances);
    jest.spyOn(metadataFetchService, 'fetchLatestTokenTransaction').mockResolvedValue(mockTransferTransaction);
    jest.spyOn(executor as any, 'getAccountByTokenBoundAccountAddress').mockResolvedValue({ accountId: 'account123' });
    jest.spyOn(executor as any, 'upsertFirestore').mockResolvedValue(false);
    jest.spyOn(nftsFirestoreRepository, 'deleteNft').mockResolvedValue();
    jest
      .spyOn(global, 'setTimeout')
      .mockImplementation((cb: (...args: any[]) => void, delay?: number, ...args: any[]): NodeJS.Timeout => {
        cb(...args);
        return {} as NodeJS.Timeout;
      });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('execute', () => {
    test('should execute successfully with no tokenIds and return empty failure list', async () => {
      const result = await executor.sync({
        contractAddress: '0xe785E82358879F061BC3dcAC6f0444462D4b5330',
        serviceId: 'service1',
      });
      expect(result.failedList).toEqual([]);
    });

    test('should throw NotFoundError if NFT contract not found', async () => {
      (nftContractsRepository.selectNftContractByContractAddress as jest.Mock).mockResolvedValueOnce(null);
      await expect(executor.sync({ contractAddress: '0xNotFound', serviceId: 'service1' })).rejects.toThrow(
        NotFoundError,
      );
    });

    test('should execute successfully with valid tokenIds and return empty failure list', async () => {
      const tokenIds = ['1', '2'];
      (nftContractsRepository.selectNftContractByContractAddress as jest.Mock).mockResolvedValueOnce({
        nft_type: 'CONTENT',
      });
      const result = await executor.sync({
        contractAddress: '0xe785E82358879F061BC3dcAC6f0444462D4b5330',
        tokenIds,
        serviceId: 'service1',
      });
      expect(result.failedList).toEqual([]);
    });

    test('if failed in getTokenInfo returns failure in failureList', async () => {
      (nftContractsRepository.selectNftContractByContractAddress as jest.Mock).mockResolvedValueOnce({
        nft_type: 'CONTENT',
      });
      jest.spyOn(metadataFetchService, 'fetchNftTokens').mockImplementation(async () => {
        throw new Error('Failed to get token info');
      });
    });

    test('if failed in getMetadataFromURI returns failure in failureList', async () => {
      (nftContractsRepository.selectNftContractByContractAddress as jest.Mock).mockResolvedValueOnce({
        nft_type: 'CONTENT',
      });
      jest.spyOn(metadataFetchService, 'fetchMetadataFromTokenURI').mockImplementation(async () => {
        throw new Error('Failed to get metadata');
      });
      const result = await executor.sync({
        contractAddress: '0xe785E82358879F061BC3dcAC6f0444462D4b5330',
        tokenIds: ['1'],
        serviceId: 'service1',
      });
      expect(result.failedList).toHaveLength(1);
      expect(result.failedList[0].failedReason).toBe('Failed to retrieve metadata');
    });

    test('if failed in fetchTokenIdAndOwnerBalances returns failure in failureList', async () => {
      (nftContractsRepository.selectNftContractByContractAddress as jest.Mock).mockResolvedValueOnce({
        nft_type: 'CONTENT',
      });
      jest.spyOn(metadataFetchService, 'fetchTokenIdAndOwnerBalances').mockImplementation(async () => {
        throw new Error('Failed to get owner address');
      });
      const result = await executor.sync({
        contractAddress: '0xe785E82358879F061BC3dcAC6f0444462D4b5330',
        tokenIds: ['1'],
        serviceId: 'service1',
      });
      expect(result.failedList).toHaveLength(1);
      expect(result.failedList[0].failedReason).toBe('Failed to retrieve token ids and owner balances (all tokens)');
    });

    test('if failed in getAccountByTokenBoundAccountAddress returns failure in failureList', async () => {
      (nftContractsRepository.selectNftContractByContractAddress as jest.Mock).mockResolvedValueOnce({
        nft_type: 'CONTENT',
      });
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      jest.spyOn(executor as any, 'getAccountByTokenBoundAccountAddress').mockRejectedValueOnce(undefined);
      const result = await executor.sync({
        contractAddress: '0xe785E82358879F061BC3dcAC6f0444462D4b5330',
        tokenIds: ['1', '2'],
        serviceId: 'service1',
      });
      expect(result.failedList).toHaveLength(1);
      expect(result.failedList[0].failedReason).toBe('Failed to get account');
    });

    test('if failed in upsertFirestore returns failure in failureList', async () => {
      (nftContractsRepository.selectNftContractByContractAddress as jest.Mock).mockResolvedValueOnce({
        nft_type: 'CONTENT',
      });
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      jest.spyOn(executor as any, 'upsertFirestore').mockImplementation(async () => {
        throw new Error('Failed to upsert firestore');
      });
      const result = await executor.sync({
        contractAddress: '0xe785E82358879F061BC3dcAC6f0444462D4b5330',
        tokenIds: ['1'],
        serviceId: 'service1',
      });
      expect(result.failedList).toHaveLength(1);
      expect(result.failedList[0].failedReason).toBe('Firestore update failed');
    });
  });
});
