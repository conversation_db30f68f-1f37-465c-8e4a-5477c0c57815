import { z } from '@hono/zod-openapi';
import { NftMintRequestSchema } from '../../src/dtos/nfts/schemas';
import { NftType } from '../../src/enum/nftType';

describe('NftMintRequestSchema', () => {
  const validData = {
    serviceId: '8552df83-cbd5-44db-b67e-0cbeb2785918',
    accountId: '8552df83-cbd5-44db-b67e-0cbeb2785918',
    nftType: NftType.CONTENT,
    nftContractId: 'cee3e4aa-b0bf-482d-bb70-57a979c032c5',
    nftTokenId: 32,
    toAddress: '0xBC4CA0EdA7647A8aB7C2061c2E118A18a936f13D',
  };

  test('validates correctly with valid data', () => {
    expect(() => {
      NftMintRequestSchema.parse(validData);
    }).not.toThrow();
  });

  test('throws ZodError when serviceId is invalid', () => {
    const invalidData = { ...validData, serviceId: '' };

    expect(() => {
      NftMintRequestSchema.parse(invalidData);
    }).toThrow(z.ZodError);
  });

  test('throws ZodError when nftType is invalid', () => {
    const invalidData = { ...validData, nftType: 'INVALID_TYPE' };

    expect(() => {
      NftMintRequestSchema.parse(invalidData);
    }).toThrow(z.ZodError);
  });

  test('throws ZodError when nftContractId is invalid', () => {
    const invalidData = { ...validData, nftContractId: '' };

    expect(() => {
      NftMintRequestSchema.parse(invalidData);
    }).toThrow(z.ZodError);
  });

  test('allows nftTokenId to be null', () => {
    const dataWithNullTokenId = { ...validData, nftTokenId: null };

    expect(() => {
      NftMintRequestSchema.parse(dataWithNullTokenId);
    }).not.toThrow();
  });

  test('throws ZodError when toAddress is invalid', () => {
    const invalidData = { ...validData, toAddress: 'invalid-address' };

    expect(() => {
      NftMintRequestSchema.parse(invalidData);
    }).toThrow(z.ZodError);
  });

  test('throws ZodError when nftTokenId is not an integer', () => {
    const invalidData = { ...validData, nftTokenId: 3.14 };

    expect(() => {
      NftMintRequestSchema.parse(invalidData);
    }).toThrow(z.ZodError);
  });
});
