import { RewardStatus } from '../../../src/enum/rewardStatus';
import { QuestStatus } from '../../../src/enum/questStatus';
import { GetAccountQuestsQuerySchema } from '../../../src/dtos/accounts/path';

describe('getAccountQuestsQuerySchema', () => {
  it('passes with only one of each range', () => {
    const result = GetAccountQuestsQuerySchema.safeParse({
      expireAtFrom: '2024-01-01T00:00:00Z',
      startAtTo: '2024-01-10T00:00:00Z',
    });

    expect(result.success).toBe(true);
  });

  it('passes with full valid data', () => {
    const result = GetAccountQuestsQuerySchema.safeParse({
      expireAtFrom: '2024-01-01T00:00:00Z',
      expireAtTo: '2024-01-02T00:00:00Z',
      startAtFrom: '2024-01-03T00:00:00Z',
      startAtTo: '2024-01-04T00:00:00Z',
      rewardStatus: RewardStatus.NOT_ACQUIRED,
      questStatus: QuestStatus.NOT_STARTED,
    });

    expect(result.success).toBe(true);
  });

  it('fails when expireAtTo is before expireAtFrom', () => {
    const result = GetAccountQuestsQuerySchema.safeParse({
      expireAtFrom: '2024-01-05T00:00:00Z',
      expireAtTo: '2024-01-01T00:00:00Z',
    });

    expect(result.success).toBe(false);
    expect(result.error?.issues[0].path).toEqual(['expireAtTo']);
  });

  it('fails when startAtTo is before startAtFrom', () => {
    const result = GetAccountQuestsQuerySchema.safeParse({
      startAtFrom: '2024-02-10T00:00:00Z',
      startAtTo: '2024-02-01T00:00:00Z',
    });

    expect(result.success).toBe(false);
    expect(result.error?.issues[0].path).toEqual(['startAtTo']);
  });
});
