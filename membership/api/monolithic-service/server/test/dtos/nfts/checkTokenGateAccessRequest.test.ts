import { checkTokenGateAccessRequestSchema } from '../../../src/dtos/nfts/schemas';

describe('checkTokenGateAccessRequestSchema', () => {
  it('should succeed for minimal valid request', () => {
    const data = {
      tokenGateIds: ['8552df83-cbd5-44db-b67e-0cbeb2785918'],
      nfts: [
        {
          chainId: '137',
          contractAddress: '0xBC4CA0EdA7647A8aB7C2061c2E118A18a936f13D',
          tokenId: '123',
        },
      ],
    };
    const result = checkTokenGateAccessRequestSchema.safeParse(data);
    expect(result.success).toBe(true);
  });

  it('should succeed for multiple tokenGateIds and nfts', () => {
    const data = {
      tokenGateIds: ['11111111-1111-1111-1111-111111111111', '22222222-2222-2222-2222-222222222222'],
      nfts: [
        {
          chainId: '1',
          contractAddress: '0x0000000000000000000000000000000000000000',
          tokenId: '0',
        },
        {
          chainId: '10',
          contractAddress: '0x1234567890abcdef1234567890abcdef12345678',
          tokenId: '99999',
        },
      ],
    };
    const result = checkTokenGateAccessRequestSchema.safeParse(data);
    expect(result.success).toBe(true);
  });

  it('should fail when tokenGateIds is empty', () => {
    const data = {
      tokenGateIds: [],
      nfts: [{ chainId: '1', contractAddress: '0x0000000000000000000000000000000000000000', tokenId: '1' }],
    };
    const result = checkTokenGateAccessRequestSchema.safeParse(data);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.errors[0].message).toContain('at least one token gate id is required');
    }
  });

  it('should fail when tokenGateIds contains invalid UUID', () => {
    const data = {
      tokenGateIds: ['not-a-uuid'],
      nfts: [{ chainId: '1', contractAddress: '0x0000000000000000000000000000000000000000', tokenId: '1' }],
    };
    const result = checkTokenGateAccessRequestSchema.safeParse(data);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.errors.some((e) => e.path.includes('tokenGateIds'))).toBe(true);
    }
  });

  it('should fail when nfts is empty', () => {
    const data = { tokenGateIds: ['8552df83-cbd5-44db-b67e-0cbeb2785918'], nfts: [] };
    const result = checkTokenGateAccessRequestSchema.safeParse(data);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.errors[0].message).toContain('at least one NFT is required');
    }
  });

  it('should fail when chainId is empty', () => {
    const data = {
      tokenGateIds: ['8552df83-cbd5-44db-b67e-0cbeb2785918'],
      nfts: [{ chainId: '', contractAddress: '0x0000000000000000000000000000000000000000', tokenId: '1' }],
    };
    const result = checkTokenGateAccessRequestSchema.safeParse(data);
    expect(result.success).toBe(false);
  });

  it('should fail when contractAddress is invalid', () => {
    const data = {
      tokenGateIds: ['8552df83-cbd5-44db-b67e-0cbeb2785918'],
      nfts: [{ chainId: '1', contractAddress: '0xINVALID', tokenId: '1' }],
    };
    const result = checkTokenGateAccessRequestSchema.safeParse(data);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.errors.some((e) => e.path.includes('contractAddress'))).toBe(true);
    }
  });

  it('should fail when tokenId is non-numeric string', () => {
    const data = {
      tokenGateIds: ['8552df83-cbd5-44db-b67e-0cbeb2785918'],
      nfts: [{ chainId: '1', contractAddress: '0x0000000000000000000000000000000000000000', tokenId: 'abc' }],
    };
    const result = checkTokenGateAccessRequestSchema.safeParse(data);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.errors[0].message).toContain('token must be a numeric string');
    }
  });
});
