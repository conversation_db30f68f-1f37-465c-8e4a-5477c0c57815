import { registerTokenGateRequestSchema } from '../../../src/dtos/nfts/schemas';

describe('registerTokenGateRequestSchema', () => {
  it('should succeed for a minimal valid request (without tokenId)', () => {
    const validData = {
      tokenGateTranslations: [
        {
          language: 'ja',
          gateName: '限定記事35号用TokenGate',
          gateDescription: 'この記事は、限定NFT Aと限定NFT Bの両方を所持しているユーザーのみが閲覧可能です。',
        },
      ],
      tokenGateConditions: [
        {
          setNumber: 1,
          nft: {
            chainId: '137',
            contractAddress: '0xBC4CA0EdA7647A8aB7C2061c2E118A18a936f13D',
          },
        },
      ],
    };

    const result = registerTokenGateRequestSchema.safeParse(validData);
    expect(result.success).toBe(true);
  });

  it('should succeed when tokenId is provided', () => {
    const validData = {
      tokenGateTranslations: [{ language: 'en-US', gateName: 'TokenGate Name', gateDescription: 'Description.' }],
      tokenGateConditions: [
        {
          setNumber: 2,
          nft: {
            chainId: '1',
            contractAddress: '0x0000000000000000000000000000000000000000',
            tokenId: '123',
          },
        },
      ],
    };

    const result = registerTokenGateRequestSchema.safeParse(validData);
    expect(result.success).toBe(true);
  });

  it('should fail when tokenGateTranslations is empty', () => {
    const invalidData = {
      tokenGateTranslations: [],
      tokenGateConditions: [{ setNumber: 1, nft: { chainId: '137', contractAddress: '0xabc' } }],
    };

    const result = registerTokenGateRequestSchema.safeParse(invalidData);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.errors[0].message).toContain('at least one token gate translation is required');
    }
  });

  it('should fail when gateName or gateDescription is missing or empty', () => {
    const cases = [
      {
        tokenGateTranslations: [{ language: 'ja', gateDescription: 'Desc' }],
        tokenGateConditions: [{ setNumber: 1, nft: { chainId: '137', contractAddress: '0xabc' } }],
      },
      {
        tokenGateTranslations: [{ language: 'ja', gateName: 'Name', gateDescription: '' }],
        tokenGateConditions: [{ setNumber: 1, nft: { chainId: '137', contractAddress: '0xabc' } }],
      },
    ];

    for (const data of cases) {
      const result = registerTokenGateRequestSchema.safeParse(data as any);
      expect(result.success).toBe(false);
    }
  });

  it('should fail when tokenGateConditions is empty', () => {
    const invalidData = {
      tokenGateTranslations: [{ language: 'ja', gateName: 'A', gateDescription: 'B' }],
      tokenGateConditions: [],
    };

    const result = registerTokenGateRequestSchema.safeParse(invalidData);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.errors[0].message).toContain('at least one token gate condition is required');
    }
  });

  it('should fail when setNumber is less than 1', () => {
    const invalidData = {
      tokenGateTranslations: [{ language: 'ja', gateName: 'A', gateDescription: 'B' }],
      tokenGateConditions: [{ setNumber: 0, nft: { chainId: '137', contractAddress: '0xabc' } }],
    };

    const result = registerTokenGateRequestSchema.safeParse(invalidData);
    expect(result.success).toBe(false);
  });

  it('should fail when nft fields are invalid', () => {
    const invalidData = {
      tokenGateTranslations: [{ language: 'ja', gateName: 'A', gateDescription: 'B' }],
      tokenGateConditions: [
        {
          setNumber: 1,
          nft: {
            chainId: '',
            contractAddress: '',
            tokenId: '',
          },
        },
      ],
    };

    const result = registerTokenGateRequestSchema.safeParse(invalidData);
    expect(result.success).toBe(false);
  });
});
