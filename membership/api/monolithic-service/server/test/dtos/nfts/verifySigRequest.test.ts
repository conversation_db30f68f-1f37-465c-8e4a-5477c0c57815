import { z } from '@hono/zod-openapi';
import { verifySigRequestSchema } from '../../../src/dtos/nfts/schemas';

describe('verifySigRequestSchema', () => {
  const goodSig = '0x' + 'a'.repeat(130);
  const goodMsg =
    'Nonce: a1b2c3d4\nIssued At: 2025-06-05T14:00:00Z\nExpiration: 2025-08-05T14:00:00Z\nDomain: example.com';

  it('parses a valid payload', () => {
    const result = verifySigRequestSchema.safeParse({
      sig: goodSig,
      msg: goodMsg,
    });
    expect(result.success).toBe(true);
    if (result.success) {
      // the inferred type should match
      const data: z.infer<typeof verifySigRequestSchema> = result.data;
      expect(data.sig).toBe(goodSig);
      expect(data.msg).toBe(goodMsg);
    }
  });

  describe('rejects invalid sig', () => {
    it('when missing 0x prefix', () => {
      const result = verifySigRequestSchema.safeParse({
        sig: goodSig.slice(2),
        msg: goodMsg,
      });
      expect(result.success).toBe(false);
    });

    it('when wrong length (too short)', () => {
      const short = '0x' + 'a'.repeat(128);
      const result = verifySigRequestSchema.safeParse({
        sig: short,
        msg: goodMsg,
      });
      expect(result.success).toBe(false);
    });

    it('when containing non-hex chars', () => {
      const bad = '0x' + 'g'.repeat(130);
      const result = verifySigRequestSchema.safeParse({
        sig: bad,
        msg: goodMsg,
      });
      expect(result.success).toBe(false);
    });
  });

  describe('rejects invalid msg', () => {
    it('when missing Nonce prefix', () => {
      const result = verifySigRequestSchema.safeParse({
        sig: goodSig,
        msg: goodMsg.replace(/Nonce:.+\n/, ''),
      });
      expect(result.success).toBe(false);
    });

    it('when empty string', () => {
      const result = verifySigRequestSchema.safeParse({
        sig: goodSig,
        msg: '',
      });
      expect(result.success).toBe(false);
    });
  });
});
