import { languageCode } from '../../../src/enum/languageCode';
import { QuestionnaireType } from '../../../src/enum/questionnaireTypeEnum';
import { QuestionType } from '../../../src/enum/questionTypeEnum';
import { QuestionnaireCreateRequestSchema } from '../../../src/dtos/questionnaire/schemas';
describe('createQuestionnaireRequestSchema', () => {
  it('passes with only questionnaireType MESSAGE', () => {
    const result = QuestionnaireCreateRequestSchema.safeParse({
      questionnaireType: QuestionnaireType.MESSAGE,
      themes: [
        {
          themeThumbnailImageUrl: 'https://example.com/theme-thumbnail.jpg',
          themeCoverImageUrl: 'https://example.com/cover1.jpg',
          themeNumber: 1,
          themeTimeLimitSeconds: 900,
          themeTranslations: [
            {
              language: languageCode.EN_US,
              themeTitle: 'Comprehensive Questions Theme',
              themeDescription: 'Theme with various question types',
            },
          ],
          questions: [
            {
              questionNumber: 1,
              questionType: QuestionType.TEXT_LINES,
              questionImageUrl: 'https://example.com/question-image.jpg',
              isRequired: true,
              questionTranslations: [
                {
                  language: languageCode.EN_US,
                  questionTitle: 'title',
                  questionDetail: 'detail',
                  questionExtra: {
                    type: QuestionType.TEXT_LINES,
                    validations: [],
                  },
                },
              ],
            },
          ],
        },
      ],
    });
    expect(result.success).toBe(true);
  });
  it('fails when MESSAGE and rank validation', () => {
    const result = QuestionnaireCreateRequestSchema.safeParse({
      questionnaireType: QuestionnaireType.MESSAGE,
      themes: [
        {
          themeTitle: 'Comprehensive Questions Theme',
          themeDescription: 'Theme with various question types',
          themeThumbnailImageUrl: 'https://example.com/theme-thumbnail.jpg',
          themeCoverImageUrl: 'https://example.com/cover1.jpg',
          themeNumber: 1,
          themeTimeLimitSeconds: 900,
          questions: [
            {
              questionNumber: 1,
              questionTitle: 'title',
              questionDetail: 'detail',
              questionType: QuestionType.TEXT_LINES,
              questionImageUrl: 'https://example.com/question-image.jpg',
              isRequired: true,
              questionExtra: {
                type: QuestionType.TEXT_LINES,
                validations: [],
              },
            },
          ],
        },
      ],
      ranks: [
        {
          rankName: 'Beginner',
          rankHeaderAnimationUrl: 'https://example.com/animations/beginner.gif',
          rank: 1,
          lowerLimitPoints: 0,
          upperLimitPoints: 50,
          isPassed: true,
        },
      ],
    });
    expect(result.success).toBe(false);
  });
  it('fails when MESSAGE and question correct validation', () => {
    const result = QuestionnaireCreateRequestSchema.safeParse({
      questionnaireType: QuestionnaireType.MESSAGE,
      themes: [
        {
          themeTitle: 'Comprehensive Questions Theme',
          themeDescription: 'Theme with various question types',
          themeThumbnailImageUrl: 'https://example.com/theme-thumbnail.jpg',
          themeCoverImageUrl: 'https://example.com/cover1.jpg',
          themeNumber: 1,
          themeTimeLimitSeconds: 900,
          questions: [
            {
              questionNumber: 1,
              questionTitle: 'title',
              questionDetail: 'detail',
              questionType: QuestionType.TEXT_LINES,
              questionImageUrl: 'https://example.com/question-image.jpg',
              isRequired: true,
              questionExtra: {},
              answerPoint: 1,
              correctData: 'correctData',
              correctDataValidation: '^*$',
            },
          ],
        },
      ],
    });
    expect(result.success).toBe(false);
  });
  it('fails when MESSAGE and question questionType validation', () => {
    const result = QuestionnaireCreateRequestSchema.safeParse({
      questionnaireType: QuestionnaireType.MESSAGE,
      themes: [
        {
          themeTitle: 'Comprehensive Questions Theme',
          themeDescription: 'Theme with various question types',
          themeThumbnailImageUrl: 'https://example.com/theme-thumbnail.jpg',
          themeCoverImageUrl: 'https://example.com/cover1.jpg',
          themeNumber: 1,
          themeTimeLimitSeconds: 900,
          questions: [
            {
              questionNumber: 1,
              questionTitle: 'title',
              questionDetail: 'detail',
              questionType: QuestionType.SINGLE_CHOICE,
              questionImageUrl: 'https://example.com/question-image.jpg',
              isRequired: true,
              questionExtra: {
                type: QuestionType.SINGLE_CHOICE,
                validations: [],
              },
            },
          ],
        },
      ],
    });
    expect(result.success).toBe(false);
  });
  it('fails when MESSAGE and question length validation', () => {
    const result = QuestionnaireCreateRequestSchema.safeParse({
      questionnaireType: QuestionnaireType.MESSAGE,
      themes: [
        {
          themeTitle: 'Comprehensive Questions Theme',
          themeDescription: 'Theme with various question types',
          themeThumbnailImageUrl: 'https://example.com/theme-thumbnail.jpg',
          themeCoverImageUrl: 'https://example.com/cover1.jpg',
          themeNumber: 1,
          themeTimeLimitSeconds: 900,
          questions: [
            {
              questionNumber: 1,
              questionTitle: 'title',
              questionDetail: 'detail',
              questionType: QuestionType.TEXT_LINES,
              questionImageUrl: 'https://example.com/question-image.jpg',
              isRequired: true,
              questionExtra: {
                type: QuestionType.TEXT_LINES,
                validations: [],
              },
            },
            {
              questionNumber: 1,
              questionTitle: 'title',
              questionDetail: 'detail',
              questionType: QuestionType.TEXT_LINES,
              questionExtra: {
                type: QuestionType.TEXT_LINES,
                validations: [],
              },
            },
          ],
        },
      ],
    });
    expect(result.success).toBe(false);
  });
});

describe('updateQuestionnaireRequestSchema', () => {
  it('passes with only questionnaireType MESSAGE', () => {
    const result = QuestionnaireCreateRequestSchema.safeParse({
      questionnaireType: QuestionnaireType.MESSAGE,
      themes: [
        {
          themeId: 'e9bbba96-37dc-495f-9371-1e20ab2588a8',
          themeThumbnailImageUrl: 'https://example.com/theme-thumbnail.jpg',
          themeCoverImageUrl: 'https://example.com/cover1.jpg',
          themeNumber: 1,
          themeTimeLimitSeconds: 900,
          themeTranslations: [
            {
              language: languageCode.EN_US,
              themeTitle: 'Comprehensive Questions Theme',
              themeDescription: 'Theme with various question types',
            },
          ],
          questions: [
            {
              questionId: 'e9bbba96-37dc-495f-9371-1e20ab2588a8',
              questionNumber: 1,
              questionType: QuestionType.TEXT_LINES,
              questionImageUrl: 'https://example.com/question-image.jpg',
              isRequired: true,
              questionTranslations: [
                {
                  language: languageCode.EN_US,
                  questionTitle: 'title',
                  questionDetail: 'detail',
                  questionExtra: {
                    type: QuestionType.TEXT_LINES,
                    validations: [],
                  },
                },
              ],
            },
          ],
        },
      ],
    });
    expect(result.success).toBe(true);
  });
  it('fails when MESSAGE and rank validation', () => {
    const result = QuestionnaireCreateRequestSchema.safeParse({
      questionnaireType: QuestionnaireType.MESSAGE,
      themes: [
        {
          themeTitle: 'Comprehensive Questions Theme',
          themeDescription: 'Theme with various question types',
          themeThumbnailImageUrl: 'https://example.com/theme-thumbnail.jpg',
          themeCoverImageUrl: 'https://example.com/cover1.jpg',
          themeNumber: 1,
          themeTimeLimitSeconds: 900,
          questions: [
            {
              questionNumber: 1,
              questionTitle: 'title',
              questionDetail: 'detail',
              questionType: QuestionType.TEXT_LINES,
              questionImageUrl: 'https://example.com/question-image.jpg',
              isRequired: true,
              questionExtra: {
                type: QuestionType.TEXT_LINES,
                validations: [],
              },
            },
          ],
        },
      ],
      ranks: [
        {
          rankName: 'Beginner',
          rankHeaderAnimationUrl: 'https://example.com/animations/beginner.gif',
          rank: 1,
          lowerLimitPoints: 0,
          upperLimitPoints: 50,
          isPassed: true,
        },
      ],
    });
    expect(result.success).toBe(false);
  });
  it('fails when MESSAGE and question correct validation', () => {
    const result = QuestionnaireCreateRequestSchema.safeParse({
      questionnaireType: QuestionnaireType.MESSAGE,
      themes: [
        {
          themeTitle: 'Comprehensive Questions Theme',
          themeDescription: 'Theme with various question types',
          themeThumbnailImageUrl: 'https://example.com/theme-thumbnail.jpg',
          themeCoverImageUrl: 'https://example.com/cover1.jpg',
          themeNumber: 1,
          themeTimeLimitSeconds: 900,
          questions: [
            {
              questionNumber: 1,
              questionTitle: 'title',
              questionDetail: 'detail',
              questionType: QuestionType.TEXT_LINES,
              questionImageUrl: 'https://example.com/question-image.jpg',
              isRequired: true,
              questionExtra: {},
              answerPoint: 1,
              correctData: 'correctData',
              correctDataValidation: '^*$',
            },
          ],
        },
      ],
    });
    expect(result.success).toBe(false);
  });
  it('fails when MESSAGE and question questionType validation', () => {
    const result = QuestionnaireCreateRequestSchema.safeParse({
      questionnaireType: QuestionnaireType.MESSAGE,
      themes: [
        {
          themeTitle: 'Comprehensive Questions Theme',
          themeDescription: 'Theme with various question types',
          themeThumbnailImageUrl: 'https://example.com/theme-thumbnail.jpg',
          themeCoverImageUrl: 'https://example.com/cover1.jpg',
          themeNumber: 1,
          themeTimeLimitSeconds: 900,
          questions: [
            {
              questionNumber: 1,
              questionTitle: 'title',
              questionDetail: 'detail',
              questionType: QuestionType.SINGLE_CHOICE,
              questionExtra: {},
            },
          ],
        },
      ],
    });
    expect(result.success).toBe(false);
  });
  it('fails when MESSAGE and question length validation', () => {
    const result = QuestionnaireCreateRequestSchema.safeParse({
      questionnaireType: QuestionnaireType.MESSAGE,
      themes: [
        {
          themeTitle: 'Comprehensive Questions Theme',
          themeDescription: 'Theme with various question types',
          themeThumbnailImageUrl: 'https://example.com/theme-thumbnail.jpg',
          themeCoverImageUrl: 'https://example.com/cover1.jpg',
          themeNumber: 1,
          themeTimeLimitSeconds: 900,
          questions: [
            {
              questionNumber: 1,
              questionTitle: 'title',
              questionDetail: 'detail',
              questionType: QuestionType.TEXT_LINES,
              questionImageUrl: 'https://example.com/question-image.jpg',
              isRequired: true,
              questionExtra: {},
            },
            {
              questionNumber: 1,
              questionTitle: 'title',
              questionDetail: 'detail',
              questionType: QuestionType.TEXT_LINES,
              questionExtra: {},
            },
          ],
        },
      ],
    });
    expect(result.success).toBe(false);
  });
});
