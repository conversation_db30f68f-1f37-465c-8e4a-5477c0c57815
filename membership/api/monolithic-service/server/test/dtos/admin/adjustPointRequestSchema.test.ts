import { adjustPointRequestSchema } from '../../../src/dtos/admins/schemas';
import { PointOps } from '../../../src/enum/pointOpsType';
import { pointType } from '../../../src/enum/pointType';


const baseValidData = {
  accountId: '99DAFCE9-C629-4E39-9F75-37C93D8BC3AF',
  pointType: pointType.REWARD,
  pointOpsType: PointOps.ADD,
  amount: 100,
};

describe('adjustPointRequestSchema', () => {
  beforeAll(() => {
    jest.useFakeTimers({ now: new Date('2030-01-01T00:00:00.000Z') });
  });

  afterAll(() => {
    jest.useRealTimers();
  });

  it('should pass with positive amount and omitted expiresOn', () => {
    expect(() => adjustPointRequestSchema.parse(baseValidData)).not.toThrow();
  });

  it('should pass with negative amount', () => {
    const data = {
      ...baseValidData,
      pointOpsType: PointOps.SUB,
      amount: -50,
    };
    expect(() => adjustPointRequestSchema.parse(data)).not.toThrow();
  });

  it('should pass with future expiresOn', () => {
    const data = {
      ...baseValidData,
      expiresOn: '2031-01-01T00:00:00.000Z',
    };
    expect(() => adjustPointRequestSchema.parse(data)).not.toThrow();
  });

  it('should fail with 0 amount', () => {
    const data = { ...baseValidData, amount: 0 };
    const result = adjustPointRequestSchema.safeParse(data);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.issues[0].message).toBe('amount should not be 0');
    }
  });

  it('should fail with negative amount in ADD', () => {
    const data = { ...baseValidData, amount: -10 };
    const result = adjustPointRequestSchema.safeParse(data);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.flatten().fieldErrors.amount).toContain('amount should be greater than 0 in case of "ADD"');
    }
  });

  it('should fail with positive amount in SUB', () => {
    const data = {
      ...baseValidData,
      pointOpsType: PointOps.SUB,
      amount: 30,
    };
    const result = adjustPointRequestSchema.safeParse(data);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.flatten().fieldErrors.amount).toContain('amount should be less than 0 in case of "SUB"');
    }
  });

  it('should fail with past expiresOn', () => {
    const data = {
      ...baseValidData,
      expiresOn: '2020-01-01T00:00:00.000Z',
    };
    const result = adjustPointRequestSchema.safeParse(data);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.flatten().fieldErrors.expiresOn).toContain('expiresOn should be future time');
    }
  });

  it('should fail with invalid accountId', () => {
    const data = { ...baseValidData, accountId: 'not-a-uuid' };
    const result = adjustPointRequestSchema.safeParse(data);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.flatten().fieldErrors.accountId?.[0]).toMatch(/Invalid/);
    }
  });
});
