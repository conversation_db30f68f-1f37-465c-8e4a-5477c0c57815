import { serve } from '@hono/node-server';
import dotenv from 'dotenv';
import { ContentfulStatusCode } from 'hono/utils/http-status';
import 'reflect-metadata';
import { BaseError } from './errors/baseError';
import { logger } from './utils/logger';

import { OpenAPIHono, z } from '@hono/zod-openapi';
import { runBatch } from './batch';
import { corsSetting, loggerMiddleware } from './configs/middleware';
import { registerOpenApiDocs, registerOpenApiSecurity } from './configs/openapi';
import { registerRoutes } from './routes';
import { setLanguage } from './utils/i18n';

dotenv.config();
const batchArg = process.argv.find((arg) => arg.startsWith('batch:'));

if (batchArg) {
  const batchCommand = batchArg.split(':')[1];
  logger.info(`start batch with command: ${batchCommand}`);
  runBatch();
} else {
  const app = new OpenAPIHono();
  app.use(corsSetting);
  app.use('*', loggerMiddleware);

  setLanguage(app);
  registerRoutes(app);

  registerOpenApiSecurity(app);
  registerOpenApiDocs(app);

  app.onError((err, c) => {
    if (err instanceof z.ZodError) {
      logger.warn('Zod validation error', {
        issues: err.issues,
        path: c.req.path,
      });
      return c.json({ status: 400, error: 'Validation failed', issues: err.issues }, 400);
    }

    logger.error(err);
    const statusCode = ((err as BaseError)?.status || 500) as ContentfulStatusCode;
    const code = (err as BaseError)?.code || '';
    const message = statusCode === 500 ? 'Internal Server Error' : err.message;
    const errorData = { status: statusCode, code: code, message: message };
    return c.json(errorData, statusCode);
  });

  const port = Number(process.env.PORT) || 8080;
  serve({ fetch: app.fetch, port });
}
