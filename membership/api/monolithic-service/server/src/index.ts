import { serve } from '@hono/node-server';
import dotenv from 'dotenv';
import { ContentfulStatusCode } from 'hono/utils/http-status';
import 'reflect-metadata';
import { BaseError } from './errors/baseError';
import { logger, loggerMiddleware } from './utils/middleware/loggerMiddleware';

import { OpenAPIHono, z } from '@hono/zod-openapi';
import { runBatch } from './batch';
import { registerOpenApiDocs, registerOpenApiSecurity } from './configs/openapi';
import { registerRoutes } from './routes';
import { corsSetting } from './utils/middleware/corsMiddleware';
import { languageMiddleware } from './utils/middleware/languageMiddleware';
import { configureMiddlewares } from './utils/middleware/dynamicConfigMiddleware';

dotenv.config();
const batchArg = process.argv.find((arg) => arg.startsWith('batch:'));

if (batchArg) {
  const batchCommand = batchArg.split(':')[1];
  logger.info(`start batch with command: ${batchCommand}`);
  runBatch();
} else {
  const app = new OpenAPIHono();
  const ephemeralApp = new OpenAPIHono();
  registerRoutes(ephemeralApp);

  app.use(corsSetting);
  app.use('*', loggerMiddleware);
  app.use('*', languageMiddleware);
  // ルート登録後に動的ミドルウェアを適用
  configureMiddlewares(ephemeralApp, app);
  registerRoutes(app);


  registerOpenApiSecurity(app);
  registerOpenApiDocs(app);

  app.onError((err, c) => {
    if (err instanceof z.ZodError) {
      logger.warn('Zod validation error', {
        issues: err.issues,
        path: c.req.path,
      });
      return c.json({ status: 400, error: 'Validation failed', issues: err.issues }, 400);
    }

    logger.error(err);
    const statusCode = ((err as BaseError)?.status || 500) as ContentfulStatusCode;
    const code = (err as BaseError)?.code || '';
    const message = statusCode === 500 ? 'Internal Server Error' : err.message;
    const errorData = { status: statusCode, code: code, message: message };
    return c.json(errorData, statusCode);
  });

  const port = Number(process.env.PORT) || 8080;
  serve({ fetch: app.fetch, port });
}

