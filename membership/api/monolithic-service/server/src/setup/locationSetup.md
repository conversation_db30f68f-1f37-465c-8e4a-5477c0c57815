# Location Check-in Feature Setup Guide

## 1. Database Migration
Run the migration file to create the required tables:
```sql
-- Run: membership/api/monolithic-service/server/migrations/add_geofence_location_checkin.sql
```

## 2. Dependency Injection Registration

Add to your DI container setup (usually in `src/container.ts` or similar):

```typescript
import { LocationService } from './services/locationService';
import { LocationController } from './controllers/locationController';
import { GeofenceRepository } from './repositories/geofenceRepository';

// Register repositories
container.registerSingleton(GeofenceRepository);

// Register services
container.register('LocationService', { useClass: LocationService });

// Register controllers
container.registerSingleton(LocationController);
```

## 3. Route Registration

Add to your main routes file (usually `src/routes/index.ts` or `src/app.ts`):

```typescript
import { locationRoutes } from './routes/locationRoutes';

// Add location routes
app.route('/api/v1/location', locationRoutes);
```

## 4. API Endpoints

### Geofence Management
- `POST /api/v1/location/geofences` - Create geofence
- `GET /api/v1/location/geofences` - List geofences
- `GET /api/v1/location/geofences/:geofenceId` - Get geofence
- `PUT /api/v1/location/geofences/:geofenceId` - Update geofence
- `DELETE /api/v1/location/geofences/:geofenceId` - Delete geofence

### Location Check-in
- `POST /api/v1/location/accounts/:accountId/checkin` - Perform location check-in
- `GET /api/v1/location/accounts/:accountId/actions/:actionId/attempts` - Get check-in attempts
- `POST /api/v1/location/geofences/:geofenceId/check` - Check if location is within geofence

## 5. Creating Location Check-in Actions

To create a location check-in action, you need to:

1. Create a geofence first:
```json
POST /api/v1/location/geofences
{
  "name": "Tokyo Station",
  "description": "Check-in location for Tokyo Station quest",
  "latitude": 35.6812,
  "longitude": 139.7671,
  "radiusMeters": 100
}
```

2. Create an action with type `LOCATION_CHECKIN`:
```json
POST /api/v1/actions
{
  "actionType": "LOCATION_CHECKIN",
  "name": "Check in at Tokyo Station",
  "description": "Visit Tokyo Station to complete this action"
}
```

3. Link the action to the geofence using the GeofenceRepository:
```typescript
await geofenceRepository.createLocationCheckinAction(
  actionId, 
  geofenceId, 
  requiredDurationSeconds // optional, defaults to 0
);
```

## 6. Frontend Integration

### Location Check-in Flow
1. Get user's current location using GPS
2. Call the check-in endpoint with location data
3. Handle success/failure responses
4. Show appropriate feedback to user

### Example Frontend Code
```typescript
// Get user location
navigator.geolocation.getCurrentPosition(async (position) => {
  const checkinData = {
    actionId: "action-uuid",
    location: {
      latitude: position.coords.latitude,
      longitude: position.coords.longitude,
      accuracy: position.coords.accuracy
    }
  };

  try {
    const response = await fetch('/api/v1/location/accounts/account-id/checkin', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'service-id-header': 'your-service-id'
      },
      body: JSON.stringify(checkinData)
    });

    const result = await response.json();
    
    if (result.success) {
      // Show success message
      console.log(`Successfully checked in! ${result.message}`);
    } else {
      // Show failure message with distance
      console.log(`Check-in failed: ${result.message}`);
    }
  } catch (error) {
    console.error('Check-in error:', error);
  }
});
```

## 7. Key Features

### Geofence Validation
- Validates latitude/longitude ranges
- Ensures radius is positive
- Calculates distance using Haversine formula
- Supports circular geofences only (can be extended for polygons)

### Location Check-in Logic
- Prevents duplicate completions
- Records all attempts (successful and failed)
- Calculates precise distance from geofence center
- Provides helpful feedback messages
- Supports GPS accuracy consideration

### Security Considerations
- Validates account ownership
- Checks action type before processing
- Logs all location attempts for audit
- Rate limiting should be implemented at API gateway level

## 8. Database Schema

The migration creates these tables:
- `geofences` - Store geofence definitions
- `location_checkin_actions` - Link actions to geofences
- `location_checkin_attempts` - Track all check-in attempts
- `calculate_distance_meters()` - PostgreSQL function for distance calculation

## 9. Testing

Create tests for:
- Geofence CRUD operations
- Location check-in success/failure scenarios
- Distance calculation accuracy
- Edge cases (GPS accuracy, boundary conditions)
- Security validations

## 10. Monitoring

Consider monitoring:
- Check-in success rates
- Average distance from target
- GPS accuracy distribution
- Failed attempt patterns
- Performance of distance calculations
