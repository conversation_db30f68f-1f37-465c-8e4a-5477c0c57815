import { container } from 'tsyringe';
import { UserService } from '../services/userService';
import { UserRepository } from '../repositories/userRepository';
import { AuthService } from '../services/authService';
import { AuthProviderRepository } from '../repositories/authProviderRepository';
import { FirebaseComponent } from '../components/firebaseComponent';
import { LineComponent } from '../components/lineComponent';
import { QuestRepository } from '../repositories/questRepository';
import { QuestService } from '../services/questService';
import { ActionRepository } from '../repositories/actionRepository';
import { RewardRepository } from '../repositories/rewardRepository';
import { ServiceInfoService } from '../services/serviceInfoService';
import { RewardService } from '../services/rewardService';
import { AccountRepository } from '../repositories/accountRepository';
import { AccountService } from '../services/accountService';
import { CertificateRewardRepository } from '../repositories/certificateRewardRepository';
import { QuestActivityRepository } from '../repositories/questActivityRepository';
import { ClaimedRewardRepository } from '../repositories/claimedRewardRepository';
import { ActionService } from '../services/actionService';
import { AchievementActionRepository } from '../repositories/achievementActionRepository';
import { OnlineCheckinActionRepository } from '../repositories/onlineCheckinActionRepository';
import { QuestionnaireRepository } from '../repositories/questionnaireRepository';
import { QuestActionRepository } from '../repositories/questActionRepository';
import { ActionActivityRepository } from '../repositories/actionActivityRepository';
import { QuestRewardRepository } from '../repositories/questRewardRepository';
import { NftMetadatasRepository } from '../repositories/nftMetadatasRepository';
import { NftsService } from '../services/nftsService';
import { ServiceInfoRepository } from '../repositories/serviceInfoRepository';
import { NftContractsRepository } from '../repositories/nftContractsRepository';
import { NftMintService } from '../services/nftMintService';
import { BulkMintService } from '../services/bulkMintService';
import { TransactionService } from '../services/transactionService';
import { NftContractTypesRepository } from '../repositories/nftContractTypesRepository';
import { NftBaseMetadatasRepository } from '../repositories/nftBaseMetadatasRepository';
import { VaultTransactionQueuesRepository } from '../repositories/vaultTransactionQueuesRepository';
import { TransactionQueuesRepository } from '../repositories/transactionQueuesRepository';
import { TransactionsRepository } from '../repositories/transactionsRepository';
import { AttemptTransactionsRepository } from '../repositories/attemptTransactionsRepository';
import { VaultKeyRepository } from '../repositories/vaultKeyRepository';
import { WebhookService } from '../services/webhookService';
import { TokenBoundAccountRegistryAddressRepository } from '../repositories/tokenBoundAccountRegistryAddressRepository';
import { CouponRewardRepository } from '../repositories/couponRewardRepository';
import { DigitalContentRewardRepository } from '../repositories/digitalContentRewardRepository';
import { UserOperationQueuesRepository } from '../repositories/userOperationQueuesRepository';
import { QrCheckinActionRepository } from '../repositories/qrCheckinActionRepository';
import { RpcComponent } from '../components/rpcComponent';
import { TransactionComponent } from '../components/transactionComponent';
import { NftRegisterService } from '../services/nftRegisterService';
import { MetadataService } from '../services/metadataService';
import { UserOperationService } from '../services/userOperationService';
import { TokenBoundAccountImplementationRepository } from '../repositories/tokenBoundAccountImplementationsRepository';
import { VaultKeyService } from '../services/vaultKeyService';
import { ProductService } from '../services/productService';
import { CheckoutRepository } from '../repositories/checkoutRepository';
import { ProductRepository } from '../repositories/productRepository';
import { CustomFieldsRepository } from '../repositories/customFieldsRepository';
import { NftsFirestoreRepository } from '../repositories/nftsFirestoreRepository';
import { AdminService } from '../services/adminService';
import { NftTransactionUpdateService } from '../services/transactionUpdateService';
import { MetadataFetchService } from '../services/metadataFetchService';
import { GeofenceService } from '../services/geofenceService';
import { GeofenceRepository } from '../repositories/geofenceRepository';
import { ContentPurchaseActionRepository } from '../repositories/contentPurchaseActionRepository';
import { TenantsService } from '../services/tenantService';
import { TenantRepository } from '../repositories/tenantRepository';
import { PlanRepository } from '../repositories/planRepository';
import { QuestionnaireService } from '../services/questionnaireService';
import { QuestionnaireQuestionRepository } from '../repositories/questionnaireQuestionRepository';
import { QuestionnaireQuestionAnswerRepository } from '../repositories/questionnaireQuestionAnswerRepository';
import { QuestionnaireResultAnswerRepository } from '../repositories/questionnaireResultAnswerRepository';
import { QuestionnaireResultRankRepository } from '../repositories/questionnaireResultRankRepository';
import { QuestionnaireThemeRepository } from '../repositories/questionnaireThemeRepository';
import { QuestionnaireActionRepository } from '../repositories/questionnaireActionRepository';
import { DeliveryNftsFirestoreRepository } from '../repositories/deliveryNftsFirestoreRepository';
import { AlchemyComponent } from '../components/alchemyComponent';
import { ViemComponent } from '../components/viemComponent';
import { SerialCodeService } from '../services/serialCodeService';
import { SerialCodeProjectsRepository } from '../repositories/serialCodeProjectsRepository';
import { SerialCodesRepository } from '../repositories/serialCodesRepository';
import { AccountSerialCodesRepository } from '../repositories/accountSerialCodesRepository';
import { CloudStorageComponent } from '../components/cloudStorageComponent';
import { ImageService } from '../services/imageService';
import { GptComponent } from '../components/gptComponent';
import { RedisComponent } from '../components/redisComponent';
import { I18nService } from '../services/i18nService';
import { NotificationService } from '../services/notificationService';
import { NotificationRepository } from '../repositories/notificationRepository';
import { StatusPointTxsRepository } from '../repositories/statusPointTxsRepository';
import { RewardPointTxsRepository } from '../repositories/rewardPointTxsRepository';
import { StatusPointService } from '../services/statusPointService';
import { RewardPointService } from '../services/rewardPointService';
import { SerialCodeActionRepository } from '../repositories/serialCodeActionRepository';

// ----- repository -----
container.register('AccountRepository', { useClass: AccountRepository });
container.register('AchievementActionRepository', { useClass: AchievementActionRepository });
container.register('UserRepository', { useClass: UserRepository });
container.register('ActionActivityRepository', { useClass: ActionActivityRepository });
container.register('ActionRepository', { useClass: ActionRepository });
container.register('AuthProviderRepository', { useClass: AuthProviderRepository });
container.register('CertificateRewardRepository', { useClass: CertificateRewardRepository });
container.register('CheckoutRepository', { useClass: CheckoutRepository });
container.register('ClaimedRewardRepository', { useClass: ClaimedRewardRepository });
container.register('ContentPurchaseActionRepository', { useClass: ContentPurchaseActionRepository });
container.register('CouponRewardRepository', { useClass: CouponRewardRepository });
container.register('CustomFieldsRepository', { useClass: CustomFieldsRepository });
container.register('DigitalContentRewardRepository', { useClass: DigitalContentRewardRepository });
container.register('GeofenceRepository', { useClass: GeofenceRepository });
container.register('NftBaseMetadatasRepository', { useClass: NftBaseMetadatasRepository });
container.register('NftContractsRepository', { useClass: NftContractsRepository });
container.register('NftContractTypesRepository', { useClass: NftContractTypesRepository });
container.register('NftMetadatasRepository', { useClass: NftMetadatasRepository });
container.register('OnlineCheckinActionRepository', { useClass: OnlineCheckinActionRepository });
container.register('PlanRepository', { useClass: PlanRepository });
container.register('ProductRepository', { useClass: ProductRepository });
container.register('QrCheckinActionRepository', { useClass: QrCheckinActionRepository });
container.register('QuestActionRepository', { useClass: QuestActionRepository });
container.register('QuestActivityRepository', { useClass: QuestActivityRepository });
container.register('QuestionnaireRepository', { useClass: QuestionnaireRepository });
container.register('QuestionnaireActionRepository', { useClass: QuestionnaireActionRepository });
container.register('QuestionnaireQuestionAnswerRepository', { useClass: QuestionnaireQuestionAnswerRepository });
container.register('QuestionnaireQuestionRepository', { useClass: QuestionnaireQuestionRepository });
container.register('QuestionnaireResultAnswerRepository', { useClass: QuestionnaireResultAnswerRepository });
container.register('QuestionnaireResultRankRepository', { useClass: QuestionnaireResultRankRepository });
container.register('QuestionnaireThemeRepository', { useClass: QuestionnaireThemeRepository });
container.register('QuestRepository', { useClass: QuestRepository });
container.register('QuestRewardRepository', { useClass: QuestRewardRepository });
container.register('RewardRepository', { useClass: RewardRepository });
container.register('ServiceInfoRepository', { useClass: ServiceInfoRepository });
container.register('TenantRepository', { useClass: TenantRepository });
container.register('TokenBoundAccountImplementationRepository', {
  useClass: TokenBoundAccountImplementationRepository,
});
container.register('TokenBoundAccountRegistryAddressRepository', {
  useClass: TokenBoundAccountRegistryAddressRepository,
});
container.register('UserOperationQueuesRepository', { useClass: UserOperationQueuesRepository });
container.register('UserRepository', { useClass: UserRepository });
container.register('CheckoutRepository', { useClass: CheckoutRepository });
container.register('ProductRepository', { useClass: ProductRepository });
container.register('CustomFieldsRepository', { useClass: CustomFieldsRepository });
container.register('ContentPurchaseActionRepository', { useClass: ContentPurchaseActionRepository });
container.register('TenantRepository', { useClass: TenantRepository });
container.register('PlanRepository', { useClass: PlanRepository });
container.register('NftsFirestoreRepository', { useClass: NftsFirestoreRepository });
container.register('VaultTransactionQueuesRepository', { useClass: VaultTransactionQueuesRepository });
container.register('VaultKeyRepository', { useClass: VaultKeyRepository });
container.register('TransactionQueuesRepository', { useClass: TransactionQueuesRepository });
container.register('TransactionsRepository', { useClass: TransactionsRepository });
container.register('AttemptTransactionsRepository', { useClass: AttemptTransactionsRepository });
container.register('DeliveryNftsFirestoreRepository', { useClass: DeliveryNftsFirestoreRepository });
container.register('NotificationRepository', { useClass: NotificationRepository });
container.register('SerialCodeProjectsRepository', { useClass: SerialCodeProjectsRepository });
container.register('SerialCodesRepository', { useClass: SerialCodesRepository });
container.register('AccountSerialCodesRepository', { useClass: AccountSerialCodesRepository });
container.register('StatusPointTxsRepository', { useClass: StatusPointTxsRepository });
container.register('RewardPointTxsRepository', { useClass: RewardPointTxsRepository });
container.register('SerialCodeActionRepository', { useClass: SerialCodeActionRepository });

// ----- service ----
container.register('UserService', { useClass: UserService });
container.register('AuthService', { useClass: AuthService });
container.register('QuestService', { useClass: QuestService });
container.register('AccountService', { useClass: AccountService });
container.register('ActionService', { useClass: ActionService });
container.register('AuthService', { useClass: AuthService });
container.register('MetadataService', { useClass: MetadataService });
container.register('NftMintService', { useClass: NftMintService });
container.register('BulkMintService', { useClass: BulkMintService });
container.register('NftRegisterService', { useClass: NftRegisterService });
container.register('NftsService', { useClass: NftsService });
container.register('ProductService', { useClass: ProductService });
container.register('QuestionnaireService', { useClass: QuestionnaireService });
container.register('QuestService', { useClass: QuestService });
container.register('RewardService', { useClass: RewardService });
container.register('ServiceInfoService', { useClass: ServiceInfoService });
container.register('TenantsService', { useClass: TenantsService });
container.register('TransactionService', { useClass: TransactionService });
container.register('UserOperationService', { useClass: UserOperationService });
container.register('UserService', { useClass: UserService });
container.register('VaultKeyService', { useClass: VaultKeyService });
container.register('ProductService', { useClass: ProductService });
container.register('AdminService', { useClass: AdminService });
container.register('MetadataFetchService', { useClass: MetadataFetchService });
container.register('NftTransactionUpdateService', { useClass: NftTransactionUpdateService });
container.register('TenantsService', { useClass: TenantsService });
container.register('WebhookService', { useClass: WebhookService });
container.register('SerialCodeService', { useClass: SerialCodeService });
container.register('ImageService', { useClass: ImageService });
container.register('I18nService', { useClass: I18nService });
container.register('NotificationService', { useClass: NotificationService });
container.register('StatusPointService', { useClass: StatusPointService });
container.register('RewardPointService', { useClass: RewardPointService });
container.register('GeofenceService', { useClass: GeofenceService });

// ----- component ----
container.register('FirebaseComponent', { useClass: FirebaseComponent });
container.register('LineComponent', { useClass: LineComponent });
container.register('TransactionComponent', { useClass: TransactionComponent });
container.register('RpcComponent', { useClass: RpcComponent });
container.register('AlchemyComponent', { useClass: AlchemyComponent });
container.register('ViemComponent', { useClass: ViemComponent });
container.register('CloudStorageComponent', { useClass: CloudStorageComponent });
container.register('GptComponent', { useClass: GptComponent });
container.registerSingleton('RedisComponent', RedisComponent);
export { container };
