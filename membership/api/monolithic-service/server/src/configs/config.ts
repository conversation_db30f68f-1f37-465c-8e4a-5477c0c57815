import { z } from '@hono/zod-openapi';
import dotenv from 'dotenv';
import { InternalServerError } from '../errors/internalServerError';
import { isAddress } from 'viem/utils';
import { logger } from '../utils/middleware/loggerMiddleware';

// .envファイルをロード
dotenv.config();

// スキーマ定義
const envSchema = z.object({
  ACCOUNT_FACTORY_ADDRESS: z
    .string()
    .min(1, 'ACCOUNT_FACTORY_ADDRESS is required.')
    .refine(isAddress, 'ACCOUNT_FACTORY_ADDRESS is required as address format'),
  ALCHEMY_ADDRESS_ACTIVITY_WEBHOOK_ID: z.string().min(1, 'ALCHEMY_ADDRESS_ACTIVITY_WEBHOOK_ID is required.'),
  ALCHEMY_NFT_ACTIVITY_WEBHOOK_ID: z.string().min(1, 'ALCHEMY_NFT_ACTIVITY_WEBHOOK_ID is required.'),
  ALCHEMY_ADDRESS_ACTIVITY_WEBHOOK_SIGNING_KEY: z.string().min(1, 'ALCHEMY_ADDRESS_ACTIVITY_WEBHOOK_SIGNING_KEY is required.'),
  ALCHEMY_NFT_ACTIVITY_WEBHOOK_SIGNING_KEY: z.string().min(1, 'ALCHEMY_NFT_ACTIVITY_WEBHOOK_SIGNING_KEY is required.'),
  ALCHEMY_CHAIN_NAME: z.string().min(1, 'ALCHEMY_CHAIN_NAME is required.'),
  ALCHEMY_API_KEY: z.string().min(1, 'ALCHEMY_API_KEY is required.'),
  ALCHEMY_AUTH_TOKEN: z.string().min(1, 'ALCHEMY_AUTH_TOKEN is required.'),
  FIREBASE_CLIENT_EMAIL: z.string().email().min(1, 'FIREBASE_CLIENT_EMAIL is required.'),
  FIREBASE_PRIVATE_KEY: z.string().min(1, 'FIREBASE_PRIVATE_KEY is required.'),
  FIREBASE_PROJECT_ID: z.string().min(1, 'FIREBASE_PROJECT_ID is required.'),
  GCP_PROJECT_ID: z.string().min(1, 'GCP_PROJECT_ID is required.'),
  GCS_BUCKET_NAME: z.string().min(1, 'GCS_BUCKET_NAME is required.'),
  JSON_RPC_URL: z.string().min(1, 'JSON_RPC_URL is required.').url('JSON_RPC_URL is required as url format'),
  OPENAI_API_KEY: z.string().min(1, 'OPENAI_API_KEY is required.'),
  REDIS_URL: z.string().min(1, 'REDIS_URL is required.'),
  CHAIN_ID: z.string().min(1, 'CHAIN_ID is required.'),
  METADATA_URL: z.string().min(1, 'METADATA_URL is required.').url('METADATA_URL is required as url format'),
  STRIPE_WEBHOOK_SECRET: z.string().min(1, 'STRIPE_WEBHOOK_SECRET is required.'),
  RESEND_API_KEY: z.string().min(1, 'RESEND_API_KEY is required.'),
});

// 環境変数をバリデーション
const parsedEnv = envSchema.safeParse(process.env);

if (!parsedEnv.success) {
  const details = parsedEnv.error.issues.map((issue) => {
    const varName = issue.path.join('.');
    return `${varName}: ${issue.message}`;
  });
  logger.error('❌ Environment variable validation errors:');
  details.forEach((line) => console.error(`- ${line}`));

  throw new InternalServerError(`Environment validation failed:\n- ${details.join('\n- ')}`);
}

// 正常時に型安全なconfigを生成
export const config = {
  alchemyAddressWebhookId: parsedEnv.data.ALCHEMY_ADDRESS_ACTIVITY_WEBHOOK_ID,
  alchemyNftWebhookId: parsedEnv.data.ALCHEMY_NFT_ACTIVITY_WEBHOOK_ID,
  alchemyAddressSigningKey: parsedEnv.data.ALCHEMY_ADDRESS_ACTIVITY_WEBHOOK_SIGNING_KEY,
  alchemyNftSigningKey: parsedEnv.data.ALCHEMY_NFT_ACTIVITY_WEBHOOK_SIGNING_KEY,
  alchemyChainName: parsedEnv.data.ALCHEMY_CHAIN_NAME,
  alchemyApiKey: parsedEnv.data.ALCHEMY_API_KEY,
  alchemyAuthToken: parsedEnv.data.ALCHEMY_AUTH_TOKEN,
  gcpProjectId: parsedEnv.data.GCP_PROJECT_ID,
  gcsBucketName: parsedEnv.data.GCS_BUCKET_NAME,
  openaiApiKey: parsedEnv.data.OPENAI_API_KEY,
  redisUrl: parsedEnv.data.REDIS_URL,
  jsonRpcUrl: parsedEnv.data.JSON_RPC_URL,
  chainId: parsedEnv.data.CHAIN_ID,
  metadataUrl: parsedEnv.data.METADATA_URL,
  stripeWebhookSecret: parsedEnv.data.STRIPE_WEBHOOK_SECRET,
  accountFactoryAddress: parsedEnv.data.ACCOUNT_FACTORY_ADDRESS,
  firebaseProjectId: parsedEnv.data.FIREBASE_PROJECT_ID,
  firebaseClientEmail: parsedEnv.data.FIREBASE_CLIENT_EMAIL,
  firebasePrivateKey: parsedEnv.data.FIREBASE_PRIVATE_KEY.replace(/\\n/g, '\n'),
  resendApiKey: parsedEnv.data.RESEND_API_KEY,
};
