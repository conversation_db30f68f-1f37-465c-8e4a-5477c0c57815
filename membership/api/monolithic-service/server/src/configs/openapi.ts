import { swaggerUI } from '@hono/swagger-ui';
import { type OpenAPIHono } from '@hono/zod-openapi';
import { ApiAccessLevelTag, ApiCategoryTagDescriptions } from '../enum/apiTag';
import { filterOpenAPIDocByTags } from '../utils/hono_extension/filterOpenAPIDocByTags';
import { cleanUnusedTags } from '../utils/hono_extension/cleanUnusedTags';
import { cleanAccessControlTags } from '../utils/hono_extension/cleanAccessControlTags';

export function registerOpenApiSecurity(app: OpenAPIHono) {
  app.openAPIRegistry.registerComponent('securitySchemes', 'firebase', {
    type: 'http',
    scheme: 'bearer',
    bearerFormat: 'JWT',
    description: `
      * Set the token in the Authorization header in the form **Bearer {token}**
      * The token is in JWT format and validation is performed by API Gateway
    `,
  });
  app.openAPIRegistry.registerComponent('securitySchemes', 'apiKey', {
    type: 'apiKey',
    name: 'x-api-key',
    in: 'header',
  });
}

export function registerOpenApiDocs(app: OpenAPIHono) {
  const openApiFullDocs = app.getOpenAPIDocument({
    openapi: '3.0.0',
    info: { title: 'API', version: '1.3.1' },
    tags: ApiCategoryTagDescriptions,
  });

  // 外部公開あり
  app.get('/docs/full/openapi.json', (c) => {
    return c.json(openApiFullDocs, 200);
  });
  app.get('/docs/admin/openapi.json', (c) => {
    const filteredDoc = filterOpenAPIDocByTags(openApiFullDocs, [ApiAccessLevelTag.ADMIN, ApiAccessLevelTag.SYSTEM]);
    const cleanDoc = cleanUnusedTags(filteredDoc);
    const adminDoc = cleanAccessControlTags(cleanDoc);
    return c.json(adminDoc, 200);
  });
  app.get('/docs/front/openapi.json', (c) => {
    const filteredDoc = filterOpenAPIDocByTags(openApiFullDocs, [ApiAccessLevelTag.MEMBER, ApiAccessLevelTag.GUEST]);
    const cleanDoc = cleanUnusedTags(filteredDoc);
    const frontDoc = cleanAccessControlTags(cleanDoc);
    return c.json(frontDoc, 200);
  });

  // Swagger UIはLocal実行時のみ
  app.get('/docs/admin/doc', swaggerUI({ url: '/docs/admin/openapi.json' }));
  app.get('/docs/front/doc', swaggerUI({ url: '/docs/front/openapi.json' }));
}
