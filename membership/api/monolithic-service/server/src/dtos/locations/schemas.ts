import { z } from '@hono/zod-openapi';

// =======================
// Geofence Type Enum
// =======================
export enum GeofenceType {
  CIRCLE = 'CIRCLE',
  POLYGON = 'POLYGON',
}

export const GeofenceTypeSchema = z.nativeEnum(GeofenceType).openapi({
  description: 'Type of geofence geometry',
  example: GeofenceType.CIRCLE
});

// =======================
// Location Coordinates
// =======================
export const LocationCoordinatesSchema = z.object({
  latitude: z.number()
    .min(-90, 'Latitude must be between -90 and 90')
    .max(90, 'Latitude must be between -90 and 90')
    .openapi({
      description: 'Latitude coordinate in decimal degrees',
      example: 35.6762,
      minimum: -90,
      maximum: 90
    }),
  longitude: z.number()
    .min(-180, 'Longitude must be between -180 and 180')
    .max(180, 'Longitude must be between -180 and 180')
    .openapi({
      description: 'Longitude coordinate in decimal degrees',
      example: 139.6503,
      minimum: -180,
      maximum: 180
    }),
}).openapi('LocationCoordinates');

export type LocationCoordinates = z.infer<typeof LocationCoordinatesSchema>;

// =======================
// Base Geofence Schema
// =======================
export const BaseGeofenceSchema = z.object({
  geofenceType: GeofenceTypeSchema.openapi({
    description: 'Type of geofence geometry'
  }),
});

// =======================
// Circle Geofence Schema
// =======================
export const CircleGeofenceSchema = BaseGeofenceSchema.extend({
  geofenceType: z.literal(GeofenceType.CIRCLE),
  center: LocationCoordinatesSchema.openapi({
    description: 'Center coordinates of the circle'
  }),
  radiusMeters: z.number().min(1).openapi({
    description: 'Radius of the circle in meters',
    example: 100
  })
}).openapi('CircleGeofence');

// =======================
// Polygon Geofence Schema
// =======================
export const PolygonGeofenceSchema = BaseGeofenceSchema.extend({
  geofenceType: z.literal(GeofenceType.POLYGON),
  coordinates: z.array(LocationCoordinatesSchema).min(3).openapi({
    description: 'Array of coordinate pairs forming a polygon (minimum 3 points)',
    example: [
      { latitude: 35.6812, longitude: 139.7671 },
      { latitude: 35.6812, longitude: 139.7681 },
      { latitude: 35.6822, longitude: 139.7681 },
      { latitude: 35.6822, longitude: 139.7671 },
      { latitude: 35.6812, longitude: 139.7671 }
    ]
  })
}).openapi('PolygonGeofence');

// =======================
// Geofence Schema
// =======================
export const GeofenceSchema = z.discriminatedUnion('geofenceType', [
  CircleGeofenceSchema,
  PolygonGeofenceSchema
]).openapi('Geofence');

// =======================
// Location Geofence Schema
// =======================
export const LocationGeofenceSchema = z.object({
  slug: z.string().min(1).openapi({
    description: 'Slug for the geofence',
    example: 'tokyo-station'
  }),
  geofence: GeofenceSchema.openapi({
    description: 'Geofence geometry data'
  })
}).openapi('LocationGeofence');

export type LocationGeofence = z.infer<typeof LocationGeofenceSchema>;

// =======================
// Location Geofence with ID Schema
// =======================
export const LocationGeofenceIdSchema = LocationGeofenceSchema.extend({
  geofenceId: z.string().uuid().openapi({
    description: 'Unique identifier for the geofence',
    example: '5850b40d-333b-4fbe-8c87-8e34d0f47404'
  })
}).openapi('LocationGeofenceId');

export type LocationGeofenceId = z.infer<typeof LocationGeofenceIdSchema>;

// =======================
// Get Location Geofences Response Schema
// =======================
export const GetLocationGeofencesSchema = z.object({
  geofences: z.array(LocationGeofenceIdSchema).openapi({
    description: 'Array of geofences'
  })
}).openapi('GetLocationGeofences');

export type GetLocationGeofences = z.infer<typeof GetLocationGeofencesSchema>;
