import { LocationGeofenceSchema } from './schemas';

export const PostLocationGeofenceRequestBodySchema = {
  description: 'Geofence data to create',
  required: true,
  content: {
    'application/json': {
      schema: LocationGeofenceSchema,
    },
  },
};

export const PutLocationGeofenceRequestBodySchema = {
  description: 'Updated geofence data',
  required: true,
  content: {
    'application/json': {
      schema: LocationGeofenceSchema,
    },
  },
};
