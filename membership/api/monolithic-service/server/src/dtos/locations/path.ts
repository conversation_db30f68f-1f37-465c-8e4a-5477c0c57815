import { z } from '@hono/zod-openapi';
import { regexPattern } from '../../utils/regex';

export const PathParameterGeofenceIdSchema = z.object({
  geofenceId: z
    .string()
    .regex(regexPattern.uuid, 'Invalid UUID v4')
    .openapi({
      param: { name: 'geofenceId', in: 'path', required: true },
      example: '5850b40d-333b-4fbe-8c87-8e34d0f47404',
      description: 'Uniquely given identifier for each geofence',
    }),
});

export type PathParameterGeofenceId = z.infer<typeof PathParameterGeofenceIdSchema>;
