import {
  LocationGeofenceIdSchema,
  GetLocationGeofencesSchema,
} from './schemas';

export const PostLocationGeofenceResponseSchema = {
  description: 'Geofence created successfully',
  content: {
    'application/json': {
      schema: LocationGeofenceIdSchema,
    },
  },
};

export const PutLocationGeofenceResponseSchema = {
  description: 'Geofence updated successfully',
  content: {
    'application/json': {
      schema: LocationGeofenceIdSchema,
    },
  },
};

export const GetLocationGeofenceResponseSchema = {
  description: 'Geofence retrieved successfully',
  content: {
    'application/json': {
      schema: LocationGeofenceIdSchema,
    },
  },
};

export const GetLocationGeofencesResponseSchema = {
  description: 'Geofences retrieved successfully',
  content: {
    'application/json': {
      schema: GetLocationGeofencesSchema,
    },
  },
};
