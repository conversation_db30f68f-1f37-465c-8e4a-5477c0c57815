import { z } from '@hono/zod-openapi';
import { regexPattern } from '../../utils/regex';
import { pointTxDetail, pointTxDetailSchema } from '../../enum/pointTxDetail';
import { pointOpsTypeSchema } from '../../enum/pointOpsType';
import { pointOpsActorSchema } from '../../enum/pointOpsActor';
import { OperationStatus } from '../../enum/operationStatus';

export const expireRewardPointsResponseSchema = z
  .object({
    status: z.nativeEnum(OperationStatus).openapi({
      description: 'process status',
      example: OperationStatus.SUCCESS,
    }),
    startedAt: z.string().regex(regexPattern.isoDateTime).openapi({
      description: 'batch start time',
      example: '2024-01-01T10:00:00Z',
    }),
    endedAt: z.string().regex(regexPattern.isoDateTime).openapi({
      description: 'batch end time',
      example: '2024-01-01T10:00:00Z',
    }),
    processedCount: z.number().openapi({
      description: 'number of processed accounts',
      example: 100,
    }),
    failedAccounts: z
      .array(
        z.object({
          accountId: z.string().uuid(),
          reason: z.string(),
        }),
      )
      .openapi({
        description: 'error details',
        example: [
          {
            accountId: 'acc-102',
            reason: 'INSERT failed: unique constraint violation',
          },
          {
            accountId: 'acc-133',
            reason: 'lock timeout after 5000ms',
          },
        ],
      }),
  })
  .openapi('expireRewardPointsResponse');

export type ExpireRewardPointsResponse = z.infer<typeof expireRewardPointsResponseSchema>;

export const basePointTxSchema = z.object({
  service_id: z.string().uuid().openapi({
    description: 'Service ID',
    example: '123e4567-e89b-12d3-a456-************',
  }),
  account_id: z.string().uuid().openapi({
    description: 'Account ID',
    example: '123e4567-e89b-12d3-a456-************',
  }),
  amount: z
    .number()
    .int()
    .refine((val) => val !== 0, {
      message: 'amount should not be 0',
    })
    .openapi({
      description: 'Amount',
      example: 100,
    }),
  ops_type: pointOpsTypeSchema,
  tx_by: pointOpsActorSchema,
  tx_detail: pointTxDetailSchema,
  tx_extra: z.string().uuid().optional().openapi({
    description: 'The ID of the transaction that caused the point change (ex. reward_id, gacha_id)',
    example: '123e4567-e89b-12d3-a456-************',
  }),
  created_at: z.string().regex(regexPattern.isoDateTime).openapi({
    description: 'Creation date',
    example: '2024-01-01T10:00:00Z',
  }),
});

export const rewardPointTxSchema = basePointTxSchema
  .merge(
    z.object({
      reward_point_tx_id: z.string().uuid().openapi({
        description: 'Reward point transaction ID',
        example: '123e4567-e89b-12d3-a456-************',
      }),
      expires_on: z.string().regex(regexPattern.isoDateTime).openapi({
        description: 'Expiration date',
        example: '2024-01-01T10:00:00Z',
      }),
    }),
  )
  .superRefine((data, ctx) => {
    if ((data.tx_detail === pointTxDetail.REWARD || data.tx_detail === pointTxDetail.GACHA) && !data.tx_extra) {
      ctx.addIssue({
        path: ['tx_extra'],
        code: z.ZodIssueCode.custom,
        message: `'tx_extra' is required when 'tx_detail' is ${data.tx_detail}`,
      });
    }
  })
  .openapi('RewardPointTx');
export type RewardPointTx = z.infer<typeof rewardPointTxSchema>;

export const statusPointTxSchema = basePointTxSchema
  .merge(
    z.object({
      status_point_tx_id: z.string().uuid().openapi({
        description: 'Status point transaction ID',
        example: '123e4567-e89b-12d3-a456-************',
      }),
    }),
  )
  .superRefine((data, ctx) => {
    if ((data.tx_detail === pointTxDetail.REWARD || data.tx_detail === pointTxDetail.GACHA) && !data.tx_extra) {
      ctx.addIssue({
        path: ['tx_extra'],
        code: z.ZodIssueCode.custom,
        message: `'tx_extra' is required when 'tx_detail' is ${data.tx_detail}`,
      });
    }
  });
export type StatusPointTx = z.infer<typeof statusPointTxSchema>;

export const getTotalPointResponseSchema = z.object({
  total_points: z.number().int().openapi({
    description: 'Total points',
    example: 100,
  }),
});
export type GetTotalPointResponse = z.infer<typeof getTotalPointResponseSchema>;
