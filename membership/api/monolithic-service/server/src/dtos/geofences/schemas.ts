import { z } from 'zod';
import { openApiRegistry } from '../../utils/openApiRegistry';

// =======================
// Base Geofence Schemas
// =======================
export const GeofenceTypeSchema = z.enum(['CIRCLE', 'POLYGON']).openapi({
  description: 'Type of geofence geometry',
  example: 'CIRCLE'
});

export type GeofenceType = z.infer<typeof GeofenceTypeSchema>;

// =======================
// Create Geofence Request (TABLE_GEOFENCES structure)
// =======================
export const CreateGeofenceRequestSchema = z.object({
  name: z.string().min(1).max(255).openapi({
    description: 'Name of the geofence',
    example: 'Tokyo Station'
  }),
  description: z.string().optional().openapi({
    description: 'Description of the geofence',
    example: 'Check-in location for Tokyo Station quest'
  }),
  geofenceType: GeofenceTypeSchema.openapi({
    description: 'Type of geofence (CIRCLE or POLYGON)'
  }),
  centerCoordinateLatitude: z.string().optional().openapi({
    description: 'Center latitude coordinate as string (for CIRCLE type)',
    example: '35.6812'
  }),
  centerCoordinateLongitude: z.string().optional().openapi({
    description: 'Center longitude coordinate as string (for CIRCLE type)',
    example: '139.7671'
  }),
  centerPinName: z.string().optional().openapi({
    description: 'Name of the center pin/landmark',
    example: 'Tokyo Station Main Entrance'
  }),
  circleRadius: z.string().optional().openapi({
    description: 'Radius in meters as string (for CIRCLE type)',
    example: '100'
  }),
  polygonCoordinates: z.array(
    z.object({
      latitude: z.string().openapi({
        description: 'Latitude coordinate as string',
        example: '35.6812'
      }),
      longitude: z.string().openapi({
        description: 'Longitude coordinate as string',
        example: '139.7671'
      })
    })
  ).min(3).optional().openapi({
    description: 'Array of coordinate points for POLYGON type (minimum 3 points)',
    example: [
      { latitude: '35.6812', longitude: '139.7671' },
      { latitude: '35.6822', longitude: '139.7681' },
      { latitude: '35.6832', longitude: '139.7671' },
      { latitude: '35.6812', longitude: '139.7671' }
    ]
  })
}).superRefine((data, ctx) => {
  // Validation for CIRCLE type
  if (data.geofenceType === 'CIRCLE') {
    if (!data.centerCoordinateLatitude) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'centerCoordinateLatitude is required for CIRCLE geofence',
        path: ['centerCoordinateLatitude']
      });
    }
    if (!data.centerCoordinateLongitude) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'centerCoordinateLongitude is required for CIRCLE geofence',
        path: ['centerCoordinateLongitude']
      });
    }
    if (!data.circleRadius) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'circleRadius is required for CIRCLE geofence',
        path: ['circleRadius']
      });
    }
  }
  
  // Validation for POLYGON type
  if (data.geofenceType === 'POLYGON') {
    if (!data.polygonCoordinates || data.polygonCoordinates.length < 3) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'polygonCoordinates with minimum 3 points is required for POLYGON geofence',
        path: ['polygonCoordinates']
      });
    }
  }
}).openapi('CreateGeofenceRequest');

export type CreateGeofenceRequest = z.infer<typeof CreateGeofenceRequestSchema>;

// =======================
// Update Geofence Request
// =======================
export const UpdateGeofenceRequestSchema = CreateGeofenceRequestSchema.partial().openapi('UpdateGeofenceRequest');

export type UpdateGeofenceRequest = z.infer<typeof UpdateGeofenceRequestSchema>;

// =======================
// Geofence Response
// =======================
export const GeofenceResponseSchema = z.object({
  geofenceId: z.string().openapi({
    description: 'Unique identifier for the geofence',
    example: 'geofence-123'
  }),
  serviceId: z.string().openapi({
    description: 'Service identifier',
    example: 'service-123'
  }),
  name: z.string().openapi({
    description: 'Name of the geofence',
    example: 'Tokyo Station'
  }),
  description: z.string().optional().openapi({
    description: 'Description of the geofence'
  }),
  geofenceType: GeofenceTypeSchema.openapi({
    description: 'Type of geofence geometry'
  }),
  centerCoordinateLatitude: z.string().optional().openapi({
    description: 'Center latitude coordinate (for CIRCLE type)'
  }),
  centerCoordinateLongitude: z.string().optional().openapi({
    description: 'Center longitude coordinate (for CIRCLE type)'
  }),
  centerPinName: z.string().optional().openapi({
    description: 'Name of the center pin/landmark'
  }),
  circleRadius: z.string().optional().openapi({
    description: 'Radius in meters (for CIRCLE type)'
  }),
  polygonCoordinates: z.array(
    z.object({
      latitude: z.string(),
      longitude: z.string()
    })
  ).optional().openapi({
    description: 'Array of coordinate points (for POLYGON type)'
  }),
  isActive: z.boolean().openapi({
    description: 'Whether the geofence is active',
    example: true
  }),
  createdAt: z.string().datetime().openapi({
    description: 'Creation timestamp',
    example: '2023-01-01T00:00:00Z'
  }),
  updatedAt: z.string().datetime().openapi({
    description: 'Last update timestamp',
    example: '2023-01-01T00:00:00Z'
  })
}).openapi('GeofenceResponse');

export type GeofenceResponse = z.infer<typeof GeofenceResponseSchema>;

// =======================
// Geofence List Response
// =======================
export const GeofenceListResponseSchema = z.object({
  geofences: z.array(GeofenceResponseSchema).openapi({
    description: 'Array of geofences'
  }),
  total: z.number().openapi({
    description: 'Total number of geofences',
    example: 10
  }),
  page: z.number().optional().openapi({
    description: 'Current page number',
    example: 1
  }),
  limit: z.number().optional().openapi({
    description: 'Number of items per page',
    example: 20
  })
}).openapi('GeofenceListResponse');

export type GeofenceListResponse = z.infer<typeof GeofenceListResponseSchema>;

// =======================
// Geofence Query Parameters
// =======================
export const GeofenceQuerySchema = z.object({
  page: z.string().transform(Number).pipe(z.number().min(1)).optional().openapi({
    description: 'Page number for pagination',
    example: '1'
  }),
  limit: z.string().transform(Number).pipe(z.number().min(1).max(100)).optional().openapi({
    description: 'Number of items per page (max 100)',
    example: '20'
  }),
  geofenceType: GeofenceTypeSchema.optional().openapi({
    description: 'Filter by geofence type'
  }),
  search: z.string().optional().openapi({
    description: 'Search by name or description',
    example: 'Tokyo'
  }),
  isActive: z.string().transform((val) => val === 'true').optional().openapi({
    description: 'Filter by active status',
    example: 'true'
  })
}).openapi('GeofenceQuery');

export type GeofenceQuery = z.infer<typeof GeofenceQuerySchema>;

// Register schemas with OpenAPI
openApiRegistry.register('GeofenceType', GeofenceTypeSchema);
openApiRegistry.register('CreateGeofenceRequest', CreateGeofenceRequestSchema);
openApiRegistry.register('UpdateGeofenceRequest', UpdateGeofenceRequestSchema);
openApiRegistry.register('GeofenceResponse', GeofenceResponseSchema);
openApiRegistry.register('GeofenceListResponse', GeofenceListResponseSchema);
openApiRegistry.register('GeofenceQuery', GeofenceQuerySchema);
