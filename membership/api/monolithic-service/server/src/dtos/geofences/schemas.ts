import { z } from 'zod';
import { regexPattern } from '../../utils/regexPattern';

// =======================
// Geofence Type Enum
// =======================
export enum GeofenceType {
  CIRCLE = 'CIRCLE',
  POLYGON = 'POLYGON',
}

export const GeofenceTypeSchema = z.nativeEnum(GeofenceType).openapi({
  description: 'Type of geofence geometry',
  example: GeofenceType.CIRCLE
});

// =======================
// Location Coordinates
// =======================
export const LocationCoordinatesSchema = z.object({
  latitude: z.number()
    .min(-90, 'Latitude must be between -90 and 90')
    .max(90, 'Latitude must be between -90 and 90')
    .openapi({
      description: 'Latitude coordinate in decimal degrees',
      example: 35.6762,
      minimum: -90,
      maximum: 90
    }),
  longitude: z.number()
    .min(-180, 'Longitude must be between -180 and 180')
    .max(180, 'Longitude must be between -180 and 180')
    .openapi({
      description: 'Longitude coordinate in decimal degrees',
      example: 139.6503,
      minimum: -180,
      maximum: 180
    }),
}).openapi('LocationCoordinates');

export type LocationCoordinates = z.infer<typeof LocationCoordinatesSchema>;

// =======================
// Base Geofence Schema
// =======================
export const BaseGeofenceSchema = z.object({
  geofenceType: GeofenceTypeSchema.openapi({
    description: 'Type of geofence geometry'
  }),
});

// =======================
// Circle Geofence Schema
// =======================
export const CircleGeofenceSchema = BaseGeofenceSchema.extend({
  geofenceType: z.literal(GeofenceType.CIRCLE),
  center: LocationCoordinatesSchema.openapi({
    description: 'Center coordinates of the circle'
  }),
  radiusMeters: z.number().min(1).openapi({
    description: 'Radius of the circle in meters',
    example: 100
  })
}).openapi('CircleGeofence');

// =======================
// Polygon Geofence Schema
// =======================
export const PolygonGeofenceSchema = BaseGeofenceSchema.extend({
  geofenceType: z.literal(GeofenceType.POLYGON),
  coordinates: z.array(LocationCoordinatesSchema).min(3).openapi({
    description: 'Array of coordinate pairs forming a polygon (minimum 3 points)',
    example: [
      { latitude: 35.6812, longitude: 139.7671 },
      { latitude: 35.6812, longitude: 139.7681 },
      { latitude: 35.6822, longitude: 139.7681 },
      { latitude: 35.6822, longitude: 139.7671 },
      { latitude: 35.6812, longitude: 139.7671 }
    ]
  })
}).openapi('PolygonGeofence');

// =======================
// Geofence Schema
// =======================
export const GeofenceSchema = z.discriminatedUnion('geofenceType', [
  CircleGeofenceSchema,
  PolygonGeofenceSchema
]).openapi('Geofence');

// =======================
// Location Geofence Schema
// =======================
export const LocationGeofenceSchema = z.object({
  name: z.string().min(1).max(255).openapi({
    description: 'Name of the geofence',
    example: 'Tokyo Station'
  }),
  geofence: GeofenceSchema.openapi({
    description: 'Geofence geometry data'
  })
}).openapi('LocationGeofence');

export type LocationGeofence = z.infer<typeof LocationGeofenceSchema>;

// =======================
// Location Geofence with ID Schema
// =======================
export const LocationGeofenceIdSchema = LocationGeofenceSchema.merge({
  geofenceId: z.string().uuid().openapi({
    description: 'Unique identifier for the geofence',
    example: '5850b40d-333b-4fbe-8c87-8e34d0f47404'
  })
}).openapi('LocationGeofenceId');

export type LocationGeofenceId = z.infer<typeof LocationGeofenceIdSchema>;

// =======================
// Path Parameter Schema
// =======================
export const PathParameterGeofenceIdSchema = z.object({
  geofenceId: z.string().regex(regexPattern.uuid).openapi({
    description: 'Uniquely given identifier for each geofence',
    example: '5850b40d-333b-4fbe-8c87-8e34d0f47404',
    pattern: regexPattern.uuid.source,
  }),
}).openapi('PathParameterGeofenceId');

export type PathParameterGeofenceId = z.infer<typeof PathParameterGeofenceIdSchema>;

// =======================
// Request Schemas
// =======================
export const PostLocationGeofenceRequestSchema = LocationGeofenceSchema.openapi('PostLocationGeofenceRequest');
export type PostLocationGeofenceRequest = z.infer<typeof PostLocationGeofenceRequestSchema>;

export const PutLocationGeofenceRequestSchema = LocationGeofenceSchema.openapi('PutLocationGeofenceRequest');
export type PutLocationGeofenceRequest = z.infer<typeof PutLocationGeofenceRequestSchema>;

// =======================
// Response Schemas
// =======================
export const PostLocationGeofenceResponseSchema = LocationGeofenceIdSchema.openapi('PostLocationGeofenceResponse');
export type PostLocationGeofenceResponse = z.infer<typeof PostLocationGeofenceResponseSchema>;

export const PutLocationGeofenceResponseSchema = LocationGeofenceIdSchema.openapi('PutLocationGeofenceResponse');
export type PutLocationGeofenceResponse = z.infer<typeof PutLocationGeofenceResponseSchema>;

export const GetLocationGeofenceResponseSchema = LocationGeofenceIdSchema.openapi('GetLocationGeofenceResponse');
export type GetLocationGeofenceResponse = z.infer<typeof GetLocationGeofenceResponseSchema>;

export const GetLocationGeofencesResponseSchema = z.object({
  geofences: z.array(LocationGeofenceIdSchema).openapi({
    description: 'Array of geofences'
  })
}).openapi('GetLocationGeofencesResponse');
export type GetLocationGeofencesResponse = z.infer<typeof GetLocationGeofencesResponseSchema>;


import { openApiRegistry } from '../../utils/openApiRegistry';

// =======================
// Export Types
// =======================
export type CircleGeofence = z.infer<typeof CircleGeofenceSchema>;
export type PolygonGeofence = z.infer<typeof PolygonGeofenceSchema>;
export type Geofence = z.infer<typeof GeofenceSchema>;

// =======================
// Register schemas with OpenAPI
// =======================
openApiRegistry.register('GeofenceType', GeofenceTypeSchema);
openApiRegistry.register('LocationCoordinates', LocationCoordinatesSchema);
openApiRegistry.register('BaseGeofence', BaseGeofenceSchema);
openApiRegistry.register('CircleGeofence', CircleGeofenceSchema);
openApiRegistry.register('PolygonGeofence', PolygonGeofenceSchema);
openApiRegistry.register('Geofence', GeofenceSchema);
openApiRegistry.register('LocationGeofence', LocationGeofenceSchema);
openApiRegistry.register('LocationGeofenceId', LocationGeofenceIdSchema);
openApiRegistry.register('PathParameterGeofenceId', PathParameterGeofenceIdSchema);
openApiRegistry.register('PostLocationGeofenceRequest', PostLocationGeofenceRequestSchema);
openApiRegistry.register('PutLocationGeofenceRequest', PutLocationGeofenceRequestSchema);
openApiRegistry.register('PostLocationGeofenceResponse', PostLocationGeofenceResponseSchema);
openApiRegistry.register('PutLocationGeofenceResponse', PutLocationGeofenceResponseSchema);
openApiRegistry.register('GetLocationGeofenceResponse', GetLocationGeofenceResponseSchema);
openApiRegistry.register('GetLocationGeofencesResponse', GetLocationGeofencesResponseSchema);
