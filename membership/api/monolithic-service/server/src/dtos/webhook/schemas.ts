import { z } from '@hono/zod-openapi';
import { regexPattern } from '../../utils/regex';
import { WebhookType } from '../../enum/webhookType';
import { BigNumber } from '@ethersproject/bignumber';

// =======================
// WebhookTransactionResponse
// =======================
export const WebhookTransactionResponseSchema = z
  .object({
    success: z
      .boolean()
      .openapi({ description: 'Webhook processing result' })
      .optional(),
    message: z
      .string()
      .openapi({ description: 'Processing result message or error details' })
      .optional(),
    timestamp: z
      .string()
      .regex(regexPattern.isoDateTime)
      .openapi({
        description: 'Processing timestamp',
        format: 'date-time',
        pattern: regexPattern.isoDateTime.source,
      })
      .optional(),
    transactionHash: z
      .string()
      .regex(regexPattern.blockChainHash)
      .openapi({
        description: 'Transaction hash',
        pattern: regexPattern.blockChainHash.source,
      })
      .optional(),
  })
  .openapi('WebhookTransactionResponse')

export type WebhookTransactionResponse = z.infer<
  typeof WebhookTransactionResponseSchema
>

// =======================
// Log
// =======================
export const LogSchema = z
  .object({
    blockNumber: z.number(),
    blockHash: z.string(),
    transactionIndex: z.number(),
    removed: z.boolean(),
    address: z.string(),
    data: z.string(),
    topics: z.array(z.string()),
    transactionHash: z.string(),
    logIndex: z.number(),
  })
  .openapi('Log');
export type Log = z.infer<typeof LogSchema>;

// =======================
// TransactionReceipt
// =======================
export const TransactionReceiptSchema = z
  .object({
    to: z.string(),
    from: z.string(),
    contractAddress: z.string().nullable(),
    transactionIndex: z.number(),
    root: z.string().optional(),
    gasUsed: z.instanceof(BigNumber),
    logsBloom: z.string(),
    blockHash: z.string(),
    transactionHash: z.string(),
    logs: z.array(LogSchema),
    blockNumber: z.number(),
    confirmations: z.number(),
    cumulativeGasUsed: z.instanceof(BigNumber),
    effectiveGasPrice: z.instanceof(BigNumber),
    byzantium: z.boolean(),
    type: z.number(),
    status: z.number().optional(),
  })
  .openapi('TransactionReceipt');
export type TransactionReceipt = z.infer<typeof TransactionReceiptSchema>;

// =======================
// CreatedWebhookResponse
// =======================
export const CreatedWebhookResponseSchema = z
  .object({
    webhookId: z.string(),
    webhookType: z.nativeEnum(WebhookType),
    signingKey: z.string(),
  })
  .openapi('CreatedWebhookResponse');
export type CreatedWebhookResponse = z.infer<typeof CreatedWebhookResponseSchema>;

// =======================
// WebhookBase
// =======================
export const WebhookBaseSchema = z
  .object({
    webhookId: z.string(),
    id: z.string(),
    createdAt: z.string(),
  })
  .openapi('WebhookBase');
export type WebhookBase = z.infer<typeof WebhookBaseSchema>;

// =======================
// GraphQLTransactionLog
// =======================
export const GraphQLTransactionLogSchema = z
  .object({
    index: z.number(),
    topics: z.array(z.string()),
    data: z.string(),
  })
  .openapi('GraphQLTransactionLog');
export type GraphQLTransactionLog = z.infer<typeof GraphQLTransactionLogSchema>;
// =======================
// GraphQLTransaction
// =======================
export const GraphQLTransactionSchema = z
  .object({
    hash: z.string(),
    nonce: z.number(),
    from: z.object({ address: z.string() }),
    to: z.object({ address: z.string() }).nullable(),
    createdContract: z.object({ address: z.string() }).nullable(),
    gasUsed: z.number(),
    gasPrice: z.string(),
    status: z.number(),
    logs: z.array(GraphQLTransactionLogSchema),
  })
  .openapi('GraphQLTransaction');
export type GraphQLTransaction = z.infer<typeof GraphQLTransactionSchema>;  

// =======================
// GraphQLBlock
// =======================
export const GraphQLBlockSchema = z
  .object({
    hash: z.string(),
    number: z.number(),
    timestamp: z.number(),
    transactions: z.array(GraphQLTransactionSchema),
  })
  .openapi('GraphQLBlock');
export type GraphQLBlock = z.infer<typeof GraphQLBlockSchema>;

// =======================
// GraphQLEventData
// =======================
export const GraphQLEventDataSchema = z.object({ block: GraphQLBlockSchema }).openapi('GraphQLEventData');
export type GraphQLEventData = z.infer<typeof GraphQLEventDataSchema>;

// =======================
// GraphQLWebhookEvent
// =======================
export const GraphQLWebhookEventSchema = z
  .object({
    data: GraphQLEventDataSchema,
    sequenceNumber: z.string(),
    network: z.string(),
  })
  .openapi('GraphQLWebhookEvent');
export type GraphQLWebhookEvent = z.infer<typeof GraphQLWebhookEventSchema>;
// =======================
// ActivityRawContract
// =======================
export const ActivityRawContractSchema = z
  .object({
    rawValue: z.string().optional(),
    address: z.string().optional(),
    decimals: z.number().optional(),
  })
  .openapi('ActivityRawContract');
export type ActivityRawContract = z.infer<typeof ActivityRawContractSchema>;
// =======================
// ActivityLog
// =======================
export const ActivityLogSchema = z
  .object({
    address: z.string(),
    blockHash: z.string(),
    blockNumber: z.string(),
    data: z.string(),
    logIndex: z.string(),
    removed: z.boolean(),
    topics: z.array(z.string()),
    transactionIndex: z.string(),
    transactionHash: z.string(),
  })
  .openapi('ActivityLog');
export type ActivityLog = z.infer<typeof ActivityLogSchema>;

// =======================
// NFTActivity
// =======================
export const NFTActivitySchema = z
  .object({
    blockNum: z.string(),
    hash: z.string(),
    fromAddress: z.string(),
    toAddress: z.string(),
    contractAddress: z.string(),
    value: z.number().optional(),
    erc721TokenId: z.string().optional(),
    erc1155Metadata: z
      .array(
        z.object({
          tokenId: z.string(),
          value: z.string(),
        }),
      )
      .optional(),
    asset: z.string().optional(),
    category: z.string().optional(),
    rawContract: ActivityRawContractSchema.optional(),
    typeTraceAddress: z.string().nullable().optional(),
    log: ActivityLogSchema,
  })
  .openapi('NFTActivity');
export type NFTActivity = z.infer<typeof NFTActivitySchema>;
// =======================
// AddressActivity
// =======================
export const AddressActivitySchema = z
  .object({
    fromAddress: z.string(),
    toAddress: z.string().optional(),
    blockNum: z.string(),
    hash: z.string(),
    value: z.number(),
    asset: z.string().optional(),
    category: z.string(),
    rawContract: ActivityRawContractSchema.optional(),
    erc721TokenId: z.string().nullable().optional(),
    erc1155Metadata: z
      .array(
        z.object({
          tokenId: z.string(),
          value: z.string(),
        }),
      )
      .optional(),
    log: ActivityLogSchema.optional(),
  })
  .openapi('AddressActivity');
export type AddressActivity = z.infer<typeof AddressActivitySchema>;

// =======================
// NFTActivityWebhookEvent
// =======================
export const NFTActivityWebhookEventSchema = z
  .object({
    network: z.string(),
    activity: z.array(NFTActivitySchema),
    source: z.string(),
  })
  .openapi('NFTActivityWebhookEvent');
export type NFTActivityWebhookEvent = z.infer<typeof NFTActivityWebhookEventSchema>;

// =======================
// AddressActivityWebhookEvent
// =======================
export const AddressActivityWebhookEventSchema = z
  .object({
    network: z.string(),
    activity: z.array(AddressActivitySchema),
    source: z.string(),
  })
  .openapi('AddressActivityWebhookEvent');
export type AddressActivityWebhookEvent = z.infer<typeof AddressActivityWebhookEventSchema>;
// =======================
// GraphQLWebhook
// =======================
export const GraphQLWebhookSchema = WebhookBaseSchema.merge(
  z.object({
    type: z.literal(WebhookType.GRAPHQL),
    event: GraphQLWebhookEventSchema,
  }),
).openapi('GraphQLWebhook');
export type GraphQLWebhook = z.infer<typeof GraphQLWebhookSchema>;
// =======================
// NFTActivityWebhook
// =======================
export const NFTActivityWebhookSchema = WebhookBaseSchema.merge(
  z.object({
    type: z.literal(WebhookType.NFT_ACTIVITY),
    event: NFTActivityWebhookEventSchema,
  }),
).openapi('NFTActivityWebhook');
export type NFTActivityWebhook = z.infer<typeof NFTActivityWebhookSchema>;
// =======================
// AddressActivityWebhook
// =======================
export const AddressActivityWebhookSchema = WebhookBaseSchema.merge(
  z.object({
    type: z.literal(WebhookType.ADDRESS_ACTIVITY),
    event: AddressActivityWebhookEventSchema,
  }),
).openapi('AddressActivityWebhook');
export type AddressActivityWebhook = z.infer<typeof AddressActivityWebhookSchema>;
// =======================
// WebhookResponse (union)
// =======================
export const WebhookRequestSchema = z
  .discriminatedUnion('type', [GraphQLWebhookSchema, NFTActivityWebhookSchema, AddressActivityWebhookSchema])
  .openapi('WebhookResponse');
export type WebhookRequest = z.infer<typeof WebhookRequestSchema>;
