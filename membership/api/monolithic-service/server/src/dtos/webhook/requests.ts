import { z } from '@hono/zod-openapi';
import { WebhookRequestSchema } from './schemas';

// =======================
// WebhookReceiveTransactionRequest
// =======================
export const webhookReceiveTransactionRequestBody = {
  description: 'Transaction receipt',
  required: true,
  content: {
    'application/json': {
      schema: z.any(),
    },
  },
};

export const handleStripeEventsRequestBody = {
  description: 'Stripe events',
  required: true,
  content: {
    'application/json': {
      schema: z.any(),
    },
  },
};
