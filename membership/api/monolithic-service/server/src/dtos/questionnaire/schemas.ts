import { z } from '@hono/zod-openapi';
import { languageCodeSchema } from '../../enum/languageCode';
import { QuestionType } from '../../enum/questionTypeEnum';
import { QuestionnaireType } from '../../enum/questionnaireTypeEnum';
import { regexPattern } from '../../utils/regex';

// Generic UUID pattern (without version constraint)
const genericUuidPattern = /^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$/;
const QuestionTypeSchema = z.nativeEnum(QuestionType).openapi({ description: 'Type of the question' });
const QuestionnaireTypeSchema = z.nativeEnum(QuestionnaireType).openapi({ example: 'QUIZ' });

// =======================
// QuestionValidation
// =======================
export const QuestionValidationSchema = z
  .object({
    validationOrder: z.number().int().min(0).openapi({ minimum: 0 }),
    validationRegex: z.string().min(2).openapi({ minLength: 2 }),
    errorMessage: z.string().min(1).openapi({ minLength: 1 }),
  })
  .openapi('QuestionValidation');

export type QuestionValidation = z.infer<typeof QuestionValidationSchema>;
// =======================
// SingleChoiceQuestionExtra
// =======================
export const SingleChoiceQuestionExtraSchema = z
  .object({
    type: z.literal(QuestionType.SINGLE_CHOICE),
    minimumSelectable: z
      .number()
      .min(1)
      .max(1)
      .optional()
      .openapi({ description: 'Minimum number of selections required', example: 1 }),
    maximumSelectable: z
      .number()
      .min(1)
      .max(1)
      .optional()
      .openapi({ description: 'Maximum number of selections allowed', example: 3 }),
    selections: z
      .array(
        z
          .object({
            order: z.number().int().min(0).openapi({ minimum: 0 }),
            selectionText: z.string().min(1).openapi({ minLength: 1 }),
          })
          .openapi('SingleChoiceSelection'),
      )
      .openapi({ description: 'questionType is SINGLE-CHOICE' }),
  })
  .openapi('SingleChoiceQuestionExtra');

export type SingleChoiceQuestionExtra = z.infer<typeof SingleChoiceQuestionExtraSchema>;
// =======================
// MultiChoiceQuestionExtra
// =======================
export const MultiChoiceQuestionExtraSchema = z
  .object({
    type: z.literal(QuestionType.MULTI_CHOICE),
    minimumSelectable: z
      .number()
      .min(1)
      .optional()
      .openapi({ description: 'Minimum number of selections required', example: 1 }),
    maximumSelectable: z
      .number()
      .min(1)
      .optional()
      .openapi({ description: 'Maximum number of selections allowed', example: 3 }),
    selections: z
      .array(
        z
          .object({
            order: z.number().int().min(0).openapi({ minimum: 0 }),
            selectionText: z.string().min(1).openapi({ minLength: 1 }),
          })
          .openapi('MultiChoiceSelection'),
      )
      .openapi({ description: 'questionType is MULTI-CHOICE' }),
  })
  .openapi('MultiChoiceQuestionExtra');

export type MultiChoiceQuestionExtra = z.infer<typeof MultiChoiceQuestionExtraSchema>;

// =======================
// TextQuestionExtra
// =======================
export const TextQuestionExtraSchema = z
  .object({
    type: z.literal(QuestionType.TEXT),
    validations: z.array(QuestionValidationSchema).openapi({ description: 'questionType is TEXT' }),
  })
  .openapi('TextQuestionExtra');

export type TextQuestionExtra = z.infer<typeof TextQuestionExtraSchema>;

// =======================
// TextLinesQuestionExtra
// =======================
export const TextLinesQuestionExtraSchema = z
  .object({
    // Note: OpenAPI missing maxLines, minLines, defaultLines definitions
    type: z.literal(QuestionType.TEXT_LINES),
    validations: z.array(QuestionValidationSchema).openapi({ description: 'questionType is TEXT-LINES' }),
  })
  .openapi('TextLinesQuestionExtra');

export type TextLinesQuestionExtra = z.infer<typeof TextLinesQuestionExtraSchema>;

// =======================
// NumberQuestionExtra
// =======================
export const NumberQuestionExtraSchema = z
  .object({
    type: z.literal(QuestionType.NUMBER),
    validations: z.array(QuestionValidationSchema).openapi({ description: 'questionType is NUMBER' }),
  })
  .openapi('NumberQuestionExtra');

export type NumberQuestionExtra = z.infer<typeof NumberQuestionExtraSchema>;

// =======================
// ImageQuestionExtra
// =======================
export const ImageQuestionExtraSchema = z
  .object({
    type: z.literal(QuestionType.IMAGE),
    validations: z.array(QuestionValidationSchema).openapi({ description: 'questionType is IMAGE' }),
  })
  .openapi('ImageQuestionExtra');

export type ImageQuestionExtra = z.infer<typeof ImageQuestionExtraSchema>;

// =======================
// QuestionExtra (oneOf)
// =======================
export const QuestionExtraSchema = z
  .discriminatedUnion('type', [
    SingleChoiceQuestionExtraSchema,
    MultiChoiceQuestionExtraSchema,
    TextQuestionExtraSchema,
    TextLinesQuestionExtraSchema,
    NumberQuestionExtraSchema,
    ImageQuestionExtraSchema,
  ])
  .openapi('QuestionExtra');

export type QuestionExtra = z.infer<typeof QuestionExtraSchema>;

// =======================
// QuestionnaireDetailGetResponse
// =======================
export const ResponseQuestionSchema = z
  .object({
    themeId: z.string().regex(genericUuidPattern).openapi({
      description: 'Unique identifier for the theme',
      example: '9a0f4999-cb69-48ef-b564-ea5df315ca80',
      pattern: genericUuidPattern.source,
    }),
    questionId: z.string().regex(genericUuidPattern).openapi({
      description: 'Unique identifier for the question',
      example: '7a7b4284-84f2-4dec-994f-148f89d54957',
      pattern: genericUuidPattern.source,
    }),
    questionNumber: z.number().int().openapi({
      description: 'The number of the question',
      example: 0,
    }),
    questionTitle: z.string().openapi({
      description: 'The title of the question',
      example: '',
    }),
    questionDetail: z.string().optional().openapi({
      description: 'Detailed description of the question',
      example: '',
    }),
    questionType: QuestionTypeSchema,
    questionExtra: QuestionExtraSchema.optional(),
    questionImageUrl: z.string().url('Invalid URL format').optional().openapi({
      description: 'URL for the question image',
      example: '',
      pattern: '^https://.*$',
    }),
    isRequired: z.boolean().openapi({
      description: 'Indicates if the question is required',
      example: true,
    }),
  })
  .openapi('ResponseQuestion');
export type ResponseQuestion = z.infer<typeof ResponseQuestionSchema>;

export const ResponseThemeSchema = z
  .object({
    themeId: z.string().regex(genericUuidPattern).openapi({
      description: 'Unique identifier for the theme',
      example: '9a0f4999-cb69-48ef-b564-ea5df315ca80',
      pattern: genericUuidPattern.source,
    }),
    themeThumbnailImageUrl: z.string().url('Invalid URL format').optional().openapi({
      description: 'URL for the theme thumbnail image',
      example: '',
      pattern: '^https://.*$',
    }),
    themeCoverImageUrl: z.string().url('Invalid URL format').optional().openapi({
      description: 'URL for the theme cover image',
      example: '',
      pattern: '^https://.*$',
    }),
    themeTitle: z
      .string()
      .min(1, 'Theme title is required')
      .optional()
      .openapi({ description: 'The title of the theme', example: '' }),
    themeDescription: z
      .string()
      .min(1, 'Theme description is required')
      .optional()
      .openapi({ description: 'Description of the theme', example: '' }),
    themeNumber: z.number().min(1).int().openapi({ description: 'The number associated with the theme', example: 0 }),
    themeTimeLimitSeconds: z
      .number()
      .optional()
      .openapi({ description: 'Time limit for the theme in seconds', example: 100 }),
  })
  .openapi('ResponseTheme');
export type ResponseTheme = z.infer<typeof ResponseThemeSchema>;

export const QuestionnaireDetailGetResponseSchema = z
  .object({
    questionnaireId: z.string().regex(regexPattern.uuid).openapi({
      description: 'Uniquely given identifier for each questionnaire',
      example: 'cb9555c4-07e5-4137-a22d-44291faeb2c6',
      pattern: regexPattern.uuid.source,
    }),
    themes: z.array(ResponseThemeSchema).openapi({ description: 'List of themes related to the questionnaire' }),
    questions: z
      .array(ResponseQuestionSchema)
      .openapi({ description: 'List of questions related to the questionnaire' }),
  })
  .openapi('QuestionnaireDetailGetResponse');

export type QuestionnaireDetailGetResponse = z.infer<typeof QuestionnaireDetailGetResponseSchema>;

// =======================
// QuestionnaireTypeEnum
// =======================
export const questionnaireQuestionTranslationSchema = z.object({
  language: languageCodeSchema,
  questionTitle: z.string().min(1, 'Question title cannot be empty'),
  questionDetail: z.string().optional(),
  questionExtra: QuestionExtraSchema,
  correctData: z.string().optional(),
  correctDataValidation: z.string().optional(),
});

export const questionnaireThemeTranslationSchema = z.object({
  language: languageCodeSchema,
  themeTitle: z.string().min(1, 'Theme title is required').optional(),
  themeDescription: z.string().min(1, 'Theme description is required').optional(),
});

export const questionnaireRankTranslationSchema = z.object({
  language: languageCodeSchema,
  rankName: z.string().min(1, 'Rank name is required'),
});

// =======================
// QuestionnaireCreateRequest
// =======================
export const QuestionnaireCreateRequestSchema = z
  .object({
    questionnaireType: QuestionnaireTypeSchema,
    themes: z.array(
      z
        .object({
          themeNumber: z.number().openapi({ example: 1 }),
          themeTranslations: z.array(questionnaireThemeTranslationSchema).optional(),
          themeThumbnailImageUrl: z
            .string()
            .url()
            .openapi({
              format: 'uri',
              example: 'https://example.com/theme-thumbnail.png',
              maxLength: 512,
            })
            .optional(),
          themeCoverImageUrl: z
            .string()
            .url()
            .openapi({
              format: 'uri',
              example: 'https://example.com/theme-cover.png',
              maxLength: 512,
            })
            .optional(),
          themeTimeLimitSeconds: z.number().openapi({ example: 600 }).optional(),
          questions: z.array(
            z
              .object({
                questionNumber: z.number().openapi({ example: 1 }),
                questionType: QuestionTypeSchema,
                answerPoint: z.number().openapi({ example: 10 }).optional(),
                questionTranslations: z.array(questionnaireQuestionTranslationSchema),
                questionImageUrl: z.string().url('Invalid URL format').optional().openapi({
                  description: 'URL for the question image',
                  example: '',
                  pattern: '^https://.*$',
                }),
                isRequired: z.boolean().openapi({
                  description: 'Indicates if the question is required',
                  example: true,
                }),
              })
              .openapi('CreateQuestion'),
          ),
        })
        .openapi('CreateTheme'),
    ),
    ranks: z
      .array(
        z
          .object({
            rankHeaderAnimationUrl: z
              .string()
              .url()
              .openapi({ format: 'uri', example: 'https://cdn.rive.app/animations/vehicles.riv', maxLength: 512 }),
            rank: z.number().int().openapi({ example: 1 }),
            lowerLimitPoints: z.number().int().openapi({ example: 0 }),
            upperLimitPoints: z.number().int().openapi({ example: 10 }),
            isPassed: z.boolean().openapi({ example: true }),
            rankTranslations: z.array(questionnaireRankTranslationSchema),
          })
          .openapi('CreateRank'),
      )
      .optional(),
  })
  .openapi('QuestionnaireCreateRequest');

export type QuestionnaireCreateRequest = z.infer<typeof QuestionnaireCreateRequestSchema>;

// =======================
// QuestionnaireUpdateRequest
// =======================
export const QuestionnaireUpdateRequestSchema = z
  .object({
    questionnaireType: QuestionnaireTypeSchema,
    themes: z
      .array(
        z
          .object({
            themeId: z
              .string()
              .regex(regexPattern.uuid)
              .openapi({ example: 'cb9555c4-07e5-4137-a22d-44291faeb2c6', pattern: regexPattern.uuid.source }),
            themeTranslations: z.array(questionnaireThemeTranslationSchema).optional(),
            themeNumber: z.number().openapi({ example: 1 }),
            themeThumbnailImageUrl: z
              .string()
              .url()
              .openapi({
                format: 'uri',
                example: 'https://example.com/theme-thumbnail.png',
                maxLength: 512,
              })
              .optional(),
            themeCoverImageUrl: z
              .string()
              .url()
              .openapi({
                format: 'uri',
                example: 'https://example.com/theme-cover.png',
                maxLength: 512,
              })
              .optional(),
            themeTimeLimitSeconds: z.number().openapi({ example: 600 }).optional(),
            questions: z.array(
              z
                .object({
                  questionId: z
                    .string()
                    .regex(regexPattern.uuid)
                    .openapi({ example: 'cb9555c4-07e5-4137-a22d-44291faeb2c6', pattern: regexPattern.uuid.source }),
                  questionNumber: z.number().openapi({ example: 1 }),
                  questionType: QuestionTypeSchema,
                  answerPoint: z.number().openapi({ example: 10 }).optional(),
                  questionTranslations: z.array(questionnaireQuestionTranslationSchema),
                  questionImageUrl: z.string().url('Invalid URL format').optional().openapi({
                    description: 'URL for the question image',
                    example: '',
                    pattern: '^https://.*$',
                  }),
                  isRequired: z.boolean().openapi({
                    description: 'Indicates if the question is required',
                    example: true,
                  }),
                })
                .openapi('UpdateQuestion'),
            ),
          })
          .openapi('UpdateTheme'),
      )
      .min(1),
    ranks: z
      .array(
        z
          .object({
            rankId: z
              .string()
              .regex(regexPattern.uuid)
              .openapi({ example: 'cb9555c4-07e5-4137-a22d-44291faeb2c6', pattern: regexPattern.uuid.source }),
            rankHeaderAnimationUrl: z
              .string()
              .openapi({ format: 'uri', example: 'https://cdn.rive.app/animations/vehicles.riv', maxLength: 512 }),
            rankTranslations: z.array(questionnaireRankTranslationSchema),
            rank: z.number().int().openapi({ example: 1 }),
            lowerLimitPoints: z.number().int().openapi({ example: 0 }),
            upperLimitPoints: z.number().int().openapi({ example: 10 }),
            isPassed: z.boolean().openapi({ example: true }),
          })
          .openapi('UpdateRank'),
      )
      .optional(),
  })
  .openapi('QuestionnaireUpdateRequest');

export type QuestionnaireUpdateRequest = z.infer<typeof QuestionnaireUpdateRequestSchema>;

// =======================
// QuestionnaireReadResponse
// =======================

export const QuestionnaireReadResponseSchema = z
  .object({
    questionnaireId: QuestionnaireDetailGetResponseSchema.shape.questionnaireId,
    questionnaireType: QuestionnaireTypeSchema,
    themes: z.array(ResponseThemeSchema).min(1),
    questions: z.array(ResponseQuestionSchema).min(1),
  })
  .openapi('QuestionnaireReadResponse');

export type QuestionnaireReadResponse = z.infer<typeof QuestionnaireReadResponseSchema>;

// =======================
// QuestionnaireDetailResponse
// =======================
const ResponseQuestionUpdateSchema = z
  .object({
    questionId: z.string().regex(genericUuidPattern).openapi({
      description: 'Unique identifier for the question',
      example: '7a7b4284-84f2-4dec-994f-148f89d54957',
      pattern: genericUuidPattern.source,
    }),
    questionNumber: z.number().int().openapi({ description: 'The number of the question', example: 0 }),
    questionType: QuestionTypeSchema,
    questionTranslations: z.array(questionnaireQuestionTranslationSchema),
    questionImageUrl: z.string().url('Invalid URL format').optional().openapi({
      description: 'URL for the question image',
      example: '',
      pattern: '^https://.*$',
    }),
    isRequired: z.boolean().openapi({
      description: 'Indicates if the question is required',
      example: true,
    }),
    answerPoint: z.number().int().openapi({ example: 10 }).optional(),
  })
  .openapi('ResponseQuestionUpdate');
export type ResponseQuestionUpdate = z.infer<typeof ResponseQuestionUpdateSchema>;

const ResponseThemeUpdateSchema = z
  .object({
    themeId: z.string().regex(genericUuidPattern).openapi({
      description: 'Unique identifier for the theme',
      example: '9a0f4999-cb69-48ef-b564-ea5df315ca80',
      pattern: genericUuidPattern.source,
    }),
    themeThumbnailImageUrl: z
      .string()
      .url()
      .openapi({
        format: 'uri',
        example: 'https://example.com/theme-thumbnail.png',
        maxLength: 512,
      })
      .optional(),
    themeCoverImageUrl: z
      .string()
      .url()
      .openapi({
        format: 'uri',
        example: 'https://example.com/theme-cover.png',
        maxLength: 512,
      })
      .optional(),
    themeNumber: z.number().min(1).int().openapi({ description: 'The number associated with the theme', example: 0 }),
    themeTimeLimitSeconds: z
      .number()
      .int()
      .optional()
      .openapi({ description: 'Time limit for the theme in seconds', example: 100 }),
    themeTranslations: z.array(questionnaireThemeTranslationSchema),
  })
  .openapi('ResponseThemeUpdate');
export type ResponseThemeUpdate = z.infer<typeof ResponseThemeUpdateSchema>;

const ResponseRankDetailSchema = z
  .object({
    rankId: z
      .string()
      .regex(genericUuidPattern)
      .openapi({ example: '4ae4cc33-5e08-4903-a6e6-7af79479c054', pattern: genericUuidPattern.source }),
    rankHeaderAnimationUrl: z
      .string()
      .openapi({ format: 'uri', example: 'https://cdn.rive.app/animations/vehicles.riv', maxLength: 512 }),
    rank: z.number().int().openapi({ example: 1 }),
    lowerLimitPoints: z.number().int().openapi({ example: 0 }),
    upperLimitPoints: z.number().int().openapi({ example: 10 }),
    isPassed: z.boolean().openapi({ example: true }),
    rankTranslations: z.array(questionnaireRankTranslationSchema).min(1),
  })
  .openapi('ResponseRankDetail');
export type ResponseRankDetail = z.infer<typeof ResponseRankDetailSchema>;

const ResponseRankSettingDetailSchema = z
  .object({
    maxRank: z.number().int().openapi({ example: 5 }),
    passingPoints: z.number().int().openapi({ example: 10 }),
    rankDetails: z.array(ResponseRankDetailSchema).openapi({ description: 'List of rank details' }),
  })
  .openapi('ResponseRanks');
export type ResponseRankSettingDetail = z.infer<typeof ResponseRankSettingDetailSchema>;

export const QuestionnaireDetailResponseSchema = z
  .object({
    questionnaireId: QuestionnaireDetailGetResponseSchema.shape.questionnaireId,
    questionnaireType: QuestionnaireTypeSchema,
    themes: z.array(ResponseThemeUpdateSchema).min(1),
    questions: z.array(ResponseQuestionUpdateSchema).min(1),
    ranks: ResponseRankSettingDetailSchema.optional(),
  })
  .openapi('QuestionnaireDetailResponse');

export type QuestionnaireDetailResponse = z.infer<typeof QuestionnaireDetailResponseSchema>;
