import { z } from '@hono/zod-openapi';
import { regexPattern } from '../../utils/regex';
// =======================
// PathParameterUserId
// =======================
export const PathParameterUserIdSchema = z
  .object({
    userId: z
      .string()
      .min(1)
      .refine((val) =>  new TextEncoder().encode(val).length <= 1500, {
        message: 'Specify within a maximum of 1500 bytes',
      })
      .openapi({
        description: 'Uniquely given identifier for each user',
        example: '19bcd4c4-892a-4ecc-9832-90bd2b0a7ed0',
        pattern: regexPattern.uuid.source,
      }),
  })
  .openapi('PathParameterUserId');

export type PathParameterUserId = z.infer<typeof PathParameterUserIdSchema>;
