import { CreatedUserSchema, UserCheckSchema, UserSchema, ContractAccountSchema, RecoveryShareSchema } from './schemas';

export const userCreateResponseBody = {
  description: 'successful operation',
  content: {
    'application/json': {
      schema: CreatedUserSchema,
    },
  },
};

export const userDeleteResponseBody = {
  description: 'successful operation',
};

export const userCheckResponseBody = {
  description: 'successful operation',
  content: {
    'application/json': {
      schema: UserCheckSchema,
    },
  },
};

export const userResponseBody = {
  description: 'successful operation',
  content: {
    'application/json': {
      schema: UserSchema,
    },
  },
};

export const userUpdatePhonenumberResponseBody = {
  description: 'successful operation',
};

export const createBackupKeyReponseBody = {
  description: 'successful operation',
};

export const contractAccountUpdateResponseBody = {
  description: 'successful operation',
};

export const backupKeyResponseBody = {
  description: 'successful operation',
  content: {
    'application/json': {
      schema: ContractAccountSchema,
    },
  },
};

export const recoveryShareResponseBody = {
  description: 'successful operation',
  content: {
    'application/json': {
      schema: RecoveryShareSchema,
    },
  },
};

export const rrecoveryShareStoreResponseBody = {
  description: 'successful operation',
};