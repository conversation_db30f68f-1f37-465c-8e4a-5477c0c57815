import { z } from '@hono/zod-openapi';
import { regexPattern } from '../../utils/regex';

// =======================
// CreateUser
// =======================
export const CreateUserSchema = z
  .object({
    userId: z
      .string()
      .min(1)
      .refine((val) => new TextEncoder().encode(val).length <= 1500, {
        message: 'Specify within a maximum of 1500 bytes',
      })
      .openapi({
        description: 'Uniquely given identifier for each user',
        example: '19bcd4c4-892a-4ecc-9832-90bd2b0a7ed0',
        pattern: '^[a-zA-Z0-9_-]{1,128}$',
      }),
    countryCode: z.string().regex(regexPattern.countryCode).openapi({
      description: 'Country code according to `ISO 3166-1 alpha-2`',
      example: 'JP',
      pattern: regexPattern.countryCode.source,
    }),
    phoneNumber: z.string().regex(regexPattern.phoneNumber).openapi({
      description: 'Global phone number format according to `E.164`',
      example: '+819011112222',
      pattern: regexPattern.phoneNumber.source,
    }),
    mnemonicBackupKey: z.string().regex(regexPattern.rawHex).max(512).openapi({
      description: 'Hex encoded AES GSM 256 common key',
      example: '0x51599a5cd6ac766ff116c8e56dc8d8f006a3f8f11f3230a6ae784a2c491dffa1',
      pattern: regexPattern.rawHex.source,
    }),
  })
  .openapi('CreateUser');

export type CreateUser = z.infer<typeof CreateUserSchema>;

// =======================
// PhoneNumber
// =======================
export const PhoneNumberSchema = z
  .object({
    countryCode: z.string().regex(regexPattern.countryCode).openapi({
      description: 'Country code according to `ISO 3166-1 alpha-2`',
      example: 'JP',
      pattern: regexPattern.countryCode.source,
    }),
    phoneNumber: z.string().regex(regexPattern.phoneNumber).openapi({
      description: 'Global phone number format according to `E.164`',
      example: '+819011112222',
      pattern: regexPattern.phoneNumber.source,
    }),
  })
  .openapi('Phone');

export type PhoneNumber = z.infer<typeof PhoneNumberSchema>;

// =======================
// CreatedUser
// =======================
export const CreatedUserSchema = z
  .object({
    userId: z
      .string()
      .min(1)
      .refine((val) => new TextEncoder().encode(val).length <= 1500, {
        message: 'Specify within a maximum of 1500 bytes',
      })
      .openapi({
        description: 'Uniquely given identifier for each user',
        example: '19bcd4c4-892a-4ecc-9832-90bd2b0a7ed0',
        pattern: regexPattern.uuid.source,
      }),
    phone: PhoneNumberSchema.openapi({ description: 'User phone information' }),
    contractAccountAddress: z.string().regex(regexPattern.ethereumAddress).optional().openapi({
      description: '* Address format with EVM checksum according to `EIP-55`\n* Perform checksum verification',
      example: '******************************************',
      pattern: regexPattern.ethereumAddress.source,
    }),
    account: z.object({
      serviceId: z.string().regex(regexPattern.uuid).openapi({
        description: 'Uniquely given identifier for each service',
        example: '19bcd4c4-892a-4ecc-9832-90bd2b0a7ed0',
        pattern: regexPattern.uuid.source,
      }),
      accountId: z.string().regex(regexPattern.uuid).optional().openapi({
        description: 'None if no account for the service has been issued',
        example: '19bcd4c4-892a-4ecc-9832-90bd2b0a7ed0',
        pattern: regexPattern.uuid.source,
      }),
    }),
  })
  .openapi('CreatedUser');

export type CreatedUser = z.infer<typeof CreatedUserSchema>;

// =======================
// ContractAccount
// =======================
export const ContractAccountSchema = z
  .object({
    mnemonicBackupKey: z.string().regex(regexPattern.blockChainHash).openapi({
      description: 'Hex encoded AES GSM 256 common key',
      example: '0x51599a5cd6ac766ff116c8e56dc8d8f006a3f8f11f3230a6ae784a2c491dffa1',
      pattern: regexPattern.blockChainHash.source,
    }),
    contractAccountAddress: z.string().regex(regexPattern.ethereumAddress).optional().openapi({
      description: '* Address format with EVM checksum according to `EIP-55`\n* Perform checksum verification',
      example: '******************************************',
      pattern: regexPattern.ethereumAddress.source,
    }),
  })
  .openapi('ContractAccount');

export type ContractAccount = z.infer<typeof ContractAccountSchema>;

// =======================
// ContractAccountAddress
// =======================
export const ContractAccountAddressSchema = z
  .object({
    contractAccountAddress: z.string().regex(regexPattern.ethereumAddress).openapi({
      description: '* Address format with EVM checksum according to `EIP-55`\n* Perform checksum verification',
      example: '******************************************',
      pattern: regexPattern.ethereumAddress.source,
    }),
  })
  .openapi('ContractAccountAddress');

export type ContractAccountAddress = z.infer<typeof ContractAccountAddressSchema>;

// =======================
// UserCheck
// =======================
export const UserCheckSchema = z
  .object({
    isUserExist: z.boolean().openapi({ example: true }),
  })
  .openapi('UserCheck');

export type UserCheck = z.infer<typeof UserCheckSchema>;

// =======================
// User
// =======================
const UserAccountInnerSchema = z
  .object({
    serviceId: z
      .string()
      .regex(/^[a-zA-Z0-9_-]{1,128}$/)
      .openapi({
        description: 'Uniquely given identifier for each service',
        example: '19bcd4c4-892a-4ecc-9832-90bd2b0a7ed0',
        pattern: '^[a-zA-Z0-9_-]{1,128}$',
      }),
    accountId: z
      .string()
      .regex(/^[a-zA-Z0-9_-]{1,128}$/)
      .openapi({
        description: 'None if no account for the service has been issued',
        example: '19bcd4c4-892a-4ecc-9832-90bd2b0a7ed0',
        pattern: '^[a-zA-Z0-9_-]{1,128}$',
      })
      .optional(),
  })
  .openapi({ description: 'Linked service account info' });

export const UserSchema = z
  .object({
    userId: z
      .string()
      .min(1)
      .refine((val) => new TextEncoder().encode(val).length <= 1500, {
        message: 'Specify within a maximum of 1500 bytes',
      })
      .openapi({
        description: 'Uniquely given identifier for each user',
        example: '19bcd4c4-892a-4ecc-9832-90bd2b0a7ed0',
        pattern: '^[a-zA-Z0-9_-]{1,128}$',
      }),
    phone: PhoneNumberSchema.openapi({ description: 'User phone information' }),
    contractAccountAddress: z
      .string()
      .regex(regexPattern.ethereumAddress)
      .openapi({
        description: '* Address format with EVM checksum according to `EIP-55`\n* Perform checksum verification',
        example: '******************************************',
        pattern: regexPattern.ethereumAddress.source,
      })
      .optional(),
    account: UserAccountInnerSchema,
  })
  .openapi('User');

export type User = z.infer<typeof UserSchema>;

// =======================
// RecoveryShare
// =======================

export const RecoveryShareSchema = z
  .object({
    share: z.string().min(1).openapi({
      description: 'Hex encoded TSS share',
      example: '0x2bedd47a238e3729ac5e79895e5cdf22f07b5e751c12e5b8ea0547a692ee90cf',
    }),
    shareIndex: z.string().min(1).openapi({
      description: 'Hex encoded TSS share index',
      example: '0x9ad6054dd00e700b42f49d1301439c1bec1e862f3520d0e9f1bf31510bf8f8a',
    }),
    polynomialID: z.string().min(1).openapi({
      description: 'Hex encoded TSS polynomial ID',
      example:
        '0270c95de13e677612662b10792be43c0b432b8e0ac78498fe5217b1522292b302|02450b45c8d0a610733dd238ea6e211aa328b674605b1d4d82d1357193ea8ec84e',
    }),
  })
  .openapi('RecoveryShare');

export type RecoveryShare = z.infer<typeof RecoveryShareSchema>;
