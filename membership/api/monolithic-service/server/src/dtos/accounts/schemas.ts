import { z } from '@hono/zod-openapi';
import { ActionType, ActionTypeSchema, RewardActionTypeSchema } from '../../enum/actionType';
import { NftTagSchema } from '../../enum/nftTag';
import { QuestStatusSchema } from '../../enum/questStatus';
import { regexPattern } from '../../utils/regex';
import { ExpireAtFromSchema, ExpireAtToSchema, PathParameterAccountIdSchema, PathParameterRewardIdSchema, StartAtFromSchema, StartAtToSchema } from './path';
import { RewardStatusSchema } from '../../enum/rewardStatus';
import { GachaCostTypeSchema, GachaTypeSchema } from '../../enum/gachaType';
import { RewardTypeSchema } from '../../enum/rewardType';
import { LocationCoordinatesSchema } from '../locations/schemas';


// =======================
// Account
// =======================
export const AccountSchema = z
  .object({
    accountId: z
      .string()
      .regex(regexPattern.uuid)
      .openapi({
        description: 'Uniquely given identifier for each account',
        example: '28cc7f3c-b47d-486e-b035-************',
        pattern: regexPattern.uuid.source,
      }),
    displayName: z
      .string()
      .openapi({
        description: 'user name stored in LINE profile information',
        example: 'LINE-user-name',
      })
      .optional(),
    profileImage: z
      .string()
      .regex(regexPattern.imageUrl)
      .openapi({
        description: 'user image stored in LINE profile information',
        example: 'https://profile.image',
        pattern: regexPattern.imageUrl.source,
      })
      .optional(),
    membership: z
      .object({
        contractAddress: z
          .string()
          .regex(regexPattern.ethereumAddress)
          .openapi({
            description: 'ERC721 contract address',
            example: '******************************************',
            pattern: regexPattern.ethereumAddress.source,
          }),
        tokenId: z
          .number()
          .openapi({
            description: 'ERC721 tokenId',
            example: 100,
          }),
      })
      .openapi({ description: 'Membership information' }),
    tokenBoundAccountAddress: z
      .string()
      .regex(regexPattern.ethereumAddress)
      .openapi({
        description: 'ContractAddress for Smart Contract Account linked to ERC721 in membership',
        example: '******************************************',
        pattern: regexPattern.ethereumAddress.source,
      })
      .optional(),
    membershipNftImageUrl: z
      .string()
      .regex(regexPattern.imageUrl)
      .openapi({
        description: 'URL of the membership NFT image',
        example: 'https://example.com/images/membership-nft.png',
        pattern: regexPattern.imageUrl.source,
      }).optional(),
  })
  .openapi('Account');

export type Account = z.infer<typeof AccountSchema>;

// =======================
// StatusCertificate
// =======================
export const StatusCertificateSchema = z
  .object({
    rewardId: z
      .string()
      .regex(regexPattern.uuid)
      .openapi({
        description: 'The uniquely given id of the reward',
        example: '8552df83-cbd5-44db-b67e-0cbeb2785918',
        pattern: regexPattern.uuid.source,
      })
      .optional(),
    title: z
      .string()
      .regex(/^.{4,128}$/)
      .openapi({
        description: 'The name of the certificate',
        example: 'Test cert',
        pattern: '^.{4,128}$',
      })
      .optional(),
    description: z
      .string()
      .openapi({
        description: 'Markdown text encoded in base64',
        example: 'IyBDZXJ0aWZpZWQgT3BlbkFQSSBTcGVjaWFsaXN0Lg==',
      })
      .optional(),
    imageUrl: z
      .string()
      .regex(regexPattern.imageUrl)
      .openapi({
        description: 'URL of the certificate image.',
        example: 'https://example.com/images/certificate.png',
        pattern: regexPattern.imageUrl.source,
      })
      .optional(),
    rank: z
      .number()
      .openapi({
        description: 'Status Badge Priority. The higher the higher the contribution.',
        example: 1,
      })
      .optional(),
  })
  .openapi('StatusCertificate');

export type StatusCertificate = z.infer<typeof StatusCertificateSchema>;

// =======================
// Status
// =======================
export const StatusSchema = z
  .object({
    completedQuests: z
      .number()
      .openapi({
        description: 'Aggregate number of quests currently completed',
        example: 4,
      })
      .optional(),
    obtainedRewards: z
      .number()
      .openapi({
        description: 'Aggregate number of rewards currently retrieved',
        example: 10,
      })
      .optional(),
    unusedCoupon: z
      .number()
      .openapi({
        description: 'Aggregate number of current unused coupons',
        example: 2,
      })
      .optional(),
    currentPeriodCompletedQuests: z
      .number()
      .openapi({
        description: 'Aggregate number of quests currently completed (by current Status Quest period)',
        example: 1,
      })
      .optional(),
    badges: z
      .array(StatusCertificateSchema)
      .openapi({
        description: 'List of currently held status certificates that are valid',
        example: [],
      })
      .optional(),
  })
  .openapi('Status');

export type Status = z.infer<typeof StatusSchema>;

// =======================
// CompletedQuestHistory
// =======================
export const CompletedQuestHistorySchema = z
  .object({
    completedTime: z
      .string()
      .regex(regexPattern.isoDateTimeWithOffset)
      .openapi({
        description: 'Date and time the quest was completed',
        example: '2024-01-01T10:00:00Z',
        pattern: regexPattern.isoDateTimeWithOffset.source,
      })
      .optional(),
    thumbnailImageUrl: z
      .string()
      .regex(regexPattern.imageUrl)
      .openapi({
        description: 'Thumbnail image of Quest',
        example: 'https://marbullx.com/icon/quest/1',
        pattern: regexPattern.imageUrl.source,
      })
      .optional(),
    title: z
      .string()
      .regex(/^.+$/)
      .openapi({
        description: 'Title of Quest',
        example: 'Quest123',
        pattern: '^.+$',
      })
      .optional(),
  })
  .openapi('CompletedQuestHistory');

export type CompletedQuestHistory = z.infer<typeof CompletedQuestHistorySchema>;

// =======================
// CompletedActionHistory
// =======================
export const CompletedActionHistorySchema = z
  .object({
    completedTime: z
      .string()
      .regex(regexPattern.isoDateTimeWithOffset)
      .openapi({
        description: 'Date and time the action was completed',
        example: '2024-01-01T10:00:00Z',
        pattern: regexPattern.isoDateTimeWithOffset.source,
      })
      .optional(),
    thumbnailImageUrl: z
      .string()
      .regex(regexPattern.imageUrl)
      .openapi({
        description: 'Thumbnail image of Action',
        example: 'https://marbullx.com/icon/action/1',
        pattern: regexPattern.imageUrl.source,
      })
      .optional(),
    title: z
      .string()
      .regex(/^.+$/)
      .openapi({
        description: 'Title of Action',
        example: 'Action123',
        pattern: '^.+$',
      })
      .optional(),
  })
  .openapi('CompletedActionHistory');
export type CompletedActionHistory = z.infer<typeof CompletedActionHistorySchema>;

// =======================
// RewardHistory
// =======================
export const RewardHistorySchema = z
  .object({
    completedTime: z
      .string()
      .regex(regexPattern.isoDateTimeWithOffset)
      .openapi({
        description: 'Date and time the reward was completed or used',
        example: '2024-01-01T10:00:00Z',
        pattern: regexPattern.isoDateTimeWithOffset.source,
      })
      .optional(),
    actionType: RewardActionTypeSchema.optional(),
    thumbnailImageUrl: z
      .string()
      .regex(regexPattern.imageUrl)
      .openapi({
        description: 'Thumbnail image of Reward',
        example: 'https://marbullx.com/icon/reward/1',
        pattern: regexPattern.imageUrl.source,
      })
      .optional(),
    title: z
      .string()
      .regex(/^.+$/)
      .openapi({
        description: 'Title of Reward',
        example: 'reward123',
        pattern: '^.+$',
      })
      .optional(),
  })
  .openapi('RewardHistory');

export type RewardHistory = z.infer<typeof RewardHistorySchema>;

// =======================
// ActivityHistory
// =======================
export const ActivityHistorySchema = z
  .object({
    completedQuests: z
      .array(CompletedQuestHistorySchema)
      .openapi({
        description: 'List of quest completion information',
        example: [],
      })
      .optional(),
    completedActions: z
      .array(CompletedActionHistorySchema)
      .openapi({
        description: 'List of completed actions',
        example: [],
      })
      .optional(),
    rewards: z
      .array(RewardHistorySchema)
      .openapi({
        description: 'List of information on acquisition and use of rewards',
        example: [],
      })
      .optional(),
  })
  .openapi('ActivityHistory');

export type ActivityHistory = z.infer<typeof ActivityHistorySchema>;

// =======================
// QuestActivityStatus
// =======================
export const QuestActivityStatusSchema = z
  .object({
    questIds: z
      .array(
        z.string().regex(regexPattern.uuid).openapi({
          description: 'Uniquely given identifier for each quest',
          example: '8552df83-cbd5-44db-b67e-0cbeb2785918',
          pattern: regexPattern.uuid.source,
        }),
      )
      .openapi({
        description: 'List of questIds',
        example: [],
      })
      .optional(),
  })
  .openapi('QuestActivityStatus');

export type QuestActivityStatus = z.infer<typeof QuestActivityStatusSchema>;

// =======================
// QuestActivityDetail
// =======================
export const QuestActivityDetailSchema = z
  .object({
    questStatus: QuestStatusSchema.optional(),
    claimedRewardIds: z
      .array(
        z.string().regex(regexPattern.uuid).openapi({
          description: 'Uniquely given identifier for each reward',
          example: '5850b40d-333b-4fbe-8c87-8e34d0f47404',
          pattern: regexPattern.uuid.source,
        }),
      )
      .openapi({
        description: "List of Id's of rewards already retrieved",
        example: [],
      })
      .optional(),
    clearedActionIds: z
      .array(
        z.string().regex(regexPattern.uuid).openapi({
          description: 'Uniquely given identifier for each action',
          example: '8552df83-cbd5-44db-b67e-0cbeb2785918',
          pattern: regexPattern.uuid.source,
        }),
      )
      .openapi({
        description: 'List of cleared action ids',
        example: [],
      })
      .optional(),
  })
  .openapi('QuestActivityDetail');

export type QuestActivityDetail = z.infer<typeof QuestActivityDetailSchema>;

// =======================
// ActionComplete
// =======================
const QuestionnaireAnswerSchema = z.object({
  questionId: z.string().regex(regexPattern.uuid).openapi({
    description: 'Uniquely given identifier for each question',
    example: '8552df83-cbd5-44db-b67e-0cbeb2785918',
    pattern: regexPattern.uuid.source,
  }),
  answer: z.string().nullable().openapi({
    description: 'Answer provided for the question',
    example: '100',
  }),
});

const QuestionnaireAnswerRequestSchema = z.object({
  questionnaireId: z.string().regex(regexPattern.uuid).openapi({
    description: 'Questionnaire identifier',
    example: '8552df83-cbd5-44db-b67e-0cbeb2785918',
    pattern: regexPattern.uuid.source,
  }),
  questionAnswers: z.array(QuestionnaireAnswerSchema).openapi({
    description: 'List of question answers. Each item includes questionId and answer.',
    example: [],
  }),
});

export type QuestionnaireAnswerRequest = z.infer<typeof QuestionnaireAnswerRequestSchema>;

export const ActionCompleteSchema = z
  .object({
    actionType: ActionTypeSchema,
    qrVerificationData: z
      .string()
      .regex(/^.+$/)
      .openapi({
        description: 'Data acquired by reading with QR reading. Required only QR-CHECKIN action.',
        example: 'action-qr-test-id',
        pattern: '^.+$',
      })
      .optional(),
    serialCodeData: z
      .string()
      .min(4)
      .max(36)
      .openapi({
        description: 'Data acquired by serial code action. Required only SERIAL-CODE action.',
        example: 'A23B-C456-DEF7-89GH',
        pattern: '^.+$',
      })
      .optional(),
    serialCodeProjectId: z
      .string()
      .regex(regexPattern.uuid)
      .openapi({
        description: 'Data acquired by serial code action. Required only SERIAL-CODE action.',
        example: '8552df83-cbd5-44db-b67e-0cbeb2785918',
        pattern: regexPattern.uuid.source,
      })
      .optional(),
    answerData: QuestionnaireAnswerRequestSchema.openapi({
      description:
        'Base64 encoded data from survey responses saved in JSON format. Required only QUESTIONNAIRE action.',
    }).optional(),
    locationData: LocationCoordinatesSchema.openapi({
      description: 'Data acquired by location checkin action. Required only LOCATION-CHECKIN action.',
    }).optional(),
  })
  .refine((data) => !!data.actionType, {
    message: 'actionType is required',
    path: ['actionType'],
  })
  .superRefine((data, ctx) => {
    const { actionType, qrVerificationData, serialCodeData, serialCodeProjectId, answerData, locationData } = data;

    if (actionType === ActionType.QR_CHECKIN && !qrVerificationData) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: '`qrVerificationData` is required when actionType is QR-CHECKIN',
        path: ['qrVerificationData'],
      });
    }

    if (actionType === ActionType.SERIAL_CODE && (!serialCodeData || !serialCodeProjectId)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: '`serialCodeData` is required when actionType is SERIAL-CODE',
        path: ['serialCodeData'],
      });
    }

    if (actionType === ActionType.QUESTIONNAIRE && !answerData) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: '`answerData` is required when actionType is QUESTIONNAIRE',
        path: ['answerData'],
      });
    }

    if (actionType === ActionType.LOCATION_CHECKIN && !locationData) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: '`locationData` is required when actionType is LOCATION-CHECKIN',
        path: ['locationData'],
      });
    }

    if (actionType === ActionType.LOCATION_CHECKIN && locationData) {
      if (typeof locationData.latitude !== 'number') {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: '`locationData.latitude` is required for LOCATION-CHECKIN',
          path: ['locationData', 'latitude'],
        });
      }

      if (typeof locationData.longitude !== 'number') {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: '`locationData.longitude` is required for LOCATION-CHECKIN',
          path: ['locationData', 'longitude'],
        });
      }
    }
  })
  .openapi('ActionComplete');

export type ActionComplete = z.infer<typeof ActionCompleteSchema>;

// =======================
// ClaimedReward
// =======================
export const ClaimedRewardSchema = z
  .object({
    rewardId: z.string().regex(regexPattern.uuid).openapi({
      description: 'The uniquely given id of the reward',
      example: '8552df83-cbd5-44db-b67e-0cbeb2785918',
      pattern: regexPattern.uuid.source,
    }),
    title: z
      .string()
      .regex(/^.{4,128}$/)
      .openapi({
        description: 'The name of the reward',
        example: 'Test Reward',
        pattern: '^.{4,128}$',
      }),
    coverImageUrl: z.string().regex(regexPattern.imageUrl).openapi({
      description: 'URL of the Reward cover image',
      example: 'https://marbullx.com/reward',
      pattern: regexPattern.imageUrl.source,
    }),
    rewardType: NftTagSchema,
  })
  .openapi('ClaimedReward');

export type ClaimedReward = z.infer<typeof ClaimedRewardSchema>;

// =======================
// NotificationItem
// =======================
export const NotificationItemSchema = z
  .object({
    notificationId: z.string().regex(regexPattern.uuid).openapi({
      description: 'The uniquely given id of the notification',
      example: '8552df83-cbd5-44db-b67e-0cbeb2785918',
      pattern: regexPattern.uuid.source,
    }),
    title: z.string().openapi({
      description: 'Title defined per Notification',
      example: 'システムメンテナンスのお知らせ',
    }),
    text: z.string().openapi({
      description: 'Markdown text encoded in base64',
      example:
        '44GK5a6i5qeY5ZCE5L2NCgrml6XpoIPjgojjgorjgZTliKnnlKjjgYTjgZ/jgaDjgY3jgIHoqqDjgavjgYLjgorjgYzjgajjgYbjgZTjgZbjgYTjgb7jgZnjgILkuIvoqJjjga7ml6XnqIvjgafjgrfjgrnjg4bjg6Djg6Hjg7Pjg4bjg4rjg7PjgrnjgpLlrp/mlr3jgYTjgZ/jgZfjgb7jgZnjgIIKCiMjIOODoeODs+ODhuODiuODs+OCueaXpeaZggotICoq5pel5pmCKio6IDIwMjTlubQxMOaciDIw5pel77yI5pel77yJ5Y2I5YmNMTowMCDjgJwg5Y2I5YmNNTowMAoKIyMg5b2x6Z+/56+E5ZuyCuODoeODs+ODhuODiuODs+OCueacn+mWk+S4reOBr+OAgeS7peS4i+OBruOCteODvOODk+OCueOBjOS4gOaZgueahOOBq+OBlOWIqeeUqOOBhOOBn+OBoOOBkeOBvuOBm+OCk+OAggotIFvjgrXjg7zjg5PjgrnlkI0xXQotIFvjgrXjg7zjg5PjgrnlkI0yXQoKIyMg44GU5rOo5oSPCuODoeODs+ODhuODiuODs+OCuee1guS6huW+jOOAgeOCteODvOODk+OCueOBr+iHquWLleeahOOBq+WGjemWi+OBleOCjOOBvuOBmeOAguOBiuaJi+aVsOOCkuOBiuOBi+OBkeOBl+OBvuOBmeOBjOOAgeOBlOeQhuino+OBqOOBlOWNlOWKm+OCkuOBiumhmOOBhOOBhOOBn+OBl+OBvuOBmeOAggoK5LuK5b6M44Go44KC44CB44Gp44GG44Ge44KI44KN44GX44GP44GK6aGY44GE55Sz44GX5LiK44GS44G+44GZ44CCCgotLS0KCuOBiuWVj+OBhOWQiOOCj+OBmzogc3VwcG9ydEBleGFtcGxlLmNvbQ==',
    }),
    broadcastDate: z.string().regex(regexPattern.isoDateTimeWithOffset).openapi({
      description: 'Time when notifications are broadcasted',
      example: '2024-01-01T10:00:00Z',
      pattern: regexPattern.isoDateTimeWithOffset.source,
    }),
  })
  .openapi('NotificationItem');

export type NotificationItem = z.infer<typeof NotificationItemSchema>;

// =======================
// NotificationList
// =======================
export const NotificationListSchema = z
  .object({
    notifications: z.array(NotificationItemSchema).openapi({
      description: 'Global and Account notifications list',
      example: [],
    }),
  })
  .openapi('NotificationList');

export type NotificationList = z.infer<typeof NotificationListSchema>;

// =======================
// AccountQuestionnaireDetail
// =======================
export const QuestionnaireQuestionSchema = z
  .object({
    questionId: z.string().regex(regexPattern.uuid).openapi({
      description: 'Unique identifier for the question',
      example: '7a7b4284-84f2-4dec-994f-148f89d54957',
      pattern: regexPattern.uuid.source,
    }),
    isRequired: z.boolean().openapi({
      description: 'Whether the question is required to be answered',
      example: true,
    }),
    correctAnswer: z
      .string()
      .openapi({
        description: 'The correct answer to the question',
        example: 'Correct answer',
      })
      .optional(),
    postedAnswer: z
      .string()
      .nullable()
      .openapi({
        description: 'The answer posted by the user',
        example: 'Your answer',
      })
      .optional(),
    obtainedAnswerPoint: z
      .number()
      .openapi({
        description: 'Points obtained for the answer',
        example: 10,
      })
      .optional(),
    isCorrect: z
      .boolean()
      .openapi({
        description: "Whether the user's answer was correct",
        example: true,
      })
      .optional(),
  })
  .openapi('QuestionnaireQuestion');

export type QuestionnaireQuestion = z.infer<typeof QuestionnaireQuestionSchema>;
export const QuestionnaireThemeSchema = z
  .object({
    themeId: z.string().regex(regexPattern.uuid).openapi({
      description: 'Unique identifier for the theme',
      example: '7a7b4284-84f2-4dec-994f-148f89d54957',
      pattern: regexPattern.uuid.source,
    }),
    correctCount: z
      .number()
      .openapi({
        description: 'Number of correct answers in the theme',
        example: 7,
      })
      .optional(),
    failedCount: z
      .number()
      .openapi({
        description: 'Number of failed answers in the theme',
        example: 1,
      })
      .optional(),
    questions: z.array(QuestionnaireQuestionSchema).openapi({
      description: 'List of questions within the theme',
      example: [],
    }),
  })
  .openapi('QuestionnaireTheme');

export type QuestionnaireTheme = z.infer<typeof QuestionnaireThemeSchema>;

export const AccountQuestionnaireDetailSchema = z
  .object({
    questionnaireId: z.string().regex(regexPattern.uuid).openapi({
      description: 'Uniquely given identifier for each questionnaire',
      example: 'cb9555c4-07e5-4137-a22d-44291faeb2c6',
      pattern: regexPattern.uuid.source,
    }),
    result: z.object({
      currentPoint: z
        .number()
        .openapi({
          description: 'Points currently earned',
          example: 45,
        })
        .optional(),
      maxPoint: z
        .number()
        .openapi({
          description: 'Maximum points possible',
          example: 50,
        })
        .optional(),
      rankId: z
        .string()
        .regex(/^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$/)
        .openapi({
          description: 'Identifier for the rank',
          example: '4ae4cc33-5e08-4903-a6e6-7af79479c054',
          pattern: '^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$',
        }),
      rankName: z
        .string()
        .openapi({
          description: 'Name of the rank',
          example: 'rank',
        }),
      rank: z
        .number()
        .openapi({
          description: 'Rank level',
          example: 1,
        }),
      rankHeaderAnimation: z
        .string()
        .regex(/^https:\/\/.*$/)
        .openapi({
          description: 'URL for rank header animation',
          example: 'https://cdn.rive.app/animations/vehicles.riv',
          pattern: '^https:\\/\\/.*$',
        }),
      isPassed: z
        .boolean()
        .openapi({
          description: 'is action completed or not',
          example: true,
        }),
    }).openapi({ description: 'Set only if result of quiz' }).optional(),
    questionnaireThemes: z.array(QuestionnaireThemeSchema).openapi({
      description: 'List of themes in the questionnaire',
      example: [],
    }),
  })
  .strict()
  .refine((data) => data.questionnaireThemes?.length !== 0, {
    message: 'questionnaireThemes is required',
    path: ['questionnaireThemes'],
  })
  .openapi('AccountQuestionnaireDetail');

export type AccountQuestionnaireDetail = z.infer<typeof AccountQuestionnaireDetailSchema>;

// =======================
// SerialCodeRedeem
// =======================
export const SerialCodeRedeemSchema = z
  .object({
    serialCode: z.string().min(4).max(36),
    serialCodeProjectId: z.string().uuid(),
  })
  .openapi('SerialCodeRedeemReq');
export type SerialCodeRedeemRequest = z.infer<typeof SerialCodeRedeemSchema>;

export const SerialCodeRedeemResponseSchema = z
  .object({
    accountSerialCodeId: z.string(),
    serialCodeId: z.string(),
    remainingUseNum: z.number(),
  })
  .openapi('SerialCodeRedeemRes');
export type SerialCodeRedeemResponse = z.infer<typeof SerialCodeRedeemResponseSchema>;

// =======================
// Gacha
// =======================
export const GachaRewardSchema = z
  .object({
    rewardId: z
      .string()
      .regex(regexPattern.uuid),
    coverImageUrl: z
      .string()
      .regex(regexPattern.imageUrl),
    thumbnailImageUrl: z
      .string()
      .regex(regexPattern.imageUrl),
    rewardType: RewardTypeSchema,
    title: z
      .string()
      .regex(/^.{4,128}$/),
    description: z.string(),
  }).openapi('GachaReward');
export type GachaReward = z.infer<typeof GachaRewardSchema>;

export const GachaBoxSchema = z
  .object({
    remainingNum: z.number(),
    initialNum: z.number(),
    maxReset: z.number(),
    usedReset: z.number(),
  }).openapi('GachaBox');
export type GachaBox = z.infer<typeof GachaBoxSchema>;

export const GachaListResponseSchema = z
  .object({
    gachas: z.array(
      z.object({
        gachaId: z
          .string()
          .regex(regexPattern.uuid),
        type: GachaTypeSchema,
        title: z
          .string()
          .regex(/^.{4,128}$/),
        description: z.string(),
        thumbnailUrl: z.string(),
        cost: z.number(),
        costType: GachaCostTypeSchema,
        balance: z.number(),
        closeAt: z
          .string()
          .nullable(),
        executeLimit: z.object({
          max: z.number(),
          executed: z.number(),
        }).nullable(),
        box: GachaBoxSchema.nullable()
      })
    )
  })
  .openapi('GachaListResponse');
export type GachaListResponse = z.infer<typeof GachaListResponseSchema>;

export const ExecutedGachaResponseSchema = z
  .object({
    gachaId: z
      .string()
      .regex(regexPattern.uuid),
    rewards: z.array(GachaRewardSchema),
    cost: z.number(),
    costType: GachaCostTypeSchema,
    balance: z.number(),
    executeLimit: z.object({
      max: z.number(),
      executed: z.number(),
    }).nullable(),
    box: GachaBoxSchema.nullable()
  })
  .openapi('ExecutedGachaResponse');
export type ExecutedGachaResponse = z.infer<typeof ExecutedGachaResponseSchema>;
