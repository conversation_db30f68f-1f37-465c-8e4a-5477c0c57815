import { z } from '@hono/zod-openapi';
import { ActionType, ActionTypeSchema, RewardActionTypeSchema } from '../../enum/actionType';
import { NftTagSchema } from '../../enum/nftTag';
import { QuestStatusSchema } from '../../enum/questStatus';
import { regexPattern } from '../../utils/regex';
import { ExpireAtFromSchema, ExpireAtToSchema, PathParameterAccountIdSchema, PathParameterRewardIdSchema, StartAtFromSchema, StartAtToSchema } from './path';
import { RewardStatusSchema } from '../../enum/rewardStatus';
import { ServiceCustomFieldResponseSchema } from '../services/schemas';

// =======================
// Account
// =======================
export const AccountSchema = z
  .object({
    accountId: z
      .string()
      .regex(regexPattern.uuid)
      .openapi({
        description: 'Uniquely given identifier for each account',
        example: '28cc7f3c-b47d-486e-b035-************',
        pattern: regexPattern.uuid.source,
      })
      .optional(),
    displayName: z
      .string()
      .openapi({
        description: 'user nasme stored in LINE profile information',
        example: 'LINE-user-name',
      })
      .optional(),
    profileImage: z
      .string()
      .regex(regexPattern.imageUrl)
      .openapi({
        description: 'user image stored in LINE profile information',
        example: 'https://profile.image',
        pattern: regexPattern.imageUrl.source,
      })
      .optional(),
    membership: z
      .object({
        contractAddress: z
          .string()
          .regex(regexPattern.ethereumAddress)
          .openapi({
            description: 'ERC721 contract address',
            example: '******************************************',
            pattern: regexPattern.ethereumAddress.source,
          })
          .optional(),
        tokenId: z
          .number()
          .openapi({
            description: 'ERC721 tokenId',
            example: 100,
          })
          .optional(),
      })
      .openapi({ description: 'Membership information' })
      .optional(),
    tokenBoundAccountAddress: z
      .string()
      .regex(regexPattern.ethereumAddress)
      .openapi({
        description: 'ContractAddress for Smart Contract Account linked to ERC721 in membership',
        example: '******************************************',
        pattern: regexPattern.ethereumAddress.source,
      })
      .optional(),
  })
  .openapi('Account');

export type Account = z.infer<typeof AccountSchema>;

// =======================
// StatusCertificate
// =======================
export const StatusCertificateSchema = z
  .object({
    rewardId: z
      .string()
      .regex(regexPattern.uuid)
      .openapi({
        description: 'The uniquely given id of the reward',
        example: '8552df83-cbd5-44db-b67e-0cbeb2785918',
        pattern: regexPattern.uuid.source,
      })
      .optional(),
    title: z
      .string()
      .regex(/^.{4,128}$/)
      .openapi({
        description: 'The name of the certificate',
        example: 'Test cert',
        pattern: '^.{4,128}$',
      })
      .optional(),
    description: z
      .string()
      .openapi({
        description: 'Markdown text encoded in base64',
        example: 'IyBDZXJ0aWZpZWQgT3BlbkFQSSBTcGVjaWFsaXN0Lg==',
      })
      .optional(),
    imageUrl: z
      .string()
      .regex(regexPattern.imageUrl)
      .openapi({
        description: 'URL of the certificate image.',
        example: 'https://example.com/images/certificate.png',
        pattern: regexPattern.imageUrl.source,
      })
      .optional(),
    rank: z
      .number()
      .openapi({
        description: 'Status Badge Priority. The higher the higher the contribution.',
        example: 1,
      })
      .optional(),
  })
  .openapi('StatusCertificate');

export type StatusCertificate = z.infer<typeof StatusCertificateSchema>;

// =======================
// Status
// =======================
export const StatusSchema = z
  .object({
    completedQuests: z
      .number()
      .openapi({
        description: 'Aggregate number of quests currently completed',
        example: 4,
      })
      .optional(),
    obtainedRewards: z
      .number()
      .openapi({
        description: 'Aggregate number of rewards currently retrieved',
        example: 10,
      })
      .optional(),
    unusedCoupon: z
      .number()
      .openapi({
        description: 'Aggregate number of current unused coupons',
        example: 2,
      })
      .optional(),
    currentPeriodCompletedQuests: z
      .number()
      .openapi({
        description: 'Aggregate number of quests currently completed (by current Status Quest period)',
        example: 1,
      })
      .optional(),
    badges: z
      .array(StatusCertificateSchema)
      .openapi({
        description: 'List of currently held status certificates that are valid',
        example: [],
      })
      .optional(),
  })
  .openapi('Status');

export type Status = z.infer<typeof StatusSchema>;

// =======================
// CompletedQuestHistory
// =======================
export const CompletedQuestHistorySchema = z
  .object({
    completedTime: z
      .string()
      .regex(regexPattern.isoDateTimeWithOffset)
      .openapi({
        description: 'Date and time the quest was completed',
        example: '2024-01-01T10:00:00Z',
        pattern: regexPattern.isoDateTimeWithOffset.source,
      })
      .optional(),
    thumbnailImageUrl: z
      .string()
      .regex(regexPattern.imageUrl)
      .openapi({
        description: 'Thumbnail image of Quest',
        example: 'https://marbullx.com/icon/quest/1',
        pattern: regexPattern.imageUrl.source,
      })
      .optional(),
    title: z
      .string()
      .regex(/^.+$/)
      .openapi({
        description: 'Title of Quest',
        example: 'Quest123',
        pattern: '^.+$',
      })
      .optional(),
  })
  .openapi('CompletedQuestHistory');

export type CompletedQuestHistory = z.infer<typeof CompletedQuestHistorySchema>;

// =======================
// CompletedActionHistory
// =======================
export const CompletedActionHistorySchema = z
  .object({
    completedTime: z
      .string()
      .regex(regexPattern.isoDateTimeWithOffset)
      .openapi({
        description: 'Date and time the action was completed',
        example: '2024-01-01T10:00:00Z',
        pattern: regexPattern.isoDateTimeWithOffset.source,
      })
      .optional(),
    thumbnailImageUrl: z
      .string()
      .regex(regexPattern.imageUrl)
      .openapi({
        description: 'Thumbnail image of Action',
        example: 'https://marbullx.com/icon/action/1',
        pattern: regexPattern.imageUrl.source,
      })
      .optional(),
    title: z
      .string()
      .regex(/^.+$/)
      .openapi({
        description: 'Title of Action',
        example: 'Action123',
        pattern: '^.+$',
      })
      .optional(),
  })
  .openapi('CompletedActionHistory');
export type CompletedActionHistory = z.infer<typeof CompletedActionHistorySchema>;

// =======================
// RewardHistory
// =======================
export const RewardHistorySchema = z
  .object({
    completedTime: z
      .string()
      .regex(regexPattern.isoDateTimeWithOffset)
      .openapi({
        description: 'Date and time the reward was completed or used',
        example: '2024-01-01T10:00:00Z',
        pattern: regexPattern.isoDateTimeWithOffset.source,
      })
      .optional(),
    actionType: RewardActionTypeSchema.optional(),
    thumbnailImageUrl: z
      .string()
      .regex(regexPattern.imageUrl)
      .openapi({
        description: 'Thumbnail image of Reward',
        example: 'https://marbullx.com/icon/reward/1',
        pattern: regexPattern.imageUrl.source,
      })
      .optional(),
    title: z
      .string()
      .regex(/^.+$/)
      .openapi({
        description: 'Title of Reward',
        example: 'reward123',
        pattern: '^.+$',
      })
      .optional(),
  })
  .openapi('RewardHistory');

export type RewardHistory = z.infer<typeof RewardHistorySchema>;


// =======================
// ActivityHistory
// =======================
export const ActivityHistorySchema = z
  .object({
    completedQuests: z
      .array(CompletedQuestHistorySchema)
      .openapi({
        description: 'List of quest completion information',
        example: [],
      })
      .optional(),
    completedActions: z
      .array(CompletedActionHistorySchema)
      .openapi({
        description: 'List of completed actions',
        example: [],
      })
      .optional(),
    rewards: z
      .array(RewardHistorySchema)
      .openapi({
        description: 'List of information on acquisition and use of rewards',
        example: [],
      })
      .optional(),
  })
  .openapi('ActivityHistory');

export type ActivityHistory = z.infer<typeof ActivityHistorySchema>;

// =======================
// QuestActivityStatus
// =======================
export const QuestActivityStatusSchema = z
  .object({
    questIds: z
      .array(
        z.string().regex(regexPattern.uuid).openapi({
          description: 'Uniquely given identifier for each quest',
          example: '8552df83-cbd5-44db-b67e-0cbeb2785918',
          pattern: regexPattern.uuid.source,
        }),
      )
      .openapi({
        description: 'List of questIds',
        example: [],
      })
      .optional(),
  })
  .openapi('QuestActivityStatus');

export type QuestActivityStatus = z.infer<typeof QuestActivityStatusSchema>;

// =======================
// QuestActivityDetail
// =======================
export const QuestActivityDetailSchema = z
  .object({
    questStatus: QuestStatusSchema.optional(),
    claimedRewardIds: z
      .array(
        z.string().regex(regexPattern.uuid).openapi({
          description: 'Uniquely given identifier for each reward',
          example: '5850b40d-333b-4fbe-8c87-8e34d0f47404',
          pattern: regexPattern.uuid.source,
        }),
      )
      .openapi({
        description: "List of Id's of rewards already retrieved",
        example: [],
      })
      .optional(),
    clearedActionIds: z
      .array(
        z.string().regex(regexPattern.uuid).openapi({
          description: 'Uniquely given identifier for each action',
          example: '8552df83-cbd5-44db-b67e-0cbeb2785918',
          pattern: regexPattern.uuid.source,
        }),
      )
      .openapi({
        description: 'List of cleared action ids',
        example: [],
      })
      .optional(),
  })
  .openapi('QuestActivityDetail');

export type QuestActivityDetail = z.infer<typeof QuestActivityDetailSchema>;

// =======================
// ActionComplete
// =======================
const QuestionnaireAnswerSchema = z.object({
  questionId: z.string().regex(regexPattern.uuid).openapi({
    description: 'Uniquely given identifier for each question',
    example: '8552df83-cbd5-44db-b67e-0cbeb2785918',
    pattern: regexPattern.uuid.source,
  }),
  answer: z.string().nullable().openapi({
    description: 'Answer provided for the question',
    example: '100',
  }),
});

export const ActionCompleteSchema = z
  .object({
    actionType: ActionTypeSchema,
    qrVerificationData: z
      .string()
      .regex(/^.+$/)
      .openapi({
        description: 'Data acquired by reading with QR reading. Required only QR-CHECKIN action.',
        example: 'action-qr-test-id',
        pattern: '^.+$',
      })
      .nullable()
      .optional(),
    serialCodeData: z
      .string()
      .min(4)
      .max(36)
      .openapi({
        description: 'Data acquired by serial code action. Required only SERIAL-CODE action.',
        example: 'A23B-C456-DEF7-89GH',
        pattern: '^.+$',
      })
      .nullable()
      .optional(),
    serialCodeProjectId: z
      .string()
      .regex(regexPattern.uuid)
      .openapi({
        description: 'Data acquired by serial code action. Required only SERIAL-CODE action.',
        example: '8552df83-cbd5-44db-b67e-0cbeb2785918',
        pattern: regexPattern.uuid.source,
      })
      .nullable()
      .optional(),
    answerData: z
      .object({
        questionnaireId: z.string().regex(regexPattern.uuid).openapi({
          description: 'Questionnaire identifier',
          example: '8552df83-cbd5-44db-b67e-0cbeb2785918',
          pattern: regexPattern.uuid.source,
        }),
        questionAnswers: z.array(QuestionnaireAnswerSchema).openapi({
          description: 'List of question answers. Each item includes questionId and answer.',
          example: [],
        }),
      })
      .openapi({
        description:
          'Base64 encoded data from survey responses saved in JSON format. Required only QUESTIONNAIRE action.',
      })
      .nullable()
      .optional(),
  })
  .refine((data) => !!data.actionType, {
    message: 'actionType is required',
    path: ['actionType'],
  })
  .superRefine((data, ctx) => {
    const { actionType, qrVerificationData, serialCodeData, serialCodeProjectId, answerData } = data;

    if (actionType === ActionType.QR_CHECKIN && !qrVerificationData) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: '`qrVerificationData` is required when actionType is QR-CHECKIN',
        path: ['qrVerificationData'],
      });
    }

    if (actionType === ActionType.SERIAL_CODE && (!serialCodeData || !serialCodeProjectId)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: '`serialCodeData` is required when actionType is SERIAL-CODE',
        path: ['serialCodeData'],
      });
    }

    if (actionType === ActionType.QUESTIONNAIRE && !answerData) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: '`answerData` is required when actionType is QUESTIONNAIRE',
        path: ['answerData'],
      });
    }
  })
  .openapi('ActionComplete');

export type ActionComplete = z.infer<typeof ActionCompleteSchema>;

// =======================
// ClaimedReward
// =======================
export const ClaimedRewardSchema = z
  .object({
    rewardId: z.string().regex(regexPattern.uuid).openapi({
      description: 'The uniquely given id of the reward',
      example: '8552df83-cbd5-44db-b67e-0cbeb2785918',
      pattern: regexPattern.uuid.source,
    }),
    title: z
      .string()
      .regex(/^.{4,128}$/)
      .openapi({
        description: 'The name of the reward',
        example: 'Test Reward',
        pattern: '^.{4,128}$',
      }),
    coverImageUrl: z.string().regex(regexPattern.imageUrl).openapi({
      description: 'URL of the Reward cover image',
      example: 'https://marbullx.com/reward',
      pattern: regexPattern.imageUrl.source,
    }),
    rewardType: NftTagSchema,
    toDate: z
      .string()
      .regex(regexPattern.isoDateTimeWithOffset)
      .openapi({
        description:
          'The expiration date of the coupon. If it is not a coupon (without COUPON), this field does not exist.',
        example: '2024-01-01T10:00:00Z',
        pattern: regexPattern.isoDateTimeWithOffset.source,
      })
      .optional(),
  })
  .openapi('ClaimedReward');

export type ClaimedReward = z.infer<typeof ClaimedRewardSchema>;

// =======================
// NotificationItem
// =======================
export const NotificationItemSchema = z
  .object({
    notificationId: z.string().regex(regexPattern.uuid).openapi({
      description: 'The uniquely given id of the notification',
      example: '8552df83-cbd5-44db-b67e-0cbeb2785918',
      pattern: regexPattern.uuid.source,
    }),
    title: z.string().openapi({
      description: 'Title defined per Notification',
      example: 'システムメンテナンスのお知らせ',
    }),
    text: z.string().openapi({
      description: 'Markdown text encoded in base64',
      example:
        '44GK5a6i5qeY5ZCE5L2NCgrml6XpoIPjgojjgorjgZTliKnnlKjjgYTjgZ/jgaDjgY3jgIHoqqDjgavjgYLjgorjgYzjgajjgYbjgZTjgZbjgYTjgb7jgZnjgILkuIvoqJjjga7ml6XnqIvjgafjgrfjgrnjg4bjg6Djg6Hjg7Pjg4bjg4rjg7PjgrnjgpLlrp/mlr3jgYTjgZ/jgZfjgb7jgZnjgIIKCiMjIOODoeODs+ODhuODiuODs+OCueaXpeaZggotICoq5pel5pmCKio6IDIwMjTlubQxMOaciDIw5pel77yI5pel77yJ5Y2I5YmNMTowMCDjgJwg5Y2I5YmNNTowMAoKIyMg5b2x6Z+/56+E5ZuyCuODoeODs+ODhuODiuODs+OCueacn+mWk+S4reOBr+OAgeS7peS4i+OBruOCteODvOODk+OCueOBjOS4gOaZgueahOOBq+OBlOWIqeeUqOOBhOOBn+OBoOOBkeOBvuOBm+OCk+OAggotIFvjgrXjg7zjg5PjgrnlkI0xXQotIFvjgrXjg7zjg5PjgrnlkI0yXQoKIyMg44GU5rOo5oSPCuODoeODs+ODhuODiuODs+OCuee1guS6huW+jOOAgeOCteODvOODk+OCueOBr+iHquWLleeahOOBq+WGjemWi+OBleOCjOOBvuOBmeOAguOBiuaJi+aVsOOCkuOBiuOBi+OBkeOBl+OBvuOBmeOBjOOAgeOBlOeQhuino+OBqOOBlOWNlOWKm+OCkuOBiumhmOOBhOOBhOOBn+OBl+OBvuOBmeOAggoK5LuK5b6M44Go44KC44CB44Gp44GG44Ge44KI44KN44GX44GP44GK6aGY44GE55Sz44GX5LiK44GS44G+44GZ44CCCgotLS0KCuOBiuWVj+OBhOWQiOOCj+OBmzogc3VwcG9ydEBleGFtcGxlLmNvbQ==',
    }),
    broadcastDate: z.string().regex(regexPattern.isoDateTimeWithOffset).openapi({
      description: 'Time when notifications are broadcasted',
      example: '2024-01-01T10:00:00Z',
      pattern: regexPattern.isoDateTimeWithOffset.source,
    }),
  })
  .openapi('NotificationItem');

export type NotificationItem = z.infer<typeof NotificationItemSchema>;

// =======================
// NotificationList
// =======================
export const NotificationListSchema = z
  .object({
    notifications: z.array(NotificationItemSchema).openapi({
      description: 'Global and Account notifications list',
      example: [],
    }),
  })
  .openapi('NotificationList');

export type NotificationList = z.infer<typeof NotificationListSchema>;

// =======================
// AccountQuestionnaireDetail
// =======================
export const QuestionnaireQuestionSchema = z
  .object({
    questionId: z.string().regex(regexPattern.uuid).openapi({
      description: 'Unique identifier for the question',
      example: '7a7b4284-84f2-4dec-994f-148f89d54957',
      pattern: regexPattern.uuid.source,
    }),
    correctAnswer: z
      .string()
      .openapi({
        description: 'The correct answer to the question',
        example: 'Correct answer',
      })
      .optional(),
    postedAnswer: z
      .string()
      .nullable()
      .openapi({
        description: 'The answer posted by the user',
        example: 'Your answer',
      })
      .optional(),
    obtainedAnswerPoint: z
      .number()
      .openapi({
        description: 'Points obtained for the answer',
        example: 10,
      })
      .optional(),
    isCorrect: z
      .boolean()
      .openapi({
        description: "Whether the user's answer was correct",
        example: true,
      })
      .optional(),
  })
  .openapi('QuestionnaireQuestion');

export type QuestionnaireQuestion = z.infer<typeof QuestionnaireQuestionSchema>;
export const QuestionnaireThemeSchema = z
  .object({
    themeId: z.string().regex(regexPattern.uuid).openapi({
      description: 'Unique identifier for the theme',
      example: '7a7b4284-84f2-4dec-994f-148f89d54957',
      pattern: regexPattern.uuid.source,
    }),
    correctCount: z
      .number()
      .openapi({
        description: 'Number of correct answers in the theme',
        example: 7,
      })
      .optional(),
    failedCount: z
      .number()
      .openapi({
        description: 'Number of failed answers in the theme',
        example: 1,
      })
      .optional(),
    questions: z.array(QuestionnaireQuestionSchema).openapi({
      description: 'List of questions within the theme',
      example: [],
    }),
  })
  .openapi('QuestionnaireTheme');

export type QuestionnaireTheme = z.infer<typeof QuestionnaireThemeSchema>;

export const AccountQuestionnaireDetailSchema = z
  .object({
    questionnaireId: z.string().regex(regexPattern.uuid).openapi({
      description: 'Uniquely given identifier for each questionnaire',
      example: 'cb9555c4-07e5-4137-a22d-44291faeb2c6',
      pattern: regexPattern.uuid.source,
    }),
    currentPoint: z
      .number()
      .openapi({
        description: 'Points currently earned',
        example: 45,
      })
      .optional(),
    maxPoint: z
      .number()
      .openapi({
        description: 'Maximum points possible',
        example: 50,
      })
      .optional(),
    rankId: z
      .string()
      .regex(/^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$/)
      .openapi({
        description: 'Identifier for the rank',
        example: '4ae4cc33-5e08-4903-a6e6-7af79479c054',
        pattern: '^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$',
      }),
    rankName: z.string().openapi({
      description: 'Name of the rank',
      example: 'rank',
    }),
    rank: z.number().openapi({
      description: 'Rank level',
      example: 1,
    }),
    rankHeaderAnimation: z
      .string()
      .regex(/^https:\/\/.*$/)
      .openapi({
        description: 'URL for rank header animation',
        example: 'https://cdn.rive.app/animations/vehicles.riv',
        pattern: '^https:\\/\\/.*$',
      }),
    isPassed: z.boolean().openapi({
      description: 'is action completed or not',
      example: true,
    }),
    questionnaireThemes: z.array(QuestionnaireThemeSchema).openapi({
      description: 'List of themes in the questionnaire',
      example: [],
    }),
  })
  .refine((data) => data.questionnaireThemes?.length !== 0, {
    message: 'questionnaireThemes is required',
    path: ['questionnaireThemes'],
  })
  .openapi('AccountQuestionnaireDetail');

export type AccountQuestionnaireDetail = z.infer<typeof AccountQuestionnaireDetailSchema>;

// =======================
// SerialCodeRedeem
// =======================
export const SerialCodeRedeemSchema = z
  .object({
    serialCode: z.string().min(4).max(36),
    serialCodeProjectId: z.string().uuid(),
  })
  .openapi('SerialCodeRedeemReq');
export type SerialCodeRedeemRequest = z.infer<typeof SerialCodeRedeemSchema>;

export const SerialCodeRedeemResponseSchema = z
  .object({
    accountSerialCodeId: z.string(),
    serialCodeId: z.string(),
    rewardId: z.string().nullable(),
    remainingUseNum: z.number(),
  })
  .openapi('SerialCodeRedeemRes');
export type SerialCodeRedeemResponse = z.infer<typeof SerialCodeRedeemResponseSchema>;

export const AccountCustomFieldSchema = ServiceCustomFieldResponseSchema
  .omit({ validator: true })
  .extend({
    values: z.array(z.string()).openapi({
      description: 'Values for the custom field',
      example: ["<EMAIL>"],
    }),
  })
  .openapi('AccountCustomField');

export const AccountCustomFieldsSchema = z.array(AccountCustomFieldSchema).openapi({
  description: 'List of custom fields for the account',
  example: [
    {
      "custom_field_id": "9af64bc9-96ab-4943-9797-a072ade951c0",
      "service_id": "5ac198b3-ade6-403a-9f26-1a0eafc65b09",
      "field_key": "email",
      "version": 9,
      "type": "EMAIL",
      "default_value": null,
      "max_length": 15,
      "min_length": 10,
      "unique": false,
      "verify": false,
      "optional": false,
      "sort_order": 1,
      "created_at": "2025-06-10T06:44:53.892Z",
      "label": "メールアドレス",
      "locale": "ja",
      "values": ["<EMAIL>"],
      "options": null
    }
  ]
});

export type AccountCustomFieldsResponse = z.infer<typeof AccountCustomFieldsSchema>;


export const AccountCustomFieldResponseSchema = z.object({
  customFieldId: z.string().uuid().openapi({
    description: 'Unique identifier for the custom field',
    example: 'f8107ba6-3d7a-4148-8b92-1f0ae54675c4'
  }),
  fieldKey: z.string().openapi({
    description: 'Localized label for the field',
    example: 'Email Address'
  }),
  value: z.string().openapi({
    description: 'Value of the custom field',
    example: '<EMAIL>'
  }),
}).openapi('AccountCustomFieldSchema');

export type AccountCustomFieldResponse = z.infer<typeof AccountCustomFieldResponseSchema>;

export const AccountCustomFieldsUpdateSchema = z.array(AccountCustomFieldResponseSchema).openapi({
  description: 'List of custom fields that were updated',
  example: [
    {
      customFieldId: '9af64bc9-96ab-4943-9797-a072ade951c0',
      fieldKey: 'email',
      value: '<EMAIL>'
    },
    {
      customFieldId: 'dfe3646a-1b36-42e1-8c25-0a7b31c5cee9',
      fieldKey: 'gender',
      value: 'male'
    }
  ]
})

export type AccountCustomFieldsUpdateResponse = z.infer<typeof AccountCustomFieldsUpdateSchema>;


export const AccountCustomFieldsUpdateRequestSchema = z.record(
  z.union([
    z.string().openapi({
      description: "Single-valued field (text, email, phone, etc.)",
      example: "<EMAIL>",
    }),
    z.array(z.string()).openapi({
      description: "Multi-valued field (for multi-select dropdowns)",
      example: ["male", "other"],
    })
  ])
)
  .openapi("AccountCustomFieldsUpdateRequest", {
    description: "An object whose keys are the custom field keys and whose values are either a string or an array of strings.",
    example: {
      email: "<EMAIL>",
      gender: ["male", "other"],
      phoneNumber: "+***************",
    },
  });

export type AccountCustomFieldsUpdateRequest = z.infer<typeof AccountCustomFieldsUpdateRequestSchema>;
