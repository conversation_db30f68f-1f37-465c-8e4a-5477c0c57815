import { 
  ActionCompleteSchema,
  SerialCodeRedeemSchema,
  AccountCustomFieldsUpdateRequestSchema,
  GenerateEmailOtpRequestSchema,
  ValidateEmailOtpRequestSchema
} from './schemas';

export const ActionCompleteRequestBodySchema = {
  description: 'Reports that an action has been completed and registers the status',
  required: true,
  content: {
    'application/json': {
      schema: ActionCompleteSchema,
    },
  },
}

export const SerialCodeRedeemRequestBodySchema = {
  description: 'Use serial code',
  required: true,
  content: {
    'application/json': {
      schema: SerialCodeRedeemSchema,
    },
  },
}

export const AccountCustomFieldsRequestBodySchema = {
  description: 'Update custom field values for an account',
  required: true,
  content: {
    'application/json': {
      schema: AccountCustomFieldsUpdateRequestSchema,
    },
  },
}

export const GenerateEmailOtpRequestBodySchema = {
  description: 'Request to send OTP to email',
  required: true,
  content: {
    'application/json': {
      schema: GenerateEmailOtpRequestSchema,
    }
  }
}

export const ValidateEmailOtpRequestBodySchema = {
  description: 'Request to verify email OTP',
  required: true,
  content: {
    'application/json': {
      schema: ValidateEmailOtpRequestSchema,
    }
  }
}
