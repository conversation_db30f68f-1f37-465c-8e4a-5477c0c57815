import { ActionCompleteSchema, SerialCodeRedeemSchema } from './schemas';

export const ActionCompleteRequestBodySchema = {
  description: 'Reports that an action has been completed and registers the status',
  required: true,
  content: {
    'application/json': {
      schema: ActionCompleteSchema,
    },
  },
}

export const SerialCodeRedeemRequestBodySchema = {
  description: 'Use serial code',
  required: true,
  content: {
    'application/json': {
      schema: SerialCodeRedeemSchema,
    },
  },
}
