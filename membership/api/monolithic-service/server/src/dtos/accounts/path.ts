import { z } from '@hono/zod-openapi';
import { QuestStatusSchema } from '../../enum/questStatus';
import { RewardStatusSchema } from '../../enum/rewardStatus';
import { regexPattern } from '../../utils/regex';

export const PathParameterAccountIdSchema = z.object({
  accountId: z
    .string()
    .regex(regexPattern.uuid, 'Invalid UUID v4')
    .openapi({
      param: { name: 'accountId', in: 'path', required: true },
      example: '28cc7f3c-b47d-486e-b035-************',
      description: 'Uniquely given identifier for each account',
    }),
});

export const PathParameterQuestIdSchema = z.object({
  questId: z
    .string()
    .regex(regexPattern.uuid)
    .openapi({
      param: { name: 'questId', in: 'path', required: true },
      example: '8552df83-cbd5-44db-b67e-0cbeb2785918',
      description: 'Uniquely given identifier for each quest',
    }),
});

export const PathParameterQuestionnaireIdSchema = z.object({
  questionnaireId: z
    .string()
    .regex(regexPattern.uuid)
    .openapi({
      param: { name: 'questionnaireId', in: 'path', required: true },
      example: '8552df83-cbd5-44db-b67e-0cbeb2785918',
      description: 'Uniquely given identifier for each questionnaire',
    }),
});

export const PathParameterActionIdSchema = z.object({
  actionId: z
    .string()
    .regex(regexPattern.uuid)
    .openapi({
      param: { name: 'actionId', in: 'path', required: true },
      example: '28cc7f3c-b47d-486e-b035-************',
      description: 'Uniquely given identifier for each action',
    }),
});

export const PathParameterRewardIdSchema = z.object({
  rewardId: z
    .string()
    .regex(regexPattern.uuid)
    .openapi({
      param: { name: 'rewardId', in: 'path', required: true },
      example: '5850b40d-333b-4fbe-8c87-8e34d0f47404',
      description: 'Uniquely given identifier for each action',
    }),
});

export const PathParameterNotificationIdSchema = z.object({
  notificationId: z
    .string()
    .regex(regexPattern.uuid)
    .openapi({
      param: { name: 'notificationId', in: 'path', required: true },
      example: 'e9f1f7d1-ff3c-4d12-b66e-00f9f4e1a889',
      description: 'Uniquely given identifier for each notification',
    }),
});

export const ExpireAtFromSchema = z.object({
  expireAtFrom: z
    .string()
    .regex(regexPattern.isoDateTime)
    .optional()
    .openapi({
      param: { name: 'expireAtFrom', in: 'query', required: false },
      example: '2024-01-01T10:00:00Z',
      description:
        '* Start date of the date the quest expire\n* validation\n  * Conforms to ISO8601\n  * expireAtTo is not null\n  * expireAtTo is later than expireAtFrom',
    }),
});

export const ExpireAtToSchema = z.object({
  expireAtTo: z
    .string()
    .regex(regexPattern.isoDateTime)
    .optional()
    .openapi({
      param: { name: 'expireAtTo', in: 'query', required: false },
      example: '2024-01-01T10:00:00Z',
      description:
        '* End date of the date the quest expire\n* validation\n  * Conforms to ISO8601 YYYY-MM-DDThh:mm:ss.sssZ\n  * expireAtFrom is not null\n  * expireAtTo is later than expireAtFrom',
    }),
});

export const StartAtFromSchema = z.object({
  startAtFrom: z
    .string()
    .regex(regexPattern.isoDateTime)
    .optional()
    .openapi({
      param: { name: 'startAtFrom', in: 'query', required: false },
      example: '2024-01-01T10:00:00Z',
      description:
        '* Start date of the date the quest start\n* validation\n  * Conforms to ISO8601 YYYY-MM-DDThh:mm:ss.sssZ\n  * StartAtTo is not null\n  * StartAtTo is later than StartAtFrom',
    }),
});

export const StartAtToSchema = z.object({
  startAtTo: z
    .string()
    .regex(regexPattern.isoDateTime)
    .optional()
    .openapi({
      param: { name: 'startAtTo', in: 'query', required: false },
      example: '2024-01-01T10:00:00Z',
      description:
        '* End date of the date the quest start\n* validation\n  * Conforms to ISO8601 YYYY-MM-DDThh:mm:ss.sssZ\n  * StartAtFrom is not null\n  * StartAtTo is later than StartAtFrom',
    }),
});

export const PostClaimRewardPathSchema = PathParameterAccountIdSchema.merge(PathParameterRewardIdSchema);

const questStatusParamSchema = z.object({
  questStatus: QuestStatusSchema.optional(),
});
const rewardStatusParamSchema = z.object({
  rewardStatus: RewardStatusSchema.optional(),
});

export const GetAccountQuestsQuerySchema = ExpireAtFromSchema.merge(ExpireAtToSchema)
  .merge(StartAtFromSchema)
  .merge(StartAtToSchema)
  .merge(questStatusParamSchema)
  .merge(rewardStatusParamSchema)
  .superRefine((data, ctx) => {
    const isValidRange = (from?: string, to?: string, fromKey = '', toKey = '') => {
      if (from && to) {
        const fromDate = new Date(from);
        const toDate = new Date(to);
        if (fromDate > toDate) {
          ctx.addIssue({
            path: [toKey],
            code: z.ZodIssueCode.custom,
            message: `${toKey} must be later than ${fromKey}`,
          });
        }
      }
    };
    isValidRange(data.expireAtFrom, data.expireAtTo, 'expireAtFrom', 'expireAtTo');
    isValidRange(data.startAtFrom, data.startAtTo, 'startAtFrom', 'startAtTo');
  });

export const PathParameterGachaIdSchema = z.object({
  gachaId: z
    .string()
    .regex(regexPattern.uuid, 'Invalid UUID v4')
    .openapi({
      param: { name: 'gachaId', in: 'path', required: true },
      example: '28cc7f3c-b47d-486e-b035-************',
      description: 'Uniquely given identifier for each gacha',
    }),
});

