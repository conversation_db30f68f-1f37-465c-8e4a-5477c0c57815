import {
  AccountSchema,
  StatusSchema,
  ActivityHistorySchema,
  QuestActivityStatusSchema,
  QuestActivityDetailSchema,
  ClaimedRewardSchema,
  NotificationListSchema,
  AccountQuestionnaireDetailSchema,
  SerialCodeRedeemResponseSchema as SerialCodeRedeemSchema,
  GachaListResponseSchema as GachaListSchema,
  ExecutedGachaResponseSchema as ExecutedGachaSchema,
  AccountCustomFieldsSchema,
  AccountCustomFieldsUpdateSchema,
  EmailOtpResponseSchema,
} from './schemas';

export const AccountResponseSchema = {
  description: 'successful operation',
  content: {
    'application/json': {
      schema: AccountSchema,
    },
  },
};

export const AccountDeleteResponseSchema = {
  description: 'successful operation',
};

export const AccountStatusResponseSchema = {
  description: 'successful operation',
  content: {
    'application/json': {
      schema: StatusSchema,
    },
  },
};

export const ActivityHistoriesResponseSchema = {
  description: 'successful operation',
  content: {
    'application/json': {
      schema: ActivityHistorySchema,
    },
  },
};

export const ActivityQuestsResponseSchema = {
  description: 'successful operation',
  content: {
    'application/json': {
      schema: QuestActivityStatusSchema,
    },
  },
};

export const ActivityQuestDetailResponseSchema = {
  description: 'successful operation',
  content: {
    'application/json': {
      schema: QuestActivityDetailSchema,
    },
  },
};

export const ActivityQuestCompleteResponseSchema = {
  description: 'successful operation',
};

export const ActivityRewardClaimResponseSchema = {
  description: 'successful operation',
  content: {
    'application/json': {
      schema: ClaimedRewardSchema,
    },
  },
};

export const NotificationListResponseSchema = {
  description: 'successful operation',
  content: {
    'application/json': {
      schema: NotificationListSchema,
    },
  },
};

export const AccountQuestionnaireDetailResponseSchema = {
  description: 'successful operation',
  content: {
    'application/json': {
      schema: AccountQuestionnaireDetailSchema,
    },
  },
};

export const SerialCodeRedeemResponseSchema = {
  description: 'Serial code redeem and claim reward successful',
  content: {
    'application/json': {
      schema: ClaimedRewardSchema,
    },
  },
};

export const GachaListResponseSchema = {
  description: 'Get Gacha List',
  content: {
    'application/json': {
      schema: GachaListSchema,
    },
  },
};

export const ExecutedGachaResponseSchema = {
  description: 'Executed Gacha',
  content: {
    'application/json': {
      schema: ExecutedGachaSchema,
    },
  },
};

export const AccountCustomFieldsResponseSchema = {
  description: 'Account custom field values',
  content: {
    'application/json': {
      schema: AccountCustomFieldsSchema,
    },
  },
};

export const AccountCustomFieldsUpdateResponseSchema = {
  description: 'Account custom field values updated successfully',
  content: {
    'application/json': {
      schema: AccountCustomFieldsUpdateSchema,
    },
  },
};

export const AccountLastLoginResponseSchema = {
  description: 'successful operation',
};

export const GenerateEmailOtpResponseSchema = {
  description: 'Verification code sent successfully',
  content: {
    'application/json': {
      schema: EmailOtpResponseSchema,
    }
  }
} as const;

export const ValidateEmailOtpResponseSchema = {
  description: 'Email verification result',
  content: {
    'application/json': {
      schema: EmailOtpResponseSchema,
    }
  }
} as const;