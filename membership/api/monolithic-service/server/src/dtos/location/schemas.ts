import { z } from 'zod';
import { openApiRegistry } from '../../utils/openApiRegistry';

// =======================
// Location Coordinates
// =======================
export const LocationCoordinatesSchema = z.object({
  latitude: z.number()
    .min(-90, 'Latitude must be between -90 and 90')
    .max(90, 'Latitude must be between -90 and 90')
    .openapi({
      description: 'Latitude coordinate in decimal degrees',
      example: 35.6762,
      minimum: -90,
      maximum: 90
    }),
  longitude: z.number()
    .min(-180, 'Longitude must be between -180 and 180')
    .max(180, 'Longitude must be between -180 and 180')
    .openapi({
      description: 'Longitude coordinate in decimal degrees',
      example: 139.6503,
      minimum: -180,
      maximum: 180
    }),
  accuracy: z.number()
    .min(0, 'Accuracy must be non-negative')
    .optional()
    .openapi({
      description: 'GPS accuracy in meters',
      example: 5.0,
      minimum: 0
    })
}).openapi('LocationCoordinates');

export type LocationCoordinates = z.infer<typeof LocationCoordinatesSchema>;

// =======================
// Geofence Types
// =======================
export const GeofenceTypeSchema = z.enum(['CIRCLE', 'POLYGON']).openapi({
  description: 'Type of geofence geometry',
  example: 'CIRCLE'
});

export type GeofenceType = z.infer<typeof GeofenceTypeSchema>;

// =======================
// Polygon Coordinates
// =======================
export const PolygonCoordinatesSchema = z.array(
  z.array(z.number()).length(2)
).min(3).openapi({
  description: 'Array of [longitude, latitude] coordinate pairs forming a polygon (minimum 3 points)',
  example: [
    [139.7671, 35.6812],
    [139.7681, 35.6812],
    [139.7681, 35.6822],
    [139.7671, 35.6822],
    [139.7671, 35.6812]
  ]
});

export type PolygonCoordinates = z.infer<typeof PolygonCoordinatesSchema>;

// =======================
// Geofence Response (Supports both Circle and Polygon)
// =======================
export const BaseGeofenceSchema = z.object({
  geofenceId: z.string().openapi({
    description: 'Unique identifier for the geofence',
    example: 'geofence-123'
  }),
  serviceId: z.string().openapi({
    description: 'Service identifier',
    example: 'service-123'
  }),
  name: z.string().openapi({
    description: 'Name of the geofence',
    example: 'Tokyo Station'
  }),
  description: z.string().optional().openapi({
    description: 'Description of the geofence',
    example: 'Check-in location for Tokyo Station quest'
  }),
  centerPinName: z.string().optional().openapi({
    description: 'Name of the center pin/landmark',
    example: 'Tokyo Station Main Entrance'
  }),
  geofenceType: GeofenceTypeSchema.openapi({
    description: 'Type of geofence geometry'
  }),
  isActive: z.boolean().openapi({
    description: 'Whether the geofence is active',
    example: true
  }),
  createdAt: z.string().datetime().openapi({
    description: 'Creation timestamp',
    example: '2023-01-01T00:00:00Z'
  }),
  updatedAt: z.string().datetime().openapi({
    description: 'Last update timestamp',
    example: '2023-01-01T00:00:00Z'
  })
});

export const CircleGeofenceSchema = BaseGeofenceSchema.extend({
  geofenceType: z.literal('CIRCLE'),
  center: LocationCoordinatesSchema.pick({ latitude: true, longitude: true }).openapi({
    description: 'Center coordinates of the circle'
  }),
  radiusMeters: z.number().openapi({
    description: 'Radius of the circle in meters',
    example: 100
  })
}).openapi('CircleGeofence');

export const PolygonGeofenceSchema = BaseGeofenceSchema.extend({
  geofenceType: z.literal('POLYGON'),
  coordinates: PolygonCoordinatesSchema.openapi({
    description: 'Array of coordinate pairs defining the polygon boundary'
  }),
  boundingBox: z.object({
    northEast: LocationCoordinatesSchema.pick({ latitude: true, longitude: true }),
    southWest: LocationCoordinatesSchema.pick({ latitude: true, longitude: true })
  }).optional().openapi({
    description: 'Bounding box of the polygon for optimization'
  })
}).openapi('PolygonGeofence');

export const GeofenceSchema = z.discriminatedUnion('geofenceType', [
  CircleGeofenceSchema,
  PolygonGeofenceSchema
]).openapi('Geofence');

export type Geofence = z.infer<typeof GeofenceSchema>;
export type CircleGeofence = z.infer<typeof CircleGeofenceSchema>;
export type PolygonGeofence = z.infer<typeof PolygonGeofenceSchema>;

// =======================
// Location Check-in Request
// =======================
export const LocationCheckinRequestSchema = z.object({
  actionId: z.string().uuid().openapi({
    description: 'Action ID for the location check-in',
    example: '123e4567-e89b-12d3-a456-************'
  }),
  location: LocationCoordinatesSchema.openapi({
    description: 'Current location coordinates'
  }),
  timestamp: z.string().datetime().optional().openapi({
    description: 'Timestamp when location was captured (defaults to current time)',
    example: '2023-01-01T12:00:00Z'
  })
}).openapi('LocationCheckinRequest');

export type LocationCheckinRequest = z.infer<typeof LocationCheckinRequestSchema>;

// =======================
// Location Check-in Response
// =======================
export const LocationCheckinResponseSchema = z.object({
  success: z.boolean().openapi({
    description: 'Whether the check-in was successful',
    example: true
  }),
  actionId: z.string().uuid().openapi({
    description: 'Action ID that was checked',
    example: '123e4567-e89b-12d3-a456-************'
  }),
  distanceFromTarget: z.number().openapi({
    description: 'Distance from geofence center in meters',
    example: 45.5
  }),
  withinGeofence: z.boolean().openapi({
    description: 'Whether the location is within the required geofence',
    example: true
  }),
  geofence: GeofenceSchema.optional().openapi({
    description: 'Geofence information for the action'
  }),
  message: z.string().optional().openapi({
    description: 'Additional message about the check-in result',
    example: 'Successfully checked in at Tokyo Station'
  }),
  completedAt: z.string().datetime().optional().openapi({
    description: 'Timestamp when action was completed (if successful)',
    example: '2023-01-01T12:00:00Z'
  })
}).openapi('LocationCheckinResponse');

export type LocationCheckinResponse = z.infer<typeof LocationCheckinResponseSchema>;

// =======================
// Create Geofence Request (Supports both Circle and Polygon)
// =======================
export const CreateCircleGeofenceSchema = z.object({
  type: z.literal('CIRCLE'),
  name: z.string().min(1).max(255).openapi({
    description: 'Name of the geofence',
    example: 'Tokyo Station Circle'
  }),
  description: z.string().optional().openapi({
    description: 'Description of the geofence',
    example: 'Circular check-in area around Tokyo Station'
  }),
  centerPinName: z.string().optional().openapi({
    description: 'Name of the center pin/landmark',
    example: 'Tokyo Station Main Entrance'
  }),
  center: LocationCoordinatesSchema.pick({ latitude: true, longitude: true }).openapi({
    description: 'Center coordinates of the circle'
  }),
  radiusMeters: z.number()
    .min(1)
    .max(10000)
    .openapi({
      description: 'Radius of the circle in meters (max 10km)',
      example: 100
    })
}).openapi('CreateCircleGeofence');

export const CreatePolygonGeofenceSchema = z.object({
  type: z.literal('POLYGON'),
  name: z.string().min(1).max(255).openapi({
    description: 'Name of the geofence',
    example: 'Tokyo Station Building'
  }),
  description: z.string().optional().openapi({
    description: 'Description of the geofence',
    example: 'Polygon area covering the entire Tokyo Station building complex'
  }),
  centerPinName: z.string().optional().openapi({
    description: 'Name of the center pin/landmark',
    example: 'Tokyo Station Central Hall'
  }),
  coordinates: PolygonCoordinatesSchema.openapi({
    description: 'Array of coordinate pairs defining the polygon boundary'
  })
}).openapi('CreatePolygonGeofence');

export const CreateGeofenceRequestSchema = z.discriminatedUnion('type', [
  CreateCircleGeofenceSchema,
  CreatePolygonGeofenceSchema
]).openapi('CreateGeofenceRequest');

export type CreateGeofenceRequest = z.infer<typeof CreateGeofenceRequestSchema>;
export type CreateCircleGeofence = z.infer<typeof CreateCircleGeofenceSchema>;
export type CreatePolygonGeofence = z.infer<typeof CreatePolygonGeofenceSchema>;

// =======================
// Location Action Data
// =======================
export const LocationActionDataSchema = z.object({
  geofenceId: z.string().uuid().openapi({
    description: 'Geofence ID for the location action',
    example: '123e4567-e89b-12d3-a456-************'
  }),
  requiredDurationSeconds: z.number()
    .min(0)
    .default(0)
    .openapi({
      description: 'Minimum time to stay in geofence (in seconds)',
      example: 30
    })
}).openapi('LocationActionData');

export type LocationActionData = z.infer<typeof LocationActionDataSchema>;

// Register schemas with OpenAPI
openApiRegistry.register('LocationCoordinates', LocationCoordinatesSchema);
openApiRegistry.register('Geofence', GeofenceSchema);
openApiRegistry.register('LocationCheckinRequest', LocationCheckinRequestSchema);
openApiRegistry.register('LocationCheckinResponse', LocationCheckinResponseSchema);
openApiRegistry.register('CreateGeofenceRequest', CreateGeofenceRequestSchema);
openApiRegistry.register('LocationActionData', LocationActionDataSchema);
