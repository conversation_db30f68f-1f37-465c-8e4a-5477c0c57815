import { z } from '@hono/zod-openapi';
import {
  GeneralMetadataSchema,
  NftDeploySchema,
  TransactionDataSchema,
  NftMetadataUnionSchema,
  RetryTransactionResponseSchema,
  GetI18nNFTMetadataResponseSchema,
  BulkMintNftSchema,
  TxFinalityStatusSchema,
  GrantedNftContractSchema,
  RegsteredModularContractSchema as RegisteredModularContractSchema,
} from './schemas';
import {
  validationErrorSchema,
  unauthorizedErrorSchema,
  serverErrorSchema,
  serviceUnavailableErrorSchema,
  notFoundErrorSchema,
  gatewayTimeoutErrorSchema,
} from '../utils/errorResponse';

// GeneralMetadataResponse
export const generalMetadataResponseBody = {
  description: 'successful operation',
  content: {
    'application/json': {
      schema: GeneralMetadataSchema,
    },
  },
};

// NftRegisterResponse
export const nftRegisterResponseBody = {
  description: 'NFT register response',
  content: {
    'application/json': {
      schema: NftDeploySchema,
    },
  },
};
// GetMetadataResponse
export const getMetadataResponseBody = {
  description: 'A list of NFT metadata',
  content: {
    'application/json': {
      schema: NftMetadataUnionSchema,
    },
  },
};

// NftMintResponse
export const nftMintResponseBody = {
  description: 'Returns the id that ties the transaction status of the mint',
  content: {
    'application/json': {
      schema: TransactionDataSchema,
    },
  },
};

// NftRetryTransactionsResponse
export const nftRetryTransactionsResponseBody = {
  description: 'Returns the transaction status of the retry',
  content: {
    'application/json': {
      schema: z.array(RetryTransactionResponseSchema),
    },
  },
};

// GetI18nNFTMetadataResponse
export const getI18nNFTMetadataResponseBody = {
  description: 'Returns the i18n NFT metadata',
  content: {
    'application/json': {
      schema: GetI18nNFTMetadataResponseSchema,
    },
  },
};

export const getI18nNFTMetadataErrorResponses = {
  400: {
    description: 'Validation error',
    content: {
      'application/json': {
        schema: validationErrorSchema,
      },
    },
  },
  401: {
    description: 'Unauthenticated',
    content: {
      'application/json': {
        schema: unauthorizedErrorSchema,
      },
    },
  },
  404: {
    description: 'Not found',
    content: {
      'application/json': {
        schema: notFoundErrorSchema,
      },
    },
  },
  500: {
    description: 'Server error',
    content: {
      'application/json': {
        schema: serverErrorSchema,
      },
    },
  },
  503: {
    description: 'Service unavailable',
    content: {
      'application/json': {
        schema: serviceUnavailableErrorSchema,
      },
    },
  },
  504: {
    description: 'Gateway timeout',
    content: {
      'application/json': {
        schema: gatewayTimeoutErrorSchema,
      },
    },
  },
};

export const bulkMintNftResponseBody = {
  description: 'Returns the bulk mint NFT response',
  content: {
    'application/json': {
      schema: BulkMintNftSchema,
    },
  },
};

export const txFinalityStatusResponseBody = {
  description: 'Returns the transaction finality status',
  content: {
    'application/json': {
      schema: TxFinalityStatusSchema,
    },
  },
};

export const deployModularNFTResponseBody = {
  description: 'Returns the deployed modular NFT response',
  content: {
    'application/json': {
      schema: RegisteredModularContractSchema,
    },
  },
};

export const grantNftsMinterRoleResponseBody = {
  description: 'Returns the response for granting NFT minter role',
  content: {
    'application/json': {
      schema: GrantedNftContractSchema,
    },
  },
};