import { z } from '@hono/zod-openapi';
import { regexPattern } from '../../utils/regex';
import { RewardType, RewardTypeSchema } from '../../enum/rewardType';
import { NftTag, NftTagSchema } from '../../enum/nftTag';
import { NftTypeSchema } from '../../enum/nftType';

// =======================
// GeneralMetadata
// =======================
export const GeneralMetadataSchema = z
  .object({
    name: z.string().openapi({
      description: 'Name of the NFT.',
      example: 'Cool NFT',
    }),
    description: z.string().openapi({
      description: 'Description of the NFT.',
      example: 'This is a cool NFT that represents ownership of unique digital art.',
    }),
    image: z.string().openapi({
      description: 'URI to the image associated with the NFT.',
      example: 'https://example.com/image.png',
    }),
    external_url: z
      .string()
      .openapi({
        description: 'External link to the item.',
        example: 'https://example.com',
      })
      .optional(),
    animation_url: z
      .string()
      .openapi({
        description: 'URI for multimedia content such as video or audio.',
        example: 'https://example.com/video.mp4',
      })
      .optional(),
    background_color: z
      .string()
      .openapi({
        description: 'Background color of the item without the `#` prefix.',
        example: '000000',
      })
      .optional(),
    youtube_url: z
      .string()
      .openapi({
        description: 'YouTube video link associated with the NFT.',
        example: 'https://youtube.com/watch?v=dQw4w9WgXcQ',
      })
      .optional(),
    attributes: z
      .array(
        z.object({
          trait_type: z.string().openapi({
            description: 'Type of the attribute.',
            example: 'Color',
          }),
          value: z.string().openapi({
            description: 'Value of the attribute.',
            example: 'Blue',
          }),
        }),
      )
      .openapi({ description: 'Traits or attributes of the NFT.' })
      .optional(),
  })
  .openapi('GeneralMetadata');

// =======================
// NftContractIds
// =======================
export const NftContractIdsSchema = z
  .array(
    z.string().regex(regexPattern.uuid).openapi({
      description: 'The uniquely given id of the nft contract',
      example: 'cee3e4aa-b0bf-482d-bb70-57a979c032c5',
      pattern: regexPattern.uuid.source,
    }),
  )
  .openapi('NftContractIds');

// =======================
// NftFilter
// =======================
export const NftFilterSchema = z
  .object({
    nftContractId: z.string().regex(regexPattern.uuid).openapi({
      description: 'The uniquely given id of the nft contract',
      example: 'cee3e4aa-b0bf-482d-bb70-57a979c032c5',
      pattern: regexPattern.uuid.source,
    }),
    nftTokenId: z
      .number()
      .openapi({
        description: 'The uniquely given id of the token',
        example: 32,
      })
      .nullable()
      .optional(),
  })
  .openapi('NftFilter');

// =======================
// NftMetadataQuery
// =======================
export const NftMetadataQuerySchema = z
  .object({
    serviceId: z
      .string()
      .regex(regexPattern.uuid)
      .openapi({
        description: 'The uniquely given id of the service',
        example: '8552df83-cbd5-44db-b67e-0cbeb2785918',
        pattern: regexPattern.uuid.source,
      })
      .optional(),
    walletAddress: z
      .string()
      .regex(regexPattern.ethereumAddress)
      .openapi({
        description: '* Address format with EVM checksum according to `EIP-55`\n* Perform checksum verification',
        example: '******************************************',
        pattern: regexPattern.ethereumAddress.source,
      })
      .optional(),
    includes: z
      .array(NftFilterSchema)
      .openapi({
        description:
          'Specify the contract among the NFTs held in the Wallet that is the target of acquisition of metadata',
      })
      .optional(),
    isDetail: z
      .boolean()
      .openapi({
        description: 'Whether to retrieve detailed metadata information',
        example: false,
      })
      .optional(),
  })
  .openapi('NftMetadataQuery');

// =======================
// NftRegister
// =======================
export const NftRegisterSchema = z
  .object({
    nftContractTypeId: z.string().regex(regexPattern.uuid, 'Invalid nftContractTypeId format').openapi({
      description: 'The uniquely given id of the NFT format',
      example: '8552df83-cbd5-44db-b67e-0cbeb2785918',
      pattern: regexPattern.uuid.source,
    }),
    nftName: z.string().min(1).openapi({
      description: 'Name to be specified when issuing NFT',
      example: 'marbull NFT',
    }),
    nftSymbol: z
      .string()
      .regex(/^[A-Z0-9]{3,5}$/)
      .min(1)
      .openapi({
        description: 'Symbol to be specified when issuing NFT',
        example: 'MBL1',
        pattern: '^[A-Z0-9]{3,5}$',
      }),
    nftCollectionName: z.string().min(1).openapi({
      description: 'Name of the NFT collection',
      example: 'Marbull NFT collection',
    }),
    metadata: z
      .string()
      .min(1)
      .refine((desc) => {
        try {
          const decoded = Buffer.from(desc, 'base64').toString('utf8');
          JSON.parse(decoded);
          return true;
        } catch {
          return false;
        }
      })
      .openapi({
        description: 'URI to the metadata of the NFT',
        example: '',
      }),
    deliveryImageUrl: z
      .string()
      .url('Invalid delivery image URL')
      .max(512, 'URL too long')
      .openapi({
        description: 'URI to the delivery image of the NFT',
        example: '',
      })
      .optional(),
  })
  .openapi('NftRegister');

// =======================
// NftDeploy
// =======================
export const NftDeploySchema = z
  .object({
    nftContractId: z.string().regex(regexPattern.uuid).openapi({
      description: 'registered NFT Contract Id',
      example: '8552df83-cbd5-44db-b67e-0cbeb2785918',
      pattern: regexPattern.uuid.source,
    }),
    contractAddress: z.string().regex(regexPattern.ethereumAddress).openapi({
      description: 'registered NFT Contract Address',
      example: '******************************************',
      pattern: regexPattern.ethereumAddress.source,
    }),
  })
  .openapi('NftDeploy');

// =======================
// RequestAccount
// =======================
export const RequestAccountSchema = z
  .object({
    accountId: z.string().regex(regexPattern.uuid).openapi({
      description: 'The uniquely given id of the account',
      example: 'cee3e4aa-b0bf-482d-bb70-57a979c032c5',
      pattern: regexPattern.uuid.source,
    }),
    rewardType: RewardTypeSchema.openapi({
      description: 'The type of the reward',
      example: 'COUPON',
    }),
  })
  .openapi('RequestAccount');

// =======================
// NftList (Discriminator)
// =======================
export const NftListSchema = z
  .object({
    type: z.enum(['coupons', 'certificates', 'contents']).openapi({
      description: 'The type of the NFT',
      example: 'coupons',
    }),
  })
  .openapi('NftList');

// =======================
// CouponNftOverview
// =======================
export const CouponNftOverviewSchema = z
  .object({
    fromDate: z.date().openapi({
      description: 'The start date when the coupon is available',
      example: '2024-01-01T10:00:00Z',
      pattern: regexPattern.isoDateTimeWithOffset.source,
    }),
    toDate: z.date().openapi({
      description: 'The expiration date of the coupon',
      example: '2024-01-01T10:00:00Z',
    }),
    tags: NftTagSchema.openapi({
      description: 'Tags indicating discount or exchange',
      example: NftTag.DISCOUNT_COUPON,
    }),
  })
  .openapi('CouponNftOverview');

// =======================
// CouponNftDetail
// =======================
export const CouponNftDetailSchema = z
  .object({
    janCode: z
      .string()
      .regex(regexPattern.janCode)
      .openapi({
        description: 'JAN code of the coupon. accept null',
        example: '1234567890128',
        pattern: regexPattern.janCode.source,
      })
      .optional(),
    discountAmount: z
      .number()
      .openapi({
        description: 'The discount amount associated with the coupon',
        example: 1000,
        format: 'float',
      })
      .optional(),
    discountUnit: z
      .string()
      .openapi({
        description: 'The unit of the discount amount',
        example: 'JPY',
      })
      .optional(),
    exchangeProduct: z
      .string()
      .openapi({
        description: 'The product to be exchanged',
        example: 'Product A',
      })
      .optional(),
  })
  .openapi('CouponNftDetail');

// =======================
// CertificateNftOverview
// =======================
export const CertificateNftOverviewSchema = z
  .object({
    rank: z
      .number()
      .openapi({
        description:
          '- When status is "status", the higher the number, the higher the priority certificate, and the higher the number, the higher the priority certificate.\n- Always 0 for standard',
        example: 3,
      })
      .optional(),
    tags: NftTagSchema.openapi({
      description: 'Tags indicating standard or status',
      example: NftTag.STATUS_CERTIFICATE,
    }),
  })
  .openapi('CertificateNftOverview');

// =======================
// ContentNftOverview
// =======================
export const ContentNftOverviewSchema = z
  .object({
    tags: NftTagSchema.openapi({
      description: 'Tags indicating digital content',
      example: NftTag.DIGITAL_CONTENT,
    }),
  })
  .openapi('ContentNftOverview');

// =======================
// CertificateNftDetail
// =======================
export const CertificateNftDetailSchema = z
  .object({
    publishDate: z.date().openapi({
      description: 'The start date when the coupon is available',
      example: '2024-01-01T10:00:00Z',
    }),
    expireDate: z.date().openapi({
      description: 'The expiration date of the coupon',
      example: '2024-01-01T10:00:00Z',
    }),
    issuer: z.string().openapi({
      description: 'The issuer of the certificate',
      example: 'Marbull',
    }),
  })
  .openapi('CertificateNftDetail');

// =======================
// NftMetadata
// =======================
export const NftMetadataSchema = z
  .object({
    rewardId: z
      .string()
      .regex(regexPattern.uuid)
      .openapi({
        description: 'The uniquely given id of the reward',
        example: '8552df83-cbd5-44db-b67e-0cbeb2785918',
        pattern: regexPattern.uuid.source,
      })
      .optional(),
    nftContractId: z.string().regex(regexPattern.uuid).openapi({
      description: 'The uniquely given id of the nft contract',
      example: 'cee3e4aa-b0bf-482d-bb70-57a979c032c5',
      pattern: regexPattern.uuid.source,
    }),
    contractAddress: z.string().regex(regexPattern.ethereumAddress).openapi({
      description: 'ERC721/ERC1155 contract address of the coupon',
      example: '******************************************',
      pattern: regexPattern.ethereumAddress.source,
    }),
    tokenId: z.number().openapi({
      description: 'NFT tokenId',
      example: 32,
    }),
    amount: z.number().openapi({
      description: '- Number of this NFT owned\n  - Always 1 for ERC721\n  - 1 or more for ERC1155',
      example: 1,
    }),
    title: z
      .string()
      .regex(/^.{4,128}$/)
      .openapi({
        description: 'The name of the NFT',
        example: '10% Off Discount Coupon',
        pattern: '^.{4,128}$',
      }),
    description: z.string().openapi({
      description: 'Markdown text encoded in base64',
      format: 'byte',
      example: 'IyBBcHBsaWVzIHRvIGFsbCBwdXJjaGFzZXMgb3ZlciAkNTAu',
    }),
    imageUrl: z.string().regex(regexPattern.imageUrl).openapi({
      description: 'URL of the coupon image',
      example: 'https://example.com/images/coupon.png',
      format: 'uri',
      pattern: regexPattern.imageUrl.source,
    }),
  })
  .openapi('NftMetadata');

export const CouponNftMetadataSchema = z
  .intersection(NftMetadataSchema, z.intersection(CouponNftOverviewSchema, CouponNftDetailSchema))
  .openapi({ description: 'coupon NFTs with overview and detail' })
  .openapi('CouponNftMetadata');

// =======================
// CouponNft
// =======================
export const CouponNftSchema = z
  .object({
    type: z.literal(RewardType.COUPON),
    coupons: z.array(CouponNftMetadataSchema).openapi({ description: 'List of coupon NFTs with overview and detail' }),
  })
  .openapi('CouponNft');

// =======================
// CertificateNft
// =======================
export const CertificateNftMetadataSchema = z
  .intersection(NftMetadataSchema, z.intersection(CertificateNftOverviewSchema, CertificateNftDetailSchema))
  .openapi({ description: 'certificate NFTs with overview and detail' })
  .openapi('CertificateNftMetadata');

export const CertificateNftSchema = z
  .object({
    type: z.literal(RewardType.CERTIFICATE),
    certificates: z
      .array(CertificateNftMetadataSchema)
      .openapi({ description: 'List of certificate NFTs with overview and detail' }),
  })
  .openapi('CertificateNft');

// =======================
// ContentNft
// =======================
export const ContentNftMetadataSchema = z
  .intersection(NftMetadataSchema, ContentNftOverviewSchema)
  .openapi({ description: 'content NFTs with overview' })
  .openapi('ContentNftMetadata');

export const ContentNftSchema = z
  .object({
    type: z.literal(RewardType.CONTENT),
    contents: z.array(ContentNftMetadataSchema).openapi({ description: 'List of content NFTs with overview' }),
  })
  .openapi('ContentNft');

// =======================
// NftMintRequest
// =======================
export const NftMintRequestSchema = z
  .object({
    serviceId: z.string().regex(regexPattern.uuid, 'Invalid serviceId format').openapi({
      description: 'Id paid out uniquely for each tenant service',
      example: '8552df83-cbd5-44db-b67e-0cbeb2785918',
      pattern: regexPattern.uuid.source,
    }),
    accountId: z.string().regex(regexPattern.uuid, 'Invalid accountId format').openapi({
      description: 'Id paid out uniquely for each tenant account',
      example: '8552df83-cbd5-44db-b67e-0cbeb2785918',
      pattern: regexPattern.uuid.source,
    }),
    nftType: NftTypeSchema.openapi({
      description: 'Specify the type of nft to issue',
      example: 'CONTENT',
    }),
    nftContractId: z.string().regex(regexPattern.uuid, 'Invalid nftContractId format').openapi({
      description: 'The uniquely given id of the nft contract',
      example: 'cee3e4aa-b0bf-482d-bb70-57a979c032c5',
      pattern: regexPattern.uuid.source,
    }),
    nftTokenId: z.number().int().nullable().optional().openapi({
      description: 'The uniquely given id of the token',
      example: 32,
    }),
    toAddress: z.string().regex(regexPattern.ethereumAddress, 'Invalid toAddress format').openapi({
      description: "WalletAddress of NFT's Mint destination",
      example: '******************************************',
      pattern: regexPattern.ethereumAddress.source,
    }),
  })
  .openapi('NftMintRequest');

// =======================
// NftRetryTransactionRequest
// =======================
export const NftRetryTransactionRequestSchema = z
  .object({
    transactionId: z.string().regex(regexPattern.uuid).openapi({
      description: 'Id paid out uniquely for each vault transaction queues table record',
      example: '8552df83-cbd5-44db-b67e-0cbeb2785918',
      pattern: regexPattern.uuid.source,
    }),
  })
  .openapi('NftRetryTransactionRequest');

// =======================
// NftMintMembershipData
// =======================
export const NftMintMembershipDataSchema = z
  .object({
    serviceId: z
      .string()
      .regex(regexPattern.uuid)
      .openapi({
        description: 'Id paid out uniquely for each tenant service',
        example: '8552df83-cbd5-44db-b67e-0cbeb2785918',
        pattern: regexPattern.uuid.source,
      })
      .optional(),
    nftContractId: z
      .string()
      .regex(regexPattern.uuid)
      .openapi({
        description: 'The uniquely given id of the nft contract',
        example: 'cee3e4aa-b0bf-482d-bb70-57a979c032c5',
        pattern: regexPattern.uuid.source,
      })
      .optional(),
    toAddress: z
      .string()
      .regex(regexPattern.ethereumAddress)
      .openapi({
        description: "WalletAddress of NFT's Mint destination",
        example: '******************************************',
        pattern: regexPattern.ethereumAddress.source,
      })
      .optional(),
  })
  .openapi('NftMintMembershipData');

// =======================
// TransactionData
// =======================
export const TransactionDataSchema = z
  .object({
    queueId: z.string().regex(regexPattern.uuid).openapi({
      description: 'Id of uniquely paid out transaction',
      example: '8552df83-cbd5-44db-b67e-0cbeb2785918',
      pattern: regexPattern.uuid.source,
    }),
    contractAddress: z.string().regex(regexPattern.ethereumAddress).openapi({
      description: 'ERC721/ERC1155 contract address of the coupon',
      example: '******************************************',
      pattern: regexPattern.ethereumAddress.source,
    }),
    tokenBoundAccountAddress: z.string().regex(regexPattern.ethereumAddress).openapi({
      description: 'TBA wallet address linked membership NFT',
      example: '******************************************',
      pattern: regexPattern.ethereumAddress.source,
    }),
  })
  .openapi('TransactionData');

// =======================
// RetryTransactionResponse
// =======================
export const RetryTransactionResponseSchema = z
  .object({
    transactionId: z
      .string()
      .regex(regexPattern.uuid)
      .openapi({
        description: 'Id of uniquely paid out transaction',
        example: '8552df83-cbd5-44db-b67e-0cbeb2785918',
        pattern: regexPattern.uuid.source,
      })
      .optional(),
    toAddress: z
      .string()
      .regex(regexPattern.ethereumAddress)
      .openapi({
        description: 'destination address',
        example: '******************************************',
        pattern: regexPattern.ethereumAddress.source,
      })
      .optional(),
    fromAddress: z
      .string()
      .regex(regexPattern.ethereumAddress)
      .openapi({
        description: 'source address',
        example: '******************************************',
        pattern: regexPattern.ethereumAddress.source,
      })
      .optional(),
    txHash: z
      .string()
      .regex(regexPattern.blockChainHash)
      .openapi({
        description: 'transaction hash',
        example: '******************************************',
        pattern: regexPattern.blockChainHash.source,
      })
      .optional(),
    nonce: z
      .number()
      .openapi({
        description: 'nonce',
        example: 590,
      })
      .optional(),
  })
  .openapi('RetryTransactionResponse');

// Union schema for NFT metadata responses
export const NftMetadataUnionSchema = z.discriminatedUnion('type', [
  CouponNftSchema,
  ContentNftSchema,
  CertificateNftSchema,
]);

// =======================
// GetI18nNFTMetadataResponse
// =======================
export const GetI18nNFTMetadataResponseSchema = z
  .object({
    name: z.string().openapi({
      description: 'The translated name of the NFT',
      example: 'Cool NFT',
    }),
    description: z.string().openapi({
      description: 'The translated description of the NFT',
      example:
        '**Eligible Restaurant**\nYakitori Yuka\n\n**Offer Details**\nComplimentary 10,000 yen course for one person\n\n**Terms of Use**\n- Valid for one-time use by the recipient only.\n- No change will be given, please understand.\n- To use this offer, please call to reserve your visit date and inform them of the offer usage.\n- Reservations for your desired date and time are not guaranteed.\n- Cannot be combined with other services.\n- Drink charges will be incurred separately.\n- Can be used by two or more people, but the course fee for accompanying guests will be charged separately.\n\n**How to Use**\nAt checkout, show the screen to the staff and press the "Use Coupon" button.',
    }),
  })
  .openapi('GetI18nNFTMetadataResponse');

// =======================
// BulkMint
// =======================

export const MintTransactionInfoSchema = z
  .object({
    transactionId: z.string().regex(regexPattern.uuid).openapi({
      description: 'Id of uniquely paid out transaction',
      example: '8552df83-cbd5-44db-b67e-0cbeb2785918',
      pattern: regexPattern.uuid.source,
    }),
    txHash: z.string().regex(regexPattern.blockChainHash).openapi({
      description: 'transaction hash',
      example: '******************************************',
      pattern: regexPattern.blockChainHash.source,
    }),
    nonce: z.number().openapi({
      description: 'nonce',
      example: 590,
    }),
    queueIds: z.array(z.string().regex(regexPattern.uuid)).openapi({
      description: 'List of queue IDs associated with the mint transaction',
      example: ['8552df83-cbd5-44db-b67e-0cbeb2785918'],
    }),
  })
  .openapi('MintTransactionInfo');

export const BulkMintNftSchema = z
  .object({
    mintTransactions: z.array(MintTransactionInfoSchema),
  })
  .openapi('BulkMintNft');

// =======================
// TxFinality
// =======================

export const TxFinalityStatusSchema = z
  .object({
    status: z.string().openapi({
      description: 'The finality status of the transaction',
      example: 'success',
    }),
    message: z.string().openapi({
      description: 'A message providing additional information about the transaction status',
      example: 'Transaction completed successfully',
    }),
  })
  .openapi('TxFinalityStatus');

// =======================
// ModularContract
// =======================
export const RegsteredModularContractSchema = z
  .object({
    serviceId: z.string().regex(regexPattern.uuid).openapi({
      description: 'The uniquely given id of the service',
      example: '8552df83-cbd5-44db-b67e-0cbeb2785918',
      pattern: regexPattern.uuid.source,
    }),
    modularContractAddress: z.string().regex(regexPattern.ethereumAddress).openapi({
      description: 'The address of the modular contract',
      example: '******************************************',
      pattern: regexPattern.ethereumAddress.source,
    }),
  })
  .openapi('RegsteredModularContractSchema');

export const ContractItemSchema = z
  .object({
    nftContractId: z.string().regex(regexPattern.uuid).openapi({
      description: 'The uniquely given id of the nft contract',
      example: 'cee3e4aa-b0bf-482d-bb70-57a979c032c5',
      pattern: regexPattern.uuid.source,
    }),
    nftContractAddress: z.string().regex(regexPattern.ethereumAddress).openapi({
      description: 'The address of the NFT contract',
      example: '******************************************',
      pattern: regexPattern.ethereumAddress.source,
    }),
  })
  .openapi('ContractItemSchema');

export const GrantedNftContractSchema = z
  .object({
    roleGrantedList: z.array(ContractItemSchema).openapi({
      description: 'List of NFT contracts where the role was successfully granted',
      example: [
        {
          nftContractId: 'cee3e4aa-b0bf-482d-bb70-57a979c032c5',
          nftContractAddress: '******************************************',
        },
      ],
    }),
    roleGrantFailedList: z.array(ContractItemSchema).openapi({
      description: 'List of NFT contracts where the role grant failed',
      example: [
        {
          nftContractId: 'cee3e4aa-b0bf-482d-bb70-57a979c032c5',
          nftContractAddress: '******************************************',
        },
      ],
    }),
  })
  .openapi('GrantedNftContractSchema');

// Export types
export type GeneralMetadata = z.infer<typeof GeneralMetadataSchema>;
export type NftContractIds = z.infer<typeof NftContractIdsSchema>;
export type NftMetadataQuery = z.infer<typeof NftMetadataQuerySchema>;
export type NftRegister = z.infer<typeof NftRegisterSchema>;
export type NftDeploy = z.infer<typeof NftDeploySchema>;
export type NftFilter = z.infer<typeof NftFilterSchema>;
export type RequestAccount = z.infer<typeof RequestAccountSchema>;
export type NftList = z.infer<typeof NftListSchema>;
export type CouponNft = z.infer<typeof CouponNftSchema>;
export type CertificateNft = z.infer<typeof CertificateNftSchema>;
export type ContentNft = z.infer<typeof ContentNftSchema>;
export type CouponNftOverview = z.infer<typeof CouponNftOverviewSchema>;
export type CouponNftDetail = z.infer<typeof CouponNftDetailSchema>;
export type CertificateNftOverview = z.infer<typeof CertificateNftOverviewSchema>;
export type CertificateNftDetail = z.infer<typeof CertificateNftDetailSchema>;
export type NftMetadata = z.infer<typeof NftMetadataSchema>;
export type NftMintRequest = z.infer<typeof NftMintRequestSchema>;
export type NftRetryTransactionRequest = z.infer<typeof NftRetryTransactionRequestSchema>;
export type NftMintMembershipData = z.infer<typeof NftMintMembershipDataSchema>;
export type TransactionData = z.infer<typeof TransactionDataSchema>;
export type RetryTransactionResponse = z.infer<typeof RetryTransactionResponseSchema>;
export type NftMetadataUnion = z.infer<typeof NftMetadataUnionSchema>;
export type CouponNftMetadata = z.infer<typeof CouponNftMetadataSchema>;
export type CertificateNftMetadata = z.infer<typeof CertificateNftMetadataSchema>;
export type ContentNftMetadata = z.infer<typeof ContentNftMetadataSchema>;
export type GetI18nNFTMetadataResponse = z.infer<typeof GetI18nNFTMetadataResponseSchema>;
export type MintTransactionInfo = z.infer<typeof MintTransactionInfoSchema>;
export type BulkMintNft = z.infer<typeof BulkMintNftSchema>;
export type TxFinalityStatus = z.infer<typeof TxFinalityStatusSchema>;
export type RegsteredModularContract = z.infer<typeof RegsteredModularContractSchema>;
export type ContractItem = z.infer<typeof ContractItemSchema>;
export type GrantedNftContract = z.infer<typeof GrantedNftContractSchema>;
