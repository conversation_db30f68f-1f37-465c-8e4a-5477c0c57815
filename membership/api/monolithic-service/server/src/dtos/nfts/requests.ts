import {
  RequestAccountSchema,
  NftRegisterSchema,
  NftMintRequestSchema,
  NftMintMembershipDataSchema,
  verifySigRequestSchema,
  checkTokenGateAccessRequestSchema,
  registerTokenGateRequestSchema,
} from './schemas';

// GetMetadataRequest Body
export const getMetadataRequestBody = {
  description: 'Wallet and NFT whitelist to retrieve metadata',
  required: true,
  content: {
    'application/json': {
      schema: RequestAccountSchema,
    },
  },
};

// NftRegisterRequest Body
export const nftRegisterRequestBody = {
  description: 'NFT register request',
  required: true,
  content: {
    'application/json': {
      schema: NftRegisterSchema,
    },
  },
};

// NftMintRequest Body
export const nftMintRequestBody = {
  description: 'NFT',
  required: true,
  content: {
    'application/json': {
      schema: NftMintRequestSchema,
    },
  },
};

// NftMintMembershipRequest Body
export const nftMintMembershipRequestBody = {
  description: 'NFT',
  required: true,
  content: {
    'application/json': {
      schema: NftMintMembershipDataSchema,
    },
  },
};

// VerifySigRequest Body
export const verifySigRequestBody = {
  description: 'Verify signature and fetch NFTs',
  required: true,
  content: {
    'application/json': {
      schema: verifySigRequestSchema,
    },
  },
};

// CheckTokenGateAccessRequest Body
export const checkTokenGateAccessRequestBody = {
  description: 'Check token gate access',
  required: true,
  content: {
    'application/json': {
      schema: checkTokenGateAccessRequestSchema,
    },
  },
};

// RegisterTokenGateRequest Body
export const registerTokenGateRequestBody = {
  description: 'Register token gate',
  required: true,
  content: {
    'application/json': {
      schema: registerTokenGateRequestSchema,
    },
  },
};
