import { z } from '@hono/zod-openapi';
import { Point, Polygon } from 'geojson';
import { LocationCoordinates } from '../locations/schemas';

export function createPoint(
  location: LocationCoordinates
): Point {
  const point: Point = {
    type: "Point",
    coordinates: [location.longitude, location.latitude],
  };
  return point;
}

export function createPolygon(
  locations: LocationCoordinates[]
): Polygon[] {
  const polygon: Polygon = {
    type: "Polygon",
    coordinates: [[
      ...locations.map(location => [location.longitude, location.latitude]),
      [locations[0].longitude, locations[0].latitude],
    ]],
  };
  return polygon;
}


export function createLocationFromPoint(
  point: Point
): LocationCoordinates {
  const location: Location = {
    latitude: point.coordinates[0],
    longitude: point.coordinates[1],
  }
  return location;
}

export function createLocationFromPolygon(
  polygon: Polygon
): LocationCoordinates[] {
  const location = polygon.coordinates[0];

  return location.map(([lng, lat]) => ({
    latitude:  lat,
    longitude: lng,
  }));
}


      