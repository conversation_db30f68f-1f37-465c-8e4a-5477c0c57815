import { z } from '@hono/zod-openapi';
import { Point, Polygon, Position } from 'geojson';
import { LocationCoordinates } from '../locations/schemas';

export function createPoint(
  location: LocationCoordinates
): Point {
  const point: Point = {
    type: "Point",
    coordinates: [location.longitude, location.latitude],
  };
  return point;
}

export function createPolygon(
  locations: LocationCoordinates[]
): Polygon {
  const coords: Position[] = locations.map(loc => [loc.longitude, loc.latitude] as Position)
  return {
    type: 'Polygon',
    coordinates: [
      [...coords, coords[0]],
    ],
  }
}


export function createLocationFromPoint(
  point: Point
): LocationCoordinates {
  const location: LocationCoordinates = {
    latitude: point.coordinates[0],
    longitude: point.coordinates[1],
  }
  return location;
}

export function createLocationFromPolygon(
  polygon: Polygon
): LocationCoordinates[] {
  const location = polygon.coordinates[0];

  return location.map(([lng, lat]) => ({
    latitude:  lat,
    longitude: lng,
  }));
}


      