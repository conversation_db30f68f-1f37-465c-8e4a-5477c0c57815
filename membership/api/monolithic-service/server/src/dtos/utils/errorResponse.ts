import { z } from '@hono/zod-openapi';

export const errorResponseBaseSchema = z.object({
  status: z.number().int().openapi({
    description: 'HTTP status code of the error',
    example: 400,
  }),
  code: z.string().openapi({
    description: 'Application-specific error code',
    example: 'VALIDATION_FAILURE',
  }),
  message: z.string().openapi({
    description: 'A human-readable message providing more details about the error',
    example: 'Wrong request has been sent',
  }),
});

export const validationErrorSchema = errorResponseBaseSchema.openapi({
  example: {
    status: 400,
    code: 'VALIDATION_FAILURE',
    message: 'Wrong request has been sent',
  },
});

export const unauthorizedErrorSchema = errorResponseBaseSchema.openapi({
  example: {
    status: 401,
    code: 'UNAUTHORIZED',
    message: 'Incorrect authentication information',
  },
});

export const forbiddenErrorSchema = errorResponseBaseSchema.openapi({
  example: {
    status: 403,
    code: 'FORBIDDEN',
    message: 'You do not have access to the resource',
  },
});

export const notFoundErrorSchema = z.object({
  status: z.number().int().openapi({
    description: 'HTTP status code of the error',
    example: 404,
  }),
  code: z.string().openapi({
    description: 'Application-specific error code',
    example: 'NOT_FOUND',
  }),
  message: z.string().openapi({
    description: 'A human-readable message providing more details about the error',
    example: 'Resource does not exist.',
  }),
});

export const serverErrorSchema = errorResponseBaseSchema.openapi({
  example: {
    status: 500,
    code: 'INTERNAL_SERVER_ERROR',
    message: 'Internal Server Error',
  },
});

export const serviceUnavailableErrorSchema = errorResponseBaseSchema.openapi({
  example: {
    status: 503,
    code: 'SERVICE_UNAVAILABLE',
    message: 'Service Unavailable',
  },
});

export const gatewayTimeoutErrorSchema = errorResponseBaseSchema.openapi({
  example: {
    status: 504,
    code: 'GATEWAY_TIMEOUT',
    message: 'Gateway Timeout',
  },
});

export type ErrorResponse = z.infer<typeof errorResponseBaseSchema>;

export const commonErrorResponses = {
  400: {
    description: 'Validation error',
    content: {
      'application/json': {
        schema: validationErrorSchema,
      },
    },
  },
  401: {
    description: 'Unauthenticated',
    content: {
      'application/json': {
        schema: unauthorizedErrorSchema,
      },
    },
  },
  403: {
    description: 'Forbidden',
    content: {
      'application/json': {
        schema: forbiddenErrorSchema,
      },
    },
  },
  500: {
    description: 'Server error',
    content: {
      'application/json': {
        schema: serverErrorSchema,
      },
    },
  },
  503: {
    description: 'Service unavailable',
    content: {
      'application/json': {
        schema: serviceUnavailableErrorSchema,
      },
    },
  },
};

export const internalServerErrorResponse = {
  description: 'Server error',
  content: {
    'application/json': {
      schema: serverErrorSchema,
    },
  },
};
