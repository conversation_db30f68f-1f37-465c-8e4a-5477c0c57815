import { z } from '@hono/zod-openapi';
import { languageCodeSchema } from '../../enum/languageCode';
import { regexPattern } from '../../utils/regex';

export const serialCodeTranslationSchema = z.object({
  language: languageCodeSchema,
  name: z.string().min(1).openapi({
    description: 'ProjectName',
    example: 'ProjectName',
  }),
  description: z.string().min(1).openapi({
    description: 'ProjectDescription',
    example: 'ProjectDescription',
  }),
}).openapi('SerialCodeTranslations');
export type SerialCodeTranslation = z.infer<typeof serialCodeTranslationSchema>;

export const serialCodesSchema = z
  .object({
    code: z.string().min(4).max(36).openapi({
      description: 'Serial code',
      example: 'X11018Z',
    }),
    maxUseNum: z.number().min(1).openapi({
      description: 'Serial code use num',
      example: 100,
    }),
  })
  .openapi('SerialCode');
export type SerialCodes = z.infer<typeof serialCodesSchema>;

export const SerialCodeImportRequestSchema = z
  .object({
    serialCodeTranslations: z.array(serialCodeTranslationSchema),
    serialCodes: z.array(serialCodesSchema).min(4).max(36),
    slug: z.string().regex(regexPattern.slug).min(5).max(255).openapi({
      description:
        'Slug for the serial code project, used to identify the project in URLs. Must be unique.(allowed characters: a-z, 0-9, -, minimum length: 5, maximum length: 255)',
      example: 'my-serial-code-project',
      pattern: regexPattern.slug.source,
    }),
    rewardId: z.string().regex(regexPattern.uuid).openapi({
      description: 'Reward id can get after redeem Serial code',
      example: '8552df83-cbd5-44db-b67e-0cbeb2785918',
      pattern: regexPattern.uuid.source,
    }).optional(),
    hashKey: z
      .string()
      .min(1)
      .openapi({
        description: 'Used for hashing when saving serial codes. Specify an arbitrary string.',
        example: 'abc123DEF456...',
      })
      .optional(),
    startAt: z.string().regex(regexPattern.isoDateTime, 'Start date must be in yyyy-MM-ddThh:mm:ssZ format').openapi({
      description: 'The expiration date of the serial code.',
      example: '2024-01-01T10:00:00Z',
      pattern: regexPattern.isoDateTime.source,
    }),
    endAt: z.string().regex(regexPattern.isoDateTime, 'Start date must be in yyyy-MM-ddThh:mm:ssZ format').openapi({
      description: 'The expiration date of the serial code.',
      example: '2024-01-01T10:00:00Z',
      pattern: regexPattern.isoDateTime.source,
    }),
  })
  .openapi('SerialCodesImport');

export type SerialCodeImportRequest = z.infer<typeof SerialCodeImportRequestSchema>;

export const SerialCodesProcedureSchema = z
  .object({
    createCodeNum: z.number().min(1).openapi({
      description: 'Serial code create num',
      example: 100,
    }),
    maxUseNum: z.number().min(1).openapi({
      description: 'Serial code use num',
      example: 100,
    }),
  })
  .openapi('serialCodeProcedure');
export type SerialCodesProcedure = z.infer<typeof SerialCodesProcedureSchema>;

export const SerialCodeCreateRequestSchema = z
  .object({
    serialCodeTranslations: z.array(serialCodeTranslationSchema),
    codeProcedures: z.array(SerialCodesProcedureSchema).min(1),
    slug: z.string().regex(regexPattern.slug).min(5).max(255).openapi({
      description:
        'Slug for the serial code project, used to identify the project in URLs. Must be unique.(allowed characters: a-z, 0-9, -, minimum length: 5, maximum length: 255)',
      example: 'my-serial-code-project',
      pattern: regexPattern.slug.source,
    }),
    rewardId: z.string().regex(regexPattern.uuid).openapi({
      description: 'Reward id can get after redeem Serial code',
      example: '8552df83-cbd5-44db-b67e-0cbeb2785918',
      pattern: regexPattern.uuid.source,
    }).optional(),
    startAt: z.string().regex(regexPattern.isoDateTime, 'Start date must be in yyyy-MM-ddThh:mm:ssZ format').openapi({
      description: 'The expiration date of the serial code.',
      example: '2024-01-01T10:00:00Z',
      pattern: regexPattern.isoDateTime.source,
    }),
    endAt: z.string().regex(regexPattern.isoDateTime, 'Start date must be in yyyy-MM-ddThh:mm:ssZ format').openapi({
      description: 'The expiration date of the serial code.',
      example: '2024-01-01T10:00:00Z',
      pattern: regexPattern.isoDateTime.source,
    }),
  })
  .openapi('SerialCodeCreateReq');

export type SerialCodeCreateRequest = z.infer<typeof SerialCodeCreateRequestSchema>;

export const SerialCodeResponseSchema = z.object({
  serialCodeId: z.string().regex(regexPattern.uuid).openapi({
    description: 'Serial code ID',
    example: '8552df83-cbd5-44db-b67e-0cbeb2785918',
    pattern: regexPattern.uuid.source,
  }),
  code: z.string().min(4).max(36).openapi({
    description: 'Serial code',
    example: 'X11018Z',
  }),
  codeHash: z
    .string()
    .regex(/^0x[a-fA-F0-9]{64}$/)
    .openapi({
      description: 'Serial code to sha256 Hash',
      example: '0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef',
    }),
  maxUseNum: z.number().min(1).openapi({
    description: 'Serial code use num',
    example: 100,
  }),
}).openapi('GeneratedCode');
export type SerialCodeResponse = z.infer<typeof SerialCodeResponseSchema>;

export const SerialCodeGenerationResponseSchema = z.object({
  serialCodeProjectId: z.string().regex(regexPattern.uuid).openapi({
    description: 'Serial code ID',
    example: '8552df83-cbd5-44db-b67e-0cbeb2785918',
    pattern: regexPattern.uuid.source,
  }),
  slug: z.string().regex(regexPattern.slug).min(5).max(255).openapi({
    description:
      'Slug for the serial code project, used to identify the project in URLs. Must be unique.(allowed characters: a-z, 0-9, -, minimum length: 5, maximum length: 255)',
    example: 'my-serial-code-project',
    pattern: regexPattern.slug.source,
  }),
  serialCodeProjectTranslations: z.array(serialCodeTranslationSchema).openapi({
    description: 'List of translations for the serial code project',
  }),
  rewardId: z.string().regex(regexPattern.uuid).openapi({
    description: 'Reward id can get after redeem Serial code',
    example: '8552df83-cbd5-44db-b67e-0cbeb2785918',
    pattern: regexPattern.uuid.source,
  }),
  startAt: z.string().regex(regexPattern.isoDateTime, 'Start date must be in yyyy-MM-ddThh:mm:ssZ format').openapi({
    description: 'The expiration date of the serial code.',
    example: '2024-01-01T10:00:00Z',
    pattern: regexPattern.isoDateTime.source,
  }),
  endAt: z.string().regex(regexPattern.isoDateTime, 'Start date must be in yyyy-MM-ddThh:mm:ssZ format').openapi({
    description: 'The expiration date of the serial code.',
    example: '2024-01-01T10:00:00Z',
    pattern: regexPattern.isoDateTime.source,
  }),
  serialCodes: SerialCodeResponseSchema.array(),
}).openapi('SerialCodeGenerationProject');

export type SerialCodeGenerationResponse = z.infer<typeof SerialCodeGenerationResponseSchema>;

// =======================
// SerialCodeProjects
// =======================
export const SerialCodeProjectsSchema = z
  .object({
    projects: z.array(
      z.object({
        serialCodeProjectId: z.string().uuid(),
        slug: z.string().regex(regexPattern.slug).min(5).max(255),
        name: z.string().min(1),
        description: z.string().min(1),
        startAt: z.string().regex(regexPattern.isoDateTime, 'Start date must be in yyyy-MM-ddThh:mm:ssZ format'),
        endAt: z.string().regex(regexPattern.isoDateTime, 'Start date must be in yyyy-MM-ddThh:mm:ssZ format'),
      }),
    ),
  })
  .openapi('SerialCodeProject');

export type SerialCodeProjects = z.infer<typeof SerialCodeProjectsSchema>;
