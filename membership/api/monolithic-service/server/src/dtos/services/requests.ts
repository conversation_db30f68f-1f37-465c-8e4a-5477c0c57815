
import { CreateActionRequestSchema, createQuestRewardSchema, createRewardSchema, QuestCreateRequestSchema, registerServiceSchema, ServiceCustomFieldsSchema } from './schemas';


export const questCreateRequestBody = {
  description: 'Create quest',
  required: true,
  content: {
    'application/json': {
      schema: QuestCreateRequestSchema,
    },
  },
};

export const registerServiceRequestBody = {
  description: 'Register service',
  required: true,
  content: {
    'application/json': {
      schema: registerServiceSchema,
    },
  },
};

export const serviceCustomFieldsRequestBody = {
  description: 'Define or update the custom fields for service accounts',
  required: true,
  content: {
    'application/json': {
      schema: ServiceCustomFieldsSchema,
    },
  },
};

export const questActionCreateRequestBody = {
  description: 'Input information for quest action creation',
  content: {
    'application/json': {
      schema: CreateActionRequestSchema,
    },
  },
};

export const questRewardCreateRequestBody = {
  description: 'Input information for quest reward creation',
  content: {
    'application/json': {
      schema: createQuestRewardSchema,
    },
  },
};

export const createRewardRequestBody = {
  description: 'Input information for quest reward creation',
  content: {
    'application/json': {
      schema: createRewardSchema,
    },
  },
};
