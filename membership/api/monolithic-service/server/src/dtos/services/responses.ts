import {
  ServiceSchema,
  QuestCreateResponseSchema,
  QuestItemSchema,
  StatusQuestSchema,
  QuestDetailSchema,
  ActionDetailSchema,
  RewardDetailSchema,
  PaneJsonSchema,
  registerServiceResponseSchema,
  ServiceCustomFieldsUpdateResponseSchema,
  ServiceCustomFieldsResponseSchema,
  CreateActionResponseSchema,
  createQuestRewardResponseSchema,
  createRewardResponseSchema,
} from './schemas';

export const questCreateResponseBody = {
  description: 'Successfully created quest',
  content: {
    'application/json': {
      schema: QuestCreateResponseSchema,
    },
  },
};

export const questListResponseBody = {
  description: 'Successfully retrieved list of quests',
  content: {
    'application/json': {
      schema: QuestItemSchema.array(),
    },
  },
};

export const statusQuestResponseBody = {
  description: 'Successfully retrieved list of quests',
  content: {
    'application/json': {
      schema: StatusQuestSchema.array(),
    },
  },
};

export const questDetailResponseBody = {
  description: 'Successfully retrieved quest details',
  content: {
    'application/json': {
      schema: QuestDetailSchema,
    },
  },
};

export const actionDetailResponseBody = {
  description: 'Successfully retrieved action details',
  content: {
    'application/json': {
      schema: ActionDetailSchema,
    },
  },
};

export const rewardDetailResponseBody = {
  description: 'Successfully retrieved reward details',
  content: {
    'application/json': {
      schema: RewardDetailSchema,
    },
  },
};

export const paneResponseBody = {
  description: 'Successfully retrieved customizable widget json',
  content: {
    'application/json': {
      schema: PaneJsonSchema,
    },
  },
};

export const getServiceResponseBody = {
  description: 'Successfully retrieved service information',
  content: {
    'application/json': {
      schema: ServiceSchema,
    },
  },
};

export const registerServiceResponseBody = {
  description: 'Successfully registered service',
  content: {
    'application/json': {
      schema: registerServiceResponseSchema,
    },
  },
};

export const serviceCustomFieldsUpdateResponseBody = {
  description: 'Service custom fields update response',
  content: {
    'application/json': {
      schema: ServiceCustomFieldsUpdateResponseSchema,
    },
  },
};

export const getServiceCustomFieldsResponseBody = {
  description: 'Service custom fields definition',
  content: {
    'application/json': {
      schema: ServiceCustomFieldsResponseSchema,
    },
  },
};

export const createQuestActionResponseBody = {
  description: 'successful operation',
  content: {
    'application/json': {
      schema: CreateActionResponseSchema,
    },
  },
};

export const createQuestRewardResponseBody = {
  description: 'successful operation',
  content: {
    'application/json': {
      schema: createQuestRewardResponseSchema,
    },
  },
};
export const createRewardResponseBody = {
  description: 'successful operation',
  content: {
    'application/json': {
      schema: createRewardResponseSchema,
    },
  },
};
