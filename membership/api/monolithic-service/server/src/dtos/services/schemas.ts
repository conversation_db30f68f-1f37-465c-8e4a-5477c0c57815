import { z } from '@hono/zod-openapi';
import { regexPattern } from '../../utils/regex';
import { QuestTypeSchema } from '../../enum/questType';
import { QuestRewardPriorityType, QuestRewardPriorityTypeSchema } from '../../enum/questRewardPriorityType';
import { ActionType, ActionTypeSchema } from '../../enum/actionType';
import { RewardAcquirementType, RewardAcquirementTypeSchema } from '../../enum/rewardAcquirementType';
import { RewardType, RewardTypeSchema } from '../../enum/rewardType';
import { languageCodeSchema } from '../../enum/languageCode';
import { CertificateType } from '../../enum/certificateType';
import { isValidOpenSeaMetadata } from '../../utils/helper';
import { PointType, pointTypeSchema } from '../../enum/pointType';
import { CustomFieldTypeSchema } from '../../enum/customFieldType';

// =======================
// Service
// =======================
export const ServiceSchema = z
  .object({
    serviceId: z.string().regex(regexPattern.uuid).openapi({
      description: 'The uniquely given id of the service',
      example: '8552df83-cbd5-44db-b67e-0cbeb2785918',
      pattern: regexPattern.uuid.source,
    }),
    name: z
      .string()
      .regex(/^.{4,128}$/)
      .openapi({
        description: 'The name of the service',
        example: 'Test Service',
        pattern: '^.{4,128}$',
      }),
    policy: z.string().min(1, 'Service policy cannot be empty').openapi({
      description: 'Markdown text encoded in base64',
      format: 'byte',
      example: '## サービス利用規約',
    }),
    logoImageUrl: z.string().regex(regexPattern.imageUrl).openapi({
      description: 'URL of the logo image',
      example: 'https://marbullx.com/logo',
      pattern: regexPattern.imageUrl.source,
    }),
    marketCoverImageUrl: z
      .string()
      .regex(regexPattern.imageUrl)
      .openapi({
        description: 'URL of the cover image',
        example: 'https://marbullx.com/cover',
        pattern: regexPattern.imageUrl.source,
      })
      .optional(),
    themePrimaryColorLowest: z
      .string()
      .regex(regexPattern.color, { message: 'Invalid color format' })
      .openapi({
        description: 'ARGB hex color code',
        example: '0xFFFDD4D9',
        pattern: regexPattern.color.source,
      })
      .optional(),
    themePrimaryColorLower: z
      .string()
      .regex(regexPattern.color, { message: 'Invalid color format' })
      .openapi({
        description: 'ARGB hex color code',
        example: '0xFFFD8E9C',
        pattern: regexPattern.color.source,
      })
      .optional(),
    themePrimaryColorHigher: z
      .string()
      .regex(regexPattern.color, { message: 'Invalid color format' })
      .openapi({
        description: 'ARGB hex color code',
        example: '0xFFF94D62',
        pattern: regexPattern.color.source,
      })
      .optional(),
    themePrimaryColorHighest: z
      .string()
      .regex(regexPattern.color, { message: 'Invalid color format' })
      .openapi({
        description: 'ARGB hex color code',
        example: '0xFF6F000D',
        pattern: regexPattern.color.source,
      })
      .optional(),
    isMarketEnabled: z.boolean().openapi({
      description: 'Whether the service is enabled in the market',
      example: true,
    }),
  })
  .openapi('Service');

export type Service = z.infer<typeof ServiceSchema>;

// =======================
// QuestItem
// =======================
export const QuestItemSchema = z
  .object({
    questId: z.string().regex(regexPattern.uuid).openapi({
      description: 'Uniquely given identifier for each quest',
      example: '8552df83-cbd5-44db-b67e-0cbeb2785918',
      pattern: regexPattern.uuid.source,
    }),
    title: z
      .string()
      .regex(/^.{4,128}$/)
      .openapi({
        description: 'The name of the service',
        example: 'Quest title',
        pattern: '^.{4,128}$',
      }),
    questType: QuestTypeSchema,
    mainRewardTitle: z
      .string()
      .regex(/^.{4,128}$/)
      .openapi({
        description: 'The name of the main Reward',
        example: 'Reward name aaa',
        pattern: '^.{4,128}$',
      }),
    mainRewardThumbnailImageUrl: z.string().regex(regexPattern.imageUrl).openapi({
      description: 'URL of the Main reward thumbnail image',
      example: 'https://marbullx.com/image/reward',
      pattern: regexPattern.imageUrl.source,
    }),
    thumbnailImageUrl: z.string().regex(regexPattern.imageUrl).openapi({
      description: 'URL of the Quest thumbnail image',
      example: 'https://marbullx.com/image/quest',
      pattern: regexPattern.imageUrl.source,
    }),
    orderIndex: z.number({ coerce: true }).int().positive('Integer greater than or equal to 1').openapi({
      description: 'quest item order priority (default is desc and >= 0)',
      example: 0,
    }),
    startedAt: z.string().regex(regexPattern.isoDateTime, 'Start date must be in yyyy-MM-ddThh:mm:ssZ format').openapi({
      description: 'Start date and time when the quest is activated',
      example: '2024-01-01T10:00:00Z',
      pattern: regexPattern.isoDateTime.source,
    }),
    expiredAt: z.string().regex(regexPattern.isoDateTime, 'End date must be in yyyy-MM-ddThh:mm:ssZ format').openapi({
      description: 'End date and time when the quest is disabled',
      example: '2024-01-20T10:00:00Z',
      pattern: regexPattern.isoDateTime.source,
    }),
  })
  .openapi('QuestItem');

export type QuestItem = z.infer<typeof QuestItemSchema>;

// =======================
// AchievementAction
// =======================
export const AchievementActionSchema = z
  .object({
    actionId: z.string().regex(regexPattern.uuid).openapi({
      description: 'Uniquely given identifier for each action',
      example: '8552df83-cbd5-44db-b67e-0cbeb2785918',
      pattern: regexPattern.uuid.source,
    }),
    title: z.string().regex(regexPattern.uuid).openapi({
      description: 'Uniquely given identifier for each action',
      example: '8552df83-cbd5-44db-b67e-0cbeb2785918',
      pattern: regexPattern.uuid.source,
    }),
    rewardId: z.string().regex(regexPattern.uuid).openapi({
      description: 'Uniquely given identifier for each reward',
      example: '449B5E6E-B682-4B18-B66E-74C69425B75D',
      pattern: regexPattern.uuid.source,
    }),
    milestone: z.number({ coerce: true }).int().openapi({
      description: 'Uniquely given identifier for each action',
      example: 1,
    }),
    statusRank: z.number({ coerce: true }).int().openapi({
      description: 'Uniquely given identifier for each action',
      example: 1,
    }),
  })
  .openapi('AchievementAction');

export type AchievementAction = z.infer<typeof AchievementActionSchema>;

// =======================
// StatusQuest
// =======================
export const StatusQuestSchema = z
  .object({
    questId: QuestItemSchema.shape.questId,
    title: QuestItemSchema.shape.title,
    description: z.string().openapi({
      description: 'Markdown text encoded in base64',
      format: 'byte',
      example:
        'IyBIZWxsbyB3b3JsZCEKIyMgcG9saWN5IGRhdGEKVGhpcyBpcyB3aGVyZSB0aGUgdGV4dCBvZiB0aGUgcG9saWN5IGRhdGEgd2lsbCBiZSBsaXN0ZWQuCgotIGhpbnQgMQotIGhpbnQgMgoKW2ltYWdlXShodHRwczovL2ltYWdlLmNvbSkKCg==',
    }),
    startedAt: QuestItemSchema.shape.startedAt,
    expiredAt: QuestItemSchema.shape.expiredAt,
    actions: z.array(AchievementActionSchema).openapi({ description: 'List of action information linked to Quest' }),
  })
  .openapi('StatusQuest');

export type StatusQuest = z.infer<typeof StatusQuestSchema>;

export const questTranslationSchema = z.object({
  language: languageCodeSchema,
  title: z
    .string()
    .min(1, { message: 'Quest title is required' })
    .openapi({ description: 'Quest title', example: 'Quest 1' }),
  description: z.string().min(1, 'description cannot be empty').openapi({
    description: 'Quest description',
    example: 'Quest 1 description',
  }),
});

// =======================
// QuestCreateRequest
// =======================
export const QuestCreateRequestSchema = z
  .object({
    questTranslations: z.array(questTranslationSchema),
    thumbnailImageUrl: z
      .string()
      .regex(regexPattern.imageUrl)
      .url('Invalid thumbnail image URL')
      .max(512, 'URL too long')
      .openapi({
        description: 'Quest image URL',
        example: 'https://example.com/icons/xxx.png',
        pattern: regexPattern.imageUrl.source,
      }),
    coverImageUrl: z
      .string()
      .regex(regexPattern.imageUrl)
      .url('Invalid cover image URL')
      .max(512, 'URL too long')
      .openapi({
        description: 'Quest image URL',
        example: 'https://example.com/covers/xxx.png',
        pattern: regexPattern.imageUrl.source,
      }),
    orderIndex: z.number({ coerce: true }).int().positive('Integer greater than or equal to 1').openapi({
      description: 'quest item order priority (default is desc and >= 0)',
      example: 0,
    }),
    availableStartDate: z
      .string()
      .regex(regexPattern.isoDateTime, 'Start date must be in yyyy-MM-ddThh:mm:ssZ format')
      .openapi({
        description: 'Quest available start date',
        example: '2024-01-01',
        pattern: regexPattern.isoDateTime.source,
      }),
    availableEndDate: z
      .string()
      .regex(regexPattern.isoDateTime, 'End date must be in yyyy-MM-ddThh:mm:ssZ format')
      .openapi({
        description: 'Quest available end date',
        example: '2024-01-01',
        pattern: regexPattern.isoDateTime.source,
      }),
    questType: QuestTypeSchema,
  })
  .refine(
    (data) =>
      !data.availableStartDate ||
      !data.availableEndDate ||
      new Date(data.availableEndDate) >= new Date(data.availableStartDate),
    {
      message: 'End date must be greater than or equal to start date',
      path: ['availableEndDate', 'availableStartDate'],
    },
  )
  .openapi('QuestCreateRequest');

export type QuestCreateRequest = z.infer<typeof QuestCreateRequestSchema>;

// =======================
// QuestCreateResponse
// =======================
export const QuestCreateResponseSchema = z
  .object({
    questId: QuestItemSchema.shape.questId,
    questTranslations: z.array(questTranslationSchema),
    coverImageUrl: z
      .string()
      .regex(regexPattern.imageUrl)
      .url('Invalid image base URL')
      .max(1024, 'URL too long')
      .openapi({
        description: 'URL of the Quest cover image',
        example: 'https://marbullx.com/cover',
        pattern: regexPattern.imageUrl.source,
      }),
    thumbnailImageUrl: z
      .string()
      .regex(regexPattern.imageUrl)
      .url('Invalid image base URL')
      .max(1024, 'URL too long')
      .openapi({
        description: 'URL of the Quest thumbnail image',
        example: 'https://marbullx.com/thumbnail',
        pattern: regexPattern.imageUrl.source,
      }),
    orderIndex: QuestItemSchema.shape.orderIndex,
    availableStartDate: QuestItemSchema.shape.startedAt,
    availableEndDate: QuestItemSchema.shape.expiredAt,
    questType: QuestItemSchema.shape.questType,
  })
  .openapi('QuestCreateResponse');

export type QuestCreateResponse = z.infer<typeof QuestCreateResponseSchema>;

// =======================
// Reward
// =======================
export const RewardSchema = z
  .object({
    rewardId: z.string().regex(regexPattern.uuid).openapi({
      description: 'The uniquely given id of the reward',
      example: '8552df83-cbd5-44db-b67e-0cbeb2785918',
      pattern: regexPattern.uuid.source,
    }),
    title: z
      .string()
      .regex(/^.{4,128}$/)
      .openapi({
        description: 'The name of the reward',
        example: 'Test Reward',
        pattern: '^.{4,128}$',
      }),
    thumbnailImageUrl: z.string().regex(regexPattern.imageUrl).openapi({
      description: 'URL of the Reward thumbnail image',
      example: 'https://marbullx.com/logo',
      pattern: regexPattern.imageUrl.source,
    }),
    orderIndex: z.number({ coerce: true }).int().openapi({
      description: 'quest item order priority (default is desc and >= 0)',
      example: 0,
    }),
    rewardPriorityType: QuestRewardPriorityTypeSchema,
  })
  .openapi('Reward');

export type Reward = z.infer<typeof RewardSchema>;

// =======================
// Action
// =======================
export const ActionSchema = z
  .object({
    actionId: z.string().regex(regexPattern.uuid).openapi({
      description: 'Uniquely given identifier for each action',
      example: '28cc7f3c-b47d-486e-b035-************',
      pattern: regexPattern.uuid.source,
    }),
    title: z
      .string()
      .regex(/^.{4,128}$/)
      .openapi({
        description: 'The name of the action',
        example: 'Test Action',
        pattern: '^.{4,128}$',
      }),
    thumbnailImageUrl: z.string().regex(regexPattern.imageUrl).openapi({
      description: 'URL of the Action thumbnail image',
      example: 'https://marbullx.com/logo',
      pattern: regexPattern.imageUrl.source,
    }),
    orderIndex: z.number({ coerce: true }).int().openapi({
      description: 'quest item order priority (default is desc and >= 0)',
      example: 0,
    }),
    availableStartDate: z.string().regex(regexPattern.isoDateTimeWithOffset).openapi({
      description: 'Start date and time when the action is activated',
      example: '2024-01-01T10:00:00Z',
      pattern: regexPattern.isoDateTimeWithOffset.source,
    }),
    availableEndDate: z.string().regex(regexPattern.isoDateTimeWithOffset).openapi({
      description: 'End date and time when the action is disabled',
      example: '2024-01-20T10:00:00Z',
      pattern: regexPattern.isoDateTimeWithOffset.source,
    }),
    actionType: ActionTypeSchema,
  })
  .openapi('Action');

export type Action = z.infer<typeof ActionSchema>;

// =======================
// QuestDetail
// =======================
export const QuestDetailSchema = z
  .object({
    questId: z.string().regex(regexPattern.uuid).openapi({
      description: 'Uniquely given identifier for each quest',
      example: '8552df83-cbd5-44db-b67e-0cbeb2785918',
      pattern: regexPattern.uuid.source,
    }),
    title: z
      .string()
      .regex(/^.{4,128}$/)
      .openapi({
        description: 'The name of the quest',
        example: 'Quest title',
        pattern: '^.{4,128}$',
      }),
    description: z.string().openapi({
      description: 'Markdown text encoded in base64',
      format: 'byte',
      example:
        'IyBIZWxsbyB3b3JsZCEKIyMgcG9saWN5IGRhdGEKVGhpcyBpcyB3aGVyZSB0aGUgdGV4dCBvZiB0aGUgcG9saWN5IGRhdGEgd2lsbCBiZSBsaXN0ZWQuCgotIGhpbnQgMQotIGhpbnQgMgoKW2ltYWdlXShodHRwczovL2ltYWdlLmNvbSkKCg==',
    }),
    coverImageUrl: z.string().regex(regexPattern.imageUrl).openapi({
      description: 'URL of the Quest cover image',
      example: 'https://marbullx.com/logo',
      pattern: regexPattern.imageUrl.source,
    }),
    startedAt: z.string().regex(regexPattern.isoDateTimeWithOffset).openapi({
      description: 'Start date and time when the quest is activated',
      example: '2024-01-01T10:00:00Z',
      pattern: regexPattern.isoDateTimeWithOffset.source,
    }),
    expiredAt: z.string().regex(regexPattern.isoDateTimeWithOffset).openapi({
      description: 'End date and time when the quest is disabled',
      example: '2024-01-20T10:00:00Z',
      pattern: regexPattern.isoDateTimeWithOffset.source,
    }),
    questType: QuestTypeSchema,
    rewards: z.array(RewardSchema).openapi({
      description: 'List of reward information linked to Quest',
    }),
    actions: z.array(ActionSchema).openapi({
      description: 'List of action information linked to Quest',
    }),
  })
  .openapi('QuestDetail');

export type QuestDetail = z.infer<typeof QuestDetailSchema>;

// =======================
// RewardDetail
// =======================
export const RewardDetailSchema = z
  .object({
    rewardId: RewardSchema.shape.rewardId,
    title: RewardSchema.shape.title,
    coverImageUrl: z.string().regex(regexPattern.imageUrl).openapi({
      description: 'URL of the Reward cover image',
      example: 'https://marbullx.com/reward',
      pattern: regexPattern.imageUrl.source,
    }),
    description: z.string().openapi({
      description: 'Markdown text encoded in base64',
      format: 'byte',
      example:
        'IyBIZWxsbyB3b3JsZCEKIyMgcG9saWN5IGRhdGEKVGhpcyBpcyB3aGVyZSB0aGUgdGV4dCBvZiB0aGUgcG9saWN5IGRhdGEgd2lsbCBiZSBsaXN0ZWQuCgotIGhpbnQgMQotIGhpbnQgMgoKW2ltYWdlXShodHRwczovL2ltYWdlLmNvbSkKCg==',
    }),
    acquirementType: RewardAcquirementTypeSchema,
    rewardPriorityType: QuestRewardPriorityTypeSchema,
    nfts: z
      .array(
        z.object({
          nftContractId: z.string().uuid('Invalid UUID format for nftContractTypeId'),
          nftContractType: RewardTypeSchema,
        }),
      )
      .optional(),
    points: z
      .array(
        z.object({
          amount: z.number().int().min(1, 'amount must be greater than 0').openapi({
            description: 'Amount of the point',
            example: 100,
          }),
          pointType: z.nativeEnum(PointType).openapi({
            description: 'The type of the point REWARD or STATUS',
            example: PointType.REWARD,
          }),
        }),
      )
      .optional(),
  })
  .superRefine((data, ctx) => {
    const hasValidNfts = Array.isArray(data.nfts) && data.nfts.length > 0;
    const hasValidPoints = Array.isArray(data.points) && data.points.length > 0;
    if (!hasValidNfts && !hasValidPoints) {
      ctx.addIssue({
        path: [],
        code: z.ZodIssueCode.custom,
        message: 'At least one NFT or one point must be provided',
      });
    }
  })
  .openapi('RewardDetail');

export type RewardDetail = z.infer<typeof RewardDetailSchema>;

// =======================
// ActionAchievement
// =======================
export const ActionAchievementSchema = z
  .object({
    type: z.literal(ActionType.ACHIEVEMENT),
    priority: z.number({ coerce: true }).int().openapi({
      description: 'Priority of the achievement. The higher the number, the higher the priority.',
      example: 0,
    }),
    milestone: z.number({ coerce: true }).int().openapi({
      description: 'Number of quests completed to achieve an achievement',
      example: 9,
    }),
  })
  .openapi('ActionAchievement');

export type ActionAchievement = z.infer<typeof ActionAchievementSchema>;

// =======================
// ActionOnlineCheckin
// =======================
export const ActionOnlineCheckinSchema = z
  .object({
    type: z.literal(ActionType.ONLINE_CHECKIN),
    targetUrl: z.string().regex(regexPattern.imageUrl).openapi({
      description: 'URL of the checkin target page',
      example: 'https://marbullx.com/test/image',
      pattern: regexPattern.imageUrl.source,
    }),
  })
  .openapi('ActionOnlineCheckin');

export type ActionOnlineCheckin = z.infer<typeof ActionOnlineCheckinSchema>;

// =======================
// ActionQuestionnaire
// =======================
export const ActionQuestionnaireSchema = z
  .object({
    type: z.literal(ActionType.QUESTIONNAIRE),
    questionnaireId: z.string().regex(regexPattern.uuid).openapi({
      description: 'The uniquely given id of the questionnaire',
      example: '9b9767a1-255e-4dcf-9618-7679d6370404',
      pattern: regexPattern.uuid.source,
    }),
  })
  .openapi('ActionQuestionnaire');

export type ActionQuestionnaire = z.infer<typeof ActionQuestionnaireSchema>;

// =======================
// ActionSerialCode
// =======================
export const ActionSerialCodeSchema = z
  .object({
    type: z.literal(ActionType.SERIAL_CODE),
    serialCodeProjectId: z.string().regex(regexPattern.uuid).openapi({
      description: 'The uniquely given id of the serial code project',
      example: '9b9767a1-255e-4dcf-9618-7679d6370404',
      pattern: regexPattern.uuid.source,
    }),
  })
  .openapi('ActionSerialCode');

export type ActionSerialCode = z.infer<typeof ActionSerialCodeSchema>;

// =======================
// ActionDetailInfo (oneOf)
// =======================
export const ActionDetailInfoSchema = z
  .discriminatedUnion('type', [
    ActionAchievementSchema,
    ActionOnlineCheckinSchema,
    ActionQuestionnaireSchema,
    ActionSerialCodeSchema,
  ])
  .openapi({
    description: `Select and specify the propety that matches the actionType
  * ACHIEVEMENT: priority, title, milestone
  * ONLINE-CHECKIN: targetUrl
  * QUESTIONNAIRE: questionnaireId
  * SERIAL-CODE: serialCodeProjectId`,
  })
  .openapi('ActionDetailInfo');

export type ActionDetailInfo = z.infer<typeof ActionDetailInfoSchema>;

// =======================
// ActionDetail
// =======================
export const ActionDetailSchema = z
  .object({
    actionId: ActionSchema.shape.actionId,
    title: ActionSchema.shape.title,
    coverImageUrl: z.string().regex(regexPattern.imageUrl).openapi({
      description: 'URL of the Reward cover image',
      example: 'https://marbullx.com/action',
      pattern: regexPattern.imageUrl.source,
    }),
    description: StatusQuestSchema.shape.description,
    actionType: ActionSchema.shape.actionType,
    availableStartDate: ActionSchema.shape.availableStartDate,
    availableEndDate: ActionSchema.shape.availableEndDate,
    actionLabel: z
      .string()
      .regex(/^.{4,16}$/)
      .optional()
      .openapi({
        description: 'Name of the button to execute the action',
        example: 'Use Coupon',
        pattern: '^.{4,16}$',
      }),
    actionDetailInfo: ActionDetailInfoSchema.optional(),
  })
  .openapi('ActionDetail');

export type ActionDetail = z.infer<typeof ActionDetailSchema>;

// =======================
// PaneJson
// =======================
export const PaneJsonSchema = z
  .object({
    contents: z
      .string()
      .openapi({
        description: 'Markdown text encoded in base64. Encode Flutter widget in JSON',
        example:
          'ewogICAgInR5cGUiOiAiQ29udGFpbmVyIiwKICAgICJhbGlnbm1lbnQiOiAiY2VudGVyIiwKICAgICJjaGlsZCI6IHsKICAgICJ0eXBlIjogIlJhaXNlZEJ1dHRvbiIsCiAgICAiY29sb3IiOiAiIyNGRjAwRkYiLAogICAgInBhZGRpbmciOiAiOCw4LDgsOCIsCiAgICAidGV4dENvbG9yIjogIiMwMEZGMDAiLAogICAgImVsZXZhdGlvbiIgOiA4LjAsCiAgICAic3BsYXNoQ29sb3IiIDogIiMwMEZGMDAiLAogICAgImNsaWNrX2V2ZW50IiA6ICJyb3V0ZTovL3Byb2R1Y3REZXRhaWw/Z29vZHNfaWQ9MTIzIiwKICAgICJjaGlsZCIgOiB7CiAgICAgICAgInR5cGUiOiAiVGV4dCIsCiAgICAgICAgImRhdGEiOiAiSSBhbSBhIGJ1dHRvbiIKICAgIH0KICAgIH0KfQo=',
      })
      .optional(),
  })
  .openapi('PaneJson');

export type PaneJson = z.infer<typeof PaneJsonSchema>;

export const serviceTranslationSchema = z.object({
  language: languageCodeSchema,
  serviceName: z.string().min(1).max(512),
  servicePolicy: z.string().min(1, 'Service policy cannot be empty'),
  servicePane: z.string().optional(),
});

export const registerServiceSchema = z
  .object({
    serviceTranslations: z.array(serviceTranslationSchema),
    serviceUrl: z.string().url().max(512),
    serviceLogoImageUrl: z.string().url().max(128),
    marketCoverImageUrl: z.string().url().max(128).optional(),
    themePrimaryColorLowest: z.string().regex(regexPattern.color).optional(),
    themePrimaryColorLower: z.string().regex(regexPattern.color).optional(),
    themePrimaryColorHigher: z.string().regex(regexPattern.color).optional(),
    themePrimaryColorHighest: z.string().regex(regexPattern.color).optional(),
    isMarketEnabled: z.boolean(),
    lineChannelId: z.string().regex(regexPattern.lineChannelId),
    stripeAccountId: z.string().optional(),
    modularContractId: z.string(),
  })
  .openapi('CreateServiceReq');

export type RegisterServiceRequest = z.infer<typeof registerServiceSchema>;

export const registerServiceResponseSchema = z
  .object({
    serviceId: z.string().uuid('Invalid serviceId'),
    serviceTranslations: z.array(serviceTranslationSchema),
    logoImageUrl: z.string().url().max(128),
    marketCoverImageUrl: z.string().url().max(128).optional(),
    themePrimaryColorLowest: z.string().regex(regexPattern.color, { message: 'Invalid color format' }).optional(),
    themePrimaryColorLower: z.string().regex(regexPattern.color, { message: 'Invalid color format' }).optional(),
    themePrimaryColorHigher: z.string().regex(regexPattern.color, { message: 'Invalid color format' }).optional(),
    themePrimaryColorHighest: z.string().regex(regexPattern.color, { message: 'Invalid color format' }).optional(),
    isMarketEnabled: z.boolean(),
  })
  .openapi('CreateServiceRes');

export type RegisterServiceResponse = z.infer<typeof registerServiceResponseSchema>;

export const ServiceCustomFieldsUpdateResponseSchema = z.object({
  customFieldsDefinitionId: z.string().openapi({
    description: 'ID of the custom fields definition',
    example: '8552df83-cbd5-44db-b67e-0cbeb2785918'
  }),
  version: z.number().int().openapi({
    description: 'Version of the custom fields definition',
    example: 1
  })
}).openapi('ServiceCustomFieldsUpdateResponse');

export type ServiceCustomFieldsUpdateResponse = z.infer<typeof ServiceCustomFieldsUpdateResponseSchema>;

export const OptionTranslationSchema = z.object({
  locale: z.string().min(2).max(10).openapi({
    description: 'Locale code (e.g., en, ja, fr)',
    example: 'en'
  }),
  label: z.string().min(1).openapi({
    description: 'Translated label for the option',
    example: 'Male'
  })
}).openapi('OptionTranslation');



export const OptionSchema = z.object({
  value: z.string().min(1).openapi({
    description: 'Value to be stored when this option is selected',
    example: 'male'
  }),
  sortOrder: z.number().int().nonnegative().optional().openapi({
    description: 'Order in which to display this option (0-based)',
    example: 0
  }),
  translations: z.array(OptionTranslationSchema).min(1).openapi({
    description: 'Translated labels for different locales',
    example: [
      {
        locale: 'en',
        label: 'Male'
      },
      {
        locale: 'ja',
        label: '男性'
      }
    ]
  })
}).openapi('Option');

export const ServiceCustomFieldSchema = z.object({
  fieldKey: z.string().openapi({
    description: 'Unique key for the custom field',
    example: 'email'
  }),
  type: CustomFieldTypeSchema,
  optional: z.boolean().openapi({
    description: 'Whether this field is optional',
    example: false
  }),
  unique: z.boolean().openapi({
    description: 'Whether this field must have a unique value across all accounts',
    example: true
  }),
  verify: z.boolean().openapi({
    description: 'Whether this field requires verification',
    example: false
  }),
  sortOrder: z.number().int().nonnegative().openapi({
    description: 'Order in which to display the field (0-based)',
    example: 1
  }),
  defaultValue: z.string().optional().openapi({
    description: "Default value for this field (if any)",
    example: "+**********",
  }),
  maxLength: z.number().int().optional().openapi({
    description: "Maximum length (characters) for TEXT/EMAIL/PHONE types",
    example: 15,
  }),
  minLength: z.number().int().optional().openapi({
    description: "Minimum length (characters) for TEXT/EMAIL/PHONE types",
    example: 10,
  }),
  translations: z.array(z.object({
    locale: languageCodeSchema,
    label: z.string().openapi({
      description: 'Localized label for the field',
      example: 'Email Address'
    })
  })).openapi({
    description: 'Localized labels for the field'
  }),
  validator: z.object({
    pattern: z.string().optional().openapi({
      description: 'Regex pattern for validation',
      example: '^[\\w-\\.]+@([\\w-]+\\.)+[\\w-]{2,4}$'
    }),
    translations: z.array(z.object({
      locale: languageCodeSchema,
      errorMessage: z.string().openapi({
        description: 'Localized error message',
        example: 'Please enter a valid email address'
      }),
      description: z.string().optional().openapi({
        description: 'Localized description of the validator',
        example: 'Email address must be in the <NAME_EMAIL>'
      })
    })).optional().openapi({
      description: 'Localized error messages'
    })
  }).optional().openapi({
    description: 'Validation rules for the field'
  }),
  options: z.array(OptionSchema).optional().openapi({
    description: 'Options for DROPDOWN type fields',
  })
}).openapi('ServiceCustomField');

export type ServiceCustomField = z.infer<typeof ServiceCustomFieldSchema>;

export const ServiceCustomFieldsSchema = z.object({
  fields: z.array(ServiceCustomFieldSchema).openapi({
    description: 'Custom field definitions for the service',
  })
}).openapi('ServiceCustomFields');

export type ServiceCustomFields = z.infer<typeof ServiceCustomFieldsSchema>;

export const ServiceCustomFieldValidatorTranslationSchema = z.object({
  custom_field_validator_translation_id: z.string().uuid().openapi({
    description: 'ID of the validator translation',
    example: '83c4a960-cb5e-4ceb-aca2-f86dbdb2dccb'
  }),
  locale: z.string().openapi({
    description: 'Locale of the validator translation',
    example: 'ja'
  }),
  error_message: z.string().openapi({
    description: 'Error message for validation failure',
    example: '有効なメールアドレスを入力してください'
  }),
  description: z.string().nullable().openapi({
    description: 'Optional description of the validator',
    example: null
  })
}).openapi('ServiceCustomFieldValidatorTranslation');

export const ServiceCustomFieldValidatorSchema = z.object({
  validator_id: z.string().uuid().openapi({
    description: 'ID of the validator',
    example: '75d6eca8-eec0-465a-8bbd-26bcb8bd8246'
  }),
  service_id: z.string().openapi({
    description: 'Service ID',
    example: '5ac198b3-ade6-403a-9f26-1a0eafc65b09'
  }),
  custom_field_id: z.string().uuid().openapi({
    description: 'ID of the custom field',
    example: '612a61d7-39f8-4f73-a5be-732672e08e30'
  }),
  pattern: z.string().openapi({
    description: 'Regex pattern for validation',
    example: '^[\\w-\\.]+@([\\w-]+\\.)+[\\w-]{2,4}$'
  }),
  custom_field_validator_translation_id: z.string().uuid().openapi({
    description: 'ID of the validator translation',
    example: '83c4a960-cb5e-4ceb-aca2-f86dbdb2dccb'
  }),
  locale: z.string().openapi({
    description: 'Locale of the validator translation',
    example: 'ja'
  }),
  error_message: z.string().openapi({
    description: 'Error message for validation failure',
    example: '有効なメールアドレスを入力してください'
  }),
  description: z.string().nullable().openapi({
    description: 'Optional description of the validator',
    example: null
  })
}).openapi('ServiceCustomFieldValidator');

export const ServiceCustomFieldOptionSchema = z.object({
  label: z.string().openapi({
    description: 'Display label for the option',
    example: '男性'
  }),
  value: z.string().openapi({
    description: 'Value of the option',
    example: 'male'
  }),
  locale: z.string().openapi({
    description: 'Locale of the option label',
    example: 'ja'
  }),
  service_id: z.string().openapi({
    description: 'Service ID',
    example: '5ac198b3-ade6-403a-9f26-1a0eafc65b09'
  }),
  sort_order: z.number().openapi({
    description: 'Sort order of the option',
    example: 0
  }),
  custom_field_id: z.string().uuid().openapi({
    description: 'ID of the custom field',
    example: '75174d56-b377-4302-855b-46248118ad92'
  }),
  custom_field_option_id: z.string().uuid().openapi({
    description: 'ID of the custom field option',
    example: '22de31dd-43e5-49d7-b52e-6d1bc2389134'
  }),
  custom_field_option_translation_id: z.string().uuid().openapi({
    description: 'ID of the custom field option translation',
    example: 'bab492ee-ffcb-4445-985b-a264dbaa7152'
  })
}).openapi('ServiceCustomFieldOption');

// Define the schema for a single custom field in the response
export const ServiceCustomFieldResponseSchema = z.object({
  custom_field_id: z.string().uuid().openapi({
    description: 'Unique identifier for the custom field',
    example: '612a61d7-39f8-4f73-a5be-732672e08e30'
  }),
  service_id: z.string().openapi({
    description: 'Service ID',
    example: '5ac198b3-ade6-403a-9f26-1a0eafc65b09'
  }),
  field_key: z.string().openapi({
    description: 'Key of the custom field',
    example: 'email'
  }),
  version: z.number().openapi({
    description: 'Version of the custom field definition',
    example: 10
  }),
  type: CustomFieldTypeSchema,
  default_value: z.string().nullable().openapi({
    description: 'Default value of the custom field',
    example: null
  }),
  max_length: z.number().nullable().openapi({
    description: 'Maximum length of the custom field value',
    example: 15
  }),
  min_length: z.number().nullable().openapi({
    description: 'Minimum length of the custom field value',
    example: 10
  }),
  unique: z.boolean().openapi({
    description: 'Whether the custom field value must be unique',
    example: false
  }),
  verify: z.boolean().openapi({
    description: 'Whether the custom field value must be verified',
    example: false
  }),
  optional: z.boolean().openapi({
    description: 'Whether the custom field is optional',
    example: false
  }),
  sort_order: z.number().openapi({
    description: 'Sort order of the custom field',
    example: 1
  }),
  created_at: z.string().openapi({
    description: 'Creation timestamp of the custom field',
    example: '2025-06-10T11:05:04.828Z'
  }),
  label: z.string().openapi({
    description: 'Display label for the custom field',
    example: 'メールアドレス'
  }),
  locale: z.string().openapi({
    description: 'Locale of the custom field label',
    example: 'ja'
  }),
  validator: ServiceCustomFieldValidatorSchema.nullable().openapi({
    description: 'Validator for the custom field',
    example: {
      "validator_id": "75d6eca8-eec0-465a-8bbd-26bcb8bd8246",
      "service_id": "5ac198b3-ade6-403a-9f26-1a0eafc65b09",
      "custom_field_id": "612a61d7-39f8-4f73-a5be-732672e08e30",
      "pattern": "^[\\w-\\.]+@([\\w-]+\\.)+[\\w-]{2,4}$",
      "custom_field_validator_translation_id": "83c4a960-cb5e-4ceb-aca2-f86dbdb2dccb",
      "locale": "ja",
      "error_message": "有効なメールアドレスを入力してください",
      "description": null
    }
  }),
  options: z.array(ServiceCustomFieldOptionSchema).nullable().openapi({
    description: 'Options for dropdown fields',
    example: null
  })
}).openapi('ServiceCustomFieldResponse');

// Define the schema for the complete response
export const ServiceCustomFieldsResponseSchema = z.object({
  fields: z.array(ServiceCustomFieldResponseSchema).openapi({
    description: 'List of custom fields for the service',
    example: [
      {
        "custom_field_id": "612a61d7-39f8-4f73-a5be-732672e08e30",
        "service_id": "5ac198b3-ade6-403a-9f26-1a0eafc65b09",
        "field_key": "email",
        "version": 10,
        "type": "EMAIL",
        "default_value": null,
        "max_length": 15,
        "min_length": 10,
        "unique": false,
        "verify": false,
        "optional": false,
        "sort_order": 1,
        "created_at": "2025-06-10T11:05:04.828Z",
        "label": "メールアドレス",
        "locale": "ja",
        "validator": null,
        "options": null
      }
    ]
  })
}).openapi('ServiceCustomFieldsResponse');

export type ServiceCustomFieldsResponse = z.infer<typeof ServiceCustomFieldsResponseSchema>;

// ActionAchievement
// =======================
export const ActionSetAchievementSchema = z
  .object({
    type: z.literal(ActionType.ACHIEVEMENT),
    rewardId: z.string().regex(regexPattern.uuid).openapi({
      description: 'The uniquely given id of the reward',
      example: '8552df83-cbd5-44db-b67e-0cbeb2785918',
      pattern: regexPattern.uuid.source,
    }),
    statusRank: z.number({ coerce: true }).int().openapi({
      description: 'Status rank of the achievement. The higher the number, the higher the status.',
      example: 0,
    }),
    milestone: z.number({ coerce: true }).int().openapi({
      description: 'Number of quests completed to achieve an achievement',
      example: 9,
    }),
  })
  .openapi('ActionSetAchievement');

export type ActionSetAchievement = z.infer<typeof ActionSetAchievementSchema>;

// =======================
// ActionOnlineCheckin
// =======================
export const ActionSetOnlineCheckinSchema = z
  .object({
    type: z.literal(ActionType.ONLINE_CHECKIN),
    targetUrl: z.string().regex(regexPattern.imageUrl).openapi({
      description: 'URL of the checkin target page',
      example: 'https://marbullx.com/test/image',
      pattern: regexPattern.imageUrl.source,
    }),
  })
  .openapi('ActionSetOnlineCheckin');

// =======================
// ActionOnlineCheckin
// =======================
export const ActionSetQrCheckinSchema = z
  .object({
    type: z.literal(ActionType.QR_CHECKIN),
    qrVerificationData: z.string().regex(regexPattern.uuid).openapi({
      description: 'UUID of the QR code verification data',
      example: '9b9767a1-255e-4dcf-9618-7679d6370404',
      pattern: regexPattern.uuid.source,
    }),
  })
  .openapi('ActionSetQrCheckin');

export type ActionSetQrCheckin = z.infer<typeof ActionSetQrCheckinSchema>;

// =======================
// ActionQuestionnaire
// =======================
export const ActionSetQuestionnaireSchema = z
  .object({
    type: z.literal(ActionType.QUESTIONNAIRE),
    questionnaireId: z.string().regex(regexPattern.uuid).openapi({
      description: 'The uniquely given id of the questionnaire',
      example: '9b9767a1-255e-4dcf-9618-7679d6370404',
      pattern: regexPattern.uuid.source,
    }),
  })
  .openapi('ActionSetQuestionnaire');

export type ActionSetQuestionnaire = z.infer<typeof ActionSetQuestionnaireSchema>;

// =======================
// ActionSerialCode
// =======================
export const ActionSetSerialCodeSchema = z
  .object({
    type: z.literal(ActionType.SERIAL_CODE),
    serialCodeProjectId: z.string().regex(regexPattern.uuid).openapi({
      description: 'The uniquely given id of the serial code project',
      example: '9b9767a1-255e-4dcf-9618-7679d6370404',
      pattern: regexPattern.uuid.source,
    }),
  })
  .openapi('ActionSetSerialCode');

export type ActionSetSerialCode = z.infer<typeof ActionSetSerialCodeSchema>;


// =======================
// ActionSetLocationCheckinSchema
// =======================
export const ActionSetLocationCheckinSchema = z
  .object({
    type: z.literal(ActionType.LOCATION_CHECKIN),
    geofenceId: z.string().regex(regexPattern.uuid).openapi({
      description: 'The uniquely given id of the geofence',
      example: '9b9767a1-255e-4dcf-9618-7679d6370404',
      pattern: regexPattern.uuid.source,
    }),
  })
  .openapi('ActionSetLocationCheckin');

export type ActionSetLocationCheckin = z.infer<typeof ActionSetLocationCheckinSchema>;

// =======================
// ActionDetailInfo (oneOf)
// =======================
export const ActionSetDetailInfoSchema = z
  .discriminatedUnion('type', [
    ActionSetAchievementSchema,
    ActionSetOnlineCheckinSchema,
    ActionSetQrCheckinSchema,
    ActionSetQuestionnaireSchema,
    ActionSetSerialCodeSchema,
    ActionSetLocationCheckinSchema,
  ])
  .openapi({
    description: `Select and specify the propety that matches the actionType
  * ACHIEVEMENT: priority, title, milestone
  * ONLINE-CHECKIN: targetUrl
  * QR-CHECKIN: qrVerificationData
  * QUESTIONNAIRE: questionnaireId
  * SERIAL-CODE: serialCodeProjectId
  * LOCATION-CHECKIN: geofenceId`,
  })
  .openapi('ActionSetDetailInfo');

export type ActionSetDetailInfo = z.infer<typeof ActionSetDetailInfoSchema>;

// =======================
// Action Req Res
// =======================

export const ActionTranslationSchema = z.object({
  language: languageCodeSchema,
  title: z.string().min(1, 'Title is required').max(256, 'Title must not exceed 256 characters'),
  description: z.string().min(1, 'Description is required'),
  label: z.string().min(1, 'Label is required').max(128, 'Label must not exceed 128 characters').optional(),
});
export type ActionTranslation = z.infer<typeof ActionTranslationSchema>;

export const CreateActionRequestSchema = z
  .object({
    actionTranslations: z.array(ActionTranslationSchema),
    thumbnailImageUrl: z.string().url('Invalid thumbnail image URL').max(512, 'URL too long'),
    coverImageUrl: z.string().url('Invalid cover image URL').max(512, 'URL too long'),
    availableStartDate: z.string().regex(regexPattern.isoDateTime, 'Start date must be in yyyy-MM-ddThh:mm:ssZ format'),
    availableEndDate: z.string().regex(regexPattern.isoDateTime, 'End date must be in yyyy-MM-ddThh:mm:ssZ format'),
    orderIndex: z.number().int().positive('Integer greater than or equal to 1'),
    geofenceId: z.string().min(1, 'Geofence ID is required').nullable().optional(),
    actionDetailInfo: ActionSetDetailInfoSchema,
  })
  .refine((data) => new Date(data.availableEndDate) > new Date(data.availableStartDate), {
    message: 'Available end date must be after available start date',
    path: ['availableEndDate', 'availableStartDate'],
  })
  .openapi('CreateActionRequest');

export type CreateActionRequest = z.infer<typeof CreateActionRequestSchema>;

export const CreateActionResponseSchema = z
  .object({
    actionId: z.string().uuid('Invalid actionId'),
    serviceId: z.string().uuid('Invalid serviceId'),
    actionTranslations: z.array(ActionTranslationSchema),
    thumbnailImageUrl: z.string().url('Invalid thumbnail image URL').max(512, 'URL too long'),
    coverImageUrl: z.string().url('Invalid cover image URL').max(512, 'URL too long'),
    availableStartDate: z.string().regex(regexPattern.isoDateTime, 'Start date must be in yyyy-MM-ddThh:mm:ssZ format'),
    availableEndDate: z.string().regex(regexPattern.isoDateTime, 'End date must be in yyyy-MM-ddThh:mm:ssZ format'),
    orderIndex: z.number().int().positive('Integer greater than or equal to 1'),
    geofenceId: z.string().min(1, 'Geofence ID is required').nullable().optional(),
    actionDetailInfo: ActionSetDetailInfoSchema,
  })
  .refine((data) => new Date(data.availableEndDate) > new Date(data.availableStartDate), {
    message: 'Available end date must be after available start date',
    path: ['availableEndDate', 'availableStartDate'],
  })
  .openapi('CreateActionResponse');

export type CreateActionResponse = z.infer<typeof CreateActionResponseSchema>;

const validateNftMetadata = async (data: string) => {
  let json: object;
  try {
    // Check if metadata is Base64 encoded and decode it
    const decoded = Buffer.from(data, 'base64').toString('utf8');
    // Parse decoded string as JSON
    json = JSON.parse(decoded) as object;
  } catch {
    return {
      success: false,
      message: 'invalid base64 json format',
    };
  }
  try {
    // Step 3: Validate if it follows OpenSea standard
    if (!isValidOpenSeaMetadata(json)) {
      throw new Error('invalid medatadata');
    }
  } catch {
    return {
      success: false,
      message: 'invalid medatadata',
    };
  }

  return {
    success: true,
    message: '',
  };
};

export const rewardTranslationSchema = z.array(
  z.object({
    language: languageCodeSchema,
    title: z
      .string()
      .min(1, { message: 'Title cannot be empty' })
      .max(128, { message: 'Title cannot be longer than 128 characters' })
      .openapi({ description: 'Title of the reward' }),
    description: z
      .string()
      .min(1, { message: 'Description cannot be empty' })
      .openapi({ description: 'Description of the reward' }),
  }),
);

// --------------------
// Create Reward
// --------------------
export const createRewardSchema = z
  .object({
    thumbnailImageUrl: z.string().url('Invalid thumbnail image URL').max(512, 'URL too long').openapi({
      description: 'Thumbnail image URL of the reward',
      example: 'https://marbullx.com/test/image',
    }),
    coverImageUrl: z.string().url('Invalid cover image URL').max(512, 'URL too long').openapi({
      description: 'Cover image URL of the reward',
      example: 'https://marbullx.com/test/image',
    }),
    rewardTranslations: rewardTranslationSchema,
    acquirementType: z.nativeEnum(RewardAcquirementType).openapi({
      description: 'Acquirement type of the reward',
      example: RewardAcquirementType.DISTRIBUTION,
    }),
    orderIndex: z.number().int().positive('Integer greater than or equal to 1').openapi({
      description: 'Order index of the reward',
      example: 1,
    }),
    nfts: z
      .array(
        z.object({
          nftName: z.string().min(1, 'NFT name cannot be empty').openapi({
            description: 'NFT name of the reward',
            example: 'NFT name',
          }),
          nftSymbol: z.string().min(1, 'NFT symbol cannot be empty').openapi({
            description: 'NFT symbol of the reward',
            example: 'SYMBOL',
          }),
          nftCollectionName: z.string().min(1, 'NFT collection name cannot be empty').openapi({
            description: 'NFT collection name of the reward',
            example: 'COLLECTION',
          }),
          nftMetadata: z.string().superRefine(async (value, ctx) => {
            const result = await validateNftMetadata(value);
            if (!result.success) {
              ctx.addIssue({
                code: z.ZodIssueCode.custom,
                message: result.message,
              });
            }
          }),
          nftContractType: z.nativeEnum(RewardType).openapi({
            description: 'NFT contract type of the reward',
            example: RewardType.CERTIFICATE,
          }),
          nftContractTypeId: z.string().uuid('Invalid UUID format for nftContractTypeId').openapi({
            description: 'NFT contract type id of the reward',
            example: '123e4567-e89b-12d3-a456-************',
          }),
          deliveryImageUrl: z.string().url('Invalid delivery image URL').max(512, 'URL too long').openapi({
            description: 'Delivery image URL of the reward',
            example: 'https://marbullx.com/test/image',
          }),
          tokenId: z.string().optional().openapi({
            description: 'Token id of the reward only for Coupon reward',
            example: '123e4567-e89b-12d3-a456-************',
          }),
          certificateReward: z
            .object({
              certificateType: z.nativeEnum(CertificateType).openapi({
                description:
                  'Certificate type of the reward STATUS for status certificate and STANDARD for standard certificate',
                example: CertificateType.STATUS,
              }),
              statusCertificateRank: z.number().int().nonnegative().nullable().optional().openapi({
                description: 'Status certificate rank of the reward',
                example: 1,
              }),
            })
            .optional()
            .openapi({
              description: 'Only for Certificate reward',
            }),
        }),
      )
      .optional(),
    points: z
      .array(
        z.object({
          amount: z.number().int().openapi({
            description: 'Amount of the point',
            example: 100,
          }),
          pointType: z.nativeEnum(PointType).openapi({
            description: 'Point type of the reward, REWARD for mile and STATUS for diamond',
            example: PointType.REWARD,
          }),
          expireDate: z
            .string()
            .regex(regexPattern.isoDateTime, 'Expire date must be in yyyy-MM-ddThh:mm:ssZ format')
            .optional()
            .openapi({
              description: 'Expire date of the reward only for Reward type',
              example: '2024-01-01T10:00:00Z',
            }),
        }),
      )
      .optional(),
  })
  .superRefine((data, ctx) => {
    const hasValidNfts = Array.isArray(data.nfts) && data.nfts.length > 0;
    const hasValidPoints = Array.isArray(data.points) && data.points.length > 0;
    if (!hasValidNfts && !hasValidPoints) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'At least one NFT or one point must be provided',
      });
    }
  })
  .openapi('RewardCreationRequest');

export type CreateRewardRequest = z.infer<typeof createRewardSchema>;

export const createRewardResponseSchema = z
  .object({
    rewardId: z.string().uuid('Invalid UUID format for rewardId'),
    serviceId: z.string().uuid('Invalid UUID format for serviceId'),
    coverImageUrl: z
      .string()
      .url('Invalid URL format for coverImageUrl')
      .max(1024, 'Cover image URL cannot be longer than 1024 characters'),
    thumbnailImageUrl: z
      .string()
      .url('Invalid URL format for thumbnailImageUrl')
      .max(1024, 'Thumbnail image URL cannot be longer than 1024 characters'),
    rewardTranslations: rewardTranslationSchema,
    acquirementType: z.nativeEnum(RewardAcquirementType),
    nfts: z.array(
      z.object({
        nftContractId: z.string().uuid('Invalid UUID format for nftContractTypeId'),
        nftContractType: z.nativeEnum(RewardType),
      }),
    ),
    points: z.array(
      z.object({
        amount: z.number().int().openapi({
          description: 'Amount of the point',
          example: 100,
        }),
        pointType: z.nativeEnum(PointType).openapi({
          description: 'Point type of the reward, REWARD for mile and STATUS for diamond',
          example: PointType.REWARD,
        }),
      }),
    ),
  })
  .openapi('RewardCreationResponse');
export type CreateRewardResponse = z.infer<typeof createRewardResponseSchema>;

// --------------------
// Create Quest Reward
// --------------------
export const createQuestRewardResponseSchema = createRewardResponseSchema
  .extend({
    questRewardPriorityType: z.nativeEnum(QuestRewardPriorityType),
  })
  .openapi('QuestRewardCreationResponse');
export type CreateQuestRewardResponse = z.infer<typeof createQuestRewardResponseSchema>;

export const createQuestRewardSchema = createRewardSchema
  .and(
    z.object({
      questRewardPriorityType: z.nativeEnum(QuestRewardPriorityType),
      selector: z
        .object({
          rankId: z.string().uuid('Invalid rankId format').nullable().optional(),
          gachaWeight: z.number().positive().nullable().optional(),
        })
        .optional(),
    }),
  )
  .openapi('QuestRewardCreationRequest');
export type CreateQuestRewardRequest = z.infer<typeof createQuestRewardSchema>;

// export const createRewardResponseSchema = z.object({
//   rewardId: z.string().uuid('Invalid UUID format for rewardId'),
//   serviceId: z.string().uuid('Invalid UUID format for serviceId'),
//   nftContractId: z.string().uuid('Invalid UUID format for nftContractTypeId'),
//   coverImageUrl: z
//     .string()
//     .url('Invalid URL format for coverImageUrl')
//     .max(1024, 'Cover image URL cannot be longer than 1024 characters'),
//   thumbnailImageUrl: z
//     .string()
//     .url('Invalid URL format for thumbnailImageUrl')
//     .max(1024, 'Thumbnail image URL cannot be longer than 1024 characters'),
//   rewardTranslations: rewardTranslationSchema,
//   acquirementType: z.nativeEnum(RewardAcquirementType),
//   rewardType: z.nativeEnum(RewardType),
// }).openapi('RewardCreationResponse');
// export type CreateRewardResponse = z.infer<typeof createRewardResponseSchema>;
