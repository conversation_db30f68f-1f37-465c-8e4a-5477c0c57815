import { z } from '@hono/zod-openapi';
import { regexPattern } from '../../utils/regex';
import { PointOps, pointOpsTypeSchema } from '../../enum/pointOpsType';
import { pointTypeSchema } from '../../enum/pointType';

// =======================
// UpdateMetadataRequest
// =======================
export const UpdateMetadataRequestSchema = z
  .object({
    contractAddress: z.string().regex(regexPattern.ethereumAddress).openapi({
      description: 'registered NFT Contract Address',
      example: '******************************************',
      pattern: regexPattern.ethereumAddress.source,
    }),
    tokenId: z
      .array(z.string())
      .openapi({
        description: 'NFT tokenId',
        example: ['32'],
      })
      .optional(),
    chainId: z.number().int().openapi({
      description: 'Chain ID',
      example: 137,
    }),
  })
  .openapi('UpdateMetadataRequest');

export type UpdateMetadataRequest = z.infer<typeof UpdateMetadataRequestSchema>;

// =======================
// UpdateMetadataResponse
// =======================
export const UpdateMetadataResponseSchema = z
  .object({
    updatedTokenUris: z
      .array(
        z.object({
          tokenId: z.string(),
          tokenUri: z.string().url(),
        }),
      )
      .openapi({
        description: 'List of tokenIds that were updated successfully',
      }),
    failedIds: z
      .array(
        z.object({
          tokenId: z.string(),
          errorMsg: z.string().optional(),
        }),
      )
      .optional()
      .openapi({
        description: 'List of tokenIds that failed to update metadata',
      }),
  })
  .openapi('UpdateMetadataResponse');

export type UpdateMetadataResponse = z.infer<typeof UpdateMetadataResponseSchema>;

export const adjustPointRequestSchema = z
  .object({
    accountId: z.string().uuid().openapi({
      description: 'Account ID',
      example: '99DAFCE9-C629-4E39-9F75-37C93D8BC3AF',
    }),
    pointType: pointTypeSchema,
    amount: z
      .number()
      .int()
      .refine((val) => val !== 0, {
        message: 'amount should not be 0',
      })
      .openapi({
        description: 'Amount',
        example: 100,
      }),
    pointOpsType: pointOpsTypeSchema,
    expiresOn: z
      .string()
      .regex(regexPattern.isoDateTime)
      .optional()
      .refine(
        (val) => {
          return val === undefined || new Date(val) > new Date();
        },
        {
          message: 'expiresOn should be future time',
        },
      ).openapi({
        description: 'Expires On',
        example: '2025-01-01T00:00:00.000Z',
      }),
  })
  .superRefine((data, ctx) => {
    if (data.pointOpsType === PointOps.ADD && data.amount < 0) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ['amount'],
        message: 'amount should be greater than 0 in case of "ADD"',
      });
    }
    if (data.pointOpsType === PointOps.SUB && data.amount > 0) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ['amount'],
        message: 'amount should be less than 0 in case of "SUB"',
      });
    }
  });

export type AdjustPointRequest = z.infer<typeof adjustPointRequestSchema>;
