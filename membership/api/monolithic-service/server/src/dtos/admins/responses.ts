import { z } from '@hono/zod-openapi';
import { rewardPointTxSchema, statusPointTxSchema } from '../points/schemas';
import { UpdateMetadataResponseSchema } from './schemas';

export const UpdateMetadataResponse = {
  description: 'successful operation',
  content: {
    'application/json': {
      schema: UpdateMetadataResponseSchema,
    },
  },
};

export const adjustPointResponse = {
  description: 'successful operation',
  content: {
    'application/json': {
      schema: z.union([rewardPointTxSchema, statusPointTxSchema]),
    },
  },
};
