import { z } from 'zod';
import { openApiRegistry } from '../../utils/openApiRegistry';

// =======================
// Location Check-in Action Data
// =======================
export const LocationActionDataSchema = z.object({
  geofenceId: z.string().openapi({
    description: 'Geofence ID for the location action',
    example: 'geofence-123'
  }),
  requiredDurationSeconds: z.number()
    .min(0)
    .default(0)
    .openapi({
      description: 'Minimum time to stay in geofence (in seconds)',
      example: 30
    })
}).openapi('LocationActionData');

export type LocationActionData = z.infer<typeof LocationActionDataSchema>;

// =======================
// Create Location Check-in Action Request
// =======================
export const CreateLocationActionRequestSchema = z.object({
  actionTitle: z.string().min(1).max(256).openapi({
    description: 'Title of the location check-in action',
    example: 'Check in at Tokyo Station'
  }),
  actionDescription: z.string().optional().openapi({
    description: 'Description of the action',
    example: 'Visit Tokyo Station and check in to complete this quest'
  }),
  actionCoverImageUrl: z.string().url().optional().openapi({
    description: 'URL of the cover image for the action',
    example: 'https://example.com/images/tokyo-station-cover.jpg'
  }),
  actionThumbnailImageUrl: z.string().url().optional().openapi({
    description: 'URL of the thumbnail image for the action',
    example: 'https://example.com/images/tokyo-station-thumb.jpg'
  }),
  actionLabel: z.string().max(128).optional().openapi({
    description: 'Label or category for the action',
    example: 'Station Visit'
  }),
  actionAvailableStartDate: z.string().datetime().optional().openapi({
    description: 'Start date when action becomes available',
    example: '2023-01-01T00:00:00Z'
  }),
  actionAvailableEndDate: z.string().datetime().optional().openapi({
    description: 'End date when action expires',
    example: '2023-12-31T23:59:59Z'
  }),
  locationData: LocationActionDataSchema.openapi({
    description: 'Location-specific configuration for the action'
  })
}).openapi('CreateLocationActionRequest');

export type CreateLocationActionRequest = z.infer<typeof CreateLocationActionRequestSchema>;

// =======================
// Update Location Check-in Action Request
// =======================
export const UpdateLocationActionRequestSchema = z.object({
  actionTitle: z.string().min(1).max(256).optional().openapi({
    description: 'Updated title of the action'
  }),
  actionDescription: z.string().optional().openapi({
    description: 'Updated description of the action'
  }),
  actionCoverImageUrl: z.string().url().optional().openapi({
    description: 'Updated cover image URL'
  }),
  actionThumbnailImageUrl: z.string().url().optional().openapi({
    description: 'Updated thumbnail image URL'
  }),
  actionLabel: z.string().max(128).optional().openapi({
    description: 'Updated label or category'
  }),
  actionAvailableStartDate: z.string().datetime().optional().openapi({
    description: 'Updated start date'
  }),
  actionAvailableEndDate: z.string().datetime().optional().openapi({
    description: 'Updated end date'
  }),
  locationData: LocationActionDataSchema.partial().optional().openapi({
    description: 'Updated location-specific configuration'
  })
}).openapi('UpdateLocationActionRequest');

export type UpdateLocationActionRequest = z.infer<typeof UpdateLocationActionRequestSchema>;

// =======================
// Location Check-in Action Response
// =======================
export const LocationActionResponseSchema = z.object({
  actionId: z.string().openapi({
    description: 'Unique identifier for the action',
    example: 'action-789'
  }),
  serviceId: z.string().openapi({
    description: 'Service identifier',
    example: 'service-123'
  }),
  actionTitle: z.string().openapi({
    description: 'Title of the action',
    example: 'Check in at Tokyo Station'
  }),
  actionDescription: z.string().optional().openapi({
    description: 'Description of the action'
  }),
  actionCoverImageUrl: z.string().optional().openapi({
    description: 'Cover image URL'
  }),
  actionThumbnailImageUrl: z.string().optional().openapi({
    description: 'Thumbnail image URL'
  }),
  actionLabel: z.string().optional().openapi({
    description: 'Action label or category'
  }),
  actionAvailableStartDate: z.string().datetime().optional().openapi({
    description: 'Start date when action becomes available'
  }),
  actionAvailableEndDate: z.string().datetime().optional().openapi({
    description: 'End date when action expires'
  }),
  actionType: z.literal('LOCATION_CHECKIN').openapi({
    description: 'Type of action',
    example: 'LOCATION_CHECKIN'
  }),
  geofenceId: z.string().optional().openapi({
    description: 'Associated geofence ID (legacy field)',
    example: 'geofence-123'
  }),
  locationData: LocationActionDataSchema.openapi({
    description: 'Location-specific configuration'
  }),
  createdAt: z.string().datetime().openapi({
    description: 'Creation timestamp',
    example: '2023-01-01T00:00:00Z'
  }),
  updatedAt: z.string().datetime().openapi({
    description: 'Last update timestamp',
    example: '2023-01-01T00:00:00Z'
  })
}).openapi('LocationActionResponse');

export type LocationActionResponse = z.infer<typeof LocationActionResponseSchema>;

// =======================
// Location Check-in Status
// =======================
export const LocationCheckinStatusSchema = z.object({
  actionId: z.string().openapi({
    description: 'Action ID',
    example: 'action-789'
  }),
  accountId: z.string().openapi({
    description: 'Account ID',
    example: 'account-456'
  }),
  isCompleted: z.boolean().openapi({
    description: 'Whether the action has been completed',
    example: false
  }),
  completedAt: z.string().datetime().optional().openapi({
    description: 'Completion timestamp if completed',
    example: '2023-01-01T12:00:00Z'
  }),
  lastAttemptAt: z.string().datetime().optional().openapi({
    description: 'Timestamp of last check-in attempt',
    example: '2023-01-01T11:55:00Z'
  }),
  totalAttempts: z.number().openapi({
    description: 'Total number of check-in attempts',
    example: 3
  }),
  successfulAttempts: z.number().openapi({
    description: 'Number of successful attempts (should be 0 or 1)',
    example: 0
  }),
  lastDistance: z.number().optional().openapi({
    description: 'Distance from target in last attempt (meters)',
    example: 150.5
  })
}).openapi('LocationCheckinStatus');

export type LocationCheckinStatus = z.infer<typeof LocationCheckinStatusSchema>;

// Register schemas with OpenAPI
openApiRegistry.register('LocationActionData', LocationActionDataSchema);
openApiRegistry.register('CreateLocationActionRequest', CreateLocationActionRequestSchema);
openApiRegistry.register('UpdateLocationActionRequest', UpdateLocationActionRequestSchema);
openApiRegistry.register('LocationActionResponse', LocationActionResponseSchema);
openApiRegistry.register('LocationCheckinStatus', LocationCheckinStatusSchema);
