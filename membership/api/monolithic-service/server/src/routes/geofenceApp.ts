import { OpenAPIHono } from '@hono/zod-openapi';
import { container } from 'tsyringe';
import { GeofenceController } from '../controllers/geofenceController';
import { GeofenceService } from '../services/geofenceService';
import { GeofenceRepository } from '../repositories/geofenceRepository';
import {
  postGeofenceRoute,
  putGeofenceRoute,
  getGeofencesRoute,
  getGeofenceRoute,
  deleteGeofenceRoute,
} from './geofenceRoutes';

// Initialize the geofence app
const geofenceApp = new OpenAPIHono();

// Register dependencies
container.register('GeofenceRepository', { useClass: GeofenceRepository });
container.register('GeofenceService', { useClass: GeofenceService });
container.register('GeofenceController', { useClass: GeofenceController });

// Get controller instance
const geofenceController = container.resolve(GeofenceController);

// =======================
// Geofence Routes
// =======================

// POST /services/geofences - Create geofence
geofenceApp.openapi(postGeofenceRoute, async (c) => {
  return await geofenceController.createGeofence(c);
});

// PUT /services/geofences/{geofenceId} - Update geofence
geofenceApp.openapi(putGeofenceRoute, async (c) => {
  return await geofenceController.updateGeofence(c);
});

// GET /services/geofences - Get all geofences
geofenceApp.openapi(getGeofencesRoute, async (c) => {
  return await geofenceController.getGeofences(c);
});

// GET /services/geofences/{geofenceId} - Get specific geofence
geofenceApp.openapi(getGeofenceRoute, async (c) => {
  return await geofenceController.getGeofence(c);
});

// DELETE /services/geofences/{geofenceId} - Delete geofence
geofenceApp.openapi(deleteGeofenceRoute, async (c) => {
  await geofenceController.deleteGeofence(c);
  return c.body(null, 204);
});

export { geofenceApp };
