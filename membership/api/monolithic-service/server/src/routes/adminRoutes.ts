import { OpenAPIHono } from '@hono/zod-openapi';
import * as adminHandler from '../endpoints/adminsEndpoint';
import { container } from '../configs/container';
import { AdminController } from '../controllers/adminController';

const adminRoutes = new OpenAPIHono();
const adminController = container.resolve(AdminController);

adminRoutes.openapi(adminHandler.updateMetadataRoute, (c) => adminController.updateNftMetadata(c));
adminRoutes.openapi(adminHandler.adjustPointRoute, (c) => adminController.adjustPoint(c));

export { adminRoutes };
