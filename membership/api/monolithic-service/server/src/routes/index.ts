import type { OpenAPIHono } from '@hono/zod-openapi';
import { userRoutes } from './userRoutes';
import { authRoutes } from './authRoutes';
import { serviceRoutes } from './serviceRoutes';
import { accountsRoutes } from './accountRoutes';
import { nftsRoutes } from './nftsRoutes';
import { webhookRoutes } from './webhookRoutes';
import { vaultRoutes } from './vaultRoutes';
import { productsRoutes } from './productRoute';
import { adminRoutes } from './adminRoutes';
import { tenantsRoutes } from './tenantRoutes';
import { imageRoutes } from './imageRoutes';
import { notificationRoutes } from './notificationRoutes';
import { pointRoutes } from './pointRoutes';
import { locationRoutes } from './locationRoutes';
import { oldNftMetadataRoute } from './oldNftMetadataRoute';

export function registerRoutes(app: OpenAPIHono) {
  app.route('/users', userRoutes);
  app.route('/auth', authRoutes);
  app.route('/services', serviceRoutes);
  app.route('/accounts', accountsRoutes);
  app.route('/webhook', webhookRoutes);
  app.route('/vaults', vaultRoutes);
  app.route('/products', productsRoutes);
  app.route('/admin', adminRoutes);
  app.route('/tenants', tenantsRoutes);
  app.route('/images', imageRoutes);
  app.route('/notifications', notificationRoutes);
  app.route('/points', pointRoutes);
  app.route('/nfts', nftsRoutes);
  app.route('/locations', locationRoutes);
  app.route('/', oldNftMetadataRoute);
}
