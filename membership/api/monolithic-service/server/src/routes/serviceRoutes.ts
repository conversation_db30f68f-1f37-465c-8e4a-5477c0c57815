import { OpenAPIHono } from '@hono/zod-openapi';
import { container } from '../configs/container';
import { QuestController } from '../controllers/questController';
import { ServiceInfoController } from '../controllers/serviceInfoController';
import { RewardController } from '../controllers/rewardController';
import { ActionController } from '../controllers/actionController';
import { QuestionnaireController } from '../controllers/questionnaireController';
import * as servicesEndpoint from '../endpoints/servicesEndpoint';
import { SerialCodeController } from '../controllers/serialCodeController';

const serviceRoutes = new OpenAPIHono();
const questController = container.resolve(QuestController);
const serviceInfoController = container.resolve(ServiceInfoController);
const rewardController = container.resolve(RewardController);
const actionController = container.resolve(ActionController);
const questionnaireController = container.resolve(QuestionnaireController);
const serialCodeController = container.resolve(SerialCodeController);

serviceRoutes.openapi(servicesEndpoint.fetchServiceRoute, (c) => serviceInfoController.getServiceInfo(c));
serviceRoutes.openapi(servicesEndpoint.fetchQuestsRoute, (c) => questController.getQuests(c));
serviceRoutes.openapi(servicesEndpoint.fetchStatusQuestsRoute, (c) => questController.getQuestsByStatus(c));
serviceRoutes.openapi(servicesEndpoint.fetchQuestRoute, (c) => questController.getQuest(c));
serviceRoutes.openapi(servicesEndpoint.fetchRewardRoute, (c) => rewardController.getReward(c));
serviceRoutes.openapi(servicesEndpoint.fetchActionRoute, (c) => actionController.getAction(c));
serviceRoutes.openapi(servicesEndpoint.getQuestionnaireRoute, (c) => questionnaireController.getQuestionnaire(c));
serviceRoutes.openapi(servicesEndpoint.createServiceRoute, (c) => serviceInfoController.registerService(c));
serviceRoutes.openapi(servicesEndpoint.createQuestRoute, (c) => questController.createQuest(c));
serviceRoutes.openapi(servicesEndpoint.createQuestionnaireRoute, (c) => questionnaireController.createQuestionnaire(c));
serviceRoutes.openapi(servicesEndpoint.createQuestActionRoute, (c) => questController.createQuestAction(c));
serviceRoutes.openapi(servicesEndpoint.createQuestRewardRoute, (c) => questController.createQuestReward(c));
serviceRoutes.openapi(servicesEndpoint.createRewardRoute, (c) => questController.createReward(c));
serviceRoutes.openapi(servicesEndpoint.updateQuestionnaireRoute, (c) => questionnaireController.updateQuestionnaire(c));
serviceRoutes.openapi(servicesEndpoint.importSerialCodeRoute, (c) => serialCodeController.import(c));
serviceRoutes.openapi(servicesEndpoint.createSerialCodeRoute, (c) => serialCodeController.create(c));
serviceRoutes.openapi(servicesEndpoint.updateServiceCustomFieldsRoute, (c) => serviceInfoController.updateServiceCustomFields(c));
serviceRoutes.openapi(servicesEndpoint.getServiceCustomFieldsRoute, (c) => serviceInfoController.getServiceCustomFields(c));
serviceRoutes.openapi(servicesEndpoint.getSerialCodeProjects, (c) => serialCodeController.fetchProjects(c));

export { serviceRoutes };
