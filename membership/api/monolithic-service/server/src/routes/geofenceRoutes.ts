import { createRoute } from '@hono/zod-openapi';
import { z } from 'zod';
import {
  PostLocationGeofenceRequestSchema,
  PostLocationGeofenceResponseSchema,
  PutLocationGeofenceRequestSchema,
  PutLocationGeofenceResponseSchema,
  GetLocationGeofenceResponseSchema,
  GetLocationGeofencesResponseSchema,
  PathParameterGeofenceIdSchema,
} from '../dtos/geofences/schemas';
import { ErrorResponseSchema } from '../dtos/common/schemas';

// =======================
// POST /services/geofences - Create Geofence
// =======================
export const postGeofenceRoute = createRoute({
  method: 'post',
  path: '/services/geofences',
  tags: ['Geofences'],
  summary: 'Create a new geofence',
  description: 'Create a new geofence for location-based actions. Supports both circle and polygon geofences.',
  request: {
    body: {
      content: {
        'application/json': {
          schema: PostLocationGeofenceRequestSchema,
        },
      },
      description: 'Geofence data to create',
      required: true,
    },
    headers: z.object({
      'service-id-header': z.string().openapi({
        description: 'Service ID header',
        example: 'service-123',
      }),
    }),
  },
  responses: {
    201: {
      content: {
        'application/json': {
          schema: PostLocationGeofenceResponseSchema,
        },
      },
      description: 'Geofence created successfully',
    },
    400: {
      content: {
        'application/json': {
          schema: ErrorResponseSchema,
        },
      },
      description: 'Invalid request data',
    },
    401: {
      content: {
        'application/json': {
          schema: ErrorResponseSchema,
        },
      },
      description: 'Unauthorized',
    },
    500: {
      content: {
        'application/json': {
          schema: ErrorResponseSchema,
        },
      },
      description: 'Internal server error',
    },
  },
});

// =======================
// PUT /services/geofences/{geofenceId} - Update Geofence
// =======================
export const putGeofenceRoute = createRoute({
  method: 'put',
  path: '/services/geofences/{geofenceId}',
  tags: ['Geofences'],
  summary: 'Update an existing geofence',
  description: 'Update an existing geofence by ID. Can modify name and geometry data.',
  request: {
    params: PathParameterGeofenceIdSchema,
    body: {
      content: {
        'application/json': {
          schema: PutLocationGeofenceRequestSchema,
        },
      },
      description: 'Updated geofence data',
      required: true,
    },
    headers: z.object({
      'service-id-header': z.string().openapi({
        description: 'Service ID header',
        example: 'service-123',
      }),
    }),
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: PutLocationGeofenceResponseSchema,
        },
      },
      description: 'Geofence updated successfully',
    },
    400: {
      content: {
        'application/json': {
          schema: ErrorResponseSchema,
        },
      },
      description: 'Invalid request data',
    },
    401: {
      content: {
        'application/json': {
          schema: ErrorResponseSchema,
        },
      },
      description: 'Unauthorized',
    },
    404: {
      content: {
        'application/json': {
          schema: ErrorResponseSchema,
        },
      },
      description: 'Geofence not found',
    },
    500: {
      content: {
        'application/json': {
          schema: ErrorResponseSchema,
        },
      },
      description: 'Internal server error',
    },
  },
});

// =======================
// GET /services/geofences - Get All Geofences
// =======================
export const getGeofencesRoute = createRoute({
  method: 'get',
  path: '/services/geofences',
  tags: ['Geofences'],
  summary: 'Get all geofences for a service',
  description: 'Retrieve all geofences belonging to the specified service.',
  request: {
    headers: z.object({
      'service-id-header': z.string().openapi({
        description: 'Service ID header',
        example: 'service-123',
      }),
    }),
    query: z.object({
      page: z.string().optional().openapi({
        description: 'Page number for pagination',
        example: '1',
      }),
      limit: z.string().optional().openapi({
        description: 'Number of items per page',
        example: '20',
      }),
      geofenceType: z.enum(['CIRCLE', 'POLYGON']).optional().openapi({
        description: 'Filter by geofence type',
        example: 'CIRCLE',
      }),
      search: z.string().optional().openapi({
        description: 'Search by name',
        example: 'Tokyo',
      }),
    }),
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: GetLocationGeofencesResponseSchema,
        },
      },
      description: 'Geofences retrieved successfully',
    },
    401: {
      content: {
        'application/json': {
          schema: ErrorResponseSchema,
        },
      },
      description: 'Unauthorized',
    },
    500: {
      content: {
        'application/json': {
          schema: ErrorResponseSchema,
        },
      },
      description: 'Internal server error',
    },
  },
});

// =======================
// GET /services/geofences/{geofenceId} - Get Specific Geofence
// =======================
export const getGeofenceRoute = createRoute({
  method: 'get',
  path: '/services/geofences/{geofenceId}',
  tags: ['Geofences'],
  summary: 'Get a specific geofence by ID',
  description: 'Retrieve detailed information about a specific geofence.',
  request: {
    params: PathParameterGeofenceIdSchema,
    headers: z.object({
      'service-id-header': z.string().openapi({
        description: 'Service ID header',
        example: 'service-123',
      }),
    }),
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: GetLocationGeofenceResponseSchema,
        },
      },
      description: 'Geofence retrieved successfully',
    },
    401: {
      content: {
        'application/json': {
          schema: ErrorResponseSchema,
        },
      },
      description: 'Unauthorized',
    },
    404: {
      content: {
        'application/json': {
          schema: ErrorResponseSchema,
        },
      },
      description: 'Geofence not found',
    },
    500: {
      content: {
        'application/json': {
          schema: ErrorResponseSchema,
        },
      },
      description: 'Internal server error',
    },
  },
});

// =======================
// DELETE /services/geofences/{geofenceId} - Delete Geofence
// =======================
export const deleteGeofenceRoute = createRoute({
  method: 'delete',
  path: '/services/geofences/{geofenceId}',
  tags: ['Geofences'],
  summary: 'Delete a geofence',
  description: 'Delete a geofence by ID. This will also remove any associated location actions.',
  request: {
    params: PathParameterGeofenceIdSchema,
    headers: z.object({
      'service-id-header': z.string().openapi({
        description: 'Service ID header',
        example: 'service-123',
      }),
    }),
  },
  responses: {
    204: {
      description: 'Geofence deleted successfully',
    },
    401: {
      content: {
        'application/json': {
          schema: ErrorResponseSchema,
        },
      },
      description: 'Unauthorized',
    },
    404: {
      content: {
        'application/json': {
          schema: ErrorResponseSchema,
        },
      },
      description: 'Geofence not found',
    },
    500: {
      content: {
        'application/json': {
          schema: ErrorResponseSchema,
        },
      },
      description: 'Internal server error',
    },
  },
});
