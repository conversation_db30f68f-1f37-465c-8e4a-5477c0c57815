import { OpenAPIHono } from '@hono/zod-openapi';
import { container } from '../configs/container';
import { LocationController } from '../controllers/locationController';
import * as locationHandler from '../endpoints/locationEndpoints';

const locationRoutes = new OpenAPIHono();
const locationController = container.resolve(LocationController);


locationRoutes.openapi(locationHandler.postGeofenceRoute, (c) => locationController.createGeofence(c));
locationRoutes.openapi(locationHandler.putGeofenceRoute, (c) => locationController.updateGeofence(c));
locationRoutes.openapi(locationHandler.getGeofencesRoute, (c) => locationController.getGeofences(c));
locationRoutes.openapi(locationHandler.getGeofenceRoute, (c) => locationController.getGeofence(c));

export { locationRoutes };
