import { Hono } from 'hono';
import { zValidator } from '@hono/zod-validator';
import { container } from 'tsyringe';
import { LocationController } from '../controllers/locationController';
import { serviceIdHeaderSchema } from '../dtos/common/schemas';
import { 
  LocationCheckinRequestSchema,
  CreateGeofenceRequestSchema 
} from '../dtos/location/schemas';
import { validationResultHandler } from '../middleware/validationResultHandler';

const locationRoutes = new Hono();
const locationController = container.resolve(LocationController);

// =======================
// Geofence Management Routes
// =======================

// Create a new geofence
locationRoutes.post(
  '/geofences',
  zValidator('header', serviceIdHeaderSchema, validationResultHandler),
  zValidator('json', CreateGeofenceRequestSchema, validationResultHandler),
  (c) => locationController.createGeofence(c)
);

// Get all geofences for a service
locationRoutes.get(
  '/geofences',
  zValidator('header', serviceIdHeaderSchema, validationResultHandler),
  (c) => locationController.getGeofences(c)
);

// Get a specific geofence
locationRoutes.get(
  '/geofences/:geofenceId',
  zValidator('header', serviceIdHeaderSchema, validationResultHandler),
  (c) => locationController.getGeofence(c)
);

// Update a geofence
locationRoutes.put(
  '/geofences/:geofenceId',
  zValidator('header', serviceIdHeaderSchema, validationResultHandler),
  zValidator('json', CreateGeofenceRequestSchema.partial(), validationResultHandler),
  (c) => locationController.updateGeofence(c)
);

// Delete a geofence
locationRoutes.delete(
  '/geofences/:geofenceId',
  zValidator('header', serviceIdHeaderSchema, validationResultHandler),
  (c) => locationController.deleteGeofence(c)
);

// =======================
// Location Check-in Routes
// =======================

// Perform location check-in for an action
locationRoutes.post(
  '/accounts/:accountId/checkin',
  zValidator('header', serviceIdHeaderSchema, validationResultHandler),
  zValidator('json', LocationCheckinRequestSchema, validationResultHandler),
  (c) => locationController.performLocationCheckin(c)
);

// Get location check-in attempts for an account and action
locationRoutes.get(
  '/accounts/:accountId/actions/:actionId/attempts',
  zValidator('header', serviceIdHeaderSchema, validationResultHandler),
  (c) => locationController.getLocationCheckinAttempts(c)
);

// Check if location is within geofence (utility endpoint)
locationRoutes.post(
  '/geofences/:geofenceId/check',
  zValidator('header', serviceIdHeaderSchema, validationResultHandler),
  zValidator('json', LocationCheckinRequestSchema.pick({ location: true }), validationResultHandler),
  (c) => locationController.checkLocationInGeofence(c)
);

export { locationRoutes };
