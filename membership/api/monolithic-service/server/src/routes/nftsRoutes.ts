import { OpenAPIHono } from '@hono/zod-openapi';
import { container } from '../configs/container';
import { NftsController } from '../controllers/nftsController';
import * as nftHandler from '../endpoints/nftsEndpoint';

const  nftsRoutes = new OpenAPIHono();
const nftsController = container.resolve(NftsController);

nftsRoutes.openapi(nftHandler.getMetadataRoute, (c) => nftsController.getMetadata(c));
nftsRoutes.openapi(nftHandler.postNftMetadataRoute, (c) => nftsController.searchMetadataByWallet(c));
nftsRoutes.openapi(nftHandler.retryTransactionRoute, (c) => nftsController.retryTransactions(c));
nftsRoutes.openapi(nftHandler.mintNFTRoute, (c) => nftsController.mint(c));
nftsRoutes.openapi(nftHandler.registerNFTRoute, (c) => nftsController.register(c));
nftsRoutes.openapi(nftHandler.getI18nMetadataRoute, (c) => nftsController.getI18nMetadata(c));
nftsRoutes.openapi(nftHandler.bulkMintNFTRoute, (c) => nftsController.bulkMint(c));
nftsRoutes.openapi(nftHandler.updateTxFinalityStatusRoute, (c) => nftsController.updateTxFinalityStatus(c));
nftsRoutes.openapi(nftHandler.deployModularNFTRoute, (c) => nftsController.deployModular(c));
nftsRoutes.openapi(nftHandler.grantNftsMinterRoleRoute, (c) => nftsController.grantNftsMinterRole(c));
nftsRoutes.openapi(nftHandler.generateNonceForSigRoute, (c) => nftsController.generateNonceForSig(c));
nftsRoutes.openapi(nftHandler.verifySigAndFetchNftsRoute, (c) => nftsController.verifySigAndFetchNfts(c));
nftsRoutes.openapi(nftHandler.checkTokenGateAccessRoute, (c) => nftsController.checkTokenGateAccess(c));
nftsRoutes.openapi(nftHandler.registerTokenGateRoute, (c) => nftsController.registerTokenGate(c));

export { nftsRoutes };

