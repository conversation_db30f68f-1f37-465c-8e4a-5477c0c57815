import { OpenAPIHono } from '@hono/zod-openapi';
import { container } from '../configs/container';
import { UserController } from '../controllers/userController';
import * as userHandlers from '../endpoints/userEndpoints';

const userRoutes = new OpenAPIHono();
const userController = container.resolve(UserController);

userRoutes.openapi(userHandlers.createUserRoute, (c) => userController.create(c));
userRoutes.openapi(userHandlers.getUserByIdRoute, (c) => userController.getSanitized(c));
userRoutes.openapi(userHandlers.deleteUserRoute, (c) => userController.delete(c));
userRoutes.openapi(userHandlers.updateContractAccountRoute, (c) => userController.updateContractAccount(c));
userRoutes.openapi(userHandlers.updateUserPhoneNumberRoute, (c) => userController.updatePhoneNumber(c));
userRoutes.openapi(userHandlers.fetchUserBackupKeyRoute, (c) => userController.getBackupKey(c));
userRoutes.openapi(userHandlers.checkUserRoute, (c) => userController.checkUser(c));
userRoutes.openapi(userHandlers.fetchUserRecoveryShareRoute, (c) => userController.getRecoveryShare(c));
userRoutes.openapi(userHandlers.storeUserRecoveryShareRoute, (c) => userController.storeRecoveryShare(c));

export { userRoutes };
