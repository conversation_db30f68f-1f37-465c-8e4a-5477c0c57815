import { OpenAPIHono } from '@hono/zod-openapi';
import { container } from '../configs/container';
import { NftsController } from '../controllers/nftsController';
import * as nftHandler from '../endpoints/nftsEndpoint';

const oldNftMetadataRoute = new OpenAPIHono();
const nftsController = container.resolve(NftsController);

oldNftMetadataRoute.openapi(nftHandler.getOldMetadataRoute, (c) => nftsController.getMetadata(c));

export { oldNftMetadataRoute };
