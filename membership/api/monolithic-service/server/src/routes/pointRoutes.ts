import { OpenAPIHono } from '@hono/zod-openapi';
import { container } from '../configs/container';
import * as pointHandler from '../endpoints/pointEndpoints';
import { PointController } from '../controllers/pointController';

const pointRoutes = new OpenAPIHono();
const pointController = container.resolve(PointController);

pointRoutes.openapi(pointHandler.expireRewardPointsRoute, (c) => pointController.expireRewardPoints(c));
pointRoutes.openapi(pointHandler.getTotalRewardPointsRoute, (c) => pointController.getTotalRewardPoints(c));
pointRoutes.openapi(pointHandler.getTotalStatusPointsRoute, (c) => pointController.getTotalStatusPoints(c));

export { pointRoutes };
