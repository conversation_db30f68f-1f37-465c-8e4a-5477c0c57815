import { OpenAPIHono } from '@hono/zod-openapi';
import { AccountController } from '../controllers/accountController';
import { container } from '../configs/container';
import * as accountHandler from '../endpoints/accountsEndpoints';
import { ActionController } from '../controllers/actionController';
import { UserOperationController } from '../controllers/userOperationController';
import { RewardController } from '../controllers/rewardController';
import { QuestController } from '../controllers/questController';
import { SerialCodeController } from '../controllers/serialCodeController';

const accountsRoutes = new OpenAPIHono();

const accountController = container.resolve(AccountController);
const actionController = container.resolve(ActionController);
const userOpController = container.resolve(UserOperationController);
const serialCodeController = container.resolve(SerialCodeController);
const rewardController = container.resolve(RewardController);
const questController = container.resolve(QuestController);

accountsRoutes.openapi(accountHandler.postAccountRoute, (c) => accountController.createAccount(c));
accountsRoutes.openapi(accountHandler.putAccountLineProfileRoute, (c) => accountController.updateUserLineProfile(c));
accountsRoutes.openapi(accountHandler.getAccountRoute, (c) => accountController.getAccount(c));
accountsRoutes.openapi(accountHandler.getAccountStatusRoute, (c) => accountController.getAccountStatus(c));
accountsRoutes.openapi(accountHandler.deleteAccountRoute, (c) => accountController.delete(c));
accountsRoutes.openapi(accountHandler.getAccountActivityHistoryRoute, (c) =>
  accountController.getAccountActivityHistory(c),
);
accountsRoutes.openapi(accountHandler.getNotificationsRoute, (c) => accountController.getAccountNotifications(c));
accountsRoutes.openapi(accountHandler.updateLastLogin, (c) => accountController.updateLastLogin(c));

accountsRoutes.openapi(accountHandler.completeActionRoute, (c) => actionController.completeAction(c));
accountsRoutes.openapi(accountHandler.prepareConsumptionUORoute, (c) => userOpController.prepareUserOperation(c));
accountsRoutes.openapi(accountHandler.sendUORoute, (c) => userOpController.storeUserOperationData(c));

accountsRoutes.openapi(accountHandler.postClaimRewardRoute, (c) => rewardController.claimReward(c));

accountsRoutes.openapi(accountHandler.getAccountQuests, (c) => questController.getAccountQuests(c));
accountsRoutes.openapi(accountHandler.getAccountQuestDetail, (c) => questController.getAccountQuest(c));
accountsRoutes.openapi(accountHandler.getAccountQuestionnaireDetail, (c) =>
  questController.getAnsweredQuestionnaire(c),
);
accountsRoutes.openapi(accountHandler.postAccountSerialCodeRedeem, (c) => serialCodeController.redeem(c));
accountsRoutes.openapi(accountHandler.getAccountCustomFieldRoute, (c) => accountController.getAccountCustomField(c));
accountsRoutes.openapi(accountHandler.updateAccountCustomFieldRoute, (c) => accountController.updateAccountCustomField(c));


export { accountsRoutes };
