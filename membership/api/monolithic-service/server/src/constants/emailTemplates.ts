import { LanguageCode, languageCode } from '../enum/languageCode';

export enum EmailTemplateType {
  OTP_VERIFICATION = 'OTP_VERIFICATION',
}

export interface EmailTemplate {
  subject: string;
  html: string;
}

type EmailTemplateGenerator = (params: any) => EmailTemplate;

interface TemplateLanguageMap {
  [key: string]: EmailTemplateGenerator;
}

interface EmailTemplatesMap {
  [key: string]: TemplateLanguageMap;
}


export const EMAIL_TEMPLATES: EmailTemplatesMap = {
  [EmailTemplateType.OTP_VERIFICATION]: {
    [languageCode.EN_US]: ({ otp }: { otp: string }) => ({
      subject: 'Email Verification',
      html: `Your verification code is <strong>${otp}</strong>. It will expire in 5 minutes.`
    }),
    [languageCode.JA]: ({ otp }: { otp: string }) => ({
      subject: 'メール認証',
      html: `認証コードは <strong>${otp}</strong> です。このコードは5分後に期限切れになります。`
    })
  },
};

export function getEmailTemplate(
  templateName: EmailTemplateType, 
  lang: LanguageCode, 
  params: any = {}
): EmailTemplate {
  const template = EMAIL_TEMPLATES[templateName];
  
  if (!template) {
    throw new Error(`Email template "${templateName}" not found`);
  }
  
  const templateGenerator = template[lang] || template[languageCode.JA];
  
  if (!templateGenerator) {
    throw new Error(`No template available for language "${lang}" and no Japanese fallback available`);
  }
  
  return templateGenerator(params);
}
