// Tenant Dashboard MS
export const TABLE_TOKEN_BOUND_ACCOUNT_REGISTRIES = 'token_bound_account_registries';
export const TABLE_TOKEN_BOUND_ACCOUNT_IMPLEMENTATIONS = 'token_bound_account_implementations';
export const TABLE_NFT_CONTRACT_TYPES = 'nft_contract_types';
export const TABLE_NFT_BASE_METADATAS = 'nft_base_metadatas';
export const TABLE_NFT_METADATAS = 'nft_metadatas';
export const TABLE_USER_OPERATION_QUEUES = 'user_operation_queues';
export const TABLE_VAULT_TRANSACTION_QUEUES = 'vault_transaction_queues';
export const TABLE_TRANSACTION_QUEUES = 'transaction_queues';
export const TABLE_ATTEMPT_TRANSACTIONS = 'attempt_transactions';
export const TABLE_TRANSACTIONS = 'transactions';
export const TABLE_NFT_CONTRACTS = 'nft_contracts';
// Contract Engine MS
export const TABLE_ADMINS = 'admins';
export const TABLE_TENANTS = 'tenants';
export const TABLE_PLANS = 'plans';
export const TABLE_SERVICES = 'services';
export const TABLE_SERVICE_CUSTOM_FIELDS = 'service_custom_fields';
export const TABLE_SERVICE_CUSTOM_FIELD_TRANSLATIONS = 'service_custom_field_translations';
export const TABLE_SERVICE_CUSTOM_FIELD_OPTIONS = 'service_custom_field_options';
export const TABLE_SERVICE_CUSTOM_FIELD_OPTION_TRANSLATIONS = 'service_custom_field_option_translations';
export const TABLE_SERVICE_CUSTOM_FIELD_VALIDATORS = 'service_custom_field_validators';
export const TABLE_SERVICE_CUSTOM_FIELD_VALIDATOR_TRANSLATIONS = 'service_custom_field_validator_translations';
export const TABLE_VAULT_KEYS = 'vault_keys';
export const TABLE_REWARDS = 'rewards';
export const TABLE_QUESTS = 'quests';

export const TABLE_ACTIONS = 'actions';
export const TABLE_QR_CHECKIN_ACTIONS = 'qr_checkin_actions';
export const TABLE_ONLINE_CHECKIN_ACTIONS = 'online_checkin_actions';
export const TABLE_ACHIEVEMENT_ACTIONS = 'achievement_actions';
export const TABLE_QUESTIONNAIRE_ACTIONS = 'questionnaire_actions';

export const TABLE_USERS = 'users';
export const TABLE_AUTH_PROVIDERS = 'auth_providers';
export const TABLE_ACCOUNTS = 'accounts';
export const TABLE_ACCOUNT_CUSTOM_FIELD_VALUES = 'account_custom_field_values';
export const TABLE_QUEST_REWARDS = 'quest_rewards';

export const TABLE_PRODUCTS = 'products';
export const TABLE_CHECKOUTS = 'checkouts';
export const TABLE_CUSTOM_FIELDS = 'custom_fields';
export const TABLE_PRODUCT_CUSTOM_FIELDS = 'product_custom_fields';
export const TABLE_GEOFENCES = 'geofences';

export const TABLE_QUESTIONNAIRES = 'questionnaires';
export const TABLE_QUESTIONNAIRE_THEMES = 'questionnaire_themes';
export const TABLE_QUESTIONNAIRE_QUESTIONS = 'questionnaire_questions';
export const TABLE_QUESTIONNAIRE_RESULT_RANKS = 'questionnaire_result_ranks';
export const TABLE_QUESTIONNAIRE_RESULT_ANSWERS = 'questionnaire_result_answers';
export const TABLE_QUESTIONNAIRE_QUESTION_ANSWERS = 'questionnaire_question_answers';

export const TABLE_GLOBAL_NOTIFICATIONS = 'global_notifications';
export const TABLE_ACCOUNT_NOTIFICATIONS = 'account_notifications';
export const TABLE_NOTIFICATIONS = 'notifications';
export const TABLE_NOTIFICATION_TRANSLATIONS = 'notification_translations';
export const TABLE_SERIAL_CODE_PROJECTS = 'serial_code_projects';
export const TABLE_SERIAL_CODES = 'serial_codes';
export const TABLE_ACCOUNT_SERIAL_CODES = 'account_serial_codes';

// Translations
export const TABLE_ACTION_TRANSLATIONS = 'action_translations';
export const TABLE_CUSTOM_FIELD_TRANSLATIONS = 'custom_field_translations';
export const TABLE_PLAN_TRANSLATIONS = 'plan_translations';
export const TABLE_PRODUCT_TRANSLATIONS = 'product_translations';
export const TABLE_QUEST_TRANSLATIONS = 'quest_translations';
export const TABLE_QUESTIONNAIRE_QUESTION_TRANSLATIONS = 'questionnaire_question_translations';
export const TABLE_REWARD_TRANSLATIONS = 'reward_translations';
export const TABLE_SERVICE_TRANSLATIONS = 'service_translations';
export const TABLE_TENANT_TRANSLATIONS = 'tenant_translations';
export const TABLE_QUESTIONNAIRE_THEME_TRANSLATIONS = 'questionnaire_theme_translations';
export const TABLE_SERIAL_CODE_PROJECT_TRANSLATIONS = 'serial_code_project_translations';
export const TABLE_QUESTIONNAIRE_RESULT_RANK_TRANSLATIONS = 'questionnaire_result_rank_translations';
