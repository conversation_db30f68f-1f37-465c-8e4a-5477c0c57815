// SerialCodeService.ts
import { createHmac, randomBytes } from 'crypto';
import { inject, injectable } from 'tsyringe';
import { db } from '../db/database';
import { ClaimedReward, SerialCodeRedeemResponse } from '../dtos/accounts/schemas';
import {
  SerialCodeGenerationResponse,
  SerialCodeProjects,
  SerialCodeResponse,
  SerialCodes,
  SerialCodesProcedure,
  SerialCodeTranslation,
} from '../dtos/serial_codes/schemas';
import { LanguageCode } from '../enum/languageCode';
import { ConflictError } from '../errors/conflictError';
import { NotFoundError } from '../errors/notFoundError';
import { ValidationError } from '../errors/validationError';
import { AccountSerialCodesRepository } from '../repositories/accountSerialCodesRepository';
import { SerialCodeProjectsCreation, SerialCodeProjectsRepository } from '../repositories/serialCodeProjectsRepository';
import { InsertSerialCode, SerialCodesRepository } from '../repositories/serialCodesRepository';
import { InsertableSerialCodeProjectTranslationRow } from '../tables/translations/serialCodeProjectTranslationsTable';
import { logger } from '../utils/middleware/loggerMiddleware';
import { RewardService } from './rewardService';

const CODE_TO_HASH_ALGORITHM = 'sha256';
const RANDOM_BYTES = 32;
const LOWRE_CHARS = 'abcdefghjkmnpqrstuvwxyz';
const UPPERS_CHARS = 'ABCDEFGHJKLMNPQRSTUVWXYZ';
const NUMBERS_CHARS = '********';
const DEFAULT_GENERATE_LENGTH = 16;
const DEFAULT_GENERATE_INCLUDE_LOWER = false;
const DEFAULT_GENERATE_INCLUDE_UPPER = true;
const DEFAULT_GENERATE_INCLUDE_NUMBERS = true;
const DEFAULT_GENERATE_SEPARATOR = '-';
const DEFAULT_GENERATE_SEGMENT_LENGTH = 4;

interface InsertSerialCodeOriginCode extends InsertSerialCode {
  code: string;
}

@injectable()
export class SerialCodeService {
  constructor(
    @inject('SerialCodeProjectsRepository') private serialCodeProjectsRepository: SerialCodeProjectsRepository,
    @inject('SerialCodesRepository') private serialCodesRepository: SerialCodesRepository,
    @inject('AccountSerialCodesRepository') private accountSerialCodesRepository: AccountSerialCodesRepository,
    @inject('RewardService') private rewardService: RewardService,
  ) {}

  async import(
    serviceId: string,
    slug: string,
    serialCodeTranslations: SerialCodeTranslation[],
    serialCodes: SerialCodes[],
    rewardId: string | undefined,
    hashKey: string | undefined,
    startAt: Date,
    endAt: Date,
  ): Promise<SerialCodeGenerationResponse> {
    hashKey = hashKey ?? randomBytes(RANDOM_BYTES).toString('hex');

    const { serialCodeProject, serialCodesImportResult, serialCodeProjectTranslations } = await db
      .transaction()
      .execute(async (trx) => {
        const creation: SerialCodeProjectsCreation = { serviceId, rewardId, slug, hashKey, startAt, endAt };
        const serialCodeProject = await this.serialCodeProjectsRepository.insert(creation, trx);
        logger.info({ function: 'serialCode.import', message: 'insert serialCodeProject', serialCodeProject });

        const serialCodeProjectTranslations: InsertableSerialCodeProjectTranslationRow[] = serialCodeTranslations.map(
          (translation) => {
            return {
              serial_code_project_id: serialCodeProject.serial_code_project_id,
              service_id: serviceId,
              language: translation.language,
              name: translation.name,
              description: translation.description,
            };
          },
        );
        const createdSerialCodeProjectTranslations = await this.serialCodeProjectsRepository.insertTranslation(
          serviceId,
          serialCodeProjectTranslations,
          trx,
        );
        const serialCodeDatas = serialCodes.reduce<Record<string, InsertSerialCodeOriginCode>>((acc, serialCode) => {
          const codeHash = this.serialCodeToHash(serialCode.code, hashKey);
          acc[codeHash] = {
            code: serialCode.code,
            codeHash,
            serialCodeProjectId: serialCodeProject.serial_code_project_id,
            maxUseNum: serialCode.maxUseNum,
          };
          return acc;
        }, {});
        const importedSerialCodes = await this.serialCodesRepository.insertSerialCodes(
          serviceId,
          Object.values(serialCodeDatas),
          trx,
        );
        logger.info({ function: 'serialCode.import', message: 'insert serialCodes', serialCodeDatas: serialCodeDatas });

        return {
          serialCodeProject,
          serialCodesImportResult: importedSerialCodes.map((code) => {
            return {
              serialCodeId: code.serial_code_id,
              code: serialCodeDatas[code.code_hash].code,
              codeHash: code.code_hash,
              maxUseNum: serialCodeDatas[code.code_hash].maxUseNum,
            };
          }),
          serialCodeProjectTranslations: createdSerialCodeProjectTranslations,
        };
      });

    return {
      serialCodeProjectId: serialCodeProject.serial_code_project_id,
      rewardId: serialCodeProject.reward_id!,
      slug: serialCodeProject.slug,
      startAt: serialCodeProject.start_at.toISOString(),
      endAt: serialCodeProject.end_at.toISOString(),
      serialCodes: serialCodesImportResult.map((data) => {
        const res: SerialCodeResponse = {
          serialCodeId: data.serialCodeId,
          code: data.code,
          codeHash: data.codeHash,
          maxUseNum: data.maxUseNum,
        };
        return res;
      }),
      serialCodeProjectTranslations: serialCodeProjectTranslations.map((translation) => {
        return {
          language: translation.language,
          name: translation.name,
          description: translation.description,
        };
      }),
    };
  }

  async create(
    serviceId: string,
    slug: string,
    serialCodeTranslations: SerialCodeTranslation[],
    procedures: SerialCodesProcedure[],
    rewardId: string | undefined,
    startAt: Date,
    endAt: Date,
  ): Promise<SerialCodeGenerationResponse> {
    const hashKey = randomBytes(RANDOM_BYTES).toString('hex');
    const generatedCodes: SerialCodes[] = [];
    for (const procedure of procedures) {
      const codes = this.generateUniqueSerialCodes(procedure.createCodeNum);

      generatedCodes.push(
        ...codes.map((code) => {
          return {
            code,
            maxUseNum: procedure.maxUseNum,
          };
        }),
      );
    }

    const createdResult = await this.import(
      serviceId,
      slug,
      serialCodeTranslations,
      generatedCodes,
      rewardId,
      hashKey,
      startAt,
      endAt,
    );

    return createdResult;
  }

  async fetchAvailableProjects(serviceId: string, lang: LanguageCode): Promise<SerialCodeProjects> {
    const projects = await this.serialCodeProjectsRepository.getAvailableSerialCodeProject(serviceId, lang);
    return {
      projects: projects.map((project) => ({
        serialCodeProjectId: project.serialCodeProjectId,
        name: project.name,
        description: project.description,
        slug: project.slug,
        startAt: project.startAt.toISOString(),
        endAt: project.endAt.toISOString(),
      })),
    };
  }

  async redeemAndClaimReward(
    serviceId: string,
    accountId: string,
    serialCode: string,
    serialCodeProjectId: string,
  ): Promise<ClaimedReward> {
    const rewardId = await this.serialCodeProjectsRepository.selectReferencedRewardId(serviceId, serialCodeProjectId);
    if (rewardId == null) {
      throw new ValidationError(`serial code project does not have reward. [${serialCodeProjectId}]`);
    }

    await this.redeem(serviceId, accountId, serialCode, serialCodeProjectId);
    return await this.rewardService.claimRewardWithoutQuest(accountId, rewardId, serviceId);
  }

  async redeem(
    serviceId: string,
    accountId: string,
    serialCode: string,
    serialCodeProjectId: string,
  ): Promise<SerialCodeRedeemResponse> {
    logger.info({ function: 'serialCode.redeem', serviceId, accountId, serialCode, serialCodeProjectId });

    const project = await this.serialCodeProjectsRepository.selectSerialCodeProject(serviceId, serialCodeProjectId);

    if (!project) {
      throw new NotFoundError(`serial code project does not exist. [${serialCodeProjectId}]`);
    }

    const codeHash = this.serialCodeToHash(serialCode, project.hash_key);
    logger.info({ function: 'serialCode.redeem', codeHash, serialCode, projectHashKey: project.hash_key });

    const { accountSerialCode, remainingUseNum } = await db.transaction().execute(async (trx) => {
      const serialCode = await this.serialCodesRepository.selectSerialCodeWithLock(serviceId, codeHash, trx);
      if (!serialCode) {
        throw new NotFoundError(`serial code does not exist. [${codeHash}]`);
      }

      if (serialCode.remaining_use_num === 0) {
        throw new ConflictError('not enough serial codes used.');
      }

      // use.
      const accountSerialCode = await this.accountSerialCodesRepository.insert(
        accountId,
        serialCode.serial_code_id,
        trx,
      );
      // update remaining.
      const useCodeCount = await this.accountSerialCodesRepository.countBySerialCodeId(serialCode.serial_code_id, trx);
      const remainingUseNum = serialCode.max_use_num - useCodeCount;
      await this.serialCodesRepository.updateRemainingUseNum(
        serviceId,
        serialCode.serial_code_id,
        remainingUseNum,
        trx,
      );

      logger.info({
        function: 'serialCode.redeem',
        message: 'use serialCode',
        accountId,
        serialCodeId: serialCode.serial_code_id,
        maxUseNum: serialCode.max_use_num,
        useCodeCount,
        remainingUseNum,
      });

      return {
        accountSerialCode,
        remainingUseNum,
      };
    });

    return {
      accountSerialCodeId: accountSerialCode.account_serial_code_id,
      serialCodeId: accountSerialCode.serial_code_id,
      remainingUseNum,
    };
  }

  serialCodeToHash(serialCode: string, hashKey: string): string {
    return createHmac(CODE_TO_HASH_ALGORITHM, hashKey).update(serialCode, 'utf8').digest('hex');
  }

  generateSerialCode(
    length: number = DEFAULT_GENERATE_LENGTH,
    includeLower: boolean = DEFAULT_GENERATE_INCLUDE_LOWER,
    includeUpper: boolean = DEFAULT_GENERATE_INCLUDE_UPPER,
    includeNumbers: boolean = DEFAULT_GENERATE_INCLUDE_NUMBERS,
    separator: string = DEFAULT_GENERATE_SEPARATOR,
    segmentLength: number = DEFAULT_GENERATE_SEGMENT_LENGTH,
  ): string {
    let pool = '';
    if (includeLower) {
      pool += LOWRE_CHARS;
    }
    if (includeUpper) {
      pool += UPPERS_CHARS;
    }
    if (includeNumbers) {
      pool += NUMBERS_CHARS;
    }

    if (!pool) {
      throw new Error('Not enough characters to Serial Code generate.');
    }

    const bytes = randomBytes(length);
    let raw = '';
    for (let i = 0; i < length; i++) {
      const idx = bytes[i] % pool.length;
      raw += pool[idx];
    }

    if (separator && segmentLength > 0) {
      let segments = [];
      for (let i = 0; i < raw.length; i += segmentLength) {
        segments.push(raw.slice(i, i + segmentLength));
      }
      return segments.join(separator);
    } else {
      return raw;
    }
  }

  generateUniqueSerialCodes(createCodeNum: number): string[] {
    const codes = new Set<string>();
    // infinite loop
    const maxAttempts = createCodeNum * 10;
    let attempts = 0;

    while (codes.size < createCodeNum) {
      codes.add(this.generateSerialCode());
      attempts++;
      if (attempts > maxAttempts) {
        throw new Error('unique serialcode generated error.');
      }
    }

    return Array.from(codes);
  }
}
