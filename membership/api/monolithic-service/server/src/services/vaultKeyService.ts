 
import { inject, injectable } from 'tsyringe';
import { VaultKeyRepository } from '../repositories/vaultKeyRepository';
import { KeyManagementServiceClient, protos } from '@google-cloud/kms';
import { InternalServerError } from '../errors/internalServerError';
import { v4 as uuidv4 } from 'uuid';
import { GcpKmsSigner } from '@cuonghx.gu-tech/ethers-gcp-kms-signer';
import Moralis from 'moralis';
import { ethers } from 'ethers';
import { WebhookService } from './webhookService';
import { ConsumptionData, UpdateVaultNonce, VaultDetails, VaultKeyInfo } from '../dtos/vaults/schemas';
import { LanguageCode } from '../enum/languageCode';

Moralis.start({
  apiKey: process.env.MORALIS_API_KEY,
});

@injectable()
export class VaultKeyService {
  private client: KeyManagementServiceClient;
  private projectId: string;
  private ALGORITHM: protos.google.cloud.kms.v1.CryptoKeyVersion.CryptoKeyVersionAlgorithm =
    protos.google.cloud.kms.v1.CryptoKeyVersion.CryptoKeyVersionAlgorithm.EC_SIGN_SECP256K1_SHA256;
  private PROTECTION_LEVEL: protos.google.cloud.kms.v1.ProtectionLevel = protos.google.cloud.kms.v1.ProtectionLevel.HSM;
  private PURPOSE: protos.google.cloud.kms.v1.CryptoKey.CryptoKeyPurpose =
    protos.google.cloud.kms.v1.CryptoKey.CryptoKeyPurpose.ASYMMETRIC_SIGN;
  private LOCATION = 'global';

  // should be '1' when the key is created
  private KEY_VERSION = '1';
  constructor(
    @inject('VaultKeyRepository') private vaultKeyRepository: VaultKeyRepository,
    @inject('WebhookService') private webhookService: WebhookService,
  ) {
    if (!process.env.GCP_PROJECT_ID) {
      throw new InternalServerError('GCP_PROJECT_ID environment variable is not set');
    }

    this.projectId = process.env.GCP_PROJECT_ID;
    this.client = new KeyManagementServiceClient();
  }

  async addVaultKey(tenantId: string): Promise<VaultKeyInfo> {
    try {
      const keyRingParent = this.client.locationPath(this.projectId, this.LOCATION);
      const keyringCreateResult = await this.client.createKeyRing({
        parent: keyRingParent,
        keyRingId: tenantId,
      });
      const keyring = keyringCreateResult[0];
      // example: projects/<projectName>>/locations/<locationName>/keyRings/<keyRingName>
      const keyringPath = keyring.name;

      const keyCreateResult = await this.client.createCryptoKey({
        parent: keyringPath,
        cryptoKeyId: tenantId,
        cryptoKey: {
          purpose: this.PURPOSE,
          versionTemplate: {
            algorithm: this.ALGORITHM,
            protectionLevel: this.PROTECTION_LEVEL,
          },
        },
      });
      const key = keyCreateResult[0];

      const keyVersionPath = `${key.name}/cryptoKeyVersions/${this.KEY_VERSION}`;
      await this.waitForCryptoKeyVersionEnabled(keyVersionPath);

      const signer = new GcpKmsSigner({
        projectId: this.projectId,
        locationId: this.LOCATION,
        keyRingId: tenantId,
        keyId: tenantId,
        versionId: this.KEY_VERSION,
      });

      const walletAddress = await signer.getAddress();

      const vaultKeyId = uuidv4();
      await this.vaultKeyRepository.insertVaultKey({
        vault_key_id: vaultKeyId,
        key_ring_project: tenantId,
        key_ring_location: this.LOCATION,
        key_ring_name: tenantId,
        key_version: this.KEY_VERSION,
        vault_wallet_address: walletAddress,
        tenant_id: tenantId,
        nonce: 0,
      });

      await this.webhookService.addVaultWalletAddressToWebhook(walletAddress);

      return {
        keyRingProject: tenantId,
        keyRingLocation: this.LOCATION,
        keyRingName: tenantId,
        vaultKeyName: key.name ?? '',
        vaultWalletAddress: walletAddress,
      };
    } catch (error) {
      throw new InternalServerError(`Error adding vault key: ${error}`);
    }
  }

  async waitForCryptoKeyVersionEnabled(name: string): Promise<void> {
    const targetState = 'ENABLED';
    const MAX_RETRIES = 30;
    let attempts = 0;
    let keyVersion;
    const retryDelay = process.env.NODE_ENV === 'test' ? 1 : 1000; // 1ms for test, 1000ms otherwise
    do {
      [keyVersion] = await this.client.getCryptoKeyVersion({ name });
      if (keyVersion.state !== targetState) {
        if (attempts >= MAX_RETRIES) {
          throw new InternalServerError('Exceeded maximum retries while waiting for CryptoKeyVersion to be enabled');
        }
        attempts++;
        await new Promise((resolve) => setTimeout(resolve, retryDelay)); // Use retryDelay
      }
      [keyVersion] = await this.client.getCryptoKeyVersion({ name });
      if (keyVersion.state !== targetState) {
        if (attempts >= MAX_RETRIES) {
          throw new InternalServerError('Exceeded maximum retries while waiting for CryptoKeyVersion to be enabled');
        }
        attempts++;
        await new Promise((resolve) => setTimeout(resolve, retryDelay)); // Use retryDelay
      }
    } while (keyVersion.state !== targetState);
  }

  async getVaultKeys(lang: LanguageCode): Promise<VaultDetails[]> {
    try {
      const vaultKeys = await this.vaultKeyRepository.getVaultKeys(lang);
      const walletAddresses = vaultKeys.map((vaultKey) => vaultKey.vault_wallet_address);
      const balancesResponses = await Promise.all(
        walletAddresses.map(async (address) => {
          if (!ethers.isAddress(address)) {
            return '0';
          }
          const balance = await Moralis.EvmApi.wallets.getWalletTokenBalancesPrice({
            chain: process.env.MORALIS_API_CHAIN,
            address: address,
          });

          const nativeTokenAddress = '******************************************';
          const balanceEntry = balance.result.find((e) => e.tokenAddress?.equals(nativeTokenAddress));
          return balanceEntry?.balanceFormatted;
        }),
      );

      const transactionHistories = await Promise.all(
        walletAddresses.map(async (address) => {
          if (!ethers.isAddress(address)) return { address, transactions: [] };

          let allTransactions: unknown[] = [];
          let hasNext = true;
          let cursor: string | undefined = undefined;

          while (hasNext) {
            const response = await Moralis.EvmApi.transaction.getWalletTransactions({
              chain: process.env.MORALIS_API_CHAIN,
              order: 'DESC',
              fromDate: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
              toDate: new Date().toISOString(),
              address: address,
              cursor: cursor,
            });

            allTransactions = [...allTransactions, ...response.raw.result];
            hasNext = response.hasNext();
            cursor = response.raw.cursor;
          }

          return { address, transactions: allTransactions };
        }),
      );

      const transactionMap = new Map(
        transactionHistories.map((history) => [history.address.toLowerCase(), history.transactions]),
      );

      return vaultKeys.map((vaultKey, index) => {
        const walletAddress = vaultKey.vault_wallet_address.toLowerCase();
        const balanceResponse = balancesResponses[index] || '0';
        const transactionHistory = transactionMap.get(walletAddress) || [];

        return {
          tenantName: vaultKey.tenant_name,
          vaultWalletAddress: walletAddress,
          chain: process.env.ALCHEMY_CHAIN_NAME || '',
          currentBalance: balanceResponse || '0',
          consumptionData: this.aggregateConsumption(transactionHistory, walletAddress),
        };
      });
    } catch (error) {
      throw new InternalServerError(`Error fetching vault key data: ${error}`);
    }
  }

  // async transferToken(
  //   tenantId: string,
  //   amount: number = 0,
  //   transferAll: boolean = false,
  // ): Promise<VaultTransferResponse> {

  //   return this.transactionService.transferNativeToken({
  //     tenantId,
  //     destinationAddress: this.destinationAddress,
  //     amount: amount,
  //     transferAll: transferAll,
  //   });
  // }

  private filterAndSumConsumption(transactions: any[], hours: number, from: string): number {
    const now = new Date();
    const filterTime = new Date(now.getTime() - hours * 60 * 60 * 1000);

    return transactions
      .filter(
        (tx: { block_timestamp: string; from_address: string; receipt_status: string }) =>
          new Date(tx.block_timestamp) >= filterTime &&
          tx.from_address.toLowerCase() === from.toLowerCase() &&
          tx.receipt_status === '1',
      )
      .reduce((total: number, tx: { transaction_fee: string; gas_price: string; value?: string }) => {
        const transactionFee = tx.transaction_fee || '0';
        const value = tx.value ? (parseFloat(tx.value) / 1e18).toString() : '0';
        return total + parseFloat(transactionFee) + parseFloat(value);
      }, 0);
  }

  public aggregateConsumption(transactions: unknown[], from: string): ConsumptionData {
    const now = new Date().toISOString();
    return {
      lastUpdatedAt: now,
      last1h: this.filterAndSumConsumption(transactions, 1, from),
      last6h: this.filterAndSumConsumption(transactions, 6, from),
      last12h: this.filterAndSumConsumption(transactions, 12, from),
      last24h: this.filterAndSumConsumption(transactions, 24, from),
    };
  }

  async updateNonce(vaultKeyId: string, nonce: number): Promise<UpdateVaultNonce> {
    await this.vaultKeyRepository.updateNonce(vaultKeyId, nonce);
    return { vaultKeyId, nonce };
  }
}
