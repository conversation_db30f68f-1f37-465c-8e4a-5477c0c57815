// NftsService.ts
import { Transaction } from 'kysely';
import { inject, injectable } from 'tsyringe';
import { v4 as uuidv4 } from 'uuid';
import { config } from '../configs/config';
import { Database } from '../db/database';
import { NftType } from '../enum/nftType';
import { InternalServerError } from '../errors/internalServerError';
import { NotFoundError } from '../errors/notFoundError';
import { ValidationError } from '../errors/validationError';
import { NftBaseMetadatasRepository } from '../repositories/nftBaseMetadatasRepository';
import { NftContractTypesRepository } from '../repositories/nftContractTypesRepository';
import { NftMetadatasRepository } from '../repositories/nftMetadatasRepository';
import { logger } from '../utils/middleware/loggerMiddleware';

export interface MetadataStandardFormat {
  name: string;
  description: string;
  image: string;
  external_url: string | null;
  animation_url: string | null;
  background_color: string | null;
  youtube_url: string | null;
  attributes:
    | {
        trait_type: string;
        value: unknown;
      }[]
    | null;
}

@injectable()
export class MetadataService {
  constructor(
    @inject('NftMetadatasRepository') private nftMetadatasRepository: NftMetadatasRepository,
    @inject('NftBaseMetadatasRepository') private nftBaseMetadatasRepository: NftBaseMetadatasRepository,
    @inject('NftContractTypesRepository') private nftContractTypesRepository: NftContractTypesRepository,
  ) {}

  public async getNftMetadata(nftId: string, tokenId: number): Promise<object> {
    // Attempt to find metadata in the NftMetadatas table
    const metadataEntity = await this.nftMetadatasRepository.selectMetadataByNftIdAndToken(nftId, tokenId);
    if (!metadataEntity) throw new NotFoundError('Metadata not found');

    return metadataEntity;
  }

  /**
   * Retrieves and parses NFT metadata from the repository.
   * @param contractAddress - The ContractAddress of the NFT
   * @param tokenId - The token ID of the NFT.
   * @returns Parsed metadata in the standard format.
   * @throws NotFoundError if metadata is not found.
   * @throws ValidationError if metadata parsing fails.
   */
  public async getParsedMetadataFromBaseMetadata(
    contractAddress: string,
    tokenId: number | undefined,
  ): Promise<MetadataStandardFormat> {
    // Attempt to find metadata in the NftMetadatas table
    let metadataEntity;
    logger.info({
      method: 'getParsedMetadataFromBaseMetadata',
      contractAddress: contractAddress,
      tokenId: tokenId,
    });
    if (tokenId === undefined || tokenId === null) {
      metadataEntity = await this.nftMetadatasRepository.selectBaseMetadataByContractAddress(contractAddress);
    } else {
      metadataEntity = await this.nftMetadatasRepository.selectBaseMetadataByContractAddressAndToken(
        contractAddress,
        tokenId,
      );
    }
    logger.info({ method: 'getParsedMetadataFromBaseMetadata', metadataEntity: metadataEntity });

    if (!metadataEntity) throw new NotFoundError('Base Metadata not found');

    const metadata: { [key: string]: unknown } = metadataEntity as { [key: string]: unknown };
    logger.info({ method: 'getParsedMetadataFromBaseMetadata', metadata: metadata });

    return {
      name: metadata.name as string,
      description: metadata.description as string,
      image: metadata.image as string,
      external_url: metadata.external_url as string | null,
      animation_url: metadata.animation_url as string | null,
      background_color: metadata.background_color as string | null,
      youtube_url: metadata.youtube_url as string | null,
      attributes: metadata.attributes as { trait_type: string; value: unknown }[],
    };
  }

  public async updateBaseMetadata(baseNftId: string, contractAddress: string) {
    await this.nftBaseMetadatasRepository.updateContractAddressNftBaseMetadata(baseNftId, contractAddress);
  }

  public async insertBaseMetadataAndGenerateBaseUrl(
    nftType: NftType,
    serviceId: string,
    contractTypeId: string,
    metadata: string | undefined,
  ) {
    const nftContractType = await this.nftContractTypesRepository.selectNftContractTypeById(contractTypeId);
    if (!nftContractType) throw new ValidationError('NFT contract type not found.');

    let metadataJson = undefined;
    if (!metadata) throw new ValidationError('Missing metadataUri for COUPON type.');
    try {
      const metadataJsonString = Buffer.from(metadata, 'base64').toString('utf-8');
      metadataJson = JSON.parse(metadataJsonString) as object;
    } catch {
      throw new ValidationError('Json format is invalid.');
    }

    const baseNftId = uuidv4();
    if (metadataJson) {
      await this.nftBaseMetadatasRepository.insertNftBaseMetadata({
        base_metadata_id: baseNftId,
        service_id: serviceId,
        metadata: metadataJson,
      });
    }

    switch (nftType) {
      case NftType.COUPON:
        return { metadataUrl: `${config.metadataUrl}/${baseNftId}/0`, baseNftId: baseNftId };
      case NftType.CERTIFICATE:
      case NftType.CONTENT:
      case NftType.MEMBERSHIP:
        return { metadataUrl: `${config.metadataUrl}/${baseNftId}/`, baseNftId: baseNftId };
    }
  }

  public async insertMetadata(
    nftType: NftType,
    contractAddress: string,
    tokenId: number,
    queueId: string,
    dbTx?: Transaction<Database>,
  ): Promise<string> {
    let metadata;
    let nftId: string | undefined;
    switch (nftType) {
      case NftType.CERTIFICATE: {
        const baseMetadataEntity = await this.nftBaseMetadatasRepository.selectDataByContractAddress(contractAddress);
        nftId = baseMetadataEntity?.base_metadata_id;
        const baseMetadata = baseMetadataEntity?.metadata;
        metadata = baseMetadata as { attributes: { trait_type: string; value: string }[] };
        metadata.attributes.forEach((attribute) => {
          if (attribute.trait_type === 'publish_date') {
            const date = new Date();
            attribute.value = date.toISOString().replace(/\.\d{3}Z$/, 'Z');
          }
        });
        logger.info({ method: 'insertMetadata', nftType: nftType, nftId: nftId, metadata: metadata });

        break;
      }
      case NftType.COUPON: {
        const baseMetadataEntity = await this.nftBaseMetadatasRepository.selectDataByContractAddressAndTokenId(
          contractAddress,
          tokenId,
        );
        nftId = baseMetadataEntity?.base_metadata_id;
        metadata = baseMetadataEntity?.metadata;
        logger.info({ method: 'insertMetadata', nftType: nftType, nftId: nftId, metadata: metadata });

        break;
      }
      case NftType.CONTENT:
      case NftType.MEMBERSHIP: {
        const baseMetadataEntity = await this.nftBaseMetadatasRepository.selectDataByContractAddress(contractAddress);
        nftId = baseMetadataEntity?.base_metadata_id;
        metadata = baseMetadataEntity?.metadata;
        logger.info({ method: 'insertMetadata', nftType: nftType, nftId: nftId, metadata: metadata });

        break;
      }
    }
    if (!metadata || !nftId) throw new NotFoundError(`Base metadata not found ${contractAddress}`);

    const insertdata = {
      base_metadata_id: nftId,
      token_id: tokenId,
      metadata: metadata,
      queue_id: queueId,
    };

    const isStored = await this.nftMetadatasRepository.selectMetadataByNftIdAndToken(nftId, tokenId);
    if (isStored) {
      logger.info({ method: 'insertMetadata', isStored: true });
      return `${config.metadataUrl}/${nftId}/${tokenId}`;
    }

    try {
      await this.nftMetadatasRepository.insertMetadata(insertdata, dbTx);
    } catch (error) {
      throw new InternalServerError(`Insert metadata failes: ${error}`);
    }

    return `${config.metadataUrl}/${nftId}/${tokenId}`;
  }
}
