// NftsService.ts
import { inject, injectable } from 'tsyringe';
import { NotFoundError } from '../errors/notFoundError';
import { NftContractsRepository } from '../repositories/nftContractsRepository';
import { AccountRepository } from '../repositories/accountRepository';
import { RewardRepository } from '../repositories/rewardRepository';
import { RewardType } from '../enum/rewardType';
import axios from 'axios';
import { InternalServerError } from '../errors/internalServerError';
import { logger } from '../utils/logger';
import { NftType } from '../enum/nftType';
import {
  CertificateNftMetadata,
  ContentNftMetadata,
  CouponNftMetadata,
  NftMetadataUnion,
  RequestAccount,
} from '../dtos/nfts/schemas';

interface BadgeInfo {
  rewardId: string;
  title: string;
  description: string;
  imageUrl: string;
  rank: number;
}

interface BadgeMetadata {
  attributes: unknown[];
  name: string;
  description: string;
  image: string;
}

interface MetaDataFormat {
  ownedNfts: [
    {
      contract: { address: string };
      tokenId: string;
      tokenType: string;
      name?: string;
      description?: string;
      image?: {
        cachedUrl?: string;
        originalUrl?: string;
      };
      raw?: {
        metadata?: {
          attributes?: [
            {
              value: string;
              trait_type: unknown;
            },
          ];
        };
      };
      balance?: string;
    },
  ];
}

@injectable()
export class NftsService {
  constructor(
    @inject('NftContractsRepository')
    private nftContractsRepository: NftContractsRepository,
    @inject('AccountRepository')
    private accountRepository: AccountRepository,
    @inject('RewardRepository')
    private rewardRepository: RewardRepository,
  ) {}

  async getUserBadges(serviceId: string, accountId: string): Promise<BadgeInfo[]> {
    const account = await this.accountRepository.selectAccountById(accountId, serviceId);
    if (!account) throw new NotFoundError('account not found');

    const data = await this.rewardRepository.selectStatusAchievementBadgeData(accountId, serviceId);
    const badgeMetadatas = data.map((d) => d.metadata) as BadgeMetadata[];

    const current = new Date();
    const badges = badgeMetadatas.map((data) => {
      const attr = this.parseMetadataAttributes(data.attributes);
      return {
        rewardId: attr.rewardId as string,
        title: data.name,
        description: data.description,
        imageUrl: data.image,
        rank: attr.rank as number | undefined,
        expireDate: attr.expireDate as Date,
      };
    });

    const filteredBadges = badges
      .filter((badge) => badge.expireDate > current && badge.rank !== undefined)
      .map((badge) => {
        return {
          rewardId: badge.rewardId,
          title: badge.title,
          description: badge.description,
          imageUrl: badge.imageUrl,
          rank: badge.rank as number,
        };
      });

    return filteredBadges;
  }

  async getMetadataByWallet(serviceId: string, { accountId, rewardType }: RequestAccount): Promise<NftMetadataUnion> {
    const account = await this.accountRepository.selectAccountById(accountId, serviceId);
    if (!account) throw new NotFoundError('account not found');
    const nftContracts = await this.nftContractsRepository.selectContractIdsByType(
      rewardType as unknown as NftType,
      serviceId,
    );
    if (nftContracts.length === 0) {
      switch (rewardType) {
        case RewardType.COUPON:
          return this.createCouponResponse([]);
        case RewardType.CERTIFICATE:
          return this.createCertificateResponse([]);
        case RewardType.CONTENT:
          return this.createContentResponse([]);
      }
    }

    const nftContractAddresses = nftContracts.map((contract) => contract.nft_contract_address) as string[];

    const alchemyEndpoint = `https://${process.env.ALCHEMY_CHAIN_NAME}.g.alchemy.com/nft/v3/${process.env.ALCHEMY_API_KEY}/getNFTsForOwner`;
    let axiosResponse;
    try {
      axiosResponse = await axios.get(alchemyEndpoint, {
        headers: {
          accept: 'application/json',
        },
        params: {
          owner: account.token_bound_account_address,
          contractAddresses: nftContractAddresses,
          withMetadata: true,
        },
        paramsSerializer: (params) => {
          return Object.entries(params)
            .map(([key, value]) => {
              if (Array.isArray(value)) {
                return value.map((v) => `${key}[]=${v}`).join('&');
              }
              return `${key}=${value}`;
            })
            .join('&');
        },
      });
    } catch {
      throw new InternalServerError('Failed to fetch NFT metadata from Alchemy API');
    }

    if (!axiosResponse.data || axiosResponse.data.ownedNfts.length === 0) {
      return this.createEmptyResponse(rewardType);
    }

    const metadatas = axiosResponse.data as MetaDataFormat;
    logger.info({ nfts_service: 'getMetadataByWallet', fetch_metadata: metadatas });

    const contractAddressToIdMap = Object.fromEntries(
      nftContracts.map((contract) => [contract.nft_contract_address, contract.nft_contract_id]),
    );
    const formattedMetadata: any = metadatas.ownedNfts
      .filter((nft) => {
        return nft.raw?.metadata?.attributes && nft.image && nft.description && nft.name;
      })
      .map((nft) => {
        const parsedAttributes = this.parseMetadataAttributes(nft.raw!.metadata!.attributes!);
        return {
          nftContractId: contractAddressToIdMap[nft.contract.address],
          contractAddress: nft.contract.address,
          tokenId: Number(nft.tokenId),
          amount: Number(nft.balance),
          title: nft.name,
          description: nft.description,
          imageUrl: nft.image!.originalUrl,
          ...parsedAttributes,
        };
      });

    switch (rewardType) {
      case RewardType.COUPON:
        return this.createCouponResponse(formattedMetadata);
      case RewardType.CERTIFICATE:
        logger.info(formattedMetadata);
        return this.createCertificateResponse(formattedMetadata);
      case RewardType.CONTENT:
        logger.info(formattedMetadata);
        return this.createContentResponse(formattedMetadata);
      default:
        throw new InternalServerError('Unexpected reward type is found');
    }
  }

  private parseMetadataAttributes(attributes: any[]): { [key: string]: any } {
    const parsed: { [key: string]: any } = {} as { [key: string]: any };

    for (const attr of attributes) {
      switch (attr.trait_type) {
        case 'reward_id':
          // COUPON & CERTIFICATE & STATUS CERTIFICATE & CONTENT
          parsed.rewardId = attr.value;
          break;
        case 'tags':
          // ALL
          parsed.tags = attr.value;
          break;
        case 'from_date':
          // COUPON only
          parsed.fromDate = attr.value ? new Date(attr.value) : attr.value;
          break;
        case 'to_date':
          // COUPON only
          parsed.toDate = attr.value ? new Date(attr.value) : attr.value;
          break;
        case 'jan_code':
          // COUPON only
          parsed.janCode = attr.value;
          break;
        case 'discount_amount':
          // COUPON only
          parsed.discountAmount = attr.value;
          break;
        case 'discount_unit':
          // COUPON only
          parsed.discountUnit = attr.value;
          break;
        case 'exchange_product':
          // COUPON only
          parsed.exchangeProduct = attr.value;
          break;
        case 'issuer':
          // CERTIFICATE & STATUS CERTIFICATE
          parsed.issuer = attr.value;
          break;
        case 'publish_date':
          // CERTIFICATE & STATUS CERTIFICATE
          parsed.publishDate = attr.value ? new Date(attr.value) : attr.value;
          break;
        case 'expire_date':
          // CERTIFICATE & STATUS CERTIFICATE
          parsed.expireDate = attr.value ? new Date(attr.value) : attr.value;
          break;
        case 'rank':
          // STATUS CERTIFICATE
          parsed.rank = attr.value;
          break;
        default:
          break;
      }
    }

    return parsed;
  }

  private createEmptyResponse(type: RewardType): NftMetadataUnion {
    switch (type) {
      case RewardType.COUPON:
        return this.createCouponResponse([]);
      case RewardType.CONTENT:
        return this.createContentResponse([]);
      case RewardType.CERTIFICATE:
        return this.createCertificateResponse([]);
    }
  }

  public createCouponResponse(coupons: CouponNftMetadata[]): NftMetadataUnion {
    return { type: RewardType.COUPON, coupons };
  }

  public createContentResponse(contents: ContentNftMetadata[]): NftMetadataUnion {
    return { type: RewardType.CONTENT, contents };
  }

  public createCertificateResponse(certificates: CertificateNftMetadata[]): NftMetadataUnion {
    return { type: RewardType.CERTIFICATE, certificates };
  }
}
