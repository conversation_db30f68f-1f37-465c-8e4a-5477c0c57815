import { inject, injectable } from 'tsyringe';
import { v4 as uuidv4 } from 'uuid';
import { AuthProviderRepository } from '../repositories/authProviderRepository';
import { ConflictError } from '../errors/conflictError';
import { NotFoundError } from '../errors/notFoundError';
import { UserService } from './userService';
import { FirebaseComponent } from '../components/firebaseComponent';
import { LineComponent } from '../components/lineComponent';
import { ServiceInfoRepository } from '../repositories/serviceInfoRepository';
import { CustomToken } from '../dtos/auth/schemas';
import { AccountRepository } from '../repositories/accountRepository';

@injectable()
export class AuthService {
  constructor(
    @inject('AuthProviderRepository')
    private authProviderRepository: AuthProviderRepository,
    @inject('UserService') private userService: UserService,
    @inject('LineComponent') private lineComponent: LineComponent,
    @inject('FirebaseComponent') private firebaseComponent: FirebaseComponent,
    @inject('ServiceInfoRepository') private serviceInfoRepository: ServiceInfoRepository,
    @inject('AccountRepository') private accountRepository: AccountRepository,
  ) {}

  async linkLineIdToken(firebaseIdToken: string, lineIdToken: string, serviceId: string): Promise<CustomToken> {
    const decodedFirebaseToken = await this.firebaseComponent.verifyFirebaseIdToken(firebaseIdToken);
    const clientId = await this.serviceInfoRepository.selectLineChannelId(serviceId);
    if (!clientId) {
      throw new NotFoundError('Service line channel id not found');
    }

    const decodedLineToken = await this.lineComponent.verifyLineIdToken(lineIdToken, clientId);
    const authProvider = await this.authProviderRepository.selectAuthProvider(decodedLineToken.sub, serviceId);
    if (authProvider) {
      throw new ConflictError('Line already linked');
    }

    const user = await this.userService.getUser(decodedFirebaseToken.uid);
    if (!user) {
      throw new NotFoundError('User not found');
    }
    const generatedUuid = uuidv4();

    await this.authProviderRepository.insertAuthProvider({
      provider_id: generatedUuid,
      provider_uid: decodedLineToken.sub,
      user_id: decodedFirebaseToken.uid,
      service_id: serviceId,
      provider_name: 'line',
    });

    const additionalClaims = {
      access: {
        serviceId: serviceId,
        providerUid: decodedLineToken.sub,
        provider: 'line',
      },
    };

    const newCustomToken = await this.firebaseComponent.createCustomToken(decodedFirebaseToken.uid, additionalClaims);
    return { token: newCustomToken };
  }

  async getCustomToken(lineIdToken: string, serviceId: string): Promise<CustomToken> {
    const clientId = await this.serviceInfoRepository.selectLineChannelId(serviceId);
    if (!clientId) {
      throw new NotFoundError('Service line channel id not found');
    }
    const decodedLineToken = await this.lineComponent.verifyLineIdToken(lineIdToken, clientId);
    const authProvider = await this.authProviderRepository.selectAuthProvider(decodedLineToken.sub, serviceId);
    if (!authProvider) {
      throw new NotFoundError('Auth provider not found');
    }
    const account = await this.accountRepository.selectAccountByUserId(authProvider.user_id, serviceId);
    if (!account) {
      throw new NotFoundError('Account not found for user');
    }

    const additionalClaims = {
      access: {
        service_id: serviceId,
        account_id: account.account_id,
      },
      provider: {
        name: 'line',
        uid: decodedLineToken.sub,
      }
    };

    const customToken = await this.firebaseComponent.createCustomToken(authProvider.user_id, additionalClaims);
    return { token: customToken };
  }
}
