import { GcpKmsSigner } from '@cuonghx.gu-tech/ethers-gcp-kms-signer';
import { ethers } from 'ethers';
import { Transaction } from 'kysely';
import { inject, injectable } from 'tsyringe';
import { v4 as uuidv4 } from 'uuid';
import { config } from '../configs/config';
import { Database, db } from '../db/database';
import { BulkMintNft, MintTransactionInfo } from '../dtos/nfts/schemas';
import { NftType } from '../enum/nftType';
import { TransactionQueueStatus } from '../enum/transactionQueueStatus';
import { TxType } from '../enum/txType';
import { InternalServerError } from '../errors/internalServerError';
import { NotFoundError } from '../errors/notFoundError';
import { ValidationError } from '../errors/validationError';
import { AccountRepository } from '../repositories/accountRepository';
import { DeliveryNftsFirestoreRepository } from '../repositories/deliveryNftsFirestoreRepository';
import { NftBaseMetadatasRepository } from '../repositories/nftBaseMetadatasRepository';
import { NftContractsRepository } from '../repositories/nftContractsRepository';
import { ServiceInfoRepository } from '../repositories/serviceInfoRepository';
import { TokenBoundAccountImplementationRepository } from '../repositories/tokenBoundAccountImplementationsRepository';
import { TokenBoundAccountRegistryAddressRepository } from '../repositories/tokenBoundAccountRegistryAddressRepository';
import { TransactionQueuesRepository } from '../repositories/transactionQueuesRepository';
import { TransactionsRepository } from '../repositories/transactionsRepository';
import { VaultKeyRepository } from '../repositories/vaultKeyRepository';
import { TransactionQueuesEntity, TransactionQueuesTable } from '../tables/transactionQueuesTable';
import { chunkArray, handleSettledResults } from '../utils/helper';
import { logger } from '../utils/middleware/loggerMiddleware';
import { MetadataService } from './metadataService';
import { NftMintService } from './nftMintService';
import { TransactionService } from './transactionService';

interface MembershipBulkData {
  contractAddress: string;
  tokenId: number;
  toAddress: string;
  tbaRegistoryAddress: string;
  tokenBoundAccountImplementationAddress: string;
  salt: string;
  chainId: string;
}

interface TbaData {
  queueId: string;
  tokenId: number;
  contractAddress: string;
  contractABI: object;
  args: (string | number)[];
}

@injectable()
export class BulkMintService {
  constructor(
    @inject('TransactionService') private transactionService: TransactionService,
    @inject('NftMintService') private nftMintService: NftMintService,
    @inject('NftContractsRepository') private nftContractsRepository: NftContractsRepository,
    @inject('TransactionQueuesRepository') private transactionQueuesRepository: TransactionQueuesRepository,
    @inject('TransactionsRepository') private transactionsRepository: TransactionsRepository,
    @inject('VaultKeyRepository') private vaultKeyRepository: VaultKeyRepository,
    @inject('ServiceInfoRepository') private serviceInfoRepository: ServiceInfoRepository,
    @inject('TokenBoundAccountImplementationRepository')
    private tokenBoundAccountImplementationRepository: TokenBoundAccountImplementationRepository,
    @inject('TokenBoundAccountRegistryAddressRepository')
    private tokenBoundAccountRegistryAddressRepository: TokenBoundAccountRegistryAddressRepository,
    @inject('AccountRepository') private accountRepository: AccountRepository,
    @inject('DeliveryNftsFirestoreRepository') private deliveryNftsFirestoreRepository: DeliveryNftsFirestoreRepository,
    @inject('NftBaseMetadatasRepository') private nftBaseMetadatasRepository: NftBaseMetadatasRepository,
    @inject('MetadataService') private metadataService: MetadataService,
  ) {}

  public async handleBulkMint(): Promise<BulkMintNft> {
    const results: MintTransactionInfo[] = [];
    const maxMembershipBulkCount = Number(process.env.MAX_CHUNKED_MEMBERSHIP_TX_COUNT ?? 10);
    const maxRewardBulkCount = Number(process.env.MAX_CHUNKED_REWARD_TX_COUNT ?? 10);

    // Queueから処理待ちのものを取得
    const pendingQueues = await this.transactionQueuesRepository.selectPendingQueues();
    logger.info({
      message: 'Start processing pending transaction queues',
      data: { function: 'handleBulkMint', pendingQueues },
    });
    if (pendingQueues.length === 0) {
      return { mintTransactions: [] };
    }

    // Queueをチャンクに分割
    const groupedMap = pendingQueues.reduce((map, queue) => {
      const isMembership = queue.nft_type === NftType.MEMBERSHIP;
      const key = `${queue.service_id}_${isMembership}`;

      const entry = map.get(key) ?? {
        serviceId: queue.service_id,
        isMembership,
        queues: [],
      };

      entry.queues.push(queue);
      map.set(key, entry);

      return map;
    }, new Map<string, { serviceId: string; isMembership: boolean; queues: TransactionQueuesEntity[] }>());

    for (const { serviceId, isMembership, queues } of groupedMap.values()) {
      const maxCount = isMembership ? maxMembershipBulkCount : maxRewardBulkCount;
      const chunkedQueues = chunkArray(queues, maxCount);

      for (const chunkQueues of chunkedQueues) {
        const queueIds = chunkQueues.map((q) => q.queue_id);
        await this.transactionQueuesRepository.updateQueueStatus(
          pendingQueues.map((queue) => queue.queue_id),
          TransactionQueueStatus.PROCESSING,
        );

        const { transactionId, txHash, nonce, isFailRevert } = await this.bulkMint(
          serviceId,
          isMembership,
          chunkQueues,
        );

        if (isFailRevert) {
          await this.transactionQueuesRepository.updateQueueStatus(queueIds, TransactionQueueStatus.PENDING);
        }

        results.push({ transactionId, txHash, nonce, queueIds });
      }
    }

    return { mintTransactions: results };
  }

  public async bulkMint(
    serviceId: string,
    isMembership: boolean,
    chunkQueues: TransactionQueuesTable[],
  ): Promise<{
    transactionId: string;
    txHash: string;
    nonce: number;
    isFailRevert: boolean;
  }> {
    return await db.transaction().execute(async (trx) => {
      const queueIds = chunkQueues.map((queue) => queue.queue_id);

      const serviceInfo = await this.serviceInfoRepository.getServiceById(serviceId);
      if (!serviceInfo) throw new ValidationError('Service not found');
      const modularContractId = serviceInfo.modular_contract_id;
      const tenantId = serviceInfo.tenant_id;

      const {
        abi: modularAbi,
        contractAddress: modularContractAddress,
        implementAddress,
      } = await this.nftMintService.getNftContractAbiAndAddress(modularContractId);
      logger.info({ transaction: 'handleBulkMint', contract: modularContractAddress, implement: implementAddress });

      if (isMembership) {
        return await this.processMembershipBulkMint(
          serviceInfo,
          queueIds,
          modularAbi,
          modularContractAddress,
          tenantId,
          chunkQueues,
          trx,
        );
      } else {
        return await this.processRewardBulkMint(
          serviceInfo,
          queueIds,
          modularAbi,
          modularContractAddress,
          tenantId,
          chunkQueues,
          trx,
        );
      }
    });
  }

  private async processMembershipBulkMint(
    serviceInfo: { service_id?: string; id?: string; membership_nft_contract_id?: string },
    queueIds: string[],
    modularAbi: ethers.InterfaceAbi,
    modularContractAddress: string,
    tenantId: string,
    chunkQueues: TransactionQueuesTable[],
    trx: Transaction<Database>,
  ) {
    const membershipNftContractId = serviceInfo.membership_nft_contract_id;
    if (!membershipNftContractId) {
      throw new NotFoundError('Membership Nft is not registered');
    }
    const nftId = await this.nftBaseMetadatasRepository.selectNftIdFromContractId(membershipNftContractId);
    if (!nftId) {
      throw new NotFoundError('Membership Nft is not registered');
    }

    const serviceId = serviceInfo.service_id || serviceInfo.id;
    if (!serviceId) {
      throw new ValidationError('Service ID not found');
    }

    const { encodedData, tbaDatas, requestId } = await this.createMembershipEncodedData(
      serviceId,
      membershipNftContractId,
      modularAbi,
      chunkQueues,
      trx,
    );
    logger.info({ transaction: 'handleBulkMint', encodedData, tbaDatas });

    const createdTx = await this.createTransaction(
      tenantId,
      serviceId,
      queueIds,
      modularContractAddress,
      encodedData,
      requestId,
      trx,
    );

    if (!createdTx) {
      return { transactionId: '', txHash: '', nonce: 0, isFailRevert: true };
    }

    if (tbaDatas.length > 0) {
      await this.processTbaUpdates(tbaDatas, tenantId, serviceId, nftId, trx);
    }

    return {
      transactionId: createdTx.transactionId,
      txHash: createdTx.txHash,
      nonce: createdTx.nonce,
      isFailRevert: false,
    };
  }

  private async processRewardBulkMint(
    serviceInfo: { service_id?: string; id?: string },
    queueIds: string[],
    modularAbi: ethers.InterfaceAbi,
    modularContractAddress: string,
    tenantId: string,
    chunkQueues: TransactionQueuesTable[],
    trx: Transaction<Database>,
  ) {
    const { mintData: encodedData, requestId } = await this.createRewardEncodedData(
      modularAbi,
      chunkQueues.map((queue) => ({
        toAddress: queue.to_address,
        nftContractAddress: queue.nft_contract_address,
        nftType: queue.nft_type,
        tokenId: queue.token_id,
      })),
    );

    const serviceId = serviceInfo.service_id || serviceInfo.id;
    if (!serviceId) {
      throw new ValidationError('Service ID not found');
    }

    const createdTx = await this.createTransaction(
      tenantId,
      serviceId,
      queueIds,
      modularContractAddress,
      encodedData,
      requestId,
      trx,
    );

    if (!createdTx) {
      logger.error({ message: 'TXの発行に失敗しました', data: { func: 'handleBulkMint', createdTx } });
      return { transactionId: '', txHash: '', nonce: 0, isFailRevert: true };
    }

    await this.insertMetadataForQueues(chunkQueues, trx);

    if (queueIds.length > 0) {
      await this.updateRewardNftDelivery(chunkQueues, createdTx.txHash, trx);
    }

    return {
      transactionId: createdTx.transactionId,
      txHash: createdTx.txHash,
      nonce: createdTx.nonce,
      isFailRevert: false,
    };
  }

  private async processTbaUpdates(
    tbaDatas: TbaData[],
    tenantId: string,
    serviceId: string,
    nftId: string,
    trx: Transaction<Database>,
  ) {
    const updateResults = await handleSettledResults(
      tbaDatas,
      (tbaData) => tbaData.queueId,
      async (tbaData) => {
        const tbaAccountAddress = (await this.transactionService.callContractViewFunction({
          tenantId,
          contractAddress: tbaData.contractAddress,
          contractABI: tbaData.contractABI,
          functionName: 'account',
          args: tbaData.args,
        })) as string;

        await this.accountRepository.updateAccountTBA(
          trx,
          tbaData.queueId,
          serviceId,
          tbaAccountAddress,
          tbaData.tokenId,
          `${config.metadataUrl}/${nftId}/${tbaData.tokenId}`,
        );

        await this.transactionQueuesRepository.updateQueueTokenId(tbaData.queueId, tbaData.tokenId, trx);
      },
    );

    updateResults.forEach((result) => {
      if (!result.result) {
        logger.warn({
          message: 'TBA更新処理失敗',
          data: { queueId: result.id, serviceId, tenantId, error: result.message },
        });
      }
    });
  }

  private async insertMetadataForQueues(chunkQueues: TransactionQueuesTable[], trx: Transaction<Database>) {
    await Promise.all(
      chunkQueues.map(async (queue) => {
        return this.metadataService
          .insertMetadata(queue.nft_type, queue.nft_contract_address, queue.token_id ?? 0, queue.queue_id, trx)
          .catch((error) => {
            logger.error(error);
          });
      }),
    );
  }

  private async updateRewardNftDelivery(
    chunkQueues: TransactionQueuesTable[],
    txHash: string,
    trx: Transaction<Database>,
  ) {
    const deleteResults = await handleSettledResults(
      chunkQueues,
      (queue) => queue.queue_id,
      async (queue) => {
        await this.deliveryNftsFirestoreRepository.updateDeliveryNftTxHash(queue.queue_id, txHash);

        if (queue.token_id === undefined) {
          throw new InternalServerError(`Token ID is undefined for queue_id: ${queue.queue_id}`);
        }

        await this.metadataService.insertMetadata(
          queue.nft_type,
          queue.nft_contract_address,
          queue.token_id ?? 0,
          queue.queue_id,
          trx,
        );
      },
    );

    deleteResults.forEach((result) => {
      if (!result.result) {
        logger.error({
          message: 'metadataの更新に失敗しました',
          data: { queueId: result.id, txHash, error: result.message },
        });
      }
    });
  }

  private async createTransaction(
    tenantId: string,
    serviceId: string,
    queueIds: string[],
    modularContractAddress: string,
    encodedData: string,
    requestId: string,
    trx: Transaction<Database>,
  ): Promise<
    | {
        transactionId: string;
        txHash: string;
        nonce: number;
      }
    | undefined
  > {
    const vaultKey = await this.vaultKeyRepository.getVaultKeyIdWithLock(tenantId, trx);
    if (!vaultKey) {
      throw new NotFoundError(`No vault_keys record found for tenantId = ${tenantId}`);
    }
    const nonce = vaultKey.nonce;
    const vaultKeyId = vaultKey.vault_key_id;

    try {
      const { transactionRequest, signer, fallbackSigner } = await this.transactionService.createTransactionRequest(
        tenantId,
        modularContractAddress,
        encodedData,
        nonce,
      );

      if (!transactionRequest) {
        throw new InternalServerError('transaction request is null.');
      }

      await this.validateTransactionRequest(transactionRequest, signer, fallbackSigner, requestId);

      logger.info({
        message: 'Transaction request validated, proceeding to sign',
        data: { requestId, modularContractAddress },
      });

      const signedTransaction = await this.transactionService.signTransaction(
        transactionRequest,
        signer,
        fallbackSigner,
      );

      const txHash = await this.transactionService.calculateTxHash(signedTransaction);
      if (!txHash) {
        throw new InternalServerError('calculate transaction hash is null.');
      }

      const { transactionId } = await this.transactionService.createTransactionDatabases(
        serviceId,
        encodedData,
        nonce,
        txHash,
        queueIds,
        trx,
      );

      const txResponse = await this.transactionService.sendTransactionFromSignedTransaction(signedTransaction);
      if (!txResponse) {
        throw new InternalServerError('sendTransactionFromSignedTransaction result is null.');
      }

      if (txResponse.hash !== txHash) {
        logger.warn({ message: 'calc hash is wrong.', data: { txHash, txResponse } });
        await this.transactionsRepository.updateTransactionHash(transactionId, txResponse.hash);
      }

      logger.info({ transaction: 'handleBulkMint', txHash, tenantId, modularContractAddress, nonce });

      const nextNonce = nonce + 1;
      await this.vaultKeyRepository.updateNonce(vaultKeyId, nextNonce, trx);
      return { transactionId, txHash, nonce };
    } catch (error) {
      logger.error(error);
      throw error;
    }
  }

  private async validateTransactionRequest(
    transactionRequest: ethers.TransactionRequest,
    signer: GcpKmsSigner,
    fallbackSigner: GcpKmsSigner,
    requestId: string,
  ) {
    const result = await this.transactionService.ethCallFromTransactionRequest(
      transactionRequest,
      signer,
      fallbackSigner,
    );
    const abiCoder = ethers.AbiCoder.defaultAbiCoder();
    const decoded = abiCoder.decode(['string'], result);

    if (requestId != decoded[0]) {
      throw new InternalServerError(`eth_call error. ${result}`);
    }
  }

  private async createMembershipEncodedData(
    serviceId: string,
    membershipNftContractId: string,
    modularAbi: ethers.InterfaceAbi,
    transactionQueues: TransactionQueuesTable[],
    trx: Transaction<Database>,
  ): Promise<{
    encodedData: string;
    tbaDatas: TbaData[];
    requestId: string;
  }> {
    logger.info({ transaction: 'membershipBulkMint', membershipNftContractId, transactionQueues });
    const tbaRegistory = await this.tokenBoundAccountRegistryAddressRepository.selectAddressByServiceId(serviceId, trx);
    if (!tbaRegistory) {
      throw new NotFoundError('tbaRegistory is undefined');
    }
    const tbaImplementation = await this.tokenBoundAccountImplementationRepository.selectByServiceId(serviceId, trx);
    if (!tbaImplementation) {
      throw new NotFoundError('tbaImplementation is undefined');
    }
    const nftContract = await this.nftContractsRepository.getNftContractsByIdWithLock(membershipNftContractId, trx);
    if (!nftContract || !nftContract.nft_contract_address) {
      throw new NotFoundError(`No nft_contracts record found for nftContractId = ${membershipNftContractId}`);
    }

    let { next_token_id: nextTokenId } = nftContract;
    if (nextTokenId === null || nextTokenId === undefined) {
      throw new NotFoundError(`nextTokenId is null for nftContractId = ${membershipNftContractId}`);
    }

    const bulkDatas: MembershipBulkData[] = [];
    const tbaDatas: TbaData[] = [];
    for (const queue of transactionQueues) {
      // tba + mint data.
      if (queue.tx_type == TxType.MINT_MEMBERSHIP) {
        if (queue.token_id == undefined) {
          logger.warn({
            message: 'generateBulkMintData has invalid deploy contract record. It is skipped',
            data: { queue },
          });
          continue;
        }

        const bulkData: MembershipBulkData = {
          contractAddress: nftContract.nft_contract_address,
          tokenId: queue.token_id,
          toAddress: queue.to_address,
          tbaRegistoryAddress: tbaRegistory.token_bound_account_registry_address,
          tokenBoundAccountImplementationAddress: tbaImplementation.token_bound_account_implementation_address,
          salt: tbaRegistory.salt,
          chainId: tbaRegistory.chain_id,
        };

        const tbaData = {
          queueId: queue.queue_id,
          tokenId: queue.token_id,
          contractAddress: tbaRegistory.token_bound_account_registry_address,
          contractABI: tbaRegistory.abi,
          args: [
            bulkData.tokenBoundAccountImplementationAddress,
            bulkData.salt,
            bulkData.chainId,
            bulkData.contractAddress,
            queue.token_id,
          ],
        };

        bulkDatas.push(bulkData);
        tbaDatas.push(tbaData);
      }
    }

    const requestId = uuidv4();
    const contractInterface = new ethers.Interface(modularAbi);
    const encodedData = contractInterface.encodeFunctionData('bulkCreateAccount', [requestId, bulkDatas]);
    logger.info({ message: 'Create Bulk Membership Data', data: { encodedData, modularAbi, bulkDatas } });

    return { encodedData, tbaDatas, requestId };
  }

  private async createRewardEncodedData(
    abi: ethers.InterfaceAbi,
    queueDatas: {
      toAddress: string;
      nftContractAddress: string;
      nftType: NftType;
      tokenId?: number;
    }[],
  ): Promise<{ mintData: string; requestId: string }> {
    const requestId = uuidv4();
    const contractInterface = new ethers.Interface(abi);

    const bulkDatas = queueDatas.map((queue) => {
      if (queue.nftType == NftType.COUPON) {
        if (queue.tokenId == undefined) {
          logger.warn({ message: 'generateBulkMintData has invalid coupon record. It is skipped', bulkDatas });
          return null;
        }
        return {
          contractAddress: queue.nftContractAddress,
          tokenId: queue.tokenId,
          amount: 1,
          toAddress: queue.toAddress,
          functionName: this.getFunctionName(queue.nftType),
        };
      } else {
        return {
          contractAddress: queue.nftContractAddress,
          tokenId: 0,
          amount: 1,
          toAddress: queue.toAddress,
          functionName: this.getFunctionName(queue.nftType),
        };
      }
    });

    const mintData = contractInterface.encodeFunctionData('bulkMint', [requestId, bulkDatas]);
    return { mintData, requestId };
  }

  private getFunctionName(nftType: NftType): string {
    switch (nftType) {
      case NftType.COUPON:
        return 'additinalMint'; //typoだが現状これが正しい
      case NftType.CERTIFICATE:
      case NftType.CONTENT:
      case NftType.MEMBERSHIP:
        return 'safeMint';
      default:
        throw new InternalServerError('Invalid reward type for minting');
    }
  }

  public async updateAccountTBAFromId(
    accountTokenIds: {
      tokenId: number;
      accountId: string;
      contractAccountAddress: string;
      membershipContractAddress: string;
    }[],
    serviceId: string,
    tenantId: string,
    trx: Transaction<Database>,
  ) {
    const tbaRegistory = await this.tokenBoundAccountRegistryAddressRepository.selectAddressByServiceId(serviceId, trx);
    if (!tbaRegistory) {
      throw new InternalServerError('tbaRegistory is undefined');
    }
    const tbaImplementation = await this.tokenBoundAccountImplementationRepository.selectByServiceId(serviceId, trx);
    if (!tbaImplementation) {
      throw new NotFoundError('tbaImplementation is undefined');
    }

    const service = await this.serviceInfoRepository.getServiceById(serviceId);
    if (!service) {
      throw new NotFoundError('Service not found');
    }
    const contractId = service.membership_nft_contract_id;
    if (!contractId) {
      throw new NotFoundError('Membership Nft is not registered');
    }
    const nftId = await this.nftBaseMetadatasRepository.selectNftIdFromContractId(contractId);

    const updateResults = await handleSettledResults(
      accountTokenIds,
      (accountTokenId) => accountTokenId.tokenId.toString(),
      async (accountTokenId) => {
        const tbaAccountAddress = (await this.transactionService.callContractViewFunction({
          tenantId,
          contractAddress: tbaRegistory.token_bound_account_registry_address,
          contractABI: tbaRegistory.abi,
          functionName: 'account',
          args: [
            tbaImplementation.token_bound_account_implementation_address,
            tbaRegistory.salt,
            tbaRegistory.chain_id,
            accountTokenId.membershipContractAddress,
            accountTokenId.tokenId,
          ],
        })) as string;
        await this.recreateTba(serviceId, accountTokenId.contractAccountAddress, accountTokenId.tokenId);

        return this.accountRepository.updateAccountTBAFromId(
          trx,
          accountTokenId.accountId,
          serviceId,
          tbaAccountAddress,
          accountTokenId.tokenId,
          `${config.metadataUrl}/${nftId}/${accountTokenId.tokenId}`,
        );
      },
    );

    // 結果ログ出力
    updateResults.forEach((result) => {
      if (!result.result) {
        logger.warn({
          message: 'TBA更新処理失敗',
          data: { queueId: result.id, serviceId, tenantId, error: result.message },
        });
      }
    });
  }

  async recreateTba(serviceId: string, contractAccountAddress: string, tbaTokenId: number) {
    // 事前のデータ取得と検証
    const service = await this.serviceInfoRepository.getServiceById(serviceId);
    if (!service || !service.membership_nft_contract_id) {
      throw new NotFoundError('Service not found or membership NFT contract ID is missing');
    }
    const membershipNftContractId = service.membership_nft_contract_id;
    const tenantId = service.tenant_id;

    const tbaRegistory = await this.tokenBoundAccountRegistryAddressRepository.selectAddressByServiceId(serviceId);
    if (!tbaRegistory) {
      throw new NotFoundError('tbaRegistory is undefined');
    }
    const registoryAbi = tbaRegistory.abi;
    const contractInterface = new ethers.Interface(registoryAbi as unknown as ethers.InterfaceAbi);
    const registoryAddress = tbaRegistory.token_bound_account_registry_address;

    const tbaImplementation = await this.tokenBoundAccountImplementationRepository.selectByServiceId(serviceId);
    if (!tbaImplementation) {
      throw new NotFoundError('tbaImplementation is undefined');
    }

    const tbaQueueId = uuidv4();
    await db.transaction().execute(async (trx: Transaction<Database>) => {
      // トランザクションのロックを取得
      const vaultKey = await this.vaultKeyRepository.getVaultKeyIdWithLock(tenantId, trx);
      if (!vaultKey) {
        throw new NotFoundError(`No vault_keys record found for tenantId = ${tenantId}`);
      }
      const nonce = vaultKey.nonce;
      const nftContract = await this.nftContractsRepository.getNftContractsByIdWithLock(membershipNftContractId, trx);
      if (!nftContract || !nftContract.nft_contract_address) {
        throw new NotFoundError(`No nft_contracts record found for nftContractId = ${membershipNftContractId}`);
      }
      const nftContractAddress = nftContract.nft_contract_address;

      // TXに必要なデータをエンコード
      const tbaContractArgs: (string | number)[] = [
        tbaImplementation.token_bound_account_implementation_address,
        tbaRegistory.salt,
        tbaRegistory.chain_id,
        nftContractAddress,
        tbaTokenId,
      ];
      const tbaCallData = contractInterface.encodeFunctionData('createAccount', tbaContractArgs);

      const { transactionRequest, signer, fallbackSigner } = await this.transactionService.createTransactionRequest(
        tenantId,
        registoryAddress,
        tbaCallData,
        nonce,
      );
      if (!transactionRequest) {
        throw new InternalServerError('transaction request is null.');
      }
      const signedTransaction = await this.transactionService.signTransaction(
        transactionRequest,
        signer,
        fallbackSigner,
      );
      const txHash = await this.transactionService.calculateTxHash(signedTransaction);
      if (!txHash) {
        throw new InternalServerError('calculate transaction hash is null.');
      }
      logger.info({ transaction: 'recreateTba', txHash, tenantId, registoryAddress, nonce, tbaCallData });

      // トランザクションキューの挿入
      await this.transactionQueuesRepository.insertQueue({
        queue_id: tbaQueueId,
        service_id: serviceId,
        from_address: vaultKey.vault_wallet_address,
        to_address: contractAccountAddress,
        status: TransactionQueueStatus.PROCESSING,
        tx_type: TxType.DEPLOY_CONTRACT,
        nft_type: NftType.MEMBERSHIP,
        nft_contract_address: nftContractAddress,
        token_id: tbaTokenId, // linked membership nft token id
        created_date: new Date(),
      });

      // トランザクションの挿入
      const { transactionId } = await this.transactionService.createTransactionDatabases(
        serviceId,
        tbaCallData,
        nonce,
        txHash,
        [tbaQueueId],
      );

      // send tx.
      const txResponse = await this.transactionService.sendTransactionFromSignedTransaction(signedTransaction);
      if (!txResponse) {
        throw new InternalServerError('sendTransactionFromSignedTransaction result is null.');
      }

      if (txResponse.hash !== txHash) {
        logger.error({ message: 'calc hash is wrong.', txHash, txResponse });

        this.transactionsRepository.updateTransactionHash(transactionId, txResponse.hash);
      }

      const nextNonce = nonce + 1;
      await this.vaultKeyRepository.updateNonce(vaultKey.vault_key_id, nextNonce, trx);
    });

    return {
      membershipId: tbaTokenId,
      tbaQueueId: tbaQueueId,
    };
  }
}
