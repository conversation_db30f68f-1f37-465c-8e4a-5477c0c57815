import { ethers, InterfaceAbi } from 'ethers';
import { inject, injectable } from 'tsyringe';
import { InternalServerError } from '../errors/internalServerError';
import { NotFoundError } from '../errors/notFoundError';
import { NftContractsRepository } from '../repositories/nftContractsRepository';
import { VaultKeyRepository } from '../repositories/vaultKeyRepository';
import { TransactionQueuesRepository } from '../repositories/transactionQueuesRepository';
import { TransactionsRepository } from '../repositories/transactionsRepository';
import { TokenBoundAccountImplementationRepository } from '../repositories/tokenBoundAccountImplementationsRepository';
import { TokenBoundAccountRegistryAddressRepository } from '../repositories/tokenBoundAccountRegistryAddressRepository';
import { AccountRepository } from '../repositories/accountRepository';
import { TransactionService } from './transactionService';
import { NftMintService } from './nftMintService';
import { v4 as uuidv4 } from 'uuid';
import { NftType } from '../enum/nftType';
import { ValidationError } from '../errors/validationError';
import { ServiceInfoRepository } from '../repositories/serviceInfoRepository';
import { TxType } from '../enum/txType';
import { logger } from '../utils/logger';
import { db, Database } from '../db/database';
import { TransactionQueueStatus } from '../enum/transactionQueueStatus';
import { Transaction } from 'kysely';
import { TransactionQueuesTable } from '../tables/transactionQueuesTable';
import { GcpKmsSigner } from '@cuonghx.gu-tech/ethers-gcp-kms-signer';
import { DeliveryNftsFirestoreRepository } from '../repositories/deliveryNftsFirestoreRepository';
import { BulkMintNft, MintTransactionInfo } from '../dtos/nfts/schemas';
import { config } from '../configs/config';
import { NftBaseMetadatasRepository } from '../repositories/nftBaseMetadatasRepository';

interface TbaData {
  queueId: string;
  tokenId: number;
  contractAddress: string;
  contractABI: object;
  args: any[];
}

@injectable()
export class BulkMintService {
  constructor(
    @inject('TransactionService') private transactionService: TransactionService,
    @inject('NftMintService') private nftMintService: NftMintService,
    @inject('NftContractsRepository') private nftContractsRepository: NftContractsRepository,
    @inject('TransactionQueuesRepository') private transactionQueuesRepository: TransactionQueuesRepository,
    @inject('TransactionsRepository') private transactionsRepository: TransactionsRepository,
    @inject('VaultKeyRepository') private vaultKeyRepository: VaultKeyRepository,
    @inject('ServiceInfoRepository') private serviceInfoRepository: ServiceInfoRepository,
    @inject('TokenBoundAccountImplementationRepository')
    private tokenBoundAccountImplementationRepository: TokenBoundAccountImplementationRepository,
    @inject('TokenBoundAccountRegistryAddressRepository')
    private tokenBoundAccountRegistryAddressRepository: TokenBoundAccountRegistryAddressRepository,
    @inject('AccountRepository') private accountRepository: AccountRepository,
    @inject('DeliveryNftsFirestoreRepository') private deliveryNftsFirestoreRepository: DeliveryNftsFirestoreRepository,
    @inject('NftBaseMetadatasRepository') private nftBaseMetadatasRepository: NftBaseMetadatasRepository,
  ) {}

  public async handleBulkMint(): Promise<BulkMintNft> {
    const pendingQueues = await this.transactionQueuesRepository.selectPendingQueues();
    if (pendingQueues.length === 0) {
      return { mintTransactions: [] };
    }
    await this.transactionQueuesRepository.updateQueueStatus(
      pendingQueues.map((queue) => queue.queue_id),
      TransactionQueueStatus.PROCESSING,
    );

    const bulkDatas = this.transactionQueuesRepository.groupByBulkData(pendingQueues);

    const results: MintTransactionInfo[] = [];

    const maxMembershipBulkCount = Number(process.env.MAX_CHUNKED_MEMBERSHIP_TX_COUNT ?? 10);
    const maxRewardBulkCount = Number(process.env.MAX_CHUNKED_REWARD_TX_COUNT ?? 10);

    for (const { serviceId, isMembership, transactionQueues } of bulkDatas) {
      const maxCount = isMembership ? maxMembershipBulkCount : maxRewardBulkCount;
      for (let i = 0; i < transactionQueues.length; i += maxCount) {
        // chunked.
        const chunkQueues = transactionQueues.slice(i, i + maxCount);
        const queueIds = chunkQueues.map((queue) => queue.queue_id);

        const { transactionId, txHash, nonce, isFailRevert } = await this.bulkMint(
          serviceId,
          isMembership,
          chunkQueues,
        );
        if (isFailRevert) {
          // revert.
          await this.transactionQueuesRepository.updateQueueStatus(queueIds, TransactionQueueStatus.PENDING);
        }

        results.push({
          transactionId: transactionId,
          txHash: txHash,
          nonce: nonce,
          queueIds: queueIds,
        });
      }
    }
    return { mintTransactions: results };
  }

  public async bulkMint(
    serviceId: string,
    isMembership: boolean,
    chunkQueues: TransactionQueuesTable[],
  ): Promise<{
    transactionId: string;
    txHash: string;
    nonce: number;
    isFailRevert: boolean;
  }> {
    return await db.transaction().execute(async (trx) => {
      let tenantId = '';
      let modularContractId = '';
      let vaultKeyId = '';
      let nonce = 0;
      let tbaDatas: TbaData[] = [];
      let encodedData = '';
      let transactionRequest;
      let signedTransaction;
      let signer: GcpKmsSigner;
      let fallbackSigner: GcpKmsSigner;
      let txHash = '';
      let requestId = '';
      const queueIds = chunkQueues.map((queue) => queue.queue_id);
      try {
        const serviceInfo = await this.serviceInfoRepository.getServiceById(serviceId);
        if (!serviceInfo) throw new ValidationError('Service not found');
        const membershipNftContractId = serviceInfo.membership_nft_contract_id;
        if (!membershipNftContractId) {
          throw new NotFoundError('Membership Nft is not registered');
        }
        ({ tenant_id: tenantId, modular_contract_id: modularContractId } = serviceInfo);

        const {
          abi: modularAbi,
          contractAddress: modularContractAddress,
          implementAddress,
        } = await this.nftMintService.getNftContractAbiAndAddress(modularContractId);
        logger.info({ transaction: 'handleBulkMint', contract: modularContractAddress, implement: implementAddress });

        const vaultKey = await this.vaultKeyRepository.getVaultKeyIdWithLock(tenantId, trx);
        if (!vaultKey) {
          throw new NotFoundError(`No vault_keys record found for tenantId = ${tenantId}`);
        }
        ({ nonce, vault_key_id: vaultKeyId } = vaultKey);

        // insert delivery.
        if (!isMembership) {
          logger.info({
            message: 'insert delivery nft.',
            body: {
              func: 'handleBulkMint',
            },
          });
        }

        ({ encodedData, tbaDatas, requestId } = await this.generateEncodedData(
          isMembership,
          serviceId,
          membershipNftContractId,
          modularAbi,
          chunkQueues,
          trx,
        ));
        logger.info({ transaction: 'handleBulkMint', encodedData, tbaDatas });

        // update tba.
        if (isMembership) {
          const nftId = await this.nftBaseMetadatasRepository.selectNftIdFromContractId(membershipNftContractId);
          if(!nftId) {
            throw new NotFoundError('Membership Nft is not registered');
          }

          await Promise.all(
            tbaDatas.flatMap(async (tbaData) => {
              const tbaAccountAddress = await this.transactionService.callContractViewFunction({
                tenantId,
                contractAddress: tbaData.contractAddress,
                contractABI: tbaData.contractABI,
                functionName: 'account',
                args: tbaData.args,
              });
              return [
                this.accountRepository.updateAccountTBA(
                  trx,
                  tbaData.queueId,
                  serviceId,
                  tbaAccountAddress,
                  tbaData.tokenId,
                  `${config.metadataUrl}/${nftId}/${tbaData.tokenId}`,
                ),
                this.transactionQueuesRepository.updateQueueTokenId(tbaData.queueId, tbaData.tokenId, trx),
              ];
            }),
          );
          logger.info({ transaction: 'handleBulkMint', msg: 'update tba.' });
        }

        ({ transactionRequest, signer, fallbackSigner } = await this.transactionService.createTransactionRequest(
          tenantId,
          modularContractAddress,
          encodedData,
          nonce,
        ));
        if (!transactionRequest) {
          throw new InternalServerError('transaction request is null.');
        }
        // eth_call
        const result = await this.transactionService.ethCallFromTransactionRequest(
          transactionRequest,
          signer,
          fallbackSigner,
        );
        const abiCoder = ethers.AbiCoder.defaultAbiCoder();
        const decoded = abiCoder.decode(['string'], result);
        logger.info({ transaction: 'ethCall', result, requestId, decoded: decoded.map((_) => _.toString()) });
        if (requestId != decoded[0]) {
          throw new InternalServerError(`eth_call error. ${result}`);
        }
        signedTransaction = await this.transactionService.signTransaction(transactionRequest, signer, fallbackSigner);
        // calc hash.
        txHash = await this.transactionService.calculateTxHash(signedTransaction);
        if (!txHash) {
          throw new InternalServerError('calculate transaction hash is null.');
        }
        logger.info({ transaction: 'handleBulkMint', txHash, tenantId, modularContractAddress, nonce, encodedData });
      } catch (error) {
        logger.error(error);
        return { transactionId: '', txHash: txHash, nonce: nonce, isFailRevert: true };
      }

      // insert queue.
      const { transactionId } = await this.transactionService.createTransactionDatabases(
        serviceId,
        encodedData,
        nonce,
        txHash,
        queueIds,
        trx,
      );

      // send tx.
      const txResponse = await this.transactionService.sendTransactionFromSignedTransaction(signedTransaction);
      if (!txResponse) {
        throw new InternalServerError('sendTransactionFromSignedTransaction result is null.');
      }

      if (txResponse.hash !== txHash) {
        logger.error({ msg: 'calc hash is wrong.', txHash, txResponse });

        this.transactionsRepository.updateTransactionHash(transactionId, txResponse.hash);
      }

      const nextNonce = nonce + 1;
      await this.vaultKeyRepository.updateNonce(vaultKeyId, nextNonce, trx);

      // delivery update hash.
      if (queueIds.length > 0) {
        await Promise.all(
          queueIds.map(async (queueId) => {
            return queueId
              ? this.deliveryNftsFirestoreRepository
                  .updateDeliveryNftTxHash(queueId, txHash)
                  .then((writeResult) => {
                    logger.info({ transaction: 'mint', updateDeliveryNft: writeResult });
                  })
              : undefined;
          }),
        );
      }

      return { transactionId: transactionId, txHash: txHash, nonce: nonce, isFailRevert: false };
    });
  }

  public async updateAccountTBAFromId(
    accountTokenIds: {
      tokenId: number;
      accountId: string;
      contractAccountAddress: string;
      membershipContractAddress: string;
    }[],
    serviceId: string,
    tenantId: string,
    trx: Transaction<Database>,
  ) {
    const tbaRegistory = await this.tokenBoundAccountRegistryAddressRepository.selectAddressByServiceId(serviceId, trx);
    if (!tbaRegistory) {
      throw new InternalServerError('tbaRegistory is undefined');
    }
    const tbaImplementation = await this.tokenBoundAccountImplementationRepository.selectByServiceId(serviceId, trx);
    if (!tbaImplementation) {
      throw new NotFoundError('tbaImplementation is undefined');
    }

    const service = await this.serviceInfoRepository.getServiceById(serviceId);
    if (!service) {
      throw new NotFoundError('Service not found');
    }
    const contractId = service.membership_nft_contract_id;
    if (!contractId) {
      throw new NotFoundError('Membership Nft is not registered');
    }
    const nftId = await this.nftBaseMetadatasRepository.selectNftIdFromContractId(contractId);

    await Promise.all(
      accountTokenIds.map(async (map) => {
        const tbaAccountAddress = await this.transactionService.callContractViewFunction({
          tenantId,
          contractAddress: tbaRegistory.token_bound_account_registry_address,
          contractABI: tbaRegistory.abi,
          functionName: 'account',
          args: [
            tbaImplementation.token_bound_account_implementation_address,
            tbaRegistory.salt,
            tbaRegistory.chain_id,
            map.membershipContractAddress,
            map.tokenId,
          ],
        });

        return this.accountRepository.updateAccountTBAFromId(
          trx,
          map.accountId,
          serviceId,
          tbaAccountAddress,
          map.tokenId,
          `${config.metadataUrl}/${nftId}/${map.tokenId}`,
        );
      }),
    );

    accountTokenIds.forEach(async (map) => {
      await this.recreateTba(serviceId, map.contractAccountAddress, map.tokenId);
    });
  }

  private async generateEncodedData(
    isMembership: boolean,
    serviceId: string,
    membershipNftContractId: string,
    modularAbi: InterfaceAbi,
    chunkQueues: TransactionQueuesTable[],
    trx: Transaction<Database>,
  ): Promise<{
    encodedData: string;
    tbaDatas: TbaData[];
    requestId: string;
  }> {
    let encodedData = '';
    let tbaDatas: TbaData[] = [];
    let requestId: string = '';
    if (isMembership) {
      ({ encodedData, tbaDatas, requestId } = await this.membershipBulkMint(
        serviceId,
        membershipNftContractId,
        modularAbi,
        chunkQueues,
        trx,
      ));
    } else {
      ({ mintData: encodedData, requestId } = await this.generateBulkMintData(
        modularAbi,
        chunkQueues.map((queue) => {
          return {
            toAddress: queue.to_address,
            nftContractAddress: queue.nft_contract_address,
            nftType: queue.nft_type,
            tokenId: queue.token_id,
          };
        }),
      ));
    }
    return { encodedData, tbaDatas, requestId };
  }

  private async membershipBulkMint(
    serviceId: string,
    membershipNftContractId: string,
    modularAbi: ethers.InterfaceAbi,
    transactionQueues: TransactionQueuesTable[],
    trx: Transaction<Database>,
  ): Promise<{
    encodedData: string;
    tbaDatas: TbaData[];
    requestId: string;
  }> {
    logger.info({ transaction: 'membershipBulkMint', membershipNftContractId, transactionQueues });
    const tbaRegistory = await this.tokenBoundAccountRegistryAddressRepository.selectAddressByServiceId(serviceId, trx);
    if (!tbaRegistory) {
      throw new NotFoundError('tbaRegistory is undefined');
    }
    const tbaImplementation = await this.tokenBoundAccountImplementationRepository.selectByServiceId(serviceId, trx);
    if (!tbaImplementation) {
      throw new NotFoundError('tbaImplementation is undefined');
    }
    const nftContract = await this.nftContractsRepository.getNftContractsByIdWithLock(membershipNftContractId, trx);
    if (!nftContract) {
      throw new NotFoundError(`No nft_contracts record found for nftContractId = ${membershipNftContractId}`);
    }

    let { next_token_id: nextTokenId } = nftContract;
    if (nextTokenId === null || nextTokenId === undefined) {
      throw new NotFoundError(`nextTokenId is null for nftContractId = ${membershipNftContractId}`);
    }

    const bulkDatas: any[] = [];
    const tbaDatas: TbaData[] = [];
    for (const queue of transactionQueues) {
      // tba + mint data.
      if (queue.tx_type == TxType.DEPLOY_CONTRACT) {
        const tokenId = nextTokenId;
        nextTokenId = nextTokenId + 1;
        const bulkData = {
          contractAddress: nftContract.nft_contract_address,
          tokenId: tokenId,
          toAddress: queue.to_address,
          tbaRegistoryAddress: tbaRegistory.token_bound_account_registry_address,
          tokenBoundAccountImplementationAddress: tbaImplementation.token_bound_account_implementation_address,
          salt: tbaRegistory.salt,
          chainId: tbaRegistory.chain_id,
        };
        bulkDatas.push(bulkData);
        tbaDatas.push({
          queueId: queue.queue_id,
          tokenId: tokenId,
          contractAddress: tbaRegistory.token_bound_account_registry_address,
          contractABI: tbaRegistory.abi,
          args: [
            bulkData.tokenBoundAccountImplementationAddress,
            bulkData.salt,
            bulkData.chainId,
            bulkData.contractAddress,
            tokenId,
          ],
        });
      }
    }

    await this.nftContractsRepository.updateNextTokenId(membershipNftContractId, nextTokenId, trx);
    logger.info({ msg: 'updateNextTokenId', membershipNftContractId, nextTokenId });

    const { accountData: encodedData, requestId } = await this.generateBulkMembershipData(modularAbi, bulkDatas);
    logger.info({ transaction: 'membershipBulkMint', encodedData, modularAbi, bulkDatas });

    return {
      encodedData,
      tbaDatas,
      requestId,
    };
  }

  private async generateBulkMintData(
    abi: ethers.InterfaceAbi,
    queueDatas: {
      toAddress: string;
      nftContractAddress: string;
      nftType: NftType;
      tokenId?: number;
    }[],
  ): Promise<{ mintData: string; requestId: string }> {
    const requestId = uuidv4();
    const contractInterface = new ethers.Interface(abi);

    const bulkDatas = queueDatas.map((queue) => {
      if (queue.nftType == NftType.COUPON) {
        if (queue.tokenId == undefined) {
          logger.warn({ msg: 'generateBulkMintData has invalid coupon record. It is skipped', bulkDatas });
          return null;
        }
        return {
          contractAddress: queue.nftContractAddress,
          tokenId: queue.tokenId,
          amount: 1,
          toAddress: queue.toAddress,
          functionName: this.getFunctionName(queue.nftType),
        };
      } else {
        return {
          contractAddress: queue.nftContractAddress,
          tokenId: 0,
          amount: 1,
          toAddress: queue.toAddress,
          functionName: this.getFunctionName(queue.nftType),
        };
      }
    });

    const mintData = contractInterface.encodeFunctionData('bulkMint', [requestId, bulkDatas]);
    return { mintData, requestId };
  }

  private async generateBulkMembershipData(
    abi: ethers.InterfaceAbi,
    bulkDatas: object[],
  ): Promise<{ accountData: string; requestId: string }> {
    const requestId = uuidv4();
    const contractInterface = new ethers.Interface(abi);
    logger.info({ msg: 'generateBulkMembershipData', bulkDatas });
    const accountData = contractInterface.encodeFunctionData('bulkCreateAccount', [requestId, bulkDatas]);
    return { accountData, requestId };
  }

  private getFunctionName(nftType: NftType): string {
    switch (nftType) {
      case NftType.COUPON:
        return 'additinalMint';
      case NftType.CERTIFICATE:
      case NftType.CONTENT:
      case NftType.MEMBERSHIP:
        return 'safeMint';
      default:
        throw new InternalServerError('Invalid reward type for minting');
    }
  }

  async recreateTba(serviceId: string, contractAccountAddress: string, tbaTokenId: number) {
    const tbaQueueId = uuidv4();
    await db.transaction().execute(async (trx: Transaction<Database>) => {
      const service = await this.serviceInfoRepository.getServiceById(serviceId);
      if (!service) {
        throw new NotFoundError('Service not found');
      }
      const membershipNftContractId = service.membership_nft_contract_id;
      if (!membershipNftContractId) {
        throw new NotFoundError('Membership Nft is not registered');
      }
      const tenantId = service.tenant_id;

      const tbaRegistory = await this.tokenBoundAccountRegistryAddressRepository.selectAddressByServiceId(serviceId);
      if (!tbaRegistory) {
        throw new NotFoundError('tbaRegistory is undefined');
      }
      const registoryAbi = tbaRegistory.abi;
      const tbaImplementation = await this.tokenBoundAccountImplementationRepository.selectByServiceId(serviceId);
      if (!tbaImplementation) {
        throw new NotFoundError('tbaImplementation is undefined');
      }

      const vaultKey = await this.vaultKeyRepository.getVaultKeyIdWithLock(tenantId, trx);
      if (!vaultKey) {
        throw new NotFoundError(`No vault_keys record found for tenantId = ${tenantId}`);
      }
      const nonce = vaultKey.nonce;
      const nftContract = await this.nftContractsRepository.getNftContractsByIdWithLock(membershipNftContractId, trx);
      if (!nftContract) {
        throw new NotFoundError(`No nft_contracts record found for nftContractId = ${membershipNftContractId}`);
      }
      const nftContractAddress = nftContract.nft_contract_address!;

      const contractInterface = new ethers.Interface(registoryAbi as unknown as ethers.InterfaceAbi);
      const registoryAddress = tbaRegistory.token_bound_account_registry_address;
      let tbaCallData: string = '';

      let tbaContractArgs: any[] = [];

      tbaContractArgs = [
        tbaImplementation.token_bound_account_implementation_address,
        tbaRegistory.salt,
        tbaRegistory.chain_id,
        nftContractAddress,
        tbaTokenId,
      ];
      tbaCallData = contractInterface.encodeFunctionData('createAccount', tbaContractArgs);

      await this.transactionQueuesRepository.insertQueue({
        queue_id: tbaQueueId,
        service_id: serviceId,
        from_address: vaultKey.vault_wallet_address,
        to_address: contractAccountAddress,
        status: TransactionQueueStatus.PROCESSING,
        tx_type: TxType.DEPLOY_CONTRACT,
        nft_type: NftType.MEMBERSHIP,
        nft_contract_address: nftContractAddress,
        token_id: tbaTokenId, // linked membership nft token id
        created_date: new Date(),
      });

      const { transactionRequest, signer, fallbackSigner } = await this.transactionService.createTransactionRequest(
        tenantId,
        registoryAddress,
        tbaCallData,
        nonce,
      );
      if (!transactionRequest) {
        throw new InternalServerError('transaction request is null.');
      }
      const signedTransaction = await this.transactionService.signTransaction(
        transactionRequest,
        signer,
        fallbackSigner,
      );
      // calc hash.
      const txHash = await this.transactionService.calculateTxHash(signedTransaction);
      if (!txHash) {
        throw new InternalServerError('calculate transaction hash is null.');
      }
      logger.info({ transaction: 'recreateTba', txHash, tenantId, registoryAddress, nonce, tbaCallData });

      // insert queue.
      const { transactionId } = await this.transactionService.createTransactionDatabases(
        serviceId,
        tbaCallData,
        nonce,
        txHash,
        [tbaQueueId],
      );

      // send tx.
      const txResponse = await this.transactionService.sendTransactionFromSignedTransaction(signedTransaction);
      if (!txResponse) {
        throw new InternalServerError('sendTransactionFromSignedTransaction result is null.');
      }

      if (txResponse.hash !== txHash) {
        logger.error({ msg: 'calc hash is wrong.', txHash, txResponse });

        this.transactionsRepository.updateTransactionHash(transactionId, txResponse.hash);
      }

      const nextNonce = nonce + 1;
      await this.vaultKeyRepository.updateNonce(vaultKey.vault_key_id, nextNonce, trx);
    });

    return {
      membershipId: tbaTokenId,
      tbaQueueId: tbaQueueId,
    };
  }
}
