import { inject, injectable } from 'tsyringe';
import { InternalServerError } from '../errors/internalServerError';
import { TransactionsRepository } from '../repositories/transactionsRepository';
import { TransactionQueuesRepository } from '../repositories/transactionQueuesRepository';
import { VaultTransactionStatus } from '../enum/vaultTransactionStatus';
import { TransactionStatus } from '../enum/transactionStatus';
import { TokenBoundAccountRegistryAddressRepository } from '../repositories/tokenBoundAccountRegistryAddressRepository';
import { TransactionsEntity } from '../tables/transactionsTable';
import { UnauthorizedError } from '../errors/unauthorizedError';
import { ValidationError } from '../errors/validationError';
import { NotFoundError } from '../errors/notFoundError';
import { MetadataService } from './metadataService';
import { NftType } from '../enum/nftType';
import { logger } from '../utils/logger';
import { NftTransactionUpdateService } from './transactionUpdateService';
import { Database, db } from '../db/database';
import { Transaction } from 'kysely';
import { WebhookType, TxCategory } from '../enum/webhookType';
import crypto from 'crypto';
import { NftContractsRepository } from '../repositories/nftContractsRepository';
import { normalizeAddress, decodeUint256Pair } from '../utils/helper';
import { hexToBigInt } from 'viem';
import { ZERO_ADDRESS, ERC721_TRANSFER_EVENT_TOPIC, ERC1155_TRANSFER_EVENT_TOPIC } from '../configs/blockchain';
import { config } from '../configs/config';
import { AlchemyComponent } from '../components/alchemyComponent';
import { TransactionQueueStatus } from '../enum/transactionQueueStatus';
import { BulkMintService } from './bulkMintService';
import { ServiceInfoRepository } from '../repositories/serviceInfoRepository';
import { UserRepository } from '../repositories/userRepository';
import {
  AddressActivity,
  AddressActivityWebhook,
  NFTActivityWebhook,
  WebhookTransactionResponse,
  TransactionReceipt,
  WebhookRequest,
} from '../dtos/webhook/schemas';
import { retryExecution } from '../utils/retry';

export interface TxData {
  tokenId: string;
  from: string;
  to: string;
  amount: string;
  contract: string;
  transactionHash: string;
  timestamp: string;
}

export enum WebhookTxType {
  NFT_DEPLOY = 'nft-deploy',
  NFT_TRANSFER = 'nft-transfer',
  TBA_DEPLOY = 'tba-deploy',
}

export interface TxDetail {
  type: WebhookTxType;
  transactionHash: string;
  nftFrom?: string;
  nftTo?: string;
  nftAmount?: string;
  nftTokenId?: string;
  nftContractAddress?: string;
  timestamp: string;
  isConfirmed: boolean;
}

export interface TxUpdateInfo {
  serviceId: string;
  transactionId: string;
  nftType: NftType;
  currentStatus: VaultTransactionStatus;
  timestamp: string;
  detail: AddressActivity;
}

interface NftTxReceipt {
  address: string;
  hash: string;
  from: string;
  to: string;
  tokenId: number;
  amount: number;
}

/**
 * VaultウォレットおよびNFT関連のブロックチェーンWebhookを処理するサービス。
 * Webhookのペイロードを処理し、署名検証、トランザクションステータスの更新、
 * NFTメタデータやFirestoreコレクションの同期を行う。
 * 失敗時には例外を投げてWebhookのリトライを促す設計。
 */
@injectable()
export class WebhookService {
  constructor(
    @inject('TransactionsRepository') private transactionsRepository: TransactionsRepository,
    @inject('TransactionQueuesRepository') private transactionQueuesRepository: TransactionQueuesRepository,
    @inject('TokenBoundAccountRegistryAddressRepository')
    private tokenBoundAccountRegistryAddressRepository: TokenBoundAccountRegistryAddressRepository,
    @inject('MetadataService') private metadataService: MetadataService,
    @inject('NftTransactionUpdateService') private nftTransactionUpdateService: NftTransactionUpdateService,
    @inject('AlchemyComponent') private alchemyComponent: AlchemyComponent,
    @inject('NftContractsRepository') private nftContractsRepository: NftContractsRepository,
    @inject('BulkMintService') private bulkMintService: BulkMintService,
    @inject('ServiceInfoRepository') private serviceInfoRepository: ServiceInfoRepository,
    @inject('UserRepository') private userRepository: UserRepository,
  ) {}

  /**
   * ブロックチェーンWebhookのメインエントリーポイント。
   * 署名を検証し、Webhookタイプに応じて適切なハンドラに振り分ける。
   * 署名検証に失敗した場合はUnauthorizedErrorをスローする。
   * @param transaction Webhookのペイロード
   * @param signature HMAC署名ヘッダー
   * @returns undefinedまたはWebhookTransactionResponse
   */
  public async handleBlockchainWebhook(
    transaction: WebhookRequest,
    signature: string | undefined,
  ): Promise<WebhookTransactionResponse | undefined> {
    if (!transaction.webhookId || !signature) {
      logger.error({ function: 'handleBlockchainWebhook', error: 'transaction.webhookIdまたはsignatureが見つかりません' });
      // 無効なWebhookの処理を避けるためundefinedを返す
      return undefined;
    }
    this.verifyWebhookSignature(transaction, signature);

    if (transaction.type === WebhookType.ADDRESS_ACTIVITY && transaction.webhookId === config.alchemyAddressWebhookId) {
      await this.handleVaultWalletTransaction(transaction);
    } else if (transaction.type === WebhookType.NFT_ACTIVITY && transaction.webhookId === config.alchemyNftWebhookId) {
      await this.handleNftActivity(transaction);
    } else {
      logger.info({ function: 'handleBlockchainWebhook', log: 'address activityでもnft activityでもありません' });
    }
    return undefined;
  }

  /**
   * Vaultウォレット関連のトランザクションWebhookを処理する内部関数。
   * webhookに含まれるトランザクションハッシュを抽出し、DBのトランザクションを更新。
   * NFTのメタデータ挿入やFirestoreの更新も行う。
   * @param webhookData AddressActivityWebhookのデータ
   */
  private async handleVaultWalletTransaction(webhookData: AddressActivityWebhook) {
    logger.info({ method: 'handleVaultWalletTransaction', data: webhookData });

    // webhookに含まれるtransactionHashをすべて抽出
    const transactionStatus = TransactionStatus.MINED;
    const txs = webhookData.event.activity;
    const uniqueTxHashs = [...new Set(txs.map((tx) => tx.hash))];

    logger.info({ method: 'handleVaultWalletTransaction', data: {tx_hashes: uniqueTxHashs} });
    // Transactionsテーブルに更新対象のtx hashが存在するか確認
    let updateTargetTx = await this.transactionsRepository.selectTransactionsByTxHashs(uniqueTxHashs);
    if (!updateTargetTx || updateTargetTx.length === 0) {
      logger.info({
        webhook: 'updateTransaction',
        log: 'updateTargetTxが見つからず、nonceで検索を試みます',
      });

      // Tx hashが保存されていなかった場合を考慮し、nonceベースでtransactionsテーブルを確認
      const nonceHashs = await this.attachNonce(uniqueTxHashs);
      updateTargetTx = await this.transactionsRepository.selectTransactionsByNonce(nonceHashs.map((n) => n.nonce));
      logger.info({ webhook: 'attachNonce', nonceHashs, updateTargetTx });
      if (!updateTargetTx || updateTargetTx.length === 0) {
        logger.info({ webhook: 'updateTransaction', log: 'Transactionsに該当トランザクションがありません' });
        return undefined;
      }
    }
    logger.info({ method: 'handleVaultWalletTransaction', data: {target_txs: updateTargetTx} });

    // tx hashからトランザクションの詳細を取得
    const receipts = await this.attachReceipt(uniqueTxHashs);
    logger.info({ method: 'handleVaultWalletTransaction', data: {attached_receipts: receipts} });

    for (const receipt of receipts) {
      // トランザクション情報のフォーマットを整形
      const nftLogs = this.extractReceiptForNfts(receipt);
      logger.info({ webhook: 'extractReceipt', receipt, nftLogs });
      if (nftLogs.length === 0) {
        logger.warn({ webhook: '対応していないNFT標準', receipt });
        continue;
      }

      // transactionsテーブルに含まれているかを確認
      const transaction = updateTargetTx.find((tx) => tx.tx_hash === receipt.transactionHash);
      if (!transaction) {
        logger.warn({ webhook: 'updateTargetが見つかりません', hash: receipt.transactionHash });
        continue;
      }
      logger.info({ webhook: 'updateTarget', transaction });

      // トランザクションのステータスを更新
      const { transaction_id: transactionId, service_id: serviceId } = transaction;
      await db.transaction().execute(async (trx: Transaction<Database>) => {
        // transactionのステータスをMINEDに変更
        await this.transactionsRepository.updateTransactionStatus(transactionId, transactionStatus, trx);

        // transaction queueのステータスもMINEDに変更
        const queues = await this.transactionQueuesRepository.selectQueueByTransactionId(transactionId, trx);
        await this.transactionQueuesRepository.updateQueueStatus(
          queues.map((q) => q.queue_id),
          TransactionQueueStatus.MINED,
          trx,
        );

        // 会員証NFTの場合を除いてメタデータを挿入
        try {
          await Promise.all(
            queues
              .filter((queue) => queue.nft_type != NftType.MEMBERSHIP)
              .map(async (queue) => {
                return this.metadataService
                  .insertMetadata(queue.nft_type, queue.nft_contract_address, queue.token_id ?? 0, queue.queue_id, trx)
                  .catch((error) => {
                    logger.error(error);
                  });
              }),
          );
        } catch (error) {
          logger.error(error);
        }
      });

      // 会員証NFTのtoken idがチェーンとDBでズレていないか確認し、必要に応じて修正
      await this.verifyTokenId(serviceId, nftLogs, transaction);

      // Firestoreの更新
      await Promise.all(
        nftLogs.map(async (nft) => {
          const transactionDetail: TxDetail = {
            type: WebhookTxType.NFT_TRANSFER,
            transactionHash: nft.hash,
            nftFrom: nft.from,
            nftTo: nft.to,
            nftAmount: String(nft.amount),
            nftTokenId: String(nft.tokenId),
            nftContractAddress: nft.address,
            timestamp: webhookData.createdAt,
            isConfirmed: false,
          };
          return this.nftTransactionUpdateService.updateFirestoreNftsCollection(transactionDetail);
        }),
      );
    }
  }

  /**
   * NFT関連のWebhookを処理する内部関数。
   * NFTのトランザクション情報を抽出し、FirestoreのNFTコレクションを更新する。
   * @param webhookData NFTActivityWebhookのデータ
   */
  private async handleNftActivity(webhookData: NFTActivityWebhook) {
    logger.info({ method: 'handleNftActivity', data: webhookData });
    const txs = webhookData.event.activity;

    for (const tx of txs.filter((tx) => tx.fromAddress !== ZERO_ADDRESS)) {
      let nftAmount: `0x${string}`;
      let nftTokenId: `0x${string}`;

      switch (tx.category) {
        case TxCategory.ERC721:
          nftAmount = '0x1';
          nftTokenId = (tx.erc721TokenId as `0x${string}`) || `0x${tx.log.topics[3]}`;
          break;
        case TxCategory.ERC1155:
          if (!tx.erc1155Metadata || tx.erc1155Metadata.length === 0) {
            logger.error({ method: 'handleNftActivity', log: 'erc1155Metadataがありません' });
            continue;
          }
          nftAmount = tx.erc1155Metadata[0].value as `0x${string}`;
          nftTokenId = tx.erc1155Metadata[0].tokenId as `0x${string}`;
          break;
        default:
          logger.warn(`対応していないトランザクションカテゴリ: ${tx.category}`);
          continue;
      }

      const transactionDetail: TxDetail = {
        type: WebhookTxType.NFT_TRANSFER,
        transactionHash: tx.hash,
        nftFrom: normalizeAddress(tx.fromAddress),
        nftTo: normalizeAddress(tx.toAddress),
        nftAmount: hexToBigInt(nftAmount).toString(),
        nftTokenId: hexToBigInt(nftTokenId).toString(),
        nftContractAddress: normalizeAddress(tx.contractAddress),
        timestamp: webhookData.createdAt,
        isConfirmed: false,
      };

      await this.nftTransactionUpdateService.updateFirestoreNftsCollection(transactionDetail);
    }
  }

  /**
   * トランザクションレシートからNFT関連のログを抽出する内部関数。
   * ERC721およびERC1155のTransferイベントを解析し、NFTの移転情報を返す。
   * @param receipt トランザクションレシート
   * @returns NFTトランザクションの配列
   */
  private extractReceiptForNfts(receipt: TransactionReceipt): NftTxReceipt[] {
    return receipt.logs
      .map((transferLog) => {
        const contractAddress: string = transferLog.address;
        const transactionHash: string = transferLog.transactionHash;
        let nftFrom: string;
        let nftTo: string;
        let nftTokenId: string;
        let nftAmount: string;

        if (transferLog.topics[0] === ERC1155_TRANSFER_EVENT_TOPIC) {
          const { data1: id, data2: value } = decodeUint256Pair(transferLog.data);
          nftFrom = normalizeAddress(transferLog.topics[2]);
          nftTo = normalizeAddress(transferLog.topics[3]);
          nftTokenId = id.toString();
          nftAmount = value.toString();
        } else if (transferLog.topics[0] === ERC721_TRANSFER_EVENT_TOPIC) {
          nftFrom = normalizeAddress(transferLog.topics[1]);
          nftTo = normalizeAddress(transferLog.topics[2]);
          nftTokenId = BigInt(transferLog.topics[3]).toString();
          nftAmount = '1';
        } else {
          return undefined;
        }
        return {
          address: contractAddress,
          hash: transactionHash,
          from: nftFrom,
          to: nftTo,
          tokenId: Number(nftTokenId),
          amount: Number(nftAmount),
        };
      })
      .filter((log) => !!log);
  }

  /**
   * Webhookの署名を検証する内部関数。
   * 署名が正しくない場合はUnauthorizedErrorをスローする。
   * @param body Webhookのペイロード
   * @param signature 署名ヘッダー
   */
  private verifyWebhookSignature(body: WebhookRequest, signature: string) {
    const signingKey =
      body.webhookId === config.alchemyAddressWebhookId ? config.alchemyAddressSigningKey : config.alchemyNftSigningKey;
    const hmac = crypto.createHmac('sha256', signingKey);
    hmac.update(JSON.stringify(body), 'utf8');
    const digest = hmac.digest('hex');
    if (signature !== digest) {
      logger.error({ body, signature, digest });
      throw new UnauthorizedError('Alchemy webhookの署名が無効です');
    }
  }

  /**
   * VaultウォレットのアドレスをAlchemyのWebhookに追加する。
   * @param address 追加するアドレス
   */
  public async addVaultWalletAddressToWebhook(address: string): Promise<void> {
    const webhookId = config.alchemyAddressWebhookId || '';
    if (!webhookId)
      logger.error({
        method: 'addVaultWalletAddressToWebhook',
        error: 'ALCHEMY_ADDRESS_ACTIVITY_WEBHOOK_IDが見つかりません',
      });
    await this.alchemyComponent.updateAddressActivityWebhook(webhookId, address);
  }

  /**
   * NFTコントラクトのアドレスをAlchemyのWebhookに追加する。
   * @param address 追加するNFTコントラクトアドレス
   */
  public async addNftContractAddressToWebhook(address: string): Promise<void> {
    const webhookId = config.alchemyNftWebhookId || '';
    if (!webhookId)
      logger.error({
        method: 'addNftContractAddressToWebhook',
        error: 'ALCHEMY_NFT_ACTIVITY_WEBHOOK_IDが見つかりません',
      });
    await this.alchemyComponent.updateNftActivityWebhook(webhookId, address);
  }

  /**
   * トランザクションハッシュからnonceを取得し、nonceとハッシュのペアを返す内部関数。
   * @param hashs トランザクションハッシュの配列
   * @returns nonceとハッシュのペアの配列
   */
  private async attachNonce(hashs: string[]): Promise<{ nonce: number; hash: string }[]> {
    const results = await Promise.all(
      hashs.map(async (hash) => {
        const transaction = await this.alchemyComponent.getTransaction(hash);
        if (!transaction) {
          logger.error({
            method: 'attachNonceAndFromAddress',
            error: `ハッシュに対応するトランザクションが見つかりません: ${hash}`,
          });
          return undefined;
        }
        return {
          hash: transaction.hash,
          nonce: transaction.nonce,
        };
      }),
    );

    return results.filter((res) => !!res);
  }

  /**
   * トランザクションハッシュからトランザクションレシートを取得する内部関数。
   * 取得に失敗した場合はリトライを行う。
   * @param hashs トランザクションハッシュの配列
   * @returns トランザクションレシートの配列
   */
  private async attachReceipt(hashs: string[]): Promise<TransactionReceipt[]> {
    const results = await Promise.all(
      hashs.map(async (hash) => {
        return retryExecution(
          async () => {
            const receipt = await this.alchemyComponent.getTransactionReceipt(hash);
            if (!receipt) {
              logger.warn({
                method: 'attachReceipt',
                hash,
                message: 'リトライ後もレシートの取得に失敗しました',
              });
              throw new InternalServerError(`ハッシュに対応するレシートが見つかりません: ${hash}`);
            }
            return receipt;
          },
          {
            retries: 3,
            retryDelay: 500,
            retryHandler: (err) => {
              logger.info({
                method: 'attachReceipt',
                hash,
                message: 'リトライ後もレシートの取得に失敗しました',
                error: err as unknown,
              });
              return true;
            },
          },
        );
      }),
    );

    // undefinedを除外して返す
    return results.filter((res) => res !== undefined);
  }

  /**
   * 会員証NFTのtokenIdがチェーンとDBでズレていないか検証し、必要に応じて修正する内部関数。
   * @param serviceId サービスID
   * @param nftLogs NFTトランザクションログ
   * @param transaction トランザクションエンティティ
   */
  private async verifyTokenId(serviceId: string, nftLogs: NftTxReceipt[], transaction: TransactionsEntity) {
    await db.transaction().execute(async (trx: Transaction<Database>) => {
      // DBに関連情報が登録されているか確認
      const tbaRegistory = await this.tokenBoundAccountRegistryAddressRepository.selectAddressByServiceId(
        serviceId,
        trx,
      );
      if (!tbaRegistory) {
        throw new InternalServerError('tbaRegistoryが未定義です');
      }

      const serviceInfo = await this.serviceInfoRepository.getServiceById(serviceId);
      if (!serviceInfo) throw new ValidationError('サービスが見つかりません');

      const membershipNftContractId = serviceInfo.membership_nft_contract_id;
      if (!membershipNftContractId) {
        throw new NotFoundError('会員証NFTが登録されていません');
      }

      const nftContract = await this.nftContractsRepository.getNftContractsByIdWithLock(membershipNftContractId, trx);
      if (!nftContract) {
        throw new NotFoundError(`nftContractId = ${membershipNftContractId}に対応するnft_contractsレコードがありません`);
      }

      // TXのログの中にmembershipNftContractが移転対象になっているものが存在するか確認
      const membershipNfts = nftLogs.filter(
        (nft) => nft.address.toLowerCase() === nftContract.nft_contract_address?.toLowerCase(),
      );
      if (membershipNfts.length === 0) {
        return;
      }

      // ユーザーのcontract_account_address(LightAccount)を取得
      const userContractAccountAddresses = await this.userRepository.selectUserAndAccountsByContractAccountAddresses(
        membershipNfts.map((nftLog) => nftLog.to),
        serviceId,
      );
      logger.info({
        webhook: 'verifyTokenId',
        userContractAccountAddresses,
        membershipNfts,
        addresses: membershipNfts.map((nftLog) => nftLog.to),
      });

      // 会員証NFTの移転先が登録されているuser idと一致し、DBのtoken idとmembership idが一致するかを確認
      const invalidAccountTokenIds = membershipNfts
        .map((nftLog) => {
          const user = userContractAccountAddresses.find(
            (user) => user.contract_account_address?.toLocaleLowerCase() === nftLog.to.toLocaleLowerCase(),
          );
          // エラーの場合
          if (!user) return undefined;

          // 正常な場合
          if (nftLog.tokenId === user.membership_id) return undefined;

          return {
            tokenId: nftLog.tokenId,
            accountId: user.account_id,
            contractAccountAddress: nftLog.to,
            membershipContractAddress: nftLog.address,
          };
        })
        .filter((idMap) => !!idMap);

      // token id と membership id の不整合がある場合、TBAを更新する
      if (invalidAccountTokenIds.length > 0) {
        logger.warn({ msg: 'verifyTokenId', invalidAccountTokenIds });
        await db.transaction().execute(async (accountTrx: Transaction<Database>) => {
          await this.bulkMintService.updateAccountTBAFromId(
            invalidAccountTokenIds,
            serviceId,
            serviceInfo.tenant_id,
            accountTrx,
          );
        });
      }

      // 会員証NFTの最新のtoken idのズレを解消。このTX以降で発行された会員証の数を計算して更新
      const maxNftTokenId = Math.max(...nftLogs.map((nftLog) => nftLog.tokenId));
      const executedCount = await this.transactionQueuesRepository.countQueueByMembershipAndNonce(
        serviceId,
        transaction.nonce,
        trx,
      );
      logger.info({
        webhook: 'verifyTokenId',
        executedCount: executedCount,
        nextTokenId: nftContract.next_token_id,
        tokenIds: nftLogs.map((nftLog) => nftLog.tokenId),
        maxNftTokenId,
      });
      const calcNextContractTokenId = maxNftTokenId + executedCount + 1;
      if (nftContract.next_token_id !== calcNextContractTokenId) {
        logger.warn(
          `不正なtoken idがDBに存在します。contract_id ${nftContract.nft_contract_id} を token_id ${calcNextContractTokenId} に更新します`,
        );
        await this.nftContractsRepository.updateNextTokenId(nftContract.nft_contract_id, calcNextContractTokenId, trx);
      }
    });
  }
}
