import { inject, injectable } from 'tsyringe';
import { AccountRepository, AccountCustomFieldType } from '../repositories/accountRepository';
import {
  ServicesCustomFieldsRepository,
  ServiceCustomFieldResult,
} from '../repositories/servicesCustomFieldsRepository';
import { NotFoundError } from '../errors/notFoundError';
import { ValidationError } from '../errors/validationError';
import { LanguageCode } from '../enum/languageCode';
import {
  AccountCustomFieldsUpdateRequest,
  AccountCustomFieldsUpdateResponse,
  AccountCustomFieldsResponse,
} from '../dtos/accounts/schemas';
import { CustomFieldType } from '../enum/customFieldType';
import { logger } from '../utils/middleware/loggerMiddleware';

@injectable()
export class AccountCustomFieldService {
  constructor(
    @inject('AccountRepository') private accountRepository: AccountRepository,
    @inject('ServicesCustomFieldsRepository') private servicesCustomFieldsRepository: ServicesCustomFieldsRepository,
  ) {}

  async getAccountCustomField(
    accountId: string,
    serviceId: string,
    lang: LanguageCode,
  ): Promise<AccountCustomFieldsResponse> {
    logger.info({ method: 'getAccountCustomField', accountId, serviceId });
    const account = await this.accountRepository.selectAccountById(accountId, serviceId);
    if (!account) {
      throw new NotFoundError('Account not found');
    }

    const customFieldValues = await this.accountRepository.getAccountCustomFieldValues(accountId, serviceId, lang);
    const formatted = customFieldValues.map((f) => ({
      ...f,
      created_at: f.created_at.toISOString(),
    }));

    return formatted as AccountCustomFieldsResponse;
  }

  async updateAccountCustomField(
    accountId: string,
    serviceId: string,
    customData: AccountCustomFieldsUpdateRequest,
    lang: LanguageCode,
  ): Promise<AccountCustomFieldsUpdateResponse> {
    logger.info({ method: 'updateAccountCustomField', accountId, serviceId, customData });
    const account = await this.accountRepository.selectAccountById(accountId, serviceId);
    if (!account) {
      throw new NotFoundError('Account not found');
    }

    const customFieldDefinition = await this.servicesCustomFieldsRepository.getLatestCustomFieldsVersion(
      serviceId,
      lang,
    );
    const existingCustomFields = await this.accountRepository.getAccountCustomFieldValues(accountId, serviceId, lang);

    // Validation
    if (customFieldDefinition && customFieldDefinition.fields && customFieldDefinition.fields.length > 0) {
      const combinedCustomFields = existingCustomFields.map((field) => {
        const val = customData[field.field_key];
        return {
          ...field,
          values: val != null ? (Array.isArray(val) ? val : [val]) : field.values,
        };
      });
      const validationErrors = await this.validateCustomData(
        combinedCustomFields,
        customFieldDefinition.fields,
        accountId,
        serviceId,
      );
      if (validationErrors.length > 0) {
        throw new ValidationError(JSON.stringify(validationErrors));
      }
    }

    const formattedCustomFields = customFieldDefinition.fields
      .filter((field: ServiceCustomFieldResult) => customData[field.field_key] !== undefined)
      .flatMap((field: ServiceCustomFieldResult) => {
        const rawValue = customData[field.field_key];
        if (Array.isArray(rawValue)) {
          return rawValue.map((v) => ({
            fieldKey: field.field_key,
            customFieldId: field.custom_field_id,
            value: v,
          }));
        }
        return [
          {
            fieldKey: field.field_key,
            customFieldId: field.custom_field_id,
            value: rawValue as string,
          },
        ];
      });

    await this.accountRepository.createOrUpdateAccountCustomField(accountId, serviceId, formattedCustomFields);

    return formattedCustomFields;
  }

  private async validateCustomData(
    customData: AccountCustomFieldType[],
    fieldDefinitions: ServiceCustomFieldResult[],
    accountId: string,
    serviceId: string,
  ): Promise<Array<{ field: string; message: string }>> {
    const errors: Array<{ field: string; message: string }> = [];

    for (const fieldDef of fieldDefinitions) {
      const fieldKey = fieldDef.field_key;
      const fieldValue = customData.find((f) => f.field_key === fieldKey)?.values;

      // Optional
      const isEmpty = ([] as (string | undefined)[]).concat(fieldValue ?? []).every((v) => v == null);

      if (!fieldDef.optional && isEmpty) {
        errors.push({
          field: fieldKey,
          message: `${fieldKey} is required`,
        });
        continue;
      }

      if (isEmpty) {
        continue;
      }

      // Unique
      if (fieldDef.unique && fieldValue) {
        const isUnique = await this.accountRepository.isCustomFieldValueUnique(
          serviceId,
          fieldDef.custom_field_id,
          fieldValue[0],
          accountId,
        );

        if (!isUnique) {
          errors.push({
            field: fieldKey,
            message: `Value '${fieldValue}' for ${fieldKey} is already in use by another account`,
          });
        }
      }

      switch (fieldDef.type) {
        case CustomFieldType.TEXT:
        case CustomFieldType.EMAIL:
        case CustomFieldType.PHONE: {
          const stringValue = Array.isArray(fieldValue) ? fieldValue[0] : fieldValue;

          if (typeof stringValue === 'string') {
            // Min length
            if (fieldDef.min_length != null && stringValue.length < fieldDef.min_length) {
              errors.push({
                field: fieldKey,
                message: `${fieldKey} must be at least ${fieldDef.min_length} characters`,
              });
            }

            // Max length
            if (fieldDef.max_length != null && stringValue.length > fieldDef.max_length) {
              errors.push({
                field: fieldKey,
                message: `${fieldKey} must be at most ${fieldDef.max_length} characters`,
              });
            }

            // Regex
            if (
              fieldDef.validator &&
              fieldDef.validator.pattern &&
              !this.validatePattern(stringValue, fieldDef.validator.pattern)
            ) {
              errors.push({
                field: fieldKey,
                message: `${fieldKey} does not match the required pattern`,
              });
            }
          }
          break;
        }

        case CustomFieldType.NUMERIC: {
          const numericValue = Array.isArray(fieldValue) ? fieldValue[0] : fieldValue;
          if (typeof numericValue === 'string' && isNaN(Number(numericValue))) {
            errors.push({
              field: fieldKey,
              message: `${fieldKey} must be a number`,
            });
          }
          break;
        }

        case CustomFieldType.SELECTION:
          if (fieldDef.options && fieldDef.options.length > 0) {
            const validOptions = fieldDef.options.map((opt) => opt.value);

            if (Array.isArray(fieldValue) && fieldValue.length > 1) {
              errors.push({
                field: fieldKey,
                message: `${fieldKey} only allows single selection`,
              });
            }

            const selectionValue = Array.isArray(fieldValue) ? fieldValue[0] : fieldValue;
            if (typeof selectionValue === 'string' && !validOptions.includes(selectionValue)) {
              errors.push({
                field: fieldKey,
                message: `'${selectionValue}' is not a valid option for ${fieldKey}`,
              });
            }
          }
          break;

        case CustomFieldType.MULTIPLE_SELECTION:
          if (fieldDef.options && fieldDef.options.length > 0) {
            const validOptions = fieldDef.options.map((opt) => opt.value);

            if (!Array.isArray(fieldValue)) {
              errors.push({
                field: fieldKey,
                message: `${fieldKey} must be an array for multiple selection`,
              });
            } else {
              for (const item of fieldValue) {
                if (!validOptions.includes(item)) {
                  errors.push({
                    field: fieldKey,
                    message: `'${item}' is not a valid option for ${fieldKey}`,
                  });
                }
              }
            }
          }
          break;
      }
    }

    return errors;
  }

  private validatePattern(value: string, pattern: string): boolean {
    try {
      const regex = new RegExp(pattern);
      return regex.test(value);
    } catch (error) {
      logger.error({ method: 'validatePattern', error });
      return false;
    }
  }
}
