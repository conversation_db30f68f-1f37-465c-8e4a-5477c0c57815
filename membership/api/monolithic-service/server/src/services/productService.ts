import Stripe from 'stripe';
import { inject, injectable } from 'tsyringe';
import { config } from '../configs/config';
import { LineItem, StripeCheckoutInfo, StripeProduct, StripeSessionConfirmStatus } from '../dtos/products/schemas';
import { CheckoutStatus } from '../enum/checkoutStatus';
import { LanguageCode } from '../enum/languageCode';
import { NftType } from '../enum/nftType';
import { SessionStatus } from '../enum/sessionStatus';
import { TxType } from '../enum/txType';
import { InternalServerError } from '../errors/internalServerError';
import { NotFoundError } from '../errors/notFoundError';
import { ValidationError } from '../errors/validationError';
import { AccountRepository } from '../repositories/accountRepository';
import { CheckoutRepository } from '../repositories/checkoutRepository';
import { CustomFieldsRepository } from '../repositories/customFieldsRepository';
import { NftContractsRepository } from '../repositories/nftContractsRepository';
import { ProductRepository } from '../repositories/productRepository';
import { ServiceInfoRepository } from '../repositories/serviceInfoRepository';
import { ProductMetadata, productMetadataSchema } from '../requestdto/stripe_webhook/productMetadata';
import { SessionMetadata, sessionMetadataSchema } from '../requestdto/stripe_webhook/sessionMetadata';
import { ProductEntity, ProductWithTranslationsEntity } from '../tables/productsTable';
import { logger } from '../utils/middleware/loggerMiddleware';
import { MetadataService } from './metadataService';
import { NftMintService } from './nftMintService';

interface ProductData {
  id: string;
  name: string;
  image: string;
  entity: ProductWithTranslationsEntity;
  price: Stripe.Price;
  metadata: ProductMetadata;
  remainingCount: number;
  orderIndex: number;
  status: {
    total_checkout_count: number;
    account_checkout_count: number;
  };
}

@injectable()
export class ProductService {
  private stripe: Stripe;

  constructor(
    @inject('NftMintService') private nftMintService: NftMintService,
    @inject('CheckoutRepository') private checkoutRepository: CheckoutRepository,
    @inject('NftContractsRepository') private nftContractsRepository: NftContractsRepository,
    @inject('ServiceInfoRepository') private serviceInfoRepository: ServiceInfoRepository,
    @inject('AccountRepository') private accountRepository: AccountRepository,
    @inject('ProductRepository') private productRepository: ProductRepository,
    @inject('CustomFieldsRepository') private customFieldsRepository: CustomFieldsRepository,
    @inject('MetadataService') private metadataService: MetadataService,
  ) {
    this.stripe = new Stripe(
      process.env.STRIPE_SECRET_KEY as string,
      { apiVersion: '2025-01-27.acacia' } as Stripe.StripeConfig,
    );
  }

  public async handleStripeEvents(body: string, signature: string): Promise<void> {
    const event = this.stripe.webhooks.constructEvent(body, signature, config.stripeWebhookSecret);

    // Filtering events
    const validEventTypes = [
      'checkout.session.completed',
      'checkout.session.async_payment_succeeded',
      'checkout.session.expired',
      'checkout.session.async_payment_failed',
    ];
    if (!validEventTypes.includes(event.type)) return;

    // Get Stripe Account Id (tenant)
    const stripeAccountId = event.account;
    if (!stripeAccountId) throw new ValidationError('stripe account id is missing');

    // Get session information.
    const sessionId = (event.data.object as Stripe.Checkout.Session).id;
    if (!sessionId) throw new ValidationError('stripe session id is missing');

    const { metadata: sessionMetadata, paymentStatus } = await this.getSessionMetaDataAndPaymentStatus(
      sessionId,
      stripeAccountId,
    );
    const serviceId = sessionMetadata.service_id;
    logger.info({ trigger: 'webhook', session_metadata: sessionMetadata, payment_status: paymentStatus });

    const service = await this.serviceInfoRepository.getServiceById(serviceId);
    if (!service || service.stripe_account_id !== stripeAccountId) return;

    const toAddress = sessionMetadata.to_address;
    if (!toAddress) throw new ValidationError('target to address is not exist');

    const account = await this.accountRepository.selectAccountByTokenBoundAddress(toAddress, serviceId);
    if (!account) throw new ValidationError('mint target account is not exists');

    // Identify the status to be updated.
    let status: CheckoutStatus;
    if (event.type === 'checkout.session.completed') {
      if (paymentStatus === 'canceled') {
        status = CheckoutStatus.FAILED;
      } else if (paymentStatus === 'succeeded') {
        status = CheckoutStatus.COMPLETED;
      } else {
        status = CheckoutStatus.FULFILLING;
      }
    } else if (event.type === 'checkout.session.async_payment_succeeded') {
      status = CheckoutStatus.COMPLETED;
    } else if (event.type === 'checkout.session.expired') {
      status = CheckoutStatus.EXPIRED;
    } else if (event.type === 'checkout.session.async_payment_failed') {
      status = CheckoutStatus.FAILED;
    } else {
      throw new ValidationError('unexpected event type caught');
    }
    logger.info({ trigger: 'webhook', message: `Handle Stripe event type: ${status}` });
    const { productId, metadata: productMetadata } = await this.getProductMetadata(sessionId, stripeAccountId);
    const contractId = productMetadata.nft_contract_id;
    const tokenId = productMetadata.nft_token_id;

    if (status === CheckoutStatus.COMPLETED) {
      const isMintSuccess = await this.checkoutRepository.handleCompletionTransaction(
        async () => {
          await this.mintNftWithCheckout(serviceId, account.account_id, toAddress, contractId, tokenId ?? undefined);
        },
        serviceId,
        sessionId,
        productId,
      );

      if (!isMintSuccess) {
        logger.error({ method: 'webhook', session: sessionId, status: 'refund' });
        await this.checkoutRepository.updateCheckoutSession(sessionId, serviceId, {
          checkout_status: CheckoutStatus.FAILED,
        });
        await this.refundProduct(stripeAccountId, event.data.object.payment_intent as string);
      }
    } else {
      logger.error({ method: 'webhook', session: sessionId, status: status });
      await this.checkoutRepository.updateCheckoutSession(sessionId, serviceId, {
        checkout_status: status,
      });
    }
  }

  public async getProducts(serviceId: string, accountId: string, lang: LanguageCode): Promise<StripeProduct[]> {
    const service = await this.serviceInfoRepository.getServiceById(serviceId);
    if (!service || !service.stripe_account_id) {
      throw new NotFoundError('Stripe account id is not found');
    }
    logger.info({ method: 'getProducts', service_id: serviceId, account_id: accountId });

    // Obtain a list of products by calling Stripe's API
    const stripeProducts: Stripe.Product[] = [];
    while (true) {
      const response = await this.stripe.products.list(
        {
          limit: 100,
          active: true,
          expand: ['data.default_price'],
        },
        { stripeAccount: service.stripe_account_id },
      );

      stripeProducts.push(...response.data);

      if (!response.has_more) break;
    }
    logger.info({ method: 'getProducts', stripe_products: stripeProducts });

    // Obtain a list of product quantities by serviceId from products table
    const productsInfo = await this.productRepository.selectProductByServiceId(serviceId, lang);
    logger.info({ method: 'getProducts', products_info: productsInfo });

    // Format the response
    const checkoutStatusMap = await this.checkoutRepository.accountAlreadyCheckoutedWithProductIds(
      serviceId,
      Array.from(stripeProducts.map((stripe) => stripe.id)),
      accountId,
    );
    logger.info({ method: 'getProducts', checkout_status_map: Array.from(checkoutStatusMap.values()) });

    const productMap = new Map<string, ProductData>();
    productsInfo.forEach((info) => {
      const data = stripeProducts.find((stripe) => stripe.id === info.stripe_product_id);
      const status = checkoutStatusMap.get(info.stripe_product_id);
      let metadata: ProductMetadata | undefined;
      try {
        if (!data) {
          metadata = undefined;
        } else {
          metadata = productMetadataSchema.parse(data.metadata);
        }
      } catch (error) {
        logger.error({ method: 'getProducts', product_metadata: data?.metadata, error: error });
        metadata = undefined;
      }
      const price = data?.default_price;

      if (data && status && metadata && price && typeof price === 'object') {
        productMap.set(info.stripe_product_id, {
          id: data.id,
          name: data.name,
          image: data.images.length === 0 ? '' : data.images[0],
          price: price,
          entity: info,
          remainingCount:
            info.quantity - status.total_checkout_count < 0 ? 0 : info.quantity - status.total_checkout_count,
          metadata,
          status: status,
          orderIndex: info.order_index,
        });
      }
    });
    logger.info({ method: 'getProducts', product_map_value: Array.from(productMap.values()) });

    const current = new Date();

    return (
      Array.from(productMap.values())
        // Products exceeding PERCHASE_LIMIT_PER_PERSON are excluded.
        .filter((product) => product.status.account_checkout_count < product.entity.perchase_limit_per_person)
        // Start date is before now
        .filter((product) => new Date(product.metadata.available_at) <= current)
        // End date now or later.
        .filter((product) => new Date(product.metadata.expire_at) > current)
        // Priced at
        .filter((product) => product.price.unit_amount != null)
        .map((product) => {
          return {
            productId: product.id,
            productName: product.name,
            imageUrl: product.image,
            thumbnailImageUrl: product.metadata.thumbnail_image_url,
            coverImageUrl: product.metadata.cover_image_url,
            availableAt: product.metadata.available_at,
            expireAt: product.metadata.expire_at,
            category: product.metadata.category_name,
            priceId: product.price.id,
            unitPrice: product.price.unit_amount!,
            currency: product.price.currency,
            quantity: product.remainingCount,
            description: product.entity.description,
            orderIndex: product.orderIndex,
          };
        })
    );
  }

  public async createCheckoutSession(
    serviceId: string,
    accountId: string,
    product: LineItem,
  ): Promise<StripeCheckoutInfo> {
    // Step 1: Get stripeAccountId and serviceUrl by serviceId
    const service = await this.serviceInfoRepository.getServiceById(serviceId);
    if (!service) throw new NotFoundError('Service not found');
    const { stripe_account_id: stripeAccountId } = service;
    if (!stripeAccountId) {
      throw new NotFoundError('stripeAccountId is not registered');
    }

    // Step 2: Get tokenBoundAccountAddress by accountId
    const account = await this.accountRepository.selectAccountById(accountId, serviceId);
    if (!account || !account.token_bound_account_address) throw new NotFoundError('Account or TBA address not found');
    const { token_bound_account_address: toAddress } = account;

    // Fetch Stripe product data
    const { productId, productUnitAmount } = await this.getProductFromPriceId(product.priceId, stripeAccountId);
    logger.info({
      method: 'createCheckoutSession',
      product_unit_amount: productUnitAmount,
      commission: Math.ceil(productUnitAmount * service.commission_rate),
    });

    // Get product data
    const productData = await this.productRepository.selectProductByStripeId(productId, serviceId);
    if (!productData) throw new NotFoundError('product data is not found');
    await this.validateProductStock(productData, productId, serviceId, true);
    await this.validatePurchaseLimit(accountId, productId, serviceId);

    const currentSessionId = await this.checkoutRepository.selectAvailableCheckoutSession(
      accountId,
      productId,
      serviceId,
    );

    if (currentSessionId) {
      logger.info({ method: 'createCheckoutSession', current_session_id: currentSessionId });
      const currentSession = await this.stripe.checkout.sessions.retrieve(currentSessionId, {
        stripeAccount: stripeAccountId,
      });
      const response: StripeCheckoutInfo = {
        sessionId: currentSession.id,
        credential: currentSession.client_secret!,
        account: stripeAccountId,
      };
      return response;
    }

    // Get custom fields
    const customFieldObjects = await this.customFieldsRepository.selectCustomFieldsByProductId(productId, serviceId);
    const customFields = customFieldObjects.map((obj) => obj as Stripe.Checkout.SessionCreateParams.CustomField);
    logger.info({ method: 'createCheckoutSession', custom_fields: customFields });

    // Create the checkout session
    const session = await this.stripe.checkout.sessions.create(
      {
        mode: 'payment',
        ui_mode: 'embedded',
        redirect_on_completion: 'never',
        line_items: [
          {
            price: product.priceId,
            quantity: product.quantity,
          },
        ],
        metadata: {
          service_id: serviceId,
          to_address: toAddress,
        },
        billing_address_collection: productData.is_billing_address_collection ? 'required' : 'auto',
        phone_number_collection: { enabled: productData.is_phone_number_collection },
        shipping_address_collection: productData.is_shipping_address_collection
          ? ({ allowed_countries: ['JP'] } as Stripe.Checkout.SessionCreateParams.ShippingAddressCollection)
          : undefined,
        currency: 'jpy',
        payment_method_types: ['card'],
        payment_intent_data: {
          application_fee_amount: Math.ceil(productUnitAmount * service.commission_rate),
        },
        custom_fields: customFields,
        expires_at: Math.floor((Date.now() + 30 * 60 * 1000) / 1000),
      },
      { stripeAccount: stripeAccountId },
    );
    logger.info({ method: 'createCheckoutSession', session: session.client_secret });

    const isCreationSuccess = await this.checkoutRepository.handleCheckoutCreationTransaction({
      checkout_session_id: session.id,
      stripe_product_id: productId,
      service_id: serviceId,
      account_id: accountId,
      checkout_status: CheckoutStatus.STARTED,
    });

    if (!isCreationSuccess) {
      await this.expireSession(stripeAccountId, accountId, productId, serviceId);
      throw new ValidationError('Stock does not exist');
    }
    const response: StripeCheckoutInfo = {
      sessionId: session.id,
      credential: session.client_secret!,
      account: stripeAccountId,
    };
    return response;
  }

  public async expireCheckoutSession(serviceId: string, accountId: string, sessionId: string): Promise<void> {
    logger.info({ method: 'expireCheckoutSession', service_id: serviceId, account_id: accountId });
    const service = await this.serviceInfoRepository.getServiceById(serviceId);
    if (!service || !service.stripe_account_id) {
      throw new NotFoundError('Stripe account id is not found');
    }
    logger.info({ method: 'expireCheckoutSession', service: service });

    const stripeAccountId = service.stripe_account_id;
    if (!stripeAccountId) {
      throw new ValidationError('stripe account id is missing');
    }
    logger.info({ method: 'expireCheckoutSession', stripe_account_id: stripeAccountId });

    const status = await this.getSessionStatus(sessionId, stripeAccountId);
    logger.info({ method: 'expireCheckoutSession', status: status });

    const session = await this.checkoutRepository.selectCheckoutSession(sessionId, serviceId);
    logger.info({ method: 'expireCheckoutSession', session: session });

    if (status === SessionStatus.PROCESSING && session?.checkout_status === CheckoutStatus.STARTED) {
      await this.expireSession(stripeAccountId, accountId, sessionId, serviceId);
    }

    return;
  }

  public async confirmCheckoutStatus(
    serviceId: string,
    accountId: string,
    sessionId: string,
  ): Promise<StripeSessionConfirmStatus> {
    logger.info({ method: 'getProducts', service_id: serviceId, account_id: accountId });
    const service = await this.serviceInfoRepository.getServiceById(serviceId);
    if (!service || !service.stripe_account_id) {
      throw new NotFoundError('Stripe account id is not found');
    }
    const stripeAccountId = service.stripe_account_id;
    if (!stripeAccountId) {
      throw new ValidationError('stripe account id is missing');
    }
    logger.info({ method: 'confirmCheckoutStatus', session_id: sessionId, stripe_account_id: stripeAccountId });

    const sessionStatus = await this.checkoutRepository.selectCheckoutSession(sessionId, serviceId);
    if (!sessionStatus) {
      throw new ValidationError('session id is invalid');
    }
    logger.info({ method: 'confirmCheckoutStatus', session_status: sessionStatus });

    switch (sessionStatus.checkout_status) {
      case CheckoutStatus.FULFILLING:
        break;
      case CheckoutStatus.COMPLETED:
        break;
      case CheckoutStatus.STARTED:
        return {
          status: SessionStatus.PROCESSING,
        };
      case CheckoutStatus.EXPIRED:
        return {
          status: SessionStatus.PROCESSING,
        };
      case CheckoutStatus.FAILED:
        return {
          status: SessionStatus.FAILED,
        };
    }

    logger.info({ method: 'confirmCheckoutStatus', session_status: sessionStatus });

    // Fetch Stripe product data
    const { metadata: productMetadata, name: productName } = await this.getProductMetadata(sessionId, stripeAccountId);
    const { nft_contract_id: contractId, nft_token_id: tokenId } = productMetadata;
    logger.info({ method: 'confirmCheckoutStatus', product_metadata: productMetadata });

    // Extract NFT titles and images
    const nftInfo = await this.getNftInfo(contractId, tokenId ?? undefined);
    const title = nftInfo.nftTitle;
    const image = nftInfo.nftImageUrl;
    const name = productName;
    logger.info({ method: 'confirmCheckoutStatus', title: title, image: image, name: name });

    const response: StripeSessionConfirmStatus = {
      status: SessionStatus.SUCCEED,
      nftTitle: title,
      nftImageUrl: image,
      productName: name,
    };
    return response;
  }

  private async getSessionStatus(sessionId: string, stripeAccountId: string): Promise<SessionStatus> {
    const sessionDetails = await this.stripe.checkout.sessions.retrieve(sessionId, { stripeAccount: stripeAccountId });
    let status: SessionStatus;
    if (sessionDetails.status === 'open') {
      status = SessionStatus.PROCESSING;
    } else if (sessionDetails.status === 'expired') {
      status = SessionStatus.FAILED;
    } else if (sessionDetails.status === 'complete') {
      status = SessionStatus.SUCCEED;
    } else {
      status = SessionStatus.FAILED;
    }

    return status;
  }

  private async getSessionMetaDataAndPaymentStatus(
    sessionId: string,
    stripeAccountId: string,
  ): Promise<{ metadata: SessionMetadata; paymentStatus: Stripe.PaymentIntent.Status | undefined }> {
    // Retrieve the session details
    const sessionDetails = await this.stripe.checkout.sessions.retrieve(
      sessionId,
      { expand: ['payment_intent'] },
      { stripeAccount: stripeAccountId },
    );
    let sessionMetadata: SessionMetadata;
    let paymentStatus: Stripe.PaymentIntent.Status | null;
    try {
      sessionMetadata = sessionMetadataSchema.parse(sessionDetails.metadata);
      paymentStatus = (sessionDetails.payment_intent as Stripe.PaymentIntent)?.status;
    } catch (error) {
      throw new InternalServerError(`getSessionData sessionMetadata parse error: ${error}`);
    }

    return { metadata: sessionMetadata, paymentStatus: paymentStatus || undefined };
  }

  private async getProductMetadata(
    sessionId: string,
    stripeAccountId: string,
  ): Promise<{ productId: string; metadata: ProductMetadata; name: string }> {
    // Fetch line items and product metadata
    const lineItems = await this.stripe.checkout.sessions.listLineItems(
      sessionId,
      { expand: ['data.price.product'] },
      { stripeAccount: stripeAccountId },
    );
    logger.info({ method: 'getProductMetadata', line_items: lineItems });
    if (lineItems.data.length !== 1) throw new InternalServerError('Checkout has invalid number of products');

    const product = lineItems.data[0]?.price?.product as Stripe.Product;
    let productMetadata: ProductMetadata;
    try {
      productMetadata = productMetadataSchema.parse(product.metadata);
      logger.info({ method: 'getProductMetadata', product_metadata: productMetadata });
    } catch (error) {
      throw new InternalServerError(`getProductMetadata productMetadata parse error: ${error}`);
    }
    return { productId: product.id, metadata: productMetadata, name: product.name };
  }

  private async mintNftWithCheckout(
    serviceId: string,
    accountId: string,
    toAddress: string,
    nftContractId: string,
    nftTokenId?: string,
  ) {
    logger.info({
      method: 'webhook',
      service_id: serviceId,
      account_id: accountId,
      to_address: toAddress,
      nft_contract_id: nftContractId,
      nft_token_id: nftTokenId,
    });
    // Retrieve NFT contract details
    const nftContract = await this.nftContractsRepository.selectNftContractIdAndNftContractAbiUrl(
      nftContractId,
      serviceId,
    );
    if (!nftContract) throw new NotFoundError('NFT contract details not found.');
    logger.info({ method: 'webhook', nft_contract: nftContract });

    const nftType = nftContract.nft_type;
    if (nftType === NftType.COUPON) {
      //  COUPON NFT is mint target
      if (!nftTokenId) throw new InternalServerError('Valid token id did not set Product Metadata');
      await this.nftMintService.mint(
        serviceId,
        accountId,
        TxType.MINT_PRODUCT,
        toAddress,
        nftContractId,
        nftType,
        Number(nftTokenId),
      );
    } else {
      //  CONTENT or CERTIFICATE NFT is mint target
      await this.nftMintService.mint(
        serviceId,
        accountId,
        TxType.MINT_PRODUCT,
        toAddress,
        nftContractId,
        nftType,
        undefined,
      );
    }
  }

  private async getProductFromPriceId(
    priceId: string,
    stripeAccountId: string,
  ): Promise<{ productId: string; productName: string; productUnitAmount: number; productMetadata: ProductMetadata }> {
    const price = await this.stripe.prices.retrieve(
      priceId,
      { expand: ['product'] },
      { stripeAccount: stripeAccountId },
    );
    if (!price.product || typeof price.product !== 'object' || !('metadata' in price.product)) {
      throw new InternalServerError('Product metadata is missing or invalid.');
    }
    // Ensure each price has valid product metadata
    let productMetadata: ProductMetadata;
    try {
      productMetadata = productMetadataSchema.parse(price.product.metadata);
    } catch (error) {
      throw new InternalServerError(`getProductFromPriceId productMetadata parse error: ${error}`);
    }
    const productId = price.product.id;
    const productName = price.product.name;
    const productUnitAmount = price.unit_amount ?? 0;

    return {
      productId: productId,
      productName: productName,
      productUnitAmount: productUnitAmount,
      productMetadata: productMetadata,
    };
  }

  private async getNftInfo(
    nftContractId: string,
    nftTokenId?: string,
  ): Promise<{ nftTitle: string; nftImageUrl: string }> {
    const nftContract = await this.nftContractsRepository.selectNftContractById(nftContractId);
    logger.info({ method: 'createCheckoutSession', nft_contract: nftContract });

    if (!nftContract?.nft_contract_address) {
      throw new NotFoundError(`${nftContractId} NFT contract not registered `);
    }
    const nftMetadata = await this.metadataService.getParsedMetadataFromBaseMetadata(
      nftContract.nft_contract_address,
      nftTokenId !== undefined ? parseInt(nftTokenId, 10) : undefined,
    );

    return { nftTitle: nftMetadata.name, nftImageUrl: nftMetadata.image };
  }

  private async validateProductStock(
    productData: ProductEntity,
    productId: string,
    serviceId: string,
    includeOpenSession: boolean,
  ): Promise<void> {
    const maxStock = productData.quantity;
    const shippedCount = await this.checkoutRepository.countBlockSessionWithProductId(
      serviceId,
      productId,
      includeOpenSession,
    );
    const remainingCount = maxStock - shippedCount;
    if (maxStock - shippedCount < 1) {
      logger.info({ method: 'validateProduct', remaining_count: remainingCount });
      await this.checkoutRepository.expireCheckoutSessionByProduct(productId, serviceId);
      throw new NotFoundError('Availavble product stock is empty');
    }
  }

  private async validatePurchaseLimit(accountId: string, productId: string, serviceId: string): Promise<void> {
    const productData = await this.productRepository.selectProductByStripeId(productId, serviceId);

    if (!productData) throw new NotFoundError('product data is not found');

    const userPurchaseLimit = productData.perchase_limit_per_person;
    const alreadyCheckoutCount = await this.checkoutRepository.accountAlreadyCheckoutedWithProductId(
      serviceId,
      productId,
      accountId,
    );
    if (alreadyCheckoutCount >= userPurchaseLimit) {
      logger.info({
        method: 'validateProduct',
        already_checkout: alreadyCheckoutCount,
        user_purchase_limit: userPurchaseLimit,
      });
      await this.checkoutRepository.expireCheckoutSessionByAccountAndProduct(accountId, productId, serviceId);
      throw new ValidationError('Product already checked');
    }
  }

  private async refundProduct(stripeAccountId: string, paymentIntent: string) {
    logger.error({
      method: 'webhook',
      refund_product: { stripe_account_id: stripeAccountId, payment_intent: paymentIntent },
    });
    await this.stripe.refunds.create(
      { payment_intent: paymentIntent, refund_application_fee: true },
      { stripeAccount: stripeAccountId },
    );
  }

  private async expireSession(stripeAccountId: string, accountId: string, sessionId: string, serviceId: string) {
    try {
      await this.stripe.checkout.sessions.expire(sessionId, { stripeAccount: stripeAccountId });
      await this.checkoutRepository.expireCheckoutSession(accountId, sessionId, serviceId);
    } catch (error) {
      logger.error({ method: 'expireSession', error: error });
    }
  }
}
