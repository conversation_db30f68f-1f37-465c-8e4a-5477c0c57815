import type FirebaseFirestore from '@google-cloud/firestore';
import { TransactionResponse } from 'ethers';
import { inject, injectable } from 'tsyringe';
import { Block, Transaction as ViemTransaction } from 'viem';
import { TransactionComponent } from '../components/transactionComponent';
import { ViemComponent } from '../components/viemComponent';
import { NftType } from '../enum/nftType';
import { TransactionStatus } from '../enum/transactionStatus';
import { TransferTxType } from '../enum/transferTxType';
import { TxType } from '../enum/txType';
import { InternalServerError } from '../errors/internalServerError';
import { ValidationError } from '../errors/validationError';
import { AccountRepository } from '../repositories/accountRepository';
import { DeliveryNftsFirestoreRepository } from '../repositories/deliveryNftsFirestoreRepository';
import { NftBaseMetadatasRepository } from '../repositories/nftBaseMetadatasRepository';
import { NftContractsRepository } from '../repositories/nftContractsRepository';
import { FirestoreNftsDocument, NftsFirestoreRepository } from '../repositories/nftsFirestoreRepository';
import { ServiceInfoRepository } from '../repositories/serviceInfoRepository';
import { TransactionsRepository } from '../repositories/transactionsRepository';
import { NftBaseMetadatasEntity } from '../tables/nftBaseMetadatasTable';
import { TransactionsEntity } from '../tables/transactionsTable';
import { TransactionQueuesRepository } from '../repositories/transactionQueuesRepository';
import { Database, db } from '../db/database';
import { Transaction } from 'kysely';
import { TxFinalityStatus } from '../dtos/nfts/schemas';
import { config } from '../configs/config';
import { logger } from '../utils/middleware/loggerMiddleware';
import { TxDetail } from './webhookService';

export interface FirestoreNftsUpdateInfo {
  serviceId: string;
  tokenId: string;
  from: string;
  to: string;
  amount: string;
  contractAddress: string;
  transactionHash: string;
  nftType: NftType;
  timestamp: string;
  isConfirmed: boolean;
}

@injectable()
export class NftTransactionUpdateService {
  constructor(
    @inject('AccountRepository') private accountRepository: AccountRepository,
    @inject('NftsFirestoreRepository') private nftsFirestoreRepository: NftsFirestoreRepository,
    @inject('NftContractsRepository') private nftContractsRepository: NftContractsRepository,
    @inject('NftBaseMetadatasRepository') private nftBaseMetadatasRepository: NftBaseMetadatasRepository,
    @inject('DeliveryNftsFirestoreRepository') private deliveryNftsFirestoreRepository: DeliveryNftsFirestoreRepository,
    @inject('ServiceInfoRepository') private serviceInfoRepository: ServiceInfoRepository,
    @inject('ViemComponent') private viemComponent: ViemComponent,
    @inject('TransactionComponent') private transactionComponent: TransactionComponent,
    @inject('TransactionsRepository') private transactionsRepository: TransactionsRepository,
    @inject('TransactionQueuesRepository') private transactionQueuesRepository: TransactionQueuesRepository,
  ) {}

  public async updateFirestoreNftsCollection(txDetail: TxDetail) {
    logger.info({ method: 'updateFirestoreNftsCollection', data: txDetail });
    const nftDocs = await this.nftsFirestoreRepository.selectNftsByTxHash(txDetail.transactionHash);
    if (nftDocs.empty) {
      return this.handleTransactionType(txDetail);
    }
    return;
  }

  async updateTxFinalityStatus(): Promise<TxFinalityStatus> {
    const minedTxs = await this.transactionsRepository.selectTransactionsByStatus(TransactionStatus.MINED);
    if (minedTxs.length === 0) {
      return {
        status: 'success',
        message: 'No mined transactions found',
      };
    }
    const latestBlock: Block | null = await this.viemComponent.getBlock('finalized');
    if (!latestBlock) {
      logger.error({ method: 'milestones_checkFinality', error: 'Failed to get latest finalized block' });
      return {
        status: 'error',
        message: 'Failed to get latest finalized block',
      };
    }
    await Promise.allSettled(
      minedTxs.map(async (minedTx) => {
        const { transaction_id: transactionId, tx_hash: txHash, service_id: serviceId } = minedTx;
        const tx = await this.viemComponent.getTransaction(txHash as `0x${string}`);
        if (!tx.success) {
          if (!tx.error.includes('TransactionNotFoundError')) {
            logger.error({ method: 'checkFinality', message: 'Unintended error when get Transaction' });
            return;
          }
          logger.warn({
            method: 'checkFinality',
            log: `Reorged transaction detected: transaction Id : ${transactionId}, tx hash : ${txHash}`,
          });
          const txQueue = await this.transactionQueuesRepository.selectQueueByTransactionId(transactionId);
          if (txQueue.length === 0) {
            logger.error({
              method: 'retryReorgedTransaction',
              log: 'cannot find transaction queue from transaction id',
            });
            return null;
          }
          const {
            nft_contract_address: nftContractAddress,
            to_address: nftTo,
            tx_type: txType,
            token_id: tokenId,
            queue_id: queueId,
          } = txQueue[0];
          if (tokenId === undefined) {
            throw new InternalServerError('NFT details are missing');
          }
          const txResponse = await this.retryReorgedTransaction(minedTx);
          if (txResponse && txType === TxType.MINT_REWARD) {
            await this.recreateDeliveryNft(
              txResponse.hash,
              serviceId,
              queueId,
              nftContractAddress,
              tokenId.toString(),
              nftTo,
            );
          }
          return;
        }
        const isFinalized = await this.checkFinality(txHash as `0x${string}`, tx.value, latestBlock);

        if (!isFinalized) return;
        await db.transaction().execute(async (trx: Transaction<Database>) => {
          await this.transactionsRepository.updateTransactionStatus(transactionId, TransactionStatus.CONFIRMED, trx);
          await this.nftsFirestoreRepository.updateTransactionConfirmation(txHash, true);
        });
      }),
    );
    return {
      status: 'success',
      message: 'Transaction finalization status updated',
    };
  }

  private async checkFinality(txHash: `0x${string}`, tx: ViemTransaction, latestBlock: Block): Promise<boolean> {
    if (latestBlock.number !== null && tx.blockNumber !== null && latestBlock.number > tx.blockNumber) {
      logger.info({
        method: 'milestones_checkFinality',
        txHash,
        latestBlockNumber: latestBlock.number,
        txBlockNumber: tx.blockNumber,
      });
      return true;
    } else {
      logger.info({
        method: 'milestones_checkFinality',
        txHash,
        latestBlockNumber: latestBlock.number,
        txBlockNumber: tx.blockNumber,
      });
      return false;
    }
  }

  private async retryReorgedTransaction(tx: TransactionsEntity): Promise<TransactionResponse | null> {
    const serviceInfo = await this.serviceInfoRepository.getServiceById(tx.service_id);
    if (!serviceInfo) throw new ValidationError('Service not found');
    const { tenant_id: tenantId, modular_contract_id: modularContractId } = serviceInfo;
    const modularContract = await this.nftContractsRepository.selectNftContractById(modularContractId);
    const modularContractAddress = modularContract?.nft_contract_address;
    if (!modularContractAddress) throw new ValidationError('Modular contract not found');

    const txResponse = await this.transactionComponent.sendRawTransaction(
      tenantId,
      modularContractAddress,
      tx.encoded_data,
      tx.nonce,
    );
    if (!txResponse) {
      throw new InternalServerError('Transaction response is null. Aborting transaction retry.');
    }

    await db.transaction().execute(async (trx: Transaction<Database>) => {
      await this.transactionsRepository.updateTransactionHash(tx.transaction_id, txResponse.hash, trx);
      await this.transactionsRepository.updateTransactionStatus(tx.transaction_id, TransactionStatus.EXECUTED, trx);
    });

    return txResponse;
  }

  private async recreateDeliveryNft(
    txHash: string,
    serviceId: string,
    queueId: string,
    nftContractAddress: string,
    tokenId: string,
    nftTo: string,
  ): Promise<void> {
    const deliveryImageUrl = await this.nftContractsRepository.selectDeliveryImageUrl(nftContractAddress);
    const nftContract = await this.nftContractsRepository.selectNftContractAndTypeByAddress(nftContractAddress);
    const account = await this.accountRepository.selectAccountByTokenBoundAddress(nftTo, serviceId);
    if (!account) {
      logger.error({ method: 'recreateDeliveryNft', messaging: 'Account not found or token bound address is missing' });
      return;
    }
    await this.deliveryNftsFirestoreRepository.insertDeliveryNft(
      nftContract.nft_type,
      nftContractAddress,
      tokenId,
      nftTo,
      serviceId,
      account.account_id,
      queueId,
      deliveryImageUrl || '',
      txHash,
    );
  }

  private async handleTransactionType(txDetail: TxDetail) {
    const { nftFrom, nftTo, nftContractAddress, nftTokenId, nftAmount } = txDetail;

    const nftTokenIdString = nftTokenId?.toString();
    if (!nftContractAddress || !nftTokenIdString || !nftAmount || !nftFrom || !nftTo) {
      logger.error({
        method: 'handleTransactionType',
        error: 'Required NFT data is missing',
      });
      return;
    }
    const { nftType, serviceId } = await this.getNftContractTypeAndServiceId(nftContractAddress);
    if (!nftType) {
      logger.error({ method: 'handleTransactionType', error: 'nftType is not found' });
      return;
    }

    const txUpdateInfo: FirestoreNftsUpdateInfo = {
      serviceId,
      tokenId: nftTokenIdString,
      from: nftFrom,
      to: nftTo,
      amount: nftAmount,
      contractAddress: nftContractAddress,
      transactionHash: txDetail.transactionHash,
      nftType,
      timestamp: txDetail.timestamp,
      isConfirmed: txDetail.isConfirmed,
    };

    if (this.isMinting(nftFrom)) {
      return this.processMint(txUpdateInfo, nftType);
    }
    if (this.isBurning(nftTo)) {
      return this.processBurn(txUpdateInfo);
    }
    return this.processTransfer(txUpdateInfo, nftType);
  }

  private async processMint(txUpdateInfo: FirestoreNftsUpdateInfo, nftContractType: NftType) {
    logger.info({ method: 'processMint' });
    const { to, serviceId, contractAddress, tokenId, amount } = txUpdateInfo;

    // MEMBERSHIPの場合はfirestore保存をしない
    if (nftContractType === NftType.MEMBERSHIP) {
      logger.info({ method: 'processMint', log: 'Membership NFT' });
      return;
    }

    const account = await this.getAccountByAddress(to, serviceId);
    if (!account) {
      logger.error({ method: 'processMint', log: 'account of nft to Address is not found' });
      return;
    }

    const nftMetadata = await this.getNftMetadata(contractAddress);
    if (!nftMetadata) {
      logger.info({ method: 'processMint', log: 'nftMetadata is not found' });
      return;
    }

    const nftData = this.buildNftDocument(
      txUpdateInfo,
      account.account_id,
      nftContractType,
      nftMetadata,
      TransferTxType.MINT,
    );

    // CONTENT / CERTIFICATEはMintをFirestoreに保存
    if (nftContractType === NftType.CONTENT || nftContractType === NftType.CERTIFICATE) {
      await this.nftsFirestoreRepository.insertNft(nftData);
    }

    // COUPONはMint以外のTXもケア
    if (nftContractType === NftType.COUPON) {
      const toNftDoc = await this.nftsFirestoreRepository.selectNftByAccountId(
        account.account_id,
        contractAddress,
        tokenId,
      );

      if (!toNftDoc.empty) {
        const toDoc = toNftDoc.docs[0];
        await toDoc.ref.update({
          amount: toDoc.data().amount + Number(amount),
          updated_at: new Date(),
          transactions: {
            ...toDoc.data().transactions,
            ...nftData.transactions,
          },
        });
      } else {
        await this.nftsFirestoreRepository.insertNft(nftData);
      }
    }

    await this.deleteDeliveryNft(
      txUpdateInfo.transactionHash,
      txUpdateInfo.contractAddress,
      account.account_id,
      txUpdateInfo.serviceId,
    );
  }

  private async processBurn(txUpdateInfo: FirestoreNftsUpdateInfo) {
    const { contractAddress, tokenId, from, serviceId } = txUpdateInfo;
    const account = await this.getAccountByAddress(from, serviceId);
    if (!account) return;

    const nftDoc = await this.nftsFirestoreRepository.selectNftByAccountId(
      account.account_id,
      contractAddress,
      tokenId,
    );

    if (nftDoc.empty) return;

    const doc = nftDoc.docs[0];
    if (this.isCoupon(doc)) {
      await this.updateOrDeleteCoupon(txUpdateInfo, account.account_id, TransferTxType.BURN);
    } else {
      await doc.ref.delete();
    }
  }

  private async processTransfer(txUpdateInfo: FirestoreNftsUpdateInfo, nftContractType: NftType) {
    logger.info({ method: 'processTransfer' });
    const { from, to, serviceId } = txUpdateInfo;

    const fromAccount = await this.getAccountByAddress(from, serviceId);
    const toAccount = await this.getAccountByAddress(to, serviceId);

    if (!fromAccount && !toAccount) {
      logger.info({ method: 'processTransfer', log: 'Both fromAccount and toAccount are not found' });
      return;
    }

    if (!fromAccount) {
      return this.createNftForAccount(txUpdateInfo, nftContractType, toAccount?.account_id);
    }

    if (nftContractType === NftType.CONTENT || nftContractType === NftType.CERTIFICATE) {
      if (!toAccount) {
        return this.deleteNftFromAccount(txUpdateInfo, fromAccount.account_id);
      } else {
        return this.handleErc721Transfer(txUpdateInfo, fromAccount.account_id, toAccount.account_id);
      }
    }

    if (nftContractType === NftType.COUPON) {
      if (!toAccount) {
        return this.updateOrDeleteCoupon(txUpdateInfo, fromAccount.account_id, TransferTxType.TRANSFER);
      }
      return this.handleErc1155Transfer(txUpdateInfo, fromAccount.account_id, toAccount.account_id);
    }
  }

  private async createNftForAccount(
    txUpdateInfo: FirestoreNftsUpdateInfo,
    nftContractType: NftType,
    accountId?: string,
  ) {
    if (!accountId) {
      logger.info({
        method: 'createNftForAccount',
        log: `Account ID is missing when creating NFT for transaction: ${txUpdateInfo.transactionHash}`,
      });
      return;
    }

    const nftMetadata = await this.getNftMetadata(txUpdateInfo.contractAddress);
    if (!nftMetadata) {
      logger.info({
        method: 'createNftForAccount',
        log: `NFT Metadata not found for contract address: ${txUpdateInfo.contractAddress}`,
      });
      return;
    }

    const nftData = this.buildNftDocument(
      txUpdateInfo,
      accountId,
      nftContractType,
      nftMetadata,
      TransferTxType.TRANSFER,
    );
    logger.info({
      method: 'createNftForAccount',
      log: `Creating NFT for accountId: ${accountId}, transactionHash: ${txUpdateInfo.transactionHash}`,
    });
    await this.nftsFirestoreRepository.insertNft(nftData);
  }

  private async deleteNftFromAccount(txUpdateInfo: FirestoreNftsUpdateInfo, accountId: string) {
    const nftDoc = await this.nftsFirestoreRepository.selectNftByAccountId(
      accountId,
      txUpdateInfo.contractAddress,
      txUpdateInfo.tokenId,
    );

    if (!nftDoc.empty) {
      const doc = nftDoc.docs[0];

      logger.info({
        method: 'deleteNftFromAccount',
        log: `Deleting NFT for accountId: ${accountId}, tokenId: ${txUpdateInfo.tokenId}`,
      });
      await doc.ref.delete();
    } else {
      logger.info({
        method: 'deleteNftFromAccount',
        log: `NFT not found for accountId: ${accountId}, tokenId: ${txUpdateInfo.tokenId}, transactionHash: ${txUpdateInfo.transactionHash}`,
      });
    }
  }

  private async updateOrDeleteCoupon(
    txUpdateInfo: FirestoreNftsUpdateInfo,
    accountId: string,
    transactionType: TransferTxType,
  ) {
    const nftDoc = await this.nftsFirestoreRepository.selectNftByAccountId(
      accountId,
      txUpdateInfo.contractAddress,
      txUpdateInfo.tokenId,
    );
    const doc = nftDoc.docs[0];
    const amount = doc.data().amount - Number(txUpdateInfo.amount);
    if (amount <= 0) {
      await doc.ref.delete();
    } else {
      await doc.ref.update({
        amount,
        updated_at: new Date(),
        transactions: {
          ...doc.data().transactions,
          transactions: {
            ...doc.data().transactions,
            [txUpdateInfo.transactionHash]: {
              from: txUpdateInfo.from,
              to: txUpdateInfo.to,
              transactionDate: txUpdateInfo.timestamp,
              transactionType,
              isConfirmed: txUpdateInfo.isConfirmed,
            },
          },
        },
      });
    }
  }

  private async handleErc721Transfer(
    txUpdateInfo: FirestoreNftsUpdateInfo,
    fromAccountId: string,
    toAccountId: string,
  ) {
    logger.info({ method: 'handleErc721Transfer' });
    const nftDoc = await this.nftsFirestoreRepository.selectNftByAccountId(
      fromAccountId,
      txUpdateInfo.contractAddress,
      txUpdateInfo.tokenId,
    );

    if (!nftDoc.empty) {
      logger.info({ method: 'handleErc721Transfer', log: 'nftDoc.empty false' });
      const doc = nftDoc.docs[0];
      await doc.ref.update({
        account_id: toAccountId,
        updated_at: new Date(),
        transactions: {
          ...doc.data().transactions,
          [txUpdateInfo.transactionHash]: {
            from: txUpdateInfo.from,
            to: txUpdateInfo.to,
            transactionDate: txUpdateInfo.timestamp,
            transactionType: TransferTxType.TRANSFER,
            isConfirmed: txUpdateInfo.isConfirmed,
          },
        },
      });
    } else {
      logger.info({ method: 'handleErc721Transfer', log: 'nftDoc.empty true' });
      const nftMetadata = await this.getNftMetadata(txUpdateInfo.contractAddress);
      const nftData = this.buildNftDocument(
        txUpdateInfo,
        toAccountId,
        txUpdateInfo.nftType,
        nftMetadata!,
        TransferTxType.TRANSFER,
      );
      await this.nftsFirestoreRepository.insertNft(nftData);
    }
  }

  private async handleErc1155Transfer(
    txUpdateInfo: FirestoreNftsUpdateInfo,
    fromAccountId: string,
    toAccountId: string,
  ) {
    const fromNftDoc = await this.nftsFirestoreRepository.selectNftByAccountId(
      fromAccountId,
      txUpdateInfo.contractAddress,
      txUpdateInfo.tokenId,
    );

    if (!fromNftDoc.empty) {
      const doc = fromNftDoc.docs[0];
      const updatedAmount = doc.data().amount - Number(txUpdateInfo.amount);

      if (updatedAmount <= 0) {
        await doc.ref.delete();
      } else {
        await doc.ref.update({
          amount: updatedAmount,
          updated_at: new Date(),
          transactions: {
            ...doc.data().transactions,
            [txUpdateInfo.transactionHash]: {
              from: txUpdateInfo.from,
              to: txUpdateInfo.to,
              transactionDate: txUpdateInfo.timestamp,
              transactionType: TransferTxType.TRANSFER,
              isConfirmed: txUpdateInfo.isConfirmed,
            },
          },
        });
      }
    }

    const toNftDoc = await this.nftsFirestoreRepository.selectNftByAccountId(
      toAccountId,
      txUpdateInfo.contractAddress,
      txUpdateInfo.tokenId,
    );

    if (!toNftDoc.empty) {
      const toDoc = toNftDoc.docs[0];
      await toDoc.ref.update({
        amount: toDoc.data().amount + Number(txUpdateInfo.amount),
        updated_at: new Date(),
        transactions: {
          ...toDoc.data().transactions,
          [txUpdateInfo.transactionHash]: {
            from: txUpdateInfo.from,
            to: txUpdateInfo.to,
            transactionDate: txUpdateInfo.timestamp,
            transactionType: TransferTxType.TRANSFER,
            isConfirmed: txUpdateInfo.isConfirmed,
          },
        },
      });
    } else {
      const nftMetadata = await this.getNftMetadata(txUpdateInfo.contractAddress);
      const nftData = this.buildNftDocument(
        txUpdateInfo,
        toAccountId,
        txUpdateInfo.nftType,
        nftMetadata!,
        TransferTxType.TRANSFER,
      );
      await this.nftsFirestoreRepository.insertNft(nftData);
    }
  }

  private buildNftDocument(
    txUpdateInfo: FirestoreNftsUpdateInfo,
    accountId: string,
    nftContractType: NftType,
    nftMetadata: NftBaseMetadatasEntity,
    transactionType: TransferTxType,
  ): FirestoreNftsDocument {
    const nowISO = this.getCurrentISODate();
    const baseMetadataUri = process.env.METADATA_URL;
    const metadataUri = `${baseMetadataUri}/${nftMetadata.base_metadata_id}/${txUpdateInfo.tokenId}`;
    return {
      chainId: config.chainId,
      contractAddress: txUpdateInfo.contractAddress,
      tokenId: txUpdateInfo.tokenId,
      amount: Number(txUpdateInfo.amount),
      accountId,
      serviceId: txUpdateInfo.serviceId,
      contractType: nftContractType,
      metadataJson: nftMetadata.metadata || {},
      metadataUri: metadataUri || '',
      metadataCachedAt: nowISO,
      updatedAt: nowISO,
      createdAt: nowISO,
      transactions: {
        [txUpdateInfo.transactionHash]: {
          from: txUpdateInfo.from,
          to: txUpdateInfo.to,
          transactionDate: txUpdateInfo.timestamp,
          transactionType: transactionType,
          isConfirmed: txUpdateInfo.isConfirmed,
        },
      },
    };
  }

  private async getAccountByAddress(address: string, serviceId: string) {
    return this.accountRepository.selectAccountByTokenBoundAddress(address, serviceId);
  }

  private async getNftContractTypeAndServiceId(
    contractAddress: string,
  ): Promise<{ nftType: NftType | undefined; serviceId: string }> {
    const result = this.nftContractsRepository.selectNftContractAndTypeByAddress(contractAddress);
    return { nftType: (await result).nft_type, serviceId: (await result).service_id };
  }

  private async getNftMetadata(contractAddress: string) {
    return this.nftBaseMetadatasRepository.selectDataByContractAddress(contractAddress);
  }

  private isMinting(from: string) {
    return from === '0x0000000000000000000000000000000000000000';
  }

  private isBurning(to: string) {
    return to === '0x0000000000000000000000000000000000000000';
  }

  private isCoupon(doc: FirebaseFirestore.QueryDocumentSnapshot) {
    return doc.data().contract_type === NftType.COUPON;
  }

  private getCurrentISODate(): string {
    return new Date().toISOString();
  }

  private async deleteDeliveryNft(
    transactionHash: string,
    contractAddress: string,
    accountId: string,
    serviceId: string,
  ) {
    await this.deliveryNftsFirestoreRepository.deleteDeliveryNft(
      transactionHash,
      contractAddress,
      accountId,
      serviceId,
    );
  }
}
