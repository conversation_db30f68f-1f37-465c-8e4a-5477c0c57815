import { injectable, inject } from 'tsyringe';
import { authenticator } from 'otplib';
import { AccountRepository } from '../repositories/accountRepository';
import { NotFoundError } from '../errors/notFoundError';
import { ValidationError } from '../errors/validationError';
import { EmailOtpResponse } from '../dtos/accounts/schemas';
import { EmailDeliveryService } from './emailDeliveryService';
import { RedisComponent } from '../components/redisComponent';
import { LanguageCode } from '../enum/languageCode';
import { EmailTemplateType } from '../constants/emailTemplates';

import * as crypto from 'crypto';
import { logger } from '../utils/logger';

interface OtpData {
  otpHash: string;
  attempts: number;
}

@injectable()
export class OtpService {
  private readonly OTP_EXPIRY_IN_SECONDS = 300;
  private readonly MAX_ATTEMPTS = 3;

  constructor(
    @inject(AccountRepository) private accountRepository: AccountRepository,
    @inject(EmailDeliveryService) private emailDeliveryService: EmailDeliveryService,
    @inject(RedisComponent) private redisComponent: RedisComponent
  ) {}

  private generateOtpKey(accountId: string, email: string): string {
    return `otp:${accountId}:${email}`;
  }

  public async generateEmailOtp( accountId: string, serviceId: string, lang: LanguageCode,email: string): Promise<EmailOtpResponse> {
    const accountCustomField = await this.accountRepository.findAccountCustomFieldByEmail(accountId, serviceId, email);
    if (!accountCustomField) throw new NotFoundError('Email not found for this account');
    if (accountCustomField.verified) throw new ValidationError('Email already verified');
    
    authenticator.options = { 
      digits: 6,
      step: this.OTP_EXPIRY_IN_SECONDS
    };
    
    const secret = authenticator.generateSecret();
    const otp = authenticator.generate(secret);
    
    const otpHash = this.hashOtp(otp);
    const otpData: OtpData = { otpHash, attempts: 0 };
    
    const key = this.generateOtpKey(accountId, email);
    await this.redisComponent.set<OtpData>(key, otpData, this.OTP_EXPIRY_IN_SECONDS);
    
    await this.emailDeliveryService.sendTemplatedEmail(
      email,
      EmailTemplateType.OTP_VERIFICATION,
      lang,
      { otp }
    );
    
    return { email };
  }

  public async verifyOtp(
    accountId: string, 
    serviceId: string, 
    email: string, 
    otp: string
  ): Promise<EmailOtpResponse> {
    const accountCustomField = await this.accountRepository.findAccountCustomFieldByEmail(accountId, serviceId, email);
    if (!accountCustomField) {
      throw new NotFoundError('Email not found for this account');
    }
    
    const key = this.generateOtpKey(accountId, email);
    const otpData = await this.redisComponent.get<OtpData>(key);
    
    if (!otpData) {
      throw new NotFoundError('Invalid or expired verification code');
    }
    
    // Max Attempts
    if (otpData.attempts >= this.MAX_ATTEMPTS) {
      logger.info(`Max attempts reached for ${key}`);
      throw new ValidationError('Maximum verification attempts reached. Please request a new code.');
    }
    
    const providedOtpHash = this.hashOtp(otp);
    const isValid = otpData.otpHash === providedOtpHash;
    
    const updatedOtpData: OtpData = {
      otpHash: otpData.otpHash,
      attempts: otpData.attempts + 1
    };
    
    await this.redisComponent.set<OtpData>(key, updatedOtpData, this.OTP_EXPIRY_IN_SECONDS);
    
    if (isValid) {
      await this.accountRepository.updateCustomFieldVerificationStatus(
        accountId,
        serviceId,
        accountCustomField.custom_field_id,
        email,
        true
      );
    } else {
      throw new ValidationError('Invalid verification code');
    }

    return { email };
  }

  private hashOtp(otp: string): string {
    return crypto.createHash('sha256').update(otp).digest('hex');
  }
}
