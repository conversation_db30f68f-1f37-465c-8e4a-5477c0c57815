import { ethers } from 'ethers';
import { inject, injectable } from 'tsyringe';
import { NotFoundError } from '../errors/notFoundError';
import { NftContractsRepository } from '../repositories/nftContractsRepository';
import { VaultKeyRepository } from '../repositories/vaultKeyRepository';
import { TransactionQueuesRepository } from '../repositories/transactionQueuesRepository';
import { v4 as uuidv4 } from 'uuid';
import { NftType } from '../enum/nftType';
import { ValidationError } from '../errors/validationError';
import { ServiceInfoRepository } from '../repositories/serviceInfoRepository';
import { TxType } from '../enum/txType';
import { BadGatewayError } from '../errors/badGateway';
import { logger } from '../utils/logger';
import { TransactionQueueStatus } from '../enum/transactionQueueStatus';
import { TransactionData } from '../dtos/nfts/schemas';
import { CloudTaskComponent } from '../components/cloudTaskComponent';
import { DeliveryNftsFirestoreRepository } from '../repositories/deliveryNftsFirestoreRepository';

interface NftContractDetails {
  abi: ethers.InterfaceAbi;
  contractAddress: string;
  implementAddress?: string;
  deliveryImageUrl?: string;
}

@injectable()
export class NftMintService {
  constructor(
    @inject('NftContractsRepository') private nftContractsRepository: NftContractsRepository,
    @inject('TransactionQueuesRepository') private transactionQueuesRepository: TransactionQueuesRepository,
    @inject('VaultKeyRepository') private vaultKeyRepository: VaultKeyRepository,
    @inject('ServiceInfoRepository') private serviceInfoRepository: ServiceInfoRepository,
    @inject('DeliveryNftsFirestoreRepository') private deliveryNftsFirestoreRepository: DeliveryNftsFirestoreRepository,
  ) {}

  public async mint(
    serviceId: string,
    accountId: string,
    txType: TxType,
    toAddress: string,
    nftContractId: string,
    nftType: NftType,
    nftTokenId?: number,
  ): Promise<TransactionData> {
    const { contractAddress, implementAddress } = await this.getNftContractAbiAndAddress(nftContractId);
    logger.info({ transaction: 'mint', contract: contractAddress, implement: implementAddress });

    if (nftType === NftType.COUPON && nftTokenId === undefined) {
      throw new ValidationError('NFT token ID is required for coupon type');
    }
    if (nftType !== NftType.COUPON && nftTokenId !== undefined) {
      throw new ValidationError('NFT token ID should not be provided for non-coupon types');
    }
    if (nftType === NftType.COUPON && !implementAddress) {
      throw new BadGatewayError('Contract deploy transaction is no available');
    }

    const serviceInfo = await this.serviceInfoRepository.getServiceById(serviceId);
    if (!serviceInfo) throw new ValidationError('Service not found');
    const tenantId = serviceInfo.tenant_id;
    logger.info({ transaction: 'mint', tenant: tenantId });

    const vaultKey = await this.vaultKeyRepository.getVaultKeyId(tenantId);
    if (!vaultKey) throw new NotFoundError('Vault key not found');
    logger.info({ transaction: 'mint', vault_wallet_address: vaultKey.vault_wallet_address });

    const nftContract = await this.nftContractsRepository.selectNftContractById(nftContractId);
    if (!nftContract) {
      throw new NotFoundError(`NFT contract with ID ${nftContractId} not found.`);
    }
    const tokenId = nftContract.next_token_id ?? 0;
    await this.nftContractsRepository.updateNextTokenId(nftContractId, tokenId + 1);


    // add queue.
    const queueId = uuidv4();
    await this.transactionQueuesRepository.insertQueue({
      queue_id: queueId,
      service_id: serviceId,
      from_address: vaultKey.vault_wallet_address,
      to_address: toAddress,
      status: TransactionQueueStatus.PENDING,
      tx_type: txType,
      nft_type: nftType,
      nft_contract_address: contractAddress,
      token_id: nftTokenId ?? tokenId,
      created_date: new Date(),
    });

    const cloudTask = new CloudTaskComponent();
    await cloudTask.enqueueMintTask();

    await this.deliveryNftsFirestoreRepository.insertDeliveryNft(
      nftType,
      contractAddress,
      tokenId.toString(),
      toAddress,
      serviceId,
      accountId,
      queueId,
      nftContract.delivery_image_url ?? '',
    );

    const response: TransactionData = {
      queueId,
      contractAddress,
      tokenBoundAccountAddress: toAddress,
    };
    return response;
  }

  public async getNftContractAbiAndAddress(nftContractId: string): Promise<NftContractDetails> {
    const contract = await this.nftContractsRepository.selectNftContractById(nftContractId);
    if (!contract) throw new ValidationError(`NFT contract with ID ${nftContractId} not found.`);
    if (!contract.nft_contract_abi) throw new ValidationError('ABI not provided for the contract.');

    const abi = contract.nft_contract_abi as unknown as ethers.InterfaceAbi;
    return {
      abi,
      contractAddress: contract.nft_contract_address as string,
      implementAddress: contract.nft_contract_implementation_address,
    };
  }
}
