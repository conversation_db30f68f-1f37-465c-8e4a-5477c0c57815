import { inject, injectable } from 'tsyringe';
import { v4 as uuidv4 } from 'uuid';
import { db } from '../db/database';
import { NotificationList } from '../dtos/accounts/schemas';
import { NotificationTranslation } from '../dtos/notifications/schemas';
import { InternalServerError } from '../errors/internalServerError';
import { NotFoundError } from '../errors/notFoundError';
import { AccountRepository } from '../repositories/accountRepository';
import { NotificationRepository } from '../repositories/notificationRepository';
import { NotificationInfo } from '../responsedto/accountResponse';
import { logger } from '../utils/middleware/loggerMiddleware';

@injectable()
export class NotificationService {
  constructor(
    @inject('AccountRepository') private accountRepository: AccountRepository,
    @inject('NotificationRepository') private notificationRepository: NotificationRepository,
  ) {}

  async getNotifications(accountId: string, serviceId: string, locale: string): Promise<NotificationList> {
    const account = await this.accountRepository.selectAccountById(accountId, serviceId);

    if (!account) {
      throw new NotFoundError('Account not found');
    }

    let accountNotifications, globalNotifications;
    try {
      [accountNotifications, globalNotifications] = await Promise.all([
        this.notificationRepository.selectAccountNotification(accountId, serviceId, locale),
        this.notificationRepository.selectGlobalNotification(serviceId, locale),
      ]);
    } catch (error) {
      throw new InternalServerError(`Failed to retrieve notifications: ${error}`);
    }

    const response = {
      notifications: [...accountNotifications, ...globalNotifications]
        .map((notification) => {
          const item: NotificationInfo = {
            notificationId: notification.notification_id,
            title: notification.notification_title,
            text: notification.notification_text,
            broadcastDate: notification.broadcast_date.toISOString(),
          };
          return item;
        })
        .sort((a, b) => {
          return new Date(a.broadcastDate || 0).getTime() - new Date(b.broadcastDate || 0).getTime();
        }),
    };

    return response;
  }

  async createNotification(
    serviceId: string,
    notificationTranslations: NotificationTranslation[],
    accountIds: string[] | undefined,
    bloadcastDate: Date,
  ): Promise<void> {
    logger.info({ method: 'createNotification', serviceId, notificationTranslations, accountIds, bloadcastDate });

    const notificationId = uuidv4();

    const notificationData = {
      notification_id: notificationId,
      service_id: serviceId,
      broadcast_date: bloadcastDate,
    };
    const notificationTranslationsData = notificationTranslations.map((notificationTranslation) => {
      return {
        notification_translation_id: uuidv4(),
        notification_id: notificationId,
        service_id: serviceId,
        language: notificationTranslation.language,
        notification_title: notificationTranslation.title,
        notification_text: notificationTranslation.text,
      };
    });

    return await db.transaction().execute(async (trx) => {
      try {
        logger.info({ process: 'insertNotifications', serviceId, notificationData });
        await this.notificationRepository.insertNotifications(serviceId, [notificationData], trx);

        logger.info({ process: 'insertNotificationTranslations', serviceId, notificationTranslationsData });
        await this.notificationRepository.insertNotificationTranslations(serviceId, notificationTranslationsData, trx);

        if (!!accountIds && accountIds.length > 0) {
          const accountNotificationsData = accountIds.map((accountId) => {
            return {
              account_notification_id: uuidv4(),
              account_id: accountId,
              service_id: serviceId,
              notification_id: notificationId,
            };
          });
          logger.info({ process: 'insertAccountNotifications', serviceId, accountNotificationsData });
          await this.notificationRepository.insertAccountNotifications(serviceId, accountNotificationsData, trx);
        } else {
          const globalNotificationsData = {
            global_notification_id: uuidv4(),
            service_id: serviceId,
            notification_id: notificationId,
          };
          logger.info({ process: 'insertGlobalNotifications', serviceId, globalNotificationsData });
          await this.notificationRepository.insertGlobalNotifications(serviceId, [globalNotificationsData], trx);
        }
      } catch (error) {
        throw new Error(`An error occurred while inserting data: ${error}`);
      }
    });
  }
}
