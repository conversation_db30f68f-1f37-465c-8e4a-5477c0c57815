import { inject, injectable } from 'tsyringe';
import { v4 as uuidv4 } from 'uuid';
import { A<PERSON>, encodeFunctionData } from 'viem';
import { SendUserOperationId, UserOperationCallData } from '../dtos/user_operations/schemas';
import { NotFoundError } from '../errors/notFoundError';
import { ValidationError } from '../errors/validationError';
import { AccountRepository } from '../repositories/accountRepository';
import { NftContractsRepository } from '../repositories/nftContractsRepository';
import { TokenBoundAccountImplementationRepository } from '../repositories/tokenBoundAccountImplementationsRepository';
import { UserOperationQueuesRepository } from '../repositories/userOperationQueuesRepository';
import { logger } from '../utils/middleware/loggerMiddleware';
import { MetadataService } from './metadataService';

@injectable()
export class UserOperationService {
  constructor(
    @inject('NftContractsRepository')
    private nftContractRepository: NftContractsRepository,
    @inject('AccountRepository')
    private accountRepository: AccountRepository,
    @inject('UserOperationQueuesRepository')
    private userOperationQueuesRepository: UserOperationQueuesRepository,
    @inject('TokenBoundAccountImplementationRepository')
    private tokenBoundAccountImplementationRepository: TokenBoundAccountImplementationRepository,
    @inject('MetadataService')
    private metadataService: MetadataService,
  ) {}

  async prepareForConsumption(
    serviceId: string,
    contractAddress: string,
    tokenId: number,
  ): Promise<UserOperationCallData> {
    // fetch contract detail info
    const nftContractEntity = await this.nftContractRepository.selectNftContractAddressAndNftContractAbiUrl(
      contractAddress,
      serviceId,
    );

    // Check expiration date in metadata
    const metadata = await this.metadataService.getParsedMetadataFromBaseMetadata(contractAddress, tokenId);
    const toDate = (metadata.attributes ?? []).findLast((attr) => attr.trait_type === 'to_date')?.value;
    if (!toDate) throw new ValidationError('Illegal format NFT is used');
    const expireDate = new Date((toDate as string | undefined)!);
    const now = new Date();
    if (expireDate.getTime() < now.getTime()) throw new ValidationError('Coupon expired');
    logger.info({
      method: 'prepareForConsumption',
      expireDtae: expireDate,
      isExpire: expireDate.getTime() < now.getTime(),
    });
    // encode coupon use transaction
    const couponUseTransactionData = this.encodeTransactionData(nftContractEntity.nft_contract_abi, 'useCoupon', [
      tokenId,
      1, // use amount
    ]);
    logger.info({ method: 'prepareForConsumption', couponUseTransactionData: couponUseTransactionData });

    // encode tba execute transaction
    const tbaAbi = await this.tokenBoundAccountImplementationRepository.selectAbiByServiceId(serviceId);
    if (!tbaAbi) throw new NotFoundError('Token bound account setting is not sotred');

    const tbaExecuteTransactionData = this.encodeTransactionData(tbaAbi, 'execute', [
      contractAddress,
      0,
      couponUseTransactionData,
      0,
    ]);
    logger.info({ method: 'prepareForConsumption', tbaExecuteTransactionData: tbaExecuteTransactionData });

    return { callData: tbaExecuteTransactionData };
  }

  private encodeTransactionData(jsonAbi: object, functionName: string, args: unknown[]): string {
    return encodeFunctionData({
      abi: jsonAbi as Abi,
      functionName: functionName,
      args: args,
    });
  }

  public async storeUserOperationInfo(
    serviceId: string,
    accountId: string,
    uoHash: string,
  ): Promise<SendUserOperationId> {
    const account = await this.accountRepository.selectAccountById(accountId, serviceId);
    if (!account || !account.token_bound_account_address) throw new NotFoundError('Account is not found');

    const tokenBoundAccountAddress = account.token_bound_account_address;
    const operationId = uuidv4();
    await this.userOperationQueuesRepository.insertQueue({
      operation_id: operationId,
      from_address: tokenBoundAccountAddress,
      hex_encoded_user_operation_data: undefined,
      signature: undefined,
      tx_hash: undefined,
      uo_hash: uoHash,
      status: 'EXECUTED',
    });

    return { operationId };
  }
}
