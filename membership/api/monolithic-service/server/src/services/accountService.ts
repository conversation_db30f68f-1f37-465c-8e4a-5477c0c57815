import { inject, injectable } from 'tsyringe';
import { v4 as uuidv4 } from 'uuid';
import { FirebaseComponent } from '../components/firebaseComponent';
import { LineComponent } from '../components/lineComponent';
import { AccountStatus } from '../enum/accoutStatus';
import { RewardType } from '../enum/rewardType';
import { RewardUsageStatus } from '../enum/rewardUsageStatus';
import { NotFoundError } from '../errors/notFoundError';
import { AccountRepository } from '../repositories/accountRepository';
import { ActionRepository } from '../repositories/actionRepository';
import { ClaimedRewardRepository } from '../repositories/claimedRewardRepository';
import { QuestActivityRepository } from '../repositories/questActivityRepository';
import { QuestRepository } from '../repositories/questRepository';
import { ServiceInfoRepository } from '../repositories/serviceInfoRepository';
import { TransactionQueuesRepository } from '../repositories/transactionQueuesRepository';
import { VaultKeyRepository } from '../repositories/vaultKeyRepository';
import { NftsService } from './nftsService';
import { UserService } from './userService';

import { Transaction } from 'kysely';
import { CloudTaskComponent } from '../components/cloudTaskComponent';
import { Database, db } from '../db/database';
import { Account, ActivityHistory, NotificationList, RewardHistory, Status } from '../dtos/accounts/schemas';
import { RewardActionType } from '../enum/actionType';
import { LanguageCode } from '../enum/languageCode';
import { NftType } from '../enum/nftType';
import { TransactionQueueStatus } from '../enum/transactionQueueStatus';
import { TxType } from '../enum/txType';
import { ConflictError } from '../errors/conflictError';
import { InternalServerError } from '../errors/internalServerError';
import { NftContractsRepository } from '../repositories/nftContractsRepository';
import { logger } from '../utils/logger';
import { MetadataService } from './metadataService';
import { NotificationService } from './notificationService';
import axios from 'axios';

@injectable()
export class AccountService {
  constructor(
    @inject('AccountRepository') private accountRepository: AccountRepository,
    @inject('ActionRepository') private actionRepository: ActionRepository,
    @inject('QuestRepository') private questRepository: QuestRepository,
    @inject('QuestActivityRepository')
    private questActivityRepository: QuestActivityRepository,
    @inject('ClaimedRewardRepository')
    private claimedRewardRepository: ClaimedRewardRepository,
    @inject('UserService') private userService: UserService,
    @inject('ServiceInfoRepository') private serviceInfoRepository: ServiceInfoRepository,
    @inject('LineComponent') private lineComponent: LineComponent,
    @inject('FirebaseComponent') private firebaseComponent: FirebaseComponent,
    @inject('NftsService') private nftsService: NftsService,
    @inject('NftContractsRepository') private nftContractsRepository: NftContractsRepository,
    @inject('VaultKeyRepository') private vaultKeyRepository: VaultKeyRepository,
    @inject('TransactionQueuesRepository') private transactionQueuesRepository: TransactionQueuesRepository,
    @inject('MetadataService') private metadataService: MetadataService,
    @inject('NotificationService') private notificationService: NotificationService,
  ) {}

  async createAccount(authorization: string, lineIdToken: string, serviceId: string): Promise<Account> {
    const clientId = await this.serviceInfoRepository.selectLineChannelId(serviceId);
    if (!clientId) throw new NotFoundError('line channel token is not set');

    const decodedLineToken = await this.lineComponent.verifyLineIdToken(lineIdToken, clientId);
    const decodedFirebaseToken = await this.firebaseComponent.verifyFirebaseIdToken(authorization);
    const user = await this.userService.getUser(decodedFirebaseToken.uid);
    if (!user || !user.contractAccountAddress) {
      throw new NotFoundError();
    }

    const contractAccountAddress = user.contractAccountAddress;
    logger.info({ createAccount: 'user', contract_address: user.contractAccountAddress });

    const account = await this.accountRepository.selectAccountByUserId(decodedFirebaseToken.sub, serviceId);
    if (account) {
      throw new ConflictError();
    }
    logger.info({ createAccount: 'account', message: 'account not conflict' });

    try {
      const result = await db.transaction().execute(async (trx: Transaction<Database>) => {
        const { nftContractAddress, nftMintQueueId, nftTokenId } = await this.prepareCreateMembership(
          trx,
          serviceId,
          contractAccountAddress,
        );
        const { tbaQueueId } = await this.prepareCreateTba(trx, serviceId, contractAccountAddress, nftTokenId);
        const metadataUrl = await this.metadataService.insertMetadata(
          NftType.MEMBERSHIP,
          nftContractAddress,
          nftTokenId,
          nftMintQueueId,
          trx,
        );
        await this.nftContractsRepository.updateNextTokenId(nftContractAddress, nftTokenId + 1, trx);

        const newAccount = await this.accountRepository.insertAccount(
          {
            account_id: uuidv4(),
            service_id: serviceId,
            user_id: decodedFirebaseToken.uid,
            display_name: decodedLineToken.name,
            profile_image_url: decodedLineToken.picture,
            status: AccountStatus.ACTIVE,
            transaction_id: nftMintQueueId, // nftMintQueueId,
            queue_id: tbaQueueId, // tba queue
            created_at: new Date(),
            updated_at: new Date(),
            last_login_at: new Date(),
            token_bound_account_address: undefined, //tbaAccountAddress,
            membership_id: nftTokenId, //membershipId,
            membership_metadata_url: metadataUrl, //membershipMetadataUrl,
          },
          trx,
        );

        return { account: newAccount, contractAddress: nftContractAddress, metadataUrl: metadataUrl };
      });

      const cloudTask = new CloudTaskComponent();
      await cloudTask.enqueueMintTask();

      const metadata = await axios.get(result.metadataUrl);
      const metadataImageUrl = typeof metadata?.data?.image === 'string' ? (metadata.data.image as string) : undefined;

      if (metadataImageUrl === undefined) {
        await this.accountRepository.updateAccountMembershipNftImageUrl(
          result.account.account_id,
          result.account.service_id,
          metadataImageUrl!,
        );
      }

      return {
        accountId: result.account.account_id,
        displayName: result.account.display_name,
        profileImage: result.account.profile_image_url,
        membership: {
          contractAddress: result.contractAddress,
          tokenId: result.account.membership_id,
        },
        tokenBoundAccountAddress: result.account?.token_bound_account_address,
        membershipNftImageUrl: metadataImageUrl,
      };
    } catch (error) {
      throw new InternalServerError(`Account data store failed: ${error}`);
    }
  }

  async updateUserLineProfile(accountId: string, lineIdToken: string, serviceId: string): Promise<Account> {
    // Search account by accountId
    const account = await this.accountRepository.selectAccountById(accountId, serviceId);
    if (!account) {
      throw new NotFoundError('Account not found');
    }
    const service = await this.serviceInfoRepository.getServiceById(serviceId);
    if (!service || !service.line_channel_id) {
      throw new NotFoundError('Service not found');
    }
    const membershipNftContractId = service.membership_nft_contract_id;
    if (!membershipNftContractId) {
      throw new NotFoundError('Membership Nft is not registered');
    }
    const membershipNft = await this.nftContractsRepository.selectNftContractById(membershipNftContractId);
    if (!membershipNft?.nft_contract_address) {
      throw new NotFoundError('Membership NFT not found');
    }

    const decodedLineToken = await this.lineComponent.verifyLineIdToken(lineIdToken, service.line_channel_id);

    await this.accountRepository.updateUserLineProfile(
      accountId,
      serviceId,
      decodedLineToken.name,
      decodedLineToken.picture,
    );

    const metadata = await axios.get(account.membership_metadata_url);
    const metadataImageUrl = typeof metadata?.data?.image === 'string' ? (metadata.data.image as string) : undefined;

    return {
      accountId,
      displayName: decodedLineToken.name,
      profileImage: decodedLineToken.picture,
      membership: {
        contractAddress: membershipNft?.nft_contract_address,
        tokenId: account.membership_id,
      },
      tokenBoundAccountAddress: account.token_bound_account_address,
      membershipNftImageUrl: metadataImageUrl,
    };
  }

  async getAccount(accountId: string, serviceId: string): Promise<Account> {
    const account = await this.accountRepository.selectAccountById(accountId, serviceId);
    if (!account) {
      throw new NotFoundError('Account not found');
    }

    const service = await this.serviceInfoRepository.getServiceById(serviceId);
    if (!service) {
      throw new NotFoundError('Service not found');
    }
    const membershipNftContractId = service.membership_nft_contract_id;
    if (!membershipNftContractId) {
      throw new NotFoundError('Membership Nft is not registered');
    }
    const membershipNft = await this.nftContractsRepository.selectNftContractById(membershipNftContractId);
    if (!membershipNft?.nft_contract_address) {
      throw new NotFoundError('Membership NFT not found');
    }

    const metadata = await axios.get(account.membership_metadata_url);
    const metadataImageUrl = typeof metadata?.data?.image === 'string' ? (metadata.data.image as string) : undefined;

    return {
      accountId: account.account_id,
      displayName: account.display_name,
      profileImage: account.profile_image_url,
      membership: {
        contractAddress: membershipNft?.nft_contract_address,
        tokenId: account.membership_id,
      },
      tokenBoundAccountAddress: account.token_bound_account_address,
      membershipNftImageUrl: metadataImageUrl,
    };
  }

  async getAccountStatus(accountId: string, serviceId: string): Promise<Status> {
    const account = await this.accountRepository.selectAccountById(accountId, serviceId);
    if (!account) {
      throw new NotFoundError('Account not found');
    }
    let badges: any[];
    try {
      badges = await this.nftsService.getUserBadges(serviceId, accountId);
    } catch {
      badges = [];
    }
    const [completedQuests, obtainedRewards, unusedCoupon] = await Promise.all([
      this.questActivityRepository.countCompletedQuestActivitiesByAccountId(accountId, serviceId),
      this.claimedRewardRepository.countClaimedRewardsByAccountId(accountId, serviceId),
      this.claimedRewardRepository.countRewardsByAccountIdAndStatus(
        accountId,
        serviceId,
        RewardType.COUPON,
        RewardUsageStatus.ACTIVE,
      ),
    ]);

    let currentPeriodCompletedQuests = 0;
    const currentPeriodStatusQuest = await this.questRepository.selectEnableStatusQuestByCurrentDate(serviceId);

    if (
      currentPeriodStatusQuest &&
      currentPeriodStatusQuest.quest_available_start_date &&
      currentPeriodStatusQuest.quest_available_end_date
    ) {
      currentPeriodCompletedQuests = await this.questActivityRepository.countQuestActivitiesByAccountIdAndPeriod(
        accountId,
        serviceId,
        currentPeriodStatusQuest?.quest_available_start_date,
        currentPeriodStatusQuest?.quest_available_end_date,
      );
    }

    return {
      completedQuests,
      obtainedRewards,
      unusedCoupon,
      currentPeriodCompletedQuests,
      badges,
    };
  }

  async deleteAccount(id: string, serviceIdHeader: string): Promise<void> {
    const account = await this.accountRepository.selectAccountById(id, serviceIdHeader);
    if (!account) {
      throw new NotFoundError('Account not found');
    }
    await this.accountRepository.updateAccountState(account.account_id, AccountStatus.DELETED, serviceIdHeader);
  }

  async getAccountActivityHistory(accountId: string, serviceId: string, lang: LanguageCode): Promise<ActivityHistory> {
    const account = await this.accountRepository.selectAccountById(accountId, serviceId);

    if (!account) {
      throw new NotFoundError('Account not found');
    }

    let actions, quests, rewards;
    try {
      [actions, quests, rewards] = await Promise.all([
        this.actionRepository.selectCompletedActions(accountId, serviceId, lang),
        this.questActivityRepository.selectCompletedQuests(accountId, serviceId, lang),
        this.claimedRewardRepository.selectClaimedRewards(accountId, serviceId, lang),
      ]);
    } catch (error) {
      throw new InternalServerError(`Failed to retrieve activity history: ${error}`);
    }

    const completedActions = actions.map((action) => {
      const actionItem = {
        completedTime: action.finish_date?.toISOString(),
        thumbnailImageUrl: action.action_thumbnail_image_url,
        title: action.action_title,
      };
      return actionItem;
    });

    const completedQuests = quests.map((quest) => {
      const questItem = {
        completedTime: quest.finish_date?.toISOString(),
        thumbnailImageUrl: quest.quest_thumbnail_image_url,
        title: quest.quest_title,
      };
      return questItem;
    });

    const claimedRewards = rewards.map((reward) => {
      const rewardItem: RewardHistory = {
        completedTime: reward.claim_date?.toISOString(),
        thumbnailImageUrl: reward.reward_thumbnail_image_url,
        title: reward.reward_title,
        actionType: reward.reward_usage_status as RewardActionType,
      };
      return rewardItem;
    });

    const response: ActivityHistory = {
      completedActions: completedActions,
      completedQuests: completedQuests,
      rewards: claimedRewards,
    };
    return response;
  }

  async getAccountNotifications(accountId: string, serviceId: string, locale: string): Promise<NotificationList> {
    const result = this.notificationService.getNotifications(accountId, serviceId, locale);
    return result;
  }

  async prepareCreateMembership(
    trx: Transaction<Database>,
    serviceId: string,
    contractAccountAddress: string,
  ): Promise<{
    nftContractAddress: string;
    nftMintQueueId: string;
    nftTokenId: number;
  }> {
    const nftMintQueueId = uuidv4();
    const service = await this.serviceInfoRepository.getServiceById(serviceId, trx);
    if (!service) {
      throw new NotFoundError('Service not found');
    }
    const tenantId = service.tenant_id;
    const vaultKey = await this.vaultKeyRepository.getVaultKeyIdWithLock(tenantId, trx);
    if (!vaultKey) {
      throw new NotFoundError(`No vault_keys record found for tenantId = ${tenantId}`);
    }

    const membershipNftContractId: string | undefined = service.membership_nft_contract_id;
    if (!membershipNftContractId) throw new NotFoundError(`NFT membership contract id ${serviceId} not found.`);

    const nftContract = await this.nftContractsRepository.selectNftContractById(membershipNftContractId, trx);
    if (!nftContract?.nft_contract_address)
      throw new NotFoundError(`NFT contract with ID ${membershipNftContractId} not found.`);
    const tokenId = nftContract?.next_token_id ?? 0;

    await this.transactionQueuesRepository.insertQueue(
      {
        queue_id: nftMintQueueId,
        service_id: serviceId,
        from_address: vaultKey.vault_wallet_address,
        to_address: contractAccountAddress,
        status: TransactionQueueStatus.PENDING,
        tx_type: TxType.MINT_MEMBERSHIP,
        nft_type: NftType.MEMBERSHIP,
        nft_contract_address: nftContract?.nft_contract_address,
        token_id: tokenId,
        created_date: new Date(),
      },
      trx,
    );

    return {
      nftContractAddress: nftContract.nft_contract_address,
      nftMintQueueId,
      nftTokenId: tokenId,
    };
  }

  async prepareCreateTba(
    trx: Transaction<Database>,
    serviceId: string,
    contractAccountAddress: string,
    tokenId: number,
  ): Promise<{
    tbaQueueId: string;
  }> {
    const tbaQueueId = uuidv4();
    const service = await this.serviceInfoRepository.getServiceById(serviceId, trx);
    if (!service) {
      throw new NotFoundError('Service not found');
    }
    const membershipNftContractId = service.membership_nft_contract_id;
    if (!membershipNftContractId) {
      throw new NotFoundError('Membership Nft is not registered');
    }
    const tenantId = service.tenant_id;

    const vaultKey = await this.vaultKeyRepository.getVaultKeyIdWithLock(tenantId, trx);
    if (!vaultKey) {
      throw new NotFoundError(`No vault_keys record found for tenantId = ${tenantId}`);
    }
    const nftContract = await this.nftContractsRepository.getNftContractsByIdWithLock(membershipNftContractId, trx);
    if (!nftContract) {
      throw new NotFoundError(`No nft_contracts record found for nftContractId = ${membershipNftContractId}`);
    }
    const nftContractAddress = nftContract.nft_contract_address!;

    await this.transactionQueuesRepository.insertQueue(
      {
        queue_id: tbaQueueId,
        service_id: serviceId,
        from_address: vaultKey.vault_wallet_address,
        to_address: contractAccountAddress, // ≠ token_bound_account_registry_address
        status: TransactionQueueStatus.PENDING,
        tx_type: TxType.DEPLOY_CONTRACT,
        nft_type: NftType.MEMBERSHIP,
        nft_contract_address: nftContractAddress,
        token_id: tokenId,
        created_date: new Date(),
      },
      trx,
    );

    return {
      tbaQueueId: tbaQueueId,
    };
  }

  async updateLastLogin(accountId: string, serviceId: string): Promise<void> {
    const account = await this.accountRepository.selectAccountById(accountId, serviceId);
    if (!account) {
      throw new NotFoundError('Account not found');
    }
    await this.accountRepository.updateLastLogin(accountId, serviceId);
  }
}
