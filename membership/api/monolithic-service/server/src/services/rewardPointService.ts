import { inject, injectable } from 'tsyringe';
import { v4 as uuidv4 } from 'uuid';
import { Database, db } from '../db/database';
import { RedisComponent } from '../components/redisComponent';
import { sha256, chunkArray } from '../utils/helper';
import { ServiceInfoRepository } from '../repositories/serviceInfoRepository';
import { RewardPointTxsRepository } from '../repositories/rewardPointTxsRepository';
import { Transaction, sql } from 'kysely';
import { TABLE_REWARD_POINT_TXS } from '../constants/database';
import { InsertableRewardPointTxRow } from '../tables/rewardPointTxs';
import { PointOpsType } from '../enum/pointOpsType';
import { PointTxDetail } from '../enum/pointTxDetail';
import { PointOpsActor } from '../enum/pointOpsActor';
import { ExpireRewardPointsResponse, RewardPointTx } from '../dtos/points/schemas';
import { retryExecution } from '../utils/retry';
import { ServiceEntity } from '../tables/servicesTable';
import { OperationStatus } from '../enum/operationStatus';
import { logger } from '../utils/middleware/loggerMiddleware';

const DEFAULT_LAST_EXECUTED_AT = '2025-01-01T00:00:00.000Z';
const BATCH_SIZE = 100;
const DEFAULT_EXPIRES_ON = '2999-12-31T00:00:00Z';

@injectable()
export class RewardPointService {
  constructor(
    @inject('RedisComponent')
    private redisComponent: RedisComponent,
    @inject('ServiceInfoRepository')
    private serviceInfoRepository: ServiceInfoRepository,
    @inject('RewardPointTxsRepository')
    private rewardPointTxsRepository: RewardPointTxsRepository,
  ) {}

  private async calculateAndInsertExpireTransaction(
    serviceId: string,
    accountId: string,
    batchStartedAt: Date,
    trx: Transaction<Database>,
  ): Promise<number> {
    // Get total points
    const totalPoints = await this.getTotalRewardPoints(serviceId, accountId, trx);
    if (totalPoints === 0) return 0;

    // Get unexpired points
    const unexpiredPoints = await this.rewardPointTxsRepository.getUnexpiredPointsByAccountId(
      serviceId,
      accountId,
      batchStartedAt,
      trx,
    );
    if (totalPoints === unexpiredPoints) return 0;

    // Calculate expire points
    const expirePoints = totalPoints - unexpiredPoints;
    if (expirePoints <= 0) return 0;

    // Insert expire points transaction
    const expirePointsTx: InsertableRewardPointTxRow = {
      reward_point_tx_id: uuidv4(),
      service_id: serviceId,
      account_id: accountId,
      amount: -expirePoints,
      ops_type: PointOpsType.SUB,
      tx_by: PointOpsActor.SCHEDULER,
      tx_detail: PointTxDetail.EXPIRE,
      expires_on: new Date(DEFAULT_EXPIRES_ON),
      created_at: batchStartedAt,
    };
    await this.rewardPointTxsRepository.insertRewardPointTx(serviceId, expirePointsTx, trx);
    return expirePoints;
  }

  async expireRewardPoints(): Promise<ExpireRewardPointsResponse> {
    const startedAt = new Date();
    logger.info({ message: 'expireRewardPoints started', startedAt });

    let processedCount = 0;
    const failedAccounts: { accountId: string; reason: string }[] = [];

    // Get last execution time
    const redisKey = sha256('batch:last_executed_at:expire_reward_points');
    const lastExecution = await this.redisComponent
      .get<string>(redisKey)
      .then((v) => v ?? DEFAULT_LAST_EXECUTED_AT)
      .catch((err) => {
        logger.error({ msg: 'Redis get failed', error: err as unknown });
        return DEFAULT_LAST_EXECUTED_AT;
      });

    // Get all services
    const serviceInfoList: ServiceEntity[] = await this.serviceInfoRepository.getServiceList();
    const serviceIds = serviceInfoList.map((service) => service.service_id);

    // Each service is processed in parallel
    try {
      await Promise.allSettled(
        serviceIds.map(async (serviceId) => {
          const expirePointsTxList = await this.rewardPointTxsRepository.findExpirePointsTxs(
            serviceId,
            new Date(lastExecution),
            startedAt,
          );
          if (expirePointsTxList.length === 0) {
            logger.info({ message: 'no expire points txs', serviceId });
            return;
          }
          const expirePointsOwnerAccountIds = expirePointsTxList.map((expirePointsTx) => expirePointsTx.account_id);
          const chunks = chunkArray(expirePointsOwnerAccountIds, BATCH_SIZE);

          // Each chunk is processed in parallel
          for (const chunk of chunks) {
            logger.info({ message: 'processing chunk', serviceId, chunkSize: chunk.length });

            // Each account is processed in parallel
            await Promise.allSettled(
              chunk.map(async (accountId) => {
                try {
                  const expired = await db.transaction().execute(async (trx: Transaction<Database>) => {
                    await this.acquireAdvisoryLockWithRetry(accountId, trx);
                    return await this.calculateAndInsertExpireTransaction(serviceId, accountId, startedAt, trx);
                  });

                  if (expired > 0) {
                    processedCount++;
                    await this.getTotalRewardPoints(serviceId, accountId);
                    await this.redisComponent.incrByWithExpire(`reward_points:${accountId}`, -expired);
                  }
                } catch (e) {
                  logger.error({
                    message: 'account processing failed',
                    serviceId,
                    accountId,
                    err: e,
                  });
                  failedAccounts.push({ accountId, reason: e instanceof Error ? e.message : 'unknown error' });
                }
              }),
            );
          }
        }),
      );
    } finally {
      // Update last execution time
      await this.redisComponent.set(redisKey, new Date().toISOString());
    }

    // Build response
    const response: ExpireRewardPointsResponse = {
      status: failedAccounts.length ? OperationStatus.PARTIAL_FAILURE : OperationStatus.SUCCESS,
      startedAt: startedAt.toISOString(),
      endedAt: new Date().toISOString(),
      processedCount,
      failedAccounts,
    };
    return response;
  }

  async adjustRewardPoints(
    serviceId: string,
    accountId: string,
    amount: number,
    pointOpsType: PointOpsType,
    expiresOn?: string,
  ): Promise<RewardPointTx> {
    const rewardPointTx: InsertableRewardPointTxRow = {
      reward_point_tx_id: uuidv4(),
      service_id: serviceId,
      account_id: accountId,
      amount,
      ops_type: pointOpsType,
      tx_by: PointOpsActor.ADMIN,
      tx_detail: PointTxDetail.ADJUST,
      expires_on: new Date(expiresOn ?? DEFAULT_EXPIRES_ON),
      created_at: new Date(),
    };
    const insertedRewardPointTx = await db.transaction().execute(async (trx: Transaction<Database>) => {
      await this.acquireAdvisoryLockWithRetry(accountId, trx);
      return await this.rewardPointTxsRepository.insertRewardPointTx(serviceId, rewardPointTx, trx);
    });
    await this.getTotalRewardPoints(serviceId, accountId);
    await this.redisComponent.incrByWithExpire(`reward_points:${accountId}`, amount);

    return {
      ...insertedRewardPointTx,
      expires_on: insertedRewardPointTx.expires_on.toISOString(),
      created_at: insertedRewardPointTx.created_at.toISOString(),
    };
  }

  private async acquireAdvisoryLockWithRetry(accountId: string, trx?: Transaction<Database>): Promise<void> {
    const lockKey = `${TABLE_REWARD_POINT_TXS}:${accountId}`;

    await retryExecution(
      async () => {
        const { pg_try_advisory_xact_lock: locked } = await sql<{
          pg_try_advisory_xact_lock: boolean;
        }>`SELECT pg_try_advisory_xact_lock(hashtext(${lockKey}))`
          .execute(trx ?? db)
          .then((r) => r.rows[0]);
        if (!locked) {
          logger.error({ message: 'pg_try_advisory_xact_lock failed', accountId });
          throw new Error('LOCK_NOT_ACQUIRED');
        }
      },
      { retryDelay: 1000, retries: 3 },
    );
  }

  async getTotalRewardPoints(serviceId: string, accountId: string, trx?: Transaction<Database>): Promise<number> {
    const redisKey = `reward_points:${accountId}`;
    const cachedPoints = await this.redisComponent.get<number>(redisKey);
    if (cachedPoints) {
      return cachedPoints;
    } else {
      const dbPoints = await this.rewardPointTxsRepository.getTotalPointsByAccountId(serviceId, accountId, trx);
      await this.redisComponent.set(redisKey, dbPoints);
      return dbPoints;
    }
  }

  async addRewardPoint(
    serviceId: string,
    accountId: string,
    amount: number,
    txBy: PointOpsActor,
    txDetail: PointTxDetail,
    expiresOn = DEFAULT_EXPIRES_ON,
    trx: Transaction<Database>,
    txExtra?: string,
  ): Promise<void> {
    await this.acquireAdvisoryLockWithRetry(accountId, trx);
    const rewardPointTx: InsertableRewardPointTxRow = {
      reward_point_tx_id: uuidv4(),
      service_id: serviceId,
      account_id: accountId,
      amount,
      ops_type: PointOpsType.ADD,
      tx_by: txBy,
      tx_detail: txDetail,
      tx_extra: txExtra,
      expires_on: new Date(expiresOn),
      created_at: new Date(),
    };
    await this.rewardPointTxsRepository.insertRewardPointTx(serviceId, rewardPointTx, trx);
    await this.redisComponent.incrByWithExpire(`reward_points:${accountId}`, amount);
  }
}
