import { inject, injectable } from 'tsyringe';
import { RedisComponent } from '../components/redisComponent';
import { randomBytes } from 'crypto';
import {
  CheckTokenGateAccessRequest,
  CheckTokenGateAccessResponse,
  GenerateNonceForSigResponse,
  RegisterTokenGateRequest,
  RegisterTokenGateResponse,
  VerifySigRequest,
  VerifySigResponse,
} from '../dtos/nfts/schemas';
import { UnauthorizedError } from '../errors/unauthorizedError';
import { AccountRepository } from '../repositories/accountRepository';
import { NftsFirestoreRepository } from '../repositories/nftsFirestoreRepository';
import { tokenBoundAccountAbi } from '../abis/tbaAbi';
import { Abi, hashMessage, recoverAddress } from 'viem';
import { NotFoundError } from '../errors/notFoundError';
import { config } from '../configs/config';
import { accountFactoryV2Abi } from '../abis/accountFactoryV2ABI';
import { InternalServerError } from '../errors/internalServerError';
import { ViemComponent } from '../components/viemComponent';
import { isAddress, isHex } from 'viem/utils';
import { sha256 } from '../utils/helper';
import { TokenGateCondition, TokenGateRepository } from '../repositories/tokenGateRepository';
import { v4 as uuidv4 } from 'uuid';
import { logger } from '../utils/middleware/loggerMiddleware';

const NONCE_TTL_SECONDS = 60;
const NONCE_VALID_MARKER = '1';
const CHALLENGE_PREFIX = 'challenge:';
const CREATE_ACCOUNT_SALT = BigInt(0);

interface ParsedMessage {
  nonce: string;
  issuedAt: Date;
  expiration: Date;
  domain: string;
}

interface TokenGateNft {
  chainId: string;
  contractAddress: string;
  tokenId?: string;
}

@injectable()
export class TokenGateService {
  constructor(
    @inject('RedisComponent') private redisComponent: RedisComponent,
    @inject('AccountRepository') private accountRepository: AccountRepository,
    @inject('NftsFirestoreRepository') private nftsFirestoreRepository: NftsFirestoreRepository,
    @inject('ViemComponent') private viemComponent: ViemComponent,
    @inject('TokenGateRepository') private tokenGateRepository: TokenGateRepository,
  ) {}

  async generateNonceForSig(): Promise<GenerateNonceForSigResponse> {
    const nonce = randomBytes(32).toString('hex');
    await this.redisComponent.set<string>(`${CHALLENGE_PREFIX}${nonce}`, NONCE_VALID_MARKER, NONCE_TTL_SECONDS);
    return { nonce: nonce };
  }

  async verifySigAndFetchNfts(
    serviceId: string,
    accountId: string,
    requestBody: VerifySigRequest,
  ): Promise<VerifySigResponse> {
    const { sig, msg } = requestBody;
    if (!isHex(sig)) throw new UnauthorizedError('Signature is invalid');
    const { nonce, expiration } = this.parseMeta(msg);
    if (expiration.getTime() < Date.now()) throw new UnauthorizedError('The expiration date is in the past');

    const isValidNonce =
      (await this.redisComponent.getDel<string>(`${CHALLENGE_PREFIX}${nonce}`)) === NONCE_VALID_MARKER;
    if (!isValidNonce) throw new UnauthorizedError('Nonce is invalid or not found');

    const msgHash = hashMessage(msg);
    const signerAddress = await recoverAddress({ hash: msgHash, signature: sig }).catch((error: unknown) => {
      logger.error({ method: '[TokenGateService] recoverAddress', error });
      throw new UnauthorizedError('Signature is invalid');
    });

    const tbaContractAddress = await this.accountRepository.selectTokenBoundAccountAddress(accountId, serviceId);
    if (!tbaContractAddress) throw new NotFoundError('Token bound account address is not found');
    if (!isAddress(tbaContractAddress)) throw new InternalServerError('Token bound account address is invalid');

    const tbaOwner = await this.viemComponent.callViewFunction<string>(
      tbaContractAddress,
      JSON.parse(tokenBoundAccountAbi.implementation.abiJson) as Abi,
      'owner',
      [],
    );
    if (!tbaOwner) throw new InternalServerError('Token bound account owner is not found');

    const contractAccountAddress = await this.viemComponent.callViewFunction<string>(
      config.accountFactoryAddress,
      accountFactoryV2Abi,
      'getAddress',
      [signerAddress, CREATE_ACCOUNT_SALT],
    );
    if (!contractAccountAddress) throw new InternalServerError('Contract account address cannot be generated');
    if (contractAccountAddress !== tbaOwner) throw new UnauthorizedError('Signature is invalid');

    try {
      const nfts = await this.nftsFirestoreRepository.selectNftsByAccountId(accountId);
      return {
        ownedNfts: nfts.map((nft) => ({
          chainId: nft.chainId ?? config.chainId,
          contractAddress: nft.contractAddress,
          tokenId: nft.tokenId,
        })),
      };
    } catch (error) {
      logger.error({ msg: '[TokenGateService] verifySigAndFetchNfts', error });
      throw new InternalServerError('Failed to fetch nfts from Firestore');
    }
  }

  async checkTokenGateAccess(
    serviceId: string,
    requestBody: CheckTokenGateAccessRequest,
  ): Promise<CheckTokenGateAccessResponse> {
    const { tokenGateIds, nfts } = requestBody;
    const conditionsMap = await this.tokenGateRepository.fetchConditions(serviceId, tokenGateIds);

    const passed: string[] = [];
    const failed: { tokenGateId: string; reason: string }[] = [];

    const emptyTokenIdRecords: TokenGateNft[] = Array.from(
      new Map<string, TokenGateNft>(
        nfts.map(({ chainId, contractAddress }) => [
          `${chainId}-${contractAddress}`,
          { chainId, contractAddress, tokenId: '' },
        ]),
      ).values(),
    );
    const nftHashes = this.hashNfts([...nfts, ...emptyTokenIdRecords]);

    await Promise.all(
      tokenGateIds.map(async (tokenGateId) => {
        const conditions = conditionsMap.get(tokenGateId);
        if (!conditions) {
          failed.push({ tokenGateId, reason: 'CONDITION_NOT_FOUND' });
          return;
        }
        const isPassed = conditions.some((condition) => {
          return condition.hashes.every((hash) => nftHashes.includes(hash));
        });
        isPassed
          ? passed.push(tokenGateId)
          : failed.push({
              tokenGateId,
              reason: 'CONDITION_NOT_MATCHED',
            });
      }),
    );

    return { passed, failed };
  }

  async registerTokenGate(
    serviceId: string,
    requestBody: RegisterTokenGateRequest,
  ): Promise<RegisterTokenGateResponse> {
    const { tokenGateTranslations, tokenGateConditions } = requestBody;
    const tokenGateId = uuidv4();

    const createdTokenGateId = await this.tokenGateRepository.createTokenGate(
      serviceId,
      tokenGateId,
      tokenGateTranslations,
      this.toTokenGateConditions(tokenGateConditions),
    );
    return { tokenGateId: createdTokenGateId };
  }

  private parseMeta(msg: string): ParsedMessage {
    const kv = new Map<string, string>();
    for (const line of msg.split(/\r?\n/)) {
      const [rawKey, ...rest] = line.split(':');
      if (!rawKey || rest.length === 0) continue;
      kv.set(rawKey.trim().toLowerCase(), rest.join(':').trim());
    }

    // Extract required keys & convert to proper type
    const nonce = kv.get('nonce');
    const issued = kv.get('issued at');
    const expiration = kv.get('expiration');
    const domain = kv.get('domain');

    // ========= Validation =========
    if (!nonce || !/^[0-9a-z]{8,}$/i.test(nonce)) throw new UnauthorizedError('Nonce is invalid or not found');
    if (!issued || Number.isNaN(Date.parse(issued))) throw new UnauthorizedError('Issued At is not ISO 8601 format');
    if (!expiration || Number.isNaN(Date.parse(expiration)))
      throw new UnauthorizedError('Expiration is not ISO 8601 format');
    if (!domain || !/^[a-z0-9.-]+\.[a-z]{2,}$/i.test(domain))
      throw new UnauthorizedError('Domain is invalid or not found');

    return {
      nonce,
      issuedAt: new Date(issued),
      expiration: new Date(expiration),
      domain,
    };
  }

  private hashNfts(nfts: TokenGateNft[]): string[] {
    return nfts.map((n) => {
      const tokenId = n.tokenId ?? '';
      const key = `${n.chainId}|${n.contractAddress.toLowerCase()}|${tokenId}`;
      return sha256(key);
    });
  }

  private toTokenGateConditions(raw: { setNumber: number; nft: TokenGateNft }[]): TokenGateCondition[] {
    const tokenHashes = this.hashNfts(raw.map((r) => r.nft));
    return raw.map((c, idx) => ({
      setNumber: c.setNumber,
      tokenHash: tokenHashes[idx],
    }));
  }
}
