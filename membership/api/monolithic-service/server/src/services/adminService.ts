import { inject, injectable } from 'tsyringe';
import { NftsFirestoreRepository } from '../repositories/nftsFirestoreRepository';
import { NftContractsRepository } from '../repositories/nftContractsRepository';
import { NftContractJoinNftContractType } from '../repositories/nftContractsRepository';
import { UpdateNftMetadata } from '../repositories/nftsFirestoreRepository';
import { NotFoundError } from '../errors/notFoundError';
import { InternalServerError } from '../errors/internalServerError';
import { ethers } from 'ethers';
import { MetadataFetchService } from './metadataFetchService';
import { AdjustPointRequest, UpdateMetadataResponse } from '../dtos/admins/schemas';
import { StatusPointTx } from '../dtos/points/schemas';
import { RewardPointTx } from '../dtos/points/schemas';
import { match } from 'ts-pattern';
import { PointType } from '../enum/pointType';
import { RewardPointService } from './rewardPointService';
import { StatusPointService } from './statusPointService';

export interface FailedTokenIdsWithMsg {
  tokenId: string;
  errorMsg?: string;
}

@injectable()
export class AdminService {
  private apiKey: string;
  private chainName: string;
  constructor(
    @inject('NftsFirestoreRepository')
    private nftsFirestoreRepository: NftsFirestoreRepository,
    @inject('NftContractsRepository')
    private nftContractsRepository: NftContractsRepository,
    @inject('MetadataFetchService')
    private metadataFetchService: MetadataFetchService,
    @inject('RewardPointService')
    private rewardPointService: RewardPointService,
    @inject('StatusPointService')
    private statusPointService: StatusPointService,
  ) {
    if (!process.env.ALCHEMY_API_KEY || !process.env.ALCHEMY_CHAIN_NAME) {
      throw new InternalServerError('ALCHEMY_API_KEY or ALCHEMY_CHAIN_NAME is not set');
    }
    this.apiKey = process.env.ALCHEMY_API_KEY;
    this.chainName = process.env.ALCHEMY_CHAIN_NAME;
  }

  async updateFirestoreMetadata(contractAddress: string, tokenIds?: string[]): Promise<UpdateMetadataResponse> {
    let nftType;
    let abi;

    try {
      const nftContractTable: NftContractJoinNftContractType =
        await this.nftContractsRepository.selectNftContractAndTypeByAddress(contractAddress);
      nftType = nftContractTable.nft_type;
      abi = nftContractTable.nft_contract_abi as unknown as ethers.InterfaceAbi;
      if (!nftType || !abi) throw new NotFoundError('Nft type or abi is not found');
    } catch {
      throw new NotFoundError('Contract not found at the table');
    }

    const failedIds: FailedTokenIdsWithMsg[] = [];
    let tokenUris: { tokenId: string; tokenUri: string }[] = [];

    if (tokenIds) {
      const results = await Promise.allSettled(
        tokenIds.map(async (tokenId) => {
          const tokenUri = await this.metadataFetchService.fetchTokenUri(nftType, contractAddress, abi, tokenId);
          if (!tokenUri) {
            throw { tokenId, reason: 'Failed to fetch tokenUri' };
          }
          const metadataJson = await this.metadataFetchService.fetchMetadataFromTokenURI(tokenUri);
          if (!metadataJson) {
            throw { tokenId, reason: 'Failed to fetch metadataJson' };
          }
          const metadata: UpdateNftMetadata = {
            tokenId,
            metadataJson,
            metadataUri: tokenUri,
          };
          try {
            await this.nftsFirestoreRepository.updateNftsMetadata(contractAddress, metadata);
          } catch {
            throw { tokenId, reason: 'Failed to update Firestore' };
          }
          return { tokenId, tokenUri };
        }),
      );

      results.forEach((result) => {
        if (result.status === 'fulfilled') {
          tokenUris.push(result.value);
        } else {
          const errorInfo = result.reason as { tokenId?: number; reason?: string };
          if (errorInfo?.tokenId != null) {
            failedIds.push({ tokenId: errorInfo.tokenId.toString(), errorMsg: errorInfo.reason });
          }
        }
      });
      return { updatedTokenUris: tokenUris, failedIds };
    } else {
      tokenUris = await this.metadataFetchService.fetchNftTokens(this.apiKey, this.chainName, contractAddress);

      const metadatas: UpdateNftMetadata[] = [];

      const fetchMetadataResults = await Promise.allSettled(
        tokenUris.map(async ({ tokenId, tokenUri }) => {
          const metadata = await this.metadataFetchService.fetchMetadataFromTokenURI(tokenUri);
          if (!metadata) {
            throw { tokenId, reason: 'Failed to fetch metadataJson' };
          }
          return { tokenId, metadataJson: metadata, metadataUri: tokenUri };
        }),
      );

      fetchMetadataResults.forEach((result) => {
        if (result.status === 'fulfilled') {
          metadatas.push(result.value);
        } else {
          const errorInfo = result.reason as { tokenId?: number; reason?: string };
          if (errorInfo?.tokenId != null) {
            failedIds.push({ tokenId: errorInfo.tokenId.toString(), errorMsg: errorInfo.reason });
          }
        }
      });

      try {
        await this.nftsFirestoreRepository.batchUpdateNftsMetadata(contractAddress, metadatas, failedIds);
        tokenUris = tokenUris.filter(
          (token) => !failedIds.some((failed) => failed.tokenId === token.tokenId.toString()),
        );
      } catch (error) {
        throw new InternalServerError(`batchUpdateNftsMetadata fails: ${error}`);
      }
      return {
        updatedTokenUris: tokenUris,
        failedIds,
      };
    }
  }

  async adjustPoint(serviceId: string, requestBody: AdjustPointRequest): Promise<RewardPointTx | StatusPointTx> {
    const { accountId, pointType: targetPointType, amount, pointOpsType, expiresOn } = requestBody;
    const response = match(targetPointType)
      .with(PointType.REWARD, () => {
        return this.rewardPointService.adjustRewardPoints(serviceId, accountId, amount, pointOpsType, expiresOn);
      })
      .with(PointType.STATUS, () => {
        return this.statusPointService.adjustStatusPoints(serviceId, accountId, amount, pointOpsType);
      })
      .exhaustive();

    return response;
  }
}
