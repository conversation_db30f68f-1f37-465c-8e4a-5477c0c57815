import { ethers } from 'ethers';
import { inject, injectable } from 'tsyringe';
import { v4 as uuidv4 } from 'uuid';
import { MINTER_ROLE_HASH, NULL_ADDRESS } from '../configs/blockchain';
import { db } from '../db/database';
import { GrantedNftContract, NftDeploy, NftRegister, RegsteredModularContract } from '../dtos/nfts/schemas';
import { NftType } from '../enum/nftType';
import { TransactionQueueStatus } from '../enum/transactionQueueStatus';
import { TxType } from '../enum/txType';
import { BadGatewayError } from '../errors/badGateway';
import { InternalServerError } from '../errors/internalServerError';
import { NotFoundError } from '../errors/notFoundError';
import { ValidationError } from '../errors/validationError';
import { NftBaseMetadatasRepository } from '../repositories/nftBaseMetadatasRepository';
import { NftContractsRepository } from '../repositories/nftContractsRepository';
import { NftContractTypesRepository } from '../repositories/nftContractTypesRepository';
import { ServiceInfoRepository } from '../repositories/serviceInfoRepository';
import { TransactionQueuesRepository } from '../repositories/transactionQueuesRepository';
import { TransactionsRepository } from '../repositories/transactionsRepository';
import { VaultKeyRepository } from '../repositories/vaultKeyRepository';
import { PROXY_ABI, PROXY_BYTECODE } from '../utils/constant';
import { logger } from '../utils/middleware/loggerMiddleware';
import { MetadataService } from './metadataService';
import { ExtendedContractTransactionReceipt, TransactionService } from './transactionService';
import { WebhookService } from './webhookService';
@injectable()
export class NftRegisterService {
  constructor(
    @inject('TransactionService') private transactionService: TransactionService,
    @inject('WebhookService') private webhookService: WebhookService,
    @inject('NftContractTypesRepository') private nftContractTypesRepository: NftContractTypesRepository,
    @inject('NftContractsRepository') private nftContractsRepository: NftContractsRepository,
    @inject('VaultKeyRepository') private vaultKeyRepository: VaultKeyRepository,
    @inject('ServiceInfoRepository') private serviceInfoRepository: ServiceInfoRepository,
    @inject('MetadataService') private metadataService: MetadataService,
    @inject('NftBaseMetadatasRepository') private nftBaseMetadatasRepository: NftBaseMetadatasRepository,
    @inject('TransactionQueuesRepository') private transactionQueuesRepository: TransactionQueuesRepository,
    @inject('TransactionsRepository') private transactionsRepository: TransactionsRepository,
  ) {}

  public async register(serviceId: string, request: NftRegister): Promise<NftDeploy> {
    try {
      // Check if modular is available
      const serviceInfo = await this.serviceInfoRepository.getServiceById(serviceId);
      if (!serviceInfo) throw new ValidationError('Service not found');

      let modularContractAddress = '';
      if (serviceInfo.modular_contract_id) {
        const modularContract = await this.nftContractsRepository.selectNftContractById(
          serviceInfo.modular_contract_id,
        );
        if (!modularContract || !modularContract.nft_contract_address) {
          throw new NotFoundError(`Modular contract address not found for service: ${serviceId}`);
        }
        modularContractAddress = modularContract.nft_contract_address;
      } else {
        modularContractAddress = await this.deployModularCoreAndModules(serviceId);
      }

      const { nftContractTypeId, nftName, nftSymbol, nftCollectionName, metadata, deliveryImageUrl } = request;
      // Retrieve tenant information
      const { tenantId, vaultWalletAddress } = await this.getTenantData(serviceId);
      // Refer to NFT contract information
      const { binary, abi, nftType, address } = await this.getNftContractBinary(nftContractTypeId);

      // Store and create base metadata url
      const baseMetadata = await this.metadataService.insertBaseMetadataAndGenerateBaseUrl(
        nftType,
        serviceId,
        nftContractTypeId,
        metadata,
      );
      const { metadataUrl: metadtaUrl, baseNftId } = baseMetadata!;

      // Generate transaction args
      const constructorArgs = this.getDeployArguments(nftType, vaultWalletAddress, nftName, nftSymbol, metadtaUrl);

      // Send deploy transaction and store nft data
      const transactionReceipt = await this.signAndSendTransaction(
        baseNftId,
        nftContractTypeId,
        nftCollectionName,
        nftType,
        serviceId,
        tenantId,
        binary,
        abi,
        address,
        constructorArgs,
        deliveryImageUrl,
      );

      // Grant Minter Role to modular contract
      await this.grantRole(
        serviceId,
        modularContractAddress,
        transactionReceipt.contractAddress,
        abi,
        nftType,
        MINTER_ROLE_HASH,
      );

      // Add to monitoring nft list
      switch (nftType) {
        case NftType.COUPON:
        case NftType.CERTIFICATE:
        case NftType.CONTENT:
          await this.webhookService.addNftContractAddressToWebhook(transactionReceipt.contractAddress);
          break;
        case NftType.MEMBERSHIP:
          await this.serviceInfoRepository.updateServiceMembershipNftContractId(
            serviceId,
            transactionReceipt.nftContractId,
          );
          await this.webhookService.addNftContractAddressToWebhook(transactionReceipt.contractAddress);
          break;
      }

      return transactionReceipt;
    } catch (error) {
      if (error instanceof ValidationError || error instanceof NotFoundError) {
        throw error;
      } else {
        throw new InternalServerError(`Failed to register NFT: ${error}`);
      }
    }
  }

  public async registerModularContract(serviceId: string): Promise<RegsteredModularContract> {
    const serviceInfo = await this.serviceInfoRepository.getServiceById(serviceId);
    if (!serviceInfo) throw new ValidationError('Service not found');

    let modularContractAddress = '';
    if (serviceInfo.modular_contract_id) {
      const modularContract = await this.nftContractsRepository.selectNftContractById(serviceInfo.modular_contract_id);
      if (!modularContract || !modularContract.nft_contract_address) {
        throw new NotFoundError(`Modular contract address not found for service: ${serviceId}`);
      }
      modularContractAddress = modularContract.nft_contract_address;
    } else {
      modularContractAddress = await this.deployModularCoreAndModules(serviceId);
    }
    return { serviceId, modularContractAddress };
  }

  public async grantNftsMinterRole(serviceId: string): Promise<GrantedNftContract> {
    const serviceInfo = await this.serviceInfoRepository.getServiceById(serviceId);
    if (!serviceInfo) throw new ValidationError('Service not found');

    let modularContractAddress = '';
    if (serviceInfo.modular_contract_id) {
      const modularContract = await this.nftContractsRepository.selectNftContractById(serviceInfo.modular_contract_id);
      if (!modularContract || !modularContract.nft_contract_address) {
        throw new NotFoundError(`Modular contract address not found for service: ${serviceId}`);
      }
      modularContractAddress = modularContract.nft_contract_address;
    } else {
      throw new NotFoundError(`No modular contract found for service: ${serviceId}`);
    }

    const { tenantId } = await this.getTenantData(serviceId);
    const NFT_TYPE_LIST = [NftType.CERTIFICATE, NftType.CONTENT, NftType.MEMBERSHIP, NftType.COUPON];

    const roleGrantedList = [];
    const roleGrantFailedList = [];
    for (const nftType of NFT_TYPE_LIST) {
      const nftContractList = await this.nftContractsRepository.selectContractIdsByType(nftType, serviceId);
      const nftContractType = await this.nftContractTypesRepository.selectNftContractTypeByNftType(nftType);

      if (!nftContractType) {
        throw new NotFoundError(`NFT contract type not found for type: ${nftType}`);
      }

      for (const contract of nftContractList) {
        try {
          const contractAddress = contract.nft_contract_address || '';
          if (!contractAddress) {
            throw new NotFoundError(`No contract address found for contract: ${contract.nft_contract_id}`);
          }
          const hasRole = await this.transactionService.callContractViewFunction({
            tenantId: tenantId,
            contractAddress: contractAddress,
            contractABI: nftContractType.nft_contract_abi,
            functionName: 'hasRole',
            args: [MINTER_ROLE_HASH, modularContractAddress],
          });
          logger.info({ transaction: 'grantRole', contract: contract.nft_contract_address, hasRole });

          if (!hasRole) {
            logger.info(`Granting Minter Role for contract: ${contract.nft_contract_address}`);

            await this.grantRole(
              serviceId,
              modularContractAddress,
              contractAddress,
              nftContractType.nft_contract_abi as unknown as ethers.InterfaceAbi,
              nftType,
              MINTER_ROLE_HASH,
            );
            roleGrantedList.push({
              nftContractId: contract.nft_contract_id,
              nftContractAddress: contractAddress,
            });
          }
        } catch (error) {
          logger.error({ transaction: 'grantRole', error, contract: contract.nft_contract_address });
          roleGrantFailedList.push({
            nftContractId: contract.nft_contract_id,
            nftContractAddress: contract.nft_contract_address || '',
          });
        }
      }
    }

    return { roleGrantedList, roleGrantFailedList };
  }

  private async getTenantData(serviceId: string): Promise<{ tenantId: string; vaultWalletAddress: string }> {
    const serviceInfo = await this.serviceInfoRepository.getServiceById(serviceId);
    if (!serviceInfo) throw new ValidationError('Service not found');

    const tenantId = serviceInfo.tenant_id;
    const vaultKey = await this.vaultKeyRepository.getVaultKeyId(tenantId);
    if (!vaultKey) throw new NotFoundError('Vault key not found');

    return { tenantId: tenantId, vaultWalletAddress: vaultKey.vault_wallet_address };
  }

  private async getNftContractBinary(
    nftContractTypeId: string,
  ): Promise<{ binary: string; abi: ethers.InterfaceAbi; nftType: NftType; address: string | undefined }> {
    const contract = await this.nftContractTypesRepository.selectNftContractTypeById(nftContractTypeId);
    if (!contract) throw new ValidationError(`NFT contract type with ID ${nftContractTypeId} not found.`);
    if (!contract.nft_contract_binary) throw new ValidationError('Binary URL not provided for the contract.');
    if (!contract.nft_contract_abi) throw new ValidationError('ABI URL not provided for the contract.');

    const abi = contract.nft_contract_abi as unknown as ethers.InterfaceAbi;
    const binary = contract.nft_contract_binary;

    return { binary, abi, nftType: contract.nft_type, address: contract.nft_contract_address };
  }

  private getDeployArguments(
    nftType: NftType,
    adminAddress: string,
    nftName: string,
    nftSymbol: string,
    metadataUri?: string,
  ): unknown[] {
    const initialMintAmount = 1;
    switch (nftType) {
      case NftType.COUPON:
        return [adminAddress, initialMintAmount, metadataUri];
      case NftType.CERTIFICATE:
      case NftType.CONTENT:
      case NftType.MEMBERSHIP:
        return [adminAddress, metadataUri, nftName, nftSymbol];
      default:
        throw new InternalServerError('Invalid reward type for minting');
    }
  }

  public async grantRole(
    serviceId: string,
    toAddress: string,
    nftContractAddress: string,
    nftContractAbi: ethers.InterfaceAbi,
    nftContractType: NftType,
    role: string,
  ): Promise<void> {
    logger.info({ transaction: 'grantRole', contract: nftContractAddress });

    const serviceInfo = await this.serviceInfoRepository.getServiceById(serviceId);
    if (!serviceInfo) throw new ValidationError('Service not found');
    const tenantId = serviceInfo.tenant_id;
    logger.info({ transaction: 'grantRole', tenant: tenantId });

    const args = [role, toAddress];

    const contractInterface = new ethers.Interface(nftContractAbi);
    const encodedData = contractInterface.encodeFunctionData('grantRole', args);

    const queueId = uuidv4();
    const nonce = await db.transaction().execute(async (trx) => {
      const vaultKey = await this.vaultKeyRepository.getVaultKeyIdWithLock(tenantId, trx);
      if (!vaultKey) {
        throw new NotFoundError(`No vault_keys record found for tenantId = ${tenantId}`);
      }
      logger.info({ transaction: 'grantRole', vault_wallet_address: vaultKey.vault_wallet_address });

      await this.transactionQueuesRepository.insertQueue(
        {
          queue_id: queueId,
          service_id: serviceId,
          from_address: vaultKey.vault_wallet_address,
          to_address: nftContractAddress,
          nft_contract_address: nftContractAddress,
          status: TransactionQueueStatus.PROCESSING,
          tx_type: TxType.EXECUTE_CONTRACT,
          nft_type: nftContractType,
          created_date: new Date(),
        },
        trx,
      );

      const nextNonce = vaultKey.nonce + 1;
      await this.vaultKeyRepository.updateNonce(vaultKey.vault_key_id, nextNonce, trx);

      return vaultKey.nonce;
    });

    logger.info({ transaction: 'grantRole', nonce });

    try {
      const { transactionRequest, signer, fallbackSigner } = await this.transactionService.createTransactionRequest(
        tenantId,
        nftContractAddress,
        encodedData,
        nonce,
      );
      if (!transactionRequest) throw new InternalServerError('transaction request is null.');
      const signedTransaction = await this.transactionService.signTransaction(
        transactionRequest,
        signer,
        fallbackSigner,
      );
      const txHash = await this.transactionService.calculateTxHash(signedTransaction);
      if (!txHash) throw new InternalServerError('calculate transaction hash is null.');
      // insert queue.
      const { transactionId: newTransactionId } = await this.transactionService.createTransactionDatabases(
        serviceId,
        encodedData,
        nonce,
        txHash,
        [queueId],
      );
      logger.info({ transaction: 'grantRole', txHash, tenantId, contractAddress: nftContractAddress, nonce });
      // send tx.
      const txResponse = await this.transactionService.sendTransactionFromSignedTransaction(signedTransaction);
      if (!txResponse) throw new InternalServerError('sendTransactionFromSignedTransaction result is null.');

      if (txResponse.hash !== txHash) {
        logger.error({ message: 'calc hash is wrong.', txHash, txResponseHash: txResponse.hash });
        this.transactionsRepository.updateTransactionHash(newTransactionId, txResponse.hash);
      }
      logger.info({ transaction: 'grantRole', response: txResponse, nonce });
    } catch (error) {
      logger.error({
        transaction: 'grantRole',
        nonce,
        queueId,
        error: error instanceof Error ? { message: error.message, stack: error.stack } : error,
      });
      throw new Error(`Failed to grant role`);
    }
  }

  private async signAndSendTransaction(
    baseNftId: string,
    nftContractTypeId: string,
    nftCollectionName: string,
    nftType: NftType,
    serviceId: string,
    tenantId: string,
    contractBytecode: string,
    contractABI: ethers.InterfaceAbi,
    deployedImplementAddress: string | undefined,
    constructorArgs: unknown[],
    deliveryImageUrl?: string,
  ): Promise<{ nftContractId: string; contractAddress: string }> {
    let implementationAddress = deployedImplementAddress;

    // Step 1: deploy implementation if COUPON and not already deployed
    if (nftType === NftType.COUPON && !implementationAddress) {
      const implementationCallData = await this.transactionService.prepareDeployContract({
        tenantId,
        contractBytecode,
        contractABI,
        constructorArgs: [],
        transaction: {},
      });

      const implementationQueueId = uuidv4();
      const nonce = await this.insertTransactionQueueAndGetNonce(
        tenantId,
        serviceId,
        nftType,
        TxType.DEPLOY_CONTRACT,
        implementationQueueId,
      );

      const { transactionRequest, signer, fallbackSigner } = await this.transactionService.createTransactionRequest(
        tenantId,
        NULL_ADDRESS,
        implementationCallData,
        nonce,
      );
      if (!transactionRequest) throw new InternalServerError('transaction request is null.');
      const signedTransaction = await this.transactionService.signTransaction(
        transactionRequest,
        signer,
        fallbackSigner,
      );
      const txHash = await this.transactionService.calculateTxHash(signedTransaction);
      if (!txHash) throw new InternalServerError('calculate transaction hash is null.');
      // insert queue.
      const { transactionId: newTransactionId } = await this.transactionService.createTransactionDatabases(
        serviceId,
        implementationCallData,
        nonce,
        txHash,
        [implementationQueueId],
      );
      logger.info({ transaction: 'contract implementation deploy', txHash, tenantId, nonce });
      // send tx.
      const implementationResponse =
        await this.transactionService.sendTransactionFromSignedTransaction(signedTransaction);
      if (!implementationResponse)
        throw new InternalServerError('sendTransactionFromSignedTransaction result is null.');

      if (implementationResponse.hash !== txHash) {
        logger.error({ message: 'calc hash is wrong.', txHash, txResponseHash: implementationResponse.hash });
        this.transactionsRepository.updateTransactionHash(newTransactionId, implementationResponse.hash);
      }

      const implementationReceipt = (await implementationResponse.wait()) as ExtendedContractTransactionReceipt;
      if (!implementationReceipt.contractAddress)
        throw new BadGatewayError('Contract implementation deploy transaction is not available');
      implementationAddress = implementationReceipt.contractAddress;
      await this.nftContractTypesRepository.updateNftContractAddress(nftContractTypeId, implementationAddress);
    }

    // Step 2: Prepare call data
    const callData =
      nftType === NftType.COUPON
        ? await this.transactionService.prepareDeployUpgradeableContract({
            tenantId,
            implementationContractAddress: implementationAddress!,
            implementationByteCode: contractBytecode,
            implementationABI: contractABI,
            proxyByteCode: PROXY_BYTECODE,
            proxyABI: PROXY_ABI,
            initializeArgs: constructorArgs,
          })
        : await this.transactionService.prepareDeployContract({
            tenantId,
            contractBytecode,
            contractABI,
            constructorArgs,
            transaction: {},
          });

    logger.info({ transaction: 'deploy', callData: callData });

    // Step 3: Send proxy or direct contract transaction
    const proxyQueueId = uuidv4();
    const nonce = await this.insertTransactionQueueAndGetNonce(
      tenantId,
      serviceId,
      nftType,
      TxType.DEPLOY_CONTRACT,
      proxyQueueId,
    );

    const { transactionRequest, signer, fallbackSigner } = await this.transactionService.createTransactionRequest(
      tenantId,
      NULL_ADDRESS,
      callData,
      nonce,
    );
    if (!transactionRequest) throw new InternalServerError('transaction request is null.');
    const signedTransaction = await this.transactionService.signTransaction(transactionRequest, signer, fallbackSigner);
    const txHash = await this.transactionService.calculateTxHash(signedTransaction);
    if (!txHash) throw new InternalServerError('calculate transaction hash is null.');
    // insert queue.
    const { transactionId: newTransactionId } = await this.transactionService.createTransactionDatabases(
      serviceId,
      callData,
      nonce,
      txHash,
      [proxyQueueId],
    );
    logger.info({ transaction: 'deploy', txHash, tenantId, nonce });
    // send tx.
    const contractTransactionResponse =
      await this.transactionService.sendTransactionFromSignedTransaction(signedTransaction);
    if (!contractTransactionResponse)
      throw new InternalServerError('sendTransactionFromSignedTransaction result is null.');

    if (contractTransactionResponse.hash !== txHash) {
      logger.error({ message: 'calc hash is wrong.', txHash, txResponseHash: contractTransactionResponse.hash });
      this.transactionsRepository.updateTransactionHash(newTransactionId, contractTransactionResponse.hash);
    }

    const receipt = await contractTransactionResponse.wait();
    const contractAddress = receipt?.contractAddress;

    if (!receipt || !contractAddress) {
      throw new BadGatewayError('Contract deploy transaction is not available');
    }

    // Step 4: Metadata 更新 & NFT Contract 保存
    await this.nftBaseMetadatasRepository.updateContractAddressAndTokenIdNftBaseMetadata(
      baseNftId,
      contractAddress,
      nftType === NftType.COUPON ? 0 : undefined,
    );

    const nftContractId = uuidv4();
    try {
      await this.nftContractsRepository.insertNftContract({
        nft_contract_id: nftContractId,
        service_id: serviceId,
        nft_contract_type_id: nftContractTypeId,
        nft_collection_name: nftCollectionName,
        nft_contract_address: contractAddress,
        nft_contract_implementation_address: implementationAddress,
        delivery_image_url: deliveryImageUrl,
      });
    } catch (error) {
      throw new InternalServerError(`Vault NFT Contract insert failed: ${error}`);
    }

    return { nftContractId, contractAddress: contractAddress };
  }

  private async insertTransactionQueueAndGetNonce(
    tenantId: string,
    serviceId: string,
    nftType: NftType,
    txType: TxType,
    queueId: string,
  ): Promise<number> {
    return await db.transaction().execute(async (trx) => {
      const vaultKey = await this.vaultKeyRepository.getVaultKeyIdWithLock(tenantId, trx);
      if (!vaultKey) throw new NotFoundError(`No vault_keys record found for tenantId = ${tenantId}`);

      await this.transactionQueuesRepository.insertQueue(
        {
          queue_id: queueId,
          service_id: serviceId,
          from_address: vaultKey.vault_wallet_address,
          to_address: NULL_ADDRESS,
          nft_contract_address: NULL_ADDRESS,
          status: TransactionQueueStatus.PROCESSING,
          tx_type: txType,
          nft_type: nftType,
          created_date: new Date(),
        },
        trx,
      );

      await this.vaultKeyRepository.updateNonce(vaultKey.vault_key_id, vaultKey.nonce + 1, trx);
      return vaultKey.nonce;
    });
  }

  public async deployModularCoreAndModules(serviceId: string): Promise<string> {
    const { tenantId, vaultWalletAddress } = await this.getTenantData(serviceId);

    logger.info({ transaction: 'deploying modular contracts' });

    // DEPLOY CORE IMPLEMENTATION
    const modularCoreContractType = await this.nftContractTypesRepository.selectNftContractTypeByNftType(
      NftType.MODULAR_CORE,
    );
    if (!modularCoreContractType) {
      throw new NotFoundError(`Modular core contract type not found`);
    }
    let coreImplementationContractAddress = modularCoreContractType.nft_contract_address;
    if (!coreImplementationContractAddress) {
      coreImplementationContractAddress = await this.deployContract(
        tenantId,
        serviceId,
        NftType.MODULAR_CORE,
        modularCoreContractType.nft_contract_abi,
        modularCoreContractType.nft_contract_binary,
        [],
        false,
      );
      await this.nftContractTypesRepository.updateNftContractAddress(
        modularCoreContractType.nft_contract_type_id,
        coreImplementationContractAddress,
      );
    }
    logger.info({
      modularCoreContractTypeId: modularCoreContractType.nft_contract_type_id,
      coreImplementationContractAddress,
    });

    // DEPLOY BULK MINT MODULE
    const bulkMintContractType = await this.nftContractTypesRepository.selectNftContractTypeByNftType(
      NftType.MODULE_BULKMINT,
    );
    if (!bulkMintContractType) {
      throw new NotFoundError(`Bulk mint contract type not found`);
    }
    let bulkMintContractAddress = bulkMintContractType.nft_contract_address;
    if (!bulkMintContractAddress) {
      bulkMintContractAddress = await this.deployContract(
        tenantId,
        serviceId,
        NftType.MODULE_BULKMINT,
        bulkMintContractType.nft_contract_abi,
        bulkMintContractType.nft_contract_binary,
        [],
        false,
      );

      await this.nftContractTypesRepository.updateNftContractAddress(
        bulkMintContractType.nft_contract_type_id,
        bulkMintContractAddress,
      );
    }
    logger.info({ bulkMintContractTypeId: bulkMintContractType.nft_contract_type_id, bulkMintContractAddress });

    // DEPLOY BULK CREATE ACCOUNT MODULE
    const bulkCreateAccountContractType = await this.nftContractTypesRepository.selectNftContractTypeByNftType(
      NftType.MODULE_BULKCREATEACCOUNT,
    );
    if (!bulkCreateAccountContractType) {
      throw new NotFoundError(`Bulk create account contract type not found`);
    }
    let bulkCreateAccountContractAddress = bulkCreateAccountContractType.nft_contract_address;
    if (!bulkCreateAccountContractAddress) {
      bulkCreateAccountContractAddress = await this.deployContract(
        tenantId,
        serviceId,
        NftType.MODULE_BULKCREATEACCOUNT,
        bulkCreateAccountContractType.nft_contract_abi,
        bulkCreateAccountContractType.nft_contract_binary,
        [],
        false,
      );
      await this.nftContractTypesRepository.updateNftContractAddress(
        bulkCreateAccountContractType.nft_contract_type_id,
        bulkCreateAccountContractAddress,
      );
    }
    logger.info({
      bulkCreateAccountContractTypeId: bulkCreateAccountContractType.nft_contract_type_id,
      bulkCreateAccountContractAddress,
    });

    // DEPLOY MODULAR PROXY
    const modularContractType = await this.nftContractTypesRepository.selectNftContractTypeByNftType(
      NftType.MODULAR_CORE,
    );
    if (!modularContractType) {
      throw new NotFoundError(`Modular contract type not found`);
    }
    const modularProxyAddress = await this.deployContract(
      tenantId,
      serviceId,
      NftType.MODULAR_CORE,
      modularContractType.nft_contract_abi,
      modularContractType.nft_contract_binary,
      [vaultWalletAddress],
      true,
      coreImplementationContractAddress,
    );
    logger.info({ modularProxyAddress });

    // MODULE INSTALLATION
    const contractInterface = new ethers.Interface(
      modularCoreContractType.nft_contract_abi as unknown as ethers.InterfaceAbi,
    );
    const installBulkMintModuleCallData = contractInterface.encodeFunctionData('installModule', [
      bulkMintContractAddress,
      '0x',
    ]);
    const installBulkCreateAccountModuleCallData = contractInterface.encodeFunctionData('installModule', [
      bulkCreateAccountContractAddress,
      '0x',
    ]);

    const bulkMintInstallQueueId = uuidv4();
    const bulkCreateAccountInstallQueueId = uuidv4();
    const initialNonce = await db.transaction().execute(async (trx) => {
      const vaultKey = await this.vaultKeyRepository.getVaultKeyIdWithLock(tenantId, trx);
      if (!vaultKey) {
        throw new NotFoundError(`No vault_keys record found for tenantId = ${tenantId}`);
      }

      logger.info({
        transaction: 'install-modules',
        vault_wallet_address: vaultKey.vault_wallet_address,
      });

      // Install bulk mint module
      await this.transactionQueuesRepository.insertQueue(
        {
          queue_id: bulkMintInstallQueueId,
          service_id: serviceId,
          from_address: vaultKey.vault_wallet_address,
          to_address: modularProxyAddress,
          nft_contract_address: modularProxyAddress,
          status: TransactionQueueStatus.PROCESSING,
          tx_type: TxType.EXECUTE_CONTRACT,
          nft_type: NftType.MODULAR_CORE,
          created_date: new Date(),
        },
        trx,
      );

      // Install bulk create account module
      await this.transactionQueuesRepository.insertQueue(
        {
          queue_id: bulkCreateAccountInstallQueueId,
          service_id: serviceId,
          from_address: vaultKey.vault_wallet_address,
          to_address: modularProxyAddress,
          nft_contract_address: modularProxyAddress,
          status: TransactionQueueStatus.PROCESSING,
          tx_type: TxType.EXECUTE_CONTRACT,
          nft_type: NftType.MODULAR_CORE,
          created_date: new Date(),
        },
        trx,
      );

      const nextNonce = vaultKey.nonce + 2;
      await this.vaultKeyRepository.updateNonce(vaultKey.vault_key_id, nextNonce, trx);
      return vaultKey.nonce;
    });

    // BULK MINT
    const {
      transactionRequest: bmTransactionRequest,
      signer: bmSigner,
      fallbackSigner: bmFallbackSigner,
    } = await this.transactionService.createTransactionRequest(
      tenantId,
      modularProxyAddress,
      installBulkMintModuleCallData,
      initialNonce,
    );
    if (!bmTransactionRequest) throw new InternalServerError('transaction request is null.');
    const bmSignedTransaction = await this.transactionService.signTransaction(
      bmTransactionRequest,
      bmSigner,
      bmFallbackSigner,
    );
    const bmTxHash = await this.transactionService.calculateTxHash(bmSignedTransaction);
    if (!bmTxHash) throw new InternalServerError('calculate transaction hash is null.');
    // insert queue.
    const { transactionId: bmNewTransactionId } = await this.transactionService.createTransactionDatabases(
      serviceId,
      installBulkMintModuleCallData,
      initialNonce,
      bmTxHash,
      [bulkMintInstallQueueId],
    );
    logger.info({
      transaction: 'installModule',
      bmTxHash,
      tenantId,
      contractAddress: modularProxyAddress,
      initialNonce,
    });
    // send tx.
    const bulkMintInstallResponse =
      await this.transactionService.sendTransactionFromSignedTransaction(bmSignedTransaction);
    if (!bulkMintInstallResponse) throw new InternalServerError('sendTransactionFromSignedTransaction result is null.');

    if (bulkMintInstallResponse.hash !== bmTxHash) {
      logger.error({ message: 'calc hash is wrong.', bmTxHash, txResponseHash: bulkMintInstallResponse.hash });
      this.transactionsRepository.updateTransactionHash(bmNewTransactionId, bulkMintInstallResponse.hash);
    }

    // BULK CREATE ACCOUNT
    const bcaNonce = initialNonce + 1;
    const {
      transactionRequest: bcaTransactionRequest,
      signer: bcaSigner,
      fallbackSigner: bcaFallbackSigner,
    } = await this.transactionService.createTransactionRequest(
      tenantId,
      modularProxyAddress,
      installBulkCreateAccountModuleCallData,
      bcaNonce,
    );
    if (!bcaTransactionRequest) throw new InternalServerError('transaction request is null.');
    const bcaSignedTransaction = await this.transactionService.signTransaction(
      bcaTransactionRequest,
      bcaSigner,
      bcaFallbackSigner,
    );
    const bcaTxHash = await this.transactionService.calculateTxHash(bcaSignedTransaction);
    if (!bcaTxHash) throw new InternalServerError('calculate transaction hash is null.');
    // insert queue.
    const { transactionId: bcaNewTransactionId } = await this.transactionService.createTransactionDatabases(
      serviceId,
      installBulkCreateAccountModuleCallData,
      bcaNonce,
      bcaTxHash,
      [bulkCreateAccountInstallQueueId],
    );
    logger.info({
      transaction: 'installModule',
      bcaTxHash,
      tenantId,
      contractAddress: modularProxyAddress,
      nonce: bcaNonce,
    });
    // send tx.
    const bulkCreateAccountInstallResponse =
      await this.transactionService.sendTransactionFromSignedTransaction(bcaSignedTransaction);
    if (!bulkCreateAccountInstallResponse)
      throw new InternalServerError('sendTransactionFromSignedTransaction result is null.');

    if (bulkCreateAccountInstallResponse.hash !== bcaTxHash) {
      logger.error({
        message: 'calc hash is wrong.',
        bcaTxHash,
        txResponseHash: bulkCreateAccountInstallResponse.hash,
      });
      this.transactionsRepository.updateTransactionHash(bcaNewTransactionId, bulkCreateAccountInstallResponse.hash);
    }

    const bulkMintInstallReceipt = (await bulkMintInstallResponse.wait()) as ExtendedContractTransactionReceipt;
    const bulkCreateAccountInstallReceipt =
      (await bulkCreateAccountInstallResponse.wait()) as ExtendedContractTransactionReceipt;

    if (!bulkMintInstallReceipt || !bulkCreateAccountInstallReceipt) {
      throw new BadGatewayError('Install module transaction receipt is not available');
    }

    // Register the modular only after all process successfully completed
    await db.transaction().execute(async (trx) => {
      const modularContractId = uuidv4();

      await this.nftContractsRepository.insertNftContract(
        {
          nft_contract_id: modularContractId,
          service_id: serviceId,
          nft_contract_type_id: modularContractType.nft_contract_type_id,
          nft_contract_address: modularProxyAddress,
          nft_contract_implementation_address: modularContractType.nft_contract_address,
        },
        trx,
      );

      await this.serviceInfoRepository.updateServiceModularContract(serviceId, modularContractId, trx);
    });

    return modularProxyAddress;
  }

  private async deployContract(
    tenantId: string,
    serviceId: string,
    nftType: NftType,
    contractABI: unknown,
    contractBytecode: string,
    constructorArgs: unknown[] = [],
    proxy: boolean = false,
    implementationContractAddress: string | undefined = undefined,
  ): Promise<string> {
    logger.info({ transaction: 'deploy', nftType, proxy });
    let deployCallData: string;
    if (proxy) {
      if (!implementationContractAddress) {
        throw new ValidationError('Implementation contract address is required for proxy deployment');
      }
      deployCallData = await this.transactionService.prepareDeployUpgradeableContract({
        tenantId,
        implementationContractAddress: implementationContractAddress,
        implementationByteCode: contractBytecode,
        implementationABI: contractABI,
        proxyByteCode: PROXY_BYTECODE,
        proxyABI: PROXY_ABI,
        initializeArgs: constructorArgs,
      });
    } else {
      deployCallData = await this.transactionService.prepareDeployContract({
        tenantId,
        contractBytecode,
        contractABI,
        constructorArgs,
        transaction: {},
      });
    }
    const deploymentQueueId = uuidv4();
    const nonce = await db.transaction().execute(async (trx) => {
      const vaultKey = await this.vaultKeyRepository.getVaultKeyIdWithLock(tenantId, trx);
      if (!vaultKey) {
        throw new NotFoundError(`No vault_keys record found for tenantId = ${tenantId}`);
      }

      await this.transactionQueuesRepository.insertQueue(
        {
          queue_id: deploymentQueueId,
          service_id: serviceId,
          from_address: vaultKey.vault_wallet_address,
          to_address: NULL_ADDRESS,
          nft_contract_address: NULL_ADDRESS,
          status: TransactionQueueStatus.PROCESSING,
          tx_type: TxType.DEPLOY_CONTRACT,
          nft_type: nftType,
          created_date: new Date(),
        },
        trx,
      );

      const nextNonce = vaultKey.nonce + 1;
      await this.vaultKeyRepository.updateNonce(vaultKey.vault_key_id, nextNonce, trx);
      return vaultKey.nonce;
    });

    const { transactionRequest, signer, fallbackSigner } = await this.transactionService.createTransactionRequest(
      tenantId,
      NULL_ADDRESS,
      deployCallData,
      nonce,
    );
    if (!transactionRequest) throw new InternalServerError('transaction request is null.');
    const signedTransaction = await this.transactionService.signTransaction(transactionRequest, signer, fallbackSigner);
    const txHash = await this.transactionService.calculateTxHash(signedTransaction);
    if (!txHash) throw new InternalServerError('calculate transaction hash is null.');
    // insert queue.
    const { transactionId: newTransactionId } = await this.transactionService.createTransactionDatabases(
      serviceId,
      deployCallData,
      nonce,
      txHash,
      [deploymentQueueId],
    );
    logger.info({ transaction: 'deployContract', txHash, tenantId, nonce });
    // send tx.
    const deployResponse = await this.transactionService.sendTransactionFromSignedTransaction(signedTransaction);
    if (!deployResponse) throw new InternalServerError('sendTransactionFromSignedTransaction result is null.');

    if (deployResponse.hash !== txHash) {
      logger.error({ message: 'calc hash is wrong.', txHash, txResponseHash: deployResponse.hash });
      this.transactionsRepository.updateTransactionHash(newTransactionId, deployResponse.hash);
    }

    const receipt = (await deployResponse.wait()) as ExtendedContractTransactionReceipt;
    logger.info({ transaction: 'deploy', receipt });
    if (!receipt.contractAddress) {
      throw new BadGatewayError('Contract deployment transaction did not produce a contract address');
    }

    return receipt.contractAddress;
  }
}
