import { injectable } from 'tsyringe';
import { Resend } from 'resend';
import { EmailTemplate, EmailTemplateType, getEmailTemplate } from '../constants/emailTemplates';
import { LanguageCode } from '../enum/languageCode';
import { logger } from '../utils/middleware/loggerMiddleware';
import { config } from '../configs/config';

const resend = new Resend(config.resendApiKey);

@injectable()
export class EmailDeliveryService {
  public async sendEmail(to: string, template: EmailTemplate): Promise<boolean> {
    try {
      const { data, error } = await resend.emails.send({
        from: 'MarbullX <<EMAIL>>',
        to: [to],
        subject: template.subject,
        html: template.html,
      });

      if (error) {
        logger.error('Error sending email:', error);
        return false;
      } else {
        logger.info('Email sent successfully:', data);
        return true;
      }
    } catch (error) {
      logger.error('Exception sending email:', error);
      return false;
    }
  }

  public async sendTemplatedEmail(
    to: string,
    templateName: EmailTemplateType,
    lang: LanguageCode,
    params: unknown = {},
  ): Promise<boolean> {
    const template = getEmailTemplate(templateName, lang, params);
    return this.sendEmail(to, template);
  }
}
