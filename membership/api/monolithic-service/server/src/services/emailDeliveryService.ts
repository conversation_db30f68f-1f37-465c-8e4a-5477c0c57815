import { injectable } from 'tsyringe';
import { Resend } from 'resend';
import { logger } from '../utils/logger';
import { EmailTemplate, EmailTemplateType, getEmailTemplate } from '../constants/emailTemplates';
import { LanguageCode, languageCode } from '../enum/languageCode';

const resend = new Resend(process.env.RESEND_API_KEY as string);

@injectable()
export class EmailDeliveryService {

  public async sendEmail(to: string, template: EmailTemplate): Promise<boolean> {
    try {
      const { data, error } = await resend.emails.send({
        from: "Acme <<EMAIL>>",
        to: [to],
        subject: template.subject,
        html: template.html,
      });

      if (error) {
        logger.error('Error sending email:', error);
        return false;
      } else {
        logger.info('Email sent successfully:', data);
        return true;
      }
    } catch (error) {
      logger.error('Exception sending email:', error);
      return false;
    }
  }

  public async sendTemplatedEmail(
    to: string, 
    templateName: EmailTemplateType,
    lang: LanguageCode,
    params: any = {}
  ): Promise<boolean> {
    const template = getEmailTemplate(templateName, lang, params);
    return this.sendEmail(to, template);
  }
}
