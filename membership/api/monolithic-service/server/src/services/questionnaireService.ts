import { inject, injectable } from 'tsyringe';
import { v4 as uuidv4 } from 'uuid';
import { AccountQuestionnaireDetail, QuestionnaireQuestion, QuestionnaireTheme } from '../dtos/accounts/schemas';
import {
  QuestionExtra,
  QuestionExtraSchema,
  QuestionnaireCreateRequest,
  QuestionnaireDetailResponse,
  QuestionnaireReadResponse,
  QuestionnaireUpdateRequest,
  ResponseQuestion,
  ResponseQuestionUpdate,
  ResponseRankDetail,
  ResponseRankSettingDetail,
  ResponseTheme,
  ResponseThemeUpdate,
} from '../dtos/questionnaire/schemas';
import { LanguageCode } from '../enum/languageCode';
import { QuestionnaireType } from '../enum/questionnaireTypeEnum';
import { QuestionType } from '../enum/questionTypeEnum';
import { NotFoundError } from '../errors/notFoundError';
import { ValidationError } from '../errors/validationError';
import { QuestionnaireQuestionRepository } from '../repositories/questionnaireQuestionRepository';
import {
  QuestionnaireRepository,
  ThemeWithQuestions,
  UpdateableThemeWithQuestions,
} from '../repositories/questionnaireRepository';
import { QuestionnaireResultAnswerRepository } from '../repositories/questionnaireResultAnswerRepository';
import { QuestionnaireThemeRepository } from '../repositories/questionnaireThemeRepository';
import { ServiceInfoRepository } from '../repositories/serviceInfoRepository';
import { QuestionnaireResultRankWithTranslations } from '../tables/questionnaireResultRanksTable';
import { logger } from '../utils/logger';

@injectable()
export class QuestionnaireService {
  constructor(
    @inject('ServiceInfoRepository')
    private serviceInfoRepository: ServiceInfoRepository,
    @inject('QuestionnaireRepository')
    private questionnaireRepository: QuestionnaireRepository,
    @inject('QuestionnaireThemeRepository')
    private questionnaireThemeRepository: QuestionnaireThemeRepository,
    @inject('QuestionnaireResultAnswerRepository')
    private questionnaireResultAnswerRepository: QuestionnaireResultAnswerRepository,
    @inject('QuestionnaireQuestionRepository')
    private questionnaireQuestionRepository: QuestionnaireQuestionRepository,
  ) {}

  async getQuestionnaire(
    serviceId: string,
    questionnaireId: string,
    lang: LanguageCode,
  ): Promise<QuestionnaireReadResponse> {
    const service = await this.serviceInfoRepository.getServiceById(serviceId);
    if (!service) throw new NotFoundError('Service not found');

    const questionnaire = await this.questionnaireRepository.selectQuestionnaireByQuestionnaireId(
      questionnaireId,
      serviceId,
    );
    if (!questionnaire) throw new NotFoundError('Questionnaire not found');

    const themes = await this.questionnaireThemeRepository.selectQuestionnaireThemesByQuestionnaireId(
      questionnaireId,
      serviceId,
      lang,
    );
    const themesMap = new Map<string, ResponseTheme>();
    const questionMap = new Map<string, ResponseQuestion>();

    for (const row of themes ?? []) {
      if (!themesMap.has(row.theme_id)) {
        const theme: ResponseTheme = {
          themeId: row.theme_id,
          themeThumbnailImageUrl: row.theme_thumbnail_image_url,
          themeCoverImageUrl: row.theme_cover_image_url,
          themeTitle: row.theme_title,
          themeDescription: row.theme_description,
          themeNumber: row.theme_number,
          themeTimeLimitSeconds: row.theme_time_limit_seconds,
        };
        themesMap.set(row.theme_id, theme);
      }

      if (!questionMap.has(row.question_id)) {
        const question: ResponseQuestion = {
          themeId: row.theme_id,
          questionId: row.question_id,
          questionNumber: row.question_number,
          questionTitle: row.question_title,
          questionDetail: row.question_detail,
          questionType: row.question_type,
          questionExtra: {
            type: row.question_type,
            ...row.question_extra,
          } as QuestionExtra,
          isRequired: row.is_required,
          questionImageUrl: row.question_image_url,
        };
        questionMap.set(row.question_id, question);
      }
    }

    const questionnaireThemes = Array.from(themesMap.values());
    const questionnaireQuestions = Array.from(questionMap.values());

    const response: QuestionnaireReadResponse = {
      questionnaireId: questionnaireId,
      questionnaireType: questionnaire.questionnaire_type,
      themes: questionnaireThemes,
      questions: questionnaireQuestions,
    };

    return response;
  }

  async getAnsweredQuestionnaire(
    serviceId: string,
    accountId: string,
    questionnaireId: string,
    lang: LanguageCode,
  ): Promise<AccountQuestionnaireDetail> {
    const service = await this.serviceInfoRepository.getServiceById(serviceId);
    if (!service) throw new NotFoundError('Service not found');

    const themes = await this.questionnaireThemeRepository.selectForQuestionnaireAnswers(
      serviceId,
      questionnaireId,
      lang,
    );
    logger.info({ themes: themes });
    const questions = await this.questionnaireResultAnswerRepository.selectQuestionnaireAnswers(
      serviceId,
      accountId,
      questionnaireId,
      lang,
    );
    logger.info({ themes: themes });

    const questionnaire = await this.questionnaireRepository.selectQuestionnaireByQuestionnaireId(
      questionnaireId,
      serviceId,
    );
    logger.info({ questionnaire: questionnaire });

    const answerThemes: QuestionnaireTheme[] = [];

    if (questionnaire?.questionnaire_type === QuestionnaireType.QUIZ) {
      const rank = await this.questionnaireResultAnswerRepository.selectAnsweredResultRank(
        serviceId,
        accountId,
        questionnaireId,
        lang,
      );
      const maxPoints = await this.questionnaireQuestionRepository.sumQuestionnaireAnswerPoints(
        serviceId,
        questionnaireId,
      );
      for (const theme of themes) {
        const themeId = theme.themeId;
        const answeredQuestions: QuestionnaireQuestion[] = questions
          .filter((question) => question.themeId == themeId)
          .map((question) => {
            const answeredQuestion: QuestionnaireQuestion = {
              questionId: question.questionId,
              isRequired: question.isRequired,
              postedAnswer: question.postedAnswer,
              correctAnswer: (rank?.isPassed ?? false) ? question.correctAnswer : undefined,
              obtainedAnswerPoint: question.obtainedAnswerPoint,
              isCorrect: question.isCorrect,
            };
            return answeredQuestion;
          });
        const answeredTheme: QuestionnaireTheme = {
          themeId,
          correctCount: answeredQuestions.filter((question) => question.isCorrect).length,
          failedCount: answeredQuestions.filter((question) => !question.isCorrect).length,
          questions: answeredQuestions,
        };
        answerThemes.push(answeredTheme);
      }

      const response: AccountQuestionnaireDetail = {
        questionnaireId: questionnaireId,
        result: {
          currentPoint: rank?.questionnairePoint,
          maxPoint: maxPoints,
          rankId: rank.rankId,
          rankName: rank.rankName,
          rank: rank.rank,
          isPassed: rank.isPassed,
          rankHeaderAnimation: rank.rankHeaderAnimation,
        },
        questionnaireThemes: rank.isPassed ? answerThemes : [],
      };
      return response;
    } else {
      logger.info({ type: 'survey' });
      for (const theme of themes) {
        const themeId = theme.themeId;
        const answeredQuestions: QuestionnaireQuestion[] = questions
          .filter((question) => question.themeId == themeId)
          .map((question) => {
            const answeredQuestion: QuestionnaireQuestion = {
              questionId: question.questionId,
              isRequired: question.isRequired,
              postedAnswer: question.postedAnswer,
            };
            return answeredQuestion;
          });
        const answeredTheme: QuestionnaireTheme = {
          themeId,
          questions: answeredQuestions,
        };
        answerThemes.push(answeredTheme);
      }
      logger.info({ answerThemes: answerThemes });

      return {
        questionnaireId: questionnaireId,
        questionnaireThemes: answerThemes,
      };
    }
  }

  async createQuestionnaire(
    serviceId: string,
    requestData: QuestionnaireCreateRequest,
  ): Promise<QuestionnaireDetailResponse> {
    const service = await this.serviceInfoRepository.getServiceById(serviceId);
    if (!service) throw new NotFoundError('Service not found');

    const { questionnaireType, themes, ranks } = requestData;
    const questionnaireId = uuidv4();

    // Prepare themes with questions
    const insertQuestionnaireThemes: ThemeWithQuestions[] = themes.map((theme) => {
      if (requestData.questionnaireType === QuestionnaireType.QUIZ) {
        if (theme.themeThumbnailImageUrl === undefined || theme.themeCoverImageUrl === undefined) {
          throw new ValidationError('Theme images are required for quiz type questionnaires');
        }
      }

      const themeId = uuidv4();
      return {
        theme_data: {
          service_id: serviceId,
          theme_id: themeId,
          questionnaire_id: questionnaireId,
          theme_thumbnail_image_url: theme.themeThumbnailImageUrl,
          theme_cover_image_url: theme.themeCoverImageUrl,
          theme_number: theme.themeNumber,
          theme_time_limit_seconds: theme.themeTimeLimitSeconds ?? undefined,
        },
        theme_translations:
          theme.themeTranslations?.map((translation) => ({
            theme_id: themeId,
            service_id: serviceId,
            language: translation.language,
            theme_title: translation.themeTitle,
            theme_description: translation.themeDescription,
          })) ?? [],
        questions: theme.questions.map((question) => {
          const questionId = uuidv4();

          return {
            question_data: {
              service_id: serviceId,
              question_id: questionId,
              theme_id: themeId,
              question_number: question.questionNumber,
              question_type: question.questionType,
              question_image_url: question.questionImageUrl,
              is_required: question.isRequired,
              answer_point: question.answerPoint,
            },
            question_translations: question.questionTranslations.map((translation) => ({
              question_id: questionId,
              service_id: serviceId,
              language: translation.language,
              question_title: translation.questionTitle,
              question_detail: translation.questionDetail ?? undefined,
              question_extra: translation.questionExtra,
              correct_data: translation.correctData ?? undefined,
              correct_data_validation: translation.correctDataValidation ?? undefined,
            })),
          };
        }),
      };
    });

    // Prepare result ranks
    const insertQuestionnaireRanks: QuestionnaireResultRankWithTranslations[] = (ranks ?? []).map((rank) => {
      const rankId = uuidv4();
      const insertQuestionnaireRank: QuestionnaireResultRankWithTranslations = {
        rank_id: rankId,
        service_id: serviceId,
        questionnaire_id: questionnaireId,
        rank_header_animation_url: rank.rankHeaderAnimationUrl,
        rank: rank.rank,
        lower_limit_points: rank.lowerLimitPoints,
        upper_limit_points: rank.upperLimitPoints,
        is_passed: rank.isPassed,
        rank_translations: rank.rankTranslations.map((translation) => ({
          rank_id: rankId,
          service_id: serviceId,
          language: translation.language,
          rank_name: translation.rankName,
        })),
      };

      return insertQuestionnaireRank;
    });

    // insert into database
    await this.questionnaireRepository.insertQuestionnaireData(
      serviceId,
      questionnaireId,
      questionnaireType,
      insertQuestionnaireThemes,
      insertQuestionnaireRanks,
    );
    // prepare response
    const maxRank = Math.max(...(ranks ?? []).map((rank) => rank.rank));
    const passingPoints = Math.min(
      ...(ranks ?? []).filter((rank) => rank.isPassed).map((rank) => rank.lowerLimitPoints),
    );
    const rankDetails = insertQuestionnaireRanks.map((rank) => {
      const rankDetail: ResponseRankDetail = {
        rankId: rank.rank_id,
        rank: rank.rank,
        lowerLimitPoints: rank.lower_limit_points,
        upperLimitPoints: rank.upper_limit_points,
        rankHeaderAnimationUrl: rank.rank_header_animation_url,
        isPassed: rank.is_passed,
        rankTranslations: rank.rank_translations.map((t) => ({
          language: t.language,
          rankName: t.rank_name,
        })),
      };

      return rankDetail;
    });

    const questionnaireThemes: ResponseThemeUpdate[] = [];
    const questionnaireQuestions: ResponseQuestionUpdate[] = [];

    for (const theme of insertQuestionnaireThemes) {
      const questionnaireTheme: ResponseThemeUpdate = {
        themeId: theme.theme_data.theme_id,
        themeThumbnailImageUrl: theme.theme_data.theme_thumbnail_image_url,
        themeCoverImageUrl: theme.theme_data.theme_cover_image_url,
        themeNumber: theme.theme_data.theme_number,
        themeTimeLimitSeconds: theme.theme_data.theme_time_limit_seconds,
        themeTranslations: theme.theme_translations.map((translation) => ({
          language: translation.language,
          themeTitle: translation.theme_title,
          themeDescription: translation.theme_description,
        })),
      };
      questionnaireThemes.push(questionnaireTheme);

      for (const question of theme.questions) {
        const questionnaireQuestion: ResponseQuestionUpdate = {
          questionId: question.question_data.question_id,
          questionNumber: question.question_data.question_number,
          questionType: question.question_data.question_type,
          isRequired: question.question_data.is_required,
          answerPoint: question.question_data.answer_point,
          questionTranslations: question.question_translations.map((t) => ({
            language: t.language,
            questionTitle: t.question_title,
            questionDetail: t.question_detail,
            questionExtra: QuestionExtraSchema.parse(t.question_extra),
            correctData: t.correct_data,
            correctDataValidation: t.correct_data_validation,
          })),
        };
        questionnaireQuestions.push(questionnaireQuestion);
      }
    }

    const rankSetting: ResponseRankSettingDetail = {
      maxRank: maxRank,
      passingPoints: passingPoints,
      rankDetails: rankDetails,
    };

    const response: QuestionnaireDetailResponse = {
      questionnaireId: questionnaireId,
      questionnaireType: questionnaireType,
      ranks: ranks ? rankSetting : undefined,
      themes: questionnaireThemes,
      questions: questionnaireQuestions,
    };

    return response;
  }

  async updateQuestionnaire(
    serviceId: string,
    questionnaireId: string,
    requestData: QuestionnaireUpdateRequest,
  ): Promise<QuestionnaireDetailResponse> {
    // Validate service exists and belongs to tenant
    const service = await this.serviceInfoRepository.getServiceById(serviceId);
    if (!service) throw new NotFoundError('Service not found');

    // Validate questionnaire exists
    const existingQuestionnaire = await this.questionnaireRepository.selectQuestionnaireByQuestionnaireId(
      questionnaireId,
      serviceId,
    );
    if (!existingQuestionnaire) throw new NotFoundError('Questionnaire not found');

    const { questionnaireType, themes: requestThemes, ranks: requestResultRanks } = requestData;

    // Prepare themes with questions for update
    const themes: UpdateableThemeWithQuestions[] = requestThemes?.map((theme) => ({
      theme_id: theme.themeId,
      service_id: serviceId,
      questionnaire_id: questionnaireId,
      theme_thumbnail_image_url: theme.themeThumbnailImageUrl,
      theme_number: theme.themeNumber,
      theme_time_limit_seconds: theme.themeTimeLimitSeconds ?? undefined,
      theme_translations:
        theme.themeTranslations?.map((translation) => ({
          theme_id: theme.themeId,
          service_id: serviceId,
          language: translation.language,
          theme_title: translation.themeTitle,
          theme_description: translation.themeDescription,
        })) ?? [],
      questions: theme.questions.map((question) => {
        return {
          service_id: serviceId,
          question_id: question.questionId,
          theme_id: theme.themeId,
          question_number: question.questionNumber,
          question_type: question.questionType as QuestionType,
          is_required: question.isRequired,
          question_image_url: question.questionImageUrl ?? undefined,
          answer_point: question.answerPoint,
          question_translations: question.questionTranslations.map((translation) => ({
            question_id: question.questionId,
            service_id: serviceId,
            language: translation.language,
            question_title: translation.questionTitle ?? undefined,
            question_detail: translation.questionDetail,
            question_extra: translation.questionExtra ?? undefined,
            correct_data: translation.correctData ?? undefined,
            correct_data_validation: translation.correctDataValidation ?? undefined,
          })),
        };
      }),
    }));

    // Prepare result ranks for update
    const resultRanks: QuestionnaireResultRankWithTranslations[] = (requestResultRanks ?? []).map((rank) => ({
      service_id: serviceId,
      rank: rank.rank,
      questionnaire_id: questionnaireId,
      rank_id: rank.rankId,
      rank_translations: rank.rankTranslations.map((translation) => ({
        rank_id: rank.rankId,
        service_id: serviceId,
        language: translation.language,
        rank_name: translation.rankName,
      })),
      rank_header_animation_url: rank.rankHeaderAnimationUrl,
      lower_limit_points: rank.lowerLimitPoints,
      upper_limit_points: rank.upperLimitPoints,
      is_passed: rank.isPassed,
    }));

    // Update all data in transaction
    await this.questionnaireRepository.updateQuestionnaireActionData(
      serviceId,
      questionnaireId,
      questionnaireType,
      themes,
      resultRanks,
    );
    const questionnaireActionDetail = await this.questionnaireRepository.getQuestionnaireActionDetail(
      serviceId,
      questionnaireId,
    );
    const updateThemes = questionnaireActionDetail.themes;
    const updateResultRanks = questionnaireActionDetail.resultRanks;

    // Prepare response
    const maxRank = Math.max(...updateResultRanks.map((rank) => rank.rank));
    const passingPoints = Math.min(
      ...updateResultRanks.filter((rank) => rank.is_passed).map((rank) => rank.lower_limit_points),
    );

    const rankDetails: ResponseRankDetail[] = updateResultRanks.map((rank) => ({
      rankId: rank.rank_id,
      rank: rank.rank,
      lowerLimitPoints: rank.lower_limit_points,
      upperLimitPoints: rank.upper_limit_points,
      rankHeaderAnimationUrl: rank.rank_header_animation_url,
      isPassed: rank.is_passed,
      rankTranslations: rank.rank_translations.map((t) => ({
        language: t.language,
        rankName: t.rank_name,
      })),
    }));

    const questionnaireThemes: ResponseThemeUpdate[] = [];
    const questionnaireQuestions: ResponseQuestionUpdate[] = [];

    for (const theme of updateThemes) {
      const questionnaireTheme = {
        themeId: theme.theme_id,
        themeThumbnailImageUrl: theme.theme_thumbnail_image_url,
        themeCoverImageUrl: theme.theme_cover_image_url,
        themeNumber: theme.theme_number,
        themeTimeLimitSeconds: theme.theme_time_limit_seconds,
        themeTranslations: theme.theme_translations.map((translation) => ({
          language: translation.language,
          themeTitle: translation.theme_title,
          themeDescription: translation.theme_description,
        })),
      };
      questionnaireThemes.push(questionnaireTheme);

      for (const question of theme.questions) {
        const questionnaireQuestion: ResponseQuestionUpdate = {
          questionId: question.question_id,
          questionNumber: question.question_number,
          questionType: question.question_type,
          isRequired: question.is_required,
          answerPoint: question.answer_point,
          questionTranslations: question.question_translations.map((t) => ({
            language: t.language,
            questionTitle: t.question_title,
            questionDetail: t.question_detail,
            questionExtra: QuestionExtraSchema.parse(t.question_extra),
            correctData: t.correct_data,
            correctDataValidation: t.correct_data_validation,
          })),
        };
        questionnaireQuestions.push(questionnaireQuestion);
      }
    }

    const response: QuestionnaireDetailResponse = {
      questionnaireId: questionnaireId,
      questionnaireType: questionnaireType,
      themes: questionnaireThemes,
      questions: questionnaireQuestions,
      ranks: { maxRank, passingPoints, rankDetails },
    };

    return response;
  }
}
