import { GeofenceRepository } from '../repositories/geofenceRepository';
import {
  PostLocationGeofenceRequest,
  PutLocationGeofenceRequest,
  LocationGeofenceId,
  GeofenceType,
} from '../dtos/geofences/schemas';
import { v4 as uuidv4 } from 'uuid';

export interface GeofenceFilters {
  page?: number;
  limit?: number;
  geofenceType?: 'CIRCLE' | 'POLYGON';
  search?: string;
}

export class GeofenceService {
  constructor(private geofenceRepository: GeofenceRepository) {}

  /**
   * Create a new geofence
   */
  async createGeofence(
    serviceId: string,
    request: PostLocationGeofenceRequest
  ): Promise<LocationGeofenceId> {
    const geofenceId = uuidv4();
    
    // Convert the request to database format
    const geofenceData = this.convertToDbFormat(geofenceId, serviceId, request);
    
    await this.geofenceRepository.createGeofence(geofenceData);
    
    // Return the created geofence
    return {
      geofenceId,
      name: request.name,
      geofence: request.geofence
    };
  }

  /**
   * Update an existing geofence
   */
  async updateGeofence(
    serviceId: string,
    geofenceId: string,
    request: PutLocationGeofenceRequest
  ): Promise<LocationGeofenceId | null> {
    // Check if geofence exists
    const existingGeofence = await this.geofenceRepository.getGeofenceById(serviceId, geofenceId);
    if (!existingGeofence) {
      return null;
    }

    // Convert the request to database format
    const geofenceData = this.convertToDbFormat(geofenceId, serviceId, request);
    
    await this.geofenceRepository.updateGeofence(geofenceId, geofenceData);
    
    // Return the updated geofence
    return {
      geofenceId,
      name: request.name,
      geofence: request.geofence
    };
  }

  /**
   * Get all geofences for a service
   */
  async getGeofences(
    serviceId: string,
    filters: GeofenceFilters = {}
  ): Promise<LocationGeofenceId[]> {
    const geofences = await this.geofenceRepository.getGeofences(serviceId, filters);
    
    return geofences.map(geofence => this.convertFromDbFormat(geofence));
  }

  /**
   * Get a specific geofence by ID
   */
  async getGeofenceById(
    serviceId: string,
    geofenceId: string
  ): Promise<LocationGeofenceId | null> {
    const geofence = await this.geofenceRepository.getGeofenceById(serviceId, geofenceId);
    
    if (!geofence) {
      return null;
    }
    
    return this.convertFromDbFormat(geofence);
  }

  /**
   * Delete a geofence
   */
  async deleteGeofence(
    serviceId: string,
    geofenceId: string
  ): Promise<boolean> {
    const existingGeofence = await this.geofenceRepository.getGeofenceById(serviceId, geofenceId);
    if (!existingGeofence) {
      return false;
    }

    await this.geofenceRepository.deleteGeofence(geofenceId);
    return true;
  }

  /**
   * Convert request format to database format
   */
  private convertToDbFormat(
    geofenceId: string,
    serviceId: string,
    request: PostLocationGeofenceRequest | PutLocationGeofenceRequest
  ): any {
    const { name, geofence } = request;
    
    const baseData = {
      geofence_id: geofenceId,
      service_id: serviceId,
      name,
      geofence_type: geofence.geofenceType,
    };

    if (geofence.geofenceType === GeofenceType.CIRCLE) {
      return {
        ...baseData,
        center_coordinate_latitude: geofence.center.latitude.toString(),
        center_coordinate_longitude: geofence.center.longitude.toString(),
        circle_radius: geofence.radiusMeters.toString(),
        circle_geometry: this.createCircleGeometry(geofence.center, geofence.radiusMeters),
        polygon_geometry: null,
      };
    } else {
      return {
        ...baseData,
        center_coordinate_latitude: null,
        center_coordinate_longitude: null,
        circle_radius: null,
        circle_geometry: null,
        polygon_geometry: this.createPolygonGeometry(geofence.coordinates),
      };
    }
  }

  /**
   * Convert database format to response format
   */
  private convertFromDbFormat(dbGeofence: any): LocationGeofenceId {
    const baseResponse = {
      geofenceId: dbGeofence.geofence_id,
      name: dbGeofence.name,
    };

    if (dbGeofence.geofence_type === GeofenceType.CIRCLE) {
      return {
        ...baseResponse,
        geofence: {
          geofenceType: GeofenceType.CIRCLE,
          center: {
            latitude: parseFloat(dbGeofence.center_coordinate_latitude),
            longitude: parseFloat(dbGeofence.center_coordinate_longitude),
          },
          radiusMeters: parseFloat(dbGeofence.circle_radius),
        }
      };
    } else {
      // Parse polygon coordinates from geometry string
      const coordinates = this.parsePolygonGeometry(dbGeofence.polygon_geometry);
      
      return {
        ...baseResponse,
        geofence: {
          geofenceType: GeofenceType.POLYGON,
          coordinates,
        }
      };
    }
  }

  /**
   * Create circle geometry string (simplified - in real implementation would use PostGIS)
   */
  private createCircleGeometry(center: { latitude: number; longitude: number }, radius: number): string {
    return `CIRCLE(${center.longitude} ${center.latitude}, ${radius})`;
  }

  /**
   * Create polygon geometry string (simplified - in real implementation would use PostGIS)
   */
  private createPolygonGeometry(coordinates: Array<{ latitude: number; longitude: number }>): string {
    const coordString = coordinates
      .map(coord => `${coord.longitude} ${coord.latitude}`)
      .join(', ');
    return `POLYGON((${coordString}))`;
  }

  /**
   * Parse polygon geometry string back to coordinates
   */
  private parsePolygonGeometry(geometryString: string): Array<{ latitude: number; longitude: number }> {
    // Simplified parser - in real implementation would use PostGIS functions
    if (!geometryString) return [];
    
    // Extract coordinates from POLYGON((lng lat, lng lat, ...)) format
    const match = geometryString.match(/POLYGON\(\(([^)]+)\)\)/);
    if (!match) return [];
    
    const coordPairs = match[1].split(', ');
    return coordPairs.map(pair => {
      const [longitude, latitude] = pair.trim().split(' ').map(parseFloat);
      return { latitude, longitude };
    });
  }
}
