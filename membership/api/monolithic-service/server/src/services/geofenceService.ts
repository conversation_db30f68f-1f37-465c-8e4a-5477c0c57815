import { GeofenceRepository } from '../repositories/geofenceRepository';
import {
  LocationGeofence,
  LocationGeofenceId,
  GeofenceType,
} from '../dtos/locations/schemas';
import { v4 as uuidv4 } from 'uuid';

export interface GeofenceFilters {
  page?: number;
  limit?: number;
  geofenceType?: 'CIRCLE' | 'POLYGON';
  search?: string;
}

export class GeofenceService {
  constructor(private geofenceRepository: GeofenceRepository) { }

  async createGeofence(
    serviceId: string,
    request: LocationGeofence
  ): Promise<LocationGeofenceId> {

    this.validateGeofenceData(request);

    const geofenceId = uuidv4();
    const geofenceData = this.convertToDbFormat(geofenceId, serviceId, request);

    await this.geofenceRepository.createGeofence(serviceId, geofenceData);
    return { geofenceId };
  }

  async updateGeofence(
    serviceId: string,
    geofenceId: string,
    request: LocationGeofence
  ): Promise<LocationGeofenceId | null> {
    this.validateGeofenceData(request);

    // Check if geofence exists
    const existingGeofence = await this.geofenceRepository.getGeofenceById(serviceId, geofenceId);

    if (!existingGeofence) {
      return null;
    }

    // Convert the request to database format
    const geofenceData = this.convertToDbFormat(geofenceId, serviceId, request);

    await this.geofenceRepository.updateGeofence(serviceId, geofenceId, geofenceData);

    // Return the updated geofence
    return {
      geofenceId,
      name: request.name,
      geofence: request.geofence
    };
  }

  /**
   * Get all geofences for a service
   */
  async getGeofences(
    serviceId: string,
    filters: GeofenceFilters = {}
  ): Promise<LocationGeofenceId[]> {
    const geofences = await this.geofenceRepository.getGeofences(serviceId, filters);

    return geofences.map(geofence => this.convertFromDbFormat(geofence));
  }

  /**
   * Get a specific geofence by ID
   */
  async getGeofenceById(
    serviceId: string,
    geofenceId: string
  ): Promise<LocationGeofenceId | null> {
    const geofence = await this.geofenceRepository.getGeofenceById(serviceId, geofenceId);

    if (!geofence) {
      return null;
    }

    return this.convertFromDbFormat(geofence);
  }

  /**
   * Delete a geofence
   */
  async deleteGeofence(
    serviceId: string,
    geofenceId: string
  ): Promise<boolean> {
    const existingGeofence = await this.geofenceRepository.getGeofenceById(serviceId, geofenceId);
    if (!existingGeofence) {
      return false;
    }

    await this.geofenceRepository.deleteGeofence(serviceId, geofenceId);
    return true;
  }

  /**
   * Convert request format to database format
   */
  private convertToDbFormat(
    geofenceId: string,
    serviceId: string,
    request: LocationGeofence
  ): any {
    const { name, geofence } = request;

    const baseData = {
      geofence_id: geofenceId,
      service_id: serviceId,
      name: name,
      center_pin_name: name, // Use name as center_pin_name for backward compatibility
      geofence_type: geofence.geofenceType,
      created_at: new Date(),
      updated_at: new Date(),
    };

    if (geofence.geofenceType === GeofenceType.CIRCLE) {
      return {
        ...baseData,
        circle_radius: geofence.radiusMeters.toString(),
        circle_geometry: this.createCircleGeometry(geofence.center, geofence.radiusMeters),
        polygon_geometry: null,
      };
    } else {
      return {
        ...baseData,
        circle_radius: null,
        circle_geometry: null,
        polygon_geometry: this.createPolygonGeometry(geofence.coordinates),
      };
    }
  }

  /**
   * Convert database format to response format
   */
  private convertFromDbFormat(dbGeofence: any): LocationGeofenceId {
    const baseResponse = {
      geofenceId: dbGeofence.geofence_id,
      name: dbGeofence.name || dbGeofence.center_pin_name || 'Unnamed Geofence',
    };

    if (dbGeofence.geofence_type === GeofenceType.CIRCLE) {
      // Parse circle coordinates from geometry string
      const center = this.parseCircleGeometry(dbGeofence.circle_geometry);

      return {
        ...baseResponse,
        geofence: {
          geofenceType: GeofenceType.CIRCLE,
          center,
          radiusMeters: parseFloat(dbGeofence.circle_radius || '0'),
        }
      };
    } else {
      // Parse polygon coordinates from geometry string
      const coordinates = this.parsePolygonGeometry(dbGeofence.polygon_geometry);

      return {
        ...baseResponse,
        geofence: {
          geofenceType: GeofenceType.POLYGON,
          coordinates,
        }
      };
    }
  }

  /**
   * Create circle geometry string (simplified - in real implementation would use PostGIS)
   */
  private createCircleGeometry(center: { latitude: number; longitude: number }, radius: number): string {
    return `CIRCLE(${center.longitude} ${center.latitude}, ${radius})`;
  }

  /**
   * Create polygon geometry string (simplified - in real implementation would use PostGIS)
   */
  private createPolygonGeometry(coordinates: Array<{ latitude: number; longitude: number }>): string {
    const coordString = coordinates
      .map(coord => `${coord.longitude} ${coord.latitude}`)
      .join(', ');
    return `POLYGON((${coordString}))`;
  }

  /**
   * Parse circle geometry string back to center coordinates
   */
  private parseCircleGeometry(geometryString: string): { latitude: number; longitude: number } {
    // Simplified parser - in real implementation would use PostGIS functions
    if (!geometryString) return { latitude: 0, longitude: 0 };

    // Extract coordinates from CIRCLE(lng lat, radius) format
    const match = geometryString.match(/CIRCLE\(([^,]+),/);
    if (!match) return { latitude: 0, longitude: 0 };

    const [longitude, latitude] = match[1].trim().split(' ').map(parseFloat);
    return { latitude, longitude };
  }

  /**
   * Parse polygon geometry string back to coordinates
   */
  private parsePolygonGeometry(geometryString: string): Array<{ latitude: number; longitude: number }> {
    // Simplified parser - in real implementation would use PostGIS functions
    if (!geometryString) return [];

    // Extract coordinates from POLYGON((lng lat, lng lat, ...)) format
    const match = geometryString.match(/POLYGON\(\(([^)]+)\)\)/);
    if (!match) return [];

    const coordPairs = match[1].split(', ');
    return coordPairs.map(pair => {
      const [longitude, latitude] = pair.trim().split(' ').map(parseFloat);
      return { latitude, longitude };
    });
  }

  private validateGeofenceData(data: LocationGeofence): void {
    const { geofence } = data;

    if (geofence.geofenceType === 'CIRCLE') {
      if (!geofence.center || !geofence.radiusMeters) {
        throw new HTTPException(400, {
          message: 'Circle geofence requires center coordinates and radius'
        });
      }

      if (geofence.radiusMeters <= 0) {
        throw new HTTPException(400, {
          message: 'Radius must be greater than 0'
        });
      }
    } else if (geofence.geofenceType === 'POLYGON') {
      if (!geofence.coordinates || geofence.coordinates.length < 3) {
        throw new HTTPException(400, {
          message: 'Polygon geofence requires at least 3 coordinate points'
        });
      }

      // Validate that first and last coordinates are the same (closed polygon)
      const first = geofence.coordinates[0];
      const last = geofence.coordinates[geofence.coordinates.length - 1];
      if (first.latitude !== last.latitude || first.longitude !== last.longitude) {
        throw new HTTPException(400, {
          message: 'Polygon must be closed (first and last coordinates must be the same)'
        });
      }
    }
  }
}
