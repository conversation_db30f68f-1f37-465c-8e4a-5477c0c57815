import { injectable, inject } from 'tsyringe';
import { GeofenceRepository } from '../repositories/geofenceRepository';
import {
  LocationGeofence,
  LocationGeofenceId,
  GeofenceType,
} from '../dtos/locations/schemas';
import { v4 as uuidv4 } from 'uuid';
import { ValidationError } from '../errors/validationError';
import { createPoint, createPolygon, createLocationFromPoint, createLocationFromPolygon } from '../dtos/utils/point';

@injectable()
export class GeofenceService {
  constructor(
    @inject('GeofenceRepository') private geofenceRepository: GeofenceRepository,
  ) { }

  async createGeofence(
    serviceId: string,
    request: LocationGeofence
  ): Promise<LocationGeofenceId> {

    this.validateGeofenceData(request);

    const geofenceId = uuidv4();
    const geofenceData = this.convertToDbFormat(geofenceId, serviceId, request);

    await this.geofenceRepository.createGeofence(serviceId, geofenceData);
    return { 
      geofenceId,
      name: request.name,
      geofence: request.geofence
    };
  }

  async updateGeofence(
    serviceId: string,
    geofenceId: string,
    request: LocationGeofence
  ): Promise<LocationGeofenceId | null> {
    this.validateGeofenceData(request);

    const existingGeofence = await this.geofenceRepository.getGeofenceById(serviceId, geofenceId);

    if (!existingGeofence) {
      return null;
    }

    const geofenceData = this.convertToDbFormat(geofenceId, serviceId, request);

    await this.geofenceRepository.updateGeofence(serviceId, geofenceId, geofenceData);

    return {
      geofenceId,
      name: request.name,
      geofence: request.geofence
    };
  }

  async getGeofences(
    serviceId: string,
  ): Promise<LocationGeofenceId[]> {
    const geofences = await this.geofenceRepository.getGeofences(serviceId);

    return geofences.map(geofence => this.convertFromDbFormat(geofence));
  }

  async getGeofenceById(
    serviceId: string,
    geofenceId: string
  ): Promise<LocationGeofenceId | null> {
    const geofence = await this.geofenceRepository.getGeofenceById(serviceId, geofenceId);

    if (!geofence) {
      return null;
    }

    return this.convertFromDbFormat(geofence);
  }

  private convertToDbFormat(
    geofenceId: string,
    serviceId: string,
    request: LocationGeofence
  ): any {
    const { name, geofence } = request;

    const baseData = {
      geofence_id: geofenceId,
      service_id: serviceId,
      center_pin_name: name,
      geofence_type: geofence.geofenceType,
    };

    if (geofence.geofenceType === GeofenceType.CIRCLE) {
      return {
        ...baseData,
        circle_radius: geofence.radiusMeters.toString(),
        circle_geometry: createPoint(geofence.center),
        polygon_geometry: null,
      };
    } else {
      return {
        ...baseData,
        circle_radius: null,
        circle_geometry: null,
        polygon_geometry: createPolygon(geofence.coordinates),
      };
    }
  }


  private convertFromDbFormat(dbGeofence: any): LocationGeofenceId {
    const baseResponse = {
      geofenceId: dbGeofence.geofence_id,
      name: dbGeofence.center_pin_name || 'Unnamed Geofence',
    };

    if (dbGeofence.geofence_type === GeofenceType.CIRCLE) {
      const center = createLocationFromPoint(dbGeofence.circle_geometry);

      return {
        ...baseResponse,
        geofence: {
          geofenceType: GeofenceType.CIRCLE,
          center,
          radiusMeters: parseFloat(dbGeofence.circle_radius || '0'),
        }
      };
    } else {
      const coordinates = createLocationFromPolygon(dbGeofence.polygon_geometry);

      return {
        ...baseResponse,
        geofence: {
          geofenceType: GeofenceType.POLYGON,
          coordinates,
        }
      };
    }
  }

  private validateGeofenceData(data: LocationGeofence): void {
    const { geofence } = data;

    if (geofence.geofenceType === 'CIRCLE') {
      if (!geofence.center || !geofence.radiusMeters) {
        throw new ValidationError('Circle geofence requires center coordinates and radius');
      }

      if (geofence.radiusMeters <= 0) {
        throw new ValidationError('Circle radius must be greater than 0');
      }
    } else if (geofence.geofenceType === 'POLYGON') {
      if (!geofence.coordinates || geofence.coordinates.length < 3) {
        throw new ValidationError('Polygon geofence requires at least 3 coordinate points');
      }

      const first = geofence.coordinates[0];
      const last = geofence.coordinates[geofence.coordinates.length - 1];
      if (first.latitude !== last.latitude || first.longitude !== last.longitude) {
        throw new ValidationError('Polygon must be closed (first and last coordinates must be the same)');
      }
    }
  }
}
