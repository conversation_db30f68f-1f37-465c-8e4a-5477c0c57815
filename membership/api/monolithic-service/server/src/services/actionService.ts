import { match } from 'ts-pattern';
import { inject, injectable } from 'tsyringe';
import { v4 as uuidv4 } from 'uuid';
import { ActionComplete, QuestionnaireAnswerRequest } from '../dtos/accounts/schemas';
import { ActionDetail, ActionDetailInfo } from '../dtos/services/schemas';
import { ActionActivitiesStatus, ActionType } from '../enum/actionType';
import { LanguageCode } from '../enum/languageCode';
import { QuestActivityStatus } from '../enum/questActivityStatus';
import { QuestionnaireStatus } from '../enum/questionnaireStatusEnum';
import { QuestionnaireType } from '../enum/questionnaireTypeEnum';
import { QuestType } from '../enum/questType';
import { ConflictError } from '../errors/conflictError';
import { InternalServerError } from '../errors/internalServerError';
import { NotFoundError } from '../errors/notFoundError';
import { ValidationError } from '../errors/validationError';
import { AchievementActionRepository } from '../repositories/achievementActionRepository';
import { ActionActivityRepository } from '../repositories/actionActivityRepository';
import { ActionRepository } from '../repositories/actionRepository';
import { OnlineCheckinActionRepository } from '../repositories/onlineCheckinActionRepository';
import { QrCheckinActionRepository } from '../repositories/qrCheckinActionRepository';
import { QuestActivityRepository } from '../repositories/questActivityRepository';
import { QuestionnaireActionRepository } from '../repositories/questionnaireActionRepository';
import { QuestionnaireQuestionAnswerRepository } from '../repositories/questionnaireQuestionAnswerRepository';
import {
  QuestionCorrectAnswers,
  QuestionnaireQuestionRepository,
} from '../repositories/questionnaireQuestionRepository';
import { QuestionnaireRepository } from '../repositories/questionnaireRepository';
import { QuestionnaireResultAnswerRepository } from '../repositories/questionnaireResultAnswerRepository';
import { QuestionnaireResultRankRepository } from '../repositories/questionnaireResultRankRepository';
import { QuestRepository } from '../repositories/questRepository';
import { ActionEntity, ActionWithTranslations } from '../tables/actionTable';
import { InsertableQuestionnaireQuestionAnswerRow } from '../tables/questionnaireQuestionAnswersTable';
import { InsertableQuestionnaireAnswerRow } from '../tables/questionnaireResultAnswersTable';
import { QuestionnaireResultRankEntity } from '../tables/questionnaireResultRanksTable';
import { SerialCodeService } from './serialCodeService';
import { SerialCodeActionRepository } from '../repositories/serialCodeActionRepository';
import { LocationCheckinActionRepository } from '../repositories/locationCheckinActionRepository';
import { GeofenceRepository } from '../repositories/geofenceRepository';
import { LocationCoordinates } from '../dtos/locations/schemas';

@injectable()
export class ActionService {
  constructor(
    @inject('ActionRepository') private actionRepository: ActionRepository,
    @inject('ActionActivityRepository')
    private actionActivityRepository: ActionActivityRepository,
    @inject('AchievementActionRepository')
    private achievementActionRepository: AchievementActionRepository,
    @inject('OnlineCheckinActionRepository')
    private onlineCheckinActionRepository: OnlineCheckinActionRepository,
    @inject('QuestRepository') private questRepository: QuestRepository,
    @inject('QuestActivityRepository')
    private questActivityRepository: QuestActivityRepository,
    @inject('QrCheckinActionRepository')
    private qrCheckinActionRepository: QrCheckinActionRepository,
    @inject('QuestionnaireActionRepository')
    private questionnaireActionRepository: QuestionnaireActionRepository,
    @inject('QuestionnaireRepository')
    private questionnaireRepository: QuestionnaireRepository,
    @inject('QuestionnaireQuestionRepository')
    private questionnaireQuestionRepository: QuestionnaireQuestionRepository,
    @inject('QuestionnaireQuestionAnswerRepository')
    private questionnaireQuestionAnswerRepository: QuestionnaireQuestionAnswerRepository,
    @inject('QuestionnaireResultAnswerRepository')
    private questionnaireResultAnswerRepository: QuestionnaireResultAnswerRepository,
    @inject('QuestionnaireResultRankRepository')
    private questionnaireResultRankRepository: QuestionnaireResultRankRepository,
    @inject('SerialCodeActionRepository')
    private serialCodeActionRepository: SerialCodeActionRepository,
    @inject('SerialCodeService')
    private serialCodeService: SerialCodeService,
    @inject('LocationCheckinActionRepository')
    private locationCheckinActionRepository: LocationCheckinActionRepository,
    @inject('GeofenceRepository')
    private geofenceRepository: GeofenceRepository,
  ) { }

  async getAction(actionId: string, serviceId: string, lang: LanguageCode): Promise<ActionDetail> {
    const action = await this.actionRepository.selectActionById(actionId, serviceId, lang);
    if (!action) throw new NotFoundError('action is not found');

    const actionDetailInfo: ActionDetailInfo | undefined = await this.handleActionInfo(action);

    const response: ActionDetail = {
      actionId: action.action_id,
      title: action.action_title,
      coverImageUrl: action.action_cover_image_url,
      description: action.action_description,
      actionType: action.action_type,
      actionLabel: action.action_label,
      availableStartDate: action.action_available_start_date.toISOString(),
      availableEndDate: action.action_available_end_date.toISOString(),
      actionDetailInfo: actionDetailInfo,
    };
    return response;
  }

  async completeAction(
    accountId: string,
    actionId: string,
    serviceId: string,
    data: ActionComplete,
    lang: LanguageCode,
  ): Promise<void> {
    const action = await this.findAction(actionId, serviceId);
    this.validateActionAvailability(action);
    await this.checkActionCompletion(action.action_id, accountId, serviceId);
    await this.validateActionTypeAndInput(
      action,
      serviceId,
      accountId,
      lang,
      data?.qrVerificationData ?? undefined,
      data.answerData ?? undefined,
      data.serialCodeData ?? undefined,
      data.serialCodeProjectId ?? undefined,
      data.locationData ?? undefined,
    );
    let rankStatus = undefined;
    if (data.actionType === ActionType.QUESTIONNAIRE) {
      rankStatus = await this.storeAndValidateQuestionnaireAnswers(serviceId, accountId, data.answerData!, lang);
      if (rankStatus === QuestionnaireStatus.FAILED) {
        return;
      }
    }
    await this.storeActionCompletion(action.action_id, accountId, serviceId);
    await this.updateQuestActivities(action.action_id, accountId, serviceId);
    await this.updateStatusQuests(accountId, serviceId);
  }

  private async findAction(actionId: string, serviceId: string): Promise<ActionWithTranslations> {
    const action = await this.actionRepository.selectActionById(actionId, serviceId);
    if (!action) {
      throw new NotFoundError('Action not found');
    }
    return action;
  }

  private validateActionAvailability(action: ActionEntity): void {
    const currentTime = new Date();
    if (currentTime < action.action_available_start_date || currentTime > action.action_available_end_date) {
      throw new ValidationError('Action is not available at this time');
    }
  }

  private async checkActionCompletion(actionId: string, accountId: string, serviceId: string): Promise<void> {
    const actionActivityExists = await this.actionActivityRepository.selectActionActivity(
      actionId,
      accountId,
      serviceId,
    );
    if (actionActivityExists) {
      throw new ConflictError('Action already completed');
    }
  }

  private async validateActionTypeAndInput(
    action: ActionEntity,
    serviceId: string,
    accountId: string,
    lang: LanguageCode,
    qrVerificationData?: string,
    questionnaireAnswerData?: QuestionnaireAnswerRequest,
    serialCodeData?: string,
    serialCodeProjectId?: string,
    locationData?: LocationCoordinates,
  ): Promise<void> {
    switch (action.action_type) {
      case ActionType.QR_CHECKIN: {
        if (!qrVerificationData) {
          throw new ValidationError('qr verification data is required');
        }
        const qrCheckinAction = await this.qrCheckinActionRepository.selectQrVerification(action.action_id, serviceId);
        if (qrCheckinAction?.qr_verification_data !== qrVerificationData) {
          throw new ValidationError('Invalid qrVerificationData');
        }
        break;
      }
      case ActionType.QUESTIONNAIRE: {
        if (!questionnaireAnswerData) {
          throw new ValidationError('questionnaire answer data is required');
        }
        const questCorrectAnswers = await this.questionnaireQuestionRepository.selectQuestionnaireQuestions(
          serviceId,
          questionnaireAnswerData.questionnaireId,
          lang,
        );

        const correctAnswerIds = questCorrectAnswers.map((answer) => answer.questionId);

        const answerIsValid = questionnaireAnswerData.questionAnswers
          .map((answer) => answer.questionId)
          .every((answer) => new Set(correctAnswerIds).has(answer));
        if (!answerIsValid) {
          throw new ValidationError('invalid question id is included');
        }
        break;
      }
      case ActionType.SERIAL_CODE:
        if (!serialCodeData || !serialCodeProjectId) {
          throw new ValidationError('serial code data and project id are required');
        }
        await this.serialCodeService.redeem(serviceId, accountId, serialCodeData, serialCodeProjectId);
        break;
      case ActionType.LOCATION_CHECKIN: {
        if (!locationData) {
          throw new ValidationError('location data is required for location checkin');
        }

        const geofenceId = await this.locationCheckinActionRepository.getGeofenceIdByActionId(action.action_id, serviceId);
        if (!geofenceId) {
          throw new ValidationError('No geofence associated with this location checkin action');
        }
        const isWithinGeofence = await this.geofenceRepository.isWithinGeofence(serviceId, geofenceId, locationData);
        if (!isWithinGeofence) {
          throw new ValidationError('Location is outside the required geofence area');
        }
        break;
      }
      case ActionType.ONLINE_CHECKIN:
      case ActionType.ACHIEVEMENT:
        break;
      default:
        throw new ValidationError('invalid action type');
    }
  }

  private async storeAndValidateQuestionnaireAnswers(
    serviceId: string,
    accountId: string,
    questionnaireAnswerData: QuestionnaireAnswerRequest,
    lang: LanguageCode,
  ): Promise<QuestionnaireStatus> {
    const answers = questionnaireAnswerData.questionAnswers;
    const questionnaireId = questionnaireAnswerData.questionnaireId;
    const questionnaireType = await this.questionnaireRepository.getQuestionnaireType(serviceId, questionnaireId);

    const questCorrectAnswers = await this.questionnaireQuestionRepository.selectQuestionnaireQuestions(
      serviceId,
      questionnaireId,
      lang,
    );
    const questionnaireResultId = uuidv4();
    const ranks = await this.questionnaireResultRankRepository.selectResultRanks(serviceId, questionnaireId);
    const postedDate = new Date();

    const ret = match(questionnaireType)
      .with(QuestionnaireType.QUIZ, () =>
        this.storeAndValidateQuestionnaireAnswerFromQuiz(
          serviceId,
          accountId,
          answers,
          questionnaireId,
          questCorrectAnswers,
          questionnaireResultId,
          ranks,
          postedDate,
        ),
      )
      .with(QuestionnaireType.SURVEY, () =>
        this.storeAndValidateQuestionnaireAnswerFromSurvey(
          serviceId,
          accountId,
          answers,
          questCorrectAnswers,
          questionnaireId,
          questionnaireResultId,
          postedDate,
        ),
      )
      .with(QuestionnaireType.MESSAGE, () =>
        this.storeAndValidateQuestionnaireAnswerFromMessage(
          serviceId,
          accountId,
          answers,
          questCorrectAnswers,
          questionnaireId,
          questionnaireResultId,
          postedDate,
        ),
      )
      .exhaustive();
    return ret;
  }

  private async storeAndValidateQuestionnaireAnswerFromQuiz(
    serviceId: string,
    accountId: string,
    answers: {
      questionId: string;
      answer: string | null;
    }[],
    questionnaireId: string,
    questCorrectAnswers: QuestionCorrectAnswers[],
    questionnaireResultId: string,
    ranks: QuestionnaireResultRankEntity[],
    postedDate: Date,
  ): Promise<QuestionnaireStatus> {
    try {
      if (ranks.length < 1) {
        throw new InternalServerError('QUIZ result must have at least 1 rank');
      }

      const status = await db.transaction().execute(async (trx) => {
        let answerPoint = 0;
        const answerDataList: InsertableQuestionnaireQuestionAnswerRow[] = [];
        for (const answer of answers) {
          const correctAnswer = questCorrectAnswers.find((ca) => ca.questionId === answer.questionId);
          if (correctAnswer === undefined) {
            throw new InternalServerError(`correct data is not assigned: ${answer.questionId}`);
          }
          let isCorrect: boolean | undefined = undefined;
          if (correctAnswer.validation != undefined) {
            const regex = new RegExp(correctAnswer.validation, 's');
            if (answer.answer == null) {
              isCorrect = false;
            } else {
              isCorrect = regex.test(answer.answer);
            }
          }

          // クイズの答えの保存
          const answerData: InsertableQuestionnaireQuestionAnswerRow = {
            questionnaire_question_answer_id: uuidv4(),
            service_id: serviceId,
            questionnaire_result_id: questionnaireResultId,
            question_id: answer.questionId,
            question_answer: answer.answer ?? undefined,
            is_correct: isCorrect,
            created_at: postedDate,
          };
          answerDataList.push(answerData);

          if (isCorrect) {
            answerPoint += correctAnswer.answerPoint ?? 0;
          }
        }

        // クイズ獲得ポイントから適当なランクを取得
        let targetRank: QuestionnaireResultRankEntity | undefined = undefined;
        for (const rank of ranks) {
          if (rank.lower_limit_points <= answerPoint && rank.upper_limit_points > answerPoint) {
            targetRank = rank;
          }
        }
        if (!targetRank) {
          throw new InternalServerError('QUIZ result rank not matched');
        }

        // Questionnaireの結果を保存
        const questionnaireAnswer: InsertableQuestionnaireAnswerRow = {
          service_id: serviceId,
          questionnaire_result_id: questionnaireResultId,
          account_id: accountId,
          rank_id: targetRank.rank_id,
          questionnaire_status: targetRank.is_passed ? QuestionnaireStatus.PASSED : QuestionnaireStatus.FAILED,
          questionnaire_points: answerPoint,
          is_available_result: true,
          created_at: postedDate,
        };
        const rank = await this.questionnaireResultAnswerRepository.insertResultAnswers(
          serviceId,
          accountId,
          questionnaireId,
          questionnaireAnswer,
          trx,
        );
        await this.questionnaireQuestionAnswerRepository.insertQuestionnaireAnswers(serviceId, answerDataList, trx);
        return rank.questionnaire_status;
      });

      return status;
    } catch (error) {
      throw new InternalServerError(
        `Error storing and validating questionnaire answer from quiz: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  private async storeAndValidateQuestionnaireAnswerFromSurvey(
    serviceId: string,
    accountId: string,
    answers: {
      questionId: string;
      answer: string | null;
    }[],
    questCorrectAnswers: QuestionCorrectAnswers[],
    questionnaireId: string,
    questionnaireResultId: string,
    postedDate: Date,
  ): Promise<QuestionnaireStatus> {
    const invalidAnswers = questCorrectAnswers.filter(
      (ans) => ans.isRequired && answers.filter((a) => a.questionId === ans.questionId)?.[0].answer === null,
    );
    if (invalidAnswers.length > 0) {
      throw new InternalServerError(`Invalid answers found: ${JSON.stringify(invalidAnswers)}`);
    }
    try {
      await db.transaction().execute(async (trx) => {
        // Questionnaireの結果を保存
        const questionnaireAnswer: InsertableQuestionnaireAnswerRow = {
          service_id: serviceId,
          questionnaire_result_id: questionnaireResultId,
          account_id: accountId,
          rank_id: undefined,
          questionnaire_status: QuestionnaireStatus.UNDEFINED,
          is_available_result: true,
          created_at: postedDate,
        };
        await this.questionnaireResultAnswerRepository.insertResultAnswers(
          serviceId,
          accountId,
          questionnaireId,
          questionnaireAnswer,
          trx,
        );

        const answerDataList: InsertableQuestionnaireQuestionAnswerRow[] = [];
        for (const answer of answers) {
          // アンケートの答えの保存
          const answerData: InsertableQuestionnaireQuestionAnswerRow = {
            questionnaire_question_answer_id: uuidv4(),
            service_id: serviceId,
            questionnaire_result_id: questionnaireResultId,
            question_id: answer.questionId,
            question_answer: answer.answer ?? undefined,
            created_at: postedDate,
          };
          answerDataList.push(answerData);
        }

        await this.questionnaireQuestionAnswerRepository.insertQuestionnaireAnswers(serviceId, answerDataList, trx);
      });
      return QuestionnaireStatus.UNDEFINED;
    } catch (error) {
      throw new InternalServerError(`Failed to store survey answers cause: ${error}`);
    }
  }

  private async storeAndValidateQuestionnaireAnswerFromMessage(
    serviceId: string,
    accountId: string,
    answers: {
      questionId: string;
      answer: string | null;
    }[],
    questCorrectAnswers: QuestionCorrectAnswers[],
    questionnaireId: string,
    questionnaireResultId: string,
    postedDate: Date,
  ): Promise<QuestionnaireStatus> {
    const invalidAnswers = questCorrectAnswers.filter(
      (ans) => ans.isRequired && answers.filter((a) => a.questionId === ans.questionId)?.[0].answer === null,
    );
    if (invalidAnswers.length > 0) {
      throw new InternalServerError(`Invalid answers found: ${JSON.stringify(invalidAnswers)}`);
    }
    try {
      await db.transaction().execute(async (trx) => {
        const questionnaireAnswer: InsertableQuestionnaireAnswerRow = {
          service_id: serviceId,
          questionnaire_result_id: questionnaireResultId,
          account_id: accountId,
          questionnaire_status: QuestionnaireStatus.UNDEFINED,
          is_available_result: true,
          created_at: postedDate,
        };
        await this.questionnaireResultAnswerRepository.insertResultAnswers(
          serviceId,
          accountId,
          questionnaireId,
          questionnaireAnswer,
          trx,
        );

        const answerDataList: InsertableQuestionnaireQuestionAnswerRow[] = [];
        for (const answer of answers) {
          // アンケートの答えの保存
          const answerData: InsertableQuestionnaireQuestionAnswerRow = {
            questionnaire_question_answer_id: uuidv4(),
            service_id: serviceId,
            questionnaire_result_id: questionnaireResultId,
            question_id: answer.questionId,
            question_answer: answer.answer ?? undefined,
            created_at: postedDate,
          };
          answerDataList.push(answerData);
        }

        await this.questionnaireQuestionAnswerRepository.insertQuestionnaireAnswers(serviceId, answerDataList, trx);
      });
      return QuestionnaireStatus.UNDEFINED;
    } catch (error) {
      throw new InternalServerError(
        `Failed to store message answers cause: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  private async storeActionCompletion(actionId: string, accountId: string, serviceId: string): Promise<void> {
    await this.actionActivityRepository.insertActionActivity({
      account_id: accountId,
      action_id: actionId,
      service_id: serviceId,
      action_activity_status: ActionActivitiesStatus.DONE,
      finish_date: new Date(),
    });
  }

  private async updateQuestActivities(actionId: string, accountId: string, serviceId: string): Promise<void> {
    const relatedQuest = await this.questRepository.selectQuestByActionId(actionId, serviceId);
    if (!relatedQuest) {
      throw new InternalServerError('Related Quest is not found');
    }
    const questActions = await this.actionRepository.selectActionsByQuestId(relatedQuest.quest_id, serviceId);
    const actionActivity = await this.actionActivityRepository.selectActionActivityByAccountId(accountId, serviceId);
    const completedActionIds = actionActivity.map((activity) => activity.action_id);
    const allActionsCompleted =
      questActions.filter((action) => completedActionIds.includes(action.action_id)).length === questActions.length;

    if (allActionsCompleted) {
      await this.questActivityRepository.upsertQuestActivity({
        account_id: accountId,
        quest_id: relatedQuest.quest_id,
        service_id: serviceId,
        quest_activity_status: QuestActivityStatus.DONE,
        finish_date: new Date(),
      });
    } else {
      await this.questActivityRepository.upsertQuestActivity({
        account_id: accountId,
        quest_id: relatedQuest.quest_id,
        service_id: serviceId,
        quest_activity_status: QuestActivityStatus.PROCEEDING,
      });
    }
  }

  private async updateStatusQuests(accountId: string, serviceId: string): Promise<void> {
    const availableStatusQuests = await this.questRepository.selectAvailableQuest(serviceId, QuestType.STATUS);
    if (availableStatusQuests.length > 1) {
      throw new InternalServerError('More than one available status quest found');
    }
    if (availableStatusQuests.length === 0) {
      return;
    }
    const statusQuest = availableStatusQuests[0];

    const completedAchievements = await this.questActivityRepository.countQuestActivitiesByAccountIdAndPeriod(
      accountId,
      serviceId,
      statusQuest.quest_available_start_date,
      statusQuest.quest_available_end_date,
    );

    const achievementActions = await this.actionRepository.selectAchievementActionsByQuestId(
      statusQuest.quest_id,
      serviceId,
    );
    const actionsToComplete = achievementActions.filter(
      (action) => action.milestone !== undefined && action.milestone <= completedAchievements,
    );

    for (const action of actionsToComplete) {
      const actionActivityExists = await this.actionActivityRepository.selectActionActivity(
        action.action_id,
        accountId,
        serviceId,
      );
      if (!actionActivityExists) {
        await this.actionActivityRepository.insertActionActivity({
          account_id: accountId,
          action_id: action.action_id,
          service_id: serviceId,
          action_activity_status: ActionActivitiesStatus.DONE,
          finish_date: new Date(),
        });
      }
    }

    const allStatusActionsCompleted = actionsToComplete.length === achievementActions.length;
    if (allStatusActionsCompleted) {
      await this.questActivityRepository.upsertQuestActivity({
        account_id: accountId,
        quest_id: statusQuest.quest_id,
        service_id: serviceId,
        quest_activity_status: QuestActivityStatus.DONE,
        finish_date: new Date(),
      });
    }
  }

  private async handleActionInfo(action: ActionEntity): Promise<ActionDetailInfo | undefined> {
    switch (action.action_type) {
      case ActionType.ACHIEVEMENT: {
        const info = await this.getAchievementActionInfo(action);
        return {
          type: ActionType.ACHIEVEMENT,
          priority: info.priority,
          milestone: info.milestone,
        };
      }
      case ActionType.ONLINE_CHECKIN: {
        const info = await this.getOnlineCheckinActionInfo(action);
        return {
          type: ActionType.ONLINE_CHECKIN,
          targetUrl: info.targetUrl,
        };
      }
      case ActionType.QUESTIONNAIRE: {
        const info = await this.getQuestionnaireActionInfo(action);
        return {
          type: ActionType.QUESTIONNAIRE,
          questionnaireId: info.questionnaireId,
        };
      }
      case ActionType.SERIAL_CODE: {
        const info = await this.getSerialCodeActionInfo(action);
        return {
          type: ActionType.SERIAL_CODE,
          serialCodeProjectId: info.serialCodeProjectId,
        };
      }
      case ActionType.QR_CHECKIN:
        break;
    }
  }

  private async getAchievementActionInfo(action: ActionEntity): Promise<{ priority: number; milestone: number }> {
    const achievement = await this.achievementActionRepository.selectAchievementActionById(
      action.action_id,
      action.service_id,
    );

    if (!achievement?.status_rank || !achievement?.milestone) {
      throw new InternalServerError('target online checkin action id is not stored');
    }

    return {
      priority: achievement.status_rank,
      milestone: achievement.milestone,
    };
  }

  private async getOnlineCheckinActionInfo(action: ActionEntity): Promise<{ targetUrl: string }> {
    const onlineCheckin = await this.onlineCheckinActionRepository.selectOnlineCheckinActionById(
      action.action_id,
      action.service_id,
    );

    if (!onlineCheckin?.online_checkin_url) {
      throw new InternalServerError('target online checkin action id is not stored');
    }

    return {
      targetUrl: onlineCheckin.online_checkin_url,
    };
  }

  private async getQuestionnaireActionInfo(action: ActionEntity): Promise<{ questionnaireId: string }> {
    const questionnaire = await this.questionnaireActionRepository.selectQuestionnaireByIdAction(
      action.action_id,
      action.service_id,
    );

    if (!questionnaire?.questionnaire_id) {
      throw new InternalServerError('target questionnaire action id is not stored');
    }

    return {
      questionnaireId: questionnaire.questionnaire_id,
    };
  }

  private async getSerialCodeActionInfo(action: ActionEntity): Promise<{ serialCodeProjectId: string }> {
    const serialCode = await this.serialCodeActionRepository.selectSerialCodeActionById(
      action.action_id,
      action.service_id,
    );

    if (!serialCode) {
      throw new InternalServerError('target serial code action id is not stored');
    }

    return {
      serialCodeProjectId: serialCode.serial_code_project_id,
    };
  }
}
