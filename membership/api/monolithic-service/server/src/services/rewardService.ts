import { inject, injectable } from 'tsyringe';
import { RewardUsageStatus } from '../enum/rewardUsageStatus';
import { InternalServerError } from '../errors/internalServerError';
import { NotFoundError } from '../errors/notFoundError';
import { ValidationError } from '../errors/validationError';
import { AccountRepository } from '../repositories/accountRepository';
import { AchievementActionRepository } from '../repositories/achievementActionRepository';
import { ClaimedRewardRepository } from '../repositories/claimedRewardRepository';
import { QuestActivityRepository } from '../repositories/questActivityRepository';
import { ExtendedPointComponent, RewardRepository } from '../repositories/rewardRepository';
import { NftMintService } from './nftMintService';
import { reward2Nft } from '../enum/nftType';
import { NftTag } from '../enum/nftTag';
import { RewardAcquirementType } from '../enum/rewardAcquirementType';
import { QuestionnaireResultAnswerRepository } from '../repositories/questionnaireResultAnswerRepository';
import { ClaimedReward } from '../dtos/accounts/schemas';
import { RewardDetail } from '../dtos/services/schemas';
import { LanguageCode } from '../enum/languageCode';
import { TxType } from '../enum/txType';
import { match } from 'ts-pattern';
import { CertificateType } from '../enum/certificateType';
import { RewardEntity } from '../tables/rewardTable';
import { ConflictError } from '../errors/conflictError';
import { ExtendedNftComponent } from '../repositories/rewardRepository';
import { Database, db } from '../db/database';
import { Transaction } from 'kysely';
import { PointType } from '../enum/pointType';
import { RewardPointService } from './rewardPointService';
import { StatusPointService } from './statusPointService';
import { PointOpsActor } from '../enum/pointOpsActor';
import { PointTxDetail } from '../enum/pointTxDetail';
import { MetadataService } from './metadataService';
import { logger } from '../utils/middleware/loggerMiddleware';

@injectable()
export class RewardService {
  constructor(
    @inject('RewardRepository') private rewardRepository: RewardRepository,
    @inject('AccountRepository') private accountRepository: AccountRepository,
    @inject('ClaimedRewardRepository')
    private claimedRewardRepository: ClaimedRewardRepository,
    @inject('AchievementActionRepository')
    private achievementActionRepository: AchievementActionRepository,
    @inject('QuestActivityRepository')
    private questActivityRepository: QuestActivityRepository,
    @inject('NftMintService')
    private nftMintService: NftMintService,
    @inject('MetadataService') private metadataService: MetadataService,
    @inject('QuestionnaireResultAnswerRepository')
    private questionnaireResultAnswerRepository: QuestionnaireResultAnswerRepository,
    @inject('RewardPointService')
    private rewardPointService: RewardPointService,
    @inject('StatusPointService')
    private statusPointService: StatusPointService,
  ) {}

  async getReward(rewardId: string, serviceId: string, lang: LanguageCode): Promise<RewardDetail> {
    const reward = await this.rewardRepository.selectRewardQuestById(rewardId, serviceId, lang);
    if (!reward) throw new NotFoundError('reward not found');
    const { nftComponents, pointComponents } = await this.rewardRepository.findRewardComponents(serviceId, rewardId);
    if (!nftComponents.length && !pointComponents.length) throw new NotFoundError('Reward has no components');
    const response: RewardDetail = {
      rewardId: reward.reward_id,
      title: reward.reward_title,
      coverImageUrl: reward.reward_cover_image_url,
      description: reward.reward_description,
      acquirementType: reward.reward_acquirement_type,
      rewardPriorityType: reward.quest_reward_priority_type,
      nfts: nftComponents.map((nftComponent) => ({
        nftContractId: nftComponent.nft_contract_id,
        nftContractType: nftComponent.nft_contract_type,
      })),
      points: pointComponents.map((pointComponent) => ({
        amount: pointComponent.amount,
        pointType: pointComponent.point_type,
      })),
    };
    return response;
  }

  /**
   * Claims a reward for a given account and reward.
   *
   * @param accountId - The ID of the account claiming the reward.
   * @param rewardId - The ID of the reward to be claimed.
   * @param serviceId - The ID of the service associated with the reward.
   * @returns A promise that resolves to a `ClaimRewardResponse` object containing reward details.
   * @throws {NotFoundError} If the account is not published or no NFT contract details are found.
   * @throws {ValidationError} If the reward is not found or the achievement action is not completed.
   * @throws {InternalServerError} If the query to check reward linked activity status fails.
   * @throws {ConflictError} If the reward has already been claimed.
   */
  public async claimReward(accountId: string, rewardId: string, serviceId: string): Promise<ClaimedReward> {
    logger.info({ type: 'claim', input: { accountId: accountId, rewardId: rewardId, serviceId: serviceId } });
    // Input validation
    const account = await this.accountRepository.selectAccountById(accountId, serviceId);
    const tbaAddress = account?.token_bound_account_address;
    if (!account || !tbaAddress) throw new NotFoundError('Account is no published yet');

    const rewardType = await this.rewardRepository.selectRewardAcquirementType(rewardId, serviceId);
    let reward: RewardEntity;
    await match(rewardType)
      .with(RewardAcquirementType.DISTRIBUTION, async () => {
        try {
          reward = await this.rewardRepository.selectRewardById(rewardId, serviceId);
        } catch (error) {
          throw new NotFoundError(`Distribution type reward not found: ${error}`);
        }
      })
      .with(RewardAcquirementType.QUIZ, async () => {
        try {
          const questId: string = await this.rewardRepository.selectQuestIdByRewardId(rewardId, serviceId);
          const rankId = await this.questionnaireResultAnswerRepository.selectQuestionnaireResultRank(
            serviceId,
            accountId,
            questId,
          );
          if (!rankId) {
            throw new Error('rank is not found.');
          }
          reward = await this.rewardRepository.selectRewardsByRankId(questId, rankId, serviceId);
          if (!reward) {
            throw new NotFoundError('Quiz type reward not found');
          }
        } catch (error) {
          throw new NotFoundError(`Quiz type reward not found: ${error}`);
        }
      })
      .with(RewardAcquirementType.GACHA, () => {
        throw new InternalServerError('GACHA is not implemented yet');
      })
      .with(RewardAcquirementType.LOTTERY, () => {
        throw new InternalServerError('LOTTERY is not implemented yet');
      })
      .exhaustive();

    // Fetch all reward components
    const { nftComponents, pointComponents } = await this.rewardRepository.findRewardComponents(serviceId, rewardId);

    // 同一トランザクションでNFTとポイントを獲得
    const { nftResults, pointResults } = await db.transaction().execute(async (trx) => {
      // ポイント獲得処理
      const pointResults = await Promise.allSettled(
        pointComponents.map(async (pointComponent) => {
          return await this.claimRewardPoint(accountId, serviceId, rewardId, pointComponent, trx);
        }),
      );

      // NFT獲得処理
      const nftResults = await Promise.allSettled(
        nftComponents.map(async (nftComponent) => {
          // 既に獲得済みか確認
          const alreadyClaimed = await this.claimedRewardRepository.selectClaimedRewardById(
            accountId,
            nftComponent.reward_component_id,
            serviceId,
          );
          if (alreadyClaimed) {
            logger.error({ message: 'Reward has already been claimed.', alreadyClaimed: alreadyClaimed });
            throw new ConflictError('Reward has already been claimed.');
          }

          // 条件を満たしているか確認
          const isStatusCertificate = nftComponent.certificate_type === CertificateType.STATUS;
          const isCompleted = await this.isRewardLinkedActivityCompleted(
            serviceId,
            accountId,
            rewardId,
            isStatusCertificate,
          );
          if (isCompleted == undefined) throw new InternalServerError('Query failed');
          if (!isCompleted) throw new ValidationError('Achievement action did not completed');

          // NFTを獲得
          return await this.claimRewardNft(accountId, serviceId, nftComponent, tbaAddress, trx);
        }),
      );
      return { nftResults, pointResults };
    });

    // 獲得結果を分類
    const { succeeded: nfts, failedIds: failedNfts } = this.classifyClaimResults(nftResults, nftComponents);
    const { succeeded: points, failedIds: failedPoints } = this.classifyClaimResults(pointResults, pointComponents);
    const failedIds = failedNfts.concat(failedPoints);
    if (failedIds.length > 0) logger.error({ type: 'claimReward', failedIds });

    // レスポンスを作成
    const response: ClaimedReward = {
      rewardId,
      nfts: nfts,
      points: points,
      failedIds: failedIds.length > 0 ? failedIds : undefined,
    };
    logger.info({ type: 'claimReward', response: response });
    return response;
  }

  async claimRewardWithoutQuest(accountId: string, rewardId: string, serviceId: string): Promise<ClaimedReward> {
    const account = await this.accountRepository.selectAccountById(accountId, serviceId);
    if (!account || !account.token_bound_account_address) throw new NotFoundError('Account is no published yet');

    const reward = await this.rewardRepository.selectRewardById(rewardId, serviceId);
    if (!reward) throw new NotFoundError('Reward not found');

    // Fetch all reward components
    const { nftComponents, pointComponents } = await this.rewardRepository.findRewardComponents(serviceId, rewardId);

    const { nftResults, pointResults } = await db.transaction().execute(async (trx) => {
      const pointResults = await Promise.allSettled(
        pointComponents.map(
          async (pointComponent) => await this.claimRewardPoint(accountId, serviceId, rewardId, pointComponent, trx),
        ),
      );
      const nftResults = await Promise.allSettled(
        nftComponents.map(
          async (nftComponent) =>
            await this.claimRewardNft(accountId, serviceId, nftComponent, account.token_bound_account_address!, trx),
        ),
      );
      return { nftResults, pointResults };
    });

    const { succeeded: nfts, failedIds: failedNfts } = this.classifyClaimResults(nftResults, nftComponents);
    const { succeeded: points, failedIds: failedPoints } = this.classifyClaimResults(pointResults, pointComponents);
    const failedIds = failedNfts.concat(failedPoints);
    if (failedIds.length > 0) logger.error({ type: 'claimReward', failedIds });

    const response = {
      rewardId,
      nfts: nfts,
      points: points,
      failedIds: failedIds.length > 0 ? failedIds : undefined,
    };
    logger.info({ type: 'claim', response: response });
    return response;
  }

  private async isRewardLinkedActivityCompleted(
    serviceId: string,
    accountId: string,
    rewardId: string,
    isStatusCertificate: boolean,
  ) {
    let alreadyCompleted: boolean | undefined;
    if (isStatusCertificate) {
      // Check to see if Action has been completed
      alreadyCompleted = await this.achievementActionRepository.checkAchievementActionCompletedWithRewardId(
        accountId,
        rewardId,
        serviceId,
      );
    } else {
      // Check to see if the Quest has been completed
      alreadyCompleted = await this.questActivityRepository.checkQuestCompletedWithRewardId(
        accountId,
        rewardId,
        serviceId,
      );
    }
    return alreadyCompleted;
  }

  // NFT取得処理を共通化
  private async claimRewardNft(
    accountId: string,
    serviceId: string,
    nftComponent: ExtendedNftComponent,
    tbaAddress: string,
    trx: Transaction<Database>,
  ): Promise<{
    title: string;
    coverImageUrl: string;
    rewardType: NftTag;
  }> {
    const nftType = reward2Nft(nftComponent.nft_contract_type);

    const { contractAddress, queueId } = await this.nftMintService.mint(
      serviceId,
      accountId,
      TxType.MINT_REWARD,
      tbaAddress,
      nftComponent.nft_contract_id,
      nftType,
      nftComponent.token_id ?? undefined,
      trx,
    );
    logger.info({ type: 'claim', mintResponse: { contractAddress, queueId } });

    await this.claimedRewardRepository.insertClaimedReward(
      {
        account_id: accountId,
        reward_component_id: nftComponent.reward_component_id,
        service_id: serviceId,
        reward_usage_status: RewardUsageStatus.MINTING,
        queue_id: queueId,
        claim_date: new Date(),
      },
      trx,
    );

    const metadata = await this.metadataService.getParsedMetadataFromBaseMetadata(
      contractAddress,
      nftComponent.token_id ?? undefined,
    );
    logger.info({ type: 'claim', metadata });

    return {
      title: metadata.name,
      coverImageUrl: metadata.image,
      rewardType: metadata.attributes?.find((a) => a.trait_type === 'tags')?.value as NftTag,
    };
  }

  private async claimRewardPoint(
    accountId: string,
    serviceId: string,
    rewardId: string,
    pointComponent: ExtendedPointComponent,
    trx: Transaction<Database>,
  ): Promise<{
    amount: number;
    pointType: PointType;
  }> {
    // 取得しているかの確認
    const alreadyClaimed = await this.claimedRewardRepository.selectClaimedRewardById(
      accountId,
      pointComponent.reward_component_id,
      serviceId,
    );
    if (alreadyClaimed) {
      throw new ConflictError('Reward has already been claimed.');
    }

    const { amount, point_type, expires_on } = pointComponent;
    const tx_by = PointOpsActor.USER;
    const tx_detail = PointTxDetail.REWARD;
    match(point_type)
      .with(PointType.REWARD, async () => {
        await this.rewardPointService.addRewardPoint(
          serviceId,
          accountId,
          amount,
          tx_by,
          tx_detail,
          expires_on?.toISOString(),
          trx,
          rewardId,
        );
      })
      .with(PointType.STATUS, async () => {
        await this.statusPointService.addStatusPoint(serviceId, accountId, amount, tx_by, tx_detail, trx, rewardId);
      })
      .exhaustive();
    await this.claimedRewardRepository.insertClaimedReward(
      {
        account_id: accountId,
        reward_component_id: pointComponent.reward_component_id,
        service_id: serviceId,
        reward_usage_status: RewardUsageStatus.ACTIVE,
        claim_date: new Date(),
      },
      trx,
    );
    return { amount, pointType: point_type };
  }

  private classifyClaimResults<T>(
    results: PromiseSettledResult<T>[],
    components: ExtendedNftComponent[] | ExtendedPointComponent[],
  ): {
    succeeded: T[];
    failedIds: { rewardComponentId: string; error?: string }[];
  } {
    return results.reduce(
      (acc, result, i) => {
        if (result.status === 'fulfilled') {
          acc.succeeded.push(result.value);
        } else {
          acc.failedIds.push({
            rewardComponentId: components[i].reward_component_id,
            error: result.reason instanceof Error ? result.reason.message : String(result.reason),
          });
        }
        return acc;
      },
      { succeeded: [], failedIds: [] } as {
        succeeded: T[];
        failedIds: { rewardComponentId: string; error?: string }[];
      },
    );
  }
}
