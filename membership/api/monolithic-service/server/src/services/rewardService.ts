import { inject, injectable } from 'tsyringe';
import { NotFoundError } from '../errors/notFoundError';
import { RewardRepository } from '../repositories/rewardRepository';
import { AccountRepository } from '../repositories/accountRepository';
import { RewardType } from '../enum/rewardType';
import { ValidationError } from '../errors/validationError';
import { RewardEntity } from '../tables/rewardTable';
import { CertificateRewardRepository } from '../repositories/certificateRewardRepository';
import { CouponRewardRepository } from '../repositories/couponRewardRepository';
import { DigitalContentRewardRepository } from '../repositories/digitalContentRewardRepository';
import { ClaimedRewardRepository } from '../repositories/claimedRewardRepository';
import { RewardUsageStatus } from '../enum/rewardUsageStatus';
import { ConflictError } from '../errors/conflictError';
import { AchievementActionRepository } from '../repositories/achievementActionRepository';
import { InternalServerError } from '../errors/internalServerError';
import { QuestActivityRepository } from '../repositories/questActivityRepository';
import { NftMintService } from './nftMintService';
import { reward2Nft } from '../enum/nftType';
import { NftsService } from './nftsService';
import { MetadataService } from './metadataService';
import { logger } from '../utils/logger';
import { NftTag } from '../enum/nftTag';
import { RewardAcquirementType } from '../enum/rewardAcquirementType';
import { QuestionnaireResultAnswerRepository } from '../repositories/questionnaireResultAnswerRepository';
import { ClaimedReward } from '../dtos/accounts/schemas';
import { RewardDetail } from '../dtos/services/schemas';
import { LanguageCode } from '../enum/languageCode';
import { TxType } from '../enum/txType';

@injectable()
export class RewardService {
  constructor(
    @inject('RewardRepository') private rewardRepository: RewardRepository,
    @inject('AccountRepository') private accountRepository: AccountRepository,
    @inject('CertificateRewardRepository')
    private certificateRewardRepository: CertificateRewardRepository,
    @inject('CouponRewardRepository')
    private couponRewardRepository: CouponRewardRepository,
    @inject('DigitalContentRewardRepository')
    private digitalContentRewardRepository: DigitalContentRewardRepository,
    @inject('ClaimedRewardRepository')
    private claimedRewardRepository: ClaimedRewardRepository,
    @inject('AchievementActionRepository')
    private achievementActionRepository: AchievementActionRepository,
    @inject('QuestActivityRepository')
    private questActivityRepository: QuestActivityRepository,
    @inject('NftMintService')
    private nftMintService: NftMintService,
    @inject('NftsService') private nftsService: NftsService,
    @inject('MetadataService') private metadataService: MetadataService,
    @inject('QuestionnaireResultAnswerRepository')
    private questionnaireResultAnswerRepository: QuestionnaireResultAnswerRepository,
  ) {}

  async getReward(rewardId: string, serviceId: string, lang: LanguageCode): Promise<RewardDetail> {
    const reward = await this.rewardRepository.selectRewardQuestById(rewardId, serviceId, lang);
    if (!reward) throw new NotFoundError('reward not found');

    const response: RewardDetail = {
      rewardId: reward.reward_id,
      title: reward.reward_title,
      coverImageUrl: reward.reward_cover_image_url,
      description: reward.reward_description,
      acquirementType: reward.reward_acquirement_type,
      rewardType: reward.reward_type,
      rewardPriorityType: reward.quest_reward_priority_type,
    };

    return response;
  }

  /**
   * Claims a reward for a given account and reward.
   *
   * @param accountId - The ID of the account claiming the reward.
   * @param rewardId - The ID of the reward to be claimed.
   * @param serviceId - The ID of the service associated with the reward.
   * @returns A promise that resolves to a `ClaimRewardResponse` object containing reward details.
   * @throws {NotFoundError} If the account is not published or no NFT contract details are found.
   * @throws {ValidationError} If the reward is not found or the achievement action is not completed.
   * @throws {InternalServerError} If the query to check reward linked activity status fails.
   * @throws {ConflictError} If the reward has already been claimed.
   */
  public async claimReward(accountId: string, rewardId: string, serviceId: string): Promise<ClaimedReward> {
    logger.info({ type: 'claim', input: { accountId: accountId, rewardId: rewardId, serviceId: serviceId } });
    // Input validation
    const account = await this.accountRepository.selectAccountById(accountId, serviceId);
    if (!account || !account.token_bound_account_address) throw new NotFoundError('Account is no published yet');

    const rewardType = await this.rewardRepository.slectRewardAcquirementType(rewardId, serviceId);
    let reward: RewardEntity;
    switch (rewardType) {
      case RewardAcquirementType.DISTRIBUTION:
        try {
          reward = await this.rewardRepository.selectRewardById(rewardId, serviceId);
        } catch (error) {
          throw new NotFoundError(`Distribution type reward not found: ${error}`);
        }
        break;
      case RewardAcquirementType.QUIZ: {
        try {
          const questId: string = await this.rewardRepository.selectQuestIdByRewardId(rewardId, serviceId);
          const rankId = await this.questionnaireResultAnswerRepository.selectQuestionnaireResultRank(
            serviceId,
            accountId,
            questId,
          );
          if (!rankId) {
            throw new Error('rank is not found.');
          }
          reward = await this.rewardRepository.selectRewardsByRankId(questId, rankId, serviceId);
        } catch (error) {
          throw new NotFoundError(`Quiz type reward not found: ${error}`);
        }
        break;
      }
      case RewardAcquirementType.GACHA:
        throw new InternalServerError('GACHA is not implemented yet');
      case RewardAcquirementType.LOTTERY:
        throw new InternalServerError('LOTTERY is not implemented yet');
    }

    // Check the status of activity (achievement action and quest) equals to completed
    const isCompleted = this.isRewardLinkedActivityCompleted(serviceId, accountId, rewardId);
    if (isCompleted == undefined) throw new InternalServerError('Query failed');
    if (!isCompleted) throw new ValidationError('Achievement action did not completed');

    // Check the status of reward activity is not recorded as already claimed
    const alreadyClaimed = await this.claimedRewardRepository.selectClaimedRewardById(accountId, rewardId, serviceId);
    if (alreadyClaimed) throw new ConflictError('Reward has already been claimed.');

    // Fetch Mint target contract data
    const contractDetails = await this.fetchNftContractDetails(reward, serviceId);
    if (!contractDetails) throw new NotFoundError('No NFT contract details found.');
    logger.info({ type: 'claim', body: { contract_detail: contractDetails } });

    // Execute mint transaction
    const nftType = reward2Nft(reward.reward_type);
    const nftMint = await this.nftMintService.mint(
      serviceId,
      accountId,
      TxType.MINT_REWARD,
      account.token_bound_account_address,
      contractDetails.nft_contract_id,
      nftType,
      contractDetails?.token_id,
    );
    logger.info({ type: 'claim', mintResponse: nftMint });

    // Store transaction status to Metadata and claimedRewardActivity
    const { contractAddress, queueId } = nftMint;

    await this.claimedRewardRepository.insertClaimedReward({
      account_id: accountId,
      reward_id: rewardId,
      service_id: serviceId,
      reward_usage_status: RewardUsageStatus.MINTING,
      queue_id: queueId,
      claim_date: new Date(),
    });
    logger.info({ type: 'claim', insertClaimedReward: true });

    // Fetch metadata locally (from DB)
    const metadata = await this.metadataService.getParsedMetadataFromBaseMetadata(
      contractAddress,
      contractDetails?.token_id,
    );
    logger.info({ type: 'claim', metadata: metadata });

    const response = {
      rewardId,
      title: metadata.name,
      coverImageUrl: metadata.image,
      rewardType:
        (metadata.attributes?.find((attr) => attr.trait_type == 'tags')?.value as NftTag) ?? NftTag.DIGITAL_CONTENT,
    };

    logger.info({ type: 'claim', response: response });
    return response;
  }

  async claimRewardWithoutQuest(accountId: string, rewardId: string, serviceId: string): Promise<ClaimedReward> {
    const account = await this.accountRepository.selectAccountById(accountId, serviceId);
    if (!account || !account.token_bound_account_address) throw new NotFoundError('Account is no published yet');

    const reward = await this.rewardRepository.selectRewardById(rewardId, serviceId);
    if (!reward) throw new NotFoundError('Reward not found');

    // Fetch Mint target contract data
    const contractDetails = await this.fetchNftContractDetails(reward, serviceId);
    if (!contractDetails) throw new NotFoundError('No NFT contract details found.');

    // Execute mint transaction
    const nftType = reward2Nft(reward.reward_type);
    const nftMint = await this.nftMintService.mint(
      serviceId,
      accountId,
      TxType.MINT_REWARD,
      account.token_bound_account_address,
      contractDetails.nft_contract_id,
      nftType,
      contractDetails?.token_id,
    );
    logger.info({ type: 'claim', mintResponse: nftMint });

    // Store transaction status to Metadata and claimedRewardActivity
    const { contractAddress } = nftMint;

    // Fetch metadata locally (from DB)
    const metadata = await this.metadataService.getParsedMetadataFromBaseMetadata(
      contractAddress,
      contractDetails?.token_id,
    );
    logger.info({ type: 'claim', metadata: metadata });

    const response = {
      rewardId,
      title: metadata.name,
      coverImageUrl: metadata.image,
      rewardType:
        (metadata.attributes?.find((attr) => attr.trait_type == 'tags')?.value as NftTag) ?? NftTag.DIGITAL_CONTENT,
    };

    return response;
  }

  private async isRewardLinkedActivityCompleted(serviceId: string, accountId: string, rewardId: string) {
    const statusCertificate = await this.certificateRewardRepository.checkStatusCertificateRewardById(
      rewardId,
      serviceId,
    );

    let alreadyCompleted: boolean | undefined;
    if (!statusCertificate) {
      // Check to see if Action has been completed
      alreadyCompleted = await this.achievementActionRepository.checkAchievementActionCompletedWithRewardId(
        accountId,
        rewardId,
        serviceId,
      );
    } else {
      // Check to see if the Quest has been completed
      alreadyCompleted = await this.questActivityRepository.checkQuestCompletedWithRewardId(
        accountId,
        rewardId,
        serviceId,
      );
    }

    return alreadyCompleted;
  }

  private async fetchNftContractDetails(
    reward: RewardEntity,
    serviceId: string,
  ): Promise<{ nft_contract_id: string; token_id?: number }> {
    // Fetch data with Reward NFT type
    switch (reward.reward_type) {
      case RewardType.COUPON: {
        const nft = await this.couponRewardRepository.selectCouponRewardById(reward.reward_id, serviceId);
        if (nft == undefined) {
          throw new NotFoundError('Coupon reward not found');
        }
        return {
          nft_contract_id: nft.nft_contract_id,
          token_id: nft.token_id,
        };
      }
      case RewardType.CONTENT: {
        const nft = await this.digitalContentRewardRepository.selectDigitalContentRewardById(
          reward.reward_id,
          serviceId,
        );
        if (nft == undefined) {
          throw new NotFoundError('Digital content reward not found');
        }
        return {
          nft_contract_id: nft.nft_contract_id,
          token_id: undefined,
        };
      }
      case RewardType.CERTIFICATE: {
        const nft = await this.certificateRewardRepository.selectCertificateRewardById(reward.reward_id, serviceId);
        if (nft == undefined) {
          throw new NotFoundError('Certificate reward not found');
        }
        return {
          nft_contract_id: nft.nft_contract_id,
          token_id: undefined,
        };
      }
      default:
        throw new NotFoundError('Reward type not supported for minting: ' + reward.reward_type);
    }
  }
}
