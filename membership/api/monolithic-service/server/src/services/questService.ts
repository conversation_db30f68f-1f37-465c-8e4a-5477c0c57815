import { inject, injectable } from 'tsyringe';
import { v4 as uuidv4 } from 'uuid';
import { QuestActivityDetail, QuestActivityStatus } from '../dtos/accounts/schemas';
import { NftRegister } from '../dtos/nfts/schemas';
import {
  QuestActionResponse,
  QuestDetailResponse,
  QuestRewardResponse,
} from '../dtos/quests/response/questDetailResponse';
import { QuestListItemResponse } from '../dtos/quests/response/questResponseItem';
import {
  AchievementAction,
  CreateActionRequest,
  CreateActionResponse,
  QuestCreateRequest,
  QuestCreateResponse,
  QuestDetail,
  QuestItem,
  StatusQuest,
  CreateQuestRewardRequest,
  CreateQuestRewardResponse,
  CreateRewardRequest,
  CreateRewardResponse,
} from '../dtos/services/schemas';

import { ActionType } from '../enum/actionType';
import { languageCode, LanguageCode } from '../enum/languageCode';
import { NftType } from '../enum/nftType';
import { QuestRewardPriorityType } from '../enum/questRewardPriorityType';
import { QuestStatus } from '../enum/questStatus';
import { QuestType } from '../enum/questType';
import { RewardType } from '../enum/rewardType';
import { InternalServerError } from '../errors/internalServerError';
import { NotFoundError } from '../errors/notFoundError';
import { ValidationError } from '../errors/validationError';
import { AchievementActionRepository } from '../repositories/achievementActionRepository';
import { ActionActivityRepository } from '../repositories/actionActivityRepository';
import { ActionRepository } from '../repositories/actionRepository';
import { ClaimedRewardRepository } from '../repositories/claimedRewardRepository';
import { NftContractTypesRepository } from '../repositories/nftContractTypesRepository';
import { OnlineCheckinActionRepository } from '../repositories/onlineCheckinActionRepository';
import { QrCheckinActionRepository } from '../repositories/qrCheckinActionRepository';
import { QuestActionRepository } from '../repositories/questActionRepository';
import { QuestionnaireActionRepository } from '../repositories/questionnaireActionRepository';
import { QuestionnaireResultRankRepository } from '../repositories/questionnaireResultRankRepository';
import { QuestRepository, QuestWithReward } from '../repositories/questRepository';
import { QuestRewardRepository } from '../repositories/questRewardRepository';
import { RewardRepository } from '../repositories/rewardRepository';
import { ServiceInfoRepository } from '../repositories/serviceInfoRepository';
import { InsertableActionRow } from '../tables/actionTable';
import { InsertableCertificateRewardRow } from '../tables/certificateRewardTable';
import { InsertableCouponRewardRow } from '../tables/couponRewardTable';
import { InsertableDigitalContentRewardRow } from '../tables/digitalContentRewardTable';
import { InsertableQuestRewardRow } from '../tables/questRewardTable';
import { QuestEntity } from '../tables/questTable';
import { InsertableRewardRow } from '../tables/rewardTable';
import { NftRegisterService } from './nftRegisterService';
import { SerialCodeActionRepository } from '../repositories/serialCodeActionRepository';
import { LocationCheckinActionRepository } from '../repositories/locationCheckinActionRepository';
import { RewardAcquirementType } from '../enum/rewardAcquirementType';

@injectable()
export class QuestService {
  constructor(
    @inject('QuestRepository')
    private questRepository: QuestRepository,
    @inject('ActionRepository')
    private actionRepository: ActionRepository,
    @inject('RewardRepository')
    private rewardRepository: RewardRepository,
    @inject('QuestActionRepository')
    private questActionRepository: QuestActionRepository,
    @inject('ActionActivityRepository')
    private actionActivityRepository: ActionActivityRepository,
    @inject('QuestRewardRepository')
    private questRewardRepository: QuestRewardRepository,
    @inject('ClaimedRewardRepository')
    private claimedRewardRepository: ClaimedRewardRepository,
    @inject('ServiceInfoRepository')
    private serviceInfoRepository: ServiceInfoRepository,
    @inject('NftContractTypesRepository')
    private nftContractTypesRepository: NftContractTypesRepository,
    @inject('NftRegisterService')
    private nftRegisterService: NftRegisterService,
    @inject('QrCheckinActionRepository')
    private qrCheckinActionRepository: QrCheckinActionRepository,
    @inject('OnlineCheckinActionRepository')
    private onlineCheckinActionRepository: OnlineCheckinActionRepository,
    @inject('AchievementActionRepository')
    private achievementActionRepository: AchievementActionRepository,
    @inject('QuestionnaireActionRepository')
    private questionnaireActionRepository: QuestionnaireActionRepository,
    @inject('SerialCodeActionRepository')
    private serialCodeActionRepository: SerialCodeActionRepository,
    @inject('LocationCheckinActionRepository')
    private locationCheckinActionRepository: LocationCheckinActionRepository,
    @inject('QuestionnaireResultRankRepository')
    private questionnaireResultRankRepository: QuestionnaireResultRankRepository,
  ) { }

  async getQuests(serviceId: string, lang: LanguageCode): Promise<QuestItem[]> {
    const quests = await this.questRepository.selectQuestsWithRewards(serviceId, lang);

    const questMap = new Map<string, QuestWithReward>();

    quests.forEach((quest) => {
      const existingQuest = questMap.get(quest.quest_id);

      // Check if the existing quest is not present or if the current quest has a 'MAIN' priority.
      // If both existing and current quests have 'SUB' priority, choose the one with the smaller reward_id.
      if (
        !existingQuest ||
        quest.quest_reward_priority_type === QuestRewardPriorityType.MAIN ||
        (existingQuest.quest_reward_priority_type === QuestRewardPriorityType.SUB &&
          quest.reward_id < existingQuest.reward_id)
      ) {
        questMap.set(quest.quest_id, quest);
      }
    });

    return Array.from(questMap.values()).map((quest) => {
      const response: QuestListItemResponse = {
        questId: quest.quest_id,
        title: quest.quest_title,
        thumbnailImageUrl: quest.quest_thumbnail_image_url,
        questType: quest.quest_type as QuestType,
        mainRewardTitle: quest.reward_title,
        mainRewardThumbnailImageUrl: quest.reward_thumbnail_image_url,
        orderIndex: quest.order_index,
        startedAt: quest.quest_available_start_date.toISOString(),
        expiredAt: quest.quest_available_end_date.toISOString(),
      };
      return response;
    });
  }

  async getQuest(questId: string, serviceId: string, lang: LanguageCode): Promise<QuestDetail> {
    const quest = await this.questRepository.selectQuestById(questId, serviceId, lang);
    if (!quest) {
      throw new NotFoundError();
    }

    const [rewardEntities, actionEntities] = await Promise.all([
      this.rewardRepository.selectRewardsByQuestId(questId, serviceId, lang),
      this.actionRepository.selectActionsByQuestId(questId, serviceId, lang),
    ]);

    const rewards: QuestRewardResponse[] = rewardEntities.map((reward) => {
      return {
        rewardId: reward.reward_id,
        title: reward.reward_title,
        thumbnailImageUrl: reward.reward_thumbnail_image_url,
        rewardPriorityType: reward.quest_reward_priority_type,
        orderIndex: reward.order_index,
      };
    });

    const actions: QuestActionResponse[] = actionEntities.map((action) => {
      return {
        actionId: action.action_id,
        title: action.action_title,
        availableStartDate: action.action_available_start_date.toISOString(),
        availableEndDate: action.action_available_end_date.toISOString(),
        thumbnailImageUrl: action.action_thumbnail_image_url,
        actionType: action.action_type,
        orderIndex: action.order_index,
      };
    });

    const response: QuestDetailResponse = {
      questId: quest.quest_id,
      title: quest.quest_title,
      description: quest.quest_description,
      coverImageUrl: quest.quest_cover_image_url,
      startedAt: quest.quest_available_start_date.toISOString(),
      expiredAt: quest.quest_available_end_date.toISOString(),
      questType: quest.quest_type,
      rewards: rewards,
      actions: actions,
    };

    return response;
  }

  async getAccountQuests(
    accountId: string,
    serviceId: string,
    rewardStatus?: string,
    questStatus?: string,
    startAtFrom?: string,
    startAtTo?: string,
    expireAtFrom?: string,
    expireAtTo?: string,
  ): Promise<QuestActivityStatus> {
    const questIds = await this.questRepository.selectQuests(
      accountId,
      serviceId,
      rewardStatus,
      questStatus,
      startAtFrom,
      startAtTo,
      expireAtFrom,
      expireAtTo,
    );
    return {
      questIds,
    };
  }

  async getActiveStatusQuest(serviceId: string, lang: LanguageCode): Promise<StatusQuest[]> {
    const activeQuests = await this.questRepository.selectActiveStatusQuest(serviceId, lang);
    if (activeQuests.length === 0) return [];
    if (activeQuests.length > 1) throw new InternalServerError();

    const actionEntities = await this.actionRepository.selectAchievementActionsByQuestId(
      activeQuests[0].quest_id,
      serviceId,
      lang,
    );
    if (actionEntities.length === 0) throw new NotFoundError();
    const actions = actionEntities.map((action) => {
      const achievementAction: AchievementAction = {
        actionId: action.action_id,
        title: action.action_title,
        rewardId: action.reward_id,
        statusRank: action.status_rank,
        milestone: action.milestone,
      };
      return achievementAction;
    });
    return [
      {
        questId: activeQuests[0].quest_id,
        title: activeQuests[0].quest_title,
        description: activeQuests[0].quest_description,
        startedAt: activeQuests[0].quest_available_start_date.toISOString(),
        expiredAt: activeQuests[0].quest_available_end_date.toISOString(),
        actions: actions,
      },
    ];
  }

  async getAccountQuest(
    accountId: string,
    questId: string,
    serviceId: string,
    lang: LanguageCode,
  ): Promise<QuestActivityDetail> {
    const quest = await this.questRepository.selectQuestById(questId, serviceId, lang);
    if (!quest) throw new NotFoundError();

    const claimedRewardIds = await this.claimedRewardRepository.selectAllClaimedRewardByClearedQuests(
      accountId,
      questId,
      serviceId,
    );

    const questActions = await this.questActionRepository.selectQuestActionsByQuestId(questId, serviceId);
    const clearedActionIds = await this.actionActivityRepository.selectAllActionIdByCompletedAction(
      accountId,
      questId,
      serviceId,
    );
    const questStatus = this.determineQuestStatus(clearedActionIds, questActions.length);

    return {
      questStatus,
      claimedRewardIds,
      clearedActionIds,
    };
  }

  async createQuestAction(
    serviceId: string,
    questId: string,
    requestBody: CreateActionRequest,
  ): Promise<CreateActionResponse> {
    const service = await this.serviceInfoRepository.getServiceById(serviceId);
    if (!service) {
      throw new ValidationError('Invalid Service ID');
    }
    const quest = await this.questRepository.selectQuestById(questId, serviceId, languageCode.JA);
    if (!quest) throw new NotFoundError('Quest ID not found for this Service');

    if (requestBody.actionDetailInfo.type === ActionType.ACHIEVEMENT) {
      const { rewardId } = requestBody.actionDetailInfo;
      const reward = await this.rewardRepository.selectRewardById(rewardId, serviceId);
      if (!reward) {
        throw new ValidationError('Reward does not exits');
      }
    }

    const actionId = uuidv4();
    const actionData: InsertableActionRow = {
      action_id: actionId,
      service_id: serviceId,
      action_cover_image_url: requestBody.coverImageUrl,
      action_thumbnail_image_url: requestBody.thumbnailImageUrl,
      action_available_start_date: new Date(requestBody.availableStartDate),
      action_available_end_date: new Date(requestBody.availableEndDate),
      action_type: requestBody.actionDetailInfo.type,
      order_index: requestBody.orderIndex,
    };
    const action = await this.actionRepository.insertAction(serviceId, actionData);

    const actionTranslations = requestBody.actionTranslations.map((t) => {
      return {
        action_id: action.action_id,
        service_id: serviceId,
        language: t.language,
        action_title: t.title,
        action_description: t.description,
        action_label: t.label,
      };
    });
    const createdActionTranslations = await this.actionRepository.insertActionTranslation(
      serviceId,
      actionTranslations,
    );

    const questActionData = {
      action_id: action.action_id,
      quest_id: questId,
      service_id: serviceId,
    };

    await this.questActionRepository.insertQuestAction(questActionData);

    switch (requestBody.actionDetailInfo.type) {
      case ActionType.QR_CHECKIN:
        await this.qrCheckinActionRepository.insertQrCheckinAction(serviceId, {
          action_id: action.action_id,
          service_id: serviceId,
          qr_verification_data: requestBody.actionDetailInfo.qrVerificationData,
        });
        break;

      case ActionType.ONLINE_CHECKIN:
        await this.onlineCheckinActionRepository.insertOnlineCheckinAction(serviceId, {
          action_id: action.action_id,
          service_id: serviceId,
          online_checkin_url: requestBody.actionDetailInfo.targetUrl,
        });
        break;

      case ActionType.ACHIEVEMENT: {
        const { rewardId, milestone, statusRank } = requestBody.actionDetailInfo;
        await this.achievementActionRepository.insertAchievementAction(serviceId, {
          action_id: action.action_id,
          service_id: serviceId,
          reward_id: rewardId,
          milestone: milestone,
          status_rank: statusRank,
        });
        break;
      }

      case ActionType.QUESTIONNAIRE:
        await this.questionnaireActionRepository.insertQuestionnaireAction(serviceId, {
          action_id: action.action_id,
          service_id: serviceId,
          questionnaire_id: requestBody.actionDetailInfo.questionnaireId,
        });
        break;

      case ActionType.SERIAL_CODE:
        await this.serialCodeActionRepository.insertSerialCodeAction(serviceId, {
          action_id: action.action_id,
          service_id: serviceId,
          serial_code_project_id: requestBody.actionDetailInfo.serialCodeProjectId,
        });
        break;
      case ActionType.LOCATION_CHECKIN:

        await this.locationCheckinActionRepository.insertLocationCheckinAction(serviceId, {
          action_id: action.action_id,
          service_id: serviceId,
          geofence_id: requestBody.actionDetailInfo.geofenceId,
        });
        break;
    }

    const response: CreateActionResponse = {
      actionId: action.action_id,
      serviceId: action.service_id,
      actionTranslations: createdActionTranslations.map((t) => {
        return {
          language: t.language,
          title: t.action_title,
          description: t.action_description,
        };
      }),
      coverImageUrl: action.action_cover_image_url,
      thumbnailImageUrl: action.action_thumbnail_image_url,
      availableStartDate: action.action_available_start_date.toISOString(), // ISO 8601 形式
      availableEndDate: action.action_available_end_date.toISOString(), // ISO 8601 形式
      actionDetailInfo: requestBody.actionDetailInfo,
      orderIndex: requestBody.orderIndex,
    };

    return response;
  }

  async createQuest(serviceId: string, requestBody: QuestCreateRequest): Promise<QuestCreateResponse> {
    const service = await this.serviceInfoRepository.getServiceById(serviceId);
    if (!service) {
      throw new ValidationError('Invalid Service ID');
    }

    // Prepare Quest Data
    const questId = uuidv4();
    const newQuest: QuestEntity = {
      quest_id: questId,
      service_id: serviceId,
      quest_cover_image_url: requestBody.coverImageUrl,
      quest_thumbnail_image_url: requestBody.thumbnailImageUrl,
      quest_available_start_date: new Date(requestBody.availableStartDate),
      quest_available_end_date: new Date(requestBody.availableEndDate),
      quest_type: requestBody.questType,
      order_index: requestBody.orderIndex,
    };

    // Insert into DB
    const createdQuest = await this.questRepository.insertQuest(newQuest);

    const questTranslations = requestBody.questTranslations.map((translation) => {
      return {
        quest_id: questId,
        service_id: serviceId,
        language: translation.language,
        quest_title: translation.title,
        quest_description: translation.description,
      };
    });
    const createdQuestTranslations = await this.questRepository.insertQuestTranslations(questTranslations);

    const response: QuestCreateResponse = {
      questId: questId,
      questTranslations: createdQuestTranslations.map((t) => {
        return {
          language: t.language,
          title: t.quest_title,
          description: t.quest_description,
        };
      }),
      coverImageUrl: createdQuest.quest_cover_image_url,
      thumbnailImageUrl: createdQuest.quest_thumbnail_image_url,
      availableStartDate: createdQuest.quest_available_start_date.toISOString(),
      availableEndDate: createdQuest.quest_available_end_date.toISOString(),
      questType: createdQuest.quest_type,
      orderIndex: createdQuest.order_index,
    };
    return response;
  }

  async createQuestReward(
    questId: string,
    serviceId: string,
    requestBody: CreateQuestRewardRequest,
  ): Promise<CreateQuestRewardResponse> {
    const service = await this.serviceInfoRepository.getServiceById(serviceId);
    if (!service) {
      throw new ValidationError('Invalid Service ID');
    }

    const quest = await this.questRepository.selectQuestById(questId, serviceId, languageCode.JA);
    if (!quest) {
      throw new ValidationError('Quest does not belong to the specified ServiceId');
    }

    const nftContractType = await this.nftContractTypesRepository.selectNftContractTypeByNftType(
      requestBody.rewardType as unknown as NftType,
    );

    if (!nftContractType || (nftContractType.nft_type as unknown as RewardType) !== requestBody.rewardType) {
      throw new ValidationError('Invalid RewardType');
    }

    const insertNft: NftRegister = {
      nftContractTypeId: nftContractType.nft_contract_type_id,
      nftName: requestBody.nftName,
      nftSymbol: requestBody.nftSymbol,
      nftCollectionName: requestBody.nftCollectionName,
      metadata: requestBody.nftMetadata,
      deliveryImageUrl: requestBody.deliveryImageUrl,
    };
    const nftRegisterResponse = await this.nftRegisterService.register(serviceId, insertNft);
    const nftContractId = nftRegisterResponse.nftContractId;

    const rewardId = uuidv4();
    let certificateReward: InsertableCertificateRewardRow | undefined;
    let contentReward: InsertableDigitalContentRewardRow | undefined;
    let couponReward: InsertableCouponRewardRow | undefined;

    switch (requestBody.rewardType) {
      case RewardType.CERTIFICATE: {
        certificateReward = {
          reward_id: rewardId,
          service_id: serviceId,
          nft_contract_id: nftContractId,
          certificate_type: requestBody.certificateReward!.certificateType,
          status_certificate_rank: requestBody.certificateReward?.statusCertificateRank ?? undefined,
        };
        contentReward = undefined;
        couponReward = undefined;
        break;
      }
      case RewardType.CONTENT: {
        certificateReward = undefined;
        contentReward = {
          reward_id: rewardId,
          service_id: serviceId,
          nft_contract_id: nftContractId,
        };
        couponReward = undefined;
        break;
      }
      case RewardType.COUPON: {
        certificateReward = undefined;
        contentReward = undefined;
        couponReward = {
          reward_id: rewardId,
          service_id: serviceId,
          nft_contract_id: nftContractId,
          token_id: 0,
        };
        break;
      }
    }

    const reward: InsertableRewardRow = {
      reward_id: rewardId,
      service_id: serviceId,
      reward_cover_image_url: requestBody.coverImageUrl,
      reward_thumbnail_image_url: requestBody.thumbnailImageUrl,
      reward_type: requestBody.rewardType,
      order_index: requestBody.orderIndex,
    };

    let questReward: InsertableQuestRewardRow = {
      reward_id: rewardId,
      service_id: serviceId,
      quest_id: questId,
      reward_acquirement_type: requestBody.acquirementType,
      quest_reward_priority_type: requestBody.questRewardPriorityType,
    };

    if (requestBody.selector !== undefined) {
      if (requestBody.selector.gachaWeight !== undefined) {
        questReward = { ...questReward, gacha_weight: requestBody.selector.gachaWeight ?? undefined };
      } else if ((requestBody.selector.rankId ?? undefined) !== undefined) {
        const rankId = requestBody.selector.rankId!;
        try {
          await this.questionnaireResultRankRepository.selectResultRankByRankId(serviceId, rankId);
        } catch {
          throw new ValidationError('Requested rank id is not stored');
        }
        questReward = { ...questReward, rank_id: rankId };
      }
    }

    const createdReward = await this.rewardRepository.insertReward(
      serviceId,
      reward,
      questReward,
      certificateReward,
      contentReward,
      couponReward,
    );

    const rewardTranslationsData = requestBody.rewardTranslations.map((t) => {
      return {
        reward_id: createdReward.reward_id,
        service_id: serviceId,
        language: t.language,
        reward_title: t.title,
        reward_description: t.description,
      };
    });

    const createdRewardTranslation = await this.rewardRepository.insertRewardTranslations(
      serviceId,
      rewardTranslationsData,
    );

    const response: CreateQuestRewardResponse = {
      rewardId: createdReward.reward_id,
      serviceId: createdReward.service_id,
      nftContractId: nftContractId,
      coverImageUrl: createdReward.reward_cover_image_url,
      thumbnailImageUrl: createdReward.reward_thumbnail_image_url,
      rewardTranslations: createdRewardTranslation.map((t) => {
        return {
          language: t.language,
          title: t.reward_title,
          description: t.reward_description,
        };
      }),
      acquirementType: createdReward.reward_acquirement_type,
      rewardType: createdReward.reward_type,
      questRewardPriorityType: requestBody.questRewardPriorityType,
    };

    return response;
  }

  async createReward(serviceId: string, requestBody: CreateRewardRequest): Promise<CreateRewardResponse> {
    const service = await this.serviceInfoRepository.getServiceById(serviceId);
    if (!service) {
      throw new ValidationError('Invalid Service ID');
    }

    const nftContractType = await this.nftContractTypesRepository.selectNftContractTypeByNftType(
      requestBody.rewardType as unknown as NftType,
    );

    if (!nftContractType || (nftContractType.nft_type as unknown as RewardType) !== requestBody.rewardType) {
      throw new ValidationError('Invalid RewardType');
    }

    const insertNft: NftRegister = {
      nftContractTypeId: nftContractType.nft_contract_type_id,
      nftName: requestBody.nftName,
      nftSymbol: requestBody.nftSymbol,
      nftCollectionName: requestBody.nftCollectionName,
      metadata: requestBody.nftMetadata,
      deliveryImageUrl: requestBody.deliveryImageUrl,
    };
    const nftRegisterResponse = await this.nftRegisterService.register(serviceId, insertNft);
    const nftContractId = nftRegisterResponse.nftContractId;

    const rewardId = uuidv4();
    let certificateReward: InsertableCertificateRewardRow | undefined;
    let contentReward: InsertableDigitalContentRewardRow | undefined;
    let couponReward: InsertableCouponRewardRow | undefined;

    switch (requestBody.rewardType) {
      case RewardType.CERTIFICATE: {
        certificateReward = {
          reward_id: rewardId,
          service_id: serviceId,
          nft_contract_id: nftContractId,
          certificate_type: requestBody.certificateReward!.certificateType,
          status_certificate_rank: requestBody.certificateReward?.statusCertificateRank ?? undefined,
        };
        contentReward = undefined;
        couponReward = undefined;
        break;
      }
      case RewardType.CONTENT: {
        certificateReward = undefined;
        contentReward = {
          reward_id: rewardId,
          service_id: serviceId,
          nft_contract_id: nftContractId,
        };
        couponReward = undefined;
        break;
      }
      case RewardType.COUPON: {
        certificateReward = undefined;
        contentReward = undefined;
        couponReward = {
          reward_id: rewardId,
          service_id: serviceId,
          nft_contract_id: nftContractId,
          token_id: 0,
        };
        break;
      }
    }

    const reward: InsertableRewardRow = {
      reward_id: rewardId,
      service_id: serviceId,
      reward_cover_image_url: requestBody.coverImageUrl,
      reward_thumbnail_image_url: requestBody.thumbnailImageUrl,
      reward_type: requestBody.rewardType,
      order_index: requestBody.orderIndex,
    };

    const createdReward = await this.rewardRepository.insertRewardOnly(
      serviceId,
      reward,
      certificateReward,
      contentReward,
      couponReward,
    );

    const rewardTranslationsData = requestBody.rewardTranslations.map((t) => {
      return {
        reward_id: createdReward.reward_id,
        service_id: serviceId,
        language: t.language,
        reward_title: t.title,
        reward_description: t.description,
      };
    });

    const createdRewardTranslation = await this.rewardRepository.insertRewardTranslations(
      serviceId,
      rewardTranslationsData,
    );

    const response: CreateRewardResponse = {
      rewardId: createdReward.reward_id,
      serviceId: createdReward.service_id,
      nftContractId: nftContractId,
      coverImageUrl: createdReward.reward_cover_image_url,
      thumbnailImageUrl: createdReward.reward_thumbnail_image_url,
      rewardTranslations: createdRewardTranslation.map((t) => {
        return {
          language: t.language,
          title: t.reward_title,
          description: t.reward_description,
        };
      }),
      acquirementType: RewardAcquirementType.DISTRIBUTION,
      rewardType: createdReward.reward_type,
    };

    return response;
  }

  private determineQuestStatus(clearedActionIds: string[], totalActionCount: number): QuestStatus {
    if (clearedActionIds.length === 0) {
      return QuestStatus.NOT_STARTED;
    }
    return clearedActionIds.length < totalActionCount ? QuestStatus.IN_PROGRESS : QuestStatus.COMPLETED;
  }
}
