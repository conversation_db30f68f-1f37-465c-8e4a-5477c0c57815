import { GcpKmsSigner } from '@cuonghx.gu-tech/ethers-gcp-kms-signer';
import { ContractTransactionReceipt, ethers, TransactionRequest, TransactionResponse } from 'ethers';
import { Transaction as Trx } from 'kysely';
import { inject, injectable } from 'tsyringe';
import { v4 as uuidv4 } from 'uuid';
import { TransactionComponent } from '../components/transactionComponent';
import { Database } from '../db/database';
import { TransactionQueueStatus } from '../enum/transactionQueueStatus';
import { TransactionStatus } from '../enum/transactionStatus';
import { InternalServerError } from '../errors/internalServerError';
import { ValidationError } from '../errors/validationError';
import { AttemptTransactionsRepository } from '../repositories/attemptTransactionsRepository';
import { NftContractsRepository } from '../repositories/nftContractsRepository';
import { ServiceInfoRepository } from '../repositories/serviceInfoRepository';
import { TransactionQueuesRepository } from '../repositories/transactionQueuesRepository';
import { TransactionsRepository } from '../repositories/transactionsRepository';
import { logger } from '../utils/middleware/loggerMiddleware';

interface CallContractFunctionParams {
  tenantId: string;
  contractAddress: string;
  contractABI: any;
  functionName: string;
  args?: any[];
  transaction?: TransactionRequest;
  addressForGasEstimation?: string;
}

interface DeployContractParams {
  tenantId: string;
  contractBytecode: string;
  contractABI: any;
  constructorArgs?: any[];
  transaction?: TransactionRequest;
}

export interface ExtendedContractTransactionReceipt extends ContractTransactionReceipt {
  implementAddress?: string;
  nonce?: number;
  data?: string;
}

export interface Transaction {
  from: string;
  to: string;
  gasPrice: bigint | null;
  data: string;
  nonce: number;
  retries: number;
}

interface RetryTransactionResponse {
  transactionId: string;
  toAddress: string;
  fromAddress: string;
  txHash: string;
  nonce: number;
}

@injectable()
export class TransactionService {
  constructor(
    @inject('TransactionComponent') private transactionComponent: TransactionComponent,
    @inject('AttemptTransactionsRepository') private attemptTransactionsRepository: AttemptTransactionsRepository,
    @inject('TransactionsRepository') private transactionsRepository: TransactionsRepository,
    @inject('TransactionQueuesRepository') private transactionQueuesRepository: TransactionQueuesRepository,
    @inject('ServiceInfoRepository') private serviceInfoRepository: ServiceInfoRepository,
    @inject('NftContractsRepository') private nftContractsRepository: NftContractsRepository,
  ) {}

  async prepareTransaction(
    contractAddress: string,
    mintData: string,
    vaultWalletAddress: string,
  ): Promise<Transaction> {
    const transactionInfo = await this.transactionComponent.getTransactionInfo(vaultWalletAddress);

    return {
      from: vaultWalletAddress,
      to: contractAddress,
      gasPrice: transactionInfo.gasPrice,
      data: mintData,
      nonce: transactionInfo.info.nonce,
      retries: 0,
    };
  }

  async callContractFunction({
    tenantId,
    contractAddress,
    contractABI,
    functionName,
    args = [],
  }: CallContractFunctionParams): Promise<TransactionResponse> {
    try {
      const txResponse = await this.transactionComponent.sendTransaction(
        tenantId,
        contractAddress,
        contractABI,
        functionName,
        args,
      );
      return txResponse;
    } catch (error) {
      throw new InternalServerError(
        `Error calling contract function ${functionName} of contract ${contractAddress}: ${error}`,
      );
    }
  }

  async callContractViewFunction({
    tenantId,
    contractAddress,
    contractABI,
    functionName,
    args = [],
  }: CallContractFunctionParams): Promise<unknown> {
    try {
      const signer = await this.transactionComponent.getSigner(tenantId);
      const walletAddress = await signer.getAddress();
      const contract = new ethers.Contract(contractAddress, contractABI, signer);

      const transactionInfo = (await this.transactionComponent.getTransactionInfo(walletAddress)).info;

      contract.interface.encodeFunctionData(functionName, args);

      // const gasEstimate = await this.transactionComponent.estimateFunctionCallGas(addressForGasEstimation || contractAddress, callData);
      const gasEstimate = await contract[functionName].estimateGas(...args);

      const txResponse = await contract[functionName](...args, {
        ...transactionInfo,
        gasLimit: gasEstimate,
      });

      if (txResponse === undefined || txResponse === null) {
        throw new InternalServerError(
          `Failed to call contract function ${functionName} of contract ${contractAddress}`,
        );
      }
      return txResponse;
    } catch (error) {
      throw new InternalServerError(
        `Error calling contract function ${functionName} of contract ${contractAddress}: ${error}`,
      );
    }
  }

  async createTransactionRequest(
    tenantId: string,
    contractAddress: string | null,
    callData: string,
    nonce: number,
  ): Promise<{
    transactionRequest: TransactionRequest | undefined;
    signer: GcpKmsSigner;
    fallbackSigner: GcpKmsSigner;
  }> {
    const txRequest = await this.transactionComponent.createTransactionRequest(
      tenantId,
      contractAddress,
      callData,
      nonce,
    );
    return txRequest;
  }

  async signTransaction(
    transactionRequest: TransactionRequest,
    signer: GcpKmsSigner,
    fallbackSigner: GcpKmsSigner,
  ): Promise<string> {
    const signedTransaction = await this.transactionComponent.signTransaction(
      transactionRequest,
      signer,
      fallbackSigner,
    );
    return signedTransaction;
  }

  async ethCallFromTransactionRequest(
    transactionRequest: TransactionRequest,
    signer: GcpKmsSigner,
    fallbackSigner: GcpKmsSigner,
  ): Promise<string> {
    const result = await this.transactionComponent.ethCallFromTransactionRequest(
      transactionRequest,
      signer,
      fallbackSigner,
    );
    return result;
  }

  async sendTransactionFromSignedTransaction(signedTransaction: string): Promise<TransactionResponse | null> {
    const txResponse = await this.transactionComponent.sendTransactionFromSignedTransaction(signedTransaction);
    return txResponse;
  }

  async sendRawTransaction(
    tenantId: string,
    contractAddress: string | null,
    callData: string,
    nonce: number,
  ): Promise<TransactionResponse | null> {
    const txResponse = await this.transactionComponent.sendRawTransaction(tenantId, contractAddress, callData, nonce);
    return txResponse;
  }

  async calculateTxHash(signedTransaction: string): Promise<string> {
    const txHash = await this.transactionComponent.calculateTxHash(signedTransaction);
    return txHash;
  }

  async retryBulkTransactions(): Promise<RetryTransactionResponse[] | undefined> {
    const results: RetryTransactionResponse[] = [];
    const transactions = await this.transactionsRepository.selectLongTimePendingTransactions();
    if (!transactions.length) {
      return results;
    }

    for (const transaction of transactions) {
      const {
        transaction_id: transactionId,
        service_id: serviceId,
        encoded_data: recordedEncodeData,
        nonce: recordedNonce,
      } = transaction;

      const maxRetries = Number(process.env.RETRY_MAX_COUNT_BEFORE_ABORT ?? 3);

      const attemptTransactions =
        await this.attemptTransactionsRepository.selectAttemptTransactionsByTransactionId(transactionId);
      if (!attemptTransactions.length) {
        throw new InternalServerError(`Attempt Transaction is empty`);
      }
      const queueIds = attemptTransactions.map((a) => a.queue_id);
      const retryCounts = await this.attemptTransactionsRepository.countAttemptTransactionsByQueueIds(queueIds);
      const abortedQueueIds = queueIds.filter((queueId) => {
        const retryCount = retryCounts.find((_) => _.queue_id == queueId)?.count ?? 0;
        return retryCount > maxRetries;
      });
      if (abortedQueueIds.length > 0) {
        logger.warn({
          message: `Transaction [${transactionId}] exceeded retry limit (${maxRetries}). Setting status to ABORTED.`,
          abortedQueueIds,
        });
        await this.transactionQueuesRepository.updateQueueStatus(abortedQueueIds, TransactionQueueStatus.ABORTED);
      }
      await this.transactionQueuesRepository.updateQueueStatus(
        queueIds.filter((queueId) => !abortedQueueIds.includes(queueId)),
        TransactionQueueStatus.RETRY,
      );
      // update status.
      await this.transactionsRepository.updateTransactionStatus(transactionId, TransactionStatus.RETRY);

      const serviceInfo = await this.serviceInfoRepository.getServiceById(serviceId);
      if (!serviceInfo) throw new ValidationError('Service not found');
      const tenantId = serviceInfo.tenant_id;
      const modularContractId = serviceInfo.modular_contract_id;
      const contract = await this.nftContractsRepository.selectNftContractById(modularContractId);
      if (!contract) throw new ValidationError(`NFT contract with ID ${modularContractId} not found.`);
      if (!contract.nft_contract_address)
        throw new ValidationError(`NFT contract nft_contract_address ${modularContractId} not found.`);

      // calc hash.
      const { transactionRequest, signer, fallbackSigner } = await this.createTransactionRequest(
        tenantId,
        contract.nft_contract_address,
        recordedEncodeData,
        recordedNonce,
      );
      if (!transactionRequest) {
        throw new InternalServerError('transaction request is null.');
      }
      const signedTransaction = await this.signTransaction(transactionRequest, signer, fallbackSigner);
      const txHash = await this.calculateTxHash(signedTransaction);
      if (!txHash) {
        throw new InternalServerError('calculate transaction hash is null.');
      }
      // insert queue.
      const { transactionId: newTransactionId } = await this.createTransactionDatabases(
        serviceId,
        recordedEncodeData,
        recordedNonce,
        txHash,
        queueIds,
      );
      logger.info({
        transaction: 'retryBulkTransactions',
        txHash,
        tenantId,
        contractAddress: contract.nft_contract_address,
        recordedNonce,
        recordedEncodeData,
      });
      // send tx.
      const txResponse = await this.sendTransactionFromSignedTransaction(signedTransaction);
      if (!txResponse) {
        throw new InternalServerError('sendTransactionFromSignedTransaction result is null.');
      }

      if (txResponse.hash !== txHash) {
        logger.error({ message: 'calc hash is wrong.', txHash, txResponseHash: txResponse.hash });

        this.transactionsRepository.updateTransactionHash(newTransactionId, txResponse.hash);
      }

      const receipt = await txResponse.wait();
      if (receipt && receipt.status === 0) {
        // update status to failure if transaction reverted
        await this.transactionQueuesRepository.updateQueueStatus(
          queueIds.filter((queueId) => !abortedQueueIds.includes(queueId)),
          TransactionQueueStatus.FAILED,
        );
        await this.transactionsRepository.updateTransactionStatus(transactionId, TransactionStatus.FAILED);
        throw new InternalServerError(`Transaction failed: receipt status is 0`);
      }

      results.push({
        transactionId: newTransactionId,
        toAddress: '',
        fromAddress: '',
        txHash: txHash,
        nonce: recordedNonce,
      });
    }

    return results;
  }

  async createTransactionDatabases(
    serviceId: string,
    encodedData: string,
    nonce: number,
    txHash: string,
    queueIds: string[],
    trx?: Trx<Database>,
  ): Promise<{
    transactionId: string;
    attemptTransactionIds: string[];
  }> {
    const transactionId = uuidv4();
    const date = new Date();
    await this.transactionsRepository.insertTransaction(
      {
        transaction_id: transactionId,
        service_id: serviceId,
        encoded_data: encodedData,
        nonce: nonce,
        tx_hash: txHash,
        status: TransactionStatus.EXECUTED,
        created_date: date,
      },
      trx,
    );

    const attemptTransactions = queueIds.map((queueId) => {
      return {
        attempt_id: uuidv4(),
        queue_id: queueId,
        transaction_id: transactionId,
        created_date: date,
      };
    });
    await this.attemptTransactionsRepository.insertAttemptTransactions(attemptTransactions, trx);
    return {
      transactionId: transactionId,
      attemptTransactionIds: attemptTransactions.map((tx) => tx.attempt_id),
    };
  }

  async prepareDeployContract({
    tenantId,
    contractBytecode,
    contractABI,
    constructorArgs = [],
  }: DeployContractParams): Promise<string> {
    try {
      const signer = await this.transactionComponent.getSigner(tenantId);
      const factory = new ethers.ContractFactory(contractABI, contractBytecode, signer);
      const deployTransaction = await factory.getDeployTransaction(...constructorArgs);
      return deployTransaction?.data;
    } catch (error) {
      throw new InternalServerError(`Error generating call data for contract deployment: ${error}`);
    }
  }

  async prepareDeployUpgradeableContract({
    tenantId,
    implementationContractAddress,
    implementationByteCode,
    implementationABI,
    proxyByteCode,
    proxyABI,
    initializeArgs = [],
  }: {
    tenantId: string;
    implementationContractAddress: string;
    implementationByteCode: string;
    proxyByteCode: string;
    implementationABI: any;
    proxyABI: any;
    initializeArgs: any[];
  }): Promise<string> {
    try {
      const signer = await this.transactionComponent.getSigner(tenantId);
      const implementationContract = new ethers.ContractFactory(implementationABI, implementationByteCode, signer);
      const initializeData = implementationContract.interface.encodeFunctionData('initialize', initializeArgs);
      const callData = await this.prepareDeployContract({
        tenantId: tenantId,
        contractBytecode: proxyByteCode,
        contractABI: proxyABI,
        constructorArgs: [implementationContractAddress, initializeData],
      });

      return callData;
    } catch (error) {
      throw new InternalServerError(`Error generating call data for upgradeable contract deployment: ${error}`);
    }
  }

  async isTokenBoundAccountDeployed(address: string): Promise<boolean> {
    return await this.transactionComponent.isContractDeployed(address);
  }
}
