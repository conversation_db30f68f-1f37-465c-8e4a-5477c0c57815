import { inject, injectable } from 'tsyringe';
import { GeofenceRepository } from '../repositories/geofenceRepository';
import { ActionRepository } from '../repositories/actionRepository';
import { AccountRepository } from '../repositories/accountRepository';
import { 
  LocationCheckinRequest,
  LocationCheckinResponse,
  CreateGeofenceRequest,
  Geofence,
  LocationCoordinates
} from '../dtos/location/schemas';
import { GeofencesTable } from '../tables/geofencesTable';
import { NotFoundError } from '../errors/notFoundError';
import { ValidationError } from '../errors/validationError';
import { logger } from '../utils/logger';
import { RewardActionType } from '../enum/actionType';

@injectable()
export class LocationService {
  constructor(
    @inject(GeofenceRepository) private geofenceRepository: GeofenceRepository,
    @inject(ActionRepository) private actionRepository: ActionRepository,
    @inject(AccountRepository) private accountRepository: AccountRepository
  ) {}

  // =======================
  // Geofence Management
  // =======================

  async createGeofence(serviceId: string, geofenceData: CreateGeofenceRequest): Promise<Geofence> {
    logger.info({ method: 'createGeofence', serviceId, geofenceData });

    const geofenceEntity = await this.geofenceRepository.createGeofence(serviceId, geofenceData);
    return this.mapGeofenceEntityToDto(geofenceEntity);
  }

  async getGeofenceById(serviceId: string, geofenceId: string): Promise<Geofence> {
    logger.info({ method: 'getGeofenceById', serviceId, geofenceId });

    const geofence = await this.geofenceRepository.selectGeofenceById(serviceId, geofenceId);
    if (!geofence) {
      throw new NotFoundError('Geofence not found');
    }

    return this.mapGeofenceEntityToDto(geofence);
  }

  async getGeofencesByService(serviceId: string): Promise<Geofence[]> {
    logger.info({ method: 'getGeofencesByService', serviceId });

    const geofences = await this.geofenceRepository.getGeofencesByService(serviceId);
    return geofences.map(geofence => this.mapGeofenceEntityToDto(geofence));
  }

  async updateGeofence(serviceId: string, geofenceId: string, updates: Partial<CreateGeofenceRequest>): Promise<Geofence> {
    logger.info({ method: 'updateGeofence', serviceId, geofenceId, updates });

    const geofence = await this.geofenceRepository.updateGeofence(serviceId, geofenceId, updates);
    if (!geofence) {
      throw new NotFoundError('Geofence not found');
    }

    return this.mapGeofenceEntityToDto(geofence);
  }

  async deleteGeofence(serviceId: string, geofenceId: string): Promise<void> {
    logger.info({ method: 'deleteGeofence', serviceId, geofenceId });

    const success = await this.geofenceRepository.deleteGeofence(serviceId, geofenceId);
    if (!success) {
      throw new NotFoundError('Geofence not found');
    }
  }

  // =======================
  // Location Check-in Logic
  // =======================

  async performLocationCheckin(
    serviceId: string,
    accountId: string,
    checkinRequest: LocationCheckinRequest
  ): Promise<LocationCheckinResponse> {
    logger.info({ 
      method: 'performLocationCheckin', 
      serviceId, 
      accountId, 
      actionId: checkinRequest.actionId 
    });

    // 1. Validate account exists
    const account = await this.accountRepository.selectAccountById(accountId, serviceId);
    if (!account) {
      throw new NotFoundError('Account not found');
    }

    // 2. Get action and validate it's a location check-in action
    const action = await this.actionRepository.selectActionById(checkinRequest.actionId);
    if (!action) {
      throw new NotFoundError('Action not found');
    }

    if (action.action_type !== RewardActionType.LOCATION_CHECKIN) {
      throw new ValidationError('Action is not a location check-in action');
    }

    // 3. Check if action is already completed
    const existingCompletion = await this.geofenceRepository.getSuccessfulLocationCheckin(
      accountId, 
      checkinRequest.actionId
    );
    
    if (existingCompletion) {
      return {
        success: false,
        actionId: checkinRequest.actionId,
        distanceFromTarget: existingCompletion.distance_from_target,
        withinGeofence: true,
        message: 'Action already completed',
        completedAt: existingCompletion.attempted_at.toISOString()
      };
    }

    // 4. Get location action data with geofence
    const locationAction = await this.geofenceRepository.getLocationCheckinActionWithGeofence(
      checkinRequest.actionId
    );
    
    if (!locationAction) {
      throw new NotFoundError('Location action configuration not found');
    }

    // 5. Check if location is within geofence
    const { withinGeofence, distance } = await this.geofenceRepository.isLocationWithinGeofence(
      checkinRequest.location,
      locationAction
    );

    // 6. Record the attempt
    await this.geofenceRepository.createLocationCheckinAttempt(
      accountId,
      checkinRequest.actionId,
      checkinRequest.location,
      withinGeofence,
      distance
    );

    // 7. If successful, complete the action
    let completedAt: string | undefined;
    if (withinGeofence) {
      await this.actionRepository.completeAction(accountId, checkinRequest.actionId);
      completedAt = new Date().toISOString();
      
      logger.info({ 
        message: 'Location check-in successful', 
        accountId, 
        actionId: checkinRequest.actionId,
        distance 
      });
    }

    return {
      success: withinGeofence,
      actionId: checkinRequest.actionId,
      distanceFromTarget: distance,
      withinGeofence,
      geofence: this.mapGeofenceEntityToDto(locationAction),
      message: withinGeofence 
        ? `Successfully checked in at ${locationAction.name}` 
        : `You are ${Math.round(distance - locationAction.radius_meters)}m away from ${locationAction.name}`,
      completedAt
    };
  }

  async getLocationCheckinAttempts(accountId: string, actionId: string) {
    logger.info({ method: 'getLocationCheckinAttempts', accountId, actionId });

    return await this.geofenceRepository.getLocationCheckinAttempts(accountId, actionId);
  }

  async checkLocationInGeofence(
    serviceId: string,
    geofenceId: string,
    location: LocationCoordinates
  ): Promise<{ withinGeofence: boolean; distance: number; geofence: Geofence }> {
    logger.info({ method: 'checkLocationInGeofence', serviceId, geofenceId, location });

    const geofence = await this.geofenceRepository.selectGeofenceById(serviceId, geofenceId);
    if (!geofence) {
      throw new NotFoundError('Geofence not found');
    }

    const { withinGeofence, distance } = await this.geofenceRepository.isLocationWithinGeofence(
      location,
      geofence
    );

    return {
      withinGeofence,
      distance,
      geofence: this.mapGeofenceEntityToDto(geofence)
    };
  }

  // =======================
  // Helper Methods
  // =======================

  private mapGeofenceEntityToDto(entity: GeofencesTable): Geofence {
    return {
      geofenceId: entity.geofence_id,
      serviceId: entity.service_id,
      name: entity.name,
      description: entity.description || undefined,
      latitude: entity.latitude,
      longitude: entity.longitude,
      radiusMeters: entity.radius_meters,
      isActive: entity.is_active,
      createdAt: entity.created_at.toISOString(),
      updatedAt: entity.updated_at.toISOString()
    };
  }
}
