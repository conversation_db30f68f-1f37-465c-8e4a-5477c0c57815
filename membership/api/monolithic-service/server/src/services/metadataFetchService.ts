import axios, { AxiosResponse } from 'axios';
import { ethers } from 'ethers';
import { inject, injectable } from 'tsyringe';
import { TransactionComponent } from '../components/transactionComponent';
import { NftType } from '../enum/nftType';
import { InternalServerError } from '../errors/internalServerError';
import { logger } from '../utils/middleware/loggerMiddleware';
import { retryExecution } from '../utils/retry';

export interface FailedTokenIdsWithMsg {
  tokenId: string;
  errorMsg?: string;
}

export interface AlchemySingleNFTMetadataResponse {
  contract: {
    address: string;
    name: string;
    symbol: string;
    totalSupply: string;
    tokenType: string;
    contractDeployer: string;
    deployedBlockNumber: number;
    openSeaMetadata: {
      floorPrice: number;
      collectionName: string;
      collectionSlug: string;
      safelistRequestStatus: string;
      imageUrl: string;
      description: string;
      externalUrl: string | null;
      twitterUsername: string;
      discordUrl: string;
      bannerImageUrl: string;
      lastIngestedAt: string;
    };
    isSpam: any;
    spamClassifications: any[];
  };
  tokenId: string;
  tokenType: string;
  name: string;
  description: string | null;
  tokenUri: string;
  image: {
    cachedUrl: string;
    thumbnailUrl: string;
    pngUrl: string;
    contentType: string;
    size: number;
    originalUrl: string;
  };
  raw: {
    tokenUri: string;
    metadata: {
      name: string;
      image: string;
      attributes: { value: string; trait_type: string }[];
    };
    error: any;
  };
  collection: {
    name: string;
    slug: string;
    externalUrl: string | null;
    bannerImageUrl: string;
  };
  mint: {
    mintAddress: string | null;
    blockNumber: number | null;
    timestamp: string | null;
    transactionHash: string | null;
  };
  owners: any;
  timeLastUpdated: string;
}
export interface AlchemyGetAssetTransfersRequest {
  id: number;
  jsonrpc: string;
  method: string;
  params: AlchemyGetAssetTransfersRequestParam[];
}

export interface AlchemyGetAssetTransfersRequestParam {
  fromBlock?: string;
  toBlock?: string;
  fromAddress?: string;
  toAddress?: string;
  contractAddresses?: string[];
  category?: string[];
  order?: string;
  withMetadata?: boolean;
  excludeZeroValue?: boolean;
  maxCount?: string;
  pageKey?: string | null;
}

export interface AlchemyNFTsForCollectionResponse {
  nfts: AlchemySingleNFTMetadataResponse[];
  pageKey: string | null;
}

export interface AlchemyNFTsForOwnerResponse {
  owners: AlchemyNftOwner[];
  pageKey: string | null;
}

export interface AlchemyNftOwner {
  ownerAddress: string;
  tokenBalances: AlchemyTokenBalance[];
}

export interface AlchemyTokenBalance {
  tokenId: string;
  balance: string;
}

export interface tokenIdAndOwnerBalances {
  [key: string]: { ownerAddress: string; balance: number }[];
}

export interface AlchemyGetAssetTransfersResponse {
  jsonrpc: string;
  id: number;
  result: AlchemyGetAssetTransfersResult;
}

export interface AlchemyGetAssetTransfersResult {
  transfers: AlchemyTransfer[];
  pageKey: string | null;
}

export interface AlchemyTransfer {
  blockNum: string;
  uniqueId: string;
  hash: string;
  from: string;
  to: string;
  value: number | null;
  erc721TokenId: string | null;
  erc1155Metadata: { tokenId: string; value: string }[] | null;
  tokenId: string | null;
  asset: string | null;
  category: string | null;
  rawContract: AlchemyRawContract | null;
  metadata: AlchemyTransferMetadata | null;
}

export interface AlchemyRawContract {
  value: string | null;
  address: string;
  decimal: string | null;
}

export interface AlchemyTransferMetadata {
  blockTimestamp: string | null;
}

@injectable()
export class MetadataFetchService {
  constructor(@inject('TransactionComponent') private transactionComponent: TransactionComponent) {}
  async fetchTokenUri(
    nftType: NftType,
    contractAddress: string,
    abi: ethers.InterfaceAbi,
    tokenId: string,
  ): Promise<string | null> {
    try {
      const functionName = nftType === NftType.COUPON ? 'uri' : 'tokenURI';
      let tokenUri = (await this.transactionComponent.callViewFunction(contractAddress, abi, functionName, [
        tokenId,
      ])) as string;
      if (nftType === NftType.COUPON) {
        tokenUri = tokenUri.replace('{id}', tokenId.toString());
      }
      return tokenUri;
    } catch (error) {
      logger.error(error, `Failed to fetch tokenUri for contractAddress: ${contractAddress} tokenId: ${tokenId}`);
      return null;
    }
  }

  async fetchMetadataFromTokenURI(tokenURI: string): Promise<object | null> {
    const retryHandler = (error: unknown) => {
      logger.error(error);
      return true;
    };
    try {
      let requestUrl = tokenURI;
      if (tokenURI.startsWith('ipfs://')) {
        requestUrl = tokenURI.replace('ipfs://', 'https://ipfs.io/ipfs/');
      }
      const axiosResponse: AxiosResponse = await retryExecution<AxiosResponse>(
        async () =>
          await axios.get(requestUrl, {
            headers: {
              accept: 'application/json',
              'Content-Type': 'application/json',
            },
          }),
        { retries: 3, retryDelay: 1000, retryHandler: retryHandler },
      );

      if (typeof axiosResponse.data !== 'object') {
        throw new Error('Invalid metadata format');
      }
      return axiosResponse.data;
    } catch (error) {
      logger.error(`Failed to fetch metadata from URI: ${tokenURI}`, error);
      return null;
    }
  }

  async fetchNftTokens(
    apiKey: string,
    chainName: string,
    contractAddress: string,
  ): Promise<{ tokenId: string; tokenUri: string }[]> {
    if (!process.env.ALCHEMY_CHAIN_NAME || !process.env.ALCHEMY_API_KEY) {
      throw new InternalServerError('ALCHEMY_CHAIN_NAME or ALCHEMY_API_KEY is not set');
    }

    const alchemyEndpoint = `https://${chainName}.g.alchemy.com/nft/v3/${apiKey}/getNFTsForCollection`;

    let tokenUris: { tokenId: string; tokenUri: string }[] = [];
    let startToken: string | null = null;

    do {
      try {
        logger.info(
          `Fetching NFT metadata from Alchemy getNFTsForCollection API for contractAddress: ${contractAddress}, startToken: ${startToken}`,
        );
        const axiosResponse = (await this.fetchNftData(alchemyEndpoint, {
          contractAddress: contractAddress,
          withMetadata: true,
          startToken: startToken,
        })) as AxiosResponse<AlchemyNFTsForCollectionResponse>;
        const currentNfts = axiosResponse.data.nfts;
        tokenUris = tokenUris.concat(this.extractTokenUris(currentNfts));
        startToken = this.getNextStartToken(currentNfts, 100);
      } catch (error) {
        logger.error(error, `Failed to fetch NFT metadata from Alchemy API ${alchemyEndpoint}`);
        // これ以上処理を続けられないのでエラーで終了させる
        throw new InternalServerError(
          `Failed to fetch NFT metadata from Alchemy API contractAddress: ${alchemyEndpoint}`,
        );
      }
    } while (startToken !== null);
    logger.info(`fetchNftTokens for ${contractAddress} end.`);
    return tokenUris;
  }

  async fetchNftData(alchemyEndpoint: string, params: any): Promise<AxiosResponse> {
    const retryHandler = (error: unknown) => {
      logger.error(`Error during Alchemy API request: ${error}`);
      return true;
    };

    return await retryExecution<AxiosResponse>(
      async () =>
        await axios.get(alchemyEndpoint, {
          headers: { accept: 'application/json', 'Content-Type': 'application/json' },
          params,
        }),
      { retries: 3, retryDelay: 1000, retryHandler: retryHandler },
    );
  }

  private extractTokenUris(nfts: AlchemySingleNFTMetadataResponse[]): { tokenId: string; tokenUri: string }[] {
    return nfts.map((nft) => {
      const tokenId = nft.tokenId;
      const tokenUri = nft.raw.tokenUri;
      return { tokenId, tokenUri };
    });
  }

  private getNextStartToken(nfts: any[], onePageMaxLength: number): string | null {
    if (nfts.length === onePageMaxLength) {
      const lastTokenId: string = nfts[nfts.length - 1].tokenId;
      return (parseInt(lastTokenId, 10) + 1).toString();
    }
    return null;
  }

  async fetchTokenIdAndOwnerBalances(
    apiKey: string,
    chainName: string,
    contractAddress: string,
  ): Promise<tokenIdAndOwnerBalances | null> {
    let tokenIdAndOwnerBalances: tokenIdAndOwnerBalances | null = null;
    let pageKey: string | undefined = undefined;
    const params = {
      contractAddress: contractAddress,
      withTokenBalances: true,
      pageKey: pageKey,
    };
    const alchemyEndpoint = `https://${chainName}.g.alchemy.com/nft/v3/${apiKey}/getOwnersForContract`;
    do {
      try {
        logger.info(`Fetching ${alchemyEndpoint} pageKey: ${pageKey}`);
        const axiosResponse = (await this.fetchNftData(
          alchemyEndpoint,
          params,
        )) as AxiosResponse<AlchemyNFTsForOwnerResponse>;
        tokenIdAndOwnerBalances = this.aggregateTokenOwners(axiosResponse.data.owners);
        pageKey = axiosResponse.data.pageKey ?? undefined;
      } catch (error) {
        logger.error(error, `Failed to fetch NFT metadata from Alchemy API ${alchemyEndpoint}`);
        // これ以上処理を続けられないのでエラーで終了させる
        throw new InternalServerError(
          `Failed to fetch NFT metadata from Alchemy API contractAddress: ${alchemyEndpoint}`,
        );
      }
    } while (pageKey);
    logger.info(`No pagekey, fetchTokenIdAndOwnerBalances for ${contractAddress} end.`);
    return tokenIdAndOwnerBalances;
  }

  private aggregateTokenOwners(nftOwners: AlchemyNftOwner[]): tokenIdAndOwnerBalances {
    const aggregatedData: { [tokenId: string]: { ownerAddress: string; balance: number }[] } = {};

    for (const nftOwner of nftOwners) {
      for (const tokenBalance of nftOwner.tokenBalances) {
        const tokenId = tokenBalance.tokenId;
        const balance = Number(tokenBalance.balance);
        if (!aggregatedData[tokenId]) {
          aggregatedData[tokenId] = [];
        }
        aggregatedData[tokenId].push({
          ownerAddress: nftOwner.ownerAddress,
          balance,
        });
      }
    }

    return aggregatedData;
  }

  async fetchLatestTokenTransaction({
    apiKey,
    chainName,
    contractAddress,
    toAddress,
    tokenId,
  }: {
    apiKey: string;
    chainName: string;
    contractAddress: string;
    toAddress: string;
    tokenId: string;
  }): Promise<AlchemyTransfer | null> {
    let pageKey: string | null = null;
    // https://docs.alchemy.com/reference/alchemy-getassettransfers
    const alchemyEndpoint = `https://${chainName}.g.alchemy.com/v2/${apiKey}`;
    do {
      try {
        logger.info(
          `Fetching latest transfer transaction: ${alchemyEndpoint}, toAddress: ${toAddress}, contractAddress: ${contractAddress}, pageKey: ${pageKey}`,
        );
        const result = await this.fetchTransferTransaction({
          endpoint: alchemyEndpoint,
          contractAddress,
          toAddress,
          pageKey,
        });
        pageKey = result?.pageKey ?? null;
        const transfers = result?.transfers ?? null;
        if (transfers) {
          const targetTransfer = transfers.find((transfer) => {
            if (transfer.category === 'erc721') {
              return transfer.tokenId && parseInt(transfer.tokenId, 16) === parseInt(tokenId);
            } else if (transfer.category === 'erc1155') {
              if (transfer.erc1155Metadata && transfer.erc1155Metadata.length > 0) {
                return parseInt(transfer.erc1155Metadata[0].tokenId, 16) === parseInt(tokenId, 16);
              }
            }
            return undefined;
          });
          if (targetTransfer) {
            return targetTransfer;
          }
        }
      } catch (error) {
        logger.error(
          error,
          `Failed to fetch latest transfer transaction: ${alchemyEndpoint}, toAddress: ${toAddress}, contractAddress: ${contractAddress}`,
        );
        return null;
      }
    } while (pageKey);
    logger.info(`No pagekey, fetchLatestTokenTransaction for ${contractAddress} end.`);
    return null;
  }

  async fetchTransferTransaction({
    endpoint,
    contractAddress,
    toAddress,
    pageKey,
  }: {
    endpoint: string;
    contractAddress: string;
    toAddress: string;
    pageKey: string | null;
  }): Promise<AlchemyGetAssetTransfersResult | null> {
    const retryHandler = (error: unknown) => {
      logger.error(error);
      return true;
    };
    try {
      const requestBody: AlchemyGetAssetTransfersRequest = {
        id: 1,
        jsonrpc: '2.0',
        method: 'alchemy_getAssetTransfers',
        params: [
          {
            toAddress: toAddress,
            contractAddresses: [contractAddress],
            category: ['erc721', 'erc1155'],
            order: 'desc',
            withMetadata: true,
            excludeZeroValue: true,
            maxCount: '0x3e8',
            pageKey: pageKey ?? undefined,
          },
        ],
      };

      const axiosResponse: AxiosResponse<AlchemyGetAssetTransfersResponse> = await retryExecution<
        AxiosResponse<AlchemyGetAssetTransfersResponse>
      >(
        async () =>
          await axios.post<AlchemyGetAssetTransfersResponse>(endpoint, requestBody, {
            headers: {
              accept: 'application/json',
              'content-type': 'application/json',
            },
          }),
        { retries: 3, retryDelay: 1000, retryHandler: retryHandler },
      );

      if (!axiosResponse.data && typeof axiosResponse.data !== 'object') {
        throw new InternalServerError('Invalid data format from Alchemy API');
      }
      return axiosResponse.data.result;
    } catch (error) {
      logger.error(
        error,
        `Failed to fetch latest transfer transaction: ${endpoint}, toAddress: ${toAddress}, contractAddress: ${contractAddress}`,
      );
      return null;
    }
  }
}
