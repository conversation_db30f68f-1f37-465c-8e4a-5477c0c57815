import { inject, injectable } from 'tsyringe';
import { ContractAccount, CreatedUser, CreateUser, RecoveryShare, User, UserCheck } from '../dtos/users/schemas';
import { UserStatus } from '../enum/userStatus';
import { BaseError } from '../errors/baseError';
import { InternalServerError } from '../errors/internalServerError';
import { NotFoundError } from '../errors/notFoundError';
import { AccountRepository } from '../repositories/accountRepository';
import { UserRepository } from '../repositories/userRepository';
import { UserResponse } from '../responsedto/userResponse';
import { logger } from '../utils/middleware/loggerMiddleware';
import { ShareBackupRepository } from '../repositories/shareBackupRepository';

@injectable()
export class UserService {
  constructor(
    @inject('UserRepository') private userRepository: UserRepository,
    @inject('AccountRepository') private accountRepository: AccountRepository,
    @inject('ShareBackupRepository') private shareBackupRepository: ShareBackupRepository,
  ) {}

  async createUser(userCreateRequest: CreateUser): Promise<CreatedUser> {
    try {
      const newUserEntity = await this.userRepository.insertUser({
        ...userCreateRequest,
        status: UserStatus.ACTIVE,
      });
      logger.debug(newUserEntity);

      const response: CreatedUser = {
        userId: newUserEntity.user_id,
        phone: {
          countryCode: newUserEntity.country_code,
          phoneNumber: newUserEntity.phone_number,
        },
        contractAccountAddress: newUserEntity.contract_account_address,
        account: {
          serviceId: newUserEntity.mnemonic_backup_key,
        },
      };

      return response;
    } catch (error) {
      if (error instanceof BaseError) {
        throw error;
      }
      throw new InternalServerError();
    }
  }

  async getUser(id: string): Promise<UserResponse> {
    const user = await this.userRepository.selectUserById(id);
    if (!user) {
      throw new NotFoundError('User not found');
    }
    return new UserResponse(
      user.user_id,
      user.country_code,
      user.phone_number,
      user.mnemonic_backup_key,
      user.contract_account_address,
      user.status,
    );
  }

  async getSanitizedUserWithAccount(id: string, serviceIdHeader: string): Promise<User> {
    const user = await this.userRepository.selectUserById(id);
    if (!user || user.status == UserStatus.DELETED) {
      throw new NotFoundError('User not found');
    }
    const account = await this.accountRepository.selectAccountByUserId(id, serviceIdHeader);
    const response: User = {
      userId: user.user_id,
      phone: {
        countryCode: user.country_code,
        phoneNumber: user.phone_number,
      },
      account: {
        serviceId: serviceIdHeader,
        accountId: account?.account_id,
      },
      contractAccountAddress: user.contract_account_address,
    };
    return response;
  }

  async deleteUser(id: string): Promise<void> {
    const user = await this.userRepository.selectUserById(id);
    if (!user) {
      throw new NotFoundError('User not found');
    }
    await this.userRepository.updateUserState(user.user_id, UserStatus.DELETED);
  }

  async updateUserPhoneNumber(id: string, phoneCountryCode: string, phone: string): Promise<void> {
    const user = await this.userRepository.selectUserById(id);
    if (!user) {
      throw new NotFoundError('User not found');
    }
    await this.userRepository.updateUserPhoneNumber(id, phoneCountryCode, phone);
  }

  async updateUserContractAccount(id: string, contractAccountAddress: string): Promise<void> {
    const user = await this.userRepository.selectUserById(id);
    if (!user) {
      throw new NotFoundError('User not found');
    }
    await this.userRepository.updateContractAccount(id, contractAccountAddress);
  }

  async getBackupKey(id: string): Promise<ContractAccount> {
    const user = await this.userRepository.selectUserById(id);
    if (!user) {
      throw new NotFoundError('User not found');
    }

    const response: ContractAccount = {
      mnemonicBackupKey: user.mnemonic_backup_key,
      contractAccountAddress: user.contract_account_address,
    };

    return response;
  }

  async checkUser(id: string): Promise<UserCheck> {
    const user = await this.userRepository.checkUser(id);
    let userExist = true;
    if (!user) {
      userExist = false;
    }

    const response: UserCheck = {
      isUserExist: userExist,
    };
    return response;
  }

  async getRecoveryShare(userId: string): Promise<RecoveryShare> {
    const user = await this.userRepository.selectUserById(userId);
    if (!user) {
      throw new NotFoundError('User not found');
    }

    const share = await this.shareBackupRepository.getShareBackupsByUserId(userId);
    return {
      shareIndex: share.share_index,
      share: share.share,
      polynomialID: share.polynomial_id,
    };
  }

  async storeRecoveryShare(userId: string, recoveryShare: RecoveryShare): Promise<void> {
    const user = await this.userRepository.selectUserById(userId);
    if (!user) {
      throw new NotFoundError('User not found');
    }

    await this.shareBackupRepository.addNewShareBackup({
      share_backup_id: crypto.randomUUID(),
      user_id: userId,
      share: recoveryShare.share,
      polynomial_id: recoveryShare.polynomialID,
      share_index: recoveryShare.shareIndex,
      created_at: new Date().toISOString(),
      is_active: true,
    });
  }
}
