import { inject, injectable } from 'tsyringe';
import { v4 as uuidv4 } from 'uuid';
import { PointOpsType } from '../enum/pointOpsType';
import { pointTxDetail } from '../enum/pointTxDetail';
import { pointOpsActor } from '../enum/pointOpsActor';
import { StatusPointTx } from '../dtos/points/schemas';
import { StatusPointTxsRepository } from '../repositories/statusPointTxsRepository';
import { InsertableStatusPointTxRow } from '../tables/statusPointTxs';
import { RedisComponent } from '../components/redisComponent';
import { Database } from '../db/database';
import { Transaction } from 'kysely';

@injectable()
export class StatusPointService {
  constructor(
    @inject('StatusPointTxsRepository')
    private statusPointTxsRepository: StatusPointTxsRepository,
    @inject('RedisComponent')
    private redisComponent: RedisComponent,
  ) {}

  async adjustStatusPoints(
    serviceId: string,
    accountId: string,
    amount: number,
    pointOpsType: PointOpsType,
  ): Promise<StatusPointTx> {
    const statusPointTx: InsertableStatusPointTxRow = {
      status_point_tx_id: uuidv4(),
      service_id: serviceId,
      account_id: accountId,
      amount,
      ops_type: pointOpsType,
      tx_by: pointOpsActor.ADMIN,
      tx_detail: pointTxDetail.ADJUST,
      created_at: new Date(),
    };
    const insertedStatusPointTx = await this.statusPointTxsRepository.insertStatusPointTx(serviceId, statusPointTx);
    await this.redisComponent.incrByWithExpire(`status_points:${accountId}`, amount);
    return {
      ...insertedStatusPointTx,
      created_at: insertedStatusPointTx.created_at.toISOString(),
    };
  }

  async getTotalStatusPoints(serviceId: string, accountId: string, trx?: Transaction<Database>): Promise<number> {
    const redisKey = `status_points:${accountId}`;
    const cachedPoints = await this.redisComponent.get<number>(redisKey);
    if (cachedPoints) {
      return cachedPoints;
    } else {
      const dbPoints = await this.statusPointTxsRepository.getTotalStatusPointsByAccountId(serviceId, accountId, trx);
      await this.redisComponent.set(redisKey, dbPoints);
      return dbPoints;
    }
  }
}
