import { injectable, inject } from 'tsyringe';
import { ServiceInfoRepository } from '../repositories/serviceInfoRepository';
import { RegisterServiceResponse, Service } from '../dtos/services/schemas';
import { NotFoundError } from '../errors/notFoundError';
import { RegisterServiceRequest } from '../dtos/services/schemas';
import { TenantRepository } from '../repositories/tenantRepository';
import { ServicesCustomFieldsRepository } from '../repositories/servicesCustomFieldsRepository';
import { ServiceCustomFields, ServiceCustomFieldsUpdateResponse, ServiceCustomFieldsResponse } from '../dtos/services/schemas';
import { ValidationError } from '../errors/validationError';
import { logger } from '../utils/logger';
import { v4 as uuidv4 } from 'uuid';
import { InsertableServiceRow } from '../tables/servicesTable';
import { createHash } from 'crypto';
import { TokenBoundAccountRegistryAddressRepository } from '../repositories/tokenBoundAccountRegistryAddressRepository';
import { tokenBoundAccountAbi } from '../abis/tbaAbi';
import { TokenBoundAccountImplementationRepository } from '../repositories/tokenBoundAccountImplementationsRepository';
import { LanguageCode, languageCode } from '../enum/languageCode';
import { config } from '../configs/config';
import { TokenBoundAccountRegistryEntity } from '../tables/tokenBoundAccountRegistriesTable';
import { TokenBoundAccountImplementationEntity } from '../tables/tokenBoundAccountImplementationTable';
import { InsertableServiceTranslationRow } from '../tables/translations/serviceTranslationsTable';
import { AccountCustomFieldService } from './accountCustomFieldService';
import { CustomFieldType } from '../enum/customFieldType';

@injectable()
export class ServiceInfoService {
  constructor(
    @inject('ServiceInfoRepository')
    private serviceInfoRepository: ServiceInfoRepository,
    @inject('TenantRepository')
    private tenantRepository: TenantRepository,
    @inject('TokenBoundAccountRegistryAddressRepository')
    private tokenBoundAccountRegistryAddressRepository: TokenBoundAccountRegistryAddressRepository,
    @inject('TokenBoundAccountImplementationRepository')
    private tokenBoundAccountImplementationRepository: TokenBoundAccountImplementationRepository,
    @inject('ServicesCustomFieldsRepository')
      private servicesCustomFieldsRepository: ServicesCustomFieldsRepository
  ) { }

  async getServiceById(serviceId: string, lang: LanguageCode): Promise<Service> {
    const serviceInfo = await this.serviceInfoRepository.getServiceWithTranslationsById(serviceId, lang);

    if (!serviceInfo) {
      throw new NotFoundError('Service not found');
    }

    const response: Service = {
      serviceId: serviceInfo.service_id,
      name: serviceInfo.service_name,
      policy: serviceInfo.service_policy,
      logoImageUrl: serviceInfo.service_logo_image_url,
      marketCoverImageUrl: serviceInfo.market_cover_image_url!,
      themePrimaryColorLowest: serviceInfo.theme_primary_color_lowest,
      themePrimaryColorLower: serviceInfo.theme_primary_color_lower,
      themePrimaryColorHigher: serviceInfo.theme_primary_color_higher,
      themePrimaryColorHighest: serviceInfo.theme_primary_color_highest,
      isMarketEnabled: serviceInfo.is_market_enabled,
    };
    return response;
  }

  async registerService(tenantId: string, request: RegisterServiceRequest): Promise<RegisterServiceResponse> {
    const tenant = await this.tenantRepository.selectTenantById(tenantId, languageCode.JA);
    if (!tenant) throw new NotFoundError('Tenant not found');

    const serviceId = uuidv4();
    const registryId = uuidv4();
    const tbaId = uuidv4();

    const serviceData: InsertableServiceRow = {
      service_id: serviceId,
      tenant_id: tenantId,
      service_url: request.serviceUrl,
      service_logo_image_url: request.serviceLogoImageUrl,
      market_cover_image_url: request.marketCoverImageUrl,
      theme_primary_color_lowest: request.themePrimaryColorLowest,
      theme_primary_color_lower: request.themePrimaryColorLower,
      theme_primary_color_higher: request.themePrimaryColorHigher,
      theme_primary_color_highest: request.themePrimaryColorHighest,
      membership_nft_contract_id: undefined,
      is_market_enabled: request.isMarketEnabled,
      stripe_account_id: request.stripeAccountId,
      line_channel_id: request.lineChannelId,
      commission_rate: 0.064,
      modular_contract_id: request.modularContractId,
    };
    const service = await this.serviceInfoRepository.insertService(serviceData);

    const serviceTranslations: InsertableServiceTranslationRow[] = request.serviceTranslations.map((t) => {
      return {
        service_id: serviceId,
        language: t.language,
        service_name: t.serviceName,
        service_policy: t.servicePolicy,
        service_pane: t.servicePane,
      };
    });
    const createdServiceTranslations = await this.serviceInfoRepository.insertServiceTranslations(
      serviceId,
      serviceTranslations,
    );

    const registry: TokenBoundAccountRegistryEntity = {
      service_id: serviceId,
      token_bound_account_registry_id: registryId,
      token_bound_account_registry_address: tokenBoundAccountAbi.registory.address,
      salt: `0x${createHash('sha256').update(serviceId).digest('hex')}`,
      chain_id: config.chainId,
      abi: JSON.parse(tokenBoundAccountAbi.registory.abiJson) as object,
    };
    await this.tokenBoundAccountRegistryAddressRepository.insertAbi(serviceId, registry);

    const implementation: TokenBoundAccountImplementationEntity = {
      service_id: serviceId,
      token_bound_account_implementation_id: tbaId,
      token_bound_account_registry_id: registryId,
      token_bound_account_implementation_address: tokenBoundAccountAbi.implementation.address,
      token_bound_account_implementation_abi: JSON.parse(tokenBoundAccountAbi.implementation.abiJson) as object,
    };
    await this.tokenBoundAccountImplementationRepository.insertAbi(serviceId, implementation);

    const insertedService: RegisterServiceResponse = {
      serviceId: service.service_id,
      serviceTranslations: createdServiceTranslations.map((t) => ({
        language: t.language,
        serviceName: t.service_name,
        servicePolicy: t.service_policy,
        servicePane: t.service_pane,
      })),
      logoImageUrl: service.service_logo_image_url,
      marketCoverImageUrl: service.market_cover_image_url,
      isMarketEnabled: service.is_market_enabled,
      themePrimaryColorLowest: service.theme_primary_color_lowest,
      themePrimaryColorLower: service.theme_primary_color_lower,
      themePrimaryColorHigher: service.theme_primary_color_higher,
      themePrimaryColorHighest: service.theme_primary_color_highest,
    };

    return insertedService;
  }

  async createOrUpdateServiceCustomFields(
    serviceId: string,
    definition: ServiceCustomFields
  ): Promise<ServiceCustomFieldsUpdateResponse> {
    logger.info({ method: 'createOrUpdateServiceCustomFields', serviceId, definition });
    this.validateCustomFields(definition);

    const currentVersion = await this.servicesCustomFieldsRepository.getCurrentVersion(serviceId);
    const nextVersion = currentVersion + 1;

    const result = await this.servicesCustomFieldsRepository.createOrUpdate(
      serviceId,
      definition,
      nextVersion
    );

    return {
      customFieldsDefinitionId: result.id,
      version: result.version
    };
  }

  async getServiceCustomFields(
    serviceId: string,
    lang: LanguageCode
  ): Promise<ServiceCustomFieldsResponse> {
    const definition = await this.servicesCustomFieldsRepository.getLatestCustomFieldsVersion(serviceId, lang);
    const formatted_definition = definition.fields.map(f => ({
      ...f,
      created_at: f.created_at.toISOString()
    }));

    return { fields: formatted_definition } as ServiceCustomFieldsResponse;
  }

  private validateCustomFields(definition: ServiceCustomFields): void {
    if (!definition.fields || !Array.isArray(definition.fields) || definition.fields.length === 0) {
      throw new ValidationError('Fields must be a non-empty array');
    }

    const fieldKeys = new Set<string>();
    for (const field of definition.fields) {
      if (!field.fieldKey) {
        throw new ValidationError('Each field must have a fieldKey');
      }

      if (fieldKeys.has(field.fieldKey)) {
        throw new ValidationError(`Duplicate field key: ${field.fieldKey}`);
      }

      fieldKeys.add(field.fieldKey);

      // Type
      if (!field.type) {
        throw new ValidationError(`Field ${field.fieldKey} must have a type`);
      }

      // Translations
      if (!field.translations || !Array.isArray(field.translations) || field.translations.length === 0) {
        throw new ValidationError(`Field ${field.fieldKey} must have at least one translation`);
      }

      const locales = new Set<string>();
      for (const translation of field.translations) {
        if (!translation.locale) {
          throw new ValidationError(`Each translation for field ${field.fieldKey} must have a locale`);
        }

        if (locales.has(translation.locale)) {
          throw new ValidationError(`Duplicate locale ${translation.locale} for field ${field.fieldKey}`);
        }

        locales.add(translation.locale);

        if (!translation.label) {
          throw new ValidationError(`Translation for locale ${translation.locale} in field ${field.fieldKey} must have a label`);
        }
      }

      if (field.validator) {
        if (!field.validator.pattern) {
          throw new ValidationError(`Validator for field ${field.fieldKey} must have a pattern`);
        }

        if (!field.validator.translations || !Array.isArray(field.validator.translations) || field.validator.translations.length === 0) {
          throw new ValidationError(`Validator for field ${field.fieldKey} must have at least one translation`);
        }
      }

      if (field.type === CustomFieldType.SELECTION || field.type === CustomFieldType.MULTIPLE_SELECTION) {
        if (!field.options || !Array.isArray(field.options) || field.options.length === 0) {
          throw new ValidationError(`SELECTION field ${field.fieldKey} must have at least one option`);
        }

        const optionValues = new Set<string>();
        for (const option of field.options) {
          if (!option.value) {
            throw new ValidationError(`Each option for field ${field.fieldKey} must have a value`);
          }

          if (optionValues.has(option.value)) {
            throw new ValidationError(`Duplicate option value ${option.value} for field ${field.fieldKey}`);
          }

          optionValues.add(option.value);

          if (!option.translations || !Array.isArray(option.translations) || option.translations.length === 0) {
            throw new ValidationError(`Option ${option.value} for field ${field.fieldKey} must have at least one translation`);
          }
        }
      }
    }
  }
}
