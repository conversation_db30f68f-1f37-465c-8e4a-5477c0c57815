import { injectable, inject } from 'tsyringe';
import { ServiceInfoRepository } from '../repositories/serviceInfoRepository';
import { RegisterServiceResponse, Service } from '../dtos/services/schemas';
import { NotFoundError } from '../errors/notFoundError';
import { RegisterServiceRequest } from '../dtos/services/schemas';
import { TenantRepository } from '../repositories/tenantRepository';
import { v4 as uuidv4 } from 'uuid';
import { InsertableServiceRow } from '../tables/servicesTable';
import { createHash } from 'crypto';
import { TokenBoundAccountRegistryAddressRepository } from '../repositories/tokenBoundAccountRegistryAddressRepository';
import { tokenBoundAccountAbi } from '../abis/tbaAbi';
import { TokenBoundAccountImplementationRepository } from '../repositories/tokenBoundAccountImplementationsRepository';
import { LanguageCode, languageCode } from '../enum/languageCode';
import { config } from '../configs/config';
import { TokenBoundAccountRegistryEntity } from '../tables/tokenBoundAccountRegistriesTable';
import { TokenBoundAccountImplementationEntity } from '../tables/tokenBoundAccountImplementationTable';
import { InsertableServiceTranslationRow } from '../tables/translations/serviceTranslationsTable';

@injectable()
export class ServiceInfoService {
  constructor(
    @inject('ServiceInfoRepository')
    private serviceInfoRepository: ServiceInfoRepository,
    @inject('TenantRepository')
    private tenantRepository: TenantRepository,
    @inject('TokenBoundAccountRegistryAddressRepository')
    private tokenBoundAccountRegistryAddressRepository: TokenBoundAccountRegistryAddressRepository,
    @inject('TokenBoundAccountImplementationRepository')
    private tokenBoundAccountImplementationRepository: TokenBoundAccountImplementationRepository,
  ) {}

  async getServiceById(serviceId: string, lang: LanguageCode): Promise<Service> {
    const serviceInfo = await this.serviceInfoRepository.getServiceWithTranslationsById(serviceId, lang);

    if (!serviceInfo) {
      throw new NotFoundError('Service not found');
    }

    const response: Service = {
      serviceId: serviceInfo.service_id,
      name: serviceInfo.service_name,
      policy: serviceInfo.service_policy,
      logoImageUrl: serviceInfo.service_logo_image_url,
      marketCoverImageUrl: serviceInfo.market_cover_image_url!,
      themePrimaryColorLowest: serviceInfo.theme_primary_color_lowest,
      themePrimaryColorLower: serviceInfo.theme_primary_color_lower,
      themePrimaryColorHigher: serviceInfo.theme_primary_color_higher,
      themePrimaryColorHighest: serviceInfo.theme_primary_color_highest,
      isMarketEnabled: serviceInfo.is_market_enabled,
    };
    return response;
  }

  async registerService(tenantId: string, request: RegisterServiceRequest): Promise<RegisterServiceResponse> {
    const tenant = await this.tenantRepository.selectTenantById(tenantId, languageCode.JA);
    if (!tenant) throw new NotFoundError('Tenant not found');

    const serviceId = uuidv4();
    const registryId = uuidv4();
    const tbaId = uuidv4();

    const serviceData: InsertableServiceRow = {
      service_id: serviceId,
      tenant_id: tenantId,
      service_url: request.serviceUrl,
      service_logo_image_url: request.serviceLogoImageUrl,
      market_cover_image_url: request.marketCoverImageUrl,
      theme_primary_color_lowest: request.themePrimaryColorLowest,
      theme_primary_color_lower: request.themePrimaryColorLower,
      theme_primary_color_higher: request.themePrimaryColorHigher,
      theme_primary_color_highest: request.themePrimaryColorHighest,
      membership_nft_contract_id: undefined,
      is_market_enabled: request.isMarketEnabled,
      stripe_account_id: request.stripeAccountId,
      line_channel_id: request.lineChannelId,
      commission_rate: 0.064,
      modular_contract_id: request.modularContractId,
    };
    const service = await this.serviceInfoRepository.insertService(serviceData);

    const serviceTranslations: InsertableServiceTranslationRow[] = request.serviceTranslations.map((t) => {
      return {
        service_id: serviceId,
        language: t.language,
        service_name: t.serviceName,
        service_policy: t.servicePolicy,
        service_pane: t.servicePane,
      };
    });
    const createdServiceTranslations = await this.serviceInfoRepository.insertServiceTranslations(
      serviceId,
      serviceTranslations,
    );

    const registry: TokenBoundAccountRegistryEntity = {
      service_id: serviceId,
      token_bound_account_registry_id: registryId,
      token_bound_account_registry_address: tokenBoundAccountAbi.registory.address,
      salt: `0x${createHash('sha256').update(serviceId).digest('hex')}`,
      chain_id: config.chainId,
      abi: JSON.parse(tokenBoundAccountAbi.registory.abiJson) as object,
    };
    await this.tokenBoundAccountRegistryAddressRepository.insertAbi(serviceId, registry);

    const implementation: TokenBoundAccountImplementationEntity = {
      service_id: serviceId,
      token_bound_account_implementation_id: tbaId,
      token_bound_account_registry_id: registryId,
      token_bound_account_implementation_address: tokenBoundAccountAbi.implementation.address,
      token_bound_account_implementation_abi: JSON.parse(tokenBoundAccountAbi.implementation.abiJson) as object,
    };
    await this.tokenBoundAccountImplementationRepository.insertAbi(serviceId, implementation);

    const insertedService: RegisterServiceResponse = {
      serviceId: service.service_id,
      serviceTranslations: createdServiceTranslations.map((t) => ({
        language: t.language,
        serviceName: t.service_name,
        servicePolicy: t.service_policy,
        servicePane: t.service_pane,
      })),
      logoImageUrl: service.service_logo_image_url,
      marketCoverImageUrl: service.market_cover_image_url,
      isMarketEnabled: service.is_market_enabled,
      themePrimaryColorLowest: service.theme_primary_color_lowest,
      themePrimaryColorLower: service.theme_primary_color_lower,
      themePrimaryColorHigher: service.theme_primary_color_higher,
      themePrimaryColorHighest: service.theme_primary_color_highest,
    };

    return insertedService;
  }
}
