import { createRoute } from '@hono/zod-openapi';
import { commonErrorResponses } from '../dtos/utils/errorResponse';

//Schemas
import { serviceIdHeaderSchema } from '../dtos/param/header/serviceIdHeaderSchema';
//Responses
import { NotificationRequestBodySchema } from '../dtos/notifications/requests';
import { ApiAccessLevelTag, ApiCategoryTag } from '../enum/apiTag';

export const postNotificationRoute = createRoute({
  method: 'post',
  path: '/',
  summary: 'create notification',
  operationId: 'createNotification',
  tags: [ApiCategoryTag.ACCOUNT, ApiAccessLevelTag.ACCOUNT],
  request: {
    headers: serviceIdHeaderSchema,
    body: NotificationRequestBodySchema,
  },
  responses: {
    200: { description: 'successful response' },
    ...commonErrorResponses,
  },
});
