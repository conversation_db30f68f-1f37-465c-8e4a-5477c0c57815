import { createRoute } from '@hono/zod-openapi';
import { serviceIdHeaderSchema } from '../dtos/param/header/serviceIdHeaderSchema';
import { PathParameterUserIdSchema } from '../dtos/users/parameters';
import {
  addContractAccountRequestBody,
  createUserRequestBody,
  updatePhoneNumberRequestBody,
} from '../dtos/users/requests';
import {
  backupKeyResponseBody,
  contractAccountUpdateResponseBody,
  recoveryShareResponseBody,
  rrecoveryShareStoreResponseBody,
  userCheckResponseBody,
  userCreateResponseBody,
  userDeleteResponseBody,
  userResponseBody,
  userUpdatePhonenumberResponseBody,
} from '../dtos/users/responses';
import { commonErrorResponses } from '../dtos/utils/errorResponse';
import { ApiAccessLevelTag, ApiCategoryTag } from '../enum/apiTag';

// POST /users
export const createUserRoute = createRoute({
  method: 'post',
  path: '/',
  operationId: 'createUser',
  summary: 'Create user',
  tags: [ApiCategoryTag.USER, ApiAccessLevelTag.GUEST],
  request: {
    body: createUserRequestBody,
  },
  responses: {
    201: userCreateResponseBody,
    ...commonErrorResponses,
  },
});

// GET /users/{userId}/check
export const checkUserRoute = createRoute({
  method: 'get',
  path: '/{userId}/check',
  operationId: 'checkUser',
  summary: 'Check user existence',
  tags: [ApiCategoryTag.USER, ApiAccessLevelTag.USER],
  request: {
    params: PathParameterUserIdSchema,
  },
  responses: {
    200: userCheckResponseBody,
    ...commonErrorResponses,
  },
});

// GET /users/:userId
export const getUserByIdRoute = createRoute({
  method: 'get',
  path: '/{userId}',
  operationId: 'getUserById',
  summary: 'Get user by user id',
  tags: [ApiCategoryTag.USER, ApiAccessLevelTag.USER],
  request: {
    headers: serviceIdHeaderSchema,
    params: PathParameterUserIdSchema,
  },
  responses: {
    200: userResponseBody,
    ...commonErrorResponses,
  },
});

// PUT /users/{userId}/contract-account
export const updateContractAccountRoute = createRoute({
  method: 'put',
  path: '/{userId}/contract-account',
  operationId: 'updateContractAccount',
  summary: 'Update user contract account',
  tags: [ApiCategoryTag.USER, ApiAccessLevelTag.USER],
  request: {
    params: PathParameterUserIdSchema,
    body: addContractAccountRequestBody,
  },
  responses: {
    200: contractAccountUpdateResponseBody,
    ...commonErrorResponses,
  },
});

// GET /users/:userId/backup-key
export const fetchUserBackupKeyRoute = createRoute({
  method: 'get',
  path: '/{userId}/backup-key',
  operationId: 'fetchUserBackupKey',
  summary: 'Obtain the backup-key',
  tags: [ApiCategoryTag.USER, ApiAccessLevelTag.USER],
  request: {
    params: PathParameterUserIdSchema,
  },
  responses: {
    200: backupKeyResponseBody,
    ...commonErrorResponses,
  },
});

// GET /users/:userId/recovery-share
export const fetchUserRecoveryShareRoute = createRoute({
  method: 'get',
  path: '/{userId}/recovery-share',
  operationId: 'fetchUserRecoveryShare',
  summary: 'Obtain the recovery-share',
  tags: [ApiCategoryTag.USER, ApiAccessLevelTag.IFRAME],
  request: {
    params: PathParameterUserIdSchema,
  },
  responses: {
    200: recoveryShareResponseBody,
    ...commonErrorResponses,
  },
});

// POST /users/:userId/recovery-share
export const storeUserRecoveryShareRoute = createRoute({
  method: 'post',
  path: '/{userId}/recovery-share',
  operationId: 'storeUserRecoveryShare',
  summary: 'Create the recovery-share',
  tags: [ApiCategoryTag.USER, ApiAccessLevelTag.IFRAME],
  request: {
    params: PathParameterUserIdSchema,
  },
  responses: {
    200: rrecoveryShareStoreResponseBody,
    ...commonErrorResponses,
  },
});

// DELETE /users/:userId
export const deleteUserRoute = createRoute({
  method: 'delete',
  path: '/{userId}',
  operationId: 'deleteUser',
  summary: 'Delete user by user id',
  tags: [ApiCategoryTag.USER, ApiAccessLevelTag.USER],
  request: {
    params: PathParameterUserIdSchema,
  },
  responses: {
    200: userDeleteResponseBody,
    ...commonErrorResponses,
  },
});

// PUT /users/:userId/phone-number
export const updateUserPhoneNumberRoute = createRoute({
  method: 'put',
  path: '/{userId}/phone-number',
  operationId: 'updateUserPhoneNumber',
  summary: 'Update user phone number',
  tags: [ApiCategoryTag.USER, ApiAccessLevelTag.USER],
  request: {
    params: PathParameterUserIdSchema,
    body: updatePhoneNumberRequestBody,
  },
  responses: {
    200: userUpdatePhonenumberResponseBody,
    ...commonErrorResponses,
  },
});
