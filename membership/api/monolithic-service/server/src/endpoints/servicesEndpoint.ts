import { createRoute } from '@hono/zod-openapi';
import { serviceIdHeaderSchema } from '../dtos/param/header/serviceIdHeaderSchema';
import { tenantIdHeaderSchema } from '../dtos/param/header/tenantIdHeaderScheme';
import { PathParameterQuestionnaireIdSchema } from '../dtos/questionnaire/parameters';
import {
  questionnaireActionCreateRequestBody,
  questionnaireActionUpdateRequestBody,
} from '../dtos/questionnaire/requests';
import { questionnaireDetailResponseBody, questionnaireReadGetResponseBody } from '../dtos/questionnaire/responses';
import { SerialCodeCreateRequestBodySchema, SerialCodeImportRequestBodySchema } from '../dtos/serial_codes/requests';
import { SerialCodeProjectsResponseBodySchema, SerialCodeResponseBodySchema } from '../dtos/serial_codes/responses';
import {
  PathParameterActionIdSchema,
  PathParameterQuestIdSchema,
  PathParameterRewardIdSchema,
} from '../dtos/services/parameters';
import {
  createRewardRequestBody,
  questActionCreateRequestBody,
  questCreateRequestBody,
  questRewardCreateRequestBody,
  registerServiceRequestBody,
  serviceCustomFieldsRequestBody,
} from '../dtos/services/requests';
import {
  actionDetailResponseBody,
  createQuestActionResponseBody,
  createQuestRewardResponseBody,
  createRewardResponseBody,
  getServiceResponseBody,
  questCreateResponseBody,
  questDetailResponseBody,
  questListResponseBody,
  registerServiceResponseBody,
  rewardDetailResponseBody,
  statusQuestResponseBody,
  serviceCustomFieldsUpdateResponseBody,
  getServiceCustomFieldsResponseBody,
} from '../dtos/services/responses';
import { commonErrorResponses } from '../dtos/utils/errorResponse';
import { ApiAccessLevelTag, ApiCategoryTag } from '../enum/apiTag';

// GET /services
export const fetchServiceRoute = createRoute({
  method: 'get',
  path: '/',
  operationId: 'fetchService',
  summary: 'Get service info',
  tags: [ApiCategoryTag.SERVICE, ApiAccessLevelTag.GUEST],
  security: [],
  request: {
    headers: serviceIdHeaderSchema,
  },
  responses: {
    200: getServiceResponseBody,
    ...commonErrorResponses,
  },
});

// GET /services/quests
export const fetchQuestsRoute = createRoute({
  method: 'get',
  path: '/quests',
  operationId: 'fetchQuests',
  summary: 'Get a list of Quests',
  tags: [ApiCategoryTag.SERVICE, ApiAccessLevelTag.MEMBER],
  request: {
    headers: serviceIdHeaderSchema,
  },

  responses: {
    200: questListResponseBody,
    ...commonErrorResponses,
  },
});

// POST /quests
export const createQuestRoute = createRoute({
  method: 'post',
  path: '/quests',
  operationId: 'createQuest',
  summary: 'Create quest',
  tags: [ApiCategoryTag.QUEST, ApiAccessLevelTag.ADMIN],
  security: [{ api_key: [] }],
  request: {
    headers: serviceIdHeaderSchema,
    body: questCreateRequestBody,
  },
  responses: {
    200: questCreateResponseBody,
    ...commonErrorResponses,
  },
});

// GET services/quests/status
export const fetchStatusQuestsRoute = createRoute({
  method: 'get',
  path: '/quests/status',
  operationId: 'fetchStatusQuests',
  summary: 'Get Status Quest data',
  tags: [ApiCategoryTag.QUEST, ApiAccessLevelTag.MEMBER],
  request: {
    headers: serviceIdHeaderSchema,
  },
  responses: {
    200: statusQuestResponseBody,
    ...commonErrorResponses,
  },
});

// GET /services/quests/:questId
export const fetchQuestRoute = createRoute({
  method: 'get',
  path: '/quests/{questId}',
  operationId: 'fetchQuest',
  summary: 'Get the quest detail',
  tags: [ApiCategoryTag.QUEST, ApiAccessLevelTag.MEMBER],
  request: {
    headers: serviceIdHeaderSchema,
    params: PathParameterQuestIdSchema,
  },
  responses: {
    200: questDetailResponseBody,
    ...commonErrorResponses,
  },
});

// GET /services/actions/:actionId
export const fetchActionRoute = createRoute({
  method: 'get',
  path: '/actions/{actionId}',
  operationId: 'fetchAction',
  summary: 'Get the action detail',
  tags: [ApiCategoryTag.QUEST, ApiAccessLevelTag.MEMBER],
  request: {
    headers: serviceIdHeaderSchema,
    params: PathParameterActionIdSchema,
  },
  responses: {
    200: actionDetailResponseBody,
    ...commonErrorResponses,
  },
});

// GET services/rewards/:rewardId
export const fetchRewardRoute = createRoute({
  method: 'get',
  path: '/rewards/{rewardId}',
  operationId: 'fetchReward',
  summary: 'Get the reward detail',
  tags: [ApiCategoryTag.QUEST, ApiAccessLevelTag.MEMBER],
  request: {
    headers: serviceIdHeaderSchema,
    params: PathParameterRewardIdSchema,
  },
  responses: {
    200: rewardDetailResponseBody,
    ...commonErrorResponses,
  },
});

// POST /services
export const createServiceRoute = createRoute({
  method: 'post',
  path: '/',
  operationId: 'createService',
  summary: 'Create service',
  tags: [ApiCategoryTag.SERVICE, ApiAccessLevelTag.ADMIN],
  security: [{ api_key: [] }],
  request: {
    headers: tenantIdHeaderSchema,
    body: registerServiceRequestBody,
  },
  responses: {
    200: registerServiceResponseBody,
    ...commonErrorResponses,
  },
});

// POST services/questionnaire
export const createQuestionnaireRoute = createRoute({
  method: 'post',
  path: '/questionnaire',
  operationId: 'createQuestionnaire',
  summary: 'Create questionnaire action',
  tags: [ApiCategoryTag.QUEST, ApiAccessLevelTag.ADMIN],
  security: [{ api_key: [] }],
  request: {
    headers: serviceIdHeaderSchema,
    body: questionnaireActionCreateRequestBody,
  },
  responses: {
    200: questionnaireDetailResponseBody,
    ...commonErrorResponses,
  },
});

// GET services/questionnaire/:questionnaireId
export const getQuestionnaireRoute = createRoute({
  method: 'get',
  path: '/questionnaire/{questionnaireId}',
  operationId: 'GetQuestionnaire',
  summary: 'Get questionnaire action',
  tags: [ApiCategoryTag.QUEST, ApiAccessLevelTag.MEMBER],
  request: {
    headers: serviceIdHeaderSchema,
    params: PathParameterQuestionnaireIdSchema,
  },
  responses: {
    200: questionnaireReadGetResponseBody,
    ...commonErrorResponses,
  },
});

// PUT services/questionnaire/:questionnaireId
export const updateQuestionnaireRoute = createRoute({
  method: 'put',
  path: '/questionnaire/{questionnaireId}',
  operationId: 'UpdateQuestionnaire',
  summary: 'Update questionnaire action',
  tags: [ApiCategoryTag.QUEST, ApiAccessLevelTag.ADMIN],
  security: [{ api_key: [] }],
  request: {
    headers: serviceIdHeaderSchema,
    params: PathParameterQuestionnaireIdSchema,
    body: questionnaireActionUpdateRequestBody,
  },
  responses: {
    200: questionnaireDetailResponseBody,
    ...commonErrorResponses,
  },
});

// POST services/quests/:questId/actions
export const createQuestActionRoute = createRoute({
  method: 'post',
  path: '/quests/{questId}/actions',
  operationId: 'createQuestAction',
  summary: 'Create quest action',
  tags: [ApiCategoryTag.QUEST, ApiAccessLevelTag.ADMIN],
  security: [{ api_key: [] }],
  request: {
    headers: serviceIdHeaderSchema,
    params: PathParameterQuestIdSchema,
    body: questActionCreateRequestBody,
  },
  responses: {
    200: createQuestActionResponseBody,
    ...commonErrorResponses,
  },
});

// POST services/quests/:questId/rewards
export const createQuestRewardRoute = createRoute({
  method: 'post',
  path: '/quests/{questId}/rewards',
  operationId: 'createQuestReward',
  summary: 'Create quest reward',
  tags: [ApiCategoryTag.QUEST, ApiAccessLevelTag.ADMIN],
  security: [{ api_key: [] }],
  request: {
    headers: serviceIdHeaderSchema,
    params: PathParameterQuestIdSchema,
    body: questRewardCreateRequestBody,
  },
  responses: {
    200: createQuestRewardResponseBody,
    ...commonErrorResponses,
  },
});

// POST services/rewards
export const createRewardRoute = createRoute({
  method: 'post',
  path: '/rewards',
  operationId: 'createReward',
  summary: 'Create reward',
  tags: [ApiCategoryTag.QUEST, ApiAccessLevelTag.ADMIN],
  security: [{ api_key: [] }],
  request: {
    headers: serviceIdHeaderSchema,
    body: createRewardRequestBody,
  },
  responses: {
    200: createRewardResponseBody,
    ...commonErrorResponses,
  },
});

// POST services/serial-codes/import
export const importSerialCodeRoute = createRoute({
  method: 'post',
  path: '/serial-codes/import',
  operationId: 'importSerialCode',
  summary: 'Import serial code',
  tags: [ApiCategoryTag.SERVICE, ApiAccessLevelTag.ADMIN],
  security: [{ api_key: [] }],
  request: {
    headers: serviceIdHeaderSchema,
    body: SerialCodeImportRequestBodySchema,
  },
  responses: {
    200: SerialCodeResponseBodySchema,
    ...commonErrorResponses,
  },
});

// POST services/serial-codes/create
export const createSerialCodeRoute = createRoute({
  method: 'post',
  path: '/serial-codes/create',
  operationId: 'createSerialCode',
  summary: 'Create serial code',
  tags: [ApiCategoryTag.SERVICE, ApiAccessLevelTag.ADMIN],
  security: [{ api_key: [] }],
  request: {
    headers: serviceIdHeaderSchema,
    body: SerialCodeCreateRequestBodySchema,
  },
  responses: {
    200: SerialCodeResponseBodySchema,
    ...commonErrorResponses,
  },
});

export const updateServiceCustomFieldsRoute = createRoute({
  method: 'post',
  path: '/custom-fields',
  operationId: 'updateServiceCustomFields',
  summary: 'Update custom fields definition for service',
  tags: [ApiCategoryTag.SERVICE, ApiAccessLevelTag.ADMIN],
  request: {
    headers: serviceIdHeaderSchema,
    body: serviceCustomFieldsRequestBody,
  },
  responses: {
    200: serviceCustomFieldsUpdateResponseBody,
    ...commonErrorResponses,
  },
});

export const getServiceCustomFieldsRoute = createRoute({
  method: 'get',
  path: '/custom-fields',
  operationId: 'getServiceCustomFields',
  summary: 'Get custom fields definition for service',
  tags: [ApiCategoryTag.SERVICE, ApiAccessLevelTag.MEMBER],
  request: {
    headers: serviceIdHeaderSchema,
  },
  responses: {
    200: getServiceCustomFieldsResponseBody,
    ...commonErrorResponses,
  },
});

// GET /serial-codes/projects
export const getSerialCodeProjects = createRoute({
  method: 'get',
  path: '/serial-codes/projects',
  operationId: 'getSerialCodeProjects',
  summary: 'Get serial code projects',
  tags: [ApiCategoryTag.SERVICE, ApiAccessLevelTag.MEMBER],
  request: {
    headers: serviceIdHeaderSchema,
  },
  responses: {
    200: SerialCodeProjectsResponseBodySchema,
    ...commonErrorResponses,
  },
});
