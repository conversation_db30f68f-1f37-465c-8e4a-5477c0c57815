import { createRoute } from '@hono/zod-openapi';
import { tenantIdHeaderSchema } from '../dtos/param/header/tenantIdHeaderScheme';
import { commonErrorResponses } from '../dtos/utils/errorResponse';
import { updateVaultNonceRequestBody } from '../dtos/vaults/requests';
import { createVaultResponseBody, getVaultResponseBody, updateVaultNonceResponseBody } from '../dtos/vaults/responses';
import { ApiAccessLevelTag, ApiCategoryTag } from '../enum/apiTag';

// POST   /
export const createVaultRoute = createRoute({
  method: 'post',
  path: '/',
  operationId: 'createVault',
  summary: 'Generate vault key for tenant',
  tags: [ApiCategoryTag.VAULT, ApiAccessLevelTag.ADMIN],
  security: [{ api_key: [] }],
  request: {
    headers: tenantIdHeaderSchema,
  },
  responses: {
    200: createVaultResponseBody,
    ...commonErrorResponses,
  },
});

// GET    /vaults
export const getVaultDetailsRoute = createRoute({
  method: 'get',
  path: '/',
  operationId: 'getVaultDetails',
  summary: 'Retrieve vault details for tenant',
  tags: [ApiCategoryTag.VAULT, ApiAccessLevelTag.ADMIN],
  security: [{ api_key: [] }],
  request: {
    headers: tenantIdHeaderSchema,
  },
  responses: {
    200: getVaultResponseBody,
    ...commonErrorResponses,
  },
});

// PUT    /nonce
export const updateVaultNonceRoute = createRoute({
  method: 'put',
  path: '/nonce',
  operationId: 'updateVaultNonce',
  summary: 'Update vault nonce',
  tags: [ApiCategoryTag.VAULT, ApiAccessLevelTag.ADMIN],
  security: [{ api_key: [] }],
  request: {
    headers: tenantIdHeaderSchema,
    body: updateVaultNonceRequestBody,
  },
  responses: {
    200: updateVaultNonceResponseBody,
    ...commonErrorResponses,
  },
});
