import { createRoute } from '@hono/zod-openapi';
import { commonErrorResponses, serverErrorSchema } from '../dtos/utils/errorResponse';
import { expireRewardPointsResponseBody } from '../dtos/points/responses';
import { PathParameterAccountIdSchema } from '../dtos/accounts/path';
import { serviceIdHeaderSchema } from '../dtos/param/header/serviceIdHeaderSchema';
import { getTotalRewardPointsResponseBody, getTotalStatusPointsResponseBody } from '../dtos/points/responses';
import { ApiAccessLevelTag, ApiCategoryTag } from '../enum/apiTag';

export const expireRewardPointsRoute = createRoute({
  method: 'post',
  path: '/reward/expire',
  summary: 'expire reward point',
  operationId: 'expireRewardPoints',
  tags: [ApiCategoryTag.POINT, ApiAccessLevelTag.SYSTEM],
  request: {},
  responses: {
    200: expireRewardPointsResponseBody,
    500: { description: 'Server error', content: { 'application/json': { schema: serverErrorSchema } } },
  },
});

export const getTotalRewardPointsRoute = createRoute({
  method: 'get',
  path: '/reward/{accountId}',
  summary: 'get total reward points',
  operationId: 'getTotalRewardPoints',
  tags: [ApiCategoryTag.POINT, ApiAccessLevelTag.MEMBER],
  request: {
    params: PathParameterAccountIdSchema,
    headers: serviceIdHeaderSchema,
  },
  responses: {
    200: getTotalRewardPointsResponseBody,
    ...commonErrorResponses,
  },
});

export const getTotalStatusPointsRoute = createRoute({
  method: 'get',
  path: '/status/{accountId}',
  summary: 'get total status points',
  operationId: 'getTotalStatusPoints',
  tags: [ApiCategoryTag.POINT, ApiAccessLevelTag.MEMBER],
  request: {
    params: PathParameterAccountIdSchema,
    headers: serviceIdHeaderSchema,
  },
  responses: {
    200: getTotalStatusPointsResponseBody,
    ...commonErrorResponses,
  },
});
