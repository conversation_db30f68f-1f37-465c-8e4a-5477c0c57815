import { createRoute } from '@hono/zod-openapi';
import { z } from '@hono/zod-openapi';
import { PathParameterGeofenceIdSchema } from '../dtos/locations/path';
import {
  PostLocationGeofenceRequestBodySchema,
  PutLocationGeofenceRequestBodySchema,
} from '../dtos/locations/requests';
import {
  PostLocationGeofenceResponseSchema,
  PutLocationGeofenceResponseSchema,
  GetLocationGeofenceResponseSchema,
  GetLocationGeofencesResponseSchema,
} from '../dtos/locations/responses';
import { commonErrorResponses } from '../dtos/utils/errorResponse';
import { ApiAccessLevelTag, ApiCategoryTag } from '../enum/apiTag';
import { serviceIdHeaderSchema } from '../dtos/param/header/serviceIdHeaderSchema';


export const postGeofenceRoute = createRoute({
  method: 'post',
  path: '/geofences',
  tags: [ApiCategoryTag.LOCATION, ApiAccessLevelTag.ADMIN],
  summary: 'Create a new geofence',
  description: 'Create a new geofence for location-based actions. Supports both circle and polygon geofences.',
  request: {
    body: PostLocationGeofenceRequestBodySchema,
    headers: serviceIdHeaderSchema,
  },
  responses: {
    201: PostLocationGeofenceResponseSchema,
    ...commonErrorResponses,
  },
});

export const putGeofenceRoute = createRoute({
  method: 'put',
  path: '/geofences/{geofenceId}',
  tags: [ApiCategoryTag.LOCATION, ApiAccessLevelTag.ADMIN],
  summary: 'Update an existing geofence',
  description: 'Update an existing geofence by ID. Can modify name and geometry data.',
  request: {
    params: PathParameterGeofenceIdSchema,
    body: PutLocationGeofenceRequestBodySchema,
    headers: serviceIdHeaderSchema,
  },
  responses: {
    200: PutLocationGeofenceResponseSchema,
    ...commonErrorResponses,
  },
});

export const getGeofencesRoute = createRoute({
  method: 'get',
  path: '/geofences',
  tags: [ApiCategoryTag.LOCATION, ApiAccessLevelTag.ADMIN],
  summary: 'Get all geofences for a service',
  description: 'Retrieve all geofences belonging to the specified service.',
  request: {
    headers: serviceIdHeaderSchema,
  },
  responses: {
    200: GetLocationGeofencesResponseSchema,
    ...commonErrorResponses,
  },
});

export const getGeofenceRoute = createRoute({
  method: 'get',
  path: '/geofences/{geofenceId}',
  tags: [ApiCategoryTag.LOCATION, ApiAccessLevelTag.MEMBER],
  summary: 'Get a specific geofence by ID',
  description: 'Retrieve detailed information about a specific geofence.',
  request: {
    params: PathParameterGeofenceIdSchema,
    headers: serviceIdHeaderSchema,
  },
  responses: {
    200: GetLocationGeofenceResponseSchema,
    ...commonErrorResponses,
  },
});
