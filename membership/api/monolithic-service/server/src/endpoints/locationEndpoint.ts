import { createRoute } from '@hono/zod-openapi';
import { z } from 'zod';
import {
  PostLocationGeofenceRequestSchema,
  PostLocationGeofenceResponseSchema,
  PutLocationGeofenceRequestSchema,
  PutLocationGeofenceResponseSchema,
  GetLocationGeofenceResponseSchema,
  GetLocationGeofencesResponseSchema,
  PathParameterGeofenceIdSchema,
} from '../dtos/locations/schemas';
import { commonErrorResponses } from '../dtos/utils/errorResponse';
import { ApiAccessLevelTag, ApiCategoryTag } from '../enum/apiTag';


export const postGeofenceRoute = createRoute({
  method: 'post',
  path: '/geofences',
  tags: [ApiCategoryTag.LOCATION, ApiAccessLevelTag.ADMIN],
  summary: 'Create a new geofence',
  description: 'Create a new geofence for location-based actions. Supports both circle and polygon geofences.',
  request: {
    body: {
      content: {
        'application/json': {
          schema: PostLocationGeofenceRequestSchema,
        },
      },
      description: 'Geofence data to create',
      required: true,
    },
    headers: z.object({
      'service-id-header': z.string().openapi({
        description: 'Service ID header',
        example: 'service-123',
      }),
    }),
  },
  responses: {
    201: {
      content: {
        'application/json': {
          schema: PostLocationGeofenceResponseSchema,
        },
      },
      description: 'Geofence created successfully',
    },
    ...commonErrorResponses,
  },
});

export const putGeofenceRoute = createRoute({
  method: 'put',
  path: '/geofences/{geofenceId}',
  tags: [ApiCategoryTag.LOCATION, ApiAccessLevelTag.ADMIN],
  summary: 'Update an existing geofence',
  description: 'Update an existing geofence by ID. Can modify name and geometry data.',
  request: {
    params: PathParameterGeofenceIdSchema,
    body: {
      content: {
        'application/json': {
          schema: PutLocationGeofenceRequestSchema,
        },
      },
      description: 'Updated geofence data',
      required: true,
    },
    headers: z.object({
      'service-id-header': z.string().openapi({
        description: 'Service ID header',
        example: 'service-123',
      }),
    }),
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: PutLocationGeofenceResponseSchema,
        },
      },
      description: 'Geofence updated successfully',
    },
    ...commonErrorResponses,
  },
});

export const getGeofencesRoute = createRoute({
  method: 'get',
  path: '/geofences',
  tags: [ApiCategoryTag.LOCATION, ApiAccessLevelTag.ADMIN],
  summary: 'Get all geofences for a service',
  description: 'Retrieve all geofences belonging to the specified service.',
  request: {
    headers: z.object({
      'service-id-header': z.string().openapi({
        description: 'Service ID header',
        example: 'service-123',
      }),
    }),
    query: z.object({
      page: z.string().optional().openapi({
        description: 'Page number for pagination',
        example: '1',
      }),
      limit: z.string().optional().openapi({
        description: 'Number of items per page',
        example: '20',
      }),
      geofenceType: z.enum(['CIRCLE', 'POLYGON']).optional().openapi({
        description: 'Filter by geofence type',
        example: 'CIRCLE',
      }),
      search: z.string().optional().openapi({
        description: 'Search by name',
        example: 'Tokyo',
      }),
    }),
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: GetLocationGeofencesResponseSchema,
        },
      },
      description: 'Geofences retrieved successfully',
    },
    ...commonErrorResponses,
  },
});

export const getGeofenceRoute = createRoute({
  method: 'get',
  path: '/geofences/{geofenceId}',
  tags: [ApiCategoryTag.LOCATION, ApiAccessLevelTag.MEMBER],
  summary: 'Get a specific geofence by ID',
  description: 'Retrieve detailed information about a specific geofence.',
  request: {
    params: PathParameterGeofenceIdSchema,
    headers: z.object({
      'service-id-header': z.string().openapi({
        description: 'Service ID header',
        example: 'service-123',
      }),
    }),
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: GetLocationGeofenceResponseSchema,
        },
      },
      description: 'Geofence retrieved successfully',
    },
    ...commonErrorResponses,
  },
});
