import { createRoute } from '@hono/zod-openapi';
import { commonErrorResponses } from '../dtos/utils/errorResponse';
import { webhookReceiveTransactionRequestBody } from '../dtos/webhook/requests';
import { webhookReceiveTransactionResponseBody } from '../dtos/webhook/responses';
import { alchemySignatureHeaderSchema, stripeSignatureHeaderSchema } from '../dtos/param/header/signatureHeaderSchema';
import { ApiAccessLevelTag, ApiCategoryTag } from '../enum/apiTag';

export const webhookTransactionRoute = createRoute({
  method: 'post',
  path: '/transaction',
  operationId: 'webhookTransaction',
  summary: 'Webhook for transaction receipt',
  tags: [ApiCategoryTag.NFT, ApiAccessLevelTag.SYSTEM],
  security: [],
  request: {
    headers: alchemySignatureHeaderSchema,
    body: webhookReceiveTransactionRequestBody,
  },
  responses: {
    200: webhookReceiveTransactionResponseBody,
    ...commonErrorResponses,
  },
});

export const handleStripeEventsRoute = createRoute({
  method: 'post',
  path: '/checkout',
  operationId: 'handleStripeEvents',
  summary: 'Handle Stripe events',
  tags: [ApiCategoryTag.PRODUCT, ApiAccessLevelTag.SYSTEM],
  security: [],
  request: {
    headers: stripeSignatureHeaderSchema,
  },
  responses: {
    200: {
      description: 'successful operation',
    },
    ...commonErrorResponses,
  },
});
