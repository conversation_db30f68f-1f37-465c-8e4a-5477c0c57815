import { createRoute, z } from '@hono/zod-openapi';
import { commonErrorResponses } from '../dtos/utils/errorResponse';

//Schemas
import {
  GetAccountQuestsQuerySchema,
  PathParameterAccountIdSchema,
  PathParameterActionIdSchema,
  PathParameterQuestIdSchema,
  PathParameterQuestionnaireIdSchema,
  PostClaimRewardPathSchema,
} from '../dtos/accounts/path';
import { lineIdTokenHeaderSchema } from '../dtos/param/header/lineIdHeaderSchema';
import { serviceIdHeaderSchema } from '../dtos/param/header/serviceIdHeaderSchema';

//Requests
import { 
  ActionCompleteRequestBodySchema,
  SerialCodeRedeemRequestBodySchema,
  AccountCustomFieldsRequestBodySchema,
  GenerateEmailOtpRequestBodySchema,
  ValidateEmailOtpRequestBodySchema
} from '../dtos/accounts/requests';
import { uoPrepareConsumptionRequestBodySchema, uoSendRequestBodySchema } from '../dtos/user_operations/requests';

//Responses
import {
  AccountDeleteResponseSchema,
  AccountLastLoginResponseSchema,
  AccountQuestionnaireDetailResponseSchema,
  AccountResponseSchema,
  AccountStatusResponseSchema,
  ActivityHistoriesResponseSchema,
  ActivityQuestCompleteResponseSchema,
  ActivityQuestDetailResponseSchema,
  ActivityQuestsResponseSchema,
  ActivityRewardClaimResponseSchema,
  NotificationListResponseSchema,
  SerialCodeRedeemResponseSchema,
  AccountCustomFieldsResponseSchema,
  AccountCustomFieldsUpdateResponseSchema,
  GenerateEmailOtpResponseSchema,
  ValidateEmailOtpResponseSchema,
} from '../dtos/accounts/responses';
import { CertificatesResponse, ContentsListResponse, CouponsListResponse } from '../dtos/collections/responses';
import { uoPrepareResponseSchema, uoSendResponseSchema } from '../dtos/user_operations/responses';
import { ApiAccessLevelTag, ApiCategoryTag } from '../enum/apiTag';

export const getAccountStatusRoute = createRoute({
  method: 'get',
  path: '/{accountId}/status',
  summary: 'Get status',
  description: '',
  operationId: 'fetchAccountStatus',
  tags: [ApiCategoryTag.ACCOUNT, ApiAccessLevelTag.MEMBER],
  request: {
    headers: serviceIdHeaderSchema,
    params: PathParameterAccountIdSchema,
  },
  responses: {
    200: AccountStatusResponseSchema,
    ...commonErrorResponses,
  },
});

export const getAccountRoute = createRoute({
  method: 'get',
  path: '/{accountId}',
  summary: 'Get account information',
  description: '',
  operationId: 'fetchServiceAccount',
  tags: [ApiCategoryTag.ACCOUNT, ApiAccessLevelTag.MEMBER],
  request: {
    headers: serviceIdHeaderSchema,
    params: PathParameterAccountIdSchema,
  },
  responses: {
    200: AccountResponseSchema,
    ...commonErrorResponses,
  },
});

const postAccountHeaderSchema = serviceIdHeaderSchema.merge(lineIdTokenHeaderSchema);
export const postAccountRoute = createRoute({
  method: 'post',
  path: '/',
  summary: 'Create account',
  description: '',
  operationId: 'createAccount',
  tags: [ApiCategoryTag.ACCOUNT, ApiAccessLevelTag.MEMBER],
  request: {
    headers: postAccountHeaderSchema,
  },
  responses: {
    200: AccountResponseSchema,
    ...commonErrorResponses,
  },
});

const putAccountLineProfileHeaderSchema = serviceIdHeaderSchema.merge(lineIdTokenHeaderSchema);
export const putAccountLineProfileRoute = createRoute({
  method: 'put',
  path: '/{accountId}/line-profile',
  summary: 'Update user line profile',
  description: '',
  operationId: 'updateAccountLineProfile',
  tags: [ApiCategoryTag.ACCOUNT, ApiAccessLevelTag.MEMBER],
  request: {
    headers: putAccountLineProfileHeaderSchema,
    params: PathParameterAccountIdSchema,
  },
  responses: {
    200: AccountResponseSchema,
    ...commonErrorResponses,
  },
});

export const deleteAccountRoute = createRoute({
  method: 'delete',
  path: '/{accountId}',
  summary: 'Delete account information',
  description: '',
  operationId: 'deleteServiceAccount',
  tags: [ApiCategoryTag.ACCOUNT, ApiAccessLevelTag.MEMBER],
  request: {
    headers: serviceIdHeaderSchema,
    params: PathParameterAccountIdSchema,
  },
  responses: {
    200: AccountDeleteResponseSchema,
    ...commonErrorResponses,
  },
});

// GET accounts/{accountId}/activity-history
export const getAccountActivityHistoryRoute = createRoute({
  method: 'get',
  path: '/{accountId}/activity-history',
  summary: 'Get activity history',
  description: '',
  operationId: 'fetchAccountActivityHistory',
  tags: [ApiCategoryTag.ACCOUNT, ApiAccessLevelTag.MEMBER],
  request: {
    params: PathParameterAccountIdSchema,
  },
  responses: {
    200: ActivityHistoriesResponseSchema,
    ...commonErrorResponses,
  },
});

export const getNotificationsRoute = createRoute({
  method: 'get',
  path: '/{accountId}/notifications',
  operationId: 'fetchNotifications',
  summary: 'Fetch notification list',
  tags: [ApiCategoryTag.ACCOUNT, ApiAccessLevelTag.MEMBER],
  request: {
    params: PathParameterAccountIdSchema,
    headers: serviceIdHeaderSchema,
  },
  responses: {
    200: NotificationListResponseSchema,
    ...commonErrorResponses,
  },
});

// POST accounts/{accountId}/actions/:actionId/complete
const completeActionPathSchema = PathParameterAccountIdSchema.merge(PathParameterActionIdSchema);
export const completeActionRoute = createRoute({
  method: 'post',
  path: '/{accountId}/actions/{actionId}/complete',
  operationId: 'completeAction',
  summary: 'Complete action',
  tags: [ApiCategoryTag.ACCOUNT, ApiAccessLevelTag.MEMBER],
  request: {
    headers: serviceIdHeaderSchema,
    params: completeActionPathSchema,
    body: ActionCompleteRequestBodySchema,
  },
  responses: {
    200: ActivityQuestCompleteResponseSchema,
    ...commonErrorResponses,
  },
});

export const prepareConsumptionUORoute = createRoute({
  method: 'post',
  path: '/{accountId}/user-operation/prepare/consumption',
  operationId: 'prepareConsumptionUO',
  summary: 'Prepare UO for coupon consumption',
  tags: [ApiCategoryTag.ACCOUNT, ApiAccessLevelTag.MEMBER],
  request: {
    headers: serviceIdHeaderSchema,
    params: PathParameterAccountIdSchema,
    body: uoPrepareConsumptionRequestBodySchema,
  },
  responses: {
    200: uoPrepareResponseSchema,
    ...commonErrorResponses,
  },
});

export const sendUORoute = createRoute({
  method: 'post',
  path: '/{accountId}/user-operation/send',
  operationId: 'sendUO',
  summary: 'Send UO',
  tags: [ApiCategoryTag.ACCOUNT, ApiAccessLevelTag.MEMBER],
  request: {
    headers: serviceIdHeaderSchema,
    params: PathParameterAccountIdSchema,
    body: uoSendRequestBodySchema,
  },
  responses: {
    200: uoSendResponseSchema,
    ...commonErrorResponses,
  },
});

export const getCouponListRoute = createRoute({
  method: 'get',
  path: '/{accountId}/collections/coupons',
  operationId: 'getCouponList',
  summary: 'Retrieve coupons',
  description: 'Get a list of coupons for the specified account.',
  tags: [ApiCategoryTag.ACCOUNT, ApiAccessLevelTag.MEMBER],
  request: {
    headers: serviceIdHeaderSchema,
    params: PathParameterAccountIdSchema,
  },
  responses: {
    200: CouponsListResponse,
    ...commonErrorResponses,
  },
});

export const getContentsRoute = createRoute({
  method: 'get',
  path: '/{accountId}/collections/contents',
  operationId: 'getContentList',
  summary: 'Retrieve digital contents',
  tags: [ApiCategoryTag.ACCOUNT, ApiAccessLevelTag.MEMBER],
  request: {
    params: PathParameterAccountIdSchema,
    headers: serviceIdHeaderSchema,
  },
  responses: {
    200: ContentsListResponse,
    ...commonErrorResponses,
  },
});

export const getCertificatesRoute = createRoute({
  method: 'get',
  path: '/{accountId}/collections/certificates',
  operationId: 'getCertificatList',
  summary: 'Retrieve certificates',
  tags: [ApiCategoryTag.ACCOUNT, ApiAccessLevelTag.MEMBER],
  request: {
    params: PathParameterAccountIdSchema,
    headers: serviceIdHeaderSchema,
  },
  responses: {
    200: CertificatesResponse,
    ...commonErrorResponses,
  },
});

export const postClaimRewardRoute = createRoute({
  method: 'post',
  path: '/{accountId}/rewards/{rewardId}/claim',
  operationId: 'claimReward',
  summary: 'Claim reward',
  tags: [ApiCategoryTag.ACCOUNT, ApiAccessLevelTag.MEMBER],
  request: {
    headers: serviceIdHeaderSchema,
    params: PostClaimRewardPathSchema,
  },
  responses: {
    200: ActivityRewardClaimResponseSchema,
    ...commonErrorResponses,
  },
});

export const getAccountQuests = createRoute({
  method: 'get',
  path: '/{accountId}/quests',
  summary: 'Get a list of filtered quests',
  operationId: 'fetchAccountQuests',
  tags: [ApiCategoryTag.ACCOUNT, ApiAccessLevelTag.MEMBER],
  request: {
    headers: serviceIdHeaderSchema,
    params: PathParameterAccountIdSchema,
    query: GetAccountQuestsQuerySchema,
  },
  responses: {
    200: ActivityQuestsResponseSchema,
    ...commonErrorResponses,
  },
});

export const getAccountQuestDetail = createRoute({
  method: 'get',
  path: '/{accountId}/quests/{questId}',
  summary: 'Get detail status of a quest',
  operationId: 'fetchAccountQuestDetail',
  tags: [ApiCategoryTag.ACCOUNT, ApiAccessLevelTag.MEMBER],
  request: {
    headers: serviceIdHeaderSchema,
    params: PathParameterAccountIdSchema.merge(PathParameterQuestIdSchema),
  },
  responses: {
    200: ActivityQuestDetailResponseSchema,
    ...commonErrorResponses,
  },
});

// GET accounts/{accountId}/questionnaire/:questionnaireId
export const getAccountQuestionnaireDetail = createRoute({
  method: 'get',
  path: '/{accountId}/questionnaire/{questionnaireId}',
  summary: 'Get questionnaire detail',
  operationId: 'fetchAccountQuestionnaireDetail',
  tags: [ApiCategoryTag.ACCOUNT, ApiAccessLevelTag.MEMBER],
  request: {
    headers: serviceIdHeaderSchema,
    params: PathParameterAccountIdSchema.merge(PathParameterQuestionnaireIdSchema),
  },
  responses: {
    200: AccountQuestionnaireDetailResponseSchema,
    ...commonErrorResponses,
  },
});

// POST accounts/{accountId}/last-login
export const updateLastLogin = createRoute({
  method: 'post',
  path: '/{accountId}/last-login',
  summary: 'Update last login timestamp',
  operationId: 'updateLastLogin',
  tags: [ApiCategoryTag.ACCOUNT, ApiAccessLevelTag.MEMBER],
  request: {
    headers: serviceIdHeaderSchema,
    params: PathParameterAccountIdSchema,
  },
  responses: {
    200: AccountLastLoginResponseSchema,
    ...commonErrorResponses,
  },
});


export const postAccountSerialCodeRedeem = createRoute({
  method: 'post',
  path: '/{accountId}/serial-codes/redeem',
  summary: 'Redeem SerialCode',
  operationId: 'redeemSerialCode',
  tags: [ApiCategoryTag.ACCOUNT, ApiAccessLevelTag.MEMBER],
  request: {
    headers: serviceIdHeaderSchema,
    params: PathParameterAccountIdSchema,
    body: SerialCodeRedeemRequestBodySchema,
  },
  responses: {
    200: SerialCodeRedeemResponseSchema,
    ...commonErrorResponses,
  },
});

export const getAccountCustomFieldRoute = createRoute({
  method: 'get',
  path: '/:accountId/custom-field',
  operationId: 'getAccountCustomField',
  summary: 'Get custom field values for an account',
  tags: [ApiCategoryTag.ACCOUNT, ApiAccessLevelTag.MEMBER],
  request: {
    headers: serviceIdHeaderSchema,
    params: PathParameterAccountIdSchema,
  },
  responses: {
    200: AccountCustomFieldsResponseSchema,
    ...commonErrorResponses,
  },
});

export const updateAccountCustomFieldRoute = createRoute({
  method: 'post',
  path: '/:accountId/custom-field',
  operationId: 'updateAccountCustomField',
  summary: 'Update custom field values for an account',
  tags: [ApiCategoryTag.ACCOUNT, ApiAccessLevelTag.MEMBER],
  request: {
    headers: serviceIdHeaderSchema,
    params: PathParameterAccountIdSchema,
    body: AccountCustomFieldsRequestBodySchema,
  },
  responses: {
    200: AccountCustomFieldsUpdateResponseSchema,
    ...commonErrorResponses,
  },
});

export const generateEmailOtp = createRoute({
  method: 'post',
  path: '/:accountId/email-otp',
  operationId: 'sendOtpToEmail',
  summary: 'Send verification code to account email address',
  tags: [ApiCategoryTag.ACCOUNT, ApiAccessLevelTag.MEMBER],
  request: {
    headers: serviceIdHeaderSchema,
    params: PathParameterAccountIdSchema,
    body: GenerateEmailOtpRequestBodySchema,
  },
  responses: {
    200: GenerateEmailOtpResponseSchema,
    ...commonErrorResponses,
  },
});

export const validateEmailOtp = createRoute({
  method: 'post',
  path: '/:accountId/email-otp/verify',
  operationId: 'verifyOtp',
  summary: 'Verify email address with OTP code',
  tags: [ApiCategoryTag.ACCOUNT, ApiAccessLevelTag.MEMBER],
  request: {
    headers: serviceIdHeaderSchema,
    params: PathParameterAccountIdSchema,
    body: ValidateEmailOtpRequestBodySchema,
  },
  responses: {
    200: ValidateEmailOtpResponseSchema,
    ...commonErrorResponses,
  },
});
