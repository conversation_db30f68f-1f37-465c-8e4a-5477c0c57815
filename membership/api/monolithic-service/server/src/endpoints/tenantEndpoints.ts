import { createRoute } from '@hono/zod-openapi';
import { registerTenantRequestBody } from '../dtos/tenants_td/requests';
import { tenantCreateResponseBody } from '../dtos/tenants_td/responses';
import { commonErrorResponses } from '../dtos/utils/errorResponse';
import { ApiAccessLevelTag, ApiCategoryTag } from '../enum/apiTag';

// POST /tenants
export const registerTenantRoute = createRoute({
  method: 'post',
  path: '/',
  operationId: 'registerTenant',
  summary: 'Register tenant',
  tags: [ApiCategoryTag.TENANT, ApiAccessLevelTag.ADMIN],
  security: [{ api_key: [] }],
  request: {
    body: registerTenantRequestBody,
  },
  responses: {
    200: tenantCreateResponseBody,
    ...commonErrorResponses,
  },
});
