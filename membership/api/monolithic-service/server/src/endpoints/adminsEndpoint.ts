import { createRoute } from '@hono/zod-openapi';
import { UpdateMetadataRequestBodySchema } from '../dtos/admins/requests';
import { UpdateMetadataResponse } from '../dtos/admins/responses';
import { adminApi<PERSON>eyHeaderSchema } from '../dtos/param/header/adminApiKeyHeader';
import { commonErrorResponses, notFoundErrorSchema } from '../dtos/utils/errorResponse';
import { ApiAccessLevelTag, ApiCategoryTag } from '../enum/apiTag';

export const updateMetadataRoute = createRoute({
  method: 'post',
  path: '/firestore/metadata/update',
  operationId: 'updateMetadata',
  summary: 'Update metadata',
  tags: [ApiCategoryTag.NFT, ApiAccessLevelTag.ADMIN],
  security: [{ api_key: [] }],
  request: {
    headers: adminApiKeyHeaderSchema,
    body: UpdateMetadataRequestBodySchema,
  },
  responses: {
    200: UpdateMetadataResponse,
    404: {
      description: 'Service unavailable',
      content: {
        'application/json': {
          schema: notFoundErrorSchema,
        },
      },
    },
    ...commonErrorResponses,
  },
});
