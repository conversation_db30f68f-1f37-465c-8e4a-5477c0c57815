import { createRoute } from '@hono/zod-openapi';
import { UpdateMetadataRequestBodySchema } from '../dtos/admins/requests';
import { adjustPointResponse, UpdateMetadataResponse } from '../dtos/admins/responses';
import { adjustPointRequestBody } from '../dtos/admins/requests';
import { adminApiKeyHeaderSchema } from '../dtos/param/header/adminApiKeyHeader';
import { commonErrorResponses, notFoundErrorSchema } from '../dtos/utils/errorResponse';
import { ApiAccessLevelTag, ApiCategoryTag } from '../enum/apiTag';

export const updateMetadataRoute = createRoute({
  method: 'post',
  path: '/firestore/metadata/update',
  operationId: 'updateMetadata',
  summary: 'Update metadata',
  tags: [ApiCategoryTag.NFT, ApiAccessLevelTag.ADMIN],
  security: [{ api_key: [] }],
  request: {
    headers: adminApiKeyHeaderSchema,
    body: UpdateMetadataRequestBodySchema,
  },
  responses: {
    200: UpdateMetadataResponse,
    404: {
      description: 'Service unavailable',
      content: {
        'application/json': {
          schema: notFoundErrorSchema,
        },
      },
    },
    ...commonErrorResponses,
  },
});

export const adjustPointRoute = createRoute({
  method: 'post',
  path: '/adjust-point',
  operationId: 'adjustPoint',
  summary: 'Adjust point',
  tags: [ApiCategoryTag.POINT, ApiAccessLevelTag.ADMIN],
  request: {
    headers: adminApiKeyHeaderSchema,
    body: adjustPointRequestBody,
  },
  responses: {
    201: adjustPointResponse,
    ...commonErrorResponses,
  },
});
