import { createRoute } from '@hono/zod-openapi';
import { CustomTokenResponseSchema } from '../dtos/auth/responses';
import { lineIdTokenHeaderSchema } from '../dtos/param/header/lineIdHeaderSchema';
import { serviceIdHeaderSchema } from '../dtos/param/header/serviceIdHeaderSchema';
import { commonErrorResponses } from '../dtos/utils/errorResponse';
import { ApiAccessLevelTag, ApiCategoryTag } from '../enum/apiTag';

const authCustomTokenHeaderSchema = serviceIdHeaderSchema.merge(lineIdTokenHeaderSchema);

// POST /auth/custom-token
export const createCustomTokenRoute = createRoute({
  method: 'post',
  path: '/custom-token',
  operationId: 'createCustomToken',
  summary: 'Link lineIdToken to account',
  tags: [ApiCategoryTag.AUTH, ApiAccessLevelTag.ACCOUNT],
  request: {
    headers: authCustomTokenHeaderSchema,
  },
  responses: {
    200: CustomTokenResponseSchema,
    ...commonErrorResponses,
  },
});

const getCustomTokenHeaderSchema = serviceIdHeaderSchema.merge(lineIdTokenHeaderSchema);

// GET /auth/custom-token
export const getCustomTokenRoute = createRoute({
  method: 'get',
  path: '/custom-token',
  operationId: 'getCustomToken',
  summary: 'Issue a CustomToken',
  security: [],
  tags: [ApiCategoryTag.AUTH, ApiAccessLevelTag.GUEST],
  request: {
    headers: getCustomTokenHeaderSchema,
  },
  responses: {
    200: CustomTokenResponseSchema,
    ...commonErrorResponses,
  },
});
