import { createRoute } from '@hono/zod-openapi';
import { serviceIdHeaderSchema } from '../dtos/param/header/serviceIdHeaderSchema';
import { PathParameterAccountIdSchema } from '../dtos/products/parameters';
import {
  confirmSessionRequestBody,
  createSessionRequestBody,
  expireSessionRequestBody,
} from '../dtos/products/requests';
import {
  confirmSessionResponseBody,
  createSessionResponseBody,
  getProductsResponseBody,
} from '../dtos/products/responses';
import { commonErrorResponses } from '../dtos/utils/errorResponse';
import { ApiAccessLevelTag, ApiCategoryTag } from '../enum/apiTag';

// GET products/{accountId}
export const getProductsRoute = createRoute({
  method: 'get',
  path: '/{accountId}',
  operationId: 'getProducts',
  summary: 'Get products',
  tags: [ApiCategoryTag.PRODUCT, ApiAccessLevelTag.ACCOUNT],
  request: {
    headers: serviceIdHeaderSchema,
    params: PathParameterAccountIdSchema,
  },
  responses: {
    200: getProductsResponseBody,
    ...commonErrorResponses,
  },
});

// POST products/{accountId}/checkout-session
export const createSessionRoute = createRoute({
  method: 'post',
  path: '/{accountId}/checkout-session',
  operationId: 'createSession',
  summary: 'Create Stripe session',
  tags: [ApiCategoryTag.PRODUCT, ApiAccessLevelTag.ACCOUNT],
  request: {
    headers: serviceIdHeaderSchema,
    params: PathParameterAccountIdSchema,
    body: createSessionRequestBody,
  },
  responses: {
    200: createSessionResponseBody,
    ...commonErrorResponses,
  },
});

// DELETE /{accountId}/products/session
export const expireSessionRoute = createRoute({
  method: 'delete',
  path: '/{accountId}/checkout-session',
  operationId: 'expireSession',
  summary: 'Expire Stripe session',
  tags: [ApiCategoryTag.PRODUCT, ApiAccessLevelTag.ACCOUNT],
  request: {
    headers: serviceIdHeaderSchema,
    params: PathParameterAccountIdSchema,
    body: expireSessionRequestBody,
  },
  responses: {
    204: { description: 'no content' },
    ...commonErrorResponses,
  },
});

// POST /{accountId}/products/session/status
export const confirmSessionRoute = createRoute({
  method: 'post',
  path: '/{accountId}/checkout-session/status',
  operationId: 'confirmSession',
  summary: 'Confirm Stripe session status',
  tags: [ApiCategoryTag.PRODUCT, ApiAccessLevelTag.ACCOUNT],
  request: {
    headers: serviceIdHeaderSchema,
    params: PathParameterAccountIdSchema,
    body: confirmSessionRequestBody,
  },
  responses: {
    200: confirmSessionResponseBody,
    ...commonErrorResponses,
  },
});
