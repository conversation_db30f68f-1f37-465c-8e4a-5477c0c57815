import { createRoute } from '@hono/zod-openapi';
import { getI18nMetadataParamsSchema, getMetadataParamsSchema } from '../dtos/nfts/parameters';
import { getMetadataRequestBody, nftMintRequestBody, nftRegisterRequestBody } from '../dtos/nfts/requests';
import {
  bulkMintNftResponseBody,
  deployModularNFTResponseBody,
  generalMetadataResponseBody,
  getI18nNFTMetadataErrorResponses,
  getI18nNFTMetadataResponseBody,
  getMetadataResponseBody,
  grantNftsMinterRoleResponseBody,
  nftMintResponseBody,
  nftRegisterResponseBody,
  nftRetryTransactionsResponseBody,
  txFinalityStatusResponseBody,
} from '../dtos/nfts/responses';
import { acceptLanguageHeaderSchema } from '../dtos/param/header/acceptLanguageHeaderSchema';
import { accountIdHeaderSchema } from '../dtos/param/header/accountIdHeaderSchema';
import { serviceIdHeaderSchema } from '../dtos/param/header/serviceIdHeaderSchema';
import { commonErrorResponses } from '../dtos/utils/errorResponse';
import { ApiAccessLevelTag, ApiCategoryTag } from '../enum/apiTag';

// GET /:contractAddress/:tokenId -> getMetadata
export const getMetadataRoute = createRoute({
  method: 'get',
  path: '/metadata/{nftId}/{tokenId}',
  operationId: 'getMetadata',
  summary: 'Get metadata',
  tags: [ApiCategoryTag.NFT, ApiAccessLevelTag.GUEST],
  security: [],
  request: {
    params: getMetadataParamsSchema,
  },
  responses: {
    200: generalMetadataResponseBody,
    ...commonErrorResponses,
  },
});

// GET /nfts/i18n/:contractAddress/:tokenId
export const getI18nMetadataRoute = createRoute({
  method: 'get',
  path: '/i18n/{contractAddress}/{tokenId}',
  operationId: 'getI18nNFTMetadata',
  summary: 'Get i18n NFT metadata',
  tags: [ApiCategoryTag.NFT, ApiAccessLevelTag.MEMBER],
  request: {
    params: getI18nMetadataParamsSchema,
    headers: accountIdHeaderSchema.merge(acceptLanguageHeaderSchema),
  },
  responses: {
    200: getI18nNFTMetadataResponseBody,
    ...getI18nNFTMetadataErrorResponses,
  },
});

// POST /nfts/register -> registerNFT
export const registerNFTRoute = createRoute({
  method: 'post',
  path: '/register',
  operationId: 'registerNFT',
  summary: 'Register and Deploy NFT',
  tags: [ApiCategoryTag.NFT, ApiAccessLevelTag.ADMIN],
  security: [{ api_key: [] }],
  request: {
    headers: serviceIdHeaderSchema,
    body: nftRegisterRequestBody,
  },
  responses: {
    200: nftRegisterResponseBody,
    ...commonErrorResponses,
  },
});

// POST /nfts/metadata -> postNftMetadata
export const postNftMetadataRoute = createRoute({
  method: 'post',
  path: '/metadata',
  operationId: 'postNftMetadata',
  summary: 'Search metadata by wallet',
  tags: [ApiCategoryTag.NFT, ApiAccessLevelTag.ADMIN],
  security: [{ api_key: [] }],
  request: {
    headers: serviceIdHeaderSchema,
    body: getMetadataRequestBody,
  },
  responses: {
    200: getMetadataResponseBody,
    ...commonErrorResponses,
  },
});

// POST /nfts/mint -> mintNFT
export const mintNFTRoute = createRoute({
  method: 'post',
  path: '/mint',
  operationId: 'mintNFT',
  summary: 'Issue NFTs',
  tags: [ApiCategoryTag.NFT, ApiAccessLevelTag.ADMIN],
  security: [{ api_key: [] }],
  request: {
    headers: serviceIdHeaderSchema,
    body: nftMintRequestBody,
  },
  responses: {
    200: nftMintResponseBody,
    ...commonErrorResponses,
  },
});

// POST /nfts/deploy-modular
export const deployModularNFTRoute = createRoute({
  method: 'post',
  path: '/deploy-modular',
  operationId: 'deployModularNFT',
  summary: 'Deploy modular NFT',
  tags: [ApiCategoryTag.NFT, ApiAccessLevelTag.ADMIN],
  security: [{ api_key: [] }],
  request: {
    headers: serviceIdHeaderSchema,
  },
  responses: {
    200: deployModularNFTResponseBody,
    ...commonErrorResponses,
  },
});

// POST /nfts/grant-nfts-minter-role
export const grantNftsMinterRoleRoute = createRoute({
  method: 'post',
  path: '/grant-nfts-minter-role',
  operationId: 'grantNftsMinterRole',
  summary: 'Grant NFT minter role',
  tags: [ApiCategoryTag.NFT, ApiAccessLevelTag.ADMIN],
  security: [{ api_key: [] }],
  request: {
    headers: serviceIdHeaderSchema,
  },
  responses: {
    200: grantNftsMinterRoleResponseBody,
    ...commonErrorResponses,
  },
});

// POST /nfts/retry-transactions -> retryTransactions
export const retryTransactionRoute = createRoute({
  method: 'post',
  path: '/retry-transactions',
  operationId: 'retryTransactions',
  summary: 'Retry transactions',
  tags: [ApiCategoryTag.NFT, ApiAccessLevelTag.SYSTEM],
  security: [],
  responses: {
    200: nftRetryTransactionsResponseBody,
    ...commonErrorResponses,
  },
});

// POST /nfts/bulk-mint
export const bulkMintNFTRoute = createRoute({
  method: 'post',
  path: '/bulk-mint',
  operationId: 'bulkMintNFT',
  summary: 'Bulk mint NFTs',
  tags: [ApiCategoryTag.NFT, ApiAccessLevelTag.SYSTEM],
  security: [],
  responses: {
    200: bulkMintNftResponseBody,
    ...commonErrorResponses,
  },
});

// POST /nfts/finality-status
export const updateTxFinalityStatusRoute = createRoute({
  method: 'post',
  path: '/finality-status',
  operationId: 'updateTxFinalityStatus',
  summary: 'Update transaction finality status',
  tags: [ApiCategoryTag.NFT, ApiAccessLevelTag.SYSTEM],
  security: [],
  responses: {
    200: txFinalityStatusResponseBody,
    ...commonErrorResponses,
  },
});

// GET /:contractAddress/:tokenId -> getMetadata
export const getOldMetadataRoute = createRoute({
  method: 'get',
  path: '/{nftId}/{tokenId}',
  operationId: 'getMetadataOld',
  summary: 'Get metadata',
  tags: [ApiCategoryTag.NFT, ApiAccessLevelTag.GUEST],
  security: [],
  request: {
    params: getMetadataParamsSchema,
  },
  responses: {
    200: generalMetadataResponseBody,
    ...commonErrorResponses,
  },
});
