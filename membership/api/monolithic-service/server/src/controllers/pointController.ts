import { Context } from 'hono';
import { inject, injectable } from 'tsyringe';
import { RewardPointService } from '../services/rewardPointService';
import { StatusPointService } from '../services/statusPointService';

@injectable()
export class PointController {
  constructor(
    @inject('RewardPointService') private rewardPointService: RewardPointService,
    @inject('StatusPointService') private statusPointService: StatusPointService,
  ) {}

  async expireRewardPoints(c: Context) {
    const response = await this.rewardPointService.expireRewardPoints();
    return c.json(response, 200);
  }

  async getTotalRewardPoints(c: Context) {
    const serviceId = c.req.header('service-id-header') as string;
    const accountId = c.req.param('accountId');
    const totalPoints = await this.rewardPointService.getTotalRewardPoints(serviceId, accountId);
    return c.json({ total_points: totalPoints }, 200);
  }

  async getTotalStatusPoints(c: Context) {
    const serviceId = c.req.header('service-id-header') as string;
    const accountId = c.req.param('accountId');
    const totalPoints = await this.statusPointService.getTotalStatusPoints(serviceId, accountId);
    return c.json({ total_points: totalPoints }, 200);
  }
}
