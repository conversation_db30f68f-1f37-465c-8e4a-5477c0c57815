// nftController.ts
import { Context } from 'hono';
import { inject, injectable } from 'tsyringe';
import { SerialCodeRedeemRequest } from '../dtos/accounts/schemas';
import { SerialCodeCreateRequest, SerialCodeImportRequest } from '../dtos/serial_codes/schemas';
import { LanguageCode } from '../enum/languageCode';
import { SerialCodeService } from '../services/serialCodeService';
import { logger } from '../utils/middleware/loggerMiddleware';
import { parseCustomToken } from '../utils/middleware/firebaseAuthAccessMiddleware';

@injectable()
export class SerialCodeController {
  constructor(@inject('SerialCodeService') private serialCodeService: SerialCodeService) {}

  async import(c: Context) {
    const serviceId = c.req.header('Service-Id-Header') as string;
    const requestBody = await c.req.json<SerialCodeImportRequest>();

    const result = await this.serialCodeService.import(
      serviceId,
      requestBody.slug,
      requestBody.serialCodeTranslations,
      requestBody.serialCodes,
      requestBody.rewardId,
      requestBody.hashKey,
      new Date(requestBody.startAt),
      new Date(requestBody.endAt),
    );

    return c.json(result, 200);
  }

  async create(c: Context) {
    logger.info(await c.req.json());
    const serviceId = c.req.header('Service-Id-Header') as string;
    const requestBody = await c.req.json<SerialCodeCreateRequest>();

    const result = await this.serialCodeService.create(
      serviceId,
      requestBody.slug,
      requestBody.serialCodeTranslations,
      requestBody.codeProcedures,
      requestBody.rewardId,
      new Date(requestBody.startAt),
      new Date(requestBody.endAt),
    );

    return c.json(result, 200);
  }

  async redeem(c: Context) {
    const custom = parseCustomToken(c);
    const serviceId = custom!.service_id!;
    const accountId = custom!.account_id!;

    const requestBody = await c.req.json<SerialCodeRedeemRequest>();

    const result = await this.serialCodeService.redeemAndClaimReward(
      serviceId,
      accountId,
      requestBody.serialCode,
      requestBody.serialCodeProjectId,
    );

    return c.json(result, 200);
  }

  async fetchProjects(c: Context) {
    const custom = parseCustomToken(c);
    const serviceId = custom!.service_id!;

    const lang = c.get('language') as LanguageCode;

    const result = await this.serialCodeService.fetchAvailableProjects(serviceId, lang);
    return c.json(result, 200);
  }
}
