import { Context } from 'hono';
import { inject, injectable } from 'tsyringe';
import { ActionService } from '../services/actionService';
import { ActionComplete } from '../dtos/accounts/schemas';
import { LanguageCode } from '../enum/languageCode';
import { parseCustomToken } from '../utils/middleware/firebaseAuthAccessMiddleware';

@injectable()
export class ActionController {
  constructor(@inject('ActionService') private actionService: ActionService) {}

  async getAction(c: Context) {
    // const serviceId = c.req.header('Service-Id-Header') as string;
    const actionId = c.req.param('actionId');
    const custom = parseCustomToken(c);
    const serviceId = custom!.service_id!;
    const lang = c.get('language') as LanguageCode;

    const action = await this.actionService.getAction(actionId, serviceId, lang);

    return c.json(action, 200);
  }

  async completeAction(c: Context) {
    // const serviceId = c.req.header('Service-Id-Header') as string;
    // const accountId = c.req.param('accountId');
    const custom = parseCustomToken(c);
    const serviceId = custom!.service_id!;
    const accountId = custom!.account_id!;
    const actionId = c.req.param('actionId');
    const data = await c.req.json<ActionComplete>();
    const lang = c.get('language') as LanguageCode;

    await this.actionService.completeAction(accountId, actionId, serviceId, data, lang);

    return c.json({}, 200);
  }
}
