import { Context } from 'hono';
import { inject, injectable } from 'tsyringe';
import { AccountService } from '../services/accountService';
import { OtpService } from '../services/otpService';
import { LanguageCode } from '../enum/languageCode';
import { AccountCustomFieldService } from '../services/accountCustomFieldService';
import {
  AccountCustomFieldsUpdateRequest,
  GenerateEmailOtpRequest,
  ValidateEmailOtpRequest,
} from '../dtos/accounts/schemas';
import { parseCustomToken } from '../utils/middleware/firebaseAuthAccessMiddleware';

@injectable()
export class AccountController {
  constructor(
    @inject('AccountService') private accountService: AccountService,
    @inject('AccountCustomFieldService') private accountCustomFieldService: AccountCustomFieldService,
    @inject('OtpService') private otpService: OtpService,
  ) {}

  /**
   * POST /accounts/
   * Request Headers:
   *   - Authorization or X-Forwarded-Authorization: Bearer {firebase_token}
   *   - line-id-token-header: string
   *   - service-id-header: string
   * Response: AccountResponseSchema (200)
   */
  async createAccount(c: Context) {
    const authHeader = c.req.header('X-Forwarded-Authorization') ?? c.req.header('Authorization');
    const firebaseIdToken = authHeader?.startsWith('Bearer ') ? authHeader.slice(7) : '';
    const lineIdTokenHeader = c.req.header('line-id-token-header') as string;
    const serviceIdHeader = c.req.header('service-id-header') as string;

    const response = await this.accountService.createAccount(
      firebaseIdToken,
      lineIdTokenHeader,
      serviceIdHeader,
    );

    return c.json(response, 200);
  }

  /**
   * GET /accounts/{accountId}
   * Path Parameters:
   *   - accountId: string
   * Request Headers:
   *   - service-id-header: string
   * Response: AccountResponseSchema (200)
   */
  async getAccount(c: Context) {
    const custom = parseCustomToken(c);
    const serviceId = custom!.service_id!;
    const accountId = custom!.account_id!;

    const response = await this.accountService.getAccount(accountId, serviceId);

    return c.json(response, 200);
  }

  /**
   * GET /accounts/{accountId}/status
   * Path Parameters:
   *   - accountId: string
   * Request Headers:
   *   - service-id-header: string
   * Response: AccountStatusResponseSchema (200)
   */
  async getAccountStatus(c: Context) {
    const custom = parseCustomToken(c);
    const serviceId = custom!.service_id!;
    const accountId = custom!.account_id!;

    const accountStatusResponse = await this.accountService.getAccountStatus(accountId, serviceId);

    return c.json(accountStatusResponse, 200);
  }

  /**
   * DELETE /accounts/{accountId}
   * Path Parameters:
   *   - accountId: string
   * Request Headers:
   *   - service-id-header: string
   * Response: AccountDeleteResponseSchema (200)
   */
  async delete(c: Context) {
    const custom = parseCustomToken(c);

    const serviceId = custom!.service_id!;
    const accountId = custom!.account_id!;

    await this.accountService.deleteAccount(accountId, serviceId);
    return c.text('', 200);
  }

  /**
   * PUT /accounts/{accountId}/line-profile
   * Path Parameters:
   *   - accountId: string
   * Request Headers:
   *   - service-id-header: string
   *   - line-id-token-header: string
   * Response: AccountResponseSchema (200)
   */
  async updateUserLineProfile(c: Context) {
    const custom = parseCustomToken(c);
    const serviceId = custom!.service_id!;
    const accountId = custom!.account_id!;

    const lineIdTokenHeader = c.req.header('line-id-token-header') as string;

    const response = await this.accountService.updateUserLineProfile(accountId, lineIdTokenHeader, serviceId);

    return c.json(response, 200);
  }

  /**
   * GET /accounts/{accountId}/activity-history
   * Path Parameters:
   *   - accountId: string
   * Request Headers:
   *   - service-id-header: string
   * Context Variables:
   *   - language: LanguageCode (from language middleware)
   * Response: ActivityHistoriesResponseSchema (200)
   */
  async getAccountActivityHistory(c: Context) {
    const custom = parseCustomToken(c);
    const serviceId = custom!.service_id!;
    const accountId = custom!.account_id!;
    const lang = c.get('language') as LanguageCode;

    const response = await this.accountService.getAccountActivityHistory(accountId, serviceId, lang);

    return c.json(response, 200);
  }

  /**
   * GET /accounts/{accountId}/notifications
   * Path Parameters:
   *   - accountId: string
   * Request Headers:
   *   - service-id-header: string
   * Context Variables:
   *   - language: LanguageCode (from language middleware)
   * Response: NotificationListResponseSchema (200)
   */
  async getAccountNotifications(c: Context) {
    const custom = parseCustomToken(c);
    const serviceId = custom!.service_id!;
    const accountId = custom!.account_id!;
    const lang = c.get('language') as LanguageCode;

    const response = await this.accountService.getAccountNotifications(accountId, serviceId, lang);

    return c.json(response, 200);
  }

  /**
   * PUT /accounts/{accountId}/last-login
   * Path Parameters:
   *   - accountId: string
   * Request Headers:
   *   - service-id-header: string
   * Response: Empty text response (200)
   */
  async updateLastLogin(c: Context) {
    const custom = parseCustomToken(c);
    const serviceId = custom!.service_id!;
    const accountId = custom!.account_id!;
    await this.accountService.updateLastLogin(accountId, serviceId);

    return c.text('', 200);
  }

  /**
   * GET /accounts/{accountId}/custom-fields
   * Path Parameters:
   *   - accountId: string
   * Request Headers:
   *   - service-id-header: string
   * Context Variables:
   *   - language: LanguageCode (from language middleware)
   * Response: CustomFieldResponseSchema (200)
   */
  async getAccountCustomField(c: Context) {
    const custom = parseCustomToken(c);
    const serviceId = custom!.service_id!;
    const accountId = custom!.account_id!;
    const lang = c.get('language') as LanguageCode;

    const response = await this.accountCustomFieldService.getAccountCustomField(accountId, serviceId, lang);

    return c.json(response, 200);
  }

  /**
   * PUT /accounts/{accountId}/custom-fields
   * Path Parameters:
   *   - accountId: string
   * Request Headers:
   *   - service-id-header: string
   * Request Body: AccountCustomFieldsUpdateRequest
   * Context Variables:
   *   - language: LanguageCode (from language middleware)
   * Response: CustomFieldResponseSchema (200)
   */
  async updateAccountCustomField(c: Context) {
    const custom = parseCustomToken(c);
    const serviceId = custom!.service_id!;
    const accountId = custom!.account_id!;
    const lang = c.get('language') as LanguageCode;
    const customFields = await c.req.json<AccountCustomFieldsUpdateRequest>();

    const response = await this.accountCustomFieldService.updateAccountCustomField(
      accountId,
      serviceId,
      customFields,
      lang,
    );

    return c.json(response, 200);
  }

  /**
   * POST /accounts/{accountId}/email-otp/generate
   * Path Parameters:
   *   - accountId: string
   * Request Headers:
   *   - service-id-header: string
   * Request Body: GenerateEmailOtpRequest
   * Context Variables:
   *   - language: LanguageCode (from language middleware)
   * Response: EmailOtpGenerateResponseSchema (200)
   */
  async generateEmailOtp(c: Context) {
    const accountId = c.req.param('accountId');
    const serviceId = c.req.header('service-id-header') as string;
    const lang = c.get('language') as LanguageCode;
    const req = await c.req.json<GenerateEmailOtpRequest>();

    const response = await this.otpService.generateEmailOtp(accountId, serviceId, lang, req.email);

    return c.json(response, 200);
  }

  /**
   * POST /accounts/{accountId}/email-otp/validate
   * Path Parameters:
   *   - accountId: string
   * Request Headers:
   *   - service-id-header: string
   * Request Body: ValidateEmailOtpRequest
   * Response: EmailOtpValidateResponseSchema (200)
   */
  async validateEmailOtp(c: Context) {
    const custom = parseCustomToken(c);
    const serviceId = custom!.service_id!;
    const accountId = custom!.account_id!;
    const req = await c.req.json<ValidateEmailOtpRequest>();

    const response = await this.otpService.verifyOtp(accountId, serviceId, req.email, req.otp);

    return c.json(response, 200);
  }
}
