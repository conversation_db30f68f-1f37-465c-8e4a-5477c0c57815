import { Context } from 'hono';
import { inject, injectable } from 'tsyringe';
import { LanguageCode } from '../enum/languageCode';
import { AccountCustomFieldService } from '../services/accountCustomFieldService';
import { AccountCustomFieldsUpdateRequest } from '../dtos/accounts/schemas';
import { AccountService } from '../services/accountService';

@injectable()
export class AccountController {
  constructor(
    @inject('AccountService') private accountService: AccountService,
    @inject('AccountCustomFieldService') private accountCustomFieldService: AccountCustomFieldService,
  ) { }

  async createAccount(c: Context) {
    const authHeader = c.req.header('X-Forwarded-Authorization') ?? c.req.header('Authorization');
    const firebaseIdToken = authHeader?.startsWith('Bearer ') ? authHeader.slice(7) : '';
    const lineIdTokenHeader = c.req.header('line-id-token-header') as string;
    const serviceIdHeader = c.req.header('service-id-header') as string;

    const accountResponse = await this.accountService.createAccount(
      firebaseIdToken,
      lineIdTokenHeader,
      serviceIdHeader
    );

    return c.json(accountResponse, 200);
  }

  async getAccount(c: Context) {
    const serviceIdHeader = c.req.header('service-id-header') as string;
    const accountId = c.req.param('accountId');

    const accountResponse = await this.accountService.getAccount(accountId, serviceIdHeader);

    return c.json(accountResponse, 200);
  }

  async getAccountStatus(c: Context) {
    const serviceIdHeader = c.req.header('service-id-header') as string;
    const accountId = c.req.param('accountId');

    const accountStatusResponse = await this.accountService.getAccountStatus(accountId, serviceIdHeader);

    return c.json(accountStatusResponse, 200);
  }

  async delete(c: Context) {
    const id = c.req.param('accountId');
    const serviceIdHeader = c.req.header('service-id-header') as string;

    await this.accountService.deleteAccount(id, serviceIdHeader);
    return c.text('', 200);
  }

  async updateUserLineProfile(c: Context) {
    const accountId = c.req.param('accountId');
    const lineIdTokenHeader = c.req.header('line-id-token-header') as string;
    const serviceIdHeader = c.req.header('service-id-header') as string;

    const accountResponse = await this.accountService.updateUserLineProfile(
      accountId,
      lineIdTokenHeader,
      serviceIdHeader,
    );

    return c.json(accountResponse, 200);
  }

  async getAccountActivityHistory(c: Context) {
    const serviceIdHeader = c.req.header('service-id-header') as string;
    const accountId = c.req.param('accountId');
    const lang = c.get('language') as LanguageCode;

    const accountStatusResponse = await this.accountService.getAccountActivityHistory(accountId, serviceIdHeader, lang);

    return c.json(accountStatusResponse, 200);
  }

  async getAccountNotifications(c: Context) {
    const serviceIdHeader = c.req.header('service-id-header') as string;
    const accountId = c.req.param('accountId');
    const lang = c.get('language') as LanguageCode;

    const accountStatusResponse = await this.accountService.getAccountNotifications(accountId, serviceIdHeader, lang);

    return c.json(accountStatusResponse, 200);
  }

  async getAccountCustomField(c: Context) {
    const serviceIdHeader = c.req.header('service-id-header') as string;
    const lang = c.get('language') as LanguageCode;
    const accountId = c.req.param('accountId');

    const accountCustomFields = await this.accountCustomFieldService.getAccountCustomField(accountId, serviceIdHeader, lang);

    return c.json(accountCustomFields, 200);
  }

  async updateAccountCustomField(c: Context) {
    const serviceIdHeader = c.req.header('service-id-header') as string;
    const accountId = c.req.param('accountId');
    const lang = c.get('language') as LanguageCode;
    const customFields = await c.req.json<AccountCustomFieldsUpdateRequest>();

    const updatedFields = await this.accountCustomFieldService.updateAccountCustomField(accountId, serviceIdHeader, customFields, lang);

    return c.json(updatedFields, 200);
  }

  async updateLastLogin(c: Context) {
    const serviceIdHeader = c.req.header('service-id-header') as string;
    const accountId = c.req.param('accountId');

    await this.accountService.updateLastLogin(accountId, serviceIdHeader);
    return c.text('', 200);
  }
}
