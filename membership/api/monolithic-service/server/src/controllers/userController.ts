import { Context } from 'hono';
import { inject, injectable } from 'tsyringe';
import { UserService } from '../services/userService';
import { ContractAccountAddress, CreateUser, PhoneNumber, RecoveryShare } from '../dtos/users/schemas';
import { parseCustomToken } from '../utils/middleware/firebaseAuthAccessMiddleware';

@injectable()
export class UserController {
  constructor(@inject('UserService') private userService: UserService) {}

  /**
   * POST /users
   * Request Body: CreateUser
   *   - userId: string (User identifier, required)
   *   - countryCode: string (ISO 3166-1 alpha-2, required)
   *   - phoneNumber: string (E.164 format, required)
   *   - mnemonicBackupKey: string (Hex encoded AES GSM 256 key, required)
   * Response: CreatedUser (201)
   *   - userId: string
   *   - phone: PhoneNumber
   *   - contractAccountAddress: string (optional)
   *   - account: {serviceId: string, accountId?: string}
   */
  async create(c: Context) {
    const userRequest = await c.req.json<CreateUser>();
    const userResponse = await this.userService.createUser(userRequest);

    return c.json(userResponse, 201);
  }

  /**
   * GET /users/{userId}
   * Path Parameters:
   *   - userId: string (User identifier, required)
   * Request Headers:
   *   - service-id-header: string (Service UUID, required)
   * Response: User (200)
   *   - userId: string
   *   - phone: PhoneNumber
   *   - contractAccountAddress: string (optional)
   *   - account: {serviceId: string, accountId?: string}
   */
  async getSanitized(c: Context) {
    const userId = c.req.param('userId');
    const serviceIdHeader = c.req.header('service-id-header') as string;

    const userResponse = await this.userService.getSanitizedUserWithAccount(userId, serviceIdHeader);
    return c.json(userResponse, 200);
  }

  /**
   * DELETE /users/{userId}
   * Path Parameters:
   *   - userId: string (User identifier, required)
   * Response: Empty (200)
   */
  async delete(c: Context) {
    const userId = c.req.param('userId');

    await this.userService.deleteUser(userId);
    return c.text('', 200);
  }

  /**
   * PUT /users/{userId}/phone-number
   * Path Parameters:
   *   - userId: string (User identifier, required)
   * Request Body: PhoneNumber
   *   - countryCode: string (ISO 3166-1 alpha-2, required)
   *   - phoneNumber: string (E.164 format, required)
   * Response: Empty (200)
   */
  async updatePhoneNumber(c: Context) {
    const userId = c.req.param('userId');
    const { countryCode, phoneNumber } = await c.req.json<PhoneNumber>();

    await this.userService.updateUserPhoneNumber(userId, countryCode, phoneNumber);
    return c.text('', 200);
  }

  /**
   * PUT /users/{userId}/contract-account
   * Path Parameters:
   *   - userId: string (User identifier, required)
   * Request Body: ContractAccountAddress
   *   - contractAccountAddress: string (EVM checksum address, required)
   * Response: Empty (200)
   */
  async updateContractAccount(c: Context) {
    const userId = c.req.param('userId');
    const { contractAccountAddress } = await c.req.json<ContractAccountAddress>();

    await this.userService.updateUserContractAccount(userId, contractAccountAddress);
    return c.text('', 200);
  }

  /**
   * GET /users/{userId}/backup-key
   * Path Parameters:
   *   - userId: string (User identifier, required)
   * Response: BackupKey (200)
   *   - mnemonicBackupKey: string (Hex encoded backup key)
   */
  async getBackupKey(c: Context) {
    const userId = c.req.param('userId');

    const backupKeyResponse = await this.userService.getBackupKey(userId);
    return c.json(backupKeyResponse, 200);
  }

  /**
   * GET /users/{userId}/check
   * Path Parameters:
   *   - userId: string (User identifier, required)
   * Response: UserCheck (200)
   *   - isUserExist: boolean
   */
  async checkUser(c: Context) {
    const userId = c.req.param('userId');

    const userCheckResponse = await this.userService.checkUser(userId);
    return c.json(userCheckResponse, 200);
  }

  /**
   * GET /users/{userId}/recovery-share
   * Request Headers:
   *   - custom-token: string (Firebase custom token, required)
   * Response: RecoveryShare (200)
   *   - shareIndex: number
   *   - share: string
   *   - polynomialID: string
   */
  async getRecoveryShare(c: Context) {
    const custom = parseCustomToken(c);
    const userId = custom!.uid;

    const recoveryResponse = await this.userService.getRecoveryShare(userId);
    return c.json(recoveryResponse, 200);
  }

  /**
   * POST /users/{userId}/recovery-share
   * Request Headers:
   *   - custom-token: string (Firebase custom token, required)
   * Request Body: RecoveryShare
   *   - shareIndex: number
   *   - share: string
   *   - polynomialID: string
   * Response: Empty (200)
   */
  async storeRecoveryShare(c: Context) {
    const custom = parseCustomToken(c);
    const userId = custom!.uid;
    const recoveryShareRequest = await c.req.json<RecoveryShare>();
    await this.userService.storeRecoveryShare(userId, recoveryShareRequest);
    return c.json({}, 200);
  }
}
