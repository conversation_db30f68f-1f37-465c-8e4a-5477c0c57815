import { Context } from 'hono';
import { inject, injectable } from 'tsyringe';
import { AuthService } from '../services/authService';

@injectable()
export class AuthController {
  constructor(@inject('AuthService') private authService: AuthService) {}

  /**
   * POST /auth/custom-token
   * Request Headers:
   *   - line-id-token-header: string (LINE ID token, required)
   *   - service-id-header: string (Service UUID, required)
   *   - Authorization: string (Firebase ID token Bearer, optional)
   *   - X-Forwarded-Authorization: string (Alternative Firebase ID token Bearer, optional)
   * Response: CustomToken (200)
   *   - token: string (JWT for custom authentication, optional)
   */
  async linkLineIdToken(c: Context) {
    const authHeader = (c.req.header('X-Forwarded-Authorization') as string) ?? c.req.header('Authorization');
    const firebaseIdToken = authHeader?.startsWith('Bearer ') ? authHeader.slice(7) : '';
    const lineIdToken = c.req.header('Line-Id-Token-Header') as string;
    const serviceId = c.req.header('Service-Id-Header') as string;

    const token = await this.authService.linkLineIdToken(firebaseIdToken, lineIdToken, serviceId);

    return c.json(token, 200);
  }

  /**
   * GET /auth/custom-token
   * Request Headers:
   *   - line-id-token-header: string (LINE ID token, required)
   *   - service-id-header: string (Service UUID, required)
   * Response: CustomToken (200)
   *   - token: string (JWT for custom authentication, optional)
   */
  async getCustomToken(c: Context) {
    const lineIdToken = c.req.header('Line-Id-Token-Header') as string;
    const serviceId = c.req.header('Service-Id-Header') as string;

    const token = await this.authService.getCustomToken(lineIdToken, serviceId);

    return c.json(token, 200);
  }
}
