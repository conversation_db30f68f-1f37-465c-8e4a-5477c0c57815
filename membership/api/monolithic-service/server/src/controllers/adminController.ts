import { Context } from 'hono';
import { inject, injectable } from 'tsyringe';
import { AdminService } from '../services/adminService';
import { UpdateMetadataRequest } from '../dtos/admins/schemas';
import { NotFoundError } from '../errors/notFoundError';
import { logger } from '../utils/logger';
import { AdjustPointRequest } from '../dtos/admins/schemas';

@injectable()
export class AdminController {
  constructor(@inject('AdminService') private adminService: AdminService) {}

  async updateNftMetadata(c: Context) {
    const { contractAddress, tokenId } = await c.req.json<UpdateMetadataRequest>();
    try {
      const response = await this.adminService.updateFirestoreMetadata(contractAddress, tokenId);
      return c.json(response, 200);
    } catch (error) {
      if (error instanceof NotFoundError) {
        return c.json({ code: 'NOT_FOUND', message: 'Resource not found', status: 404 }, 404);
      } else {
        logger.error(error);
        return c.json({ code: 'INTERNAL_SERVER_ERROR', message: 'Internal server error', status: 500 }, 500);
      }
    }
  }

  async adjustPoint(c: Context) {
    const serviceId = c.req.header('Service-Id-Header') as string;
    const requestBody = await c.req.json<AdjustPointRequest>();

    const response = await this.adminService.adjustPoint(serviceId, requestBody);
    return c.json(response, 201);
  }
}
