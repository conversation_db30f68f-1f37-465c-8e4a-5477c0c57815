import { Context } from 'hono';
import { inject, injectable } from 'tsyringe';
import { UpdateMetadataRequest } from '../dtos/admins/schemas';
import { NotFoundError } from '../errors/notFoundError';
import { AdminService } from '../services/adminService';
import { logger } from '../utils/middleware/loggerMiddleware';
import { AdjustPointRequest } from '../dtos/admins/schemas';

@injectable()
export class AdminController {
  constructor(@inject('AdminService') private adminService: AdminService) {}

  /**
   * POST /admin/firestore/metadata/update
   * Request Headers:
   *   - X-Admin-API-Key: string (API Key for admin authorization)
   * Request Body: UpdateMetadataRequest
   *   - contractAddress: string (Ethereum address, required)
   *   - tokenId: string[] (NFT token IDs, optional)
   *   - chainId: number (Chain ID, required)
   * Response: UpdateMetadataResponse (200)
   *   - updatedTokenUris: Array<{tokenId: string, tokenUri: string}>
   *   - failedIds: Array<{tokenId: string, errorMsg?: string}> (optional)
   * Error Responses:
   *   - 404: NotFoundError (Resource not found)
   *   - 500: InternalServerError
   */
  async updateNftMetadata(c: Context) {
    const { contractAddress, tokenId } = await c.req.json<UpdateMetadataRequest>();
    try {
      const response = await this.adminService.updateFirestoreMetadata(contractAddress, tokenId);
      return c.json(response, 200);
    } catch (error) {
      if (error instanceof NotFoundError) {
        return c.json({ code: 'NOT_FOUND', message: 'Resource not found', status: 404 }, 404);
      } else {
        logger.error(error);
        return c.json({ code: 'INTERNAL_SERVER_ERROR', message: 'Internal server error', status: 500 }, 500);
      }
    }
  }

  async adjustPoint(c: Context) {
    const serviceId = c.req.header('Service-Id-Header') as string;
    const requestBody = await c.req.json<AdjustPointRequest>();

    const response = await this.adminService.adjustPoint(serviceId, requestBody);
    return c.json(response, 201);
  }
}
