import { Context } from 'hono';
import { inject, injectable } from 'tsyringe';
import { QuestionnaireCreateRequest, QuestionnaireUpdateRequest } from '../dtos/questionnaire/schemas';
import { LanguageCode } from '../enum/languageCode';
import { QuestionnaireService } from '../services/questionnaireService';
import { parseCustomToken } from '../utils/middleware/firebaseAuthAccessMiddleware';

@injectable()
export class QuestionnaireController {
  constructor(
    @inject('QuestionnaireService')
    private questionnaireService: QuestionnaireService,
  ) {}

  async getQuestionnaire(c: Context) {
    const custom = parseCustomToken(c);
    const serviceId = custom!.service_id!;

    const questionnaireId = c.req.param('questionnaireId');
    const lang = c.get('language') as LanguageCode;

    const result = await this.questionnaireService.getQuestionnaire(serviceId, questionnaireId, lang);
    return c.json(result, 200);
  }

  async createQuestionnaire(c: Context) {
    const serviceId = c.req.header('Service-Id-Header') as string;
    const requestBody = await c.req.json<QuestionnaireCreateRequest>();

    const result = await this.questionnaireService.createQuestionnaire(serviceId, requestBody);

    return c.json(result, 200);
  }

  async updateQuestionnaire(c: Context) {
    const serviceId = c.req.header('Service-Id-Header') as string;
    const questionnaireId = c.req.param('questionnaireId');
    const requestBody = await c.req.json<QuestionnaireUpdateRequest>();

    const result = await this.questionnaireService.updateQuestionnaire(serviceId, questionnaireId, requestBody);

    return c.json(result, 200);
  }
}
