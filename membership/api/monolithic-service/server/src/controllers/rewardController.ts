import { Context } from 'hono';
import { inject, injectable } from 'tsyringe';
import { RewardService } from '../services/rewardService';
import { LanguageCode } from '../enum/languageCode';
import { parseCustomToken } from '../utils/middleware/firebaseAuthAccessMiddleware';

@injectable()
export class RewardController {
  constructor(@inject('RewardService') private rewardService: RewardService) {}

  async getReward(c: Context) {
    const custom = parseCustomToken(c);
    const serviceId = custom!.service_id!;

    const rewardId = c.req.param('rewardId');
    const lang = c.get('language') as LanguageCode;
    const reward = await this.rewardService.getReward(rewardId, serviceId, lang);

    return c.json(reward, 200);
  }

  async claimReward(c: Context) {
    const custom = parseCustomToken(c);
    const serviceId = custom!.service_id!;
    const accountId = custom!.account_id!;

    const rewardId = c.req.param('rewardId');

    const claimReward = await this.rewardService.claimReward(accountId, rewardId, serviceId);
    return c.json(claimReward, 200);
  }
}
