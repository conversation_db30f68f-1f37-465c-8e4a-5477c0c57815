import { Context } from 'hono';
import { inject, injectable } from 'tsyringe';
import { UserOperationService } from '../services/userOperationService';
import { ConsumptionPrepare, UserOperationResult } from '../dtos/user_operations/schemas';
import { parseCustomToken } from '../utils/middleware/firebaseAuthAccessMiddleware';

@injectable()
export class UserOperationController {
  constructor(@inject('UserOperationService') private userOperationService: UserOperationService) {}

  async storeUserOperationData(c: Context) {
    const custom = parseCustomToken(c);
    const serviceId = custom!.service_id!;
    const accountId = custom!.account_id!;

    const data = await c.req.json<UserOperationResult>();

    const operationId = await this.userOperationService.storeUserOperationInfo(serviceId, accountId, data.uoHash);

    return c.json(operationId, 200);
  }

  async prepareUserOperation(c: Context) {
    const custom = parseCustomToken(c);
    const serviceId = custom!.service_id!;

    const data = await c.req.json<ConsumptionPrepare>();

    const callData = await this.userOperationService.prepareForConsumption(
      serviceId,
      data.contractAddress,
      data.tokenId,
    );

    return c.json(callData, 200);
  }
}
