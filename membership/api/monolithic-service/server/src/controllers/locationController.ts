import { Context } from 'hono';
import { GeofenceService } from '../services/geofenceService';
import {
  PostLocationGeofenceRequest,
  PostLocationGeofenceResponse,
  PutLocationGeofenceRequest,
  PutLocationGeofenceResponse,
  GetLocationGeofenceResponse,
  GetLocationGeofencesResponse,
  PathParameterGeofenceId,
} from '../dtos/locations/schemas';
import { HTTPException } from 'hono/http-exception';

export class GeofenceController {
  constructor(private geofenceService: GeofenceService) { }

  /**
   * Create a new geofence
   */
  async createGeofence(c: Context): Promise<PostLocationGeofenceResponse> {
    try {
      const serviceId = c.req.header('service-id-header');
      if (!serviceId) {
        throw new HTTPException(400, { message: 'Service ID header is required' });
      }

      const requestBody = await c.req.json() as PostLocationGeofenceRequest;

      // Validate geofence data based on type
      this.validateGeofenceData(requestBody);

      const geofence = await this.geofenceService.createGeofence(serviceId, requestBody);

      return {
        geofenceId: geofence.geofenceId,
        name: geofence.name,
        geofence: geofence.geofence
      };
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error;
      }
      console.error('Error creating geofence:', error);
      throw new HTTPException(500, { message: 'Failed to create geofence' });
    }
  }

  /**
   * Update an existing geofence
   */
  async updateGeofence(c: Context): Promise<PutLocationGeofenceResponse> {
    try {
      const serviceId = c.req.header('service-id-header');
      if (!serviceId) {
        throw new HTTPException(400, { message: 'Service ID header is required' });
      }

      const { geofenceId } = c.req.param() as PathParameterGeofenceId;
      const requestBody = await c.req.json() as PutLocationGeofenceRequest;

      // Validate geofence data based on type
      this.validateGeofenceData(requestBody);

      const geofence = await this.geofenceService.updateGeofence(serviceId, geofenceId, requestBody);

      if (!geofence) {
        throw new HTTPException(404, { message: 'Geofence not found' });
      }

      return {
        geofenceId: geofence.geofenceId,
        name: geofence.name,
        geofence: geofence.geofence
      };
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error;
      }
      console.error('Error updating geofence:', error);
      throw new HTTPException(500, { message: 'Failed to update geofence' });
    }
  }

  /**
   * Get all geofences for a service
   */
  async getGeofences(c: Context): Promise<GetLocationGeofencesResponse> {
    try {
      const serviceId = c.req.header('service-id-header');
      if (!serviceId) {
        throw new HTTPException(400, { message: 'Service ID header is required' });
      }

      const query = c.req.query();
      const filters = {
        page: query.page ? parseInt(query.page) : undefined,
        limit: query.limit ? parseInt(query.limit) : undefined,
        geofenceType: query.geofenceType as 'CIRCLE' | 'POLYGON' | undefined,
        search: query.search,
      };

      const geofences = await this.geofenceService.getGeofences(serviceId, filters);

      return {
        geofences: geofences.map(geofence => ({
          geofenceId: geofence.geofenceId,
          name: geofence.name,
          geofence: geofence.geofence
        }))
      };
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error;
      }
      console.error('Error getting geofences:', error);
      throw new HTTPException(500, { message: 'Failed to get geofences' });
    }
  }

  /**
   * Get a specific geofence by ID
   */
  async getGeofence(c: Context): Promise<GetLocationGeofenceResponse> {
    try {
      const serviceId = c.req.header('service-id-header');
      if (!serviceId) {
        throw new HTTPException(400, { message: 'Service ID header is required' });
      }

      const { geofenceId } = c.req.param() as PathParameterGeofenceId;

      const geofence = await this.geofenceService.getGeofenceById(serviceId, geofenceId);

      if (!geofence) {
        throw new HTTPException(404, { message: 'Geofence not found' });
      }

      return {
        geofenceId: geofence.geofenceId,
        name: geofence.name,
        geofence: geofence.geofence
      };
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error;
      }
      console.error('Error getting geofence:', error);
      throw new HTTPException(500, { message: 'Failed to get geofence' });
    }
  }

  /**
   * Delete a geofence
   */
  async deleteGeofence(c: Context): Promise<void> {
    try {
      const serviceId = c.req.header('service-id-header');
      if (!serviceId) {
        throw new HTTPException(400, { message: 'Service ID header is required' });
      }

      const { geofenceId } = c.req.param() as PathParameterGeofenceId;

      const deleted = await this.geofenceService.deleteGeofence(serviceId, geofenceId);

      if (!deleted) {
        throw new HTTPException(404, { message: 'Geofence not found' });
      }

      // Return 204 No Content
      c.status(204);
    } catch (error) {
      if (error instanceof HTTPException) {
        throw error;
      }
      console.error('Error deleting geofence:', error);
      throw new HTTPException(500, { message: 'Failed to delete geofence' });
    }
  }

  /**
   * Validate geofence data based on type
   */
  private validateGeofenceData(data: PostLocationGeofenceRequest | PutLocationGeofenceRequest): void {
    const { geofence } = data;

    if (geofence.geofenceType === 'CIRCLE') {
      if (!geofence.center || !geofence.radiusMeters) {
        throw new HTTPException(400, {
          message: 'Circle geofence requires center coordinates and radius'
        });
      }

      if (geofence.radiusMeters <= 0) {
        throw new HTTPException(400, {
          message: 'Radius must be greater than 0'
        });
      }
    } else if (geofence.geofenceType === 'POLYGON') {
      if (!geofence.coordinates || geofence.coordinates.length < 3) {
        throw new HTTPException(400, {
          message: 'Polygon geofence requires at least 3 coordinate points'
        });
      }

      // Validate that first and last coordinates are the same (closed polygon)
      const first = geofence.coordinates[0];
      const last = geofence.coordinates[geofence.coordinates.length - 1];
      if (first.latitude !== last.latitude || first.longitude !== last.longitude) {
        throw new HTTPException(400, {
          message: 'Polygon must be closed (first and last coordinates must be the same)'
        });
      }
    }
  }
}
