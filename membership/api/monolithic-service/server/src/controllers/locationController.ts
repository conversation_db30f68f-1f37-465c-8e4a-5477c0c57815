import { Context } from 'hono';
import { inject, injectable } from 'tsyringe';
import { GeofenceService } from '../services/geofenceService';
import {
  LocationGeofence,
  LocationGeofenceId,
  GetLocationGeofences,
} from '../dtos/locations/schemas';
import { PathParameterGeofenceId } from '../dtos/locations/path';
import { HTTPException } from 'hono/http-exception';

@injectable()
export class LocationController {
  constructor(@inject('GeofenceService') private geofenceService: GeofenceService) {}

  async createGeofence(c: Context) {
    const serviceIdHeader = c.req.header('service-id-header') as string;
    const requestBody = await c.req.json() as LocationGeofence;

    const geofenceResponse = await this.geofenceService.createGeofence(serviceIdHeader, requestBody);
    return c.json(geofenceResponse, 201);
  }

  async updateGeofence(c: Context) {
    const serviceIdHeader = c.req.header('service-id-header') as string;
    const requestBody = await c.req.json() as LocationGeofence;
    const { geofenceId } = c.req.param() as PathParameterGeofenceId;

    const response = await this.geofenceService.updateGeofence(serviceIdHeader, geofenceId, requestBody);

    return c.json(response, 200);
  }

  async getGeofences(c: Context) {
    const serviceIdHeader = c.req.header('service-id-header') as string;
    const geofences = await this.geofenceService.getGeofences(serviceIdHeader);

    return c.json(geofences, 200);
  }

  async getGeofence(c: Context) {
    const serviceIdHeader = c.req.header('service-id-header') as string;
    const { geofenceId } = c.req.param() as PathParameterGeofenceId;

    const geofence = await this.geofenceService.getGeofenceById(serviceIdHeader, geofenceId);

    return c.json(geofence, 200);
  }
}
