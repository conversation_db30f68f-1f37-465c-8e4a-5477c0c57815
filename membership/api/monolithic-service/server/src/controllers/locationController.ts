import { Context } from 'hono';
import { inject, injectable } from 'tsyringe';
import { GeofenceService } from '../services/geofenceService';
import { PathParameterGeofenceId } from '../dtos/locations/path';
import { HTTPException } from 'hono/http-exception';

@injectable()
export class LocationController {
  constructor(@inject('GeofenceService') private geofenceService: GeofenceService) {}


  async createGeofence(c: Context) {
    const serviceIdHeader = c.req.header('service-id-header') as string;
    const requestBody = await c.req.json() as LocationGeofence;

    const geofenceResponse = await this.geofenceService.createGeofence(serviceId, requestBody);
    return c.json(geofenceResponse, 201);
  }

  async updateGeofence(c: Context) {
    const serviceIdHeader = c.req.header('service-id-header') as string;
    const requestBody = await c.req.json() as LocationGeofence;
    const { geofenceId } = c.req.param() as PathParameterGeofenceId;

    const response = await this.geofenceService.updateGeofence(serviceId, geofenceId, requestBody);

    return c.json(response, 200);
  }

  async getGeofences(c: Context) {
    const serviceIdHeader = c.req.header('service-id-header') as string;
    const geofences = await this.geofenceService.getGeofences(serviceId);

    return c.json(geofences, 200);
  }

  async getGeofence(c: Context) {
    const serviceIdHeader = c.req.header('service-id-header') as string;
    const { geofenceId } = c.req.param() as PathParameterGeofenceId;

    const geofence = await this.geofenceService.getGeofenceById(serviceId, geofenceId);

    return c.json(geofence, 200);
  }
}
