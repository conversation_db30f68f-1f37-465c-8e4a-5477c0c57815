import { Context } from 'hono';
import { inject, injectable } from 'tsyringe';
import { LocationService } from '../services/locationService';
import { 
  LocationCheckinRequest, 
  CreateGeofenceRequest,
  LocationCoordinates 
} from '../dtos/location/schemas';
import { logger } from '../utils/logger';

@injectable()
export class LocationController {
  constructor(
    @inject('LocationService') private locationService: LocationService
  ) {}

  // =======================
  // Geofence Management
  // =======================

  async createGeofence(c: Context) {
    try {
      const serviceId = c.req.header('service-id-header') as string;
      const geofenceData = await c.req.json<CreateGeofenceRequest>();

      logger.info({ method: 'createGeofence', serviceId, geofenceData });

      const geofence = await this.locationService.createGeofence(serviceId, geofenceData);
      return c.json(geofence, 201);
    } catch (error) {
      logger.error('Error creating geofence:', error);
      throw error;
    }
  }

  async getGeofences(c: Context) {
    try {
      const serviceId = c.req.header('service-id-header') as string;

      logger.info({ method: 'getGeofences', serviceId });

      const geofences = await this.locationService.getGeofencesByService(serviceId);
      return c.json(geofences, 200);
    } catch (error) {
      logger.error('Error getting geofences:', error);
      throw error;
    }
  }

  async getGeofence(c: Context) {
    try {
      const serviceId = c.req.header('service-id-header') as string;
      const geofenceId = c.req.param('geofenceId');

      logger.info({ method: 'getGeofence', serviceId, geofenceId });

      const geofence = await this.locationService.getGeofenceById(serviceId, geofenceId);
      return c.json(geofence, 200);
    } catch (error) {
      logger.error('Error getting geofence:', error);
      throw error;
    }
  }

  async updateGeofence(c: Context) {
    try {
      const serviceId = c.req.header('service-id-header') as string;
      const geofenceId = c.req.param('geofenceId');
      const updates = await c.req.json<Partial<CreateGeofenceRequest>>();

      logger.info({ method: 'updateGeofence', serviceId, geofenceId, updates });

      const geofence = await this.locationService.updateGeofence(serviceId, geofenceId, updates);
      return c.json(geofence, 200);
    } catch (error) {
      logger.error('Error updating geofence:', error);
      throw error;
    }
  }

  async deleteGeofence(c: Context) {
    try {
      const serviceId = c.req.header('service-id-header') as string;
      const geofenceId = c.req.param('geofenceId');

      logger.info({ method: 'deleteGeofence', serviceId, geofenceId });

      await this.locationService.deleteGeofence(serviceId, geofenceId);
      return c.text('', 204);
    } catch (error) {
      logger.error('Error deleting geofence:', error);
      throw error;
    }
  }

  // =======================
  // Location Check-in
  // =======================

  async performLocationCheckin(c: Context) {
    try {
      const serviceId = c.req.header('service-id-header') as string;
      const accountId = c.req.param('accountId');
      const checkinRequest = await c.req.json<LocationCheckinRequest>();

      logger.info({ 
        method: 'performLocationCheckin', 
        serviceId, 
        accountId, 
        actionId: checkinRequest.actionId,
        location: checkinRequest.location 
      });

      const result = await this.locationService.performLocationCheckin(
        serviceId,
        accountId,
        checkinRequest
      );

      return c.json(result, 200);
    } catch (error) {
      logger.error('Error performing location check-in:', error);
      throw error;
    }
  }

  async getLocationCheckinAttempts(c: Context) {
    try {
      const serviceId = c.req.header('service-id-header') as string;
      const accountId = c.req.param('accountId');
      const actionId = c.req.param('actionId');

      logger.info({ method: 'getLocationCheckinAttempts', serviceId, accountId, actionId });

      const attempts = await this.locationService.getLocationCheckinAttempts(accountId, actionId);
      return c.json(attempts, 200);
    } catch (error) {
      logger.error('Error getting location check-in attempts:', error);
      throw error;
    }
  }

  async checkLocationInGeofence(c: Context) {
    try {
      const serviceId = c.req.header('service-id-header') as string;
      const geofenceId = c.req.param('geofenceId');
      const { location } = await c.req.json<{ location: LocationCoordinates }>();

      logger.info({ 
        method: 'checkLocationInGeofence', 
        serviceId, 
        geofenceId, 
        location 
      });

      const result = await this.locationService.checkLocationInGeofence(
        serviceId,
        geofenceId,
        location
      );

      return c.json(result, 200);
    } catch (error) {
      logger.error('Error checking location in geofence:', error);
      throw error;
    }
  }
}
