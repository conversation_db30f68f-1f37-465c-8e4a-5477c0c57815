import { Context } from 'hono';
import { inject, injectable } from 'tsyringe';
import { ProductService } from '../services/productService';
import { logger } from '../utils/logger';
import { LineItem, SessionData } from '../dtos/products/schemas';
import { LanguageCode } from '../enum/languageCode';

@injectable()
export class ProductsController {
  constructor(@inject('ProductService') private productService: ProductService) {}

  async handleStripeEvents(c: Context) {
    const signature = c.req.header('Stripe-Signature');
    logger.info({ method: 'handleStripeEvents', signature: signature });
    const body = await c.req.text();
    await this.productService.handleStripeEvents(body, signature ?? '');
    return c.json('success', 200);
  }

  async getProducts(c: Context) {
    const serviceId = c.req.header('Service-Id-Header') as string;
    const accountId = c.req.param('accountId');
    const lang = c.get('language') as LanguageCode;
    const products = await this.productService.getProducts(serviceId, accountId, lang);
    return c.json(products, 200);
  }

  async createCheckoutSession(c: Context) {
    const serviceId = c.req.header('Service-Id-Header') as string;
    const accountId = c.req.param('accountId');

    const products = await c.req.json<LineItem>();
    const checkout = await this.productService.createCheckoutSession(serviceId, accountId, products);

    return c.json(checkout, 200);
  }

  async confirmCheckoutSessionStatus(c: Context) {
    const serviceId = c.req.header('Service-Id-Header') as string;
    const accountId = c.req.param('accountId');

    const session = await c.req.json<SessionData>();
    const status = await this.productService.confirmCheckoutStatus(serviceId, accountId, session.sessionId);

    return c.json(status, 200);
  }

  async expireCheckoutSessionStatus(c: Context) {
    const serviceId = c.req.header('Service-Id-Header') as string;
    const accountId = c.req.param('accountId');
    const session = await c.req.json<SessionData>();

    try {
      await this.productService.expireCheckoutSession(serviceId, accountId, session.sessionId);
    } catch (error) {
      logger.error({ api: 'expireCheckoutSessionStatus', error: error });
      throw error;
    }

    return c.body(null, 204);
  }
}
