import { Context } from 'hono';
import { inject, injectable } from 'tsyringe';
import { LineItem, SessionData } from '../dtos/products/schemas';
import { LanguageCode } from '../enum/languageCode';
import { ProductService } from '../services/productService';
import { logger } from '../utils/middleware/loggerMiddleware';
import { parseCustomToken } from '../utils/middleware/firebaseAuthAccessMiddleware';

@injectable()
export class ProductsController {
  constructor(@inject('ProductService') private productService: ProductService) {}

  /**
   * POST /webhook/checkout
   * Request Headers:
   *   - Stripe-Signature: string (Stripe webhook signature, required)
   * Request Body: Raw string (Stripe webhook payload)
   * Response: Success message (200)
   */
  async handleStripeEvents(c: Context) {
    const signature = c.req.header('Stripe-Signature');
    logger.info({ method: 'handleStripeEvents', signature: signature });
    const body = await c.req.text();
    await this.productService.handleStripeEvents(body, signature ?? '');
    return c.json('success', 200);
  }

  /**
   * GET /products/{accountId}
   * Path Parameters:
   *   - accountId: string (Account UUID, required)
   * Request Headers:
   *   - service-id-header: string (Service UUID, required)
   * Context Variables:
   *   - language: LanguageCode (from middleware)
   * Context Auth:
   *   - service_id: string (from Firebase custom token)
   *   - account_id: string (from Firebase custom token)
   * Response: StripeProduct[] (200)
   *   Array of product objects with pricing and availability info
   */
  async getProducts(c: Context) {
    const custom = parseCustomToken(c);
    const serviceId = custom!.service_id!;
    const accountId = custom!.account_id!;
    const lang = c.get('language') as LanguageCode;
    const products = await this.productService.getProducts(serviceId, accountId, lang);
    return c.json(products, 200);
  }

  /**
   * POST /products/{accountId}/checkout-session
   * Path Parameters:
   *   - accountId: string (Account UUID, required)
   * Request Headers:
   *   - service-id-header: string (Service UUID, required)
   * Request Body: LineItem
   *   - priceId: string (Stripe price ID, required)
   *   - quantity: number (Product quantity, required)
   * Context Auth:
   *   - service_id: string (from Firebase custom token)
   *   - account_id: string (from Firebase custom token)
   * Response: StripeCheckoutInfo (200)
   *   - sessionId: string
   *   - account: string
   *   - credential: string
   */
  async createCheckoutSession(c: Context) {
    const custom = parseCustomToken(c);
    const serviceId = custom!.service_id!;
    const accountId = custom!.account_id!;

    const products = await c.req.json<LineItem>();
    const checkout = await this.productService.createCheckoutSession(serviceId, accountId, products);

    return c.json(checkout, 200);
  }

  /**
   * POST /products/{accountId}/checkout-session/status
   * Path Parameters:
   *   - accountId: string (Account UUID, required)
   * Request Headers:
   *   - service-id-header: string (Service UUID, required)
   * Request Body: SessionData
   *   - sessionId: string (Stripe session ID, required)
   * Context Auth:
   *   - service_id: string (from Firebase custom token)
   *   - account_id: string (from Firebase custom token)
   * Response: CheckoutStatus (200)
   */
  async confirmCheckoutSessionStatus(c: Context) {
    const custom = parseCustomToken(c);
      const serviceId = custom!.service_id!;
    const accountId = custom!.account_id!;

    const session = await c.req.json<SessionData>();
    const status = await this.productService.confirmCheckoutStatus(serviceId, accountId, session.sessionId);

    return c.json(status, 200);
  }

  /**
   * DELETE /products/{accountId}/checkout-session
   * Path Parameters:
   *   - accountId: string (Account UUID, required)
   * Request Headers:
   *   - service-id-header: string (Service UUID, required)
   * Request Body: SessionData
   *   - sessionId: string (Stripe session ID, required)
   * Context Auth:
   *   - service_id: string (from Firebase custom token)
   *   - account_id: string (from Firebase custom token)
   * Response: Empty (204)
   */
  async expireCheckoutSessionStatus(c: Context) {
    const custom = parseCustomToken(c);
    const serviceId = custom!.service_id!;
    const accountId = custom!.account_id!;
    const session = await c.req.json<SessionData>();

    try {
      await this.productService.expireCheckoutSession(serviceId, accountId, session.sessionId);
    } catch (error) {
      logger.error({ api: 'expireCheckoutSessionStatus', error: error });
      throw error;
    }

    return c.body(null, 204);
  }
}
