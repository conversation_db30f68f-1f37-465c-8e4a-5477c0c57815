import { Context } from 'hono';
import { inject, injectable } from 'tsyringe';
import { CreateActionRequest, QuestCreateRequest } from '../dtos/services/schemas';
import { LanguageCode } from '../enum/languageCode';
import { QuestionnaireService } from '../services/questionnaireService';
import { QuestService } from '../services/questService';
import { CreateQuestRewardRequest, CreateRewardRequest } from '../dtos/services/schemas';

@injectable()
export class QuestController {
  constructor(
    @inject('QuestService') private questService: QuestService,
    @inject('QuestionnaireService') private questionnaireService: QuestionnaireService,
  ) {}

  async getQuests(c: Context) {
    const serviceId = c.req.header('Service-Id-Header') as string;
    const lang = c.get('language') as LanguageCode;

    const quests = await this.questService.getQuests(serviceId, lang);
    return c.json(quests, 200);
  }

  async getQuest(c: Context) {
    const questId = c.req.param('questId');
    const serviceId = c.req.header('Service-Id-Header') as string;
    const lang = c.get('language') as LanguageCode;

    const quest = await this.questService.getQuest(questId, serviceId, lang);
    return c.json(quest, 200);
  }

  async getAccountQuests(c: Context) {
    const accountId = c.req.param('accountId');
    const serviceId = c.req.header('Service-Id-Header') as string;
    const rewardStatus = c.req.query('rewardStatus');
    const questStatus = c.req.query('questStatus');
    const startAtFrom = c.req.query('startAtFrom');
    const startAtTo = c.req.query('startAtTo');
    const expireAtFrom = c.req.query('expireAtFrom');
    const expireAtTo = c.req.query('expireAtTo');

    const accountQuests = await this.questService.getAccountQuests(
      accountId,
      serviceId,
      rewardStatus,
      questStatus,
      startAtFrom,
      startAtTo,
      expireAtFrom,
      expireAtTo,
    );
    return c.json(accountQuests, 200);
  }

  async getQuestsByStatus(c: Context) {
    const serviceId = c.req.header('Service-Id-Header') as string;
    const lang = c.get('language') as LanguageCode;

    const questsStatus = await this.questService.getActiveStatusQuest(serviceId, lang);

    return c.json(questsStatus, 200);
  }

  async getAccountQuest(c: Context) {
    const accountId = c.req.param('accountId');
    const questId = c.req.param('questId');
    const serviceId = c.req.header('Service-Id-Header') as string;
    const lang = c.get('language') as LanguageCode;

    const accountQuests = await this.questService.getAccountQuest(accountId, questId, serviceId, lang);
    return c.json(accountQuests, 200);
  }

  async getAnsweredQuestionnaire(c: Context) {
    const serviceId = c.req.header('Service-Id-Header') as string;
    const accountId = c.req.param('accountId');
    const questionnaireId = c.req.param('questionnaireId');
    const lang = c.get('language') as LanguageCode;

    const response = await this.questionnaireService.getAnsweredQuestionnaire(
      serviceId,
      accountId,
      questionnaireId,
      lang,
    );
    return c.json(response, 200);
  }

  async createQuestReward(c: Context) {
    const questId = c.req.param('questId');
    const serviceId = c.req.header('Service-Id-Header') as string;
    const requestBody = await c.req.json<CreateQuestRewardRequest>();

    const reward = await this.questService.createQuestReward(questId, serviceId, requestBody);

    return c.json(reward, 200);
  }

  async createReward(c: Context) {
    const serviceId = c.req.header('Service-Id-Header') as string;
    const requestBody = await c.req.json<CreateRewardRequest>();

    const reward = await this.questService.createReward(serviceId, requestBody);

    return c.json(reward, 200);
  }

  async createQuestAction(c: Context) {
    const questId = c.req.param('questId');

    const serviceId = c.req.header('Service-Id-Header') as string;

    const requestBody = await c.req.json<CreateActionRequest>();

    const action = await this.questService.createQuestAction(serviceId, questId, requestBody);

    return c.json(action, 200);
  }

  async createQuest(c: Context) {
    const serviceId = c.req.header('Service-Id-Header') as string;

    const requestBody = await c.req.json<QuestCreateRequest>();

    const quest = await this.questService.createQuest(serviceId, requestBody);

    return c.json(quest, 200);
  }
}