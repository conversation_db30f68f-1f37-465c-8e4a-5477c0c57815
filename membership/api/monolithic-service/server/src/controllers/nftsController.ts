// nftController.ts
import { Context } from 'hono';
import { inject, injectable } from 'tsyringe';
import { NftsService } from '../services/nftsService';
import { NftRegisterService } from '../services/nftRegisterService';
import { MetadataService } from '../services/metadataService';
import { TransactionService } from '../services/transactionService';
import { NftMintService } from '../services/nftMintService';
import { NftMintRequest, NftRegister, RequestAccount } from '../dtos/nfts/schemas';
import { LanguageCode } from '../enum/languageCode';
import { I18nService } from '../services/i18nService';
import { BulkMintService } from '../services/bulkMintService';
import { NftTransactionUpdateService } from '../services/transactionUpdateService';
import { TxType } from '../enum/txType';

@injectable()
export class NftsController {
  constructor(
    @inject('NftsService') private nftsService: NftsService,
    @inject('MetadataService') private metadataService: MetadataService,
    @inject('NftRegisterService') private nftRegisterService: NftRegisterService,
    @inject('TransactionService') private transactionService: TransactionService,
    @inject('NftMintService') private nftMintService: NftMintService,
    @inject('NftTransactionUpdateService') private transactionUpdateService: NftTransactionUpdateService,
    @inject('BulkMintService') private bulkMintService: BulkMintService,
    @inject('I18nService') private i18nService: I18nService,
  ) {}

  async getMetadata(c: Context) {
    const nftId = c.req.param('nftId');
    const tokenId = parseInt(c.req.param('tokenId'), 10);

    const metadataResponse = await this.metadataService.getNftMetadata(nftId, tokenId);

    return c.json(metadataResponse, 200);
  }

  async searchMetadataByWallet(c: Context) {
    const serviceId = c.req.header('Service-Id-Header') as string;
    const requestBody = await c.req.json<RequestAccount>();

    const metadataList = await this.nftsService.getMetadataByWallet(serviceId, requestBody);
    return c.json(metadataList, 200);
  }

  async retryTransactions(c: Context) {
    const result = await this.transactionService.retryBulkTransactions();
    return c.json(result, 200);
  }

  async mint(c: Context) {
    const serviceId = c.req.header('Service-Id-Header') as string;
    const requestBody = await c.req.json<NftMintRequest>();
    const result = await this.nftMintService.mint(
      serviceId,
      requestBody.accountId,
      TxType.MINT_REWARD,
      requestBody.toAddress,
      requestBody.nftContractId,
      requestBody.nftType,
      requestBody.nftTokenId ?? undefined,
    );
    return c.json(result, 200);
  }

  async bulkMint(c: Context) {
    const result = await this.bulkMintService.handleBulkMint();
    return c.json(result, 200);
  }

  async register(c: Context) {
    const serviceId = c.req.header('Service-Id-Header') as string;
    const requestBody = await c.req.json<NftRegister>();
    const result = await this.nftRegisterService.register(serviceId, requestBody);
    return c.json(result, 200);
  }

  async getI18nMetadata(c: Context) {
    const contractAddress = c.req.param('contractAddress');
    const tokenId = c.req.param('tokenId');
    const accountId = c.req.header('account-id-header') as string;
    const lang = c.get('language') as LanguageCode;
    const result = await this.i18nService.getI18nNftMetadata(contractAddress, tokenId, accountId, lang);
    return c.json(result, 200);
  }

  async updateTxFinalityStatus(c: Context) {
    const result = await this.transactionUpdateService.updateTxFinalityStatus();
    return c.json(result, 200);
  }

  async deployModular(c: Context) {
    const serviceId = c.req.header('Service-Id-Header') as string;
    const result = await this.nftRegisterService.registerModularContract(serviceId);
    return c.json(result, 200);
  }

  async grantNftsMinterRole(c: Context) {
    const serviceId = c.req.header('Service-Id-Header') as string;
    const result = await this.nftRegisterService.grantNftsMinterRole(serviceId);
    return c.json(result, 200);
  }
}
