// nftController.ts
import { Context } from 'hono';
import { inject, injectable } from 'tsyringe';
import { NftsService } from '../services/nftsService';
import { NftRegisterService } from '../services/nftRegisterService';
import { MetadataService } from '../services/metadataService';
import { TransactionService } from '../services/transactionService';
import { NftMintService } from '../services/nftMintService';
import { CheckTokenGateAccessRequest, NftMintRequest, NftRegister, RegisterTokenGateRequest, RequestAccount, VerifySigRequest } from '../dtos/nfts/schemas';
import { LanguageCode } from '../enum/languageCode';
import { I18nService } from '../services/i18nService';
import { BulkMintService } from '../services/bulkMintService';
import { NftTransactionUpdateService } from '../services/transactionUpdateService';
import { TokenGateService } from '../services/tokenGateService';
import { TxType } from '../enum/txType';
import { parseCustomToken } from '../utils/middleware/firebaseAuthAccessMiddleware';

@injectable()
export class NftsController {
  constructor(
    @inject('NftsService') private nftsService: NftsService,
    @inject('MetadataService') private metadataService: MetadataService,
    @inject('NftRegisterService') private nftRegisterService: NftRegisterService,
    @inject('TransactionService') private transactionService: TransactionService,
    @inject('NftMintService') private nftMintService: NftMintService,
    @inject('NftTransactionUpdateService') private transactionUpdateService: NftTransactionUpdateService,
    @inject('BulkMintService') private bulkMintService: BulkMintService,
    @inject('I18nService') private i18nService: I18nService,
    @inject('TokenGateService') private tokenGateService: TokenGateService,
  ) {}

  /**
   * GET /nfts/metadata/{nftId}/{tokenId}
   * Path Parameters:
   *   - nftId: string (NFT identifier UUID, required)
   *   - tokenId: string (NFT token ID, required)
   * Response: GeneralMetadata (200)
   *   - name: string
   *   - description: string
   *   - image: string
   *   - external_url: string (optional)
   *   - animation_url: string (optional)
   *   - background_color: string (optional)
   *   - youtube_url: string (optional)
   */
  async getMetadata(c: Context) {
    const nftId = c.req.param('nftId');
    const tokenId = parseInt(c.req.param('tokenId'), 10);

    const metadataResponse = await this.metadataService.getNftMetadata(nftId, tokenId);

    return c.json(metadataResponse, 200);
  }

  /**
   * POST /nfts/metadata
   * Request Headers:
   *   - Service-Id-Header: string (Service UUID, required)
   * Request Body: RequestAccount
   *   - contractAccountAddress: string (Ethereum address, required)
   * Response: MetadataList (200)
   *   Array of NFT metadata objects
   */
  async searchMetadataByWallet(c: Context) {
    const serviceId = c.req.header('Service-Id-Header') as string;
    const requestBody = await c.req.json<RequestAccount>();

    const metadataList = await this.nftsService.getMetadataByWallet(serviceId, requestBody);
    return c.json(metadataList, 200);
  }

  /**
   * POST /nfts/retry-transactions
   * Response: RetryTransactionResult (200)
   *   - retriedTransactionCount: number
   *   - errors: string[] (optional)
   */
  async retryTransactions(c: Context) {
    const result = await this.transactionService.retryBulkTransactions();
    return c.json(result, 200);
  }

  /**
   * POST /nfts/mint
   * Request Headers:
   *   - Service-Id-Header: string (Service UUID, required)
   * Request Body: NftMintRequest
   *   - accountId: string (Account UUID, required)
   *   - toAddress: string (Ethereum address, required)
   *   - nftContractId: string (NFT contract UUID, required)
   *   - nftType: NftType (NFT type enum, required)
   *   - nftTokenId: string (NFT token ID, optional)
   * Response: NftMintResult (200)
   *   - transactionHash: string
   *   - tokenId: string
   *   - contractAddress: string
   */
  async mint(c: Context) {
    const serviceId = c.req.header('Service-Id-Header') as string;
    const requestBody = await c.req.json<NftMintRequest>();
    const result = await this.nftMintService.mint(
      serviceId,
      requestBody.accountId,
      TxType.MINT_REWARD,
      requestBody.toAddress,
      requestBody.nftContractId,
      requestBody.nftType,
      requestBody.nftTokenId ?? undefined,
    );
    return c.json(result, 200);
  }

  /**
   * POST /nfts/bulk-mint
   * Response: BulkMintResult (200)
   *   - processedCount: number
   *   - successCount: number
   *   - failedCount: number
   *   - errors: string[] (optional)
   */
  async bulkMint(c: Context) {
    const result = await this.bulkMintService.handleBulkMint();
    return c.json(result, 200);
  }

  /**
   * POST /nfts/register
   * Request Headers:
   *   - Service-Id-Header: string (Service UUID, required)
   * Request Body: NftRegister
   *   - name: string (NFT collection name, required)
   *   - symbol: string (NFT collection symbol, required)
   *   - description: string (NFT description, required)
   *   - image: string (NFT image URL, required)
   *   - nftType: NftType (NFT type enum, required)
   *   - nftTag: NftTag (NFT tag enum, required)
   * Response: NftRegisterResult (200)
   *   - contractAddress: string
   *   - nftContractId: string
   *   - transactionHash: string
   */
  async register(c: Context) {
    const serviceId = c.req.header('Service-Id-Header') as string;
    const requestBody = await c.req.json<NftRegister>();
    const result = await this.nftRegisterService.register(serviceId, requestBody);
    return c.json(result, 200);
  }

  /**
   * GET /nfts/i18n/{contractAddress}/{tokenId}
   * Path Parameters:
   *   - contractAddress: string (Ethereum address, required)
   *   - tokenId: string (NFT token ID, required)
   * Request Headers:
   *   - account-id-header: string (Account UUID, required)
   *   - accept-language: string (Language preference, required)
   * Context Variables:
   *   - language: LanguageCode (from middleware)
   * Context Auth:
   *   - account_id: string (from Firebase custom token)
   * Response: I18nNftMetadata (200)
   *   - name: string (localized)
   *   - description: string (localized)
   *   - image: string
   *   - attributes: object[] (localized attributes)
   */
  async getI18nMetadata(c: Context) {
    const custom = parseCustomToken(c);
    const accountId = custom!.account_id!;

    const contractAddress = c.req.param('contractAddress');
    const tokenId = c.req.param('tokenId');
    const lang = c.get('language') as LanguageCode;
    const result = await this.i18nService.getI18nNftMetadata(contractAddress, tokenId, accountId, lang);
    return c.json(result, 200);
  }

  /**
   * POST /nfts/finality-status
   * Response: TxFinalityStatusResult (200)
   *   - updatedTransactionCount: number
   *   - finalizedTransactionCount: number
   *   - errors: string[] (optional)
   */
  async updateTxFinalityStatus(c: Context) {
    const result = await this.transactionUpdateService.updateTxFinalityStatus();
    return c.json(result, 200);
  }

  /**
   * POST /nfts/deploy-modular
   * Request Headers:
   *   - Service-Id-Header: string (Service UUID, required)
   * Response: DeployModularResult (200)
   *   - contractAddress: string
   *   - transactionHash: string
   *   - deploymentStatus: string
   */
  async deployModular(c: Context) {
    const serviceId = c.req.header('Service-Id-Header') as string;
    const result = await this.nftRegisterService.registerModularContract(serviceId);
    return c.json(result, 200);
  }

  /**
   * POST /nfts/grant-nfts-minter-role
   * Request Headers:
   *   - Service-Id-Header: string (Service UUID, required)
   * Response: GrantMinterRoleResult (200)
   *   - transactionHash: string
   *   - grantedAddresses: string[]
   *   - status: string
   */
  async grantNftsMinterRole(c: Context) {
    const serviceId = c.req.header('Service-Id-Header') as string;
    const result = await this.nftRegisterService.grantNftsMinterRole(serviceId);
    return c.json(result, 200);
  }

  async generateNonceForSig(c: Context) {
    const result = await this.tokenGateService.generateNonceForSig();
    return c.json(result, 200);
  }

  async verifySigAndFetchNfts(c: Context) {
    const serviceId = c.req.header('Service-Id-Header') as string;
    const accountId = c.req.param('accountId');
    const requestBody = await c.req.json<VerifySigRequest>();
    const result = await this.tokenGateService.verifySigAndFetchNfts(serviceId, accountId, requestBody);
    return c.json(result, 200);
  }

  async checkTokenGateAccess(c: Context) {
    const serviceId = c.req.header('Service-Id-Header') as string;
    const requestBody = await c.req.json<CheckTokenGateAccessRequest>();
    const result = await this.tokenGateService.checkTokenGateAccess(serviceId, requestBody);
    return c.json(result, 200);
  }

  async registerTokenGate(c: Context) {
    const serviceId = c.req.header('Service-Id-Header') as string;
    const requestBody = await c.req.json<RegisterTokenGateRequest>();
    const result = await this.tokenGateService.registerTokenGate(serviceId, requestBody);
    return c.json(result, 201);
  }
}
