import { Context } from 'hono';
import { inject, injectable } from 'tsyringe';
import { WebhookRequest } from '../dtos/webhook/schemas';
import { WebhookService } from '../services/webhookService';
import { logger } from '../utils/middleware/loggerMiddleware';

@injectable()
export class WebhookController {
  constructor(@inject('WebhookService') private webhookService: WebhookService) {}

  async updateTransaction(c: Context) {
    const transaction = await c.req.json<WebhookRequest>();
    logger.info({ message: 'Received transaction', body: transaction });
    const signature = c.req.header('X-Alchemy-Signature') as string;
    const response = await this.webhookService.handleBlockchainWebhook(transaction, signature);
    return c.json(response, 200);
  }
}
