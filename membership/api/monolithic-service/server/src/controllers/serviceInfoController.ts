import { Context } from 'hono';
import { inject, injectable } from 'tsyringe';
import { ServiceInfoService } from '../services/serviceInfoService';
import { RegisterServiceRequest } from '../dtos/services/schemas';
import { LanguageCode } from '../enum/languageCode';

@injectable()
export class ServiceInfoController {
  constructor(
    @inject('ServiceInfoService')
    private serviceInfoService: ServiceInfoService,
  ) {}

  async getServiceInfo(c: Context) {
    const serviceId = c.req.header('Service-Id-Header') as string;
    const lang = c.get('language') as LanguageCode;
    const serviceInfo = await this.serviceInfoService.getServiceById(serviceId, lang);
    return c.json(serviceInfo, 200);
  }

  async registerService(c: Context) {
    const tenantId = c.req.header('tenant-id-header') as string;

    const requestBody = await c.req.json<RegisterServiceRequest>();

    const service = await this.serviceInfoService.registerService(tenantId, requestBody);
    return c.json(service, 200);
  }
}
