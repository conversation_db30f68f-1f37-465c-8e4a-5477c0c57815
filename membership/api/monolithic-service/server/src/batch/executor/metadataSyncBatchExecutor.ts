import type FirebaseFirestore from '@google-cloud/firestore';
import { ethers } from 'ethers';
import { injectable } from 'tsyringe';
import { NftType } from '../../enum/nftType';
import { TransferTxType } from '../../enum/transferTxType';
import { InternalServerError } from '../../errors/internalServerError';
import { NotFoundError } from '../../errors/notFoundError';
import { AccountRepository } from '../../repositories/accountRepository';
import { NftContractsRepository } from '../../repositories/nftContractsRepository';
import { FirestoreNftsDocument, NftsFirestoreRepository } from '../../repositories/nftsFirestoreRepository';
import { AlchemyTransfer, MetadataFetchService, tokenIdAndOwnerBalances } from '../../services/metadataFetchService';
import { AccountEntity } from '../../tables/accountTable';
import { logger } from '../../utils/middleware/loggerMiddleware';
import { retryExecution } from '../../utils/retry';
import { config } from '../../configs/config';

export type FailedList = { contractAddress: string; tokenId: string; failedReason: string }[];

interface MetadataSyncInput {
  serviceId: string;
  contractAddress: string;
  tokenIds?: (number | string)[];
}

@injectable()
export class MetadataSyncBatchExecutor {
  private apiKey: string;
  private chainName: string;
  constructor(
    private readonly nftContractsRepository: NftContractsRepository,
    private readonly accountRepository: AccountRepository,
    private readonly metadataFetchService: MetadataFetchService,
    private readonly nftsFirestoreRepository: NftsFirestoreRepository,
  ) {
    this.apiKey = process.env.ALCHEMY_API_KEY || '';
    this.chainName = process.env.ALCHEMY_CHAIN_NAME || '';
    if (!this.apiKey) {
      throw new InternalServerError('ALCHEMY_API_KEY not set');
    }
    if (!this.chainName) {
      throw new InternalServerError('ALCHEMY_CHAIN_NAME not set');
    }
  }

  async execute(data: MetadataSyncInput[]): Promise<FailedList[]> {
    // validation
    for (const contractAndTokenIds of data) {
      if (contractAndTokenIds.serviceId === undefined) {
        logger.error('Each item must have a serviceId', contractAndTokenIds);
        throw new Error('Each item must have a serviceId');
      }
      if (typeof contractAndTokenIds !== 'object' || contractAndTokenIds === null) {
        logger.error('Each item must be an object', contractAndTokenIds);
        throw new Error('Each item must be an object');
      }
      const hasContractAddress = typeof contractAndTokenIds.contractAddress === 'string';
      const hasTokenIds = Array.isArray(contractAndTokenIds.tokenIds);

      if (!hasContractAddress) {
        logger.error('Each item must have a contractAddress', contractAndTokenIds);
        throw new Error('Each item must have a contractAddress');
      }

      if (!hasContractAddress && hasTokenIds) {
        logger.error('tokenIds cannot exist without contractAddress', contractAndTokenIds);
        throw new Error('tokenIds cannot exist without contractAddress');
      }
    }
    // Execution
    logger.info(`Metadata sync batch execution start`);
    const finalResult: FailedList[] = [];
    for (const contractAndTokenIds of data) {
      const result = await this.sync(contractAndTokenIds);
      finalResult.push(result.failedList);
    }
    return finalResult;
  }

  async sync({
    serviceId,
    contractAddress,
    tokenIds,
  }: {
    serviceId: string;
    contractAddress: string;
    tokenIds?: (number | string)[];
  }): Promise<{ failedList: { contractAddress: string; tokenId: string; failedReason: string }[] }> {
    logger.info(`Syncing metadata with data: ${JSON.stringify({ serviceId, contractAddress, tokenIds })}`);
    // NFT contractからnft_typeを取得
    const nftContract = await this.nftContractsRepository.selectNftContractByContractAddress(
      contractAddress,
      serviceId,
    );
    if (!nftContract) {
      throw new NotFoundError(`NFT contract not found for contract address: ${contractAddress}`);
    }
    const nftType = nftContract.nft_type;
    const abi = nftContract.nft_contract_abi as unknown as ethers.InterfaceAbi;
    const tokenInfoList: { tokenId: string; tokenUri: string }[] = [];
    const failedList: { contractAddress: string; tokenId: string; failedReason: string }[] = [];

    let tokenAndBalances: tokenIdAndOwnerBalances | null = null;
    // ownerとbalanceはalchemyから取得してしまう(ERC1155の取得にはAPI利用でないと難しいため)
    logger.info(`-----------------Start retrieving token ids and owner balances-----------------`);
    try {
      tokenAndBalances = await retryExecution(
        async () =>
          await this.metadataFetchService.fetchTokenIdAndOwnerBalances(this.apiKey, this.chainName, contractAddress),
        { retryDelay: 1000 },
      );
      if (!tokenAndBalances || Object.keys(tokenAndBalances).length === 0) {
        logger.error(`No token ids and owner balances found for contract ${contractAddress}`);
        failedList.push({ contractAddress, tokenId: 'all', failedReason: 'No token ids and owner balances found' });
        return { failedList };
      }
    } catch (error) {
      logger.error(error, 'Failed to retrieve token ids and owner balances (all tokens)');
      failedList.push({
        contractAddress,
        tokenId: 'all',
        failedReason: 'Failed to retrieve token ids and owner balances (all tokens)',
      });
      return { failedList };
    }

    // tokenIdsが指定されていない場合は、すべてのtokenIdを取得
    if (!tokenIds || tokenIds.length === 0) {
      try {
        logger.info(`-----------------Start retrieving token info for all tokens-----------------`);
        const infos = await retryExecution(
          async () => await this.metadataFetchService.fetchNftTokens(this.apiKey, this.chainName, contractAddress),
          { retryDelay: 1000 },
        );
        const tokenIds = infos.map((info) => info.tokenId);
        if (tokenIds.length === 0) {
          logger.error(`No token info found for contract ${contractAddress}`);
          failedList.push({ contractAddress, tokenId: 'none', failedReason: 'No token info found' });
          return { failedList };
        }
        for (const tokenId of tokenIds) {
          // 429対策、0.5秒待機
          await new Promise((resolve) => setTimeout(resolve, 500));
          try {
            const tokenUri = await retryExecution(
              async () =>
                await this.metadataFetchService.fetchTokenUri(nftType, contractAddress, abi, tokenId.toString()),
              { retryDelay: 500 },
            );
            if (!tokenUri) {
              failedList.push({
                contractAddress,
                tokenId: tokenId.toString(),
                failedReason: 'Failed to retrieve token info',
              });
              continue;
            }
            tokenInfoList.push({ tokenId: tokenId.toString(), tokenUri: tokenUri });
          } catch (error) {
            logger.error(error, `Failed to retrieve token info ${contractAddress} ${tokenId}`);
            failedList.push({
              contractAddress,
              tokenId: tokenId.toString(),
              failedReason: 'Failed to retrieve token info',
            });
            continue;
          }
        }
      } catch (error) {
        logger.error(error, 'Failed to retrieve token info (all tokens)');
        failedList.push({
          contractAddress,
          tokenId: 'none',
          failedReason: 'Failed to retrieve token info (all tokens)',
        });
        return { failedList };
      }
      logger.info(`-----------------End retrieving token info for all tokens-----------------`);
    } else {
      logger.info(`-----------------Start retrieving token info for specified tokens-----------------`);
      // tokenIdsが指定されている場合は、指定されたtokenIdのみを取得)
      for (const tokenId of tokenIds) {
        if (!tokenAndBalances[tokenId]) {
          // tokenIdが存在しない場合は、引数として渡したtokenIdが間違っている可能性があるので通知
          failedList.push({
            contractAddress,
            tokenId: tokenId.toString(),
            failedReason: 'TokenId not found in contract.',
          });
          continue;
        }
        try {
          const tokenUri = await retryExecution(
            async () =>
              await this.metadataFetchService.fetchTokenUri(nftType, contractAddress, abi, tokenId.toString()),
            { retryDelay: 500 },
          );
          if (!tokenUri) {
            logger.error(`Failed to retrieve token info ${contractAddress} ${tokenId}`);
            failedList.push({
              contractAddress,
              tokenId: tokenId.toString(),
              failedReason: 'Failed to retrieve token info',
            });
            continue;
          }
          tokenInfoList.push({ tokenId: tokenId.toString(), tokenUri: tokenUri });
        } catch (error) {
          logger.error(error, `Failed to retrieve token info ${contractAddress} ${tokenId}`);
          failedList.push({
            contractAddress,
            tokenId: tokenId.toString(),
            failedReason: 'Failed to retrieve token info',
          });
          continue;
        }
      }
      logger.info(`-----------------End retrieving token info for specified tokens-----------------`);
    }

    for (const tokenInfo of tokenInfoList) {
      logger.info(`-----------------Start retrieving metadata for ${tokenInfo.tokenId}-----------------`);
      let metadata: unknown;
      try {
        // 429、負荷対策、基本1秒待機
        await new Promise((resolve) => setTimeout(resolve, parseInt(process.env.METADATA_SYNC_INTERVAL_MS || '1000')));
        metadata = await retryExecution(
          async () => await this.metadataFetchService.fetchMetadataFromTokenURI(tokenInfo.tokenUri),
          { retryDelay: 500 },
        );
        if (!metadata) {
          failedList.push({ contractAddress, tokenId: tokenInfo.tokenId, failedReason: 'Failed to retrieve metadata' });
          continue;
        }
      } catch {
        failedList.push({ contractAddress, tokenId: tokenInfo.tokenId, failedReason: 'Failed to retrieve metadata' });
        continue;
      }
      logger.info(`-----------------End retrieving metadata for ${tokenInfo.tokenId}-----------------`);

      logger.info(`-----------------Start upserting/deleting Firestore for ${tokenInfo.tokenId}-----------------`);
      let account: AccountEntity | undefined;
      const owners = tokenAndBalances[tokenInfo.tokenId];
      for (const owner of owners) {
        // ownerがaccountの場合はupsert、ownerがaccountでない場合はdelete
        let operation: 'upsert' | 'delete' = 'upsert';
        try {
          account = await retryExecution(
            async () => await this.getAccountByTokenBoundAccountAddress(owner.ownerAddress, serviceId),
            { retryDelay: 500 },
          );
          if (!account) {
            operation = 'delete';
          }
        } catch (error) {
          logger.info(
            error,
            `Failed to get account for owner address ${contractAddress}, tokenId: ${tokenInfo.tokenId}`,
          );
          failedList.push({ contractAddress, tokenId: tokenInfo.tokenId, failedReason: 'Failed to get account' });
          continue;
        }
        let transaction: AlchemyTransfer | null = null;
        try {
          transaction = await retryExecution(
            async () =>
              await this.metadataFetchService.fetchLatestTokenTransaction({
                apiKey: this.apiKey,
                chainName: this.chainName,
                contractAddress,
                toAddress: owner.ownerAddress,
                tokenId: tokenInfo.tokenId,
              }),
            { retryDelay: 500 },
          );

          if (!transaction) {
            failedList.push({
              contractAddress,
              tokenId: tokenInfo.tokenId,
              failedReason: 'Failed to get latest transfer transaction',
            });
            continue;
          }
        } catch {
          failedList.push({
            contractAddress,
            tokenId: tokenInfo.tokenId,
            failedReason: 'Failed to get latest transfer transaction',
          });
          continue;
        }
        // MetadataをFirestoreにupsert
        try {
          if (operation === 'upsert') {
            logger.info(
              `Upserting Firestore for contract ${contractAddress}, tokenId: ${tokenInfo.tokenId} account: ${account?.account_id}`,
            );
            await retryExecution(
              async () =>
                await this.upsertFirestore({
                  contractAddress,
                  tokenId: tokenInfo.tokenId,
                  metadata,
                  account: account as AccountEntity,
                  nftType,
                  amount: owner.balance,
                  tokenUri: tokenInfo.tokenUri,
                  transactions: {
                    [transaction.hash]: {
                      from: transaction.from,
                      to: transaction.to,
                      transactionDate: transaction.metadata?.blockTimestamp ?? '',
                      transactionType: TransferTxType.TRANSFER,
                      // 複雑さを避けるためmetadataが存在している時点でconfirmedとする、齟齬が発生した場合は再度バッチ処理で更新する
                      isConfirmed: true,
                    },
                  },
                }),
              { retryDelay: 500 },
            );
            logger.info(`Upserted Firestore for contract ${contractAddress}, tokenId: ${tokenInfo.tokenId}`);
          } else if (operation === 'delete') {
            logger.info(`Deleting Firestore for contract ${contractAddress}, tokenId: ${tokenInfo.tokenId}`);
            await retryExecution(
              async () => await this.nftsFirestoreRepository.deleteNft(contractAddress, tokenInfo.tokenId),
              { retryDelay: 500 },
            );
            logger.info(`Deleted Firestore for contract ${contractAddress}, tokenId: ${tokenInfo.tokenId}`);
          }
        } catch (error) {
          logger.error(error, 'Firestore update failed');
          failedList.push({ contractAddress, tokenId: tokenInfo.tokenId, failedReason: 'Firestore update failed' });
        }
      }
      logger.info(`-----------------End upserting/deleting Firestore for ${tokenInfo.tokenId}-----------------`);
    }

    if (failedList.length > 0) {
      logger.warn('Failed to sync metadata, failed list:' + JSON.stringify(failedList));
      return { failedList };
    }
    logger.info('Metadata sync completed successfully');
    return { failedList: [] };
  }

  private async getAccountByTokenBoundAccountAddress(
    tokenBoundAccountAddress: string,
    serviceId: string,
  ): Promise<AccountEntity | undefined> {
    const account = await this.accountRepository.selectAccountByTokenBoundAddress(tokenBoundAccountAddress, serviceId);
    if (!account) {
      return undefined;
    }
    return account;
  }

  private async upsertFirestore({
    contractAddress,
    tokenId,
    metadata,
    account,
    nftType,
    amount,
    tokenUri,
    transactions,
  }: {
    contractAddress: string;
    tokenId: string;
    metadata: object;
    account: AccountEntity;
    nftType: NftType;
    amount: number;
    tokenUri: string;
    transactions: FirestoreNftsDocument['transactions'];
  }): Promise<FirebaseFirestore.WriteResult> {
    const result = await this.nftsFirestoreRepository.insertNft({
      chainId: config.chainId,
      contractAddress,
      tokenId: tokenId.toString(),
      amount: amount,
      accountId: account.account_id,
      serviceId: account.service_id,
      contractType: nftType,
      metadataJson: metadata,
      metadataUri: tokenUri,
      metadataCachedAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      createdAt: new Date().toISOString(),
      transactions,
    });
    if (!result) {
      throw new InternalServerError('Failed to upsert Firestore');
    }
    return result;
  }
}
