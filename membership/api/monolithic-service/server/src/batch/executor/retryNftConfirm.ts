import { createPublicClient, http, isAddress, parseAbi, parseEventLogs } from 'viem';
import { polygonAmoy } from 'viem/chains';
import { config } from '../../configs/config';
import { TransactionQueueStatus } from '../../enum/transactionQueueStatus';
import { TransactionStatus } from '../../enum/transactionStatus';
import { TransferTxType } from '../../enum/transferTxType';
import { TxType } from '../../enum/txType';
import { AccountRepository } from '../../repositories/accountRepository';
import { DeliveryNftsFirestoreRepository } from '../../repositories/deliveryNftsFirestoreRepository';
import { NftBaseMetadatasRepository } from '../../repositories/nftBaseMetadatasRepository';
import { NftMetadatasRepository } from '../../repositories/nftMetadatasRepository';
import { NftsFirestoreRepository } from '../../repositories/nftsFirestoreRepository';
import { TransactionQueuesRepository } from '../../repositories/transactionQueuesRepository';
import { TransactionsRepository } from '../../repositories/transactionsRepository';
import { logger } from '../../utils/middleware/loggerMiddleware';

export interface RetryNftConfirmArgs {
  queueIds: string[];
}

export interface FailedQueues {
  queueId: string;
  txHash: string | null;
  txType: TxType;
}

const transferEventAbiJson = 'event Transfer(address indexed from, address indexed to, uint256 indexed tokenId)';

export class RetryNftConfirmExecutor {
  constructor(
    private readonly nftsFirestoreRepository: NftsFirestoreRepository,
    private readonly deliveryNftsFirestoreRepository: DeliveryNftsFirestoreRepository,
    private readonly transactionsRepository: TransactionsRepository,
    private readonly transactionQueuesRepository: TransactionQueuesRepository,

    private readonly nftMetadatasRepository: NftMetadatasRepository,
    private readonly nftBaseMetadatasRepository: NftBaseMetadatasRepository,
    private readonly accountRepository: AccountRepository,
  ) {}

  async execute(inputs: RetryNftConfirmArgs): Promise<FailedQueues[]> {
    const failedResults: FailedQueues[] = [];
    const { alchemyApiKey, alchemyChainName } = config;
    const client = createPublicClient({
      chain: polygonAmoy,
      transport: http(`https://${alchemyChainName}.g.alchemy.com/v2/${alchemyApiKey}`),
    });

    // queueIdからtxの状態を確認し、statusがEXECUTED or RETRYでなければスキップ
    if (inputs.queueIds.length === 0) {
      return failedResults;
    }
    const txs = await this.transactionsRepository.selectTransactionsByQueueIds(inputs.queueIds);
    for (const tx of txs) {
      const failedResult = { queueId: tx.queueId, txHash: tx.txHash, txType: tx.txType };
      if (tx.txHash === null) {
        const reason = 'Transaction is not executed or txHash is null';
        logger.warn({ message: reason, data: { ...failedResult, reason } });
        failedResults.push(failedResult);

        continue;
      }

      if ([TxType.MINT_MEMBERSHIP, TxType.MINT_PRODUCT, TxType.MINT_REWARD].includes(tx.txType)) {
        // txHashからreceiptを取得し、receiptが存在しなければfailedのqueueIdに追加、存在すれば取得
        const receipt = await client.getTransactionReceipt({
          hash: tx.txHash as `0x${string}`,
        });
        if (!receipt) {
          const reason = 'Failed to get transaction receipt';
          logger.warn({ message: reason, data: { ...failedResult, reason } });
          failedResults.push(failedResult);
          continue;
        }

        // receiptからNFTのcontract addressとtokenIdを取得
        const transferEventAbi = parseAbi([transferEventAbiJson]);
        const logs = parseEventLogs({ logs: receipt.logs, abi: transferEventAbi });
        if (logs.length === 0) {
          const reason = 'No Transfer event found';
          logger.warn({ message: reason, data: { ...failedResult, reason } });
          failedResults.push(failedResult);
          continue;
        }
        const mintLog = logs.filter(
          (log): log is NonNullable<typeof log> =>
            !!log && log.eventName === 'Transfer' && log.args.from === '0x0000000000000000000000000000000000000000',
        );
        if (mintLog.length === 0) {
          const reason = 'No Transfer Mint event found';
          logger.warn({ message: reason, data: { ...failedResult, reason } });
          failedResults.push(failedResult);
          continue;
        }

        // metadataにデータが保存されているかを確認し、データが保存されていなければmetadataに保存
        const nftContractAddress = mintLog[0].address;
        if (!nftContractAddress || !isAddress(nftContractAddress)) {
          const reason = 'Invalid NFT contract address';
          logger.warn({ message: reason, data: { ...failedResult, reason } });
          failedResults.push(failedResult);
          continue;
        }
        const tokenId = Number(mintLog[0].args.tokenId);
        const mintTargetContractAddress = mintLog[0].args.to;
        const mintFromAddress = mintLog[0].args.from;

        const metadata = await this.nftBaseMetadatasRepository.selectDataByContractAddress(nftContractAddress);
        if (!metadata) {
          const reason = 'No metadata found for the contract address';
          logger.warn({ message: reason, data: { ...failedResult, reason } });
          failedResults.push(failedResult);
          continue;
        }
        const insertedData = await this.nftMetadatasRepository.selectMetadataByNftIdAndToken(
          metadata.base_metadata_id,
          tokenId,
        );
        if (!insertedData) {
          await this.nftMetadatasRepository.insertMetadata({
            base_metadata_id: metadata.base_metadata_id,
            token_id: tokenId,
            metadata: metadata.metadata,
            transaction_id: tx.transactionId,
          });
        }

        // firestoreのdelivery_nftsを確認
        const deliveryId = await this.deliveryNftsFirestoreRepository.selectDeliveryNft(tx.txHash);

        if (deliveryId !== undefined) {
          // delivery_nftsのドキュメントが存在する場合は、nftsのドキュメントを確認
          const nftCache = await this.nftsFirestoreRepository.selectNft(nftContractAddress, tokenId.toString());

          if (nftCache.empty) {
            // nftsのドキュメントが存在しない場合は、nftsに挿入
            const now = new Date();
            const account = await this.accountRepository.selectAccountIdByAddress(
              mintTargetContractAddress,
              tx.serviceId,
            );

            if (!account) {
              const reason = 'No account found for the address';
              logger.warn({ message: reason, data: { ...failedResult, reason } });
              failedResults.push(failedResult);
              continue;
            }

            const block = await client.getBlock({
              blockNumber: logs[0].blockNumber,
            });
            const transactionDate = new Date(Number(block.timestamp) * 1000);

            await this.nftsFirestoreRepository.insertNft({
              contractAddress: nftContractAddress,
              tokenId: tokenId.toString(),
              amount: 1,
              chainId: config.chainId,
              accountId: account.account_id,
              serviceId: tx.serviceId,
              contractType: tx.nftType,
              metadataJson: metadata?.metadata || {},
              metadataUri: `${config.metadataUrl}/${metadata?.base_metadata_id}/${tokenId}`,
              metadataCachedAt: now.toISOString(),
              transactions: {
                txHash: {
                  from: mintFromAddress,
                  to: mintTargetContractAddress,
                  transactionDate: transactionDate.toISOString(),
                  transactionType: TransferTxType.MINT,
                  isConfirmed: true,
                },
              },
              createdAt: transactionDate.toISOString(),
              updatedAt: now.toISOString(),
            });
          }
          // nftsのドキュメントが存在する場合は、delivery_nftsを削除
          await this.deliveryNftsFirestoreRepository.deleteDeliveryNftById(deliveryId);
        }
      } else if ([TxType.EXECUTE_CONTRACT, TxType.DEPLOY_CONTRACT].includes(tx.txType)) {
        // 何もしない
      } else if ([TxType.CREATE_ACCOUNT].includes(tx.txType)) {
        failedResults.push(failedResult);
        logger.warn({ message: 'Create account transaction is not supported', data: failedResult });
        continue;
      } else {
        logger.warn({ message: 'Unknown transaction type', data: failedResult });
        failedResults.push(failedResult);
        continue;
      }

      // transaction queuesのstatusをMINEDに更新
      await this.transactionQueuesRepository.updateQueueStatus([tx.queueId], TransactionQueueStatus.MINED);
      // transactionsのstatusをMINEDに更新
      await this.transactionsRepository.updateTransactionStatus(tx.transactionId, TransactionStatus.MINED);
    }

    return failedResults;
  }
}
