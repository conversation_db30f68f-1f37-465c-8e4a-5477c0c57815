import { v4 as uuidV4 } from 'uuid';
import { Address, createPublicClient, http, isAddress, parseAbi } from 'viem';
import { polygonAmoy } from 'viem/chains';
import { config } from '../../configs/config';
import { NftType } from '../../enum/nftType';
import { TransactionStatus } from '../../enum/transactionStatus';
import { TransferTxType } from '../../enum/transferTxType';
import { AccountRepository } from '../../repositories/accountRepository';
import { DeliveryNftsFirestoreRepository } from '../../repositories/deliveryNftsFirestoreRepository';
import { NftBaseMetadatasRepository } from '../../repositories/nftBaseMetadatasRepository';
import { NftContractsRepository } from '../../repositories/nftContractsRepository';
import { NftMetadatasRepository } from '../../repositories/nftMetadatasRepository';
import { NftsFirestoreRepository } from '../../repositories/nftsFirestoreRepository';
import { TransactionsRepository } from '../../repositories/transactionsRepository';
import { logger } from '../../utils/middleware/loggerMiddleware';

export interface FixAndSyncNftArgs {
  serviceIds: string[];
}

export interface ResultInsertedData {
  serviceId: string;
  accountId: string;
  contractAddress: string;
  tokenId: number;
}

const transferEventAbiJson = 'event Transfer(address indexed from, address indexed to, uint256 indexed tokenId)';

export class FixAndSyncNftMetadataBatchExecutor {
  constructor(
    private readonly nftContractsRepository: NftContractsRepository,
    private readonly nftsFirestoreRepository: NftsFirestoreRepository,
    private readonly deliveryNftsFirestoreRepository: DeliveryNftsFirestoreRepository,
    private readonly transactionsRepository: TransactionsRepository,
    private readonly nftMetadatasRepository: NftMetadatasRepository,
    private readonly nftBaseMetadatasRepository: NftBaseMetadatasRepository,
    private readonly accountRepository: AccountRepository,
  ) {}

  async execute(inputs: FixAndSyncNftArgs): Promise<ResultInsertedData[]> {
    const result: ResultInsertedData[] = [];
    const { alchemyApiKey, alchemyChainName } = config;
    const client = createPublicClient({
      chain: polygonAmoy,
      transport: http(`https://${alchemyChainName}.g.alchemy.com/v2/${alchemyApiKey}`),
    });
    const transferEventAbi = parseAbi([transferEventAbiJson]);
    const latestBlock = await client.getBlockNumber();
    const startBlock = latestBlock - 12_960_000n; // 約6ヶ月前のブロック番号

    const updateTokens: {
      serviceId: string;
      contractId: string;
      contractAddress: string;
      tokenId: number;
      nftType: NftType;
    }[] = [];

    for (const serviceId of inputs.serviceIds) {
      const nftContracts = await this.nftContractsRepository.selectErc721Contracts(serviceId);

      for (const contract of nftContracts) {
        const address = contract.nftContractAddress;
        if (!address || !isAddress(address)) {
          console.warn(`Invalid contract address: ${address}`);
          continue;
        }
        const logs = await client.getLogs({
          address: address,
          fromBlock: startBlock,
          toBlock: 'latest',
          event: transferEventAbi[0],
        });

        if (logs.length > 0) {
          // 最も新しいTransferイベント
          const latestLog = logs[logs.length - 1];

          // indexedな tokenId は topics[3] に含まれる（0x-prefixed hex string）
          const tokenIdHex = latestLog.topics[3];
          const latestTokenId = BigInt(tokenIdHex);
          const nextTokenId = latestTokenId + 1n;

          updateTokens.push({
            serviceId: serviceId,
            contractId: contract.nftContractId,
            contractAddress: contract.nftContractAddress!,
            tokenId: Number(nextTokenId),
            nftType: contract.nftType!,
          });
        }
      }
    }

    logger.info({ message: 'Fetch updating NFT contract token IDs', data: updateTokens });
    this.nftContractsRepository.updateContractTokenId(updateTokens);
    logger.info({ message: 'Update next token ids' });

    const fromAddress = '0x0000000000000000000000000000000000000000';
    for (const [index, contractData] of updateTokens.entries()) {
      logger.info({
        message: `[${index}/${updateTokens.length}] start sync NFT metadata`,
        data: { current: contractData },
      });
      for (let tokenId = 0; tokenId < contractData.tokenId; tokenId++) {
        const execId = uuidV4();
        logger.info({
          message: `[${tokenId}/${contractData.tokenId - 1}] start sync NFT metadata`,
          id: execId,
        });
        // メタデータを取得して保存する処理

        const logs = await client.getLogs({
          address: contractData.contractAddress as Address,
          event: transferEventAbi[0],
          args: {
            from: fromAddress,
            tokenId: BigInt(tokenId),
          },
          fromBlock: startBlock,
          toBlock: 'latest',
        });
        if (logs.length === 0) {
          logger.warn({ message: 'No transaction logs are detected', id: execId });
          continue;
        }

        const txHash = logs[0].transactionHash;
        const toAddress = logs[0].args?.to;
        if (!toAddress) {
          logger.warn({ message: 'transaction logs are detected', data: { txHash, toAddress }, id: execId });
          continue;
        }
        const block = await client.getBlock({
          blockNumber: logs[0].blockNumber,
        });

        const transactionDate = new Date(Number(block.timestamp) * 1000);
        logger.info({ message: 'Transaction logs', data: { block, txHash, toAddress, transactionDate }, id: execId });

        // メタデータが存在しない場合は、NFTメタデータを挿入
        const metadata = await this.nftBaseMetadatasRepository.selectDataByContractAddress(
          contractData.contractAddress,
        );
        if (metadata) {
          const insertedData = await this.nftMetadatasRepository.selectMetadataByNftIdAndToken(
            metadata.base_metadata_id,
            tokenId,
          );
          if (!insertedData) {
            logger.info({ message: 'insert metadata', id: execId });
            await this.nftMetadatasRepository.insertMetadata({
              base_metadata_id: metadata.base_metadata_id,
              token_id: tokenId,
              metadata: metadata.metadata,
              transaction_id: undefined,
              queue_id: undefined,
            });
          }

          if (contractData.nftType !== NftType.MEMBERSHIP) {
            // NFTのキャッシュを確認して更新
            const nftCache = await this.nftsFirestoreRepository.selectNft(
              contractData.contractAddress,
              tokenId.toString(),
            );
            if (nftCache.empty) {
              // キャッシュがない場合はdeliveryを確認
              logger.info({ message: 'No NFT cache found', id: execId });

              const deliveryId = await this.deliveryNftsFirestoreRepository.selectDeliveryNft(txHash);
              if (deliveryId !== undefined) {
                logger.info({ message: 'Delivery cache found', id: execId });

                // deliveryが存在する場合は削除
                await this.deliveryNftsFirestoreRepository.deleteDeliveryNftById(deliveryId);
              }

              const account = await this.accountRepository.selectAccountIdByAddress(
                toAddress.toString(),
                contractData.serviceId,
              );
              logger.info({ message: 'Account found for the address', data: { account } });

              if (!account) {
                logger.warn({
                  message: 'No account found for the address',
                  data: { toAddress, serviceId: contractData.serviceId },
                  id: execId,
                });
                continue;
              }

              logger.info({
                message: 'Insert NFT metadata into Firestore',
                id: execId,
              });
              const now = new Date();
              await this.nftsFirestoreRepository.insertNft({
                contractAddress: contractData.contractAddress,
                tokenId: tokenId.toString(),
                amount: 1,
                chainId: config.chainId,
                accountId: account.account_id,
                serviceId: contractData.serviceId,
                contractType: contractData.nftType,
                metadataJson: metadata.metadata || {},
                metadataUri: `${config.metadataUrl}/${metadata.base_metadata_id}/${tokenId}`,
                metadataCachedAt: now.toISOString(),
                transactions: {
                  txHash: {
                    from: fromAddress,
                    to: toAddress?.toString() || '',
                    transactionDate: transactionDate.toISOString(),
                    transactionType: TransferTxType.MINT,
                    isConfirmed: true,
                  },
                },
                createdAt: transactionDate.toISOString(),
                updatedAt: now.toISOString(),
              });

              result.push({
                serviceId: contractData.serviceId,
                accountId: account.account_id,
                contractAddress: contractData.contractAddress,
                tokenId: tokenId,
              });
            }
          }
        }
        logger.info({ message: 'update transaction status', id: execId });
        // NFTのtransactionを更新
        await this.transactionsRepository.updateTransactionStatus(txHash, TransactionStatus.CONFIRMED);
      }
    }

    return result;
  }
}
