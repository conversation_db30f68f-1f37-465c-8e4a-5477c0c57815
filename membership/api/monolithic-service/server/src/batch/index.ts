import yargs from 'yargs';
import { hideBin } from 'yargs/helpers';
import { MetadataSyncBatchExecutor } from './executor/metadataSyncBatchExecutor';
import { NftContractsRepository } from '../repositories/nftContractsRepository';
import { logger } from '../utils/logger';
import { AccountRepository } from '../repositories/accountRepository';
import { MetadataFetchService } from '../services/metadataFetchService';
import { NftsFirestoreRepository } from '../repositories/nftsFirestoreRepository';
import { FirebaseComponent } from '../components/firebaseComponent';
import { TransactionComponent } from '../components/transactionComponent';
import { VaultKeyRepository } from '../repositories/vaultKeyRepository';
import { FixAndSyncNftMetadataBatchExecutor } from './executor/fixAndSyncNftMetadataBatchExecutor';
import { DeliveryNftsFirestoreRepository } from '../repositories/deliveryNftsFirestoreRepository';
import { TransactionsRepository } from '../repositories/transactionsRepository';
import { NftMetadatasRepository } from '../repositories/nftMetadatasRepository';
import { NftBaseMetadatasRepository } from '../repositories/nftBaseMetadatasRepository';
import { RetryNftConfirmExecutor } from './executor/retryNftConfirm';
import { TransactionQueuesRepository } from '../repositories/transactionQueuesRepository';

export async function runBatch(): Promise<void> {
  await yargs(hideBin(process.argv))
    .command(
      'batch:metadata-sync <jsonData>',
      'Sync metadata',
      (yargs) => {
        return yargs.positional('jsonData', {
          describe: 'JSON data for metadata sync',
          type: 'string',
        });
      },
      async (argv) => {
        try {
          const data = JSON.parse(argv.jsonData as string);
          if (!Array.isArray(data)) {
            throw new Error('Invalid data format: expected an array');
          }
          const result = await new MetadataSyncBatchExecutor(
            new NftContractsRepository(),
            new AccountRepository(),
            new MetadataFetchService(new TransactionComponent(new VaultKeyRepository())),
            new NftsFirestoreRepository(new FirebaseComponent()),
          ).execute(data);
          logger.info(`Metadata sync failed list: ${JSON.stringify(result)}`);
          process.exit(0);
        } catch (error) {
          logger.error(error);
          process.exit(1);
        }
      },
    )
    .command(
      'batch:fix-and-sync-nft <jsonData>',
      'Fix and sync NFT metadata',
      (yargs) => {
        return yargs.positional('jsonData', {
          describe: 'JSON data for metadata sync',
          type: 'string',
        });
      },
      async (argv) => {
        try {
          const data = JSON.parse(argv.jsonData as string);
          const result = await new FixAndSyncNftMetadataBatchExecutor(
            new NftContractsRepository(),
            new NftsFirestoreRepository(new FirebaseComponent()),
            new DeliveryNftsFirestoreRepository(new FirebaseComponent()),
            new TransactionsRepository(),
            new NftMetadatasRepository(),
            new NftBaseMetadatasRepository(),
            new AccountRepository(),
          ).execute(data);
          logger.info({ message: 'Metadata sync failed list', data: result });
          process.exit(0);
        } catch (error) {
          logger.error(error);
          process.exit(1);
        }
      },
    )
    .command(
      'batch:retry-nft-confirm <jsonData>',
      'Retry NFT confirmation',
      (yargs) => {
        return yargs.positional('jsonData', {
          describe: 'JSON data for retrying NFT confirmation',
          type: 'string',
        });
      },
      async (argv) => {
        try {
          const data = JSON.parse(argv.jsonData as string);
          const result = await new RetryNftConfirmExecutor(
            new NftsFirestoreRepository(new FirebaseComponent()),
            new DeliveryNftsFirestoreRepository(new FirebaseComponent()),
            new TransactionsRepository(),
            new TransactionQueuesRepository(),
            new NftMetadatasRepository(),
            new NftBaseMetadatasRepository(),
            new AccountRepository(),
          ).execute(data);
          logger.info(`Metadata sync inserted list: ${JSON.stringify(result)}`);
          process.exit(0);
        } catch (error) {
          logger.error(error);
          process.exit(1);
        }
      },
    )
    .demandCommand(1, 'You need to specify a command')
    .help()
    .parse();
}
