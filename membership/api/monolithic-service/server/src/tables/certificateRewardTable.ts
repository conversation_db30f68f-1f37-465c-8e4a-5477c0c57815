import { Selectable, Insertable, Updateable } from 'kysely';
import { CertificateType } from '../enum/certificateType';

export interface CertificateRewardsTable {
  reward_id: string;
  service_id: string;
  nft_contract_id: string;
  certificate_type: CertificateType;
  status_certificate_rank?: number;
}

export type CertificateRewardEntity = Selectable<CertificateRewardsTable>;
export type InsertableCertificateRewardRow = Insertable<CertificateRewardsTable>;
export type UpdateableCertificateRewardRow = Updateable<CertificateRewardsTable>;
