import { Insertable, Selectable, Updateable } from 'kysely';

export interface LocationCheckinActionsTable {
  action_id: string;
  service_id: string;
  geofence_id: string;
}

export type LocationCheckinActionEntity = Selectable<LocationCheckinActionsTable>;
export type InsertableLocationCheckinActionRow = Insertable<LocationCheckinActionsTable>;
export type UpdateableLocationCheckinActionRow = Updateable<LocationCheckinActionsTable>;
