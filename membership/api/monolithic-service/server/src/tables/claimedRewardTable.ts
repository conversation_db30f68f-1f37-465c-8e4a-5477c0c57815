import { Selectable, Insertable } from 'kysely';
import { RewardUsageStatus } from '../enum/rewardUsageStatus';

export interface ClaimedRewardsTable {
  account_id: string;
  reward_component_id: string;
  service_id: string;
  reward_usage_status: RewardUsageStatus;
  transaction_id?: string;
  queue_id?: string;
  operation_id?: string;
  claim_date: Date;
}

export type ClaimedRewardEntity = Selectable<ClaimedRewardsTable>;
export type InsertableClaimedRewardRow = Insertable<ClaimedRewardsTable>;
