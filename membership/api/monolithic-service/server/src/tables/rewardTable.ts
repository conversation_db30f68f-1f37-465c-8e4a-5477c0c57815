import { Insertable, Selectable, Updateable } from 'kysely';

export interface RewardsTable {
  reward_id: string;
  service_id: string;
  reward_cover_image_url: string;
  reward_thumbnail_image_url: string;
  order_index: number;
}

export type RewardEntity = Selectable<RewardsTable>;
export type InsertableRewardRow = Insertable<RewardsTable>;
export type UpdateableRewardRow = Updateable<RewardsTable>;
