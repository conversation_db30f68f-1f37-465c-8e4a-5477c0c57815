import { Selectable, Insertable, Updateable } from 'kysely';
import { PointType } from '../enum/pointType';

export interface PointComponentsTable {
  point_component_id: string;
  service_id: string;
  amount: number;
  point_type: PointType;
  expires_on?: Date | null;
}

export type PointComponentEntity = Selectable<PointComponentsTable>;
export type InsertablePointComponentRow = Insertable<PointComponentsTable>;
export type UpdateablePointComponentRow = Updateable<PointComponentsTable>;
