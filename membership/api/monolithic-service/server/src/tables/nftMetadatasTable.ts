import { Insertable, Selectable, Updateable } from 'kysely';

export interface NftMetadatasTable {
  base_metadata_id: string;
  token_id: number;
  metadata: object;
  transaction_id?: string;
  queue_id?: string;
}

export type NftMetadatasEntity = Selectable<NftMetadatasTable>;
export type InsertableNftMetadatasRow = Insertable<NftMetadatasTable>;
export type UpdateableNftMetadatasRow = Updateable<NftMetadatasTable>;
