import { Insertable, Selectable, Updateable } from 'kysely';

export interface DigitalContentRewardsTable {
  reward_id: string;
  service_id: string;
  nft_contract_id: string;
}

export type DigitalContentRewardEntity = Selectable<DigitalContentRewardsTable>;
export type InsertableDigitalContentRewardRow = Insertable<DigitalContentRewardsTable>;
export type UpdateableDigitalContentRewardRow = Updateable<DigitalContentRewardsTable>;
