import { Selectable, Insertable } from 'kysely';

export interface AccountsTable {
  account_id: string;
  service_id: string;
  user_id: string;
  membership_id: number;
  membership_metadata_url: string;
  display_name?: string;
  profile_image_url?: string;
  token_bound_account_address?: string;
  status: string;
  transaction_id: string;
  queue_id: string;
  created_at: Date;
  updated_at: Date;
  last_login_at: Date;
}

export type AccountEntity = Selectable<AccountsTable>;
export type InsertableAccountRow = Insertable<AccountsTable>;
