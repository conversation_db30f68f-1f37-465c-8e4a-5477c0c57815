import { Insertable, Selectable, Updateable } from 'kysely';

export interface GeofencesTable {
  geofence_id: string;
  service_id: string;
  name: string;
  description?: string;
  latitude: number;
  longitude: number;
  radius_meters: number;
  is_active: boolean;
  created_at: Date;
  updated_at: Date;
}

export type GeofencesTableEntity = Selectable<GeofencesTable>;
export type InsertableGeofencesTableRow = Insertable<GeofencesTable>;
export type UpdateableGeofencesTableRow = Updateable<GeofencesTable>;
