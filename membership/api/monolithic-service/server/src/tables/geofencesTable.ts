import { Insertable, Selectable, Updateable } from 'kysely';
import { Point, Polygon } from 'geojson';

export interface GeofencesTable {
  geofence_id: string;
  service_id: string;
  center_pin_name?: string;
  geofence_type: string;
  circle_radius?: string;
  circle_geometry?: string;
  polygon_geometry?: string;
}

export type GeofencesTableEntity = Selectable<GeofencesTable>;
export type InsertableGeofencesTableRow = Insertable<GeofencesTable>;
export type UpdateableGeofencesTableRow = Updateable<GeofencesTable>;
