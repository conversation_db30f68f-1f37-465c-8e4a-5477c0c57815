import { Insertable, Selectable, Updateable } from 'kysely';
import { QuestionType } from '../enum/questionTypeEnum';

export interface QuestionnaireQuestionsTable {
  question_id: string;
  theme_id: string;
  service_id: string;
  question_number: number;
  question_type: QuestionType;
  is_required: boolean;
  question_image_url?: string;
  answer_point?: number;
}

export type QuestionnaireQuestionEntity = Selectable<QuestionnaireQuestionsTable>;
export type InsertableQuestionnaireQuestionRow = Insertable<QuestionnaireQuestionsTable>;
export type UpdateableQuestionnaireQuestionsRow = Updateable<QuestionnaireQuestionsTable>;
