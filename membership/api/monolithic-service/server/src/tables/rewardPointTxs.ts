import { Insertable, Selectable, Updateable } from 'kysely';
import { PointOpsActor } from '../enum/pointOpsActor';
import { PointOpsType } from '../enum/pointOpsType';
import { PointTxDetail } from '../enum/pointTxDetail';

export interface RewardPointTxsTable {
  reward_point_tx_id: string;
  service_id: string;
  account_id: string;
  amount: number;
  ops_type: PointOpsType;
  tx_by: PointOpsActor;
  tx_detail: PointTxDetail;
  tx_extra?: string;
  expires_on: Date;
  created_at: Date;
}

export type RewardPointTxEntity = Selectable<RewardPointTxsTable>;
export type InsertableRewardPointTxRow = Insertable<RewardPointTxsTable>;
export type UpdateableRewardPointTxRow = Updateable<RewardPointTxsTable>;
