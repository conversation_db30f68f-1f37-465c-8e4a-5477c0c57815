import { Selectable, Insertable, Updateable } from 'kysely';
import { CertificateType } from '../enum/certificateType';
import { RewardType } from '../enum/rewardType';

export interface NftComponentsTable {
  nft_component_id: string;
  service_id: string;
  nft_contract_id: string;
  nft_contract_type: RewardType;
  token_id?: number | null;
  certificate_type?: CertificateType | null;
  status_certificate_rank?: number | null;
}

export type NftComponentEntity = Selectable<NftComponentsTable>;
export type InsertableNftComponentRow = Insertable<NftComponentsTable>;
export type UpdateableNftComponentRow = Updateable<NftComponentsTable>;
