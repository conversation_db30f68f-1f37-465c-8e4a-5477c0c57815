import { Insertable, Selectable, Updateable } from 'kysely';
import { CustomFieldType } from '../enum/customFieldType';

export interface ServiceCustomFieldsTable {
  custom_field_id: string;
  service_id: string;
  field_key: string;
  version: number;
  type: CustomFieldType;
  default_value?: string;
  max_length?: number;
  min_length?: number;
  unique: boolean;
  verify: boolean;
  optional: boolean;
  sort_order: number;
  created_at: Date;
}

export type ServiceCustomFieldEntity = Selectable<ServiceCustomFieldsTable>;
export type InsertableServiceCustomFieldRow = Insertable<ServiceCustomFieldsTable>;
export type UpdateableServiceCustomFieldRow = Updateable<ServiceCustomFieldsTable>;