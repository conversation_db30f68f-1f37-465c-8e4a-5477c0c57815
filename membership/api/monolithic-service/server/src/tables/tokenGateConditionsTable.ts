import { Insertable, Selectable, Updateable } from 'kysely';

export interface TokenGateConditionsTable {
  token_gate_condition_id: string;
  token_gate_id: string;
  set_number: number;
  token_hash: string;
}

export type TokenGateConditionEntity = Selectable<TokenGateConditionsTable>;
export type InsertableTokenGateConditionRow = Insertable<TokenGateConditionsTable>;
export type UpdateableTokenGateConditionRow = Updateable<TokenGateConditionsTable>;
