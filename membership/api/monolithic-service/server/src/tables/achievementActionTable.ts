import { Insertable, Selectable, Updateable } from 'kysely';

export interface AchievementActionsTable {
  action_id: string;
  service_id: string;
  reward_id: string;
  milestone: number;
  status_rank: number;
}

export type AchievementActionEntity = Selectable<AchievementActionsTable>;
export type InsertableAchievementActionRow = Insertable<AchievementActionsTable>;
export type UpdateableAchievementActionRow = Updateable<AchievementActionsTable>;