import { Insertable, Selectable, Updateable } from 'kysely';
import { LanguageCode } from '../../enum/languageCode';

export interface QuestionnaireThemeTranslationsTable {
  theme_id: string;
  service_id: string;
  language: LanguageCode;
  theme_title?: string;
  theme_description?: string;
}

export type QuestionnaireThemeTranslationEntity = Selectable<QuestionnaireThemeTranslationsTable>;
export type InsertableQuestionnaireThemeTranslationRow = Insertable<QuestionnaireThemeTranslationsTable>;
export type UpdateableQuestionnaireThemeTranslationRow = Updateable<QuestionnaireThemeTranslationsTable>;