import { Insertable, Selectable, Updateable } from 'kysely';
import { LanguageCode } from '../../enum/languageCode';

export interface TokenGateTranslationsTable {
  token_gate_id: string;
  service_id: string;
  language: LanguageCode;
  gate_name: string;
  gate_description: string;
}

export type TokenGateTranslationEntity = Selectable<TokenGateTranslationsTable>;
export type InsertableTokenGateTranslationRow = Insertable<TokenGateTranslationsTable>;
export type UpdateableTokenGateTranslationRow = Updateable<TokenGateTranslationsTable>;
