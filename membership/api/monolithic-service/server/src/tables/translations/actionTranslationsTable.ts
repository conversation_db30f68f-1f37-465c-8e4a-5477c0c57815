import { Selectable, Insertable, Updateable } from 'kysely';
import { LanguageCode } from '../../enum/languageCode';

export interface ActionTranslationsTable {
  action_id: string;
  service_id: string;
  language: LanguageCode;
  action_title: string;
  action_description: string;
  action_label?: string;
}

export type ActionTranslationEntity = Selectable<ActionTranslationsTable>;
export type InsertableActionTranslationRow = Insertable<ActionTranslationsTable>;
export type UpdateableActionTranslationRow = Updateable<ActionTranslationsTable>;
