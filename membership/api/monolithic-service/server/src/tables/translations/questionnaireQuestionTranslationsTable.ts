import { Insertable, Selectable, Updateable } from 'kysely';
import { LanguageCode } from '../../enum/languageCode';

export interface QuestionnaireQuestionTranslationsTable {
  question_id: string;
  service_id: string;
  language: LanguageCode;
  question_title: string;
  question_detail?: string;
  question_extra: object;
  correct_data?: string;
  correct_data_validation?: string;
}

export type QuestionnaireQuestionTranslationEntity = Selectable<QuestionnaireQuestionTranslationsTable>;
export type InsertableQuestionnaireQuestionTranslationRow = Insertable<QuestionnaireQuestionTranslationsTable>;
export type UpdateableQuestionnaireQuestionTranslationRow = Updateable<QuestionnaireQuestionTranslationsTable>;
