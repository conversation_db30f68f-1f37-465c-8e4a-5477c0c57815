import { Selectable, Insertable, Updateable } from 'kysely';
import { ActionType } from '../enum/actionType';

export interface ActionsTable {
  action_id: string;
  service_id: string;
  action_cover_image_url: string;
  action_thumbnail_image_url: string;
  action_available_start_date: Date;
  action_available_end_date: Date;
  action_type: ActionType;
  order_index: number;
}

export type ActionEntity = Selectable<ActionsTable>;
export type InsertableActionRow = Insertable<ActionsTable>;
export type UpdateableActionRow = Updateable<ActionsTable>;

export interface ActionWithTranslations extends ActionEntity {
  action_title: string;
  action_description: string;
  action_label?: string;
}
