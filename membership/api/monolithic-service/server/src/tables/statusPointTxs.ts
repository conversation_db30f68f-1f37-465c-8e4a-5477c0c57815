import { Insertable, Selectable, Updateable } from 'kysely';
import { PointOpsActor } from '../enum/pointOpsActor';
import { PointOpsType } from '../enum/pointOpsType';
import { PointTxDetail } from '../enum/pointTxDetail';

export interface StatusPointTxsTable {
  status_point_tx_id: string;
  service_id: string;
  account_id: string;
  amount: number;
  ops_type: PointOpsType;
  tx_by: PointOpsActor;
  tx_detail: PointTxDetail;
  tx_extra?: string;
  created_at: Date;
}

export type StatusPointTxEntity = Selectable<StatusPointTxsTable>;
export type InsertableStatusPointTxRow = Insertable<StatusPointTxsTable>;
export type UpdateableStatusPointTxRow = Updateable<StatusPointTxsTable>;
