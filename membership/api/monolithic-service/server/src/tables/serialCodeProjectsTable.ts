import { Insertable, Selectable, Updateable } from 'kysely';
import { SerialCodeProjectStatus } from '../enum/serialCodeProjectStatus';

export interface SerialCodeProjectsTable {
  serial_code_project_id: string;
  service_id: string;
  reward_id?: string;
  slug: string;
  hash_key: string;
  status: SerialCodeProjectStatus;
  start_at: Date;
  end_at: Date;
  created_at: Date;
  updated_at: Date;
}

export type SerialCodeProjectsEntity = Selectable<SerialCodeProjectsTable>;
export type InsertableSerialCodeProjectsRow = Insertable<SerialCodeProjectsTable>;
export type UpdateableRow = Updateable<SerialCodeProjectsTable>;
