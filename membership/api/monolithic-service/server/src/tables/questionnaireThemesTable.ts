import { Insertable, Selectable, Updateable } from 'kysely';
import { QuestionnaireThemeTranslationEntity } from './translations/questionnaireThemeTranslationsTable';

export interface QuestionnaireThemesTable {
  service_id: string;
  theme_id: string;
  questionnaire_id: string;
  theme_thumbnail_image_url?: string;
  theme_cover_image_url?: string;
  theme_number: number;
  theme_time_limit_seconds?: number;
}

export type QuestionnaireThemeEntity = Selectable<QuestionnaireThemesTable>;
export type InsertableQuestionnaireThemeRow = Insertable<QuestionnaireThemesTable>;
export type UpdateableQuestionnaireThemesRow = Updateable<QuestionnaireThemesTable>;

export interface QuestionnaireThemeWithTranslations extends QuestionnaireThemeEntity {
  translations: QuestionnaireThemeTranslationEntity[];
}
