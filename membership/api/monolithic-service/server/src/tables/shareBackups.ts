import { Insertable, Selectable, Updateable } from 'kysely';

export interface ShareBackupsTable {
  share_backup_id: string;
  user_id: string;
  polynomial_id: string;
  share_index: string;
  share: string;
  created_at: string;
  is_active: boolean;
}

export type ShareBackupEntity = Selectable<ShareBackupsTable>;
export type InsertableShareBackupRow = Insertable<ShareBackupsTable>;
export type UpdateableShareBackupRow = Updateable<ShareBackupsTable>;