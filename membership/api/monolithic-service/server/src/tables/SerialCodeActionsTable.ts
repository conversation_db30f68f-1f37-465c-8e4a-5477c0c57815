import { Insertable, Selectable, Updateable } from 'kysely';

export interface SerialCodeActionsTable {
  action_id: string;
  service_id: string;
  serial_code_project_id: string;
}

export type SerialCodeActionEntity = Selectable<SerialCodeActionsTable>;
export type InsertableSerialCodeActionRow = Insertable<SerialCodeActionsTable>;
export type UpdateableSerialCodeActionRow = Updateable<SerialCodeActionsTable>;