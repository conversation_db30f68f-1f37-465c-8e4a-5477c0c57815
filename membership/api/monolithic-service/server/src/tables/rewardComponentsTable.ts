import { Selectable, Insertable, Updateable } from 'kysely';
import { RewardComponentType } from '../enum/rewardComponentType';

export interface RewardComponentsTable {
  reward_component_id: string;
  service_id: string;
  reward_id: string;
  reward_component_type: RewardComponentType;
  point_component_id?: string | null;
  nft_component_id?: string | null;
}

export type RewardComponentEntity = Selectable<RewardComponentsTable>;
export type InsertableRewardComponentRow = Insertable<RewardComponentsTable>;
export type UpdateableRewardComponentRow = Updateable<RewardComponentsTable>;
