import { Pool } from 'pg';
import { Kysely, PostgresDialect } from 'kysely';
import { UsersTable } from '../tables/userTable';
import { AuthProvidersTable } from '../tables/authProviderTable';
import { AccountsTable } from '../tables/accountTable';
import { ActionsTable } from '../tables/actionTable';
import { QuestionnaireActionsTable } from '../tables/questionnaireActionTable';
import { OnlineCheckinActionsTable } from '../tables/onlineCheckinActionTable';
import { AchievementActionsTable } from '../tables/achievementActionTable';
import { GlobalNotificationsTable } from '../tables/globalNotifications';
import { QuestsTable } from '../tables/questTable';
import { QuestActionsTable } from '../tables/questActionTable';
import { RewardsTable } from '../tables/rewardTable';
import { QuestRewardsTable } from '../tables/questRewardTable';
import { QuestActivitiesTable } from '../tables/questActivityTable';
import { CouponRewardsTable } from '../tables/couponRewardTable';
import { DigitalContentRewardsTable } from '../tables/digitalContentRewardTable';
import { CertificateRewardsTable } from '../tables/certificateRewardTable';
import { ClaimedRewardsTable } from '../tables/claimedRewardTable';
import { ActionActivitiesTable } from '../tables/actionActivitesTable';
import { AccountNotificationsTable } from '../tables/accountNotifications';
import { TokenBoundAccountRegistriesTable } from '../tables/tokenBoundAccountRegistriesTable';
import { TokenBoundAccountImplementationsTable } from '../tables/tokenBoundAccountImplementationTable';
import { NftContractTypesTable } from '../tables/nftContractTypeTable';
import { NftBaseMetadatasTable } from '../tables/nftBaseMetadatasTable';
import { NftMetadatasTable } from '../tables/nftMetadatasTable';
import { UserOperationQueuesTable } from '../tables/userOperationQueuesTable';
import { VaultTransactionQueuesTable } from '../tables/vaultTransactionQueuesTable';
import { TransactionsTable } from '../tables/transactionsTable';
import { TransactionQueuesTable } from '../tables/transactionQueuesTable';
import { AttemptTransactionsTable } from '../tables/attemptTransactionsTable';
import { NftContractsTable } from '../tables/nftContractsTable';
import { PlansTable } from '../tables/plansTable';
import { TenantsTable } from '../tables/tenantsTable';
import { ServicesTable } from '../tables/servicesTable';
import { AdminsTable } from '../tables/adminsTable';
import { VaultKeysTable } from '../tables/vaultKeyTable';
import { QrCheckinActionsTable } from '../tables/qrCheckinActionTable';
import { ProductsTable } from '../tables/productsTable';
import { CheckoutsTable } from '../tables/checkoutTable';
import { CustomFieldsTable } from '../tables/customFieldsTable';
import { ProductCustomFieldsTable } from '../tables/productCustomFieldsTable';
import { GeofencesTable } from '../tables/geofencesTable';
import { LocationCheckinActionsTable } from '../tables/locationCheckinActionTable';
import { ContentPurchaseActionsTable } from '../tables/contentPurchaseActionTable';
import { QuestionnaireThemesTable } from '../tables/questionnaireThemesTable';
import { QuestionnaireResultRanksTable } from '../tables/questionnaireResultRanksTable';
import { QuestionnaireQuestionsTable } from '../tables/questionnaireQuestionsTable';
import { QuestionnaireQuestionAnswersTable } from '../tables/questionnaireQuestionAnswersTable';
import { QuestionnaireResultAnswersTable } from '../tables/questionnaireResultAnswersTable';
import { QuestionnairesTable } from '../tables/questionnairesTable';
import { SerialCodeProjectsTable } from '../tables/serialCodeProjectsTable';
import { SerialCodesTable } from '../tables/serialCodesTable';
import { AccountSerialCodesTable } from '../tables/accountSerialCodesTable';
import { RewardTranslationsTable } from '../tables/translations/rewardTranslationsTable';
import { CustomFieldTranslationsTable } from '../tables/translations/customFieldTranslationsTable';
import { TenantTranslationsTable } from '../tables/translations/tenantTranslationsTable';
import { PlanTranslationsTable } from '../tables/translations/planTranslationsTable';
import { ProductTranslationsTable } from '../tables/translations/productTranslationsTable';
import { QuestTranslationsTable } from '../tables/translations/questTranslationsTable';
import { QuestionnaireQuestionTranslationsTable } from '../tables/translations/questionnaireQuestionTranslationsTable';
import { ActionTranslationsTable } from '../tables/translations/actionTranslationsTable';
import { ServiceTranslationsTable } from '../tables/translations/serviceTranslationsTable';
import { QuestionnaireThemeTranslationsTable } from '../tables/translations/questionnaireThemeTranslationsTable';
import { QuestionnaireResultRankTranslationsTable } from '../tables/translations/questionnaireResultRankTranslationsTable';
import { SerialCodeProjectTranslationsTable } from '../tables/translations/serialCodeProjectTranslationsTable';
import { NotificationsTable } from '../tables/notificationsTable';
import { NotificationTranslationsTable } from '../tables/notificationTranslationsTable';
import { StatusPointTxsTable } from '../tables/statusPointTxs';
import { RewardPointTxsTable } from '../tables/rewardPointTxs';
import { SerialCodeActionsTable } from '../tables/SerialCodeActionsTable';

// TODO: Edit values for database, host, user, port so that they are viewed from application configuration (and env variables)
const dialect = new PostgresDialect({
  pool: new Pool({
    host: process.env.DB_HOST,
    database: process.env.DB_DATABASE,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    port: Number(process.env.DB_PORT) || 5432,
    max: 10,
  }),
});

export interface Database {
  users: UsersTable;
  auth_providers: AuthProvidersTable;
  quests: QuestsTable;
  quest_rewards: QuestRewardsTable;
  rewards: RewardsTable;
  actions: ActionsTable;
  quest_actions: QuestActionsTable;
  accounts: AccountsTable;
  certificate_rewards: CertificateRewardsTable;
  quest_activities: QuestActivitiesTable;
  claimed_rewards: ClaimedRewardsTable;
  achievement_actions: AchievementActionsTable;
  online_checkin_actions: OnlineCheckinActionsTable;
  coupon_rewards: CouponRewardsTable;
  digital_content_rewards: DigitalContentRewardsTable;
  action_activities: ActionActivitiesTable;
  global_notifications: GlobalNotificationsTable;
  account_notifications: AccountNotificationsTable;
  token_bound_account_registries: TokenBoundAccountRegistriesTable;
  nft_contracts: NftContractsTable;
  vault_transaction_queues: VaultTransactionQueuesTable;
  transactions: TransactionsTable;
  transaction_queues: TransactionQueuesTable;
  attempt_transactions: AttemptTransactionsTable;
  token_bound_account_implementations: TokenBoundAccountImplementationsTable;
  nft_contract_types: NftContractTypesTable;
  user_operation_queues: UserOperationQueuesTable;
  tokenBoundAccountRegistries: TokenBoundAccountRegistriesTable;
  vaultTransactionQueues: VaultTransactionQueuesTable;
  tokenBoundAccountImplementations: TokenBoundAccountImplementationsTable;
  nftContractTypesTable: NftContractTypesTable;
  userOperationQueues: UserOperationQueuesTable;
  nft_metadatas: NftMetadatasTable;
  nft_base_metadatas: NftBaseMetadatasTable;
  admins: AdminsTable;
  tenants: TenantsTable;
  plans: PlansTable;
  services: ServicesTable;
  vault_keys: VaultKeysTable;
  qr_checkin_actions: QrCheckinActionsTable;
  products: ProductsTable;
  checkouts: CheckoutsTable;
  custom_fields: CustomFieldsTable;
  product_custom_fields: ProductCustomFieldsTable;
  geofences: GeofencesTable;
  location_checkin_actions: LocationCheckinActionsTable;
  content_purchase_actions: ContentPurchaseActionsTable;
  questionnaires: QuestionnairesTable;
  questionnaire_themes: QuestionnaireThemesTable;
  questionnaire_result_ranks: QuestionnaireResultRanksTable;
  questionnaire_result_answers: QuestionnaireResultAnswersTable;
  questionnaire_questions: QuestionnaireQuestionsTable;
  questionnaire_question_answers: QuestionnaireQuestionAnswersTable;
  questionnaire_actions: QuestionnaireActionsTable;
  serial_code_projects: SerialCodeProjectsTable;
  serial_codes: SerialCodesTable;
  account_serial_codes: AccountSerialCodesTable;
  serial_code_actions: SerialCodeActionsTable

  // Translations
  action_translations: ActionTranslationsTable;
  custom_field_translations: CustomFieldTranslationsTable;
  tenant_translations: TenantTranslationsTable;
  plan_translations: PlanTranslationsTable;
  product_translations: ProductTranslationsTable;
  quest_translations: QuestTranslationsTable;
  reward_translations: RewardTranslationsTable;
  questionnaire_question_translations: QuestionnaireQuestionTranslationsTable;
  service_translations: ServiceTranslationsTable;
  questionnaire_theme_translations: QuestionnaireThemeTranslationsTable;
  questionnaire_result_rank_translations: QuestionnaireResultRankTranslationsTable;
  serial_code_project_translations: SerialCodeProjectTranslationsTable;
  notifications: NotificationsTable;
  notification_translations: NotificationTranslationsTable;
  reward_point_txs: RewardPointTxsTable;
  status_point_txs: StatusPointTxsTable;
}

export const db = new Kysely<Database>({
  dialect,
});
