--
-- PostgreSQL database dump
--

-- Dumped from database version 16.8
-- Dumped by pg_dump version 16.9 (Homebrew)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: language_code; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.language_code AS ENUM (
    'de',
    'en-GB',
    'en-US',
    'es',
    'fr',
    'it',
    'ja',
    'ko',
    'pt',
    'ru',
    'th',
    'zh-Hans',
    'zh-Hant'
);


SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: account_notifications; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.account_notifications (
    account_notification_id character varying(128) NOT NULL,
    account_id character varying(128) NOT NULL,
    service_id character varying(128) NOT NULL,
    notification_title character varying(256),
    notification_text character varying(2048),
    notification_id character varying(128) NOT NULL
);

ALTER TABLE ONLY public.account_notifications FORCE ROW LEVEL SECURITY;


--
-- Name: account_serial_codes; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.account_serial_codes (
    account_serial_code_id character varying(128) NOT NULL,
    account_id character varying(128) NOT NULL,
    serial_code_id character varying(128) NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


--
-- Name: accounts; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.accounts (
    account_id character varying(128) NOT NULL,
    service_id character varying(128) NOT NULL,
    user_id character varying(128) NOT NULL,
    membership_id integer,
    display_name character varying(256),
    profile_image_url character varying(1024),
    token_bound_account_address character varying(128),
    transaction_id character varying(128),
    status character varying(128) NOT NULL,
    created_at timestamp without time zone,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    queue_id character varying(128),
    CONSTRAINT accounts_status_check CHECK (((status)::text = ANY ((ARRAY['DELETED'::character varying, 'ACTIVE'::character varying])::text[])))
);

ALTER TABLE ONLY public.accounts FORCE ROW LEVEL SECURITY;


--
-- Name: achievement_actions; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.achievement_actions (
    action_id character varying(128) NOT NULL,
    service_id character varying(128) NOT NULL,
    reward_id character varying(128) NOT NULL,
    quest_completion_goal integer,
    status_rank integer
);

ALTER TABLE ONLY public.achievement_actions FORCE ROW LEVEL SECURITY;


--
-- Name: action_activities; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.action_activities (
    account_id character varying(128) NOT NULL,
    action_id character varying(128) NOT NULL,
    service_id character varying(128) NOT NULL,
    action_activity_status character varying(128) NOT NULL,
    finish_date timestamp without time zone,
    CONSTRAINT action_activities_action_activity_status_check CHECK (((action_activity_status)::text = ANY ((ARRAY['PROCEEDING'::character varying, 'DONE'::character varying, 'EXPIRED'::character varying])::text[])))
);

ALTER TABLE ONLY public.action_activities FORCE ROW LEVEL SECURITY;


--
-- Name: action_translations; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.action_translations (
    action_id character varying NOT NULL,
    service_id character varying NOT NULL,
    language public.language_code NOT NULL,
    action_title text,
    action_description text
);

ALTER TABLE ONLY public.action_translations FORCE ROW LEVEL SECURITY;


--
-- Name: actions; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.actions (
    action_id character varying(128) NOT NULL,
    service_id character varying(128) NOT NULL,
    action_cover_image_url character varying(1024),
    action_thumbnail_image_url character varying(1024),
    action_label character varying(128),
    action_available_start_date timestamp without time zone NOT NULL,
    action_available_end_date timestamp without time zone NOT NULL,
    action_type character varying(128) NOT NULL,
    geofence_id character varying(128),
    order_index integer DEFAULT 0 NOT NULL,
    CONSTRAINT actions_action_type_check CHECK (((action_type)::text = ANY ((ARRAY['ACHIEVEMENT'::character varying, 'QR-CHECKIN'::character varying, 'ONLINE-CHECKIN'::character varying, 'QUESTIONNAIRE'::character varying, 'CONTENTS-PURCHASE'::character varying, 'SERIAL-CODE'::character varying])::text[])))
);

ALTER TABLE ONLY public.actions FORCE ROW LEVEL SECURITY;


--
-- Name: admins; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.admins (
    admin_id character varying(128) NOT NULL,
    tenant_id character varying(128) NOT NULL,
    admin_name character varying(516) NOT NULL
);


--
-- Name: attempt_transactions; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.attempt_transactions (
    attempt_id character varying(128) NOT NULL,
    queue_id character varying(128) NOT NULL,
    transaction_id character varying(128) NOT NULL,
    created_date timestamp without time zone NOT NULL
);


--
-- Name: auth_providers; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.auth_providers (
    provider_id character varying(128) NOT NULL,
    user_id character varying(128) NOT NULL,
    service_id character varying(128) NOT NULL,
    provider_uid character varying(128) NOT NULL,
    provider_name character varying(128) NOT NULL
);

ALTER TABLE ONLY public.auth_providers FORCE ROW LEVEL SECURITY;


--
-- Name: certificate_rewards; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.certificate_rewards (
    reward_id character varying(128) NOT NULL,
    service_id character varying(128) NOT NULL,
    nft_contract_id character varying(128),
    token_id integer,
    certificate_type character varying(128) NOT NULL,
    status_certificate_rank integer,
    CONSTRAINT certificate_rewards_certificate_type_check CHECK (((certificate_type)::text = ANY ((ARRAY['STANDARD'::character varying, 'STATUS'::character varying])::text[])))
);

ALTER TABLE ONLY public.certificate_rewards FORCE ROW LEVEL SECURITY;


--
-- Name: checkouts; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.checkouts (
    checkout_session_id character varying(128) NOT NULL,
    stripe_product_id character varying(128) NOT NULL,
    account_id character varying(128) NOT NULL,
    service_id character varying(128) NOT NULL,
    checkout_status character varying(128) NOT NULL,
    CONSTRAINT checkouts_checkout_status_check CHECK (((checkout_status)::text = ANY ((ARRAY['STARTED'::character varying, 'FULFILLING'::character varying, 'COMPLETED'::character varying, 'FAILED'::character varying, 'EXPIRED'::character varying])::text[])))
);

ALTER TABLE ONLY public.checkouts FORCE ROW LEVEL SECURITY;


--
-- Name: claimed_rewards; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.claimed_rewards (
    account_id character varying(128) NOT NULL,
    reward_id character varying(128) NOT NULL,
    service_id character varying(128) NOT NULL,
    reward_usage_status character varying(128) NOT NULL,
    transaction_id character varying(128),
    operation_id character varying(128),
    claim_date timestamp without time zone,
    queue_id character varying(128),
    CONSTRAINT claimed_rewards_reward_usage_status_check CHECK (((reward_usage_status)::text = ANY ((ARRAY['ACTIVE'::character varying, 'USED'::character varying, 'OPERATING'::character varying, 'BURNED'::character varying, 'MINTING'::character varying])::text[])))
);

ALTER TABLE ONLY public.claimed_rewards FORCE ROW LEVEL SECURITY;


--
-- Name: content_purchase_actions; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.content_purchase_actions (
    action_id character varying(128) NOT NULL,
    service_id character varying(128) NOT NULL,
    content_purchase_url character varying(1024)
);

ALTER TABLE ONLY public.content_purchase_actions FORCE ROW LEVEL SECURITY;


--
-- Name: coupon_rewards; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.coupon_rewards (
    reward_id character varying(128) NOT NULL,
    service_id character varying(128) NOT NULL,
    nft_contract_id character varying(128),
    token_id integer
);

ALTER TABLE ONLY public.coupon_rewards FORCE ROW LEVEL SECURITY;


--
-- Name: custom_field_translations; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.custom_field_translations (
    stripe_custom_field_id character varying NOT NULL,
    service_id character varying NOT NULL,
    language public.language_code NOT NULL,
    field jsonb
);

ALTER TABLE ONLY public.custom_field_translations FORCE ROW LEVEL SECURITY;


--
-- Name: custom_fields; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.custom_fields (
    stripe_custom_field_id character varying(128) NOT NULL,
    service_id character varying(128) NOT NULL
);

ALTER TABLE ONLY public.custom_fields FORCE ROW LEVEL SECURITY;


--
-- Name: digital_content_rewards; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.digital_content_rewards (
    reward_id character varying(128) NOT NULL,
    service_id character varying(128) NOT NULL,
    nft_contract_id character varying(128),
    token_id integer
);

ALTER TABLE ONLY public.digital_content_rewards FORCE ROW LEVEL SECURITY;


--
-- Name: geofences; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.geofences (
    geofence_id character varying(128) NOT NULL,
    service_id character varying(128) NOT NULL,
    center_coordinate_latitude character varying(128),
    center_coordinate_longtitude character varying(128),
    center_pin_name character varying(128),
    geofence_radius character varying(128)
);

ALTER TABLE ONLY public.geofences FORCE ROW LEVEL SECURITY;


--
-- Name: global_notifications; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.global_notifications (
    global_notification_id character varying(128) NOT NULL,
    service_id character varying(128) NOT NULL,
    notification_title character varying(256),
    notification_text character varying(2048),
    notification_id character varying(128) NOT NULL
);

ALTER TABLE ONLY public.global_notifications FORCE ROW LEVEL SECURITY;


--
-- Name: kysely_migration; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.kysely_migration (
    name character varying(255) NOT NULL,
    "timestamp" character varying(255) NOT NULL
);


--
-- Name: kysely_migration_lock; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.kysely_migration_lock (
    id character varying(255) NOT NULL,
    is_locked integer DEFAULT 0 NOT NULL
);


--
-- Name: nft_base_metadatas; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.nft_base_metadatas (
    base_metadata_id character varying(128) NOT NULL,
    service_id character varying(128) NOT NULL,
    metadata jsonb NOT NULL,
    contract_address character varying(128),
    token_id integer
);


--
-- Name: nft_contract_types; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.nft_contract_types (
    nft_contract_type_id character varying(128) NOT NULL,
    nft_contract_type_name character varying(128) NOT NULL,
    nft_contract_type_detail character varying(2024) NOT NULL,
    nft_type character varying(32) NOT NULL,
    nft_contract_abi jsonb,
    nft_contract_binary text,
    nft_contract_address character varying(128),
    CONSTRAINT nft_contract_types_nft_type_check CHECK (((nft_type)::text = ANY ((ARRAY['MODULAR_CORE'::character varying, 'MODULE_BULKMINT'::character varying, 'MODULE_BULKCREATEACCOUNT'::character varying, 'TICKET'::character varying, 'MEMBERSHIP'::character varying, 'CERTIFICATE'::character varying, 'CONTENT'::character varying, 'COUPON'::character varying])::text[])))
);


--
-- Name: nft_contracts; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.nft_contracts (
    nft_contract_id character varying(128) NOT NULL,
    service_id character varying(128) NOT NULL,
    nft_contract_type_id character varying(128),
    nft_collection_name character varying(1024),
    nft_contract_address character varying(128),
    nft_contract_implementation_address character varying(128),
    next_token_id integer DEFAULT 0,
    delivery_image_url character varying(255)
);


--
-- Name: nft_metadatas; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.nft_metadatas (
    base_metadata_id character varying(128) NOT NULL,
    token_id integer NOT NULL,
    metadata jsonb NOT NULL,
    transaction_id character varying(128),
    queue_id character varying(128)
);


--
-- Name: notification_translations; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.notification_translations (
    notification_translation_id character varying(128) NOT NULL,
    notification_id character varying(128) NOT NULL,
    service_id character varying(128) NOT NULL,
    language character varying(128) NOT NULL,
    notification_title character varying(256) NOT NULL,
    notification_text character varying(2048) NOT NULL
);

ALTER TABLE ONLY public.notification_translations FORCE ROW LEVEL SECURITY;


--
-- Name: notifications; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.notifications (
    notification_id character varying(128) NOT NULL,
    service_id character varying(128) NOT NULL,
    broadcast_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);

ALTER TABLE ONLY public.notifications FORCE ROW LEVEL SECURITY;


--
-- Name: online_checkin_actions; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.online_checkin_actions (
    action_id character varying(128) NOT NULL,
    service_id character varying(128) NOT NULL,
    online_checkin_url character varying(1024)
);

ALTER TABLE ONLY public.online_checkin_actions FORCE ROW LEVEL SECURITY;


--
-- Name: plan_translations; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.plan_translations (
    plan_id character varying NOT NULL,
    language public.language_code NOT NULL,
    plan_name text,
    plan_description text
);


--
-- Name: plans; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.plans (
    plan_id character varying(128) NOT NULL,
    plan_image_url character varying(128),
    initial_cost_price_yen integer,
    plan_price_yen integer,
    is_status_quest_enable boolean NOT NULL,
    available_monthly_quests integer NOT NULL,
    available_monthly_issuable_nfts integer NOT NULL,
    status character varying(128) NOT NULL,
    available_from timestamp without time zone,
    available_to timestamp without time zone,
    CONSTRAINT plans_status_check CHECK (((status)::text = ANY ((ARRAY['INACTIVE'::character varying, 'ACTIVE'::character varying])::text[])))
);


--
-- Name: product_custom_fields; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.product_custom_fields (
    service_id character varying(128) NOT NULL,
    stripe_custom_field_id character varying(128) NOT NULL,
    stripe_product_id character varying(128) NOT NULL
);

ALTER TABLE ONLY public.product_custom_fields FORCE ROW LEVEL SECURITY;


--
-- Name: product_translations; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.product_translations (
    stripe_product_id character varying NOT NULL,
    service_id character varying NOT NULL,
    language public.language_code NOT NULL,
    description text
);

ALTER TABLE ONLY public.product_translations FORCE ROW LEVEL SECURITY;


--
-- Name: products; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.products (
    stripe_product_id character varying(128) NOT NULL,
    service_id character varying(128) NOT NULL,
    quantity integer,
    perchase_limit_per_person integer,
    is_phone_number_collection boolean,
    is_billing_address_collection boolean,
    is_shipping_address_collection boolean,
    order_index integer DEFAULT 0 NOT NULL
);

ALTER TABLE ONLY public.products FORCE ROW LEVEL SECURITY;


--
-- Name: qr_checkin_actions; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.qr_checkin_actions (
    action_id character varying(128) NOT NULL,
    service_id character varying(128) NOT NULL,
    qr_verification_data character varying(1024)
);

ALTER TABLE ONLY public.qr_checkin_actions FORCE ROW LEVEL SECURITY;


--
-- Name: quest_actions; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.quest_actions (
    quest_id character varying(128) NOT NULL,
    action_id character varying(128) NOT NULL,
    service_id character varying(128) NOT NULL
);

ALTER TABLE ONLY public.quest_actions FORCE ROW LEVEL SECURITY;


--
-- Name: quest_activities; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.quest_activities (
    account_id character varying(128) NOT NULL,
    quest_id character varying(128) NOT NULL,
    service_id character varying(128) NOT NULL,
    quest_activity_status character varying(128) NOT NULL,
    finish_date timestamp without time zone,
    CONSTRAINT quest_activities_quest_activity_status_check CHECK (((quest_activity_status)::text = ANY ((ARRAY['PROCEEDING'::character varying, 'DONE'::character varying, 'EXPIRED'::character varying])::text[])))
);

ALTER TABLE ONLY public.quest_activities FORCE ROW LEVEL SECURITY;


--
-- Name: quest_rewards; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.quest_rewards (
    quest_id character varying(128) NOT NULL,
    reward_id character varying(128) NOT NULL,
    service_id character varying(128) NOT NULL,
    quest_reward_priority_type character varying(128) NOT NULL,
    reward_acquirement_type character varying(128) DEFAULT 'DISTRIBUTION'::character varying,
    rank_id character varying(128),
    gacha_weight numeric,
    CONSTRAINT quest_rewards_quest_reward_priority_type_check CHECK (((quest_reward_priority_type)::text = ANY ((ARRAY['MAIN'::character varying, 'SUB'::character varying])::text[]))),
    CONSTRAINT quest_rewards_reward_acquirement_type_check CHECK (((reward_acquirement_type)::text = ANY ((ARRAY['DISTRIBUTION'::character varying, 'LOTTERY'::character varying, 'GACHA'::character varying, 'QUIZ'::character varying])::text[])))
);

ALTER TABLE ONLY public.quest_rewards FORCE ROW LEVEL SECURITY;


--
-- Name: quest_translations; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.quest_translations (
    quest_id character varying NOT NULL,
    service_id character varying NOT NULL,
    language public.language_code NOT NULL,
    quest_title text,
    quest_description text
);

ALTER TABLE ONLY public.quest_translations FORCE ROW LEVEL SECURITY;


--
-- Name: questionnaire_actions; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.questionnaire_actions (
    action_id character varying(128) NOT NULL,
    service_id character varying(128) NOT NULL,
    questionnaire_id character varying(128)
);

ALTER TABLE ONLY public.questionnaire_actions FORCE ROW LEVEL SECURITY;


--
-- Name: questionnaire_question_answers; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.questionnaire_question_answers (
    questionnaire_question_answer_id character varying(128) NOT NULL,
    service_id character varying(128) NOT NULL,
    questionnaire_result_id character varying(128) NOT NULL,
    question_id character varying(128) NOT NULL,
    question_answer text,
    is_correct boolean,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

ALTER TABLE ONLY public.questionnaire_question_answers FORCE ROW LEVEL SECURITY;


--
-- Name: questionnaire_question_translations; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.questionnaire_question_translations (
    question_id character varying NOT NULL,
    service_id character varying NOT NULL,
    language public.language_code NOT NULL,
    question_title text,
    question_detail text,
    question_extra jsonb,
    correct_data text,
    correct_data_validation text
);

ALTER TABLE ONLY public.questionnaire_question_translations FORCE ROW LEVEL SECURITY;


--
-- Name: questionnaire_questions; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.questionnaire_questions (
    question_id character varying(128) NOT NULL,
    theme_id character varying(128) NOT NULL,
    service_id character varying(128) NOT NULL,
    question_number integer NOT NULL,
    question_type character varying(64) NOT NULL,
    answer_point integer,
    CONSTRAINT questionnaire_questions_question_type_check CHECK (((question_type)::text = ANY ((ARRAY['SINGLE-CHOICE'::character varying, 'TEXT'::character varying, 'TEXT-LINES'::character varying, 'NUMBER'::character varying, 'IMAGE'::character varying])::text[])))
);

ALTER TABLE ONLY public.questionnaire_questions FORCE ROW LEVEL SECURITY;


--
-- Name: questionnaire_result_answers; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.questionnaire_result_answers (
    questionnaire_result_id character varying(128) NOT NULL,
    service_id character varying(128) NOT NULL,
    account_id character varying(128) NOT NULL,
    rank_id character varying(128) NOT NULL,
    questionnaire_status character varying(32) NOT NULL,
    questionnaire_points integer,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    is_available_result boolean DEFAULT true NOT NULL,
    CONSTRAINT questionnaire_result_answers_questionnaire_status_check CHECK (((questionnaire_status)::text = ANY ((ARRAY['UNDEFINED'::character varying, 'PASSED'::character varying, 'FAILED'::character varying])::text[])))
);

ALTER TABLE ONLY public.questionnaire_result_answers FORCE ROW LEVEL SECURITY;


--
-- Name: questionnaire_result_rank_translations; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.questionnaire_result_rank_translations (
    rank_id character varying NOT NULL,
    service_id character varying NOT NULL,
    language public.language_code NOT NULL,
    rank_name text
);

ALTER TABLE ONLY public.questionnaire_result_rank_translations FORCE ROW LEVEL SECURITY;


--
-- Name: questionnaire_result_ranks; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.questionnaire_result_ranks (
    rank_id character varying(128) NOT NULL,
    service_id character varying(128) NOT NULL,
    questionnaire_id character varying(128) NOT NULL,
    rank_header_animation_url character varying(512) NOT NULL,
    rank integer NOT NULL,
    lower_limit_points integer NOT NULL,
    upper_limit_points integer NOT NULL,
    is_passed boolean DEFAULT false NOT NULL
);

ALTER TABLE ONLY public.questionnaire_result_ranks FORCE ROW LEVEL SECURITY;


--
-- Name: questionnaire_theme_translations; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.questionnaire_theme_translations (
    theme_id character varying NOT NULL,
    service_id character varying NOT NULL,
    language public.language_code NOT NULL,
    theme_title text,
    theme_description text
);

ALTER TABLE ONLY public.questionnaire_theme_translations FORCE ROW LEVEL SECURITY;


--
-- Name: questionnaire_themes; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.questionnaire_themes (
    theme_id character varying(128) NOT NULL,
    questionnaire_id character varying(128) NOT NULL,
    service_id character varying(128) NOT NULL,
    theme_thumbnail_image_url character varying(512) NOT NULL,
    theme_cover_image_url character varying(512) NOT NULL,
    theme_number integer NOT NULL,
    theme_time_limit_seconds integer
);

ALTER TABLE ONLY public.questionnaire_themes FORCE ROW LEVEL SECURITY;


--
-- Name: questionnaires; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.questionnaires (
    questionnaire_id character varying(128) NOT NULL,
    service_id character varying(128) NOT NULL,
    questionnaire_type character varying(64) NOT NULL,
    CONSTRAINT questionnaires_questionnaire_type_check CHECK (((questionnaire_type)::text = ANY ((ARRAY['QUIZ'::character varying, 'SURVEY'::character varying, 'MESSAGE'::character varying])::text[])))
);

ALTER TABLE ONLY public.questionnaires FORCE ROW LEVEL SECURITY;


--
-- Name: quests; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.quests (
    quest_id character varying(128) NOT NULL,
    service_id character varying(128) NOT NULL,
    quest_cover_image_url character varying(1024),
    quest_thumbnail_image_url character varying(1024),
    quest_available_start_date timestamp without time zone NOT NULL,
    quest_available_end_date timestamp without time zone NOT NULL,
    quest_type character varying(128) NOT NULL,
    order_index integer DEFAULT 0 NOT NULL,
    CONSTRAINT quests_quest_type_check CHECK (((quest_type)::text = ANY ((ARRAY['STATUS'::character varying, 'CUSTOM'::character varying, 'RETENTION'::character varying])::text[])))
);

ALTER TABLE ONLY public.quests FORCE ROW LEVEL SECURITY;


--
-- Name: reward_translations; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.reward_translations (
    reward_id character varying NOT NULL,
    service_id character varying NOT NULL,
    language public.language_code NOT NULL,
    reward_title text,
    reward_description text
);

ALTER TABLE ONLY public.reward_translations FORCE ROW LEVEL SECURITY;


--
-- Name: rewards; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.rewards (
    reward_id character varying(128) NOT NULL,
    service_id character varying(128) NOT NULL,
    reward_cover_image_url character varying(1024),
    reward_thumbnail_image_url character varying(1024),
    reward_type character varying(128),
    order_index integer DEFAULT 0 NOT NULL,
    CONSTRAINT rewards_reward_type_check CHECK (((reward_type)::text = ANY ((ARRAY['COUPON'::character varying, 'CONTENT'::character varying, 'CERTIFICATE'::character varying])::text[])))
);

ALTER TABLE ONLY public.rewards FORCE ROW LEVEL SECURITY;


--
-- Name: serial_code_project_translations; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.serial_code_project_translations (
    serial_code_project_id character varying NOT NULL,
    service_id character varying NOT NULL,
    language public.language_code NOT NULL,
    name text,
    description text
);

ALTER TABLE ONLY public.serial_code_project_translations FORCE ROW LEVEL SECURITY;


--
-- Name: serial_code_projects; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.serial_code_projects (
    serial_code_project_id character varying(128) NOT NULL,
    service_id character varying(128) NOT NULL,
    reward_id character varying(128) NOT NULL,
    hash_key character varying(128) NOT NULL,
    status character varying(128),
    start_at timestamp without time zone NOT NULL,
    end_at timestamp without time zone NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    CONSTRAINT serial_code_projects_status_check CHECK (((status)::text = ANY ((ARRAY['DISABLE'::character varying, 'DRAFT'::character varying, 'ENABLE'::character varying])::text[])))
);


--
-- Name: serial_codes; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.serial_codes (
    serial_code_id character varying(128) NOT NULL,
    code_hash character varying(128) NOT NULL,
    serial_code_project_id character varying(128) NOT NULL,
    max_use_num integer NOT NULL,
    remaining_use_num integer NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


--
-- Name: service_translations; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.service_translations (
    service_id character varying NOT NULL,
    language public.language_code NOT NULL,
    service_name text,
    service_policy text,
    service_pane text
);


--
-- Name: services; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.services (
    service_id character varying(128) NOT NULL,
    tenant_id character varying(128) NOT NULL,
    service_url character varying(512) NOT NULL,
    service_logo_image_url character varying(128),
    theme_primary_color_lowest character varying(32),
    theme_primary_color_lower character varying(32),
    theme_primary_color_higher character varying(32),
    theme_primary_color_highest character varying(32),
    membership_nft_contract_id character varying(128),
    is_market_enabled boolean,
    market_cover_image_url character varying(128),
    stripe_account_id character varying(128),
    line_channel_id character varying(128),
    commission_rate numeric,
    modular_contract_id character varying(128) DEFAULT ''::character varying NOT NULL
);


--
-- Name: tenant_translations; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.tenant_translations (
    tenant_id character varying NOT NULL,
    language public.language_code NOT NULL,
    tenant_name text
);


--
-- Name: tenants; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.tenants (
    tenant_id character varying(128) NOT NULL,
    plan_id character varying(128) NOT NULL
);


--
-- Name: token_bound_account_implementations; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.token_bound_account_implementations (
    token_bound_account_implementation_id character varying(128) NOT NULL,
    token_bound_account_registry_id character varying(128),
    service_id character varying(128) NOT NULL,
    token_bound_account_implementation_address character varying(128),
    token_bound_account_implementation_abi jsonb
);


--
-- Name: token_bound_account_registries; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.token_bound_account_registries (
    token_bound_account_registry_id character varying(128) NOT NULL,
    service_id character varying(128) NOT NULL,
    token_bound_account_registry_address character varying(128),
    salt character varying(128),
    chain_id character varying(128),
    abi jsonb
);


--
-- Name: transaction_queues; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.transaction_queues (
    queue_id character varying(128) NOT NULL,
    service_id character varying(128) NOT NULL,
    from_address character varying(128) NOT NULL,
    to_address character varying(128) NOT NULL,
    nft_contract_address character varying(128) NOT NULL,
    tx_type character varying(128) NOT NULL,
    nft_type character varying(128) NOT NULL,
    token_id integer,
    status character varying(128) NOT NULL,
    created_date timestamp without time zone NOT NULL,
    CONSTRAINT transaction_queues_nft_type_check CHECK (((nft_type)::text = ANY ((ARRAY['MODULAR_CORE'::character varying, 'MODULE_BULKMINT'::character varying, 'MODULE_BULKCREATEACCOUNT'::character varying, 'TICKET'::character varying, 'MEMBERSHIP'::character varying, 'CERTIFICATE'::character varying, 'CONTENT'::character varying, 'COUPON'::character varying])::text[]))),
    CONSTRAINT transaction_queues_status_check CHECK (((status)::text = ANY ((ARRAY['PENDING'::character varying, 'PROCESSING'::character varying, 'FAILED'::character varying, 'RETRY'::character varying, 'MINED'::character varying, 'SUCCESS'::character varying, 'ABORTED'::character varying])::text[]))),
    CONSTRAINT transaction_queues_tx_type_check CHECK (((tx_type)::text = ANY ((ARRAY['MINT_REWARD'::character varying, 'MINT_MEMBERSHIP'::character varying, 'MINT_PRODUCT'::character varying, 'DEPLOY_CONTRACT'::character varying, 'CREATE_ACCOUNT'::character varying, 'EXECUTE_CONTRACT'::character varying])::text[])))
);


--
-- Name: transactions; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.transactions (
    transaction_id character varying(128) NOT NULL,
    service_id character varying(128) NOT NULL,
    encoded_data text NOT NULL,
    nonce integer NOT NULL,
    tx_hash character varying(128) NOT NULL,
    status character varying(128) NOT NULL,
    created_date timestamp without time zone NOT NULL,
    CONSTRAINT transactions_status_check CHECK (((status)::text = ANY ((ARRAY['EXECUTED'::character varying, 'FAILED'::character varying, 'RETRY'::character varying, 'MINED'::character varying, 'CONFIRMED'::character varying, 'REORGED'::character varying])::text[])))
);


--
-- Name: user_operation_queues; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.user_operation_queues (
    operation_id character varying(128) NOT NULL,
    from_address character varying(128) NOT NULL,
    hex_encoded_user_operation_data text,
    signature text,
    tx_hash character varying(128),
    uo_hash character varying(128),
    status character varying(128) NOT NULL,
    CONSTRAINT user_operation_queues_status_check CHECK (((status)::text = ANY ((ARRAY['RAW'::character varying, 'SIGNED'::character varying, 'VALIDATED'::character varying, 'EXECUTED'::character varying, 'MINED'::character varying, 'CONFIRMED'::character varying, 'FAILURE'::character varying, 'CANCELED'::character varying])::text[])))
);


--
-- Name: users; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.users (
    user_id character varying(128) NOT NULL,
    country_code character varying(128),
    phone_number character varying(128),
    mnemonic_backup_key character varying(512) NOT NULL,
    contract_account_address character varying(128),
    status character varying(128),
    created_at timestamp without time zone,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT users_status_check CHECK (((status)::text = ANY ((ARRAY['DELETED'::character varying, 'ACTIVE'::character varying])::text[])))
);


--
-- Name: vault_keys; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.vault_keys (
    vault_key_id character varying(128) NOT NULL,
    tenant_id character varying(128) NOT NULL,
    key_ring_project character varying(128) NOT NULL,
    key_ring_location character varying(128) NOT NULL,
    key_ring_name character varying(128) NOT NULL,
    key_version character varying(128) NOT NULL,
    vault_wallet_address character varying(128) NOT NULL,
    nonce integer DEFAULT 0 NOT NULL
);


--
-- Name: vault_transaction_queues; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.vault_transaction_queues (
    transaction_id character varying(128) NOT NULL,
    service_id character varying(128) NOT NULL,
    from_address character varying(128) NOT NULL,
    hex_encoded_transaction_data text NOT NULL,
    retry_count integer NOT NULL,
    nonce integer NOT NULL,
    tx_hash character varying(128) NOT NULL,
    tx_type character varying(128) NOT NULL,
    nft_type character varying(128) NOT NULL,
    status character varying(128) NOT NULL,
    to_address character varying(128),
    created_date timestamp without time zone DEFAULT '2025-01-22 00:00:00'::timestamp without time zone NOT NULL,
    token_id integer,
    CONSTRAINT vault_transaction_queues_nft_type_check CHECK (((nft_type)::text = ANY ((ARRAY['MODULAR_CORE'::character varying, 'MODULE_BULKMINT'::character varying, 'MODULE_BULKCREATEACCOUNT'::character varying, 'TICKET'::character varying, 'MEMBERSHIP'::character varying, 'CERTIFICATE'::character varying, 'CONTENT'::character varying, 'COUPON'::character varying])::text[]))),
    CONSTRAINT vault_transaction_queues_status_check CHECK (((status)::text = ANY ((ARRAY['EXECUTED'::character varying, 'MINED'::character varying, 'CONFIRMED'::character varying, 'FAILURE'::character varying, 'CANCELED'::character varying, 'ABORTED'::character varying])::text[]))),
    CONSTRAINT vault_transaction_queues_tx_type_check CHECK (((tx_type)::text = ANY ((ARRAY['MINT_REWARD'::character varying, 'MINT_MEMBERSHIP'::character varying, 'MINT_PRODUCT'::character varying, 'DEPLOY_CONTRACT'::character varying, 'CREATE_ACCOUNT'::character varying, 'EXECUTE_CONTRACT'::character varying])::text[])))
);


--
-- Name: account_notifications account_notifications_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.account_notifications
    ADD CONSTRAINT account_notifications_pkey PRIMARY KEY (account_notification_id);


--
-- Name: account_serial_codes account_serial_codes_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.account_serial_codes
    ADD CONSTRAINT account_serial_codes_pkey PRIMARY KEY (account_serial_code_id);


--
-- Name: accounts accounts_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.accounts
    ADD CONSTRAINT accounts_pkey PRIMARY KEY (account_id);


--
-- Name: achievement_actions achievement_actions_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.achievement_actions
    ADD CONSTRAINT achievement_actions_pkey PRIMARY KEY (action_id);


--
-- Name: action_translations action_translations_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.action_translations
    ADD CONSTRAINT action_translations_pkey PRIMARY KEY (action_id, language);


--
-- Name: actions actions_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.actions
    ADD CONSTRAINT actions_pkey PRIMARY KEY (action_id);


--
-- Name: admins admins_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.admins
    ADD CONSTRAINT admins_pkey PRIMARY KEY (admin_id);


--
-- Name: attempt_transactions attempt_transactions_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.attempt_transactions
    ADD CONSTRAINT attempt_transactions_pkey PRIMARY KEY (attempt_id);


--
-- Name: auth_providers auth_providers_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.auth_providers
    ADD CONSTRAINT auth_providers_pkey PRIMARY KEY (provider_id);


--
-- Name: certificate_rewards certificate_rewards_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.certificate_rewards
    ADD CONSTRAINT certificate_rewards_pkey PRIMARY KEY (reward_id);


--
-- Name: content_purchase_actions content_purchase_actions_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.content_purchase_actions
    ADD CONSTRAINT content_purchase_actions_pkey PRIMARY KEY (action_id);


--
-- Name: coupon_rewards coupon_rewards_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.coupon_rewards
    ADD CONSTRAINT coupon_rewards_pkey PRIMARY KEY (reward_id);


--
-- Name: custom_field_translations custom_field_translations_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.custom_field_translations
    ADD CONSTRAINT custom_field_translations_pkey PRIMARY KEY (stripe_custom_field_id, language);


--
-- Name: custom_fields custom_fields_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.custom_fields
    ADD CONSTRAINT custom_fields_pkey PRIMARY KEY (stripe_custom_field_id);


--
-- Name: digital_content_rewards digital_content_rewards_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.digital_content_rewards
    ADD CONSTRAINT digital_content_rewards_pkey PRIMARY KEY (reward_id);


--
-- Name: geofences geofences_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.geofences
    ADD CONSTRAINT geofences_pkey PRIMARY KEY (geofence_id);


--
-- Name: global_notifications global_notifications_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.global_notifications
    ADD CONSTRAINT global_notifications_pkey PRIMARY KEY (global_notification_id);


--
-- Name: kysely_migration_lock kysely_migration_lock_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.kysely_migration_lock
    ADD CONSTRAINT kysely_migration_lock_pkey PRIMARY KEY (id);


--
-- Name: kysely_migration kysely_migration_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.kysely_migration
    ADD CONSTRAINT kysely_migration_pkey PRIMARY KEY (name);


--
-- Name: nft_base_metadatas nft_base_metadatas_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.nft_base_metadatas
    ADD CONSTRAINT nft_base_metadatas_pkey PRIMARY KEY (base_metadata_id);


--
-- Name: nft_contract_types nft_contract_types_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.nft_contract_types
    ADD CONSTRAINT nft_contract_types_pkey PRIMARY KEY (nft_contract_type_id);


--
-- Name: nft_contracts nft_contracts_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.nft_contracts
    ADD CONSTRAINT nft_contracts_pkey PRIMARY KEY (nft_contract_id);


--
-- Name: notification_translations notification_translations_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.notification_translations
    ADD CONSTRAINT notification_translations_pkey PRIMARY KEY (notification_translation_id);


--
-- Name: notifications notifications_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.notifications
    ADD CONSTRAINT notifications_pkey PRIMARY KEY (notification_id);


--
-- Name: online_checkin_actions online_checkin_actions_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.online_checkin_actions
    ADD CONSTRAINT online_checkin_actions_pkey PRIMARY KEY (action_id);


--
-- Name: action_activities pk_action_activities; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.action_activities
    ADD CONSTRAINT pk_action_activities PRIMARY KEY (account_id, action_id);


--
-- Name: checkouts pk_checkouts; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.checkouts
    ADD CONSTRAINT pk_checkouts PRIMARY KEY (checkout_session_id, stripe_product_id);


--
-- Name: claimed_rewards pk_claimed_rewards; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.claimed_rewards
    ADD CONSTRAINT pk_claimed_rewards PRIMARY KEY (account_id, reward_id);


--
-- Name: nft_metadatas pk_nft_metadatas; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.nft_metadatas
    ADD CONSTRAINT pk_nft_metadatas PRIMARY KEY (base_metadata_id, token_id);


--
-- Name: product_custom_fields pk_product_custom_fields; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.product_custom_fields
    ADD CONSTRAINT pk_product_custom_fields PRIMARY KEY (stripe_custom_field_id, stripe_product_id);


--
-- Name: quest_actions pk_quest_actions; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.quest_actions
    ADD CONSTRAINT pk_quest_actions PRIMARY KEY (quest_id, action_id);


--
-- Name: quest_activities pk_quest_activities; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.quest_activities
    ADD CONSTRAINT pk_quest_activities PRIMARY KEY (account_id, quest_id);


--
-- Name: quest_rewards pk_quest_rewards; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.quest_rewards
    ADD CONSTRAINT pk_quest_rewards PRIMARY KEY (quest_id, reward_id);


--
-- Name: plan_translations plan_translations_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.plan_translations
    ADD CONSTRAINT plan_translations_pkey PRIMARY KEY (plan_id, language);


--
-- Name: plans plans_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.plans
    ADD CONSTRAINT plans_pkey PRIMARY KEY (plan_id);


--
-- Name: product_translations product_translations_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.product_translations
    ADD CONSTRAINT product_translations_pkey PRIMARY KEY (stripe_product_id, language);


--
-- Name: products products_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.products
    ADD CONSTRAINT products_pkey PRIMARY KEY (stripe_product_id);


--
-- Name: qr_checkin_actions qr_checkin_actions_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.qr_checkin_actions
    ADD CONSTRAINT qr_checkin_actions_pkey PRIMARY KEY (action_id);


--
-- Name: quest_translations quest_translations_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.quest_translations
    ADD CONSTRAINT quest_translations_pkey PRIMARY KEY (quest_id, language);


--
-- Name: questionnaire_actions questionnaire_actions_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.questionnaire_actions
    ADD CONSTRAINT questionnaire_actions_pkey PRIMARY KEY (action_id);


--
-- Name: questionnaire_question_answers questionnaire_question_answers_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.questionnaire_question_answers
    ADD CONSTRAINT questionnaire_question_answers_pkey PRIMARY KEY (questionnaire_question_answer_id);


--
-- Name: questionnaire_question_translations questionnaire_question_translations_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.questionnaire_question_translations
    ADD CONSTRAINT questionnaire_question_translations_pkey PRIMARY KEY (question_id, language);


--
-- Name: questionnaire_questions questionnaire_questions_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.questionnaire_questions
    ADD CONSTRAINT questionnaire_questions_pkey PRIMARY KEY (question_id);


--
-- Name: questionnaire_result_answers questionnaire_result_answers_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.questionnaire_result_answers
    ADD CONSTRAINT questionnaire_result_answers_pkey PRIMARY KEY (questionnaire_result_id);


--
-- Name: questionnaire_result_rank_translations questionnaire_result_rank_translations_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.questionnaire_result_rank_translations
    ADD CONSTRAINT questionnaire_result_rank_translations_pkey PRIMARY KEY (rank_id, language);


--
-- Name: questionnaire_result_ranks questionnaire_result_ranks_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.questionnaire_result_ranks
    ADD CONSTRAINT questionnaire_result_ranks_pkey PRIMARY KEY (rank_id);


--
-- Name: questionnaire_theme_translations questionnaire_theme_translations_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.questionnaire_theme_translations
    ADD CONSTRAINT questionnaire_theme_translations_pkey PRIMARY KEY (theme_id, language);


--
-- Name: questionnaire_themes questionnaire_themes_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.questionnaire_themes
    ADD CONSTRAINT questionnaire_themes_pkey PRIMARY KEY (theme_id);


--
-- Name: questionnaires questionnaires_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.questionnaires
    ADD CONSTRAINT questionnaires_pkey PRIMARY KEY (questionnaire_id);


--
-- Name: quests quests_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.quests
    ADD CONSTRAINT quests_pkey PRIMARY KEY (quest_id);


--
-- Name: reward_translations reward_translations_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.reward_translations
    ADD CONSTRAINT reward_translations_pkey PRIMARY KEY (reward_id, language);


--
-- Name: rewards rewards_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.rewards
    ADD CONSTRAINT rewards_pkey PRIMARY KEY (reward_id);


--
-- Name: serial_code_project_translations serial_code_project_translations_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.serial_code_project_translations
    ADD CONSTRAINT serial_code_project_translations_pkey PRIMARY KEY (serial_code_project_id, language);


--
-- Name: serial_code_projects serial_code_projects_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.serial_code_projects
    ADD CONSTRAINT serial_code_projects_pkey PRIMARY KEY (serial_code_project_id);


--
-- Name: serial_codes serial_codes_code_hash_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.serial_codes
    ADD CONSTRAINT serial_codes_code_hash_key UNIQUE (code_hash);


--
-- Name: serial_codes serial_codes_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.serial_codes
    ADD CONSTRAINT serial_codes_pkey PRIMARY KEY (serial_code_id);


--
-- Name: service_translations service_translations_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.service_translations
    ADD CONSTRAINT service_translations_pkey PRIMARY KEY (service_id, language);


--
-- Name: services services_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.services
    ADD CONSTRAINT services_pkey PRIMARY KEY (service_id);


--
-- Name: tenant_translations tenant_translations_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.tenant_translations
    ADD CONSTRAINT tenant_translations_pkey PRIMARY KEY (tenant_id, language);


--
-- Name: tenants tenants_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.tenants
    ADD CONSTRAINT tenants_pkey PRIMARY KEY (tenant_id);


--
-- Name: token_bound_account_implementations token_bound_account_implementations_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.token_bound_account_implementations
    ADD CONSTRAINT token_bound_account_implementations_pkey PRIMARY KEY (token_bound_account_implementation_id);


--
-- Name: token_bound_account_registries token_bound_account_registries_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.token_bound_account_registries
    ADD CONSTRAINT token_bound_account_registries_pkey PRIMARY KEY (token_bound_account_registry_id);


--
-- Name: transaction_queues transaction_queues_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.transaction_queues
    ADD CONSTRAINT transaction_queues_pkey PRIMARY KEY (queue_id);


--
-- Name: transactions transactions_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.transactions
    ADD CONSTRAINT transactions_pkey PRIMARY KEY (transaction_id);


--
-- Name: vault_transaction_queues unique_nonce_from_address; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.vault_transaction_queues
    ADD CONSTRAINT unique_nonce_from_address UNIQUE (nonce, from_address);


--
-- Name: notification_translations unique_notification_id_language; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.notification_translations
    ADD CONSTRAINT unique_notification_id_language UNIQUE (notification_id, language);


--
-- Name: user_operation_queues user_operation_queues_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.user_operation_queues
    ADD CONSTRAINT user_operation_queues_pkey PRIMARY KEY (operation_id);


--
-- Name: users users_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (user_id);


--
-- Name: vault_keys vault_keys_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.vault_keys
    ADD CONSTRAINT vault_keys_pkey PRIMARY KEY (vault_key_id);


--
-- Name: vault_transaction_queues vault_transaction_queues_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.vault_transaction_queues
    ADD CONSTRAINT vault_transaction_queues_pkey PRIMARY KEY (transaction_id);


--
-- Name: idx_transaction_queues_status_created_date; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_transaction_queues_status_created_date ON public.transaction_queues USING btree (status, created_date);


--
-- Name: account_serial_codes account_serial_codes_account_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.account_serial_codes
    ADD CONSTRAINT account_serial_codes_account_id_fkey FOREIGN KEY (account_id) REFERENCES public.accounts(account_id);


--
-- Name: account_serial_codes account_serial_codes_serial_code_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.account_serial_codes
    ADD CONSTRAINT account_serial_codes_serial_code_id_fkey FOREIGN KEY (serial_code_id) REFERENCES public.serial_codes(serial_code_id);


--
-- Name: accounts accounts_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.accounts
    ADD CONSTRAINT accounts_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(user_id);


--
-- Name: achievement_actions achievement_actions_action_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.achievement_actions
    ADD CONSTRAINT achievement_actions_action_id_fkey FOREIGN KEY (action_id) REFERENCES public.actions(action_id);


--
-- Name: action_activities action_activities_account_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.action_activities
    ADD CONSTRAINT action_activities_account_id_fkey FOREIGN KEY (account_id) REFERENCES public.accounts(account_id);


--
-- Name: action_activities action_activities_action_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.action_activities
    ADD CONSTRAINT action_activities_action_id_fkey FOREIGN KEY (action_id) REFERENCES public.actions(action_id);


--
-- Name: action_translations action_translations_fk; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.action_translations
    ADD CONSTRAINT action_translations_fk FOREIGN KEY (action_id) REFERENCES public.actions(action_id) ON DELETE CASCADE;


--
-- Name: actions actions_geofence_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.actions
    ADD CONSTRAINT actions_geofence_id_fkey FOREIGN KEY (geofence_id) REFERENCES public.geofences(geofence_id);


--
-- Name: attempt_transactions attempt_transactions_queue_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.attempt_transactions
    ADD CONSTRAINT attempt_transactions_queue_id_fkey FOREIGN KEY (queue_id) REFERENCES public.transaction_queues(queue_id);


--
-- Name: attempt_transactions attempt_transactions_transaction_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.attempt_transactions
    ADD CONSTRAINT attempt_transactions_transaction_id_fkey FOREIGN KEY (transaction_id) REFERENCES public.transactions(transaction_id);


--
-- Name: auth_providers auth_providers_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.auth_providers
    ADD CONSTRAINT auth_providers_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(user_id);


--
-- Name: nft_metadatas base_metadata_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.nft_metadatas
    ADD CONSTRAINT base_metadata_id_fkey FOREIGN KEY (base_metadata_id) REFERENCES public.nft_base_metadatas(base_metadata_id);


--
-- Name: certificate_rewards certificate_rewards_reward_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.certificate_rewards
    ADD CONSTRAINT certificate_rewards_reward_id_fkey FOREIGN KEY (reward_id) REFERENCES public.rewards(reward_id);


--
-- Name: checkouts checkouts_stripe_product_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.checkouts
    ADD CONSTRAINT checkouts_stripe_product_id_fkey FOREIGN KEY (stripe_product_id) REFERENCES public.products(stripe_product_id);


--
-- Name: claimed_rewards claimed_rewards_account_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.claimed_rewards
    ADD CONSTRAINT claimed_rewards_account_id_fkey FOREIGN KEY (account_id) REFERENCES public.accounts(account_id);


--
-- Name: claimed_rewards claimed_rewards_reward_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.claimed_rewards
    ADD CONSTRAINT claimed_rewards_reward_id_fkey FOREIGN KEY (reward_id) REFERENCES public.rewards(reward_id);


--
-- Name: content_purchase_actions content_purchase_actions_action_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.content_purchase_actions
    ADD CONSTRAINT content_purchase_actions_action_id_fkey FOREIGN KEY (action_id) REFERENCES public.actions(action_id);


--
-- Name: coupon_rewards coupon_rewards_reward_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.coupon_rewards
    ADD CONSTRAINT coupon_rewards_reward_id_fkey FOREIGN KEY (reward_id) REFERENCES public.rewards(reward_id);


--
-- Name: custom_field_translations custom_field_translations_fk; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.custom_field_translations
    ADD CONSTRAINT custom_field_translations_fk FOREIGN KEY (stripe_custom_field_id) REFERENCES public.custom_fields(stripe_custom_field_id) ON DELETE CASCADE;


--
-- Name: digital_content_rewards digital_content_rewards_reward_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.digital_content_rewards
    ADD CONSTRAINT digital_content_rewards_reward_id_fkey FOREIGN KEY (reward_id) REFERENCES public.rewards(reward_id);


--
-- Name: notification_translations fk_notifications_notification_id; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.notification_translations
    ADD CONSTRAINT fk_notifications_notification_id FOREIGN KEY (notification_id) REFERENCES public.notifications(notification_id);


--
-- Name: global_notifications fk_notifications_notification_id; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.global_notifications
    ADD CONSTRAINT fk_notifications_notification_id FOREIGN KEY (notification_id) REFERENCES public.notifications(notification_id);


--
-- Name: account_notifications fk_notifications_notification_id; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.account_notifications
    ADD CONSTRAINT fk_notifications_notification_id FOREIGN KEY (notification_id) REFERENCES public.notifications(notification_id);


--
-- Name: tenants fk_plans_tenants; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.tenants
    ADD CONSTRAINT fk_plans_tenants FOREIGN KEY (plan_id) REFERENCES public.plans(plan_id);


--
-- Name: quest_rewards fk_quest_rewards_rank_id; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.quest_rewards
    ADD CONSTRAINT fk_quest_rewards_rank_id FOREIGN KEY (rank_id) REFERENCES public.questionnaire_result_ranks(rank_id);


--
-- Name: questionnaire_themes fk_questionnaire_id; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.questionnaire_themes
    ADD CONSTRAINT fk_questionnaire_id FOREIGN KEY (questionnaire_id) REFERENCES public.questionnaires(questionnaire_id);


--
-- Name: questionnaire_question_answers fk_questionnaire_question_answers_question_id; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.questionnaire_question_answers
    ADD CONSTRAINT fk_questionnaire_question_answers_question_id FOREIGN KEY (question_id) REFERENCES public.questionnaire_questions(question_id);


--
-- Name: questionnaire_question_answers fk_questionnaire_question_answers_questionnaire_result_id; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.questionnaire_question_answers
    ADD CONSTRAINT fk_questionnaire_question_answers_questionnaire_result_id FOREIGN KEY (questionnaire_result_id) REFERENCES public.questionnaire_result_answers(questionnaire_result_id);


--
-- Name: questionnaire_questions fk_questionnaire_question_theme_id; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.questionnaire_questions
    ADD CONSTRAINT fk_questionnaire_question_theme_id FOREIGN KEY (theme_id) REFERENCES public.questionnaire_themes(theme_id);


--
-- Name: questionnaire_result_answers fk_questionnaire_result_answers_account_id; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.questionnaire_result_answers
    ADD CONSTRAINT fk_questionnaire_result_answers_account_id FOREIGN KEY (account_id) REFERENCES public.accounts(account_id);


--
-- Name: questionnaire_result_answers fk_questionnaire_result_answers_result_rank_id; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.questionnaire_result_answers
    ADD CONSTRAINT fk_questionnaire_result_answers_result_rank_id FOREIGN KEY (rank_id) REFERENCES public.questionnaire_result_ranks(rank_id);


--
-- Name: questionnaire_result_ranks fk_questionnaire_result_rank_questionnaire_id; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.questionnaire_result_ranks
    ADD CONSTRAINT fk_questionnaire_result_rank_questionnaire_id FOREIGN KEY (questionnaire_id) REFERENCES public.questionnaires(questionnaire_id);


--
-- Name: notifications fk_services_service_id; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.notifications
    ADD CONSTRAINT fk_services_service_id FOREIGN KEY (service_id) REFERENCES public.services(service_id);


--
-- Name: notification_translations fk_services_service_id; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.notification_translations
    ADD CONSTRAINT fk_services_service_id FOREIGN KEY (service_id) REFERENCES public.services(service_id);


--
-- Name: services fk_services_tenants; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.services
    ADD CONSTRAINT fk_services_tenants FOREIGN KEY (tenant_id) REFERENCES public.tenants(tenant_id);


--
-- Name: vault_keys fk_tenant_id; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.vault_keys
    ADD CONSTRAINT fk_tenant_id FOREIGN KEY (tenant_id) REFERENCES public.tenants(tenant_id);


--
-- Name: admins fk_tenants_admins; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.admins
    ADD CONSTRAINT fk_tenants_admins FOREIGN KEY (tenant_id) REFERENCES public.tenants(tenant_id);


--
-- Name: nft_contracts nft_contract_type_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.nft_contracts
    ADD CONSTRAINT nft_contract_type_id_fkey FOREIGN KEY (nft_contract_type_id) REFERENCES public.nft_contract_types(nft_contract_type_id);


--
-- Name: account_notifications notifications_account_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.account_notifications
    ADD CONSTRAINT notifications_account_id_fkey FOREIGN KEY (account_id) REFERENCES public.accounts(account_id);


--
-- Name: online_checkin_actions online_checkin_actions_action_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.online_checkin_actions
    ADD CONSTRAINT online_checkin_actions_action_id_fkey FOREIGN KEY (action_id) REFERENCES public.actions(action_id);


--
-- Name: plan_translations plan_translations_fk; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.plan_translations
    ADD CONSTRAINT plan_translations_fk FOREIGN KEY (plan_id) REFERENCES public.plans(plan_id) ON DELETE CASCADE;


--
-- Name: product_custom_fields product_custom_fields_stripe_custom_field_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.product_custom_fields
    ADD CONSTRAINT product_custom_fields_stripe_custom_field_id_fkey FOREIGN KEY (stripe_custom_field_id) REFERENCES public.custom_fields(stripe_custom_field_id);


--
-- Name: product_custom_fields product_custom_fields_stripe_product_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.product_custom_fields
    ADD CONSTRAINT product_custom_fields_stripe_product_id_fkey FOREIGN KEY (stripe_product_id) REFERENCES public.products(stripe_product_id);


--
-- Name: product_translations product_translations_fk; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.product_translations
    ADD CONSTRAINT product_translations_fk FOREIGN KEY (stripe_product_id) REFERENCES public.products(stripe_product_id) ON DELETE CASCADE;


--
-- Name: qr_checkin_actions qr_checkin_actions_action_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.qr_checkin_actions
    ADD CONSTRAINT qr_checkin_actions_action_id_fkey FOREIGN KEY (action_id) REFERENCES public.actions(action_id);


--
-- Name: quest_actions quest_actions_actions_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.quest_actions
    ADD CONSTRAINT quest_actions_actions_id_fkey FOREIGN KEY (action_id) REFERENCES public.actions(action_id);


--
-- Name: quest_actions quest_actions_quests_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.quest_actions
    ADD CONSTRAINT quest_actions_quests_id_fkey FOREIGN KEY (quest_id) REFERENCES public.quests(quest_id);


--
-- Name: quest_activities quest_activities_account_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.quest_activities
    ADD CONSTRAINT quest_activities_account_id_fkey FOREIGN KEY (account_id) REFERENCES public.accounts(account_id);


--
-- Name: quest_activities quest_activities_quest_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.quest_activities
    ADD CONSTRAINT quest_activities_quest_id_fkey FOREIGN KEY (quest_id) REFERENCES public.quests(quest_id);


--
-- Name: quest_rewards quest_rewards_quest_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.quest_rewards
    ADD CONSTRAINT quest_rewards_quest_id_fkey FOREIGN KEY (quest_id) REFERENCES public.quests(quest_id);


--
-- Name: quest_rewards quest_rewards_reward_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.quest_rewards
    ADD CONSTRAINT quest_rewards_reward_id_fkey FOREIGN KEY (reward_id) REFERENCES public.rewards(reward_id);


--
-- Name: quest_translations quest_translations_fk; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.quest_translations
    ADD CONSTRAINT quest_translations_fk FOREIGN KEY (quest_id) REFERENCES public.quests(quest_id) ON DELETE CASCADE;


--
-- Name: questionnaire_actions questionnaire_actions_action_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.questionnaire_actions
    ADD CONSTRAINT questionnaire_actions_action_id_fkey FOREIGN KEY (action_id) REFERENCES public.actions(action_id);


--
-- Name: questionnaire_question_translations questionnaire_question_translations_fk; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.questionnaire_question_translations
    ADD CONSTRAINT questionnaire_question_translations_fk FOREIGN KEY (question_id) REFERENCES public.questionnaire_questions(question_id) ON DELETE CASCADE;


--
-- Name: questionnaire_result_rank_translations questionnaire_result_rank_translations_fk; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.questionnaire_result_rank_translations
    ADD CONSTRAINT questionnaire_result_rank_translations_fk FOREIGN KEY (rank_id) REFERENCES public.questionnaire_result_ranks(rank_id) ON DELETE CASCADE;


--
-- Name: questionnaire_theme_translations questionnaire_theme_translations_fk; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.questionnaire_theme_translations
    ADD CONSTRAINT questionnaire_theme_translations_fk FOREIGN KEY (theme_id) REFERENCES public.questionnaire_themes(theme_id) ON DELETE CASCADE;


--
-- Name: reward_translations reward_translations_fk; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.reward_translations
    ADD CONSTRAINT reward_translations_fk FOREIGN KEY (reward_id) REFERENCES public.rewards(reward_id) ON DELETE CASCADE;


--
-- Name: serial_code_project_translations serial_code_project_translations_fk; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.serial_code_project_translations
    ADD CONSTRAINT serial_code_project_translations_fk FOREIGN KEY (serial_code_project_id) REFERENCES public.serial_code_projects(serial_code_project_id) ON DELETE CASCADE;


--
-- Name: serial_code_projects serial_code_projects_reward_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.serial_code_projects
    ADD CONSTRAINT serial_code_projects_reward_id_fkey FOREIGN KEY (reward_id) REFERENCES public.rewards(reward_id);


--
-- Name: serial_codes serial_codes_serial_code_project_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.serial_codes
    ADD CONSTRAINT serial_codes_serial_code_project_id_fkey FOREIGN KEY (serial_code_project_id) REFERENCES public.serial_code_projects(serial_code_project_id);


--
-- Name: service_translations service_translations_fk; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.service_translations
    ADD CONSTRAINT service_translations_fk FOREIGN KEY (service_id) REFERENCES public.services(service_id) ON DELETE CASCADE;


--
-- Name: tenant_translations tenant_translations_fk; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.tenant_translations
    ADD CONSTRAINT tenant_translations_fk FOREIGN KEY (tenant_id) REFERENCES public.tenants(tenant_id) ON DELETE CASCADE;


--
-- Name: token_bound_account_implementations token_bound_account_registry_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.token_bound_account_implementations
    ADD CONSTRAINT token_bound_account_registry_id_fkey FOREIGN KEY (token_bound_account_registry_id) REFERENCES public.token_bound_account_registries(token_bound_account_registry_id);


--
-- Name: account_notifications; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.account_notifications ENABLE ROW LEVEL SECURITY;

--
-- Name: accounts; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.accounts ENABLE ROW LEVEL SECURITY;

--
-- Name: achievement_actions; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.achievement_actions ENABLE ROW LEVEL SECURITY;

--
-- Name: action_activities; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.action_activities ENABLE ROW LEVEL SECURITY;

--
-- Name: action_translations; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.action_translations ENABLE ROW LEVEL SECURITY;

--
-- Name: actions; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.actions ENABLE ROW LEVEL SECURITY;

--
-- Name: auth_providers; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.auth_providers ENABLE ROW LEVEL SECURITY;

--
-- Name: certificate_rewards; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.certificate_rewards ENABLE ROW LEVEL SECURITY;

--
-- Name: checkouts; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.checkouts ENABLE ROW LEVEL SECURITY;

--
-- Name: claimed_rewards; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.claimed_rewards ENABLE ROW LEVEL SECURITY;

--
-- Name: content_purchase_actions; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.content_purchase_actions ENABLE ROW LEVEL SECURITY;

--
-- Name: coupon_rewards; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.coupon_rewards ENABLE ROW LEVEL SECURITY;

--
-- Name: custom_field_translations; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.custom_field_translations ENABLE ROW LEVEL SECURITY;

--
-- Name: custom_fields; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.custom_fields ENABLE ROW LEVEL SECURITY;

--
-- Name: digital_content_rewards; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.digital_content_rewards ENABLE ROW LEVEL SECURITY;

--
-- Name: geofences; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.geofences ENABLE ROW LEVEL SECURITY;

--
-- Name: global_notifications; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.global_notifications ENABLE ROW LEVEL SECURITY;

--
-- Name: notification_translations; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.notification_translations ENABLE ROW LEVEL SECURITY;

--
-- Name: notifications; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;

--
-- Name: online_checkin_actions; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.online_checkin_actions ENABLE ROW LEVEL SECURITY;

--
-- Name: product_custom_fields; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.product_custom_fields ENABLE ROW LEVEL SECURITY;

--
-- Name: product_translations; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.product_translations ENABLE ROW LEVEL SECURITY;

--
-- Name: products; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.products ENABLE ROW LEVEL SECURITY;

--
-- Name: qr_checkin_actions; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.qr_checkin_actions ENABLE ROW LEVEL SECURITY;

--
-- Name: quest_actions; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.quest_actions ENABLE ROW LEVEL SECURITY;

--
-- Name: quest_activities; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.quest_activities ENABLE ROW LEVEL SECURITY;

--
-- Name: quest_rewards; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.quest_rewards ENABLE ROW LEVEL SECURITY;

--
-- Name: quest_translations; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.quest_translations ENABLE ROW LEVEL SECURITY;

--
-- Name: questionnaire_actions; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.questionnaire_actions ENABLE ROW LEVEL SECURITY;

--
-- Name: questionnaire_question_answers; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.questionnaire_question_answers ENABLE ROW LEVEL SECURITY;

--
-- Name: questionnaire_question_translations; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.questionnaire_question_translations ENABLE ROW LEVEL SECURITY;

--
-- Name: questionnaire_questions; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.questionnaire_questions ENABLE ROW LEVEL SECURITY;

--
-- Name: questionnaire_result_answers; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.questionnaire_result_answers ENABLE ROW LEVEL SECURITY;

--
-- Name: questionnaire_result_rank_translations; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.questionnaire_result_rank_translations ENABLE ROW LEVEL SECURITY;

--
-- Name: questionnaire_result_ranks; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.questionnaire_result_ranks ENABLE ROW LEVEL SECURITY;

--
-- Name: questionnaire_theme_translations; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.questionnaire_theme_translations ENABLE ROW LEVEL SECURITY;

--
-- Name: questionnaire_themes; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.questionnaire_themes ENABLE ROW LEVEL SECURITY;

--
-- Name: questionnaires; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.questionnaires ENABLE ROW LEVEL SECURITY;

--
-- Name: quests; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.quests ENABLE ROW LEVEL SECURITY;

--
-- Name: reward_translations; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.reward_translations ENABLE ROW LEVEL SECURITY;

--
-- Name: rewards; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.rewards ENABLE ROW LEVEL SECURITY;

--
-- Name: serial_code_project_translations; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.serial_code_project_translations ENABLE ROW LEVEL SECURITY;

--
-- Name: serial_code_projects; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.serial_code_projects ENABLE ROW LEVEL SECURITY;

--
-- Name: account_notifications tenant_isolation_policy; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY tenant_isolation_policy ON public.account_notifications USING (((service_id)::text = current_setting('multitenant.service_id'::text)));


--
-- Name: accounts tenant_isolation_policy; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY tenant_isolation_policy ON public.accounts USING (((service_id)::text = current_setting('multitenant.service_id'::text)));


--
-- Name: achievement_actions tenant_isolation_policy; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY tenant_isolation_policy ON public.achievement_actions USING (((service_id)::text = current_setting('multitenant.service_id'::text)));


--
-- Name: action_activities tenant_isolation_policy; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY tenant_isolation_policy ON public.action_activities USING (((service_id)::text = current_setting('multitenant.service_id'::text)));


--
-- Name: action_translations tenant_isolation_policy; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY tenant_isolation_policy ON public.action_translations USING (((service_id)::text = current_setting('multitenant.service_id'::text)));


--
-- Name: actions tenant_isolation_policy; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY tenant_isolation_policy ON public.actions USING (((service_id)::text = current_setting('multitenant.service_id'::text)));


--
-- Name: auth_providers tenant_isolation_policy; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY tenant_isolation_policy ON public.auth_providers USING (((service_id)::text = current_setting('multitenant.service_id'::text)));


--
-- Name: certificate_rewards tenant_isolation_policy; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY tenant_isolation_policy ON public.certificate_rewards USING (((service_id)::text = current_setting('multitenant.service_id'::text)));


--
-- Name: checkouts tenant_isolation_policy; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY tenant_isolation_policy ON public.checkouts USING (((service_id)::text = current_setting('multitenant.service_id'::text)));


--
-- Name: claimed_rewards tenant_isolation_policy; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY tenant_isolation_policy ON public.claimed_rewards USING (((service_id)::text = current_setting('multitenant.service_id'::text)));


--
-- Name: content_purchase_actions tenant_isolation_policy; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY tenant_isolation_policy ON public.content_purchase_actions USING (((service_id)::text = current_setting('multitenant.service_id'::text)));


--
-- Name: coupon_rewards tenant_isolation_policy; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY tenant_isolation_policy ON public.coupon_rewards USING (((service_id)::text = current_setting('multitenant.service_id'::text)));


--
-- Name: custom_field_translations tenant_isolation_policy; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY tenant_isolation_policy ON public.custom_field_translations USING (((service_id)::text = current_setting('multitenant.service_id'::text)));


--
-- Name: custom_fields tenant_isolation_policy; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY tenant_isolation_policy ON public.custom_fields USING (((service_id)::text = current_setting('multitenant.service_id'::text)));


--
-- Name: digital_content_rewards tenant_isolation_policy; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY tenant_isolation_policy ON public.digital_content_rewards USING (((service_id)::text = current_setting('multitenant.service_id'::text)));


--
-- Name: geofences tenant_isolation_policy; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY tenant_isolation_policy ON public.geofences USING (((service_id)::text = current_setting('multitenant.service_id'::text)));


--
-- Name: global_notifications tenant_isolation_policy; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY tenant_isolation_policy ON public.global_notifications USING (((service_id)::text = current_setting('multitenant.service_id'::text)));


--
-- Name: notification_translations tenant_isolation_policy; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY tenant_isolation_policy ON public.notification_translations USING (((service_id)::text = current_setting('multitenant.service_id'::text)));


--
-- Name: notifications tenant_isolation_policy; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY tenant_isolation_policy ON public.notifications USING (((service_id)::text = current_setting('multitenant.service_id'::text)));


--
-- Name: online_checkin_actions tenant_isolation_policy; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY tenant_isolation_policy ON public.online_checkin_actions USING (((service_id)::text = current_setting('multitenant.service_id'::text)));


--
-- Name: product_custom_fields tenant_isolation_policy; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY tenant_isolation_policy ON public.product_custom_fields USING (((service_id)::text = current_setting('multitenant.service_id'::text)));


--
-- Name: product_translations tenant_isolation_policy; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY tenant_isolation_policy ON public.product_translations USING (((service_id)::text = current_setting('multitenant.service_id'::text)));


--
-- Name: products tenant_isolation_policy; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY tenant_isolation_policy ON public.products USING (((service_id)::text = current_setting('multitenant.service_id'::text)));


--
-- Name: qr_checkin_actions tenant_isolation_policy; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY tenant_isolation_policy ON public.qr_checkin_actions USING (((service_id)::text = current_setting('multitenant.service_id'::text)));


--
-- Name: quest_actions tenant_isolation_policy; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY tenant_isolation_policy ON public.quest_actions USING (((service_id)::text = current_setting('multitenant.service_id'::text)));


--
-- Name: quest_activities tenant_isolation_policy; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY tenant_isolation_policy ON public.quest_activities USING (((service_id)::text = current_setting('multitenant.service_id'::text)));


--
-- Name: quest_rewards tenant_isolation_policy; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY tenant_isolation_policy ON public.quest_rewards USING (((service_id)::text = current_setting('multitenant.service_id'::text)));


--
-- Name: quest_translations tenant_isolation_policy; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY tenant_isolation_policy ON public.quest_translations USING (((service_id)::text = current_setting('multitenant.service_id'::text)));


--
-- Name: questionnaire_actions tenant_isolation_policy; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY tenant_isolation_policy ON public.questionnaire_actions USING (((service_id)::text = current_setting('multitenant.service_id'::text)));


--
-- Name: questionnaire_question_answers tenant_isolation_policy; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY tenant_isolation_policy ON public.questionnaire_question_answers USING (((service_id)::text = current_setting('multitenant.service_id'::text)));


--
-- Name: questionnaire_question_translations tenant_isolation_policy; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY tenant_isolation_policy ON public.questionnaire_question_translations USING (((service_id)::text = current_setting('multitenant.service_id'::text)));


--
-- Name: questionnaire_questions tenant_isolation_policy; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY tenant_isolation_policy ON public.questionnaire_questions USING (((service_id)::text = current_setting('multitenant.service_id'::text)));


--
-- Name: questionnaire_result_answers tenant_isolation_policy; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY tenant_isolation_policy ON public.questionnaire_result_answers USING (((service_id)::text = current_setting('multitenant.service_id'::text)));


--
-- Name: questionnaire_result_rank_translations tenant_isolation_policy; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY tenant_isolation_policy ON public.questionnaire_result_rank_translations USING (((service_id)::text = current_setting('multitenant.service_id'::text)));


--
-- Name: questionnaire_result_ranks tenant_isolation_policy; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY tenant_isolation_policy ON public.questionnaire_result_ranks USING (((service_id)::text = current_setting('multitenant.service_id'::text)));


--
-- Name: questionnaire_theme_translations tenant_isolation_policy; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY tenant_isolation_policy ON public.questionnaire_theme_translations USING (((service_id)::text = current_setting('multitenant.service_id'::text)));


--
-- Name: questionnaire_themes tenant_isolation_policy; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY tenant_isolation_policy ON public.questionnaire_themes USING (((service_id)::text = current_setting('multitenant.service_id'::text)));


--
-- Name: questionnaires tenant_isolation_policy; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY tenant_isolation_policy ON public.questionnaires USING (((service_id)::text = current_setting('multitenant.service_id'::text)));


--
-- Name: quests tenant_isolation_policy; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY tenant_isolation_policy ON public.quests USING (((service_id)::text = current_setting('multitenant.service_id'::text)));


--
-- Name: reward_translations tenant_isolation_policy; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY tenant_isolation_policy ON public.reward_translations USING (((service_id)::text = current_setting('multitenant.service_id'::text)));


--
-- Name: rewards tenant_isolation_policy; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY tenant_isolation_policy ON public.rewards USING (((service_id)::text = current_setting('multitenant.service_id'::text)));


--
-- Name: serial_code_project_translations tenant_isolation_policy; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY tenant_isolation_policy ON public.serial_code_project_translations USING (((service_id)::text = current_setting('multitenant.service_id'::text)));


--
-- PostgreSQL database dump complete
--

