Enum "language_code" {
  "de"
  "en-GB"
  "en-US"
  "es"
  "fr"
  "it"
  "ja"
  "ko"
  "pt"
  "ru"
  "th"
  "zh-Hans"
  "zh-Hant"
}

Table "account_notifications" {
  "account_notification_id" "character varying(128)" [pk, not null]
  "account_id" "character varying(128)" [not null]
  "service_id" "character varying(128)" [not null]
  "notification_title" "character varying(256)"
  "notification_text" "character varying(2048)"
  "notification_id" "character varying(128)" [not null]
}

Table "account_serial_codes" {
  "account_serial_code_id" "character varying(128)" [pk, not null]
  "account_id" "character varying(128)" [not null]
  "serial_code_id" "character varying(128)" [not null]
  "created_at" timestamp [not null, default: `CURRENT_TIMESTAMP`]
}

Table "accounts" {
  "account_id" "character varying(128)" [pk, not null]
  "service_id" "character varying(128)" [not null]
  "user_id" "character varying(128)" [not null]
  "membership_id" integer
  "display_name" "character varying(256)"
  "profile_image_url" "character varying(1024)"
  "token_bound_account_address" "character varying(128)"
  "transaction_id" "character varying(128)"
  "status" "character varying(128)" [not null]
  "created_at" timestamp
  "updated_at" timestamp [default: `CURRENT_TIMESTAMP`]
  "queue_id" "character varying(128)"
}

Table "achievement_actions" {
  "action_id" "character varying(128)" [pk, not null]
  "service_id" "character varying(128)" [not null]
  "reward_id" "character varying(128)" [not null]
  "quest_completion_goal" integer
  "status_rank" integer
}

Table "action_activities" {
  "account_id" "character varying(128)" [not null]
  "action_id" "character varying(128)" [not null]
  "service_id" "character varying(128)" [not null]
  "action_activity_status" "character varying(128)" [not null]
  "finish_date" timestamp

  Indexes {
    (account_id, action_id) [pk, name: "pk_action_activities"]
  }
}

Table "action_translations" {
  "action_id" "character varying" [not null]
  "service_id" "character varying" [not null]
  "language" language_code [not null]
  "action_title" text
  "action_description" text

  Indexes {
    (action_id, language) [pk, name: "action_translations_pkey"]
  }
}

Table "actions" {
  "action_id" "character varying(128)" [pk, not null]
  "service_id" "character varying(128)" [not null]
  "action_cover_image_url" "character varying(1024)"
  "action_thumbnail_image_url" "character varying(1024)"
  "action_label" "character varying(128)"
  "action_available_start_date" timestamp [not null]
  "action_available_end_date" timestamp [not null]
  "action_type" "character varying(128)" [not null]
  "geofence_id" "character varying(128)"
  "order_index" integer [not null, default: 0]
}

Table "admins" {
  "admin_id" "character varying(128)" [pk, not null]
  "tenant_id" "character varying(128)" [not null]
  "admin_name" "character varying(516)" [not null]
}

Table "attempt_transactions" {
  "attempt_id" "character varying(128)" [pk, not null]
  "queue_id" "character varying(128)" [not null]
  "transaction_id" "character varying(128)" [not null]
  "created_date" timestamp [not null]
}

Table "auth_providers" {
  "provider_id" "character varying(128)" [pk, not null]
  "user_id" "character varying(128)" [not null]
  "service_id" "character varying(128)" [not null]
  "provider_uid" "character varying(128)" [not null]
  "provider_name" "character varying(128)" [not null]
}

Table "certificate_rewards" {
  "reward_id" "character varying(128)" [pk, not null]
  "service_id" "character varying(128)" [not null]
  "nft_contract_id" "character varying(128)"
  "token_id" integer
  "certificate_type" "character varying(128)" [not null]
  "status_certificate_rank" integer
}

Table "checkouts" {
  "checkout_session_id" "character varying(128)" [not null]
  "stripe_product_id" "character varying(128)" [not null]
  "account_id" "character varying(128)" [not null]
  "service_id" "character varying(128)" [not null]
  "checkout_status" "character varying(128)" [not null]

  Indexes {
    (checkout_session_id, stripe_product_id) [pk, name: "pk_checkouts"]
  }
}

Table "claimed_rewards" {
  "account_id" "character varying(128)" [not null]
  "reward_id" "character varying(128)" [not null]
  "service_id" "character varying(128)" [not null]
  "reward_usage_status" "character varying(128)" [not null]
  "transaction_id" "character varying(128)"
  "operation_id" "character varying(128)"
  "claim_date" timestamp
  "queue_id" "character varying(128)"

  Indexes {
    (account_id, reward_id) [pk, name: "pk_claimed_rewards"]
  }
}

Table "content_purchase_actions" {
  "action_id" "character varying(128)" [pk, not null]
  "service_id" "character varying(128)" [not null]
  "content_purchase_url" "character varying(1024)"
}

Table "coupon_rewards" {
  "reward_id" "character varying(128)" [pk, not null]
  "service_id" "character varying(128)" [not null]
  "nft_contract_id" "character varying(128)"
  "token_id" integer
}

Table "custom_field_translations" {
  "stripe_custom_field_id" "character varying" [not null]
  "service_id" "character varying" [not null]
  "language" language_code [not null]
  "field" jsonb

  Indexes {
    (stripe_custom_field_id, language) [pk, name: "custom_field_translations_pkey"]
  }
}

Table "custom_fields" {
  "stripe_custom_field_id" "character varying(128)" [pk, not null]
  "service_id" "character varying(128)" [not null]
}

Table "digital_content_rewards" {
  "reward_id" "character varying(128)" [pk, not null]
  "service_id" "character varying(128)" [not null]
  "nft_contract_id" "character varying(128)"
  "token_id" integer
}

Table "geofences" {
  "geofence_id" "character varying(128)" [pk, not null]
  "service_id" "character varying(128)" [not null]
  "center_coordinate_latitude" "character varying(128)"
  "center_coordinate_longtitude" "character varying(128)"
  "center_pin_name" "character varying(128)"
  "geofence_radius" "character varying(128)"
}

Table "global_notifications" {
  "global_notification_id" "character varying(128)" [pk, not null]
  "service_id" "character varying(128)" [not null]
  "notification_title" "character varying(256)"
  "notification_text" "character varying(2048)"
  "notification_id" "character varying(128)" [not null]
}

Table "kysely_migration" {
  "name" "character varying(255)" [pk, not null]
  "timestamp" "character varying(255)" [not null]
}

Table "kysely_migration_lock" {
  "id" "character varying(255)" [pk, not null]
  "is_locked" integer [not null, default: 0]
}

Table "nft_base_metadatas" {
  "base_metadata_id" "character varying(128)" [pk, not null]
  "service_id" "character varying(128)" [not null]
  "metadata" jsonb [not null]
  "contract_address" "character varying(128)"
  "token_id" integer
}

Table "nft_contract_types" {
  "nft_contract_type_id" "character varying(128)" [pk, not null]
  "nft_contract_type_name" "character varying(128)" [not null]
  "nft_contract_type_detail" "character varying(2024)" [not null]
  "nft_type" "character varying(32)" [not null]
  "nft_contract_abi" jsonb
  "nft_contract_binary" text
  "nft_contract_address" "character varying(128)"
}

Table "nft_contracts" {
  "nft_contract_id" "character varying(128)" [pk, not null]
  "service_id" "character varying(128)" [not null]
  "nft_contract_type_id" "character varying(128)"
  "nft_collection_name" "character varying(1024)"
  "nft_contract_address" "character varying(128)"
  "nft_contract_implementation_address" "character varying(128)"
  "next_token_id" integer [default: 0]
  "delivery_image_url" "character varying(255)"
}

Table "nft_metadatas" {
  "base_metadata_id" "character varying(128)" [not null]
  "token_id" integer [not null]
  "metadata" jsonb [not null]
  "transaction_id" "character varying(128)"
  "queue_id" "character varying(128)"

  Indexes {
    (base_metadata_id, token_id) [pk, name: "pk_nft_metadatas"]
  }
}

Table "notification_translations" {
  "notification_translation_id" "character varying(128)" [pk, not null]
  "notification_id" "character varying(128)" [not null]
  "service_id" "character varying(128)" [not null]
  "language" "character varying(128)" [not null]
  "notification_title" "character varying(256)" [not null]
  "notification_text" "character varying(2048)" [not null]

  Indexes {
    (notification_id, language) [unique, name: "unique_notification_id_language"]
  }
}

Table "notifications" {
  "notification_id" "character varying(128)" [pk, not null]
  "service_id" "character varying(128)" [not null]
  "broadcast_date" timestamp [not null, default: `CURRENT_TIMESTAMP`]
}

Table "online_checkin_actions" {
  "action_id" "character varying(128)" [pk, not null]
  "service_id" "character varying(128)" [not null]
  "online_checkin_url" "character varying(1024)"
}

Table "plan_translations" {
  "plan_id" "character varying" [not null]
  "language" language_code [not null]
  "plan_name" text
  "plan_description" text

  Indexes {
    (plan_id, language) [pk, name: "plan_translations_pkey"]
  }
}

Table "plans" {
  "plan_id" "character varying(128)" [pk, not null]
  "plan_image_url" "character varying(128)"
  "initial_cost_price_yen" integer
  "plan_price_yen" integer
  "is_status_quest_enable" boolean [not null]
  "available_monthly_quests" integer [not null]
  "available_monthly_issuable_nfts" integer [not null]
  "status" "character varying(128)" [not null]
  "available_from" timestamp
  "available_to" timestamp
}

Table "product_custom_fields" {
  "service_id" "character varying(128)" [not null]
  "stripe_custom_field_id" "character varying(128)" [not null]
  "stripe_product_id" "character varying(128)" [not null]

  Indexes {
    (stripe_custom_field_id, stripe_product_id) [pk, name: "pk_product_custom_fields"]
  }
}

Table "product_translations" {
  "stripe_product_id" "character varying" [not null]
  "service_id" "character varying" [not null]
  "language" language_code [not null]
  "description" text

  Indexes {
    (stripe_product_id, language) [pk, name: "product_translations_pkey"]
  }
}

Table "products" {
  "stripe_product_id" "character varying(128)" [pk, not null]
  "service_id" "character varying(128)" [not null]
  "quantity" integer
  "perchase_limit_per_person" integer
  "is_phone_number_collection" boolean
  "is_billing_address_collection" boolean
  "is_shipping_address_collection" boolean
  "order_index" integer [not null, default: 0]
}

Table "qr_checkin_actions" {
  "action_id" "character varying(128)" [pk, not null]
  "service_id" "character varying(128)" [not null]
  "qr_verification_data" "character varying(1024)"
}

Table "quest_actions" {
  "quest_id" "character varying(128)" [not null]
  "action_id" "character varying(128)" [not null]
  "service_id" "character varying(128)" [not null]

  Indexes {
    (quest_id, action_id) [pk, name: "pk_quest_actions"]
  }
}

Table "quest_activities" {
  "account_id" "character varying(128)" [not null]
  "quest_id" "character varying(128)" [not null]
  "service_id" "character varying(128)" [not null]
  "quest_activity_status" "character varying(128)" [not null]
  "finish_date" timestamp

  Indexes {
    (account_id, quest_id) [pk, name: "pk_quest_activities"]
  }
}

Table "quest_rewards" {
  "quest_id" "character varying(128)" [not null]
  "reward_id" "character varying(128)" [not null]
  "service_id" "character varying(128)" [not null]
  "quest_reward_priority_type" "character varying(128)" [not null]
  "reward_acquirement_type" "character varying(128)" [default: `'DISTRIBUTION'::charactervarying`]
  "rank_id" "character varying(128)"
  "gacha_weight" numeric

  Indexes {
    (quest_id, reward_id) [pk, name: "pk_quest_rewards"]
  }
}

Table "quest_translations" {
  "quest_id" "character varying" [not null]
  "service_id" "character varying" [not null]
  "language" language_code [not null]
  "quest_title" text
  "quest_description" text

  Indexes {
    (quest_id, language) [pk, name: "quest_translations_pkey"]
  }
}

Table "questionnaire_actions" {
  "action_id" "character varying(128)" [pk, not null]
  "service_id" "character varying(128)" [not null]
  "questionnaire_id" "character varying(128)"
}

Table "questionnaire_question_answers" {
  "questionnaire_question_answer_id" "character varying(128)" [pk, not null]
  "service_id" "character varying(128)" [not null]
  "questionnaire_result_id" "character varying(128)" [not null]
  "question_id" "character varying(128)" [not null]
  "question_answer" text
  "is_correct" boolean
  "created_at" timestamp [default: `CURRENT_TIMESTAMP`]
}

Table "questionnaire_question_translations" {
  "question_id" "character varying" [not null]
  "service_id" "character varying" [not null]
  "language" language_code [not null]
  "question_title" text
  "question_detail" text
  "question_extra" jsonb
  "correct_data" text
  "correct_data_validation" text

  Indexes {
    (question_id, language) [pk, name: "questionnaire_question_translations_pkey"]
  }
}

Table "questionnaire_questions" {
  "question_id" "character varying(128)" [pk, not null]
  "theme_id" "character varying(128)" [not null]
  "service_id" "character varying(128)" [not null]
  "question_number" integer [not null]
  "question_type" "character varying(64)" [not null]
  "answer_point" integer
}

Table "questionnaire_result_answers" {
  "questionnaire_result_id" "character varying(128)" [pk, not null]
  "service_id" "character varying(128)" [not null]
  "account_id" "character varying(128)" [not null]
  "rank_id" "character varying(128)" [not null]
  "questionnaire_status" "character varying(32)" [not null]
  "questionnaire_points" integer
  "created_at" timestamp [default: `CURRENT_TIMESTAMP`]
  "is_available_result" boolean [not null, default: true]
}

Table "questionnaire_result_rank_translations" {
  "rank_id" "character varying" [not null]
  "service_id" "character varying" [not null]
  "language" language_code [not null]
  "rank_name" text

  Indexes {
    (rank_id, language) [pk, name: "questionnaire_result_rank_translations_pkey"]
  }
}

Table "questionnaire_result_ranks" {
  "rank_id" "character varying(128)" [pk, not null]
  "service_id" "character varying(128)" [not null]
  "questionnaire_id" "character varying(128)" [not null]
  "rank_header_animation_url" "character varying(512)" [not null]
  "rank" integer [not null]
  "lower_limit_points" integer [not null]
  "upper_limit_points" integer [not null]
  "is_passed" boolean [not null, default: false]
}

Table "questionnaire_theme_translations" {
  "theme_id" "character varying" [not null]
  "service_id" "character varying" [not null]
  "language" language_code [not null]
  "theme_title" text
  "theme_description" text

  Indexes {
    (theme_id, language) [pk, name: "questionnaire_theme_translations_pkey"]
  }
}

Table "questionnaire_themes" {
  "theme_id" "character varying(128)" [pk, not null]
  "questionnaire_id" "character varying(128)" [not null]
  "service_id" "character varying(128)" [not null]
  "theme_thumbnail_image_url" "character varying(512)" [not null]
  "theme_cover_image_url" "character varying(512)" [not null]
  "theme_number" integer [not null]
  "theme_time_limit_seconds" integer
}

Table "questionnaires" {
  "questionnaire_id" "character varying(128)" [pk, not null]
  "service_id" "character varying(128)" [not null]
  "questionnaire_type" "character varying(64)" [not null]
}

Table "quests" {
  "quest_id" "character varying(128)" [pk, not null]
  "service_id" "character varying(128)" [not null]
  "quest_cover_image_url" "character varying(1024)"
  "quest_thumbnail_image_url" "character varying(1024)"
  "quest_available_start_date" timestamp [not null]
  "quest_available_end_date" timestamp [not null]
  "quest_type" "character varying(128)" [not null]
  "order_index" integer [not null, default: 0]
}

Table "reward_translations" {
  "reward_id" "character varying" [not null]
  "service_id" "character varying" [not null]
  "language" language_code [not null]
  "reward_title" text
  "reward_description" text

  Indexes {
    (reward_id, language) [pk, name: "reward_translations_pkey"]
  }
}

Table "rewards" {
  "reward_id" "character varying(128)" [pk, not null]
  "service_id" "character varying(128)" [not null]
  "reward_cover_image_url" "character varying(1024)"
  "reward_thumbnail_image_url" "character varying(1024)"
  "reward_type" "character varying(128)"
  "order_index" integer [not null, default: 0]
}

Table "serial_code_project_translations" {
  "serial_code_project_id" "character varying" [not null]
  "service_id" "character varying" [not null]
  "language" language_code [not null]
  "name" text
  "description" text

  Indexes {
    (serial_code_project_id, language) [pk, name: "serial_code_project_translations_pkey"]
  }
}

Table "serial_code_projects" {
  "serial_code_project_id" "character varying(128)" [pk, not null]
  "service_id" "character varying(128)" [not null]
  "reward_id" "character varying(128)" [not null]
  "hash_key" "character varying(128)" [not null]
  "status" "character varying(128)"
  "start_at" timestamp [not null]
  "end_at" timestamp [not null]
  "created_at" timestamp [not null, default: `CURRENT_TIMESTAMP`]
  "updated_at" timestamp [not null, default: `CURRENT_TIMESTAMP`]
}

Table "serial_codes" {
  "serial_code_id" "character varying(128)" [pk, not null]
  "code_hash" "character varying(128)" [unique, not null]
  "serial_code_project_id" "character varying(128)" [not null]
  "max_use_num" integer [not null]
  "remaining_use_num" integer [not null]
  "created_at" timestamp [not null, default: `CURRENT_TIMESTAMP`]
  "updated_at" timestamp [not null, default: `CURRENT_TIMESTAMP`]
}

Table "service_translations" {
  "service_id" "character varying" [not null]
  "language" language_code [not null]
  "service_name" text
  "service_policy" text
  "service_pane" text

  Indexes {
    (service_id, language) [pk, name: "service_translations_pkey"]
  }
}

Table "services" {
  "service_id" "character varying(128)" [pk, not null]
  "tenant_id" "character varying(128)" [not null]
  "service_url" "character varying(512)" [not null]
  "service_logo_image_url" "character varying(128)"
  "theme_primary_color_lowest" "character varying(32)"
  "theme_primary_color_lower" "character varying(32)"
  "theme_primary_color_higher" "character varying(32)"
  "theme_primary_color_highest" "character varying(32)"
  "membership_nft_contract_id" "character varying(128)"
  "is_market_enabled" boolean
  "market_cover_image_url" "character varying(128)"
  "stripe_account_id" "character varying(128)"
  "line_channel_id" "character varying(128)"
  "commission_rate" numeric
  "modular_contract_id" "character varying(128)" [not null, default: `''::charactervarying`]
}

Table "tenant_translations" {
  "tenant_id" "character varying" [not null]
  "language" language_code [not null]
  "tenant_name" text

  Indexes {
    (tenant_id, language) [pk, name: "tenant_translations_pkey"]
  }
}

Table "tenants" {
  "tenant_id" "character varying(128)" [pk, not null]
  "plan_id" "character varying(128)" [not null]
}

Table "token_bound_account_implementations" {
  "token_bound_account_implementation_id" "character varying(128)" [pk, not null]
  "token_bound_account_registry_id" "character varying(128)"
  "service_id" "character varying(128)" [not null]
  "token_bound_account_implementation_address" "character varying(128)"
  "token_bound_account_implementation_abi" jsonb
}

Table "token_bound_account_registries" {
  "token_bound_account_registry_id" "character varying(128)" [pk, not null]
  "service_id" "character varying(128)" [not null]
  "token_bound_account_registry_address" "character varying(128)"
  "salt" "character varying(128)"
  "chain_id" "character varying(128)"
  "abi" jsonb
}

Table "transaction_queues" {
  "queue_id" "character varying(128)" [pk, not null]
  "service_id" "character varying(128)" [not null]
  "from_address" "character varying(128)" [not null]
  "to_address" "character varying(128)" [not null]
  "nft_contract_address" "character varying(128)" [not null]
  "tx_type" "character varying(128)" [not null]
  "nft_type" "character varying(128)" [not null]
  "token_id" integer
  "status" "character varying(128)" [not null]
  "created_date" timestamp [not null]

  Indexes {
    (status, created_date) [type: btree, name: "idx_transaction_queues_status_created_date"]
  }
}

Table "transactions" {
  "transaction_id" "character varying(128)" [pk, not null]
  "service_id" "character varying(128)" [not null]
  "encoded_data" text [not null]
  "nonce" integer [not null]
  "tx_hash" "character varying(128)" [not null]
  "status" "character varying(128)" [not null]
  "created_date" timestamp [not null]
}

Table "user_operation_queues" {
  "operation_id" "character varying(128)" [pk, not null]
  "from_address" "character varying(128)" [not null]
  "hex_encoded_user_operation_data" text
  "signature" text
  "tx_hash" "character varying(128)"
  "uo_hash" "character varying(128)"
  "status" "character varying(128)" [not null]
}

Table "users" {
  "user_id" "character varying(128)" [pk, not null]
  "country_code" "character varying(128)"
  "phone_number" "character varying(128)"
  "mnemonic_backup_key" "character varying(512)" [not null]
  "contract_account_address" "character varying(128)"
  "status" "character varying(128)"
  "created_at" timestamp
  "updated_at" timestamp [default: `CURRENT_TIMESTAMP`]
}

Table "vault_keys" {
  "vault_key_id" "character varying(128)" [pk, not null]
  "tenant_id" "character varying(128)" [not null]
  "key_ring_project" "character varying(128)" [not null]
  "key_ring_location" "character varying(128)" [not null]
  "key_ring_name" "character varying(128)" [not null]
  "key_version" "character varying(128)" [not null]
  "vault_wallet_address" "character varying(128)" [not null]
  "nonce" integer [not null, default: 0]
}

Table "vault_transaction_queues" {
  "transaction_id" "character varying(128)" [pk, not null]
  "service_id" "character varying(128)" [not null]
  "from_address" "character varying(128)" [not null]
  "hex_encoded_transaction_data" text [not null]
  "retry_count" integer [not null]
  "nonce" integer [not null]
  "tx_hash" "character varying(128)" [not null]
  "tx_type" "character varying(128)" [not null]
  "nft_type" "character varying(128)" [not null]
  "status" "character varying(128)" [not null]
  "to_address" "character varying(128)"
  "created_date" timestamp [not null, default: `'2025-01-22 00:00:00'::timestampwithouttimezone`]
  "token_id" integer

  Indexes {
    (nonce, from_address) [unique, name: "unique_nonce_from_address"]
  }
}

Ref "account_serial_codes_account_id_fkey":"accounts"."account_id" < "account_serial_codes"."account_id"

Ref "account_serial_codes_serial_code_id_fkey":"serial_codes"."serial_code_id" < "account_serial_codes"."serial_code_id"

Ref "accounts_user_id_fkey":"users"."user_id" < "accounts"."user_id"

Ref "achievement_actions_action_id_fkey":"actions"."action_id" < "achievement_actions"."action_id"

Ref "action_activities_account_id_fkey":"accounts"."account_id" < "action_activities"."account_id"

Ref "action_activities_action_id_fkey":"actions"."action_id" < "action_activities"."action_id"

Ref "action_translations_fk":"actions"."action_id" < "action_translations"."action_id" [delete: cascade]

Ref "actions_geofence_id_fkey":"geofences"."geofence_id" < "actions"."geofence_id"

Ref "attempt_transactions_queue_id_fkey":"transaction_queues"."queue_id" < "attempt_transactions"."queue_id"

Ref "attempt_transactions_transaction_id_fkey":"transactions"."transaction_id" < "attempt_transactions"."transaction_id"

Ref "auth_providers_user_id_fkey":"users"."user_id" < "auth_providers"."user_id"

Ref "base_metadata_id_fkey":"nft_base_metadatas"."base_metadata_id" < "nft_metadatas"."base_metadata_id"

Ref "certificate_rewards_reward_id_fkey":"rewards"."reward_id" < "certificate_rewards"."reward_id"

Ref "checkouts_stripe_product_id_fkey":"products"."stripe_product_id" < "checkouts"."stripe_product_id"

Ref "claimed_rewards_account_id_fkey":"accounts"."account_id" < "claimed_rewards"."account_id"

Ref "claimed_rewards_reward_id_fkey":"rewards"."reward_id" < "claimed_rewards"."reward_id"

Ref "content_purchase_actions_action_id_fkey":"actions"."action_id" < "content_purchase_actions"."action_id"

Ref "coupon_rewards_reward_id_fkey":"rewards"."reward_id" < "coupon_rewards"."reward_id"

Ref "custom_field_translations_fk":"custom_fields"."stripe_custom_field_id" < "custom_field_translations"."stripe_custom_field_id" [delete: cascade]

Ref "digital_content_rewards_reward_id_fkey":"rewards"."reward_id" < "digital_content_rewards"."reward_id"

Ref "fk_notifications_notification_id":"notifications"."notification_id" < "notification_translations"."notification_id"

Ref "fk_notifications_notification_id":"notifications"."notification_id" < "global_notifications"."notification_id"

Ref "fk_notifications_notification_id":"notifications"."notification_id" < "account_notifications"."notification_id"

Ref "fk_plans_tenants":"plans"."plan_id" < "tenants"."plan_id"

Ref "fk_quest_rewards_rank_id":"questionnaire_result_ranks"."rank_id" < "quest_rewards"."rank_id"

Ref "fk_questionnaire_id":"questionnaires"."questionnaire_id" < "questionnaire_themes"."questionnaire_id"

Ref "fk_questionnaire_question_answers_question_id":"questionnaire_questions"."question_id" < "questionnaire_question_answers"."question_id"

Ref "fk_questionnaire_question_answers_questionnaire_result_id":"questionnaire_result_answers"."questionnaire_result_id" < "questionnaire_question_answers"."questionnaire_result_id"

Ref "fk_questionnaire_question_theme_id":"questionnaire_themes"."theme_id" < "questionnaire_questions"."theme_id"

Ref "fk_questionnaire_result_answers_account_id":"accounts"."account_id" < "questionnaire_result_answers"."account_id"

Ref "fk_questionnaire_result_answers_result_rank_id":"questionnaire_result_ranks"."rank_id" < "questionnaire_result_answers"."rank_id"

Ref "fk_questionnaire_result_rank_questionnaire_id":"questionnaires"."questionnaire_id" < "questionnaire_result_ranks"."questionnaire_id"

Ref "fk_services_service_id":"services"."service_id" < "notifications"."service_id"

Ref "fk_services_service_id":"services"."service_id" < "notification_translations"."service_id"

Ref "fk_services_tenants":"tenants"."tenant_id" < "services"."tenant_id"

Ref "fk_tenant_id":"tenants"."tenant_id" < "vault_keys"."tenant_id"

Ref "fk_tenants_admins":"tenants"."tenant_id" < "admins"."tenant_id"

Ref "nft_contract_type_id_fkey":"nft_contract_types"."nft_contract_type_id" < "nft_contracts"."nft_contract_type_id"

Ref "notifications_account_id_fkey":"accounts"."account_id" < "account_notifications"."account_id"

Ref "online_checkin_actions_action_id_fkey":"actions"."action_id" < "online_checkin_actions"."action_id"

Ref "plan_translations_fk":"plans"."plan_id" < "plan_translations"."plan_id" [delete: cascade]

Ref "product_custom_fields_stripe_custom_field_id_fkey":"custom_fields"."stripe_custom_field_id" < "product_custom_fields"."stripe_custom_field_id"

Ref "product_custom_fields_stripe_product_id_fkey":"products"."stripe_product_id" < "product_custom_fields"."stripe_product_id"

Ref "product_translations_fk":"products"."stripe_product_id" < "product_translations"."stripe_product_id" [delete: cascade]

Ref "qr_checkin_actions_action_id_fkey":"actions"."action_id" < "qr_checkin_actions"."action_id"

Ref "quest_actions_actions_id_fkey":"actions"."action_id" < "quest_actions"."action_id"

Ref "quest_actions_quests_id_fkey":"quests"."quest_id" < "quest_actions"."quest_id"

Ref "quest_activities_account_id_fkey":"accounts"."account_id" < "quest_activities"."account_id"

Ref "quest_activities_quest_id_fkey":"quests"."quest_id" < "quest_activities"."quest_id"

Ref "quest_rewards_quest_id_fkey":"quests"."quest_id" < "quest_rewards"."quest_id"

Ref "quest_rewards_reward_id_fkey":"rewards"."reward_id" < "quest_rewards"."reward_id"

Ref "quest_translations_fk":"quests"."quest_id" < "quest_translations"."quest_id" [delete: cascade]

Ref "questionnaire_actions_action_id_fkey":"actions"."action_id" < "questionnaire_actions"."action_id"

Ref "questionnaire_question_translations_fk":"questionnaire_questions"."question_id" < "questionnaire_question_translations"."question_id" [delete: cascade]

Ref "questionnaire_result_rank_translations_fk":"questionnaire_result_ranks"."rank_id" < "questionnaire_result_rank_translations"."rank_id" [delete: cascade]

Ref "questionnaire_theme_translations_fk":"questionnaire_themes"."theme_id" < "questionnaire_theme_translations"."theme_id" [delete: cascade]

Ref "reward_translations_fk":"rewards"."reward_id" < "reward_translations"."reward_id" [delete: cascade]

Ref "serial_code_project_translations_fk":"serial_code_projects"."serial_code_project_id" < "serial_code_project_translations"."serial_code_project_id" [delete: cascade]

Ref "serial_code_projects_reward_id_fkey":"rewards"."reward_id" < "serial_code_projects"."reward_id"

Ref "serial_codes_serial_code_project_id_fkey":"serial_code_projects"."serial_code_project_id" < "serial_codes"."serial_code_project_id"

Ref "service_translations_fk":"services"."service_id" < "service_translations"."service_id" [delete: cascade]

Ref "tenant_translations_fk":"tenants"."tenant_id" < "tenant_translations"."tenant_id" [delete: cascade]

Ref "token_bound_account_registry_id_fkey":"token_bound_account_registries"."token_bound_account_registry_id" < "token_bound_account_implementations"."token_bound_account_registry_id"
