import { Kysely, sql } from 'kysely';
import * as translations from '../migrations/00099';

export async function up(db: <PERSON>ysely<unknown>): Promise<void> {
  await sql`
  CREATE TYPE language_code AS ENUM (
    'de',
    'en-GB',
    'en-US',
    'es',
    'fr',
    'it',
    'ja',
    'ko',
    'pt',
    'ru',
    'th',
    'zh-Hans',
    'zh-Hant'
  );
`.execute(db);

  await translations.migrateActionTranslations(db);
  await translations.migrateCustomFieldTranslations(db);
  await translations.migratePlanTranslations(db);
  await translations.migrateProductTranslations(db);
  await translations.migrateQuestTranslations(db);
  await translations.migrateQuestionnaireQuestionTranslations(db);
  await translations.migrateQuestionnaireResultRankTranslations(db);
  await translations.migrateQuestionnaireThemeTranslations(db);
  await translations.migrateRewardTranslations(db);
  await translations.migrateSerialCodeProjectTranslations(db);
  await translations.migrateServiceTranslations(db);
  await translations.migrateTenantTranslations(db);
}

export async function down(db: Kysely<unknown>): Promise<void> {
  await translations.rollbackActionTranslations(db);
  await translations.rollbackCustomFieldTranslations(db);
  await translations.rollbackPlanTranslations(db);
  await translations.rollbackProductTranslations(db);
  await translations.rollbackQuestTranslations(db);
  await translations.rollbackQuestionnaireQuestionTranslations(db);
  await translations.rollbackQuestionnaireResultRankTranslations(db);
  await translations.rollbackQuestionnaireThemeTranslations(db);
  await translations.rollbackRewardTranslations(db);
  await translations.rollbackSerialCodeProjectTranslations(db);
  await translations.rollbackServiceTranslations(db);
  await translations.rollbackTenantTranslations(db);

  await sql`DROP TYPE language_code;`.execute(db);
}
