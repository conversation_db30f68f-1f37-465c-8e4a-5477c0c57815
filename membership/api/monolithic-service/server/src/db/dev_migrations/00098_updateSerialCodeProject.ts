/* eslint-disable @typescript-eslint/no-explicit-any */
import { Kysely } from 'kysely';
import { TABLE_SERIAL_CODE_PROJECTS } from '../../constants/database';

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  await db.schema
    .alterTable(TABLE_SERIAL_CODE_PROJECTS)
    .addColumn('slug', 'varchar(128)', (col) => col.notNull())
    .execute();

  await db.schema
    .alterTable(TABLE_SERIAL_CODE_PROJECTS)
    .addUniqueConstraint('unique_slug_per_service', ['slug', 'service_id'])
    .execute();
}

export async function down(db: Kysely<unknown>): Promise<void> {
  await db.schema.alterTable(TABLE_SERIAL_CODE_PROJECTS).dropConstraint('unique_slug_per_service').execute();

  await db.schema.alterTable(TABLE_SERIAL_CODE_PROJECTS).dropColumn('slug').execute();
}
