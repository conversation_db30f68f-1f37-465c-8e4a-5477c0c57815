/* eslint-disable @typescript-eslint/no-explicit-any */
import { Kysely } from 'kysely';
export async function up(db: <PERSON>ysely<any>): Promise<void> {
  await db.schema
    .alterTable('accounts')
    .alterColumn('membership_id', (c) => c.setNotNull())
    .execute();

  await db.schema
    .alterTable('accounts')
    .alterColumn('membership_metadata_url', (c) => c.setNotNull())
    .execute();
}

export async function down(db: Kysely<any>): Promise<void> {
  await db.schema
    .alterTable('accounts')
    .alterColumn('membership_id', (c) => c.dropNotNull())
    .execute();

  await db.schema
    .alterTable('accounts')
    .alterColumn('membership_metadata_url', (c) => c.dropNotNull())
    .execute();
}
