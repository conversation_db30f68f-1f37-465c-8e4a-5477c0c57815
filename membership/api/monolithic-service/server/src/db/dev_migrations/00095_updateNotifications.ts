import { Kysely, sql } from 'kysely';
import {
  TABLE_ACCOUNT_NOTIFICATIONS,
  TABLE_GLOBAL_NOTIFICATIONS,
  TABLE_NOTIFICATION_TRANSLATIONS,
  TABLE_NOTIFICATIONS,
  TABLE_SERVICES,
} from '../../constants/database';

export async function up(db: Kysely<unknown>): Promise<void> {

  await db.schema
    .createTable(TABLE_NOTIFICATIONS)
    .addColumn('notification_id', 'varchar(128)', (col) => col.primaryKey())
    .addColumn('service_id', 'varchar(128)', (col) => col.notNull())
    .addColumn('broadcast_date', 'timestamp', (col) => col.notNull().defaultTo(sql`CURRENT_TIMESTAMP`))
    .addForeignKeyConstraint(
      `fk_${TABLE_SERVICES}_service_id`,
      ['service_id'],
      TABLE_SERVICES,
      ['service_id'],
    )
    .execute();

  await sql`ALTER TABLE "notifications" ENABLE ROW LEVEL SECURITY`.execute(db);
  await sql`ALTER TABLE "notifications" FORCE ROW LEVEL SECURITY`.execute(db);
  await sql`CREATE POLICY tenant_isolation_policy ON "notifications" USING ("service_id" = current_setting('multitenant.service_id'))`.execute(
    db,
  );
  
  await db.schema
    .createTable(TABLE_NOTIFICATION_TRANSLATIONS)
    .addColumn('notification_translation_id', 'varchar(128)', (col) => col.primaryKey())
    .addColumn('notification_id', 'varchar(128)', (col) => col.notNull())
    .addColumn('service_id', 'varchar(128)', (col) => col.notNull())
    .addColumn('language', 'varchar(128)', (col) => col.notNull())
    .addColumn('notification_title', 'varchar(256)', (col) => col.notNull())
    .addColumn('notification_text', 'varchar(2048)', (col) => col.notNull())
    .addForeignKeyConstraint(
      `fk_${TABLE_NOTIFICATIONS}_notification_id`,
      ['notification_id'],
      TABLE_NOTIFICATIONS,
      ['notification_id'],
    )
    .addForeignKeyConstraint(
      `fk_${TABLE_SERVICES}_service_id`,
      ['service_id'],
      TABLE_SERVICES,
      ['service_id'],
    )
    .addUniqueConstraint('unique_notification_id_language', ['notification_id', 'language'])
    .execute();

  await sql`ALTER TABLE "notification_translations" ENABLE ROW LEVEL SECURITY`.execute(db);
  await sql`ALTER TABLE "notification_translations" FORCE ROW LEVEL SECURITY`.execute(db);
  await sql`CREATE POLICY tenant_isolation_policy ON "notification_translations" USING ("service_id" = current_setting('multitenant.service_id'))`.execute(
    db,
  );

  await sql`SELECT set_config('multitenant.service_id', '', false)`.execute(db);
  
  await db.schema
    .alterTable(TABLE_GLOBAL_NOTIFICATIONS)
    .addColumn('notification_id', 'varchar(128)', (col) => col.notNull())
    .dropColumn('broadcast_date')
    .execute();
  await db.schema
    .alterTable(TABLE_GLOBAL_NOTIFICATIONS)
    .addForeignKeyConstraint(
      `fk_${TABLE_NOTIFICATIONS}_notification_id`,
      ['notification_id'],
      TABLE_NOTIFICATIONS,
      ['notification_id'],
    )
    .execute();

  await db.schema
    .alterTable(TABLE_ACCOUNT_NOTIFICATIONS)
    .addColumn('notification_id', 'varchar(128)', (col) => col.notNull())
    .dropColumn('broadcast_date')
    .execute();
  await db.schema
    .alterTable(TABLE_ACCOUNT_NOTIFICATIONS)
    .addForeignKeyConstraint(
      `fk_${TABLE_NOTIFICATIONS}_notification_id`,
      ['notification_id'],
      TABLE_NOTIFICATIONS,
      ['notification_id'],
    )
    .execute();
}

export async function down(db: Kysely<unknown>): Promise<void> {
  await db.schema.alterTable(TABLE_GLOBAL_NOTIFICATIONS)
    .dropColumn('notification_id')
    .addColumn('broadcast_date', 'timestamp', (col) => col.notNull())
    .execute();
  await db.schema.alterTable(TABLE_ACCOUNT_NOTIFICATIONS)
    .dropColumn('notification_id')
    .addColumn('broadcast_date', 'timestamp', (col) => col.notNull())
    .execute();

  await db.schema.dropTable(TABLE_NOTIFICATION_TRANSLATIONS).execute();
  await db.schema.dropTable(TABLE_NOTIFICATIONS).execute();
}
