// import { Kysely, sql } from 'kysely';

// export async function up(db: <PERSON>ys<PERSON><any>): Promise<void> {
//   // Enable PostGIS extension for geometry support
//   await sql`CREATE EXTENSION IF NOT EXISTS postgis`.execute(db);


  
  
//   // Backup existing geofences data if any
//   await sql`
//     CREATE TABLE IF NOT EXISTS geofences_backup AS 
//     SELECT * FROM geofences WHERE 1=0
//   `.execute(db);
  
//   // Drop existing constraints and indexes
//   await sql`DROP INDEX IF EXISTS idx_geofences_service_id`.execute(db);
  
//   // Add new columns to geofences table
//   await sql`ALTER TABLE geofences ADD COLUMN IF NOT EXISTS name varchar(255)`.execute(db);
//   await sql`ALTER TABLE geofences ADD COLUMN IF NOT EXISTS description text`.execute(db);
//   await sql`ALTER TABLE geofences ADD COLUMN IF NOT EXISTS geofence_type varchar(32) DEFAULT 'CIRCLE'`.execute(db);
//   await sql`ALTER TABLE geofences ADD COLUMN IF NOT EXISTS is_active boolean DEFAULT true`.execute(db);
//   await sql`ALTER TABLE geofences ADD COLUMN IF NOT EXISTS created_at timestamptz DEFAULT CURRENT_TIMESTAMP`.execute(db);
//   await sql`ALTER TABLE geofences ADD COLUMN IF NOT EXISTS updated_at timestamptz DEFAULT CURRENT_TIMESTAMP`.execute(db);
  
//   // Add geometry column using PostGIS
//   await sql`
//     ALTER TABLE geofences 
//     ADD COLUMN IF NOT EXISTS location geometry(GEOMETRY, 4326)
//   `.execute(db);
  
//   // Convert existing data to new format (if any exists)
//   await sql`
//     UPDATE geofences 
//     SET 
//       name = COALESCE(center_pin_name, 'Unnamed Geofence'),
//       geofence_type = 'CIRCLE',
//       location = CASE 
//         WHEN center_coordinate_latitude IS NOT NULL AND center_coordinate_longtitude IS NOT NULL 
//         THEN ST_Point(
//           CAST(center_coordinate_longtitude AS DECIMAL), 
//           CAST(center_coordinate_latitude AS DECIMAL)
//         )
//         ELSE NULL
//       END
//     WHERE name IS NULL
//   `.execute(db);
  
//   // Convert geofence_radius from varchar to integer
//   await sql`
//     ALTER TABLE geofences 
//     ADD COLUMN IF NOT EXISTS radius_meters integer
//   `.execute(db);
  
//   await sql`
//     UPDATE geofences 
//     SET radius_meters = CASE 
//       WHEN geofence_radius ~ '^[0-9]+$' THEN CAST(geofence_radius AS INTEGER)
//       ELSE 100
//     END
//     WHERE radius_meters IS NULL
//   `.execute(db);
  
//   // Add constraints
//   await sql`
//     ALTER TABLE geofences 
//     ADD CONSTRAINT IF NOT EXISTS geofences_type_check 
//     CHECK (geofence_type IN ('CIRCLE', 'POLYGON'))
//   `.execute(db);
  
//   await sql`
//     ALTER TABLE geofences 
//     ADD CONSTRAINT IF NOT EXISTS geofences_radius_check 
//     CHECK (
//       (geofence_type = 'CIRCLE' AND radius_meters > 0) OR 
//       (geofence_type = 'POLYGON' AND radius_meters IS NULL)
//     )
//   `.execute(db);
  
//   // Make name column NOT NULL
//   await sql`ALTER TABLE geofences ALTER COLUMN name SET NOT NULL`.execute(db);
  
//   // Create spatial index for efficient geometry queries
//   await sql`CREATE INDEX IF NOT EXISTS idx_geofences_location_gist ON geofences USING GIST (location)`.execute(db);
  
//   // Recreate other indexes
//   await sql`CREATE INDEX IF NOT EXISTS idx_geofences_service_id ON geofences (service_id)`.execute(db);
//   await sql`CREATE INDEX IF NOT EXISTS idx_geofences_type ON geofences (geofence_type)`.execute(db);
//   await sql`CREATE INDEX IF NOT EXISTS idx_geofences_active ON geofences (is_active)`.execute(db);
  
//   // Add LOCATION_CHECKIN action type if not exists
//   await sql`
//     INSERT INTO action_types (action_type, description) 
//     VALUES ('LOCATION_CHECKIN', 'Location check-in action requiring user to be within specified geofence')
//     ON CONFLICT (action_type) DO NOTHING
//   `.execute(db);
  
//   // Add geofence_id column to actions table (nullable for backward compatibility)
//   await sql`
//     ALTER TABLE actions 
//     ADD COLUMN IF NOT EXISTS geofence_id varchar(128)
//   `.execute(db);
  
//   // Add foreign key constraint for actions.geofence_id
//   await sql`
//     ALTER TABLE actions 
//     ADD CONSTRAINT IF NOT EXISTS actions_geofence_id_fk 
//     FOREIGN KEY (geofence_id) REFERENCES geofences(geofence_id) ON DELETE SET NULL
//   `.execute(db);
  
//   // Create location_checkin_actions table
//   await sql`
//     CREATE TABLE IF NOT EXISTS location_checkin_actions (
//       action_id varchar(128) PRIMARY KEY,
//       service_id varchar(128) NOT NULL,
//       geofence_id varchar(128) NOT NULL,
//       required_duration_seconds integer DEFAULT 0 CHECK (required_duration_seconds >= 0),
//       created_at timestamptz DEFAULT CURRENT_TIMESTAMP,
//       updated_at timestamptz DEFAULT CURRENT_TIMESTAMP,
      
//       CONSTRAINT location_checkin_actions_action_id_fk 
//         FOREIGN KEY (action_id) REFERENCES actions(action_id) ON DELETE CASCADE,
//       CONSTRAINT location_checkin_actions_service_id_fk 
//         FOREIGN KEY (service_id) REFERENCES services(service_id) ON DELETE CASCADE,
//       CONSTRAINT location_checkin_actions_geofence_id_fk 
//         FOREIGN KEY (geofence_id) REFERENCES geofences(geofence_id) ON DELETE CASCADE
//     )
//   `.execute(db);
  
//   // Enable RLS for location_checkin_actions
//   await sql`ALTER TABLE location_checkin_actions ENABLE ROW LEVEL SECURITY`.execute(db);
//   await sql`ALTER TABLE location_checkin_actions FORCE ROW LEVEL SECURITY`.execute(db);
  
//   await sql`
//     CREATE POLICY IF NOT EXISTS tenant_isolation_policy ON location_checkin_actions 
//     USING (service_id = current_setting('multitenant.service_id'))
//   `.execute(db);
  
//   // Create indexes for location_checkin_actions
//   await sql`CREATE INDEX IF NOT EXISTS idx_location_checkin_actions_service_id ON location_checkin_actions (service_id)`.execute(db);
//   await sql`CREATE INDEX IF NOT EXISTS idx_location_checkin_actions_geofence_id ON location_checkin_actions (geofence_id)`.execute(db);
  
//   // Create location_checkin_attempts table for tracking attempts
//   await sql`
//     CREATE TABLE IF NOT EXISTS location_checkin_attempts (
//       attempt_id varchar(128) PRIMARY KEY DEFAULT gen_random_uuid()::text,
//       account_id varchar(128) NOT NULL,
//       action_id varchar(128) NOT NULL,
//       service_id varchar(128) NOT NULL,
//       latitude decimal(10,8) NOT NULL CHECK (latitude >= -90 AND latitude <= 90),
//       longitude decimal(11,8) NOT NULL CHECK (longitude >= -180 AND longitude <= 180),
//       accuracy_meters decimal(8,2) CHECK (accuracy_meters >= 0),
//       is_successful boolean NOT NULL,
//       distance_from_target decimal(10,2) NOT NULL,
//       attempted_at timestamptz DEFAULT CURRENT_TIMESTAMP,
      
//       CONSTRAINT location_checkin_attempts_account_id_fk 
//         FOREIGN KEY (account_id) REFERENCES accounts(account_id) ON DELETE CASCADE,
//       CONSTRAINT location_checkin_attempts_action_id_fk 
//         FOREIGN KEY (action_id) REFERENCES actions(action_id) ON DELETE CASCADE,
//       CONSTRAINT location_checkin_attempts_service_id_fk 
//         FOREIGN KEY (service_id) REFERENCES services(service_id) ON DELETE CASCADE
//     )
//   `.execute(db);
  
//   // Enable RLS for location_checkin_attempts
//   await sql`ALTER TABLE location_checkin_attempts ENABLE ROW LEVEL SECURITY`.execute(db);
//   await sql`ALTER TABLE location_checkin_attempts FORCE ROW LEVEL SECURITY`.execute(db);
  
//   await sql`
//     CREATE POLICY IF NOT EXISTS tenant_isolation_policy ON location_checkin_attempts 
//     USING (service_id = current_setting('multitenant.service_id'))
//   `.execute(db);
  
//   // Create indexes for location_checkin_attempts
//   await sql`CREATE INDEX IF NOT EXISTS idx_location_checkin_attempts_account_id ON location_checkin_attempts (account_id)`.execute(db);
//   await sql`CREATE INDEX IF NOT EXISTS idx_location_checkin_attempts_action_id ON location_checkin_attempts (action_id)`.execute(db);
//   await sql`CREATE INDEX IF NOT EXISTS idx_location_checkin_attempts_service_id ON location_checkin_attempts (service_id)`.execute(db);
//   await sql`CREATE INDEX IF NOT EXISTS idx_location_checkin_attempts_attempted_at ON location_checkin_attempts (attempted_at)`.execute(db);
  
//   // Create helper functions for geometry operations
//   await sql`
//     CREATE OR REPLACE FUNCTION is_point_in_geofence(
//       point_lat decimal(10,8),
//       point_lng decimal(11,8),
//       geofence_location geometry,
//       geofence_type varchar(32),
//       geofence_radius integer DEFAULT NULL
//     ) RETURNS boolean AS $$
//     DECLARE
//       point_geom geometry;
//       distance_meters decimal;
//     BEGIN
//       -- Create point geometry
//       point_geom := ST_Point(point_lng, point_lat);
      
//       IF geofence_type = 'CIRCLE' THEN
//         -- For circles, check distance from center
//         distance_meters := ST_Distance(
//           ST_Transform(point_geom, 3857),
//           ST_Transform(geofence_location, 3857)
//         );
//         RETURN distance_meters <= geofence_radius;
//       ELSIF geofence_type = 'POLYGON' THEN
//         -- For polygons, check if point is within polygon
//         RETURN ST_Within(point_geom, geofence_location);
//       ELSE
//         RETURN false;
//       END IF;
//     END;
//     $$ LANGUAGE plpgsql IMMUTABLE;
//   `.execute(db);
  
//   await sql`
//     CREATE OR REPLACE FUNCTION calculate_distance_to_geofence(
//       point_lat decimal(10,8),
//       point_lng decimal(11,8),
//       geofence_location geometry,
//       geofence_type varchar(32)
//     ) RETURNS decimal(10,2) AS $$
//     DECLARE
//       point_geom geometry;
//     BEGIN
//       -- Create point geometry
//       point_geom := ST_Point(point_lng, point_lat);
      
//       -- Calculate distance in meters using Web Mercator projection
//       RETURN ST_Distance(
//         ST_Transform(point_geom, 3857),
//         ST_Transform(geofence_location, 3857)
//       );
//     END;
//     $$ LANGUAGE plpgsql IMMUTABLE;
//   `.execute(db);
  
//   // Add trigger for updated_at timestamp
//   await sql`
//     CREATE OR REPLACE FUNCTION update_updated_at_column()
//     RETURNS TRIGGER AS $$
//     BEGIN
//       NEW.updated_at = CURRENT_TIMESTAMP;
//       RETURN NEW;
//     END;
//     $$ LANGUAGE plpgsql;
//   `.execute(db);
  
//   await sql`
//     CREATE TRIGGER IF NOT EXISTS update_geofences_updated_at 
//       BEFORE UPDATE ON geofences 
//       FOR EACH ROW EXECUTE FUNCTION update_updated_at_column()
//   `.execute(db);
  
//   await sql`
//     CREATE TRIGGER IF NOT EXISTS update_location_checkin_actions_updated_at 
//       BEFORE UPDATE ON location_checkin_actions 
//       FOR EACH ROW EXECUTE FUNCTION update_updated_at_column()
//   `.execute(db);
// }

// export async function down(db: Kysely<any>): Promise<void> {
//   // Drop triggers
//   await sql`DROP TRIGGER IF EXISTS update_location_checkin_actions_updated_at ON location_checkin_actions`.execute(db);
//   await sql`DROP TRIGGER IF EXISTS update_geofences_updated_at ON geofences`.execute(db);
  
//   // Drop functions
//   await sql`DROP FUNCTION IF EXISTS update_updated_at_column()`.execute(db);
//   await sql`DROP FUNCTION IF EXISTS calculate_distance_to_geofence(decimal, decimal, geometry, varchar)`.execute(db);
//   await sql`DROP FUNCTION IF EXISTS is_point_in_geofence(decimal, decimal, geometry, varchar, integer)`.execute(db);
  
//   // Drop new tables
//   await sql`DROP TABLE IF EXISTS location_checkin_attempts`.execute(db);
//   await sql`DROP TABLE IF EXISTS location_checkin_actions`.execute(db);
  
//   // Remove new columns from geofences
//   await sql`ALTER TABLE geofences DROP COLUMN IF EXISTS location`.execute(db);
//   await sql`ALTER TABLE geofences DROP COLUMN IF EXISTS radius_meters`.execute(db);
//   await sql`ALTER TABLE geofences DROP COLUMN IF EXISTS updated_at`.execute(db);
//   await sql`ALTER TABLE geofences DROP COLUMN IF EXISTS created_at`.execute(db);
//   await sql`ALTER TABLE geofences DROP COLUMN IF EXISTS is_active`.execute(db);
//   await sql`ALTER TABLE geofences DROP COLUMN IF EXISTS geofence_type`.execute(db);
//   await sql`ALTER TABLE geofences DROP COLUMN IF EXISTS description`.execute(db);
//   await sql`ALTER TABLE geofences DROP COLUMN IF EXISTS name`.execute(db);
  
//   // Remove geofence_id from actions
//   await sql`ALTER TABLE actions DROP COLUMN IF EXISTS geofence_id`.execute(db);
  
//   // Remove action type
//   await sql`DELETE FROM action_types WHERE action_type = 'LOCATION_CHECKIN'`.execute(db);
// }
