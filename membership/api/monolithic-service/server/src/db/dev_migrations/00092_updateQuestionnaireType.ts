import { Kysely, sql } from 'kysely';
import { TABLE_QUESTIONNAIRE_QUESTIONS, TABLE_QUESTIONNAIRES, TABLE_QUESTIONNAIRE_RESULT_RANKS } from '../../constants/database';

const questionnaireTypeConstraint = `${TABLE_QUESTIONNAIRES}_questionnaire_type_check`;
const questionnaireQuestionTypeConstraint = `${TABLE_QUESTIONNAIRE_QUESTIONS}_question_type_check`;
const questionnaireResultRankConstraint = 'fk_questionnaire_result_answers_result_rank_id';

export async function up(db: Kysely<unknown>): Promise<void> {
  await db.schema.alterTable(TABLE_QUESTIONNAIRES).dropConstraint(questionnaireTypeConstraint).execute();
  await db.schema.alterTable(TABLE_QUESTIONNAIRE_QUESTIONS).dropConstraint(questionnaireQuestionTypeConstraint).execute();
  await db.schema.alterTable(TABLE_QUESTIONNAIRES)
    .addCheckConstraint(
      questionnaireTypeConstraint,
      sql`questionnaire_type IN ('QUIZ', 'SURVEY', 'MESSAGE')`,
    )
    .execute();

  await db.schema.alterTable(TABLE_QUESTIONNAIRE_QUESTIONS)
    .addCheckConstraint(
      questionnaireQuestionTypeConstraint,
      sql`question_type IN ('SINGLE-CHOICE', 'TEXT', 'TEXT-LINES', 'NUMBER', 'IMAGE')`,
    )
    .execute();

  await db.schema.alterTable(TABLE_QUESTIONNAIRE_RESULT_RANKS).dropConstraint(questionnaireResultRankConstraint).execute();
  await db.schema.alterTable(TABLE_QUESTIONNAIRE_RESULT_RANKS)
    .alterColumn('rank_id', (ac) => ac.setNotNull())
    .execute();
}

export async function down(db: Kysely<unknown>): Promise<void> {
  await db.schema.alterTable(TABLE_QUESTIONNAIRES).dropConstraint(questionnaireTypeConstraint).execute();
  await db.schema.alterTable(TABLE_QUESTIONNAIRE_QUESTIONS).dropConstraint(questionnaireQuestionTypeConstraint).execute();
  await db.schema.alterTable(TABLE_QUESTIONNAIRES)
    .addCheckConstraint(
      questionnaireTypeConstraint,
      sql`questionnaire_type IN ('QUIZ', 'SURVEY')`,
    )
    .execute();

  await db.schema.alterTable(TABLE_QUESTIONNAIRE_QUESTIONS)
    .addCheckConstraint(
      questionnaireQuestionTypeConstraint,
      sql`question_type IN ('SINGLE-CHOICE', 'TEXT', 'TEXT-LINES', 'NUMBER')`,
    )
    .execute();

  await db.schema.alterTable(TABLE_QUESTIONNAIRE_RESULT_RANKS)
    .alterColumn('rank_id', (ac) => ac.dropNotNull())
    .execute();
  await db.schema.alterTable(TABLE_QUESTIONNAIRE_RESULT_RANKS)
    .addForeignKeyConstraint(
      'fk_questionnaire_result_answers_result_rank_id',
      ['rank_id'],
      TABLE_QUESTIONNAIRE_RESULT_RANKS,
      ['rank_id'],
    )
    .execute();
}
