/* eslint-disable @typescript-eslint/no-explicit-any */
import { Kysely, sql } from 'kysely';
import {
  TABLE_QUESTIONNAIRE_QUESTION_TRANSLATIONS,
  TABLE_QUESTIONNAIRE_QUESTIONS,
  TABLE_QUESTIONNAIRE_THEMES,
} from '../../constants/database';

const questionnaireQuestionTypeConstraint = `${TABLE_QUESTIONNAIRE_QUESTIONS}_question_type_check`;

export async function up(db: Kysely<any>): Promise<void> {
  //questionnaire_themes の NOT NULL 制約を解除
  await db.schema
    .alterTable(TABLE_QUESTIONNAIRE_THEMES)
    .alterColumn('theme_thumbnail_image_url', (col) => col.dropNotNull())
    .alterColumn('theme_cover_image_url', (col) => col.dropNotNull())
    .execute();

  // questionnaire_questions にカラム追加
  await db.schema
    .alterTable(TABLE_QUESTIONNAIRE_QUESTIONS)
    .addColumn('is_required', 'boolean')
    .addColumn('question_image_url', 'varchar(512)')
    .execute();

  const serviceIds = await db.selectFrom('services').select('service_id').execute();
  for (const {service_id: serviceId} of serviceIds) {
    await sql`SELECT set_config('multitenant.service_id', ${serviceId}, false)`.execute(db);
    await db
      .updateTable(TABLE_QUESTIONNAIRE_QUESTIONS)
      .set({ is_required: true })
      .where('service_id', '=', serviceId)
      .execute();
  }

  await db.schema
    .alterTable(TABLE_QUESTIONNAIRE_QUESTIONS)
    .alterColumn('is_required', (col) => col.setNotNull())
    .execute();

  // questionnaire_questions の question_type 制約を更新
  await db.schema
    .alterTable(TABLE_QUESTIONNAIRE_QUESTIONS)
    .dropConstraint(questionnaireQuestionTypeConstraint)
    .execute();

  // questionnaire_question_translations の question_extra をnotNullに設定
  await db.schema
    .alterTable(TABLE_QUESTIONNAIRE_QUESTION_TRANSLATIONS)
    .alterColumn('question_extra', (col) => col.setNotNull())
    .execute();
}

export async function down(db: Kysely<any>): Promise<void> {
  // rollback: questionnaire_themes の NOT NULL 制約を元に戻す
  await db.schema
    .alterTable(TABLE_QUESTIONNAIRE_THEMES)
    .alterColumn('theme_thumbnail_image_url', (col) => col.setNotNull())
    .alterColumn('theme_cover_image_url', (col) => col.setNotNull())
    .execute();

  // rollback: questionnaire_questions のカラム削除
  await db.schema
    .alterTable(TABLE_QUESTIONNAIRE_QUESTIONS)
    .dropColumn('is_required')
    .dropColumn('question_image_url')
    .execute();

  // rollback: questionnaire_questions の question_type 制約を元に戻す
  await db.schema
    .alterTable(TABLE_QUESTIONNAIRE_QUESTIONS)
    .dropConstraint(questionnaireQuestionTypeConstraint)
    .execute();

  await db.schema
    .alterTable(TABLE_QUESTIONNAIRE_QUESTIONS)
    .addCheckConstraint(
      questionnaireQuestionTypeConstraint,
      sql`question_type IN ('SINGLE-CHOICE', 'TEXT', 'TEXT-LINES', 'NUMBER', 'IMAGE')`,
    )
    .execute();

  // questionnaire_question_translations の question_extra の NOT NULL 制約を削除
  await db.schema
    .alterTable(TABLE_QUESTIONNAIRE_QUESTION_TRANSLATIONS)
    .alterColumn('question_extra', (col) => col.dropNotNull())
    .execute();
}
