import { Kysely, sql } from 'kysely';
import {
  TABLE_SERVICE_CUSTOM_FIELDS,
  TABLE_SERVICE_CUSTOM_FIELD_TRANSLATIONS,
  TABLE_SERVICE_CUSTOM_FIELD_OPTIONS,
  TABLE_SERVICE_CUSTOM_FIELD_OPTION_TRANSLATIONS,
  TABLE_SERVICE_CUSTOM_FIELD_VALIDATORS,
  TABLE_SERVICE_CUSTOM_FIELD_VALIDATOR_TRANSLATIONS
} from '../../constants/database';

export async function up(db: Kysely<any>): Promise<void> {
  await db.schema
    .createTable(TABLE_SERVICE_CUSTOM_FIELDS)
    .addColumn('custom_field_id', 'uuid', (col) => col.primaryKey())
    .addColumn('service_id', 'varchar(128)', (col) => col.notNull())
    .addColumn('field_key', 'text', (col) => col.notNull())
    .addColumn('version', 'integer', (col) => col.notNull().defaultTo(1))
    .addColumn('type', 'text', (col) => col.notNull().check(sql`type IN ('TEXT', 'NUMERIC', 'EMAIL', 'PHONE', 'SELECTION', 'MULTIPLE-SELECTION')`))
    .addColumn('default_value', 'text')
    .addColumn('max_length', 'integer')
    .addColumn('min_length', 'integer')
    .addColumn('optional', 'boolean', (col) => col.notNull())
    .addColumn('unique', 'boolean', (col) => col.notNull())
    .addColumn('verify', 'boolean', (col) => col.notNull())
    .addColumn('sort_order', 'integer', (col) => col.notNull())
    .addColumn('created_at', 'timestamp', (col) => col.notNull().defaultTo(sql`CURRENT_TIMESTAMP`))
    .addUniqueConstraint('unique_custom_field', ['service_id', 'field_key', 'version'])
    .execute();

  await db.schema
    .createTable(TABLE_SERVICE_CUSTOM_FIELD_TRANSLATIONS)
    .addColumn('custom_field_translation_id', 'uuid', (col) => col.primaryKey())
    .addColumn('service_id', 'varchar(128)', (col) => col.notNull())
    .addColumn('custom_field_id', 'uuid', (col) => col.notNull())
    .addColumn('locale', 'varchar(10)', (col) => col.notNull())
    .addColumn('label', 'text', (col) => col.notNull())
    .addUniqueConstraint('unique_custom_field_translation', ['custom_field_id', 'locale'])
    .execute();

  await db.schema
    .alterTable(TABLE_SERVICE_CUSTOM_FIELD_TRANSLATIONS)
    .addForeignKeyConstraint(
      'fk_custom_field_translation_custom_field_id',
      ['custom_field_id'],
      TABLE_SERVICE_CUSTOM_FIELDS,
      ['custom_field_id'],
    )
    .execute();

  await db.schema
    .createTable(TABLE_SERVICE_CUSTOM_FIELD_OPTIONS)
    .addColumn('custom_field_option_id', 'uuid', (col) => col.primaryKey().defaultTo(sql`gen_random_uuid()`))
    .addColumn('service_id', 'varchar(128)', (col) => col.notNull())
    .addColumn('custom_field_id', 'uuid', (col) => col.notNull())
    .addColumn('value', 'text', (col) => col.notNull())
    .addColumn('sort_order', 'integer', (col) => col.notNull().defaultTo(0))
    .execute();

  await db.schema
    .alterTable(TABLE_SERVICE_CUSTOM_FIELD_OPTIONS)
    .addForeignKeyConstraint(
      'fk_custom_field_option_custom_field_id',
      ['custom_field_id'],
      TABLE_SERVICE_CUSTOM_FIELDS,
      ['custom_field_id'],
    )
    .execute();

  await db.schema
    .createTable(TABLE_SERVICE_CUSTOM_FIELD_OPTION_TRANSLATIONS)
    .addColumn('custom_field_option_translation_id', 'uuid', (col) => col.primaryKey().defaultTo(sql`gen_random_uuid()`))
    .addColumn('service_id', 'varchar(128)', (col) => col.notNull())
    .addColumn('custom_field_option_id', 'uuid', (col) => col.notNull())
    .addColumn('locale', 'varchar(10)', (col) => col.notNull())
    .addColumn('label', 'text', (col) => col.notNull())
    .addUniqueConstraint('unique_custom_field_option_translation', ['custom_field_option_id', 'locale'])
    .execute();

  await db.schema
    .alterTable(TABLE_SERVICE_CUSTOM_FIELD_OPTION_TRANSLATIONS)
    .addForeignKeyConstraint(
      'fk_custom_field_option_translation_option_id',
      ['custom_field_option_id'],
      TABLE_SERVICE_CUSTOM_FIELD_OPTIONS,
      ['custom_field_option_id'],
    )
    .execute();

  await db.schema
    .createTable(TABLE_SERVICE_CUSTOM_FIELD_VALIDATORS)
    .addColumn('validator_id', 'uuid', (col) => col.primaryKey().defaultTo(sql`gen_random_uuid()`))
    .addColumn('service_id', 'varchar(128)', (col) => col.notNull())
    .addColumn('custom_field_id', 'uuid', (col) => col.notNull())
    .addColumn('pattern', 'text', (col) => col.notNull())
    .execute();

  await db.schema
    .alterTable(TABLE_SERVICE_CUSTOM_FIELD_VALIDATORS)
    .addForeignKeyConstraint(
      'fk_custom_field_validator_custom_field_id',
      ['custom_field_id'],
      TABLE_SERVICE_CUSTOM_FIELDS,
      ['custom_field_id'],
    )
    .execute();

  await db.schema
    .createTable(TABLE_SERVICE_CUSTOM_FIELD_VALIDATOR_TRANSLATIONS)
    .addColumn('custom_field_validator_translation_id', 'uuid', (col) => col.primaryKey().defaultTo(sql`gen_random_uuid()`))
    .addColumn('service_id', 'varchar(128)', (col) => col.notNull())
    .addColumn('validator_id', 'uuid', (col) => col.notNull())
    .addColumn('locale', 'varchar(10)', (col) => col.notNull())
    .addColumn('error_message', 'text', (col) => col.notNull())
    .addColumn('description', 'text')
    .addUniqueConstraint('unique_custom_field_validator_translation', ['validator_id', 'locale'])
    .execute();

  await db.schema
    .alterTable(TABLE_SERVICE_CUSTOM_FIELD_VALIDATOR_TRANSLATIONS)
    .addForeignKeyConstraint(
      'fk_custom_field_validator_translation_validator_id',
      ['validator_id'],
      TABLE_SERVICE_CUSTOM_FIELD_VALIDATORS,
      ['validator_id'],
    )
    .execute();

  await sql`ALTER TABLE ${sql.ref(TABLE_SERVICE_CUSTOM_FIELDS)} ENABLE ROW LEVEL SECURITY`.execute(db);
  await sql`ALTER TABLE ${sql.ref(TABLE_SERVICE_CUSTOM_FIELDS)} FORCE ROW LEVEL SECURITY`.execute(db);
  await sql`CREATE POLICY tenant_isolation_policy ON ${sql.ref(TABLE_SERVICE_CUSTOM_FIELDS)} USING ("service_id" = current_setting('multitenant.service_id'))`.execute(db);

  await sql`ALTER TABLE ${sql.ref(TABLE_SERVICE_CUSTOM_FIELD_TRANSLATIONS)} ENABLE ROW LEVEL SECURITY`.execute(db);
  await sql`ALTER TABLE ${sql.ref(TABLE_SERVICE_CUSTOM_FIELD_TRANSLATIONS)} FORCE ROW LEVEL SECURITY`.execute(db);
  await sql`CREATE POLICY tenant_isolation_policy ON ${sql.ref(TABLE_SERVICE_CUSTOM_FIELD_TRANSLATIONS)} USING ("service_id" = current_setting('multitenant.service_id'))`.execute(db);

  await sql`ALTER TABLE ${sql.ref(TABLE_SERVICE_CUSTOM_FIELD_OPTIONS)} ENABLE ROW LEVEL SECURITY`.execute(db);
  await sql`ALTER TABLE ${sql.ref(TABLE_SERVICE_CUSTOM_FIELD_OPTIONS)} FORCE ROW LEVEL SECURITY`.execute(db);
  await sql`CREATE POLICY tenant_isolation_policy ON ${sql.ref(TABLE_SERVICE_CUSTOM_FIELD_OPTIONS)} USING ("service_id" = current_setting('multitenant.service_id'))`.execute(db);

  await sql`ALTER TABLE ${sql.ref(TABLE_SERVICE_CUSTOM_FIELD_OPTION_TRANSLATIONS)} ENABLE ROW LEVEL SECURITY`.execute(db);
  await sql`ALTER TABLE ${sql.ref(TABLE_SERVICE_CUSTOM_FIELD_OPTION_TRANSLATIONS)} FORCE ROW LEVEL SECURITY`.execute(db);
  await sql`CREATE POLICY tenant_isolation_policy ON ${sql.ref(TABLE_SERVICE_CUSTOM_FIELD_OPTION_TRANSLATIONS)} USING ("service_id" = current_setting('multitenant.service_id'))`.execute(db);

  await sql`ALTER TABLE ${sql.ref(TABLE_SERVICE_CUSTOM_FIELD_VALIDATORS)} ENABLE ROW LEVEL SECURITY`.execute(db);
  await sql`ALTER TABLE ${sql.ref(TABLE_SERVICE_CUSTOM_FIELD_VALIDATORS)} FORCE ROW LEVEL SECURITY`.execute(db);
  await sql`CREATE POLICY tenant_isolation_policy ON ${sql.ref(TABLE_SERVICE_CUSTOM_FIELD_VALIDATORS)} USING ("service_id" = current_setting('multitenant.service_id'))`.execute(db);

  await sql`ALTER TABLE ${sql.ref(TABLE_SERVICE_CUSTOM_FIELD_VALIDATOR_TRANSLATIONS)} ENABLE ROW LEVEL SECURITY`.execute(db);
  await sql`ALTER TABLE ${sql.ref(TABLE_SERVICE_CUSTOM_FIELD_VALIDATOR_TRANSLATIONS)} FORCE ROW LEVEL SECURITY`.execute(db);
  await sql`CREATE POLICY tenant_isolation_policy ON ${sql.ref(TABLE_SERVICE_CUSTOM_FIELD_VALIDATOR_TRANSLATIONS)} USING ("service_id" = current_setting('multitenant.service_id'))`.execute(db);
}

export async function down(db: Kysely<unknown>): Promise<void> {
  await db.schema.dropTable(TABLE_SERVICE_CUSTOM_FIELD_VALIDATOR_TRANSLATIONS).execute();
  await db.schema.dropTable(TABLE_SERVICE_CUSTOM_FIELD_VALIDATORS).execute();
  await db.schema.dropTable(TABLE_SERVICE_CUSTOM_FIELD_OPTION_TRANSLATIONS).execute();
  await db.schema.dropTable(TABLE_SERVICE_CUSTOM_FIELD_OPTIONS).execute();
  await db.schema.dropTable(TABLE_SERVICE_CUSTOM_FIELD_TRANSLATIONS).execute();
  await db.schema.dropTable(TABLE_SERVICE_CUSTOM_FIELDS).execute();
}
