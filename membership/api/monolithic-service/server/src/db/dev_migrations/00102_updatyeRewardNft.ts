/* eslint-disable @typescript-eslint/no-explicit-any */
import { Kys<PERSON> } from 'kysely';
export async function up(db: <PERSON>ysely<any>): Promise<void> {
  await db.schema.alterTable('digital_content_rewards').dropColumn('token_id').execute();
  await db.schema.alterTable('certificate_rewards').dropColumn('token_id').execute();
}

export async function down(db: <PERSON>ysely<any>): Promise<void> {
  await db.schema.alterTable('digital_content_rewards').addColumn('token_id', 'integer').execute();
  await db.schema.alterTable('certificate_rewards').addColumn('token_id', 'integer').execute();
}
