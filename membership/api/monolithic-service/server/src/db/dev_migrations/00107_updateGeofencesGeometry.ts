import { Kysely, sql } from 'kysely';
import { TABLE_GEOFENCES, TABLE_ACTIONS, TABLE_LOCATION_CHECKIN_ACTIONS } from '../../constants/database';

const actionTypeConstraint = `${TABLE_ACTIONS}_action_type_check`;
const circleFieldsNotNullConstraint = `${TABLE_GEOFENCES}_circle_fields_not_null`;
const polygonGeometryNotNullConstraint = `${TABLE_GEOFENCES}_polygon_geometry_not_null`;

export async function up(db: Kysely<any>): Promise<void> {
  await db.schema
    .alterTable(TABLE_GEOFENCES)
    .dropColumn('center_coordinate_latitude')
    .dropColumn('center_coordinate_longtitude')
    .dropColumn('geofence_radius')
    .addColumn('geofence_type', 'varchar(32)', (col) =>
      col.check(sql`geofence_type IN ('CIRCLE', 'POLYGON')`),
    )
    .addColumn('circle_radius', 'varchar(128)')
    .addColumn('circle_geometry', sql`geometry(Point,4326)`)
    .addColumn('polygon_geometry', sql`geometry(Polygon,4326)`)
    .execute();

  await db.schema
    .alterTable(TABLE_GEOFENCES)
    .addCheckConstraint(
      circleFieldsNotNullConstraint,
      sql`(geofence_type != 'CIRCLE' OR (circle_radius     IS NOT NULL AND circle_geometry   IS NOT NULL))`,
    )
    .execute();

  await db.schema
    .alterTable(TABLE_GEOFENCES)
    .addCheckConstraint(
      polygonGeometryNotNullConstraint,
      sql`(geofence_type != 'POLYGON' OR (polygon_geometry IS NOT NULL))`,
    )
    .execute();

  await db.schema
    .alterTable(TABLE_ACTIONS)
    .dropColumn('geofence_id')
    .execute();
  await db.schema.alterTable(TABLE_ACTIONS).dropConstraint(actionTypeConstraint).execute();
  await db.schema
    .alterTable(TABLE_ACTIONS)
    .addCheckConstraint(
      actionTypeConstraint,
      sql`action_type IN ('ACHIEVEMENT', 'QR-CHECKIN', 'ONLINE-CHECKIN', 'QUESTIONNAIRE', 'CONTENTS-PURCHASE', 'SERIAL-CODE', 'LOCATION-CHECKIN')`,
    )
    .execute();

  await db.schema
    .createTable(TABLE_LOCATION_CHECKIN_ACTIONS)
    .addColumn('action_id', 'varchar(128)', (col) => col.primaryKey())
    .addColumn('service_id', 'varchar(128)', (col) => col.notNull())
    .addColumn('geofence_id', 'varchar(128)', (col) => col.notNull())
    .execute();
}

export async function down(db: Kysely<any>): Promise<void> {
  await db.schema.alterTable(TABLE_GEOFENCES).dropConstraint(circleFieldsNotNullConstraint).execute();
  await db.schema.alterTable(TABLE_GEOFENCES).dropConstraint(polygonGeometryNotNullConstraint).execute();

  await db.schema
    .alterTable(TABLE_GEOFENCES)
    .dropColumn('geofence_type')
    .dropColumn('circle_radius')
    .dropColumn('circle_geometry')
    .dropColumn('polygon_geometry')
    .execute();

  await db.schema
    .alterTable(TABLE_GEOFENCES)
    .addColumn('center_coordinate_latitude', 'varchar(128)')
    .addColumn('center_coordinate_longtitude', 'varchar(128)')
    .addColumn('geofence_radius', 'varchar(128)')
    .execute();

  await db.schema
    .alterTable(TABLE_ACTIONS)
    .addColumn('geofence_id', 'varchar(128)')
    .execute();

  await db.schema
    .dropTable(TABLE_LOCATION_CHECKIN_ACTIONS)
    .execute();

}
