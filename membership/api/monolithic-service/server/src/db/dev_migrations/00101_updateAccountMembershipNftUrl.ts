/* eslint-disable @typescript-eslint/no-explicit-any */
import { Kysely, sql } from 'kysely';
import {
  TABLE_ACCOUNTS,
  TABLE_NFT_BASE_METADATAS,
  TABLE_NFT_CONTRACTS,
  TABLE_SERVICES,
} from '../../constants/database';
import { config } from '../../configs/config';

export async function up(db: Kysely<any>): Promise<void> {
  const baseUrl = config.metadataUrl;
  await db.schema.alterTable(TABLE_ACCOUNTS).addColumn('membership_metadata_url', 'varchar(512)').execute();

  const serviceIds = await db.selectFrom(TABLE_SERVICES).select('service_id').execute();
  for (const { service_id: serviceId } of serviceIds) {
    await sql`SELECT set_config('multitenant.service_id', ${serviceId}, false)`.execute(db);
    const service = await db
      .selectFrom(TABLE_SERVICES)
      .select('membership_nft_contract_id')
      .where('service_id', '=', serviceId)
      .executeTakeFirst();

    const contractId = service!.membership_nft_contract_id as string;
    const nftBaseMetadata = await db
      .selectFrom(`${TABLE_NFT_BASE_METADATAS} as nbm`)
      .leftJoin(`${TABLE_NFT_CONTRACTS} as nc`, 'nbm.contract_address', 'nc.nft_contract_address')
      .select('base_metadata_id')
      .where('nc.nft_contract_id', '=', contractId)
      .executeTakeFirst();

    const metadataUrl = `${baseUrl}/${nftBaseMetadata?.base_metadata_id}/`;

    await db
      .updateTable(TABLE_ACCOUNTS)
      .set({
        membership_metadata_url: metadataUrl,
      })
      .execute();

    await db
      .updateTable(TABLE_ACCOUNTS)
      .set({
        membership_metadata_url: sql`membership_metadata_url ||  membership_id`,
      })
      .execute();
  }
}

export async function down(db: Kysely<any>): Promise<void> {
  await db.schema.alterTable(TABLE_ACCOUNTS).dropColumn('membership_metadata_url').execute();
}
