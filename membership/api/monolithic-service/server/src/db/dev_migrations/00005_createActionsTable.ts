import { Kysely, sql } from 'kysely';

 
export async function up(db: Kysely<any>): Promise<void> {
  await db.schema
    .createTable('actions')
    .addColumn('action_id', 'varchar(128)', (col) => col.primary<PERSON>ey())
    .addColumn('service_id', 'varchar(128)', (col) => col.notNull())
    .addColumn('action_title', 'varchar(256)')
    .addColumn('action_description', 'text')
    .addColumn('action_cover_image_url', 'varchar(1024)')
    .addColumn('action_thumbnail_image_url', 'varchar(1024)')
    .addColumn('action_label', 'varchar(128)')
    .addColumn('action_available_start_date', 'timestamp', (col) => col.notNull())
    .addColumn('action_available_end_date', 'timestamp', (col) => col.notNull())
    .addColumn('action_type', 'varchar(128)', (col) =>
      col
        .notNull()
        .check(
          sql`action_type IN ('ACHIEVEMENT', 'QR-CHECKIN', 'ONLINE-CHECKIN', 'QUESTIONNAIRE', 'CONTENTS-PURCHASE', 'SERIAL-CODE')`,
        ),
    )
    .addColumn('geofence_id', 'varchar(128)')
    .execute();

  await db.schema
    .alterTable('actions')
    .addForeignKeyConstraint('actions_geofence_id_fkey', ['geofence_id'], 'geofences', ['geofence_id'])
    .execute();

  await sql`ALTER TABLE "actions" ENABLE ROW LEVEL SECURITY`.execute(db);
  await sql`ALTER TABLE "actions" FORCE ROW LEVEL SECURITY`.execute(db);

  await sql`CREATE POLICY tenant_isolation_policy ON "actions" USING ("service_id" = current_setting('multitenant.service_id'))`.execute(
    db,
  );
}

export async function down(db: Kysely<unknown>): Promise<void> {
  await db.schema.dropTable('actions').execute();
}
