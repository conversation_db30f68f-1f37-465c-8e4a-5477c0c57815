import { Kysely, sql } from 'kysely';
import { 
  TABLE_ACCOUNT_CUSTOM_FIELD_VALUES,
  TABLE_SERVICE_CUSTOM_FIELDS,
  TABLE_ACCOUNTS
} from '../../constants/database';

export async function up(db: Kysely<any>): Promise<void> {
  await db.schema
    .createTable(TABLE_ACCOUNT_CUSTOM_FIELD_VALUES)
    .addColumn('custom_field_value_id', 'uuid', (col) => col.primaryKey().defaultTo(sql`gen_random_uuid()`))
    .addColumn('service_id', 'varchar(128)', (col) => col.notNull())
    .addColumn('custom_field_id', 'uuid', (col) => col.notNull())
    .addColumn('account_id', 'varchar(128)', (col) => col.notNull())
    .addColumn('value', 'text', (col) => col.notNull())
    .addColumn('verified', 'boolean', (col) => col.notNull())
    .addColumn('created_at', 'timestamp', (col) => col.notNull().defaultTo(sql`CURRENT_TIMESTAMP`))
    .execute();
  await sql`SELECT set_config('multitenant.service_id', '', false)`.execute(db);

  await db.schema
    .alterTable(TABLE_ACCOUNT_CUSTOM_FIELD_VALUES)
    .addForeignKeyConstraint(
      'fk_account_custom_field_values_custom_field_id',
      ['custom_field_id'],
      TABLE_SERVICE_CUSTOM_FIELDS,
      ['custom_field_id'],
    )
    .execute();

  await db.schema
    .alterTable(TABLE_ACCOUNT_CUSTOM_FIELD_VALUES)
    .addForeignKeyConstraint(
      'fk_account_custom_field_values_account_id',
      ['account_id'],
      TABLE_ACCOUNTS,
      ['account_id'],
    )
    .execute();

  await sql`ALTER TABLE ${sql.ref(TABLE_ACCOUNT_CUSTOM_FIELD_VALUES)} ENABLE ROW LEVEL SECURITY`.execute(db);
  await sql`ALTER TABLE ${sql.ref(TABLE_ACCOUNT_CUSTOM_FIELD_VALUES)} FORCE ROW LEVEL SECURITY`.execute(db);
  await sql`CREATE POLICY tenant_isolation_policy ON ${sql.ref(TABLE_ACCOUNT_CUSTOM_FIELD_VALUES)} USING ("service_id" = current_setting('multitenant.service_id'))`.execute(db);
}

export async function down(db: Kysely<unknown>): Promise<void> {
  await db.schema.dropTable(TABLE_ACCOUNT_CUSTOM_FIELD_VALUES).execute();
}