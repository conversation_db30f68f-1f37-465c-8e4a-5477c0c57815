import { Kysely, sql } from 'kysely';
import { TABLE_SERIAL_CODE_PROJECTS, TABLE_REWARDS } from '../../constants/database';

export async function up(db: Kysely<any>): Promise<void> {
  await db.schema
    .createTable(TABLE_SERIAL_CODE_PROJECTS)
    .addColumn('serial_code_project_id', 'varchar(128)', (col) => col.primaryKey())
    .addColumn('service_id', 'varchar(128)', (col) => col.notNull())
    .addColumn('name', 'varchar(256)', (col) => col.notNull())
    .addColumn('description', 'varchar(1024)', (col) => col.notNull())
    .addColumn('reward_id', 'varchar(128)', (col) => col.references(`${TABLE_REWARDS}.reward_id`).notNull())
    .addColumn('hash_key', 'varchar(128)', (col) => col.notNull())
    .addColumn('status', 'varchar(128)', (col) => col.check(sql`status IN ('DISABLE', 'DRAFT', 'ENABLE')`))
    .addColumn('start_at', 'timestamp', (col) => col.notNull())
    .addColumn('end_at', 'timestamp', (col) => col.notNull())
    .addColumn('created_at', 'timestamp', (col) => col.defaultTo(sql`CURRENT_TIMESTAMP`).notNull())
    .addColumn('updated_at', 'timestamp', (col) => col.defaultTo(sql`CURRENT_TIMESTAMP`).notNull())
    .execute();
}

export async function down(db: Kysely<unknown>): Promise<void> {
  await db.schema.dropTable(TABLE_SERIAL_CODE_PROJECTS).execute();
}
