/* eslint-disable @typescript-eslint/no-explicit-any */
import { Kysely } from 'kysely';
import { TABLE_SERIAL_CODE_PROJECTS } from '../../constants/database';

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  await db.schema
    .alterTable(TABLE_SERIAL_CODE_PROJECTS)
    .alterColumn('reward_id', (col) => col.dropNotNull())
    .execute();
}

export async function down(db: Kysely<any>): Promise<void> {
  await db.schema
    .alterTable(TABLE_SERIAL_CODE_PROJECTS)
    .alterColumn('reward_id', (col) => col.setNotNull())
    .execute();
}
