import { Kysely, sql } from 'kysely';
import { TABLE_SERIAL_CODES, TABLE_SERIAL_CODE_PROJECTS } from '../../constants/database';

export async function up(db: Kysely<any>): Promise<void> {
  await db.schema
    .createTable(TABLE_SERIAL_CODES)
    .addColumn('serial_code_id', 'varchar(128)', (col) => col.primaryKey())
    .addColumn('code_hash', 'varchar(128)', (col) => col.notNull().unique())
    .addColumn('serial_code_project_id', 'varchar(128)', (col) => col.references(`${TABLE_SERIAL_CODE_PROJECTS}.serial_code_project_id`).notNull())
    .addColumn('max_use_num', 'integer', (col) => col.notNull())
    .addColumn('remaining_use_num', 'integer', (col) => col.notNull())
    .addColumn('created_at', 'timestamp', (col) => col.defaultTo(sql`CURRENT_TIMESTAMP`).notNull())
    .addColumn('updated_at', 'timestamp', (col) => col.defaultTo(sql`CURRENT_TIMESTAMP`).notNull())
    .execute();
}

export async function down(db: Kysely<unknown>): Promise<void> {
  await db.schema.dropTable(TABLE_SERIAL_CODES).execute();
}
