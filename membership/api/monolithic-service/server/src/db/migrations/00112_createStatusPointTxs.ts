import { Kysely, sql } from 'kysely';
import { TABLE_ACCOUNTS, TABLE_STATUS_POINT_TXS, TABLE_SERVICES } from '../../constants/database';
import { applyTenantIsolation, disableRls } from '../utils/rls';

export async function up(db: Kysely<unknown>): Promise<void> {
  await db.schema
    .createTable(TABLE_STATUS_POINT_TXS)
    .addColumn('status_point_tx_id', 'varchar(128)', (col) => col.primaryKey())
    .addColumn('service_id', 'varchar(128)', (col) => col.notNull())
    .addColumn('account_id', 'varchar(128)', (col) => col.notNull())
    .addColumn('amount', 'integer', (col) => col.notNull())
    .addColumn('ops_type', 'varchar(128)', (col) => col.notNull().check(sql`ops_type IN ('ADD', 'SUB')`))
    .addColumn('tx_by', 'varchar(128)', (col) => col.notNull().check(sql`tx_by IN ('USER', 'ADMIN', 'SCHEDULER')`))
    .addColumn('tx_detail', 'varchar(128)', (col) =>
      col.notNull().check(sql`tx_detail IN ('REWARD', 'GACHA', 'ADJUST')`),
    )
    .addColumn('tx_extra', 'varchar(128)')
    .addColumn('created_at', 'timestamp', (col) => col.defaultTo(sql`CURRENT_TIMESTAMP`).notNull())
    .addForeignKeyConstraint('fk_reward_point_txs_service_id', ['service_id'], TABLE_SERVICES, ['service_id'])
    .addForeignKeyConstraint('fk_reward_point_txs_account_id', ['account_id'], TABLE_ACCOUNTS, ['account_id'])
    .addCheckConstraint(
      'check_reward_point_txs_amount',
      sql`(
        (ops_type = 'ADD'  AND amount > 0) OR
        (ops_type = 'SUB' AND amount < 0)
      )`,
    )
    .addCheckConstraint(
      'check_tx_extra',
      sql`((tx_detail != 'REWARD' AND tx_detail != 'GACHA') OR tx_extra IS NOT NULL)`,
    )
    .execute();

  await applyTenantIsolation(db, TABLE_STATUS_POINT_TXS);
}

export async function down(db: Kysely<unknown>): Promise<void> {
  await disableRls(db, TABLE_STATUS_POINT_TXS);
  await db.schema.dropTable(TABLE_STATUS_POINT_TXS).execute();
}
