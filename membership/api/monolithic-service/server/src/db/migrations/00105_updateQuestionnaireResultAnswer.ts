import { Kysely } from 'kysely';
import { TABLE_QUESTIONNAIRE_RESULT_ANSWERS } from '../../constants/database';

export async function up(db: Kysely<unknown>): Promise<void> {
  await db.schema
    .alterTable(TABLE_QUESTIONNAIRE_RESULT_ANSWERS)
    .alterColumn('rank_id', (col) => col.dropNotNull())
    .execute();
}

export async function down(db: Kysely<unknown>): Promise<void> {
  await db.schema
    .alterTable(TABLE_QUESTIONNAIRE_RESULT_ANSWERS)
    .alterColumn('rank_id', (col) => col.setNotNull())
    .execute();
}
