import { Kysely, sql } from 'kysely';
import { TABLE_TRANSACTIONS } from '../../constants/database';

export async function up(db: <PERSON><PERSON><PERSON><any>): Promise<void> {
  await db.schema.alterTable(TABLE_TRANSACTIONS).dropConstraint('transactions_status_check').execute();

  await db.schema
    .alterTable(TABLE_TRANSACTIONS)
    .addCheckConstraint(
      'transactions_status_check',
      sql`status IN ('EXECUTED', 'FAILED','RETRY','MINED','CONFIRMED','REORGED')`,
    )
    .execute();
}

export async function down(db: <PERSON>ysely<any>): Promise<void> {
  await db.schema.alterTable(TABLE_TRANSACTIONS).dropConstraint('transactions_status_check').execute();
  await db.schema
    .alterTable(TABLE_TRANSACTIONS)
    .addCheckConstraint('transactions_status_check', sql`status IN ('EXECUTED', 'FAILED','RETRY','MINED')`)
    .execute();
}
