/* eslint-disable @typescript-eslint/no-explicit-any */
import { Kysely, sql } from 'kysely';
import { TABLE_ACCOUNTS, TABLE_SERVICES } from '../../constants/database';

export async function up(db: Kysely<any>): Promise<void> {
  await db.schema.alterTable(TABLE_ACCOUNTS).addColumn('last_login_at', 'timestamp').execute();

  const serviceIds = await db.selectFrom(TABLE_SERVICES).select('service_id').execute();
  for (const { service_id: serviceId } of serviceIds) {
    await sql`SELECT set_config('multitenant.service_id', ${serviceId}, false)`.execute(db);
    await db
      .updateTable(TABLE_ACCOUNTS)
      .set({
        last_login_at: sql`updated_at`
      })
      .execute();
  }

 await db.schema.alterTable(TABLE_ACCOUNTS).alterColumn('last_login_at', (col) => col.setNotNull()).execute();
}

export async function down(db: Kysely<any>): Promise<void> {
  await db.schema
    .alterTable(TABLE_ACCOUNTS)
    .dropColumn('last_login_at')
    .execute();
}
