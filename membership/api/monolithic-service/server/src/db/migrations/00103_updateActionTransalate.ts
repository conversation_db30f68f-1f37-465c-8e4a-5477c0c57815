/* eslint-disable @typescript-eslint/no-explicit-any */
import { Kysely, sql } from 'kysely';
import {
  TABLE_ACHIEVEMENT_ACTIONS,
  TABLE_ACTION_TRANSLATIONS,
  TABLE_ACTIONS,
  TABLE_QUESTIONNAIRE_ACTIONS,
  TABLE_QUESTIONNAIRES,
  TABLE_SERIAL_CODE_ACTIONS,
  TABLE_SERIAL_CODE_PROJECTS,
} from '../../constants/database';
import { disableRls, enableRls } from '../utils/rls';

export async function up(db: Kysely<any>): Promise<void> {
  // Add action_label to action_translations
  await disableRls(db, TABLE_ACTIONS);
  await disableRls(db, TABLE_ACTION_TRANSLATIONS);
  await disableRls(db, TABLE_ACHIEVEMENT_ACTIONS);
  await disableRls(db, TABLE_QUESTIONNAIRES);

  await db.schema.alterTable(TABLE_ACTION_TRANSLATIONS).addColumn('action_label', 'varchar(128)').execute();

  const serviceIds = await db.selectFrom('services').select('service_id').execute();
  for (const { service_id: serviceId } of serviceIds) {
    await sql`SELECT set_config('multitenant.service_id', ${serviceId}, false)`.execute(db);
    await db
      .updateTable(TABLE_ACTION_TRANSLATIONS)
      .set({
        action_label: sql`a.action_label`,
      })
      .from('actions as a')
      .whereRef('action_translations.action_id', '=', 'a.action_id')
      .where('a.service_id', '=', serviceId)
      .execute();
  }

  await db.schema.alterTable(TABLE_ACTIONS).dropColumn('action_label').execute();

  // Rename quest_completion_goal to milestone and set not null
  await sql`
    ALTER TABLE achievement_actions
    RENAME COLUMN quest_completion_goal TO milestone;
  `.execute(db);
  await db.schema
    .alterTable(TABLE_ACHIEVEMENT_ACTIONS)
    .alterColumn('milestone', (c) => c.setNotNull())
    .alterColumn('status_rank', (c) => c.setNotNull())
    .execute();

  // Create serial_code_actions table
  await db.schema
    .createTable(TABLE_SERIAL_CODE_ACTIONS)
    .ifNotExists()
    .addColumn('action_id', 'varchar(128)', (c) => c.notNull().primaryKey())
    .addColumn('service_id', 'varchar(128)', (c) => c.notNull())
    .addColumn('serial_code_project_id', 'varchar(128)', (c) => c.notNull())
    .execute();

  await db.schema
    .alterTable(TABLE_SERIAL_CODE_ACTIONS)
    .addForeignKeyConstraint('serial_code_actions_action_id_fkey', ['action_id'], TABLE_ACTIONS, ['action_id'])
    .execute();
  await db.schema
    .alterTable(TABLE_SERIAL_CODE_ACTIONS)
    .addForeignKeyConstraint(
      'serial_code_actions_serial_code_project_id_fkey',
      ['serial_code_project_id'],
      TABLE_SERIAL_CODE_PROJECTS,
      ['serial_code_project_id'],
    )
    .execute();

  // Add questionnaire_id to questionnaire_actions
  await db.schema
    .alterTable(TABLE_QUESTIONNAIRE_ACTIONS)
    .addForeignKeyConstraint(
      'questionnaire_actions_questionnaire_id_fkey',
      ['questionnaire_id'],
      TABLE_QUESTIONNAIRES,
      ['questionnaire_id'],
    )
    .execute();

  await enableRls(db, TABLE_ACTIONS);
  await enableRls(db, TABLE_ACTION_TRANSLATIONS);
  await enableRls(db, TABLE_ACHIEVEMENT_ACTIONS);
  await enableRls(db, TABLE_QUESTIONNAIRES);

  await sql`ALTER TABLE "serial_code_actions" ENABLE ROW LEVEL SECURITY`.execute(db);
  await sql`ALTER TABLE "serial_code_actions" FORCE ROW LEVEL SECURITY`.execute(db);

  await sql`CREATE POLICY tenant_isolation_policy ON "serial_code_actions" USING ("service_id" = current_setting('multitenant.service_id'))`.execute(
    db,
  );
}

export async function down(db: Kysely<any>): Promise<void> {
  // Remove questionnaire_id from questionnaire_actions
  await disableRls(db, TABLE_ACTIONS);
  await disableRls(db, TABLE_ACTION_TRANSLATIONS);
  await disableRls(db, TABLE_ACHIEVEMENT_ACTIONS);
  await disableRls(db, TABLE_QUESTIONNAIRES);
  await db.schema.alterTable(TABLE_ACTIONS).addColumn('action_label', 'varchar(128)').execute();

  await db
    .updateTable(TABLE_ACTIONS)
    .set({
      action_label: sql`t.action_label`,
    })
    .from('action_translations as t')
    .whereRef('actions.action_id', '=', 't.action_id')
    .whereRef('t.language', '=', 'ja')
    .execute();

  await db.schema.alterTable(TABLE_ACTION_TRANSLATIONS).dropColumn('action_label').execute();
  await enableRls(db, TABLE_ACTIONS);
  await enableRls(db, TABLE_ACTION_TRANSLATIONS);

  // Rename milestone to quest_completion_goal and set nullable
  await sql`
    ALTER TABLE achievement_actions
    RENAME COLUMN milestone TO quest_completion_goal;
  `.execute(db);
  await db.schema
    .alterTable(TABLE_ACHIEVEMENT_ACTIONS)
    .alterColumn('quest_completion_goal', (c) => c.dropNotNull())
    .alterColumn('status_rank', (c) => c.dropNotNull())
    .execute();

  // Remove foreign key constraints from serial_code_actions
  await db.schema.alterTable(TABLE_SERIAL_CODE_ACTIONS).dropConstraint('serial_code_actions_action_id_fkey').execute();

  await db.schema
    .alterTable(TABLE_SERIAL_CODE_ACTIONS)
    .dropConstraint('serial_code_actions_serial_code_project_id_fkey')
    .execute();

  await db.schema
    .alterTable(TABLE_QUESTIONNAIRE_ACTIONS)
    .dropConstraint('questionnaire_actions_questionnaire_id_fkey')
    .execute();

  // Drop serial_code_actions table
  await db.schema.dropTable(TABLE_SERIAL_CODE_ACTIONS).ifExists().execute();
  await enableRls(db, TABLE_ACTIONS);
  await enableRls(db, TABLE_ACTION_TRANSLATIONS);
  await enableRls(db, TABLE_ACHIEVEMENT_ACTIONS);
  await enableRls(db, TABLE_QUESTIONNAIRES);

  await sql`
    DROP POLICY IF EXISTS tenant_isolation_policy ON "serial_code_actions"
  `.execute(db);

  // 2. RLSの強制を解除
  await sql`
    ALTER TABLE "serial_code_actions" NO FORCE ROW LEVEL SECURITY
  `.execute(db);

  // 3. RLS自体を無効化
  await sql`
    ALTER TABLE "serial_code_actions" DISABLE ROW LEVEL SECURITY
  `.execute(db);
}
