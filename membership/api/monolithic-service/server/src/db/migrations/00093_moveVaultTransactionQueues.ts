
import { Kysely, sql } from 'kysely';
import { TABLE_VAULT_TRANSACTION_QUEUES, TABLE_TRANSACTION_QUEUES, TABLE_TRANSACTIONS, TABLE_ATTEMPT_TRANSACTIONS } from '../../constants/database';
import { Database } from '../database';
import { VaultTransactionStatus } from '../../enum/vaultTransactionStatus';
import { TransactionQueueStatus } from '../../enum/transactionQueueStatus';
import { TransactionStatus } from '../../enum/transactionStatus';
import { v4 as uuidv4 } from 'uuid';

export async function up(db: Kysely<Database>): Promise<void> {
  // 100 chunked.
  const limit = 100;
  let offset = 0;
  while (true) {
    const vaultTransactions = await db
      .selectFrom(TABLE_VAULT_TRANSACTION_QUEUES)
      .selectAll()
      .orderBy('nonce')
      .orderBy('transaction_id')
      .limit(limit)
      .offset(offset)
      .execute();
    if (vaultTransactions.length == 0) {
      break;
    }

    const queues = await db
      .selectFrom(TABLE_TRANSACTION_QUEUES)
      .selectAll()
      .where('queue_id', 'in', vaultTransactions.map(t => t.transaction_id))
      .execute();

    await Promise.all(vaultTransactions.map((async vaultQueue => {
      const queue = queues.find(q => q.queue_id === vaultQueue.transaction_id);
      if (queue) {
        return undefined;
      }
      await db.insertInto(TABLE_TRANSACTION_QUEUES)
        .values({
          queue_id: vaultQueue.transaction_id,
          service_id: vaultQueue.service_id,
          from_address: vaultQueue.from_address,
          to_address: vaultQueue.to_address ?? '',
          status: convertStatus(vaultQueue.status),
          tx_type: vaultQueue.tx_type,
          nft_type: vaultQueue.nft_type,
          nft_contract_address: '',
          token_id: vaultQueue.token_id ?? undefined,
          created_date: vaultQueue.created_date,
        })
        .execute();
      return createTransactionDatabases(
        vaultQueue.service_id,
        vaultQueue.hex_encoded_transaction_data,
        vaultQueue.nonce,
        vaultQueue.tx_hash,
        [vaultQueue.transaction_id],
        db
      );
    })));

    offset += limit;
  }
}

export async function down(db: Kysely<Database>): Promise<void> {
  // 100 chunked.
  const limit = 100;
  let offset = 0;
  while (true) {
    const vaultTransactions = await db
      .selectFrom(TABLE_VAULT_TRANSACTION_QUEUES)
      .selectAll()
      .orderBy('nonce')
      .orderBy('transaction_id')
      .limit(limit)
      .offset(offset)
      .execute();
    if (vaultTransactions.length == 0) {
      break;
    }

    const queues = await db
      .selectFrom(TABLE_TRANSACTION_QUEUES)
      .selectAll()
      .where('queue_id', 'in', vaultTransactions.map(t => t.transaction_id))
      .execute();
    const attempts = await db
      .selectFrom(TABLE_ATTEMPT_TRANSACTIONS)
      .selectAll()
      .where('queue_id', 'in', queues.map(q => q.queue_id))
      .execute();
    const transactions = await db
      .selectFrom(TABLE_TRANSACTIONS)
      .selectAll()
      .where('transaction_id', 'in', attempts.map(a => a.transaction_id))
      .execute();

    await db.deleteFrom(TABLE_ATTEMPT_TRANSACTIONS)
      .where('attempt_id', 'in', attempts.map(a => a.attempt_id));
    await db.deleteFrom(TABLE_TRANSACTIONS)
      .where('transaction_id', 'in', transactions.map(t => t.transaction_id));
    await db.deleteFrom(TABLE_TRANSACTION_QUEUES)
      .where('queue_id', 'in', queues.map(q => q.queue_id));
  }
}

const convertStatus = (status: VaultTransactionStatus): TransactionQueueStatus => {
  switch (status) {
    case VaultTransactionStatus.EXECUTED: return TransactionQueueStatus.PROCESSING;
    case VaultTransactionStatus.MINED: return TransactionQueueStatus.MINED;
    case VaultTransactionStatus.CONFIRMED: return TransactionQueueStatus.MINED;
    case VaultTransactionStatus.FAILURE: return TransactionQueueStatus.FAILED;
    case VaultTransactionStatus.CANCELED: return TransactionQueueStatus.ABORTED;
    case VaultTransactionStatus.ABORTED: return TransactionQueueStatus.ABORTED;
  }
  throw new Error(`Invalid status: ${status}`);
};

// transactionService
async function createTransactionDatabases(
  serviceId: string,
  encodedData: string,
  nonce: number,
  txHash: string,
  queueIds: string[],
  db: Kysely<Database>
): Promise<{
  transactionId: string,
  attemptTransactionIds: string[],
}> {
  const transactionId = uuidv4();
  const date = new Date();
  await db.insertInto(TABLE_TRANSACTIONS)
    .values({
      transaction_id: transactionId,
      service_id: serviceId,
      encoded_data: encodedData,
      nonce: nonce,
      tx_hash: txHash,
      status: TransactionStatus.EXECUTED,
      created_date: date,
    })
    .execute();

  const attemptTransactions = queueIds.map(queueId => {
    return {
      attempt_id: uuidv4(),
      queue_id: queueId,
      transaction_id: transactionId,
      created_date: date,
    }
  });
  await db.insertInto(TABLE_ATTEMPT_TRANSACTIONS)
    .values(attemptTransactions)
    .execute();

  return {
    transactionId: transactionId,
    attemptTransactionIds: attemptTransactions.map(tx => tx.attempt_id),
  };
}
