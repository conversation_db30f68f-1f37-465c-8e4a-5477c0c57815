import { Kysely, sql } from 'kysely';
import { TABLE_ACCOUNT_SERIAL_CODES, TABLE_SERIAL_CODES, TABLE_ACCOUNTS } from '../../constants/database';

export async function up(db: Kysely<any>): Promise<void> {
  await db.schema
    .createTable(TABLE_ACCOUNT_SERIAL_CODES)
    .addColumn('account_serial_code_id', 'varchar(128)', (col) => col.primaryKey())
    .addColumn('account_id', 'varchar(128)', (col) => col.references(`${TABLE_ACCOUNTS}.account_id`).notNull())
    .addColumn('serial_code_id', 'varchar(128)', (col) =>
      col.references(`${TABLE_SERIAL_CODES}.serial_code_id`).notNull(),
    )
    .addColumn('created_at', 'timestamp', (col) => col.defaultTo(sql`CURRENT_TIMESTAMP`).notNull())
    .execute();
}

export async function down(db: Kysely<unknown>): Promise<void> {
  await db.schema.dropTable(TABLE_ACCOUNT_SERIAL_CODES).execute();
}
