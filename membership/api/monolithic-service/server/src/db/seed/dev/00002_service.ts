import { ExpressionBuilder, Kysely } from 'kysely';
import { TABLE_TENANTS, TABLE_SERVICES, TABLE_VAULT_KEYS } from '../../../constants/database';
// import { Database } from '../../../db/database';
import { planId, tenantId, serviceId, vaultKeyId, komlockWalletAddress, membershipNftContractId, modularContractId } from './def'

export default async function seed(db: Kysely<any>) {
  await db
    .insertInto(TABLE_TENANTS)
    .values({
      tenant_id: tenantId,
      // tenant_name: 'Test Tenant LLC',
      plan_id: planId, // Foreign key relationship to `plans` table
    })
    .onConflict((oc) => oc.column('tenant_id')
      .doUpdateSet({
        // tenant_name: (eb:ExpressionBuilder<any, any>) => eb.ref('excluded.tenant_name'),
        plan_id: (eb:ExpressionBuilder<any, any>) => eb.ref('excluded.plan_id'),
      })
    )
    .execute();

  await db
    .insertInto(TABLE_VAULT_KEYS)
    .values({
      vault_key_id: vaultKeyId,
      tenant_id: tenantId,
      key_ring_project: 'komlock-developer',
      key_ring_location: 'asia-northeast1',
      key_ring_name: 'komlock-developer',
      key_version: '1',
      vault_wallet_address: komlockWalletAddress,
      nonce: 0
    })
    .onConflict((oc) => oc.column('vault_key_id')
      .doUpdateSet({
        tenant_id: (eb:ExpressionBuilder<any, any>) => eb.ref('excluded.tenant_id'),
        key_ring_project: (eb:ExpressionBuilder<any, any>) => eb.ref('excluded.key_ring_project'),
        key_ring_location: (eb:ExpressionBuilder<any, any>) => eb.ref('excluded.key_ring_location'),
        key_ring_name: (eb:ExpressionBuilder<any, any>) => eb.ref('excluded.key_ring_name'),
        key_version: (eb:ExpressionBuilder<any, any>) => eb.ref('excluded.key_version'),
        vault_wallet_address: (eb:ExpressionBuilder<any, any>) => eb.ref('excluded.vault_wallet_address'),
        nonce: (eb:ExpressionBuilder<any, any>) => eb.ref('excluded.nonce'),
      })
    )
    .execute();

  await db
    .insertInto(TABLE_SERVICES)
    .values([
      {
        service_id: serviceId,
        tenant_id: tenantId, // Foreign key relationship to `tenants` table
        // service_name: 'Dummy Service 1',
        service_url: 'https://example.com/service1',
        service_logo_image_url: 'https://example.com/images/service_logo1.png',
        theme_primary_color_lowest: '#FFFFFF',
        theme_primary_color_lower: '#CCCCCC',
        theme_primary_color_higher: '#999999',
        theme_primary_color_highest: '#666666',
        membership_nft_contract_id: membershipNftContractId,
        // service_policy: 'This is a policy for dummy service 1.',
        // service_pane: 'This is the service pane description for dummy service 1.',
        is_market_enabled: true,
        modular_contract_id: modularContractId
      }
    ])
    .onConflict((oc) => oc.column('service_id')
      .doUpdateSet({
        tenant_id: (eb:ExpressionBuilder<any, any>) => eb.ref('excluded.tenant_id'),
        // service_name: (eb:ExpressionBuilder<any, any>) => eb.ref('excluded.service_name'),
        service_url: (eb:ExpressionBuilder<any, any>) => eb.ref('excluded.service_url'),
        service_logo_image_url: (eb:ExpressionBuilder<any, any>) => eb.ref('excluded.service_logo_image_url'),
        theme_primary_color_lowest: (eb:ExpressionBuilder<any, any>) => eb.ref('excluded.theme_primary_color_lowest'),
        theme_primary_color_lower: (eb:ExpressionBuilder<any, any>) => eb.ref('excluded.theme_primary_color_lower'),
        theme_primary_color_higher: (eb:ExpressionBuilder<any, any>) => eb.ref('excluded.theme_primary_color_higher'),
        theme_primary_color_highest: (eb:ExpressionBuilder<any, any>) => eb.ref('excluded.theme_primary_color_highest'),
        membership_nft_contract_id: (eb:ExpressionBuilder<any, any>) => eb.ref('excluded.membership_nft_contract_id'),
        // service_policy: (eb:ExpressionBuilder<any, any>) => eb.ref('excluded.service_policy'),
        // service_pane: (eb:ExpressionBuilder<any, any>) => eb.ref('excluded.service_pane'),
        is_market_enabled: (eb:ExpressionBuilder<any, any>) => eb.ref('excluded.is_market_enabled'),
        modular_contract_id: (eb:ExpressionBuilder<any, any>) => eb.ref('excluded.modular_contract_id'),
      })
    )
    .execute();
}
