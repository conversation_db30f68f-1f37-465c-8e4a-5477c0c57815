import { readdir } from 'fs/promises';
import path from 'path';
import { db } from '../db/database';
import { logger } from '../utils/middleware/loggerMiddleware';

const ENV = process.env.NODE_ENV || 'dev';
const SEED_DIR = process.env.SEED_DIR || path.join(__dirname, 'seed', ENV);

async function seed() {
  console.log(`Running seeders for "${ENV}" from ${SEED_DIR}`);

  const files = await readdir(SEED_DIR);
  const sorted = files.filter((f) => f.endsWith('.ts') || f.endsWith('.js')).sort();

  for (const file of sorted) {
    const fullPath = path.join(SEED_DIR, file);
    logger.info(`Executing seeder: ${file}`);
    const seeder = await import(fullPath);
    if (typeof seeder.default === 'function') {
      await seeder.default(db);
    } else {
      console.warn(`No default export function found in ${file}`);
    }
  }

  await db.destroy();
  console.log(`Seeding for "${ENV}" complete`);
}

seed().catch((err) => {
  console.error('Seeder failed:', err);
  process.exit(1);
});
