import { Context } from 'hono';
import { ZodError } from 'zod';
import { ValidationError } from '../errors/validationError';
import { logger } from '../utils/middleware/loggerMiddleware';

export const validationResultHandler = (
  result: { success: boolean; error?: object } | ZodError<unknown>,
  c: Context,
) => {
  if (result instanceof ZodError) {
    const validationError = new ValidationError(`Wrong request has been sent`);

    c.json(
      {
        status: 400,
        code: validationError.code,
        message: validationError.message,
        details: result.issues,
      },
      400,
    );
    logger.error({ ValidationError: result.issues });

    return;
  }

  if (!result.success) {
    throw new ValidationError(`Wrong request has been sent: ${result.error}`);
  }
};
