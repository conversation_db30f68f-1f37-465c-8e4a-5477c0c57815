export class AccountResponse {
  accountId: string;
  displayName?: string;
  profileImage?: string;
  membership: {
    contractAddress: string;
    tokenId?: number;
  };
  tokenBoundAccountAddress?: string;
  constructor(
    accountId: string,
    displayName: string | undefined,
    profileImage: string | undefined,
    contractAddress: string,
    tokenId: number | undefined,
    tokenBoundAccountAddress: string | undefined,
  ) {
    this.accountId = accountId;
    this.displayName = displayName;
    this.profileImage = profileImage;
    this.membership = {
      contractAddress: contractAddress,
      tokenId: tokenId,
    };
    this.tokenBoundAccountAddress = tokenBoundAccountAddress;
  }
}



export interface NotificationInfo {
  notificationId: string;
  title: string;
  text: string;
  broadcastDate: string;
}
