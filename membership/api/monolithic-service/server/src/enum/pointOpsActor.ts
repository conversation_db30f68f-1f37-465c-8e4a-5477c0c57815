import { z } from '@hono/zod-openapi';

export const pointOpsActor = {
  USER: 'USER',
  ADMIN: 'ADMIN',
  SCHEDULER: 'SCHEDULER',
} as const;

export type PointOpsActor = (typeof pointOpsActor)[keyof typeof pointOpsActor];

export const pointOpsActorSchema = z
  .enum(Object.values(pointOpsActor) as [PointOpsActor, ...PointOpsActor[]])
  .openapi({ example: 'USER', description: 'Actor of point operation (USER, ADMIN, SCHEDULER)' });
