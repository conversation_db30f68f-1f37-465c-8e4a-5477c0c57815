import { z } from '@hono/zod-openapi';

export const pointTxDetail = {
  REWARD: 'REWARD',
  GACHA: 'GACHA',
  ADJUST: 'ADJUST',
  EXPIRE: 'EXPIRE',
} as const;

export type PointTxDetail = (typeof pointTxDetail)[keyof typeof pointTxDetail];

export const pointTxDetailSchema = z
  .enum(Object.values(pointTxDetail) as [PointTxDetail, ...PointTxDetail[]])
  .openapi({ example: 'REWARD', description: 'Type of point transaction (REWARD, GACHA, ADJUST, EXPIRE)' });
