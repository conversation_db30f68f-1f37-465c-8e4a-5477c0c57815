import { z } from '@hono/zod-openapi';

export enum WebhookType {
  ADDRESS_ACTIVITY = 'ADDRESS_ACTIVITY',
  GRAPHQL = 'GRAPHQL',
  NFT_ACTIVITY = 'NFT_ACTIVITY',
}

export const WebhookTypeSchema = z.nativeEnum(WebhookType);

export enum TxCategory {
  EXTERNAL = 'external',
  INTERNAL = 'internal',
  TOKEN = 'token',
  ERC721 = 'erc721',
  ERC1155 = 'erc1155',
}

export const TxCategorySchema = z.nativeEnum(TxCategory);

export enum NftStandard {
  ERC721 = 'erc721',
  ERC1155 = 'erc1155',
}

export const NftStandardSchema = z.nativeEnum(NftStandard);
