export enum ApiCategoryTag {
  TENANT = 'tenant',
  VAULT = 'vault',
  SERVICE = 'service',
  USER = 'user',
  ACCOUNT = 'account',
  AUTH = 'authentication',
  QUEST = 'quest',
  PRODUCT = 'product',
  NFT = 'nft',
  POINT = 'point',
  LOCATION = 'location',
}

export const ApiCategoryTagDescriptions: { name: ApiCategoryTag; description: string }[] = [
  { name: ApiCategoryTag.TENANT, description: 'テナント情報の管理' },
  { name: ApiCategoryTag.SERVICE, description: 'テナントに紐づくサービスの管理' },
  { name: ApiCategoryTag.VAULT, description: 'テナントが保有する秘密鍵の管理' },
  { name: ApiCategoryTag.QUEST, description: 'サービスレベルのクエスト情報' },
  { name: ApiCategoryTag.PRODUCT, description: 'サービスレベルのマーケット情報' },
  { name: ApiCategoryTag.USER, description: 'サービス横断でのユーザー情報' },
  { name: ApiCategoryTag.ACCOUNT, description: 'サービスレベルのユーザー情報' },
  { name: ApiCategoryTag.AUTH, description: '認証・認可' },
  { name: ApiCategoryTag.NFT, description: 'NFT関連情報' },
  { name: ApiCategoryTag.POINT, description: 'サービスレベルのポイント情報' },
  { name: ApiCategoryTag.LOCATION, description: '位置情報関連情報' },
];

export enum ApiAccessLevelTag {
  SYSTEM = 'system',
  ADMIN = 'admin',
  IFRAME = 'iframe',
  USER = 'user',
  ACCOUNT = 'account',
  GUEST = 'guest',
}
