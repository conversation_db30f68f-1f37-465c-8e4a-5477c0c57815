import { InternalServerError } from "../errors/internalServerError";
import { RewardType } from "./rewardType";
import { z } from '@hono/zod-openapi';

export enum NftType {
  COUPON = 'COUPON',
  CONTENT = 'CONTENT',
  CERTIFICATE = 'CERTIFICATE',
  MEMBERSHIP = 'MEMBERSHIP',
  TICKET = 'TICKET',
  MODULAR_CORE = 'MODULAR_CORE',
  MODULE_BULKMINT = 'MODULE_BULKMINT',
  MODULE_BULKCREATEACCOUNT = 'MODULE_BULKCREATEACCOUNT',
}

export const NftTypeSchema = z.nativeEnum(NftType).openapi({
  example: NftType.COUPON,
  description: 'Type of NFT such as COUPON, CONTENT, CERTIFICATE, MEMBERSHIP, TICKET, etc.',
});


export function reward2Nft(value: RewardType): NftType {
  if (value in NftType) {
    return value as unknown as NftType;
  }
  throw new InternalServerError('reward type cast failed');
}
