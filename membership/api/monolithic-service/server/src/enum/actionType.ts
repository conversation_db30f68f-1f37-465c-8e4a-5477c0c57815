import { z } from '@hono/zod-openapi';

export enum ActionType {
  ACHIEVEMENT = 'ACHIEVEMENT',
  QR_CHECKIN = 'QR-CHECKIN',
  ONLINE_CHECKIN = 'ONLINE-CHECKIN',
  QUESTIONNAIRE = 'QUESTIONNAIRE',
  SERIAL_CODE = 'SERIAL-CODE',
  LOCATION_CHECKIN = 'LOCATION-CHECKIN',
}
export const ActionTypeSchema = z.nativeEnum(ActionType).openapi({
  description: 'Type to classify action details.',
  example: ActionType.ONLINE_CHECKIN,
});

export enum ActionActivitiesStatus {
  PROCEEDING = 'PROCEEDING',
  DONE = 'DONE',
  EXPIRED = 'EXPIRED',
}
export const ActionActivitiesStatusSchema = z.nativeEnum(ActionActivitiesStatus);

export enum QuestActivitiesStatus {
  PROCEEDING = 'PROCEEDING',
  DONE = 'DONE',
  EXPIRED = 'EXPIRED',
}
export const QuestActivitiesStatusSchema = z.nativeEnum(QuestActivitiesStatus);

export enum RewardActionType {
  OBTAINED = 'OBTAINED',
  USED = 'USED',
  LOCATION_CHECKIN = 'LOCATION_CHECKIN',
}
export const RewardActionTypeSchema = z.nativeEnum(RewardActionType).openapi({
  description: 'What kind of activity is Reward',
  example: RewardActionType.OBTAINED,
});
