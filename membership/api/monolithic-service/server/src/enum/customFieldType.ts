import { z } from '@hono/zod-openapi';

export const CustomFieldType = {
  TEXT: 'TEXT',
  NUMERIC: 'NUMERIC',
  EMAIL: 'EMAIL',
  PHONE: 'PHONE',
  SELECTION: 'SELECTION',
  MULT<PERSON>LE_SELECTION: 'MULTIPLE-SELECTION',
} as const;

export type CustomFieldType = (typeof CustomFieldType)[keyof typeof CustomFieldType];

export const CustomFieldTypeSchema = z.enum(
  Object.values(CustomFieldType) as [CustomFieldType, ...CustomFieldType[]]
).openapi({
  description: 'Type of custom field',
  example: CustomFieldType.TEXT,
});