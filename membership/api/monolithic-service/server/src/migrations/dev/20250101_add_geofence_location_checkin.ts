import { Kysely, sql } from 'kysely';

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // Create geofences table to store location boundaries
  await db.schema
    .createTable('geofences')
    .addColumn('geofence_id', 'uuid', (col) => col.primaryKey().defaultTo(sql`gen_random_uuid()`))
    .addColumn('service_id', 'varchar(255)', (col) => col.notNull())
    .addColumn('name', 'varchar(255)', (col) => col.notNull())
    .addColumn('description', 'text')
    .addColumn('latitude', 'decimal(10,8)', (col) => col.notNull())
    .addColumn('longitude', 'decimal(11,8)', (col) => col.notNull())
    .addColumn('radius_meters', 'integer', (col) => col.notNull())
    .addColumn('is_active', 'boolean', (col) => col.defaultTo(true))
    .addColumn('created_at', 'timestamptz', (col) => col.defaultTo(sql`CURRENT_TIMESTAMP`))
    .addColumn('updated_at', 'timestamptz', (col) => col.defaultTo(sql`CURRENT_TIMESTAMP`))
    .execute();

  // Add constraints
  await db.schema
    .alterTable('geofences')
    .addCheckConstraint('geofences_latitude_check', sql`latitude >= -90 AND latitude <= 90`)
    .execute();

  await db.schema
    .alterTable('geofences')
    .addCheckConstraint('geofences_longitude_check', sql`longitude >= -180 AND longitude <= 180`)
    .execute();

  await db.schema
    .alterTable('geofences')
    .addCheckConstraint('geofences_radius_check', sql`radius_meters > 0`)
    .execute();

  // Add foreign key constraint to services table
  await db.schema
    .alterTable('geofences')
    .addForeignKeyConstraint('geofences_service_id_fk', ['service_id'], 'services', ['service_id'])
    .onDelete('cascade')
    .execute();

  // Create indexes for efficient queries
  await db.schema
    .createIndex('idx_geofences_service_id')
    .on('geofences')
    .column('service_id')
    .execute();

  await db.schema
    .createIndex('idx_geofences_location')
    .on('geofences')
    .columns(['latitude', 'longitude'])
    .execute();

  await db.schema
    .createIndex('idx_geofences_active')
    .on('geofences')
    .column('is_active')
    .execute();

  // Add location check-in action type to action_types table
  await sql`
    INSERT INTO action_types (action_type, description) 
    VALUES ('LOCATION_CHECKIN', 'Location check-in action requiring user to be within specified geofence')
    ON CONFLICT (action_type) DO NOTHING
  `.execute(db);

  // Create location_checkin_actions table for location-specific action data
  await db.schema
    .createTable('location_checkin_actions')
    .addColumn('location_action_id', 'uuid', (col) => col.primaryKey().defaultTo(sql`gen_random_uuid()`))
    .addColumn('action_id', 'uuid', (col) => col.notNull())
    .addColumn('geofence_id', 'uuid', (col) => col.notNull())
    .addColumn('required_duration_seconds', 'integer', (col) => col.defaultTo(0))
    .addColumn('created_at', 'timestamptz', (col) => col.defaultTo(sql`CURRENT_TIMESTAMP`))
    .addColumn('updated_at', 'timestamptz', (col) => col.defaultTo(sql`CURRENT_TIMESTAMP`))
    .execute();

  // Add constraints for location_checkin_actions
  await db.schema
    .alterTable('location_checkin_actions')
    .addCheckConstraint('location_checkin_actions_duration_check', sql`required_duration_seconds >= 0`)
    .execute();

  await db.schema
    .alterTable('location_checkin_actions')
    .addForeignKeyConstraint('location_checkin_actions_action_id_fk', ['action_id'], 'actions', ['action_id'])
    .onDelete('cascade')
    .execute();

  await db.schema
    .alterTable('location_checkin_actions')
    .addForeignKeyConstraint('location_checkin_actions_geofence_id_fk', ['geofence_id'], 'geofences', ['geofence_id'])
    .onDelete('cascade')
    .execute();

  // Create indexes for location check-in actions
  await db.schema
    .createIndex('idx_location_checkin_actions_action_id')
    .on('location_checkin_actions')
    .column('action_id')
    .execute();

  await db.schema
    .createIndex('idx_location_checkin_actions_geofence_id')
    .on('location_checkin_actions')
    .column('geofence_id')
    .execute();

  // Create location_checkin_attempts table to track user location submissions
  await db.schema
    .createTable('location_checkin_attempts')
    .addColumn('attempt_id', 'uuid', (col) => col.primaryKey().defaultTo(sql`gen_random_uuid()`))
    .addColumn('account_id', 'varchar(255)', (col) => col.notNull())
    .addColumn('action_id', 'uuid', (col) => col.notNull())
    .addColumn('latitude', 'decimal(10,8)', (col) => col.notNull())
    .addColumn('longitude', 'decimal(11,8)', (col) => col.notNull())
    .addColumn('accuracy_meters', 'decimal(8,2)')
    .addColumn('is_successful', 'boolean', (col) => col.notNull())
    .addColumn('distance_from_target', 'decimal(10,2)', (col) => col.notNull())
    .addColumn('attempted_at', 'timestamptz', (col) => col.defaultTo(sql`CURRENT_TIMESTAMP`))
    .execute();

  // Add constraints for location_checkin_attempts
  await db.schema
    .alterTable('location_checkin_attempts')
    .addCheckConstraint('location_checkin_attempts_latitude_check', sql`latitude >= -90 AND latitude <= 90`)
    .execute();

  await db.schema
    .alterTable('location_checkin_attempts')
    .addCheckConstraint('location_checkin_attempts_longitude_check', sql`longitude >= -180 AND longitude <= 180`)
    .execute();

  await db.schema
    .alterTable('location_checkin_attempts')
    .addCheckConstraint('location_checkin_attempts_accuracy_check', sql`accuracy_meters >= 0`)
    .execute();

  await db.schema
    .alterTable('location_checkin_attempts')
    .addForeignKeyConstraint('location_checkin_attempts_account_id_fk', ['account_id'], 'accounts', ['account_id'])
    .onDelete('cascade')
    .execute();

  await db.schema
    .alterTable('location_checkin_attempts')
    .addForeignKeyConstraint('location_checkin_attempts_action_id_fk', ['action_id'], 'actions', ['action_id'])
    .onDelete('cascade')
    .execute();

  // Create indexes for location check-in attempts
  await db.schema
    .createIndex('idx_location_checkin_attempts_account_id')
    .on('location_checkin_attempts')
    .column('account_id')
    .execute();

  await db.schema
    .createIndex('idx_location_checkin_attempts_action_id')
    .on('location_checkin_attempts')
    .column('action_id')
    .execute();

  await db.schema
    .createIndex('idx_location_checkin_attempts_attempted_at')
    .on('location_checkin_attempts')
    .column('attempted_at')
    .execute();

  // Add function to calculate distance between two points (Haversine formula)
  await sql`
    CREATE OR REPLACE FUNCTION calculate_distance_meters(
      lat1 DECIMAL(10, 8),
      lon1 DECIMAL(11, 8),
      lat2 DECIMAL(10, 8),
      lon2 DECIMAL(11, 8)
    ) RETURNS DECIMAL(10, 2) AS $$
    DECLARE
      earth_radius CONSTANT DECIMAL := 6371000; -- Earth radius in meters
      dlat DECIMAL;
      dlon DECIMAL;
      a DECIMAL;
      c DECIMAL;
    BEGIN
      dlat := RADIANS(lat2 - lat1);
      dlon := RADIANS(lon2 - lon1);
      
      a := SIN(dlat/2) * SIN(dlat/2) + 
           COS(RADIANS(lat1)) * COS(RADIANS(lat2)) * 
           SIN(dlon/2) * SIN(dlon/2);
      
      c := 2 * ATAN2(SQRT(a), SQRT(1-a));
      
      RETURN earth_radius * c;
    END;
    $$ LANGUAGE plpgsql IMMUTABLE;
  `.execute(db);

  // Add trigger function to update updated_at timestamp
  await sql`
    CREATE OR REPLACE FUNCTION update_updated_at_column()
    RETURNS TRIGGER AS $$
    BEGIN
      NEW.updated_at = CURRENT_TIMESTAMP;
      RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;
  `.execute(db);

  // Create triggers for updated_at columns
  await sql`
    CREATE TRIGGER update_geofences_updated_at 
      BEFORE UPDATE ON geofences 
      FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
  `.execute(db);

  await sql`
    CREATE TRIGGER update_location_checkin_actions_updated_at 
      BEFORE UPDATE ON location_checkin_actions 
      FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
  `.execute(db);
}

export async function down(db: Kysely<any>): Promise<void> {
  // Drop triggers
  await sql`DROP TRIGGER IF EXISTS update_location_checkin_actions_updated_at ON location_checkin_actions`.execute(db);
  await sql`DROP TRIGGER IF EXISTS update_geofences_updated_at ON geofences`.execute(db);
  
  // Drop functions
  await sql`DROP FUNCTION IF EXISTS update_updated_at_column()`.execute(db);
  await sql`DROP FUNCTION IF EXISTS calculate_distance_meters(DECIMAL, DECIMAL, DECIMAL, DECIMAL)`.execute(db);
  
  // Drop tables in reverse order
  await db.schema.dropTable('location_checkin_attempts').execute();
  await db.schema.dropTable('location_checkin_actions').execute();
  await db.schema.dropTable('geofences').execute();
  
  // Remove action type
  await sql`DELETE FROM action_types WHERE action_type = 'LOCATION_CHECKIN'`.execute(db);
}
