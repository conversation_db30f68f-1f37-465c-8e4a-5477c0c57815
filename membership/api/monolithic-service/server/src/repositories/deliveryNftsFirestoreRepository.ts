import { injectable, inject } from 'tsyringe';
import { FirebaseComponent } from '../components/firebaseComponent';
import { logger } from '../utils/logger';
import { NftType } from '../enum/nftType';
import type FirebaseFirestore from '@google-cloud/firestore';

export interface DeliveryNftsDocument {
  nft_type: string;
  contract_address: string;
  to_address: string;
  token_id: string;
  service_id: string;
  account_id: string;
  queue_id: string;
  transaction_hash: string | null;
  delivery_image_url: string;
  created_at: string;
  updated_at: string;
}

@injectable()
export class DeliveryNftsFirestoreRepository {
  constructor(@inject('FirebaseComponent') private firebaseComponent: FirebaseComponent) {}

  public async insertDeliveryNft(
    nftType: NftType,
    contractAddress: string,
    tokenId: string,
    toAddress: string,
    serviceId: string,
    accountId: string,
    queueId: string,
    deliveryImageUrl: string,
    txHash: string | null = null,
  ): Promise<string> {
    const nftDocument: DeliveryNftsDocument = {
      nft_type: nftType,
      contract_address: contractAddress,
      to_address: toAddress,
      token_id: tokenId,
      service_id: serviceId,
      account_id: accountId,
      queue_id: queueId,
      delivery_image_url: deliveryImageUrl,
      transaction_hash: txHash,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    const docRef = await this.firebaseComponent.createDocument('delivery_nfts', nftDocument);
    return docRef.id;
  }

  public async selectDeliveryNftWithQueueId(queueId: string): Promise<string | undefined> {
    const query = this.firebaseComponent.getFirestoreInstance().collection('delivery_nfts');
    const snapshot = await query.where('queue_id', '==', queueId).get();
    if (snapshot.empty) {
      return;
    }
    return snapshot.docs[0].id;
  }

  public async selectDeliveryNft(transactionHash: string): Promise<string | undefined> {
    const query = this.firebaseComponent.getFirestoreInstance().collection('delivery_nfts');
    const snapshot = await query.where('transaction_hash', '==', transactionHash).get();
    if (snapshot.empty) {
      return;
    }
    return snapshot.docs[0].id;
  }

  public async updateDeliveryNftTxHash(queueId: string, txHash: string): Promise<FirebaseFirestore.WriteResult> {
    const query = this.firebaseComponent.getFirestoreInstance().collection('delivery_nfts');
    const snapshot = await query.where('queue_id', '==', queueId).get();
    const docId = snapshot.docs[0].id;
    return this.firebaseComponent.updateDocument('delivery_nfts', docId, {
      transaction_hash: txHash,
      updated_at: new Date().toISOString(),
    });
  }

  public async updateDeliveryNftTxHashForAccountId(accountId: string, txHash: string): Promise<void> {
    const query = this.firebaseComponent.getFirestoreInstance().collection('delivery_nfts');
    const snapshot = await query.where('account_id', '==', accountId).get();
    snapshot.forEach(async (doc) => {
      query.doc(doc.id).update({
        transaction_hash: txHash,
        updated_at: new Date().toISOString(),
      });
    });
  }

  public async deleteDeliveryNftById(docId: string): Promise<FirebaseFirestore.WriteResult | void> {
    return this.firebaseComponent.deleteDocument('delivery_nfts', docId);
  }

  public async deleteDeliveryNft(
    transactionHash: string,
    contractAddress: string,
    accountId: string,
    serviceId: string,
  ): Promise<FirebaseFirestore.WriteResult | void> {
    let snapshot = await this.firebaseComponent.queryCollection('delivery_nfts', [
      { field: 'transaction_hash', operator: '==', value: transactionHash },
      { field: 'contract_address', operator: '==', value: contractAddress },
      { field: 'account_id', operator: '==', value: accountId },
      { field: 'service_id', operator: '==', value: serviceId },
    ]);

    if (snapshot.empty) {
      snapshot = await this.firebaseComponent.queryCollection('delivery_nfts', [
        { field: 'transaction_hash', operator: '==', value: null },
        { field: 'contract_address', operator: '==', value: contractAddress },
        { field: 'account_id', operator: '==', value: accountId },
        { field: 'service_id', operator: '==', value: serviceId },
      ]);
    }

    if (snapshot.empty) {
      logger.warn('No matching document found to delete');
      return;
    }

    return this.firebaseComponent.deleteDocument('delivery_nfts', snapshot.docs[0].id);
  }
}
