import { injectable } from 'tsyringe';
import { db } from '../db/database';
import { v4 as uuidv4 } from 'uuid';
import {
  TABLE_SERVICE_CUSTOM_FIELDS,
  TABLE_SERVICE_CUSTOM_FIELD_TRANSLATIONS,
  TABLE_SERVICE_CUSTOM_FIELD_VALIDATORS,
  TABLE_SERVICE_CUSTOM_FIELD_VALIDATOR_TRANSLATIONS,
  TABLE_SERVICE_CUSTOM_FIELD_OPTIONS,
  TABLE_SERVICE_CUSTOM_FIELD_OPTION_TRANSLATIONS,
} from '../constants/database';
import { ServiceCustomFields } from '../dtos/services/schemas';
import { LanguageCode } from '../enum/languageCode';
import { sql } from 'kysely';
import { ServiceCustomFieldEntity } from '../tables/serviceCustomFieldsTable';
import { ServiceCustomFieldValidatorEntity } from '../tables/serviceCustomFieldValidatorsTable';
import { ServiceCustomFieldOptionEntity } from '../tables/serviceCustomFieldOptionsTable';
import { MultitenantRepository } from './multitenantRepository';

export interface ServiceCustomFieldValidatorResult extends ServiceCustomFieldValidatorEntity {
  custom_field_validator_translation_id?: string;
  locale?: LanguageCode;
  error_message?: string;
  description?: string | null;
}

export interface ServiceCustomFieldOptionResult extends ServiceCustomFieldOptionEntity {
  custom_field_option_translation_id?: string;
  locale: LanguageCode;
  label: string;
}

export interface ServiceCustomFieldResult extends ServiceCustomFieldEntity {
  label: string;
  locale: LanguageCode;
  validator: ServiceCustomFieldValidatorResult | null;
  options: ServiceCustomFieldOptionResult[] | null;
}

export interface ServiceCustomFieldsResult {
  fields: ServiceCustomFieldResult[];
}

@injectable()
export class ServicesCustomFieldsRepository extends MultitenantRepository {
  async getCurrentVersion(serviceId: string): Promise<number> {
    await this.setServiceId(serviceId);
    const result = await db
      .selectFrom(TABLE_SERVICE_CUSTOM_FIELDS)
      .select(db.fn.max('version').as('max_version'))
      .where('service_id', '=', serviceId)
      .executeTakeFirst();

    return result?.max_version ? Number(result.max_version) : 0;
  }

  async createOrUpdate(
    serviceId: string,
    definition: ServiceCustomFields,
    version: number,
  ): Promise<{ id: string; version: number }> {
    await this.setServiceId(serviceId);
    const definitionId = uuidv4();

    return await db.transaction().execute(async (trx) => {
      for (const field of definition.fields) {
        const customFieldId = uuidv4();

        await trx
          .insertInto(TABLE_SERVICE_CUSTOM_FIELDS)
          .values({
            custom_field_id: customFieldId,
            service_id: serviceId,
            field_key: field.fieldKey,
            version: version,
            type: field.type,
            default_value: field.defaultValue ?? undefined,
            max_length: field.maxLength ?? undefined,
            min_length: field.minLength ?? undefined,
            unique: field.unique,
            verify: field.verify,
            optional: field.optional || false,
            sort_order: field.sortOrder || 0,
            created_at: new Date(),
          })
          .execute();

        if (field.translations && field.translations.length > 0) {
          for (const translation of field.translations) {
            await trx
              .insertInto(TABLE_SERVICE_CUSTOM_FIELD_TRANSLATIONS)
              .values({
                custom_field_translation_id: uuidv4(),
                service_id: serviceId,
                custom_field_id: customFieldId,
                locale: translation.locale,
                label: translation.label,
              })
              .execute();
          }
        }

        if (field.validator) {
          const validatorId = uuidv4();
          await trx
            .insertInto(TABLE_SERVICE_CUSTOM_FIELD_VALIDATORS)
            .values({
              validator_id: validatorId,
              service_id: serviceId,
              custom_field_id: customFieldId,
              pattern: field.validator.pattern || '',
            })
            .execute();

          if (field.validator.translations && field.validator.translations.length > 0) {
            for (const translation of field.validator.translations) {
              await trx
                .insertInto(TABLE_SERVICE_CUSTOM_FIELD_VALIDATOR_TRANSLATIONS)
                .values({
                  custom_field_validator_translation_id: uuidv4(),
                  service_id: serviceId,
                  validator_id: validatorId,
                  locale: translation.locale,
                  error_message: translation.errorMessage,
                  description: translation.description ?? undefined,
                })
                .execute();
            }
          }
        }

        if (field.options && field.options.length > 0) {
          for (const option of field.options) {
            const optionId = uuidv4();
            await trx
              .insertInto(TABLE_SERVICE_CUSTOM_FIELD_OPTIONS)
              .values({
                custom_field_option_id: optionId,
                service_id: serviceId,
                custom_field_id: customFieldId,
                value: option.value,
                sort_order: option.sortOrder || 0,
              })
              .execute();

            if (option.translations && option.translations.length > 0) {
              for (const translation of option.translations) {
                await trx
                  .insertInto(TABLE_SERVICE_CUSTOM_FIELD_OPTION_TRANSLATIONS)
                  .values({
                    custom_field_option_translation_id: uuidv4(),
                    service_id: serviceId,
                    custom_field_option_id: optionId,
                    locale: translation.locale as LanguageCode,
                    label: translation.label,
                  })
                  .execute();
              }
            }
          }
        }
      }

      return {
        id: definitionId,
        version: version,
      };
    });
  }

  async getLatestCustomFieldsVersion(serviceId: string, locale: LanguageCode): Promise<ServiceCustomFieldsResult> {
    await this.setServiceId(serviceId);
    const latestVersion = await this.getCurrentVersion(serviceId);

    if (latestVersion === 0) {
      return { fields: [] };
    }

    const rows = await db
      .selectFrom(TABLE_SERVICE_CUSTOM_FIELDS)
      .innerJoin(TABLE_SERVICE_CUSTOM_FIELD_TRANSLATIONS, (jb) =>
        jb
          .onRef(
            `${TABLE_SERVICE_CUSTOM_FIELD_TRANSLATIONS}.custom_field_id`,
            '=',
            `${TABLE_SERVICE_CUSTOM_FIELDS}.custom_field_id`,
          )
          .on(`${TABLE_SERVICE_CUSTOM_FIELD_TRANSLATIONS}.locale`, '=', sql`${locale}`),
      )
      .leftJoin(
        TABLE_SERVICE_CUSTOM_FIELD_VALIDATORS,
        `${TABLE_SERVICE_CUSTOM_FIELD_VALIDATORS}.custom_field_id`,
        `${TABLE_SERVICE_CUSTOM_FIELDS}.custom_field_id`,
      )
      .leftJoin(TABLE_SERVICE_CUSTOM_FIELD_VALIDATOR_TRANSLATIONS, (jb) =>
        jb
          .onRef(
            `${TABLE_SERVICE_CUSTOM_FIELD_VALIDATOR_TRANSLATIONS}.validator_id`,
            '=',
            `${TABLE_SERVICE_CUSTOM_FIELD_VALIDATORS}.validator_id`,
          )
          .on(`${TABLE_SERVICE_CUSTOM_FIELD_VALIDATOR_TRANSLATIONS}.locale`, '=', sql`${locale}`),
      )
      .leftJoin(
        TABLE_SERVICE_CUSTOM_FIELD_OPTIONS,
        `${TABLE_SERVICE_CUSTOM_FIELD_OPTIONS}.custom_field_id`,
        `${TABLE_SERVICE_CUSTOM_FIELDS}.custom_field_id`,
      )
      .leftJoin(TABLE_SERVICE_CUSTOM_FIELD_OPTION_TRANSLATIONS, (jb) =>
        jb
          .onRef(
            `${TABLE_SERVICE_CUSTOM_FIELD_OPTION_TRANSLATIONS}.custom_field_option_id`,
            '=',
            `${TABLE_SERVICE_CUSTOM_FIELD_OPTIONS}.custom_field_option_id`,
          )
          .on(`${TABLE_SERVICE_CUSTOM_FIELD_OPTION_TRANSLATIONS}.locale`, '=', sql`${locale}`),
      )
      .select([
        `${TABLE_SERVICE_CUSTOM_FIELDS}.custom_field_id`,
        `${TABLE_SERVICE_CUSTOM_FIELDS}.service_id`,
        `${TABLE_SERVICE_CUSTOM_FIELDS}.field_key`,
        `${TABLE_SERVICE_CUSTOM_FIELDS}.version`,
        `${TABLE_SERVICE_CUSTOM_FIELDS}.type`,
        `${TABLE_SERVICE_CUSTOM_FIELDS}.default_value`,
        `${TABLE_SERVICE_CUSTOM_FIELDS}.max_length`,
        `${TABLE_SERVICE_CUSTOM_FIELDS}.min_length`,
        `${TABLE_SERVICE_CUSTOM_FIELDS}.unique`,
        `${TABLE_SERVICE_CUSTOM_FIELDS}.verify`,
        `${TABLE_SERVICE_CUSTOM_FIELDS}.optional`,
        `${TABLE_SERVICE_CUSTOM_FIELDS}.sort_order`,
        `${TABLE_SERVICE_CUSTOM_FIELDS}.created_at`,
        `${TABLE_SERVICE_CUSTOM_FIELD_TRANSLATIONS}.label`,
        `${TABLE_SERVICE_CUSTOM_FIELD_TRANSLATIONS}.locale`,
      ])
      .select(
        sql
          .raw(
            `
            CASE
              WHEN ${TABLE_SERVICE_CUSTOM_FIELD_VALIDATORS}.validator_id IS NOT NULL
              THEN JSON_BUILD_OBJECT(
                'validator_id',   ${TABLE_SERVICE_CUSTOM_FIELD_VALIDATORS}.validator_id,
                'service_id',     ${TABLE_SERVICE_CUSTOM_FIELD_VALIDATORS}.service_id,
                'custom_field_id',${TABLE_SERVICE_CUSTOM_FIELD_VALIDATORS}.custom_field_id,
                'pattern',        ${TABLE_SERVICE_CUSTOM_FIELD_VALIDATORS}.pattern,
                'custom_field_validator_translation_id',
                                  ${TABLE_SERVICE_CUSTOM_FIELD_VALIDATOR_TRANSLATIONS}.custom_field_validator_translation_id,
                'locale',         ${TABLE_SERVICE_CUSTOM_FIELD_VALIDATOR_TRANSLATIONS}.locale,
                'error_message',  ${TABLE_SERVICE_CUSTOM_FIELD_VALIDATOR_TRANSLATIONS}.error_message,
                'description',    ${TABLE_SERVICE_CUSTOM_FIELD_VALIDATOR_TRANSLATIONS}.description
              )
            END
          `,
          )
          .as('validator'),
      )
      .select(
        sql
          .raw(
            `
            JSON_AGG(
              DISTINCT jsonb_build_object(
                'custom_field_option_id',   ${TABLE_SERVICE_CUSTOM_FIELD_OPTIONS}.custom_field_option_id,
                'service_id',  ${TABLE_SERVICE_CUSTOM_FIELD_OPTIONS}.service_id,
                'custom_field_id',    ${TABLE_SERVICE_CUSTOM_FIELD_OPTIONS}.custom_field_id,
                'value',       ${TABLE_SERVICE_CUSTOM_FIELD_OPTIONS}.value,
                'sort_order',  ${TABLE_SERVICE_CUSTOM_FIELD_OPTIONS}.sort_order,
                'custom_field_option_translation_id', ${TABLE_SERVICE_CUSTOM_FIELD_OPTION_TRANSLATIONS}.custom_field_option_translation_id,
                'locale',      ${TABLE_SERVICE_CUSTOM_FIELD_OPTION_TRANSLATIONS}.locale,
                'label',       ${TABLE_SERVICE_CUSTOM_FIELD_OPTION_TRANSLATIONS}.label
              )
            ) FILTER (
              WHERE ${TABLE_SERVICE_CUSTOM_FIELD_OPTIONS}.custom_field_option_id IS NOT NULL
            )
          `,
          )
          .as('options'),
      )

      .where(`${TABLE_SERVICE_CUSTOM_FIELDS}.service_id`, '=', serviceId)
      .where(`${TABLE_SERVICE_CUSTOM_FIELDS}.version`, '=', latestVersion)
      .groupBy(`${TABLE_SERVICE_CUSTOM_FIELDS}.custom_field_id`)
      .groupBy(`${TABLE_SERVICE_CUSTOM_FIELD_TRANSLATIONS}.label`)
      .groupBy(`${TABLE_SERVICE_CUSTOM_FIELD_TRANSLATIONS}.locale`)
      .groupBy(`${TABLE_SERVICE_CUSTOM_FIELD_VALIDATORS}.validator_id`)
      .groupBy(`${TABLE_SERVICE_CUSTOM_FIELD_VALIDATOR_TRANSLATIONS}.custom_field_validator_translation_id`)
      .execute();

    return { fields: rows as ServiceCustomFieldResult[] };
  }
}
