import { Database, db } from '../db/database';
import { injectable } from 'tsyringe';
import { MultitenantRepository } from './multitenantRepository';
import { TABLE_STATUS_POINT_TXS } from '../constants/database';
import { sql, Transaction } from 'kysely';
import { StatusPointTxEntity } from '../tables/statusPointTxs';
import { InsertableStatusPointTxRow } from '../tables/statusPointTxs';

@injectable()
export class StatusPointTxsRepository extends MultitenantRepository {
  async getTotalStatusPointsByAccountId(serviceId: string, accountId: string, tx?: Transaction<Database>): Promise<number> {
    await this.setServiceId(serviceId, tx);
    const { total } = await (tx ?? db)
      .selectFrom(TABLE_STATUS_POINT_TXS)
      .where('service_id', '=', serviceId)
      .where('account_id', '=', accountId)
      .select(sql`COALESCE(SUM(amount), 0)`.as('total'))
      .executeTakeFirstOrThrow();
    return Number(total);
  }

  async insertStatusPointTx(
    serviceId: string,
    statusPointTx: InsertableStatusPointTxRow,
    tx?: Transaction<Database>,
  ): Promise<StatusPointTxEntity> {
    await this.setServiceId(serviceId, tx);
    return await (tx ?? db)
      .insertInto(TABLE_STATUS_POINT_TXS)
      .values(statusPointTx)
      .returningAll()
      .executeTakeFirstOrThrow();
  }
}
