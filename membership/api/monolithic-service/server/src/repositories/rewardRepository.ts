import { Database, db } from '../db/database';
import { DigitalContentRewardEntity } from '../tables/digitalContentRewardTable';
import { CertificateRewardEntity } from '../tables/certificateRewardTable';
import { InsertableRewardRow, RewardEntity } from '../tables/rewardTable';
import { MultitenantRepository } from './multitenantRepository';
import { sql } from 'kysely';
import { InsertableQuestRewardRow } from '../tables/questRewardTable';
import {
  TABLE_QUEST_REWARDS,
  TABLE_QUESTS,
  TABLE_REWARDS,
  TABLE_REWARD_TRANSLATIONS,
  TABLE_REWARD_COMPONENTS,
  TABLE_NFT_COMPONENTS,
  TABLE_POINT_COMPONENTS,
} from '../constants/database';
import { RewardAcquirementType } from '../enum/rewardAcquirementType';
import { QuestRewardPriorityType } from '../enum/questRewardPriorityType';
import { LanguageCode } from '../enum/languageCode';
import {
  InsertableRewardTranslationRow,
  RewardTranslationEntity,
} from '../tables/translations/rewardTranslationsTable';
import { Transaction } from 'kysely';
import { makeTranslationJoin, distinctByIdAndLang } from '../utils/i18n';
import { NftComponentEntity } from '../tables/nftComponentsTable';
import { PointComponentEntity } from '../tables/pointComponentsTable';
import { RewardComponentType } from '../enum/rewardComponentType';

export interface CouponReward {
  nft_contract_id?: string;
  token_id?: number;
}

export interface ExtendedReward {
  reward_id: string;
  reward_title: string;
  reward_cover_image_url: string;
  reward_thumbnail_image_url: string;
  reward_description: string;
  reward_acquirement_type: RewardAcquirementType;
  quest_reward_priority_type: QuestRewardPriorityType;
}

export interface InsertRewardResult {
  reward_id: string;
  service_id: string;
  reward_cover_image_url: string;
  reward_thumbnail_image_url: string;
  reward_acquirement_type: RewardAcquirementType;
}

export interface ExtendedQuestReward {
  reward_id: string;
  reward_title: string;
  reward_thumbnail_image_url: string;
  quest_reward_priority_type: QuestRewardPriorityType;
  order_index: number;
  rank_id?: string;
  gacha_weight?: number;
}

export interface ExtendedNftComponent extends NftComponentEntity {
  reward_component_id: string;
  reward_component_type: RewardComponentType;
}

export interface ExtendedPointComponent extends PointComponentEntity {
  reward_component_id: string;
  reward_component_type: RewardComponentType;
}

export class RewardRepository extends MultitenantRepository {
  async selectRewardById(rewardId: string, serviceId: string): Promise<RewardEntity> {
    await this.setServiceId(serviceId);
    return await db.selectFrom(TABLE_REWARDS).where('reward_id', '=', rewardId).selectAll().executeTakeFirstOrThrow();
  }

  async selectRewardQuestById(rewardId: string, serviceId: string, lang: LanguageCode): Promise<ExtendedReward> {
    await this.setServiceId(serviceId);
    const response = await db
      .selectFrom(TABLE_REWARDS)
      .innerJoin(TABLE_REWARD_TRANSLATIONS, (join) =>
        join
          .onRef('reward_translations.reward_id', '=', 'rewards.reward_id')
          .on('reward_translations.language', 'in', [lang, 'ja']),
      )
      .innerJoin(TABLE_QUEST_REWARDS, 'quest_rewards.reward_id', 'rewards.reward_id')
      .where('rewards.reward_id', '=', rewardId)
      .select([
        'rewards.reward_id',
        'reward_translations.reward_title',
        'rewards.reward_cover_image_url',
        'rewards.reward_thumbnail_image_url',
        'reward_translations.reward_description',
        'quest_rewards.reward_acquirement_type',
        'quest_rewards.quest_reward_priority_type',
      ])
      .orderBy(sql`CASE WHEN reward_translations.language = ${lang} THEN 0 ELSE 1 END`)
      .limit(1)
      .executeTakeFirstOrThrow();

    return response;
  }

  async selectQuestIdByRewardId(rewardId: string, serviceId: string): Promise<string> {
    await this.setServiceId(serviceId);

    const { quest_id } = await db
      .selectFrom(TABLE_REWARDS)
      .innerJoin(TABLE_QUEST_REWARDS, 'quest_rewards.reward_id', 'rewards.reward_id')
      .where('rewards.reward_id', '=', rewardId)
      .select('quest_rewards.quest_id')
      .executeTakeFirstOrThrow();

    return quest_id;
  }

  async selectRewardsByRankId(questId: string, rankId: string, serviceId: string): Promise<RewardEntity> {
    await this.setServiceId(serviceId);
    return await db
      .selectFrom(TABLE_REWARDS)
      .innerJoin(TABLE_QUEST_REWARDS, 'quest_rewards.reward_id', 'rewards.reward_id')
      .where('quest_rewards.quest_id', '=', questId)
      .where('quest_rewards.rank_id', '=', rankId)
      .selectAll([TABLE_REWARDS])
      .executeTakeFirstOrThrow();
  }

  async selectCouponRewardsByServiceId(serviceId: string): Promise<CouponReward[]> {
    await this.setServiceId(serviceId);
    return await db
      .selectFrom('coupon_rewards')
      .innerJoin(TABLE_REWARDS, 'rewards.reward_id', 'coupon_rewards.reward_id')
      .where('coupon_rewards.service_id', '=', serviceId)
      .select(['coupon_rewards.nft_contract_id', 'coupon_rewards.token_id'])
      .execute();
  }

  async selectDigitalContentRewardsByServiceId(serviceId: string): Promise<DigitalContentRewardEntity[]> {
    await this.setServiceId(serviceId);
    return await db
      .selectFrom('digital_content_rewards')
      .innerJoin(TABLE_REWARDS, 'rewards.reward_id', 'digital_content_rewards.reward_id')
      .where('digital_content_rewards.service_id', '=', serviceId)
      .selectAll(['digital_content_rewards'])
      .execute();
  }

  async selectCertificateRewardsByServiceId(serviceId: string): Promise<CertificateRewardEntity[]> {
    await this.setServiceId(serviceId);
    return await db
      .selectFrom('certificate_rewards')
      .innerJoin(TABLE_REWARDS, 'rewards.reward_id', 'certificate_rewards.reward_id')
      .where('certificate_rewards.service_id', '=', serviceId)
      .selectAll(['certificate_rewards'])
      .execute();
  }

  async selectRewardAcquirementType(rewardId: string, serviceId: string): Promise<RewardAcquirementType> {
    await this.setServiceId(serviceId);
    const { reward_acquirement_type } = await db
      .selectFrom(TABLE_REWARDS)
      .innerJoin(TABLE_QUEST_REWARDS, 'quest_rewards.reward_id', 'rewards.reward_id')
      .where('rewards.reward_id', '=', rewardId)
      .select('quest_rewards.reward_acquirement_type')
      .executeTakeFirstOrThrow();
    return reward_acquirement_type;
  }

  async selectMainRewardsByQuestId(questId: string, serviceId: string): Promise<RewardEntity> {
    await this.setServiceId(serviceId);

    return await db
      .selectFrom(TABLE_QUESTS)
      .innerJoin(TABLE_QUEST_REWARDS, 'quest_rewards.quest_id', 'quests.quest_id')
      .innerJoin(TABLE_REWARDS, 'quest_rewards.reward_id', 'rewards.reward_id')
      .where('quests.quest_id', '=', questId)
      .where('quest_rewards.quest_reward_priority_type', '=', QuestRewardPriorityType.MAIN)
      .selectAll([TABLE_REWARDS])
      .executeTakeFirstOrThrow();
  }

  async selectRewardsByQuestId(questId: string, serviceId: string, lang: LanguageCode): Promise<ExtendedQuestReward[]> {
    await this.setServiceId(serviceId);

    return await db
      .selectFrom(TABLE_QUESTS)
      .innerJoin(TABLE_QUEST_REWARDS, 'quest_rewards.quest_id', 'quests.quest_id')
      .innerJoin(TABLE_REWARDS, 'quest_rewards.reward_id', 'rewards.reward_id')
      .innerJoin(
        TABLE_REWARD_TRANSLATIONS,
        makeTranslationJoin(TABLE_REWARDS, TABLE_REWARD_TRANSLATIONS, 'reward_id', lang),
      )
      .where('quests.quest_id', '=', questId)
      .select([
        'rewards.reward_id',
        'reward_translations.reward_title',
        'rewards.reward_thumbnail_image_url',
        'quest_rewards.quest_reward_priority_type',
        'rewards.order_index',
        'quest_rewards.rank_id',
        'quest_rewards.gacha_weight',
      ])
      .$call(distinctByIdAndLang('reward_translations.reward_id', 'reward_translations.language', lang))
      .execute();
  }

  async selectStatusAchievementBadgeData(accountId: string, serviceId: string): Promise<{ metadata: object }[]> {
    await this.setServiceId(serviceId);

    return await db
      .selectFrom('claimed_rewards')
      .innerJoin('reward_components', 'reward_components.reward_component_id', 'claimed_rewards.reward_component_id')
      .innerJoin('nft_components', 'nft_components.nft_component_id', 'reward_components.nft_component_id')
      .innerJoin('nft_contracts', 'nft_contracts.nft_contract_id', 'nft_components.nft_contract_id')
      .innerJoin('nft_base_metadatas', 'nft_base_metadatas.contract_address', 'nft_contracts.nft_contract_address')
      .where('claimed_rewards.service_id', '=', serviceId)
      .where('claimed_rewards.account_id', '=', accountId)
      .select('nft_base_metadatas.metadata')
      .execute();
  }

  async insertReward(
    serviceId: string,
    reward: InsertableRewardRow,
    questReward: InsertableQuestRewardRow,
    trx?: Transaction<Database>,
  ): Promise<InsertRewardResult> {
    await this.setServiceId(serviceId);
    const insertedReward = await (trx ?? db)
      .insertInto(TABLE_REWARDS)
      .values(reward)
      .returningAll()
      .executeTakeFirstOrThrow();

    questReward.reward_id = insertedReward.reward_id;
    const insertedQuestReward = await (trx ?? db)
      .insertInto(TABLE_QUEST_REWARDS)
      .values(questReward)
      .returningAll()
      .executeTakeFirstOrThrow();

    return {
      ...insertedReward,
      reward_acquirement_type: insertedQuestReward.reward_acquirement_type,
    };
  }

  async insertRewardOnly(
    serviceId: string,
    reward: InsertableRewardRow,
    trx?: Transaction<Database>,
  ): Promise<RewardEntity> {
    await this.setServiceId(serviceId);
    return await (trx ?? db).insertInto(TABLE_REWARDS).values(reward).returningAll().executeTakeFirstOrThrow();
  }

  async insertRewardTranslations(
    serviceId: string,
    rewardTranslations: InsertableRewardTranslationRow[],
    trx?: Transaction<Database>,
  ): Promise<RewardTranslationEntity[]> {
    await this.setServiceId(serviceId);
    return await (trx ?? db).insertInto(TABLE_REWARD_TRANSLATIONS).values(rewardTranslations).returningAll().execute();
  }

  async findRewardComponents(
    service_id: string,
    rewardId: string,
  ): Promise<{ nftComponents: ExtendedNftComponent[]; pointComponents: ExtendedPointComponent[] }> {
    await this.setServiceId(service_id);

    const nftComponents = await db
      .selectFrom(TABLE_NFT_COMPONENTS)
      .innerJoin(TABLE_REWARD_COMPONENTS, 'reward_components.nft_component_id', 'nft_components.nft_component_id')
      .where('reward_components.reward_id', '=', rewardId)
      .select([
        'nft_components.nft_component_id',
        'nft_components.service_id',
        'nft_components.nft_contract_id',
        'nft_components.nft_contract_type',
        'nft_components.token_id',
        'nft_components.certificate_type',
        'nft_components.status_certificate_rank',
        'reward_components.reward_component_id',
        'reward_components.reward_component_type',
      ])
      .execute();

    const pointComponents = await db
      .selectFrom(TABLE_POINT_COMPONENTS)
      .innerJoin(TABLE_REWARD_COMPONENTS, 'reward_components.point_component_id', 'point_components.point_component_id')
      .where('reward_components.reward_id', '=', rewardId)
      .select([
        'point_components.point_component_id',
        'point_components.service_id',
        'point_components.amount',
        'point_components.point_type',
        'point_components.expires_on',
        'reward_components.reward_component_id',
        'reward_components.reward_component_type',
      ])
      .execute();

    return { nftComponents: nftComponents, pointComponents: pointComponents };
  }
}
