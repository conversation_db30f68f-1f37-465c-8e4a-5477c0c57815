import { Database, db } from '../db/database';
import { DigitalContentRewardEntity, InsertableDigitalContentRewardRow } from '../tables/digitalContentRewardTable';
import { CertificateRewardEntity, InsertableCertificateRewardRow } from '../tables/certificateRewardTable';
import { InsertableRewardRow, RewardEntity } from '../tables/rewardTable';
import { MultitenantRepository } from './multitenantRepository';
import { sql } from 'kysely';
import { InsertableQuestRewardRow } from '../tables/questRewardTable';
import { InsertableCouponRewardRow } from '../tables/couponRewardTable';
import { RewardType } from '../enum/rewardType';
import { TABLE_QUEST_REWARDS, TABLE_QUESTS, TABLE_REWARDS, TABLE_REWARD_TRANSLATIONS } from '../constants/database';
import { RewardAcquirementType } from '../enum/rewardAcquirementType';
import { QuestRewardPriorityType } from '../enum/questRewardPriorityType';
import { LanguageCode } from '../enum/languageCode';
import {
  InsertableRewardTranslationRow,
  RewardTranslationEntity,
} from '../tables/translations/rewardTranslationsTable';
import { Transaction } from 'kysely';
import { makeTranslationJoin, distinctByIdAndLang } from '../utils/i18n';

export interface CouponReward {
  nft_contract_id?: string;
  token_id?: number;
}

export interface ExtendedReward {
  reward_id: string;
  reward_title: string;
  reward_cover_image_url: string;
  reward_thumbnail_image_url: string;
  reward_description: string;
  reward_acquirement_type: RewardAcquirementType;
  reward_type: RewardType;
  quest_reward_priority_type: QuestRewardPriorityType;
}

export interface InsertRewardResult {
  reward_id: string;
  service_id: string;
  reward_cover_image_url: string;
  reward_thumbnail_image_url: string;
  reward_type: RewardType;
  reward_acquirement_type: RewardAcquirementType;
}
interface RewardDetail {
  nft_contract_id?: string;
  token_id?: number;
  certificate_type?: string;
  status_certificate_rank?: number;
}

export interface ExtendedQuestReward {
  reward_id: string;
  reward_title: string;
  reward_thumbnail_image_url: string;
  quest_reward_priority_type: QuestRewardPriorityType;
  reward_type: RewardType;
  order_index: number;
  rank_id?: string;
  gacha_weight?: number;
}

export class RewardRepository extends MultitenantRepository {
  async selectRewardById(rewardId: string, serviceId: string): Promise<RewardEntity> {
    await this.setServiceId(serviceId);
    return await db.selectFrom(TABLE_REWARDS).where('reward_id', '=', rewardId).selectAll().executeTakeFirstOrThrow();
  }

  async selectRewardQuestById(rewardId: string, serviceId: string, lang: LanguageCode): Promise<ExtendedReward> {
    await this.setServiceId(serviceId);
    const response = await db
      .selectFrom(TABLE_REWARDS)
      .innerJoin(TABLE_REWARD_TRANSLATIONS, (join) =>
        join
          .onRef('reward_translations.reward_id', '=', 'rewards.reward_id')
          .on('reward_translations.language', 'in', [lang, 'ja']),
      )
      .innerJoin(TABLE_QUEST_REWARDS, 'quest_rewards.reward_id', 'rewards.reward_id')
      .where('rewards.reward_id', '=', rewardId)
      .select([
        'rewards.reward_id',
        'reward_translations.reward_title',
        'rewards.reward_cover_image_url',
        'rewards.reward_thumbnail_image_url',
        'reward_translations.reward_description',
        'quest_rewards.reward_acquirement_type',
        'rewards.reward_type',
        'quest_rewards.quest_reward_priority_type',
      ])
      .orderBy(sql`CASE WHEN reward_translations.language = ${lang} THEN 0 ELSE 1 END`)
      .limit(1)
      .executeTakeFirstOrThrow();

    return response;
  }

  async selectQuestIdByRewardId(rewardId: string, serviceId: string): Promise<string> {
    await this.setServiceId(serviceId);

    const { quest_id } = await db
      .selectFrom(TABLE_REWARDS)
      .innerJoin(TABLE_QUEST_REWARDS, 'quest_rewards.reward_id', 'rewards.reward_id')
      .where('rewards.reward_id', '=', rewardId)
      .select('quest_rewards.quest_id')
      .executeTakeFirstOrThrow();

    return quest_id;
  }

  async selectRewardsByRankId(questId: string, rankId: string, serviceId: string): Promise<RewardEntity> {
    await this.setServiceId(serviceId);
    return await db
      .selectFrom(TABLE_REWARDS)
      .innerJoin(TABLE_QUEST_REWARDS, 'quest_rewards.reward_id', 'rewards.reward_id')
      .where('quest_rewards.quest_id', '=', questId)
      .where('quest_rewards.rank_id', '=', rankId)
      .selectAll([TABLE_REWARDS])
      .executeTakeFirstOrThrow();
  }

  async selectCouponRewardsByServiceId(serviceId: string): Promise<CouponReward[]> {
    await this.setServiceId(serviceId);
    return await db
      .selectFrom('coupon_rewards')
      .innerJoin(TABLE_REWARDS, 'rewards.reward_id', 'coupon_rewards.reward_id')
      .where('coupon_rewards.service_id', '=', serviceId)
      .select(['coupon_rewards.nft_contract_id', 'coupon_rewards.token_id'])
      .execute();
  }

  async selectDigitalContentRewardsByServiceId(serviceId: string): Promise<DigitalContentRewardEntity[]> {
    await this.setServiceId(serviceId);
    return await db
      .selectFrom('digital_content_rewards')
      .innerJoin(TABLE_REWARDS, 'rewards.reward_id', 'digital_content_rewards.reward_id')
      .where('digital_content_rewards.service_id', '=', serviceId)
      .selectAll(['digital_content_rewards'])
      .execute();
  }

  async selectCertificateRewardsByServiceId(serviceId: string): Promise<CertificateRewardEntity[]> {
    await this.setServiceId(serviceId);
    return await db
      .selectFrom('certificate_rewards')
      .innerJoin(TABLE_REWARDS, 'rewards.reward_id', 'certificate_rewards.reward_id')
      .where('certificate_rewards.service_id', '=', serviceId)
      .selectAll(['certificate_rewards'])
      .execute();
  }

  async selectRewardDetailById(serviceId: string, rewardId: string): Promise<RewardDetail | undefined> {
    await this.setServiceId(serviceId);
    const result = await db
      .selectFrom('rewards as r')
      .leftJoin('coupon_rewards as cr', 'cr.reward_id', 'r.reward_id')
      .leftJoin('certificate_rewards as cer', 'cer.reward_id', 'r.reward_id')
      .leftJoin('digital_content_rewards as dcr', 'dcr.reward_id', 'r.reward_id')
      .select([
        'r.reward_type',
        sql`COALESCE(cr.nft_contract_id, cer.nft_contract_id, dcr.nft_contract_id)`.as('nft_contract_id'),
        sql`COALESCE(cr.token_id, cer.token_id, dcr.token_id)`.as('token_id'),
        `cer.certificate_type`,
        `cer.status_certificate_rank`,
      ])
      .where('r.reward_id', '=', rewardId)
      .where('r.service_id', '=', serviceId)
      .executeTakeFirst();
    return result as RewardDetail | undefined;
  }

  async slectRewardAcquirementType(rewardId: string, serviceId: string): Promise<RewardAcquirementType> {
    await this.setServiceId(serviceId);
    const { reward_acquirement_type } = await db
      .selectFrom(TABLE_REWARDS)
      .innerJoin(TABLE_QUEST_REWARDS, 'quest_rewards.reward_id', 'rewards.reward_id')
      .where('rewards.reward_id', '=', rewardId)
      .select('quest_rewards.reward_acquirement_type')
      .executeTakeFirstOrThrow();
    return reward_acquirement_type;
  }

  async selectMainRewardsByQuestId(questId: string, serviceId: string): Promise<RewardEntity> {
    await this.setServiceId(serviceId);

    return await db
      .selectFrom(TABLE_QUESTS)
      .innerJoin(TABLE_QUEST_REWARDS, 'quest_rewards.quest_id', 'quests.quest_id')
      .innerJoin(TABLE_REWARDS, 'quest_rewards.reward_id', 'rewards.reward_id')
      .where('quests.quest_id', '=', questId)
      .where('quest_rewards.quest_reward_priority_type', '=', QuestRewardPriorityType.MAIN)
      .selectAll([TABLE_REWARDS])
      .executeTakeFirstOrThrow();
  }

  async selectRewardsByQuestId(questId: string, serviceId: string, lang: LanguageCode): Promise<ExtendedQuestReward[]> {
    await this.setServiceId(serviceId);

    return await db
      .selectFrom(TABLE_QUESTS)
      .innerJoin(TABLE_QUEST_REWARDS, 'quest_rewards.quest_id', 'quests.quest_id')
      .innerJoin(TABLE_REWARDS, 'quest_rewards.reward_id', 'rewards.reward_id')
      .innerJoin(
        TABLE_REWARD_TRANSLATIONS,
        makeTranslationJoin(TABLE_REWARDS, TABLE_REWARD_TRANSLATIONS, 'reward_id', lang),
      )
      .where('quests.quest_id', '=', questId)
      .select([
        'rewards.reward_id',
        'reward_translations.reward_title',
        'rewards.reward_thumbnail_image_url',
        'quest_rewards.quest_reward_priority_type',
        'rewards.reward_type',
        'rewards.order_index',
        'quest_rewards.rank_id',
        'quest_rewards.gacha_weight',
      ])
      .$call(distinctByIdAndLang('reward_translations.reward_id', 'reward_translations.language', lang))
      .execute();
  }

  async selectStatusAchievementBadgeData(accountId: string, serviceId: string): Promise<{ metadata: object }[]> {
    await this.setServiceId(serviceId);

    return await db
      .selectFrom('claimed_rewards')
      .innerJoin('certificate_rewards', 'claimed_rewards.reward_id', 'certificate_rewards.reward_id')
      .innerJoin('nft_contracts', 'nft_contracts.nft_contract_id', 'certificate_rewards.nft_contract_id')
      .innerJoin('nft_base_metadatas', 'nft_base_metadatas.contract_address', 'nft_contracts.nft_contract_address')
      .where('claimed_rewards.service_id', '=', serviceId)
      .where('claimed_rewards.account_id', '=', accountId)
      .select('nft_base_metadatas.metadata')
      .execute();
  }

  async insertReward(
    serviceId: string,
    reward: InsertableRewardRow,
    questReward: InsertableQuestRewardRow,
    certificateReward: InsertableCertificateRewardRow | undefined,
    digitalContentReward: InsertableDigitalContentRewardRow | undefined,
    couponReward: InsertableCouponRewardRow | undefined,
  ): Promise<InsertRewardResult> {
    await this.setServiceId(serviceId);
    return await db.transaction().execute(async (trx) => {
      const insertedReward = await trx
        .insertInto(TABLE_REWARDS)
        .values(reward)
        .returningAll()
        .executeTakeFirstOrThrow();

      questReward.reward_id = insertedReward.reward_id;
      const insertedQuestReward = await trx
        .insertInto(TABLE_QUEST_REWARDS)
        .values(questReward)
        .returningAll()
        .executeTakeFirstOrThrow();

      if (reward.reward_type === RewardType.CERTIFICATE && certificateReward) {
        certificateReward.reward_id = insertedReward.reward_id;
        await trx.insertInto('certificate_rewards').values(certificateReward).executeTakeFirstOrThrow();
      } else if (reward.reward_type === RewardType.CONTENT && digitalContentReward) {
        digitalContentReward.reward_id = insertedReward.reward_id;
        await trx.insertInto('digital_content_rewards').values(digitalContentReward).executeTakeFirstOrThrow();
      } else if (reward.reward_type === RewardType.COUPON && couponReward) {
        couponReward.reward_id = insertedReward.reward_id;
        await trx.insertInto('coupon_rewards').values(couponReward).executeTakeFirstOrThrow();
      } else throw new Error('invalid reward');

      return {
        ...insertedReward,
        reward_acquirement_type: insertedQuestReward.reward_acquirement_type,
      };
    });
  }

  async insertRewardOnly(
    serviceId: string,
    reward: InsertableRewardRow,
    certificateReward: InsertableCertificateRewardRow | undefined,
    digitalContentReward: InsertableDigitalContentRewardRow | undefined,
    couponReward: InsertableCouponRewardRow | undefined,
  ): Promise<RewardEntity> {
    await this.setServiceId(serviceId);
    return await db.transaction().execute(async (trx) => {
      const insertedReward = await trx
        .insertInto(TABLE_REWARDS)
        .values(reward)
        .returningAll()
        .executeTakeFirstOrThrow();

      if (reward.reward_type === RewardType.CERTIFICATE && certificateReward) {
        certificateReward.reward_id = insertedReward.reward_id;
        await trx.insertInto('certificate_rewards').values(certificateReward).executeTakeFirstOrThrow();
      } else if (reward.reward_type === RewardType.CONTENT && digitalContentReward) {
        digitalContentReward.reward_id = insertedReward.reward_id;
        await trx.insertInto('digital_content_rewards').values(digitalContentReward).executeTakeFirstOrThrow();
      } else if (reward.reward_type === RewardType.COUPON && couponReward) {
        couponReward.reward_id = insertedReward.reward_id;
        await trx.insertInto('coupon_rewards').values(couponReward).executeTakeFirstOrThrow();
      } else throw new Error('invalid reward');

      return insertedReward;
    });
  }

  async insertRewardTranslations(
    serviceId: string,
    rewardTranslations: InsertableRewardTranslationRow[],
    trx?: Transaction<Database>,
  ): Promise<RewardTranslationEntity[]> {
    await this.setServiceId(serviceId);
    return await (trx ?? db).insertInto(TABLE_REWARD_TRANSLATIONS).values(rewardTranslations).returningAll().execute();
  }
}
