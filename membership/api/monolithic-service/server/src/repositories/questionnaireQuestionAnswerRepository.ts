import { Transaction } from 'kysely';
import { TABLE_QUESTIONNAIRE_QUESTION_ANSWERS } from '../constants/database';
import { Database, db } from '../db/database';
import {
  InsertableQuestionnaireQuestionAnswerRow,
  QuestionnaireQuestionAnswerEntity,
} from '../tables/questionnaireQuestionAnswersTable';
import { MultitenantRepository } from './multitenantRepository';

export class QuestionnaireQuestionAnswerRepository extends MultitenantRepository {
  async insertQuestionnaireAnswers(
    serviceId: string,
    data: InsertableQuestionnaireQuestionAnswerRow[],
    trx?: Transaction<Database>,
  ): Promise<QuestionnaireQuestionAnswerEntity> {
    await this.setServiceId(serviceId, trx);

    return await (trx ?? db)
      .insertInto(TABLE_QUESTIONNAIRE_QUESTION_ANSWERS)
      .values(data)
      .returningAll()
      .executeTakeFirstOrThrow();
  }
}
