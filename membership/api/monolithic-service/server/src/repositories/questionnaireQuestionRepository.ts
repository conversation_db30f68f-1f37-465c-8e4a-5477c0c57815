import {
  TABLE_QUESTIONNAIRE_QUESTIONS,
  TABLE_QUESTIONNAIRE_THEMES,
  TABLE_QUESTIONNAIRE_QUESTION_TRANSLATIONS,
} from '../constants/database';
import { db } from '../db/database';
import { LanguageCode, languageCode } from '../enum/languageCode';
import { QuestionType } from '../enum/questionTypeEnum';
import { makeTranslationJoin, distinctByIdAndLang } from '../utils/i18n';
import { MultitenantRepository } from './multitenantRepository';

export interface QuestionCorrectAnswers {
  questionId: string;
  correctData?: string;
  answerPoint?: number;
  questionType: QuestionType;
  isRequired: boolean;
  validation?: string;
}

export class QuestionnaireQuestionRepository extends MultitenantRepository {
  async selectQuestionnaireQuestions(
    serviceId: string,
    questionnaireId: string,
    lang: LanguageCode = languageCode.JA,
  ): Promise<QuestionCorrectAnswers[]> {
    await this.setServiceId(serviceId);

    const answers: QuestionCorrectAnswers[] = await db
      .selectFrom(TABLE_QUESTIONNAIRE_QUESTIONS)
      .leftJoin(TABLE_QUESTIONNAIRE_THEMES, 'questionnaire_questions.theme_id', 'questionnaire_themes.theme_id')
      .innerJoin(
        TABLE_QUESTIONNAIRE_QUESTION_TRANSLATIONS,
        makeTranslationJoin(
          TABLE_QUESTIONNAIRE_QUESTIONS,
          TABLE_QUESTIONNAIRE_QUESTION_TRANSLATIONS,
          'question_id',
          lang,
        ),
      )
      .where('questionnaire_themes.questionnaire_id', '=', questionnaireId)
      .where('questionnaire_themes.service_id', '=', serviceId)
      .select((eb) => [
        eb.ref('questionnaire_questions.question_id').as('questionId'),
        eb.ref('questionnaire_question_translations.question_title').as('questionTitle'),
        eb.ref('questionnaire_question_translations.question_detail').as('questionDetail'),
        eb.ref('questionnaire_questions.answer_point').as('answerPoint'),
        eb.ref('questionnaire_questions.question_type').as('questionType'),
        eb.ref('questionnaire_question_translations.correct_data_validation').as('validation'),
        eb.ref('questionnaire_questions.is_required').as('isRequired'),
      ])
      .$call(distinctByIdAndLang('questionnaire_questions.question_id', 'questionnaire_question_translations.language', lang))
      .execute();

    return answers;
  }

  async sumQuestionnaireAnswerPoints(serviceId: string, questionnaireId: string): Promise<number | undefined> {
    const result = await db
      .selectFrom(TABLE_QUESTIONNAIRE_QUESTIONS)
      .leftJoin(TABLE_QUESTIONNAIRE_THEMES, 'questionnaire_questions.theme_id', 'questionnaire_themes.theme_id')
      .where('questionnaire_themes.questionnaire_id', '=', questionnaireId)
      .where('questionnaire_themes.service_id', '=', serviceId)
      .select((eb) => eb.fn.sum('questionnaire_questions.answer_point').as('points'))
      .execute();

    if (result[0].points === undefined) {
      return undefined;
    } else {
      return Number(result[0].points);
    }
  }
}
