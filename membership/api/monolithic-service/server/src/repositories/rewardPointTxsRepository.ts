import { Database, db } from '../db/database';
import { injectable } from 'tsyringe';
import { MultitenantRepository } from './multitenantRepository';
import { sql, Transaction } from 'kysely';
import { InsertableRewardPointTxRow, RewardPointTxEntity } from '../tables/rewardPointTxs';
import { TABLE_REWARD_POINT_TXS } from '../constants/database';
import { PointOpsType } from '../enum/pointOpsType';

@injectable()
export class RewardPointTxsRepository extends MultitenantRepository {
  async findExpirePointsTxs(
    serviceId: string,
    lastExecutedAt: Date,
    now: Date,
    tx?: Transaction<Database>,
  ): Promise<RewardPointTxEntity[]> {
    await this.setServiceId(serviceId, tx);

    return await (tx ?? db)
      .selectFrom(TABLE_REWARD_POINT_TXS)
      .where('service_id', '=', serviceId)
      .where('expires_on', '>', lastExecutedAt)
      .where('expires_on', '<=', now)
      .selectAll()
      .execute();
  }

  async getTotalPointsByAccountId(serviceId: string, accountId: string, tx?: Transaction<Database>): Promise<number> {
    await this.setServiceId(serviceId, tx);
    const { total } = await (tx ?? db)
      .selectFrom(TABLE_REWARD_POINT_TXS)
      .where('service_id', '=', serviceId)
      .where('account_id', '=', accountId)
      .select(sql`COALESCE(SUM(amount), 0)`.as('total'))
      .executeTakeFirstOrThrow();
    return Number(total);
  }

  async getUnexpiredPointsByAccountId(
    serviceId: string,
    accountId: string,
    now: Date,
    tx?: Transaction<Database>,
  ): Promise<number> {
    await this.setServiceId(serviceId, tx);
    const { remain } = await (tx ?? db)
      .selectFrom(TABLE_REWARD_POINT_TXS)
      .where('service_id', '=', serviceId)
      .where('account_id', '=', accountId)
      .where('expires_on', '>', now)
      .where('ops_type', '=', PointOpsType.ADD)
      .select(sql`COALESCE(SUM(amount), 0)`.as('remain'))
      .executeTakeFirstOrThrow();
    return Number(remain);
  }

  async insertRewardPointTx(
    serviceId: string,
    rewardPointTx: InsertableRewardPointTxRow,
    tx?: Transaction<Database>,
  ): Promise<RewardPointTxEntity> {
    await this.setServiceId(serviceId, tx);
    return await (tx ?? db)
      .insertInto(TABLE_REWARD_POINT_TXS)
      .values(rewardPointTx)
      .returningAll()
      .executeTakeFirstOrThrow();
  }
}
