import { Transaction } from 'kysely';
import { injectable } from 'tsyringe';
import { TABLE_CHECKOUTS, TABLE_PRODUCTS } from '../constants/database';
import { Database, db } from '../db/database';
import { CheckoutStatus } from '../enum/checkoutStatus';
import { ValidationError } from '../errors/validationError';
import { CheckoutEntity, InsertableCheckoutRow, UpdateableCheckoutRow } from '../tables/checkoutTable';
import { logger } from '../utils/middleware/loggerMiddleware';
import { MultitenantRepository } from './multitenantRepository';

@injectable()
export class CheckoutRepository extends MultitenantRepository {
  async selectCheckoutSession(sessionId: string, serviceId: string): Promise<CheckoutEntity | undefined> {
    await this.setServiceId(serviceId);
    return db
      .selectFrom(TABLE_CHECKOUTS)
      .selectAll()
      .where('service_id', '=', serviceId)
      .where('checkout_session_id', '=', sessionId)
      .executeTakeFirst();
  }

  async selectAvailableCheckoutSession(
    accountId: string,
    productId: string,
    serviceId: string,
  ): Promise<string | undefined> {
    await this.setServiceId(serviceId);
    const response = await db
      .selectFrom(TABLE_CHECKOUTS)
      .select('checkout_session_id')
      .where('service_id', '=', serviceId)
      .where('account_id', '=', accountId)
      .where('stripe_product_id', '=', productId)
      .where('checkout_status', '=', CheckoutStatus.STARTED)
      .executeTakeFirst();

    return response?.checkout_session_id;
  }

  async expireCheckoutSession(accountId: string, sessionId: string, serviceId: string): Promise<void> {
    await this.setServiceId(serviceId);
    await db
      .updateTable(TABLE_CHECKOUTS)
      .set({ checkout_status: CheckoutStatus.EXPIRED })
      .where('service_id', '=', serviceId)
      .where('account_id', '=', accountId)
      .where('checkout_session_id', '=', sessionId)
      .where('checkout_status', '=', CheckoutStatus.STARTED)
      .executeTakeFirst();
  }

  async expireCheckoutSessionByProduct(productId: string, serviceId: string): Promise<void> {
    await this.setServiceId(serviceId);
    await db
      .updateTable(TABLE_CHECKOUTS)
      .set({ checkout_status: CheckoutStatus.EXPIRED })
      .where('service_id', '=', serviceId)
      .where('stripe_product_id', '=', productId)
      .where('checkout_status', '=', CheckoutStatus.STARTED)
      .executeTakeFirst();
  }

  async expireCheckoutSessionByAccountAndProduct(
    accountId: string,
    productId: string,
    serviceId: string,
  ): Promise<void> {
    await this.setServiceId(serviceId);
    await db
      .updateTable(TABLE_CHECKOUTS)
      .set({ checkout_status: CheckoutStatus.EXPIRED })
      .where('service_id', '=', serviceId)
      .where('account_id', '=', accountId)
      .where('stripe_product_id', '=', productId)
      .where('checkout_status', '=', CheckoutStatus.STARTED)
      .executeTakeFirst();
  }

  async handleCheckoutCreationTransaction(data: InsertableCheckoutRow): Promise<boolean> {
    let isSuccess = false;
    try {
      await db.transaction().execute(async (trx) => {
        await this.insertCheckoutSession(trx, data);
      });
      isSuccess = true;
    } catch (error) {
      logger.error({ method: 'handleCheckoutCreationTransaction', error: error });
      isSuccess = false;
      throw error;
    }
    return isSuccess;
  }

  private async insertCheckoutSession(transaction: Transaction<Database>, data: InsertableCheckoutRow): Promise<void> {
    await this.setServiceId(data.service_id);

    const bookedSessionStatus = [CheckoutStatus.FULFILLING, CheckoutStatus.COMPLETED];
    const bookedCount = await transaction
      .selectFrom(TABLE_CHECKOUTS)
      .select(['checkout_session_id'])
      .where('stripe_product_id', '=', data.stripe_product_id)
      .where('checkout_status', 'in', bookedSessionStatus)
      .forUpdate()
      .execute();

    const product = await transaction
      .selectFrom(TABLE_PRODUCTS)
      .where('stripe_product_id', '=', data.stripe_product_id)
      .select(['quantity'])
      .forUpdate()
      .executeTakeFirst();

    if ((product?.quantity || 0) - bookedCount.length > 0) {
      await transaction.insertInto(TABLE_CHECKOUTS).values(data).execute();
    } else {
      logger.error({ method: 'insertCheckoutSession', error: 'Stock limit exceeded' });
      throw new ValidationError('Stock limit exceeded');
    }
  }

  async updateCheckoutSession(sessionId: string, serviceId: string, data: UpdateableCheckoutRow): Promise<void> {
    await this.setServiceId(serviceId);
    await db
      .updateTable(TABLE_CHECKOUTS)
      .set(data)
      .where('checkout_session_id', '=', sessionId)
      .where('service_id', '=', serviceId)
      .execute();
  }

  async countBlockSessionWithProductId(
    serviceId: string,
    productId: string,
    inclueOpenSession: boolean,
  ): Promise<number> {
    await this.setServiceId(serviceId);
    const availableStatus = inclueOpenSession
      ? [CheckoutStatus.STARTED, CheckoutStatus.FULFILLING, CheckoutStatus.COMPLETED]
      : [CheckoutStatus.FULFILLING, CheckoutStatus.COMPLETED];

    const result = await db
      .selectFrom(TABLE_CHECKOUTS)
      .select((eb) => eb.fn.count<number>('checkout_session_id').as('count'))
      .where('service_id', '=', serviceId)
      .where('stripe_product_id', '=', productId)
      .where('checkout_status', 'in', availableStatus)
      .executeTakeFirst();

    return result?.count ?? 0;
  }

  async accountAlreadyCheckoutedWithProductId(
    serviceId: string,
    productId: string,
    accountId: string,
  ): Promise<number> {
    await this.setServiceId(serviceId);
    const availableStatus = [CheckoutStatus.FULFILLING, CheckoutStatus.COMPLETED];

    const result = await db
      .selectFrom(TABLE_CHECKOUTS)
      .select((eb) => eb.fn.count<number>('checkout_session_id').as('count'))
      .where('service_id', '=', serviceId)
      .where('stripe_product_id', '=', productId)
      .where('account_id', '=', accountId)
      .where('checkout_status', 'in', availableStatus)
      .executeTakeFirst();

    return result?.count ?? 0;
  }

  async accountAlreadyCheckoutedWithProductIds(
    serviceId: string,
    productIds: string[],
    accountId: string,
  ): Promise<Map<string, { total_checkout_count: number; account_checkout_count: number }>> {
    await this.setServiceId(serviceId);
    const availableStatus = [CheckoutStatus.STARTED, CheckoutStatus.FULFILLING, CheckoutStatus.COMPLETED];

    const totalCheckoutCounts = await db
      .selectFrom(TABLE_CHECKOUTS)
      .select(['stripe_product_id', (eb) => eb.fn.count<number>('checkout_session_id').as('count')])
      .where('service_id', '=', serviceId)
      .where('stripe_product_id', 'in', productIds)
      .where('checkout_status', 'in', availableStatus)
      .groupBy('stripe_product_id')
      .execute();

    const accountCounts = await db
      .selectFrom(TABLE_CHECKOUTS)
      .select(['stripe_product_id', (eb) => eb.fn.count<number>('checkout_session_id').as('count')])
      .where('service_id', '=', serviceId)
      .where('stripe_product_id', 'in', productIds)
      .where('account_id', '=', accountId)
      .where('checkout_status', 'in', availableStatus)
      .groupBy('stripe_product_id')
      .execute();

    const resultMap = new Map<string, { total_checkout_count: number; account_checkout_count: number }>();

    productIds.forEach((productId) => {
      const totalCount = totalCheckoutCounts.find((row) => row.stripe_product_id === productId)?.count ?? 0;
      const accountCount = accountCounts.find((row) => row.stripe_product_id === productId)?.count ?? 0;
      resultMap.set(productId, {
        total_checkout_count: totalCount,
        account_checkout_count: accountCount,
      });
    });

    return resultMap;
  }

  async handleCompletionTransaction(
    executeMint: () => Promise<void>,
    serviceId: string,
    sessionId: string,
    productId: string,
  ): Promise<boolean> {
    let isSuccess = false;
    try {
      await db.transaction().execute(async (trx) => {
        await this.completeSession(trx, serviceId, sessionId, productId);
        await executeMint();
        isSuccess = true;
      });
      logger.info({ method: 'handleCompletionTransaction', status: 'success' });
    } catch (error) {
      logger.error({ method: 'handleCompletionTransaction', status: 'failure', error: error });
      return false;
    }

    logger.info({ method: 'handleCompletionTransaction', is_success: isSuccess });
    return isSuccess;
  }

  // async handleCompletionTransaction(
  //   executeMint: () => Promise<void>,
  //   serviceId: string,
  //   sessionId: string,
  //   productId: string,
  // ): Promise<boolean> {
  //   let isBlockNeed = false;
  //   let isSuccess = false;
  //   try {
  //     await db.transaction().execute(async (trx) => {
  //       isBlockNeed = await this.completeSession(trx, serviceId, sessionId, productId);
  //       if (isBlockNeed) await executeMint();
  //       isSuccess = true;
  //     });
  //     logger.info({ method: 'handleCompletionTransaction', status: 'success' });
  //   } catch (error) {
  //     logger.error({ method: 'handleCompletionTransaction', status: 'failure', error: error });
  //     return false;
  //   }

  //   if (!isBlockNeed) {
  //     try {
  //       await executeMint();
  //       isSuccess = true;
  //     } catch (error) {
  //       logger.error({ method: 'handleCompletionTransaction', status: 'failure', error: error });
  //       await this.markSessionAsFailed(serviceId, sessionId);
  //       isSuccess = false;
  //     }
  //   }

  //   logger.info({ method: 'handleCompletionTransaction', is_success: isSuccess });
  //   return isSuccess;
  // }

  private async markSessionAsFailed(serviceId: string, sessionId: string): Promise<void> {
    await this.setServiceId(serviceId);
    await db
      .updateTable(TABLE_CHECKOUTS)
      .set({ checkout_status: CheckoutStatus.FAILED })
      .where('checkout_session_id', '=', sessionId)
      .execute();
  }

  private async completeSession(
    transaction: Transaction<Database>,
    serviceId: string,
    sessionId: string,
    productId: string,
  ): Promise<boolean> {
    await this.setServiceId(serviceId);

    const product = await transaction
      .selectFrom(TABLE_PRODUCTS)
      .where('stripe_product_id', '=', productId)
      .select(['quantity'])
      .forUpdate()
      .executeTakeFirst();
    logger.info({ method: 'completeSession', product: product });

    await transaction
      .selectFrom(TABLE_CHECKOUTS)
      .select(['checkout_session_id'])
      .where('stripe_product_id', '=', productId)
      .where('checkout_status', 'in', [CheckoutStatus.STARTED, CheckoutStatus.FULFILLING])
      .forUpdate()
      .execute();

    const completeStatus = [CheckoutStatus.FULFILLING, CheckoutStatus.COMPLETED];
    const completedCount = await transaction
      .selectFrom(TABLE_CHECKOUTS)
      .select((eb) => eb.fn.count<number>('checkout_session_id').as('count'))
      .where('stripe_product_id', '=', productId)
      .where('checkout_status', 'in', completeStatus)
      .executeTakeFirstOrThrow();

    const remainingStock = (product?.quantity || 0) - completedCount.count;
    logger.info({ method: 'completeSession', remaining_stock: remainingStock });
    if (remainingStock > 0) {
      await transaction
        .updateTable(TABLE_CHECKOUTS)
        .set({ checkout_status: CheckoutStatus.COMPLETED })
        .where('checkout_session_id', '=', sessionId)
        .execute();
      return remainingStock <= 10;
    } else {
      logger.error({ method: 'completeSession', error: 'Stock limit exceeded' });
      throw new ValidationError('Stock limit exceeded');
    }
  }
}
