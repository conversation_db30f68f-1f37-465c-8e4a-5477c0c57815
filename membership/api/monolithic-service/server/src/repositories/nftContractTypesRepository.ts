import { injectable } from 'tsyringe';
import { Database, db } from '../db/database';
import { MultitenantRepository } from './multitenantRepository';
import { NftContractTypeEntity } from '../tables/nftContractTypeTable';
import { NftType } from '../enum/nftType';
import { Transaction } from 'kysely';

@injectable()
export class NftContractTypesRepository extends MultitenantRepository {
  async selectNftContractTypeById(nftContractTypeId: string): Promise<NftContractTypeEntity | undefined> {
    return db
      .selectFrom('nft_contract_types')
      .selectAll()
      .where('nft_contract_type_id', '=', nftContractTypeId)
      .executeTakeFirst();
  }

  async selectNftContractTypeByNftType(type: NftType): Promise<NftContractTypeEntity | undefined> {
    return db
      .selectFrom('nft_contract_types')
      .selectAll()
      .where('nft_type', '=', type)
      .executeTakeFirst();
  }

  async updateNftContractAddress(
    nftContractTypeId: string,
    nftContractAddress: string,
    trx?: Transaction<Database>,
  ): Promise<void> {
    await (trx ?? db)
      .updateTable('nft_contract_types')
      .set({ nft_contract_address: nftContractAddress })
      .where('nft_contract_type_id', '=', nftContractTypeId)
      .execute();
  }
}
