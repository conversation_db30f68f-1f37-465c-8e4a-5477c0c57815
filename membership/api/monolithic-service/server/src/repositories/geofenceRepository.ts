import { injectable } from 'tsyringe';
import { db } from '../db/database';
import { GeofencesTable } from '../tables/geofencesTable';
import { MultitenantRepository } from './multitenantRepository';
import { GeofenceFilters } from '../services/geofenceService';

@injectable()
export class GeofenceRepository extends MultitenantRepository {

  /**
   * Create a new geofence
   */
  async createGeofence(serviceId: string, geofenceData: Partial<GeofencesTable>): Promise<GeofencesTable> {
    await this.setServiceId(serviceId);
    return await db
      .insertInto('geofences')
      .values({
        ...geofenceData,
        created_at: new Date(),
        updated_at: new Date(),
      })
      .returningAll()
      .executeTakeFirstOrThrow();
  }

  /**
   * Update an existing geofence
   */
  async updateGeofence(serviceId: string, geofenceId: string, geofenceData: Partial<GeofencesTable>): Promise<GeofencesTable> {
    await this.setServiceId(serviceId);
    return await db
      .updateTable('geofences')
      .set({
        ...geofenceData,
        updated_at: new Date(),
      })
      .where('geofence_id', '=', geofenceId)
      .returningAll()
      .executeTakeFirstOrThrow();
  }

  /**
   * Get all geofences for a service with optional filters
   */
  async getGeofences(serviceId: string, filters: GeofenceFilters = {}): Promise<GeofencesTable[]> {
    await this.setServiceId(serviceId);

    let query = db
      .selectFrom('geofences')
      .selectAll()
      .orderBy('created_at', 'desc');

    // Apply filters
    if (filters.geofenceType) {
      query = query.where('geofence_type', '=', filters.geofenceType);
    }

    if (filters.search) {
      query = query.where('name', 'ilike', `%${filters.search}%`);
    }

    // Apply pagination
    if (filters.limit) {
      query = query.limit(filters.limit);
    }

    if (filters.page && filters.limit) {
      const offset = (filters.page - 1) * filters.limit;
      query = query.offset(offset);
    }

    return await query.execute();
  }

  /**
   * Get a specific geofence by ID
   */
  async getGeofenceById(serviceId: string, geofenceId: string): Promise<GeofencesTable | undefined> {
    await this.setServiceId(serviceId);
    return await db
      .selectFrom('geofences')
      .selectAll()
      .where('geofence_id', '=', geofenceId)
      .executeTakeFirst();
  }

  /**
   * Delete a geofence
   */
  async deleteGeofence(serviceId: string, geofenceId: string): Promise<void> {
    await this.setServiceId(serviceId);
    await db
      .deleteFrom('geofences')
      .where('geofence_id', '=', geofenceId)
      .execute();
  }

  /**
   * Check if a geofence exists
   */
  async geofenceExists(serviceId: string, geofenceId: string): Promise<boolean> {
    await this.setServiceId(serviceId);
    const result = await db
      .selectFrom('geofences')
      .select('geofence_id')
      .where('geofence_id', '=', geofenceId)
      .executeTakeFirst();

    return !!result;
  }

  /**
   * Get geofences count for a service
   */
  async getGeofencesCount(serviceId: string, filters: GeofenceFilters = {}): Promise<number> {
    await this.setServiceId(serviceId);

    let query = db
      .selectFrom('geofences')
      .select(({ fn }) => [fn.count<number>('geofence_id').as('count')]);

    // Apply filters
    if (filters.geofenceType) {
      query = query.where('geofence_type', '=', filters.geofenceType);
    }

    if (filters.search) {
      query = query.where('name', 'ilike', `%${filters.search}%`);
    }

    const result = await query.executeTakeFirst();
    return result?.count || 0;
  }

  // Legacy method for backward compatibility
  async selectGeofenceById(serviceId: string, geofenceId: string): Promise<GeofencesTable | undefined> {
    return this.getGeofenceById(serviceId, geofenceId);
  }
}
