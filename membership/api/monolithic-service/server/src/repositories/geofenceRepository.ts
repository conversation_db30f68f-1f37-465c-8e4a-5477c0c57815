import { injectable } from 'tsyringe';
import { db } from '../db/database';
import { sql } from 'kysely';
import { GeofencesTable } from '../tables/geofencesTable';
import { MultitenantRepository } from './multitenantRepository';
import { LocationCoordinates } from '../dtos/locations/schemas';

@injectable()
export class GeofenceRepository extends MultitenantRepository {
  async createGeofence(serviceId: string, geofenceData: Partial<GeofencesTable>): Promise<GeofencesTable> {
    await this.setServiceId(serviceId);
    return await db
      .insertInto('geofences')
      .values(geofenceData)
      .returningAll()
      .executeTakeFirstOrThrow();
  }

  async updateGeofence(serviceId: string, geofenceId: string, geofenceData: Partial<GeofencesTable>): Promise<GeofencesTable> {
    await this.setServiceId(serviceId);
    return await db
      .updateTable('geofences')
      .set(geofenceData)
      .where('geofence_id', '=', geofenceId)
      .returningAll()
      .executeTakeFirstOrThrow();
  }

  async getGeofences(serviceId: string): Promise<GeofencesTable[]> {
    await this.setServiceId(serviceId);

    let query = db
      .selectFrom('geofences')
      .selectAll()
      .select(sql`ST_AsGeoJSON(circle_geometry)::json`.as('circle_geometry'))
      .select(sql`ST_AsGeoJSON(polygon_geometry)::json`.as('polygon_geometry'))

    return await query.execute();
  }

  async getGeofenceById(serviceId: string, geofenceId: string): Promise<GeofencesTable | undefined> {
    await this.setServiceId(serviceId);
    return await db
      .selectFrom('geofences')
      .selectAll()
      .select(sql`ST_AsGeoJSON(circle_geometry)::json`.as('circle_geometry'))
      .select(sql`ST_AsGeoJSON(polygon_geometry)::json`.as('polygon_geometry'))
      .where('geofence_id', '=', geofenceId)
      .executeTakeFirst();
  }

  async isWithinGeofence(serviceId: string, geofenceId: string, locationData: LocationCoordinates): Promise<boolean> {
    const { is_within } = await db
      .selectFrom('geofences')
      .select(
        sql<boolean>`
          CASE
            WHEN geofence_type = 'CIRCLE' THEN
              ST_DWithin(
                ST_Transform(
                  ST_SetSRID(ST_MakePoint(${locationData.longitude}, ${locationData.latitude}), 4326),
                  3857
                ),
                ST_Transform(circle_geometry, 3857),
                circle_radius::double precision
              )
            WHEN geofence_type = 'POLYGON' THEN
              ST_Contains(
                polygon_geometry,
                ST_SetSRID(ST_MakePoint(${locationData.longitude}, ${locationData.latitude}), 4326)
              )
            ELSE FALSE
          END
        `.as('is_within')
      )
      .where('geofence_id', '=', geofenceId)
      .executeTakeFirstOrThrow();

    return is_within;
  }
}
