import { injectable } from 'tsyringe';
import { db } from '../db/database';
import { sql } from 'kysely';
import { GeofencesTable } from '../tables/geofencesTable';
import { MultitenantRepository } from './multitenantRepository';
import { GeofenceFilters } from '../services/geofenceService';

@injectable()
export class GeofenceRepository extends MultitenantRepository {
  async createGeofence(serviceId: string, geofenceData: Partial<GeofencesTable>): Promise<GeofencesTable> {
    await this.setServiceId(serviceId);
    return await db
      .insertInto('geofences')
      .values(geofenceData)
      .returningAll()
      .executeTakeFirstOrThrow();
  }

  async updateGeofence(serviceId: string, geofenceId: string, geofenceData: Partial<GeofencesTable>): Promise<GeofencesTable> {
    await this.setServiceId(serviceId);
    return await db
      .updateTable('geofences')
      .set(geofenceData)
      .where('geofence_id', '=', geofenceId)
      .returningAll()
      .executeTakeFirstOrThrow();
  }

  async getGeofences(serviceId: string, filters: GeofenceFilters = {}): Promise<GeofencesTable[]> {
    await this.setServiceId(serviceId);

    let query = db
      .selectFrom('geofences')
      .selectAll()
      .select(sql`ST_AsGeoJSON(circle_geometry)::json`.as('circle_geometry'))
      .select(sql`ST_AsGeoJSON(polygon_geometry)::json`.as('polygon_geometry'))

    return await query.execute();
  }

  async getGeofenceById(serviceId: string, geofenceId: string): Promise<GeofencesTable | undefined> {
    await this.setServiceId(serviceId);
    return await db
      .selectFrom('geofences')
      .selectAll()
      .select(sql`ST_AsGeoJSON(circle_geometry)::json`.as('circle_geometry'))
      .select(sql`ST_AsGeoJSON(polygon_geometry)::json`.as('polygon_geometry'))
      .where('geofence_id', '=', geofenceId)
      .executeTakeFirst();
  }
}
