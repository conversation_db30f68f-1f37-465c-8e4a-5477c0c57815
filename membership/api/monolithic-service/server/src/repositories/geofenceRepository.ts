import { injectable } from 'tsyringe';
import { db } from '../db/database';
import { GeofencesTable } from '../tables/geofencesTable';
import { MultitenantRepository } from './multitenantRepository';
import { CreateGeofenceRequest, LocationCoordinates } from '../dtos/location/schemas';

export interface LocationCheckinActionEntity {
  location_action_id: string;
  action_id: string;
  geofence_id: string;
  required_duration_seconds: number;
  created_at: Date;
  updated_at: Date;
}

export interface LocationCheckinAttemptEntity {
  attempt_id: string;
  account_id: string;
  action_id: string;
  latitude: number;
  longitude: number;
  accuracy_meters?: number;
  is_successful: boolean;
  distance_from_target: number;
  attempted_at: Date;
}

@injectable()
export class GeofenceRepository extends MultitenantRepository {

  // =======================
  // Geofence Operations
  // =======================

  async selectGeofenceById(serviceId: string, geofenceId: string): Promise<GeofencesTable | undefined> {
    await this.setServiceId(serviceId);
    return await db
      .selectFrom('geofences')
      .selectAll()
      .where('geofence_id', '=', geofenceId)
      .executeTakeFirst();
  }

  async createGeofence(serviceId: string, geofenceData: CreateGeofenceRequest): Promise<GeofencesTable> {
    await this.setServiceId(serviceId);

    const result = await db
      .insertInto('geofences')
      .values({
        service_id: serviceId,
        name: geofenceData.name,
        description: geofenceData.description,
        latitude: geofenceData.latitude,
        longitude: geofenceData.longitude,
        radius_meters: geofenceData.radiusMeters,
        is_active: true
      })
      .returningAll()
      .executeTakeFirstOrThrow();

    return result;
  }

  async getGeofencesByService(serviceId: string): Promise<GeofencesTable[]> {
    await this.setServiceId(serviceId);

    return await db
      .selectFrom('geofences')
      .selectAll()
      .where('service_id', '=', serviceId)
      .where('is_active', '=', true)
      .orderBy('created_at', 'desc')
      .execute();
  }

  async updateGeofence(serviceId: string, geofenceId: string, updates: Partial<CreateGeofenceRequest>): Promise<GeofencesTable | undefined> {
    await this.setServiceId(serviceId);

    const updateData: any = {};
    if (updates.name !== undefined) updateData.name = updates.name;
    if (updates.description !== undefined) updateData.description = updates.description;
    if (updates.latitude !== undefined) updateData.latitude = updates.latitude;
    if (updates.longitude !== undefined) updateData.longitude = updates.longitude;
    if (updates.radiusMeters !== undefined) updateData.radius_meters = updates.radiusMeters;

    if (Object.keys(updateData).length === 0) {
      return this.selectGeofenceById(serviceId, geofenceId);
    }

    return await db
      .updateTable('geofences')
      .set(updateData)
      .where('geofence_id', '=', geofenceId)
      .where('service_id', '=', serviceId)
      .returningAll()
      .executeTakeFirst();
  }

  async deleteGeofence(serviceId: string, geofenceId: string): Promise<boolean> {
    await this.setServiceId(serviceId);

    const result = await db
      .updateTable('geofences')
      .set({ is_active: false })
      .where('geofence_id', '=', geofenceId)
      .where('service_id', '=', serviceId)
      .execute();

    return result.length > 0;
  }

  // =======================
  // Location Check-in Action Operations
  // =======================

  async createLocationCheckinAction(actionId: string, geofenceId: string, requiredDurationSeconds: number = 0): Promise<LocationCheckinActionEntity> {
    const result = await db
      .insertInto('location_checkin_actions')
      .values({
        action_id: actionId,
        geofence_id: geofenceId,
        required_duration_seconds: requiredDurationSeconds
      })
      .returningAll()
      .executeTakeFirstOrThrow();

    return result;
  }

  async getLocationCheckinActionByActionId(actionId: string): Promise<LocationCheckinActionEntity | undefined> {
    return await db
      .selectFrom('location_checkin_actions')
      .selectAll()
      .where('action_id', '=', actionId)
      .executeTakeFirst();
  }

  async getLocationCheckinActionWithGeofence(actionId: string): Promise<(LocationCheckinActionEntity & GeofencesTable) | undefined> {
    return await db
      .selectFrom('location_checkin_actions')
      .innerJoin('geofences', 'location_checkin_actions.geofence_id', 'geofences.geofence_id')
      .selectAll()
      .where('location_checkin_actions.action_id', '=', actionId)
      .where('geofences.is_active', '=', true)
      .executeTakeFirst();
  }

  // =======================
  // Location Check-in Attempt Operations
  // =======================

  async createLocationCheckinAttempt(
    accountId: string,
    actionId: string,
    location: LocationCoordinates,
    isSuccessful: boolean,
    distanceFromTarget: number
  ): Promise<LocationCheckinAttemptEntity> {
    const result = await db
      .insertInto('location_checkin_attempts')
      .values({
        account_id: accountId,
        action_id: actionId,
        latitude: location.latitude,
        longitude: location.longitude,
        accuracy_meters: location.accuracy,
        is_successful: isSuccessful,
        distance_from_target: distanceFromTarget
      })
      .returningAll()
      .executeTakeFirstOrThrow();

    return result;
  }

  async getLocationCheckinAttempts(accountId: string, actionId: string): Promise<LocationCheckinAttemptEntity[]> {
    return await db
      .selectFrom('location_checkin_attempts')
      .selectAll()
      .where('account_id', '=', accountId)
      .where('action_id', '=', actionId)
      .orderBy('attempted_at', 'desc')
      .execute();
  }

  async getSuccessfulLocationCheckin(accountId: string, actionId: string): Promise<LocationCheckinAttemptEntity | undefined> {
    return await db
      .selectFrom('location_checkin_attempts')
      .selectAll()
      .where('account_id', '=', accountId)
      .where('action_id', '=', actionId)
      .where('is_successful', '=', true)
      .orderBy('attempted_at', 'desc')
      .executeTakeFirst();
  }

  // =======================
  // Geospatial Operations
  // =======================

  async calculateDistance(
    lat1: number,
    lon1: number,
    lat2: number,
    lon2: number
  ): Promise<number> {
    const result = await db
      .selectFrom(db.selectNoFrom(() => [
        db.fn('calculate_distance_meters', [lat1, lon1, lat2, lon2]).as('distance')
      ]).as('calc'))
      .select('distance')
      .executeTakeFirstOrThrow();

    return Number(result.distance);
  }

  async isLocationWithinGeofence(
    location: LocationCoordinates,
    geofence: GeofencesTable
  ): Promise<{ withinGeofence: boolean; distance: number }> {
    const distance = await this.calculateDistance(
      location.latitude,
      location.longitude,
      geofence.latitude,
      geofence.longitude
    );

    const withinGeofence = distance <= geofence.radius_meters;

    return { withinGeofence, distance };
  }
}
