import { injectable } from 'tsyringe';
import { db } from '../db/database';
import { GeofencesTable } from '../tables/geofencesTable';
import { MultitenantRepository } from './multitenantRepository';

@injectable()
export class GeofenceRepository extends MultitenantRepository {
  async selectGeofenceById(serviceId: string, geofenceId: string): Promise<GeofencesTable | undefined> {
    await this.setServiceId(serviceId);
    return await db
      .selectFrom('geofences')
      .selectAll()
      .where('geofence_id', '=', geofenceId)
      .executeTakeFirst();
  }
}
