import { injectable } from 'tsyringe';
import { db } from '../db/database';
import { MultitenantRepository } from './multitenantRepository';
import { LanguageCode } from '../enum/languageCode';
import { v4 as uuidv4 } from 'uuid';

type SetBucket = {
  set_number: number;
  hashes: string[];
};

interface TokenGateTranslation {
  language: LanguageCode;
  gateName: string;
  gateDescription: string;
}

export interface TokenGateCondition {
  setNumber: number;
  tokenHash: string;
}

@injectable()
export class TokenGateRepository extends MultitenantRepository {
  async fetchConditions(serviceId: string, tokenGateIds: string[]): Promise<Map<string, SetBucket[]>> {
    await this.setServiceId(serviceId);

    const rows = await db
      .selectFrom('token_gate_conditions as tgc')
      .distinct()
      .select(['tgc.token_gate_id', 'tgc.set_number', 'tgc.token_hash'])
      .where('tgc.token_gate_id', 'in', tokenGateIds)
      .execute();

    const map = new Map<string, SetBucket[]>();

    for (const row of rows) {
      const buckets = map.get(row.token_gate_id) ?? [];
      let bucket = buckets.find((b) => b.set_number === row.set_number);
      if (!bucket) {
        bucket = { set_number: row.set_number, hashes: [] };
        buckets.push(bucket);
      }
      bucket.hashes.push(row.token_hash);
      map.set(row.token_gate_id, buckets);
    }

    return map;
  }

  async createTokenGate(
    serviceId: string,
    tokenGateId: string,
    tokenGateTranslations: TokenGateTranslation[],
    tokenGateConditions: TokenGateCondition[],
  ): Promise<string> {
    await this.setServiceId(serviceId);
    await db.transaction().execute(async (trx) => {
      await trx
        .insertInto('token_gates')
        .values({
          token_gate_id: tokenGateId,
          service_id: serviceId,
        })
        .execute();

      await trx
        .insertInto('token_gate_translations')
        .values(
          tokenGateTranslations.map((t) => ({
            token_gate_id: tokenGateId,
            service_id: serviceId,
            language: t.language,
            gate_name: t.gateName,
            gate_description: t.gateDescription,
          })),
        )
        .execute();

      await trx
        .insertInto('token_gate_conditions')
        .values(
          tokenGateConditions.map((tgc) => ({
            token_gate_condition_id: uuidv4(),
            token_gate_id: tokenGateId,
            set_number: tgc.setNumber,
            token_hash: tgc.tokenHash,
          })),
        )
        .execute();
    });

    return tokenGateId;
  }
}
