import { injectable } from 'tsyringe';
import { Database, db } from '../db/database';
import { MultitenantRepository } from './multitenantRepository';
import { TABLE_TRANSACTIONS, TABLE_TRANSACTION_QUEUES, TABLE_ATTEMPT_TRANSACTIONS } from '../constants/database';
import {
  InsertableTransactionQueuesRow,
  TransactionQueuesEntity,
} from '../tables/transactionQueuesTable';
import { TransactionQueueStatus } from '../enum/transactionQueueStatus';
import { Transaction } from 'kysely';
import { NftType } from '../enum/nftType';
import { TxType } from '../enum/txType';

export interface BulkQueueData {
  serviceId: string,
  isMembership: boolean,
  transactionQueues: TransactionQueuesEntity[],
}
type BulkQueueMap = Record<string, BulkQueueData>;

@injectable()
export class TransactionQueuesRepository extends MultitenantRepository {
  async insertQueue(
    queue: InsertableTransactionQueuesRow,
    trx?: Transaction<Database>,
  ): Promise<TransactionQueuesEntity> {
    const executor = trx ?? db;
    queue.created_date ??= new Date();
    return await executor
      .insertInto(TABLE_TRANSACTION_QUEUES)
      .values(queue)
      .returningAll()
      .executeTakeFirstOrThrow();
  }

  async selectPendingQueues(
    trx?: Transaction<Database>
  ): Promise<TransactionQueuesEntity[]> {
    const executor = trx ?? db;
    return await executor
      .selectFrom(TABLE_TRANSACTION_QUEUES)
      .selectAll()
      .where('status', '=', TransactionQueueStatus.PENDING)
      .orderBy('created_date', 'asc')
      .execute();
  }

  async selectQueuesByIds(
    queueIds: string[],
    trx?: Transaction<Database>
  ): Promise<TransactionQueuesEntity[]> {
    const executor = trx ?? db;
    return await executor
      .selectFrom(TABLE_TRANSACTION_QUEUES)
      .selectAll()
      .where('queue_id', 'in', queueIds)
      .orderBy('created_date', 'asc')
      .execute();
  }

  groupByBulkData(transactionQueues: TransactionQueuesEntity[]): BulkQueueData[] {
    const ret = transactionQueues.reduce<BulkQueueMap>((acc, obj) => {
      const isMembership = obj.nft_type == NftType.MEMBERSHIP;
      const groupKey = `${obj.service_id}_${isMembership}`;
      if (!acc[groupKey]) {
        acc[groupKey] = {
          serviceId: obj.service_id,
          isMembership: isMembership,
          transactionQueues: []
        };
      }
      acc[groupKey].transactionQueues.push(obj);
      return acc;
    }, {});
    return Object.values(ret).flatMap(_ => _);
  }

  async updateQueueStatus(
    ids: string[],
    status: TransactionQueueStatus,
    trx?: Transaction<Database>
  ):Promise<void> {
    await (trx ?? db)
      .updateTable(TABLE_TRANSACTION_QUEUES)
      .set({ status: status })
      .where('queue_id', 'in', ids)
      .execute();
  }

  async updateQueueTokenId(
    queue_id: string,
    tokenId: number,
    trx?: Transaction<Database>
  ):Promise<void> {
    await (trx ?? db)
      .updateTable(TABLE_TRANSACTION_QUEUES)
      .set({ token_id: tokenId })
      .where('queue_id', '=', queue_id)
      .execute();
  }

  async selectQueueByTransactionId(
    transactionId: string,
    trx?: Transaction<Database>
  ): Promise<TransactionQueuesEntity[]> {
    return await (trx ?? db)
      .selectFrom(TABLE_ATTEMPT_TRANSACTIONS)
      .innerJoin(TABLE_TRANSACTION_QUEUES, `${TABLE_ATTEMPT_TRANSACTIONS}.queue_id`, `${TABLE_TRANSACTION_QUEUES}.queue_id`)
      .select([
        `${TABLE_TRANSACTION_QUEUES}.queue_id`,
        `${TABLE_TRANSACTION_QUEUES}.service_id`,
        `${TABLE_TRANSACTION_QUEUES}.from_address`,
        `${TABLE_TRANSACTION_QUEUES}.to_address`,
        `${TABLE_TRANSACTION_QUEUES}.status`,
        `${TABLE_TRANSACTION_QUEUES}.tx_type`,
        `${TABLE_TRANSACTION_QUEUES}.nft_type`,
        `${TABLE_TRANSACTION_QUEUES}.nft_contract_address`,
        `${TABLE_TRANSACTION_QUEUES}.token_id`,
        `${TABLE_TRANSACTION_QUEUES}.created_date`,
      ])
      .where('transaction_id', '=', transactionId)
      .execute();
  }

  async countQueueByMembershipAndNonce(
    serviceId: string,
    nonce: number,
    trx?: Transaction<Database>
  ): Promise<number> {
    const ret = await (trx ?? db)
      .selectFrom(TABLE_TRANSACTIONS)
      .innerJoin(TABLE_ATTEMPT_TRANSACTIONS, `${TABLE_ATTEMPT_TRANSACTIONS}.transaction_id`, `${TABLE_TRANSACTIONS}.transaction_id`)
      .innerJoin(TABLE_TRANSACTION_QUEUES, `${TABLE_TRANSACTION_QUEUES}.queue_id`, `${TABLE_ATTEMPT_TRANSACTIONS}.queue_id`)
      .select(({ fn }) => [fn.count(`${TABLE_TRANSACTION_QUEUES}.queue_id`).as('count')])
      .where(`${TABLE_TRANSACTION_QUEUES}.service_id`, '=', serviceId)
      .where('nonce', '>', nonce)
      .where('tx_type', '=', TxType.MINT_MEMBERSHIP)
      .executeTakeFirst();
      return Number(ret?.count);
  }
}
