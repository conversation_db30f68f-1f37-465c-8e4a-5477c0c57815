import { injectable } from 'tsyringe';
import { db } from '../db/database';
import { UserEntity } from '../tables/userTable';
import { UserStatus } from '../enum/userStatus';
import { MultitenantRepository } from './multitenantRepository';
import { ConflictError } from '../errors/conflictError';
import { InternalServerError } from '../errors/internalServerError';
import { CreateUser } from '../dtos/users/schemas';

@injectable()
export class UserRepository extends MultitenantRepository {
  async insertUser(user: CreateUser & { status: UserStatus }): Promise<UserEntity> {
    if (await this.userExists(user.userId)) {
      throw new ConflictError('User with the same ID already exists');
    }

    const newUser = await db
      .insertInto('users')
      .values({
        user_id: user.userId,
        country_code: user.countryCode,
        phone_number: user.phoneNumber,
        mnemonic_backup_key: user.mnemonicBackupKey,
        status: user.status,
        created_at: new Date(),
        updated_at: new Date(),
      })
      .returning(['user_id', 'country_code', 'phone_number', 'mnemonic_backup_key', 'status'])
      .executeTakeFirst();

    if (!newUser) {
      throw new InternalServerError('Failed to insert user: Insert operation returned no data');
    }

    return {
      user_id: newUser.user_id,
      country_code: newUser.country_code,
      phone_number: newUser.phone_number,
      mnemonic_backup_key: newUser.mnemonic_backup_key,
      status: newUser.status,
    } as UserEntity;
  }

  async userExists(userId: string): Promise<boolean> {
    const existingUser = await db
      .selectFrom('users')
      .select('user_id')
      .where('user_id', '=', userId)
      .executeTakeFirst();

    return existingUser !== undefined;
  }

  async selectUserById(id: string): Promise<UserEntity | null> {
    const user = await db
      .selectFrom('users')
      .selectAll()
      .where('user_id', '=', id)
      .where('status', '=', UserStatus.ACTIVE)
      .executeTakeFirst();

    if (!user) {
      return null;
    }

    return {
      user_id: user.user_id,
      country_code: user.country_code,
      phone_number: user.phone_number,
      contract_account_address: user.contract_account_address,
      mnemonic_backup_key: user.mnemonic_backup_key,
      status: user.status,
    } as UserEntity;
  }

  async updateUserState(userId: string, status: UserStatus): Promise<void> {
    await db
      .updateTable('users')
      .set({ status: status, updated_at: new Date() })
      .where('user_id', '=', userId)
      .execute();
  }

  async updateUserPhoneNumber(userId: string, phoneCountryCode: string, phone: string): Promise<void> {
    await db
      .updateTable('users')
      .set({
        country_code: phoneCountryCode,
        phone_number: phone,
        updated_at: new Date(),
      })
      .where('user_id', '=', userId)
      .execute();
  }

  async selectContractAccountAddress(userId: string): Promise<string | null> {
    const result = await db
      .selectFrom('users')
      .select('contract_account_address')
      .where('user_id', '=', userId)
      .executeTakeFirstOrThrow();

    return result.contract_account_address ? result.contract_account_address : null;
  }

  async selectUserAndAccountsByContractAccountAddresses(addresses: string[], serviceId: string): Promise<{
    account_id: string,
    transaction_id: string,
    membership_id?: number,
    contract_account_address?: string,
  }[]> {
    await this.setServiceId(serviceId);
    const result = await db
      .selectFrom('users')
      .innerJoin('accounts', 'accounts.user_id', 'users.user_id')
      .select([
        'account_id',
        'transaction_id',
        'membership_id',
        'contract_account_address',
      ])
      .where('contract_account_address', 'in', addresses.map(addr => addr.toLowerCase()))
      .execute();

    return result;
  }

  async updateContractAccount(userId: string, contractAccountAddress: string): Promise<void> {
    await db
      .updateTable('users')
      .set({
        contract_account_address: contractAccountAddress,
        updated_at: new Date(),
      })
      .where('user_id', '=', userId)
      .execute();
  }

  async checkUser(id: string): Promise<UserEntity | undefined> {
    return await db
      .selectFrom('users')
      .selectAll()
      .where('user_id', '=', id)
      .where('status', '=', UserStatus.ACTIVE)
      .executeTakeFirst();
  }
}
