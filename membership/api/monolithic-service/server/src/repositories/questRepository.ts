import { SelectQ<PERSON>yBuilder } from 'kysely';
import { Database, db } from '../db/database';
import { RewardStatus } from '../enum/rewardStatus';
import { InsertableQuestRow, QuestEntity, QuestWithTranslations } from '../tables/questTable';
import { MultitenantRepository } from './multitenantRepository';
import { QuestStatus } from '../enum/questStatus';
import { QuestActivityStatus } from '../enum/questActivityStatus';
import { QuestType } from '../enum/questType';
import {
  TABLE_QUEST_REWARDS,
  TABLE_QUEST_TRANSLATIONS,
  TABLE_QUESTS,
  TABLE_REWARD_TRANSLATIONS,
  TABLE_REWARDS,
} from '../constants/database';
import { LanguageCode } from '../enum/languageCode';
import { InsertableQuestTranslationRow, QuestTranslationEntity } from '../tables/translations/questTranslationsTable';
import { distinctByIdAndLang, langPref, makeTranslationJoin } from '../utils/i18n';

export interface QuestWithReward {
  quest_id: string;
  quest_title: string;
  quest_type: string;
  reward_id: string;
  reward_thumbnail_image_url: string;
  quest_thumbnail_image_url: string;
  quest_available_start_date: Date;
  quest_available_end_date: Date;
  order_index: number;
  quest_reward_priority_type: string;
  reward_title: string;
}

export class QuestRepository extends MultitenantRepository {
  async selectQuestsWithRewards(serviceId: string, lang: LanguageCode): Promise<QuestWithReward[]> {
    await this.setServiceId(serviceId);
    return db
      .selectFrom(TABLE_QUESTS)
      .innerJoin(TABLE_QUEST_REWARDS, 'quests.quest_id', 'quest_rewards.quest_id')
      .innerJoin(TABLE_REWARDS, 'quest_rewards.reward_id', 'rewards.reward_id')
      .innerJoin(
        TABLE_REWARD_TRANSLATIONS,
        makeTranslationJoin(TABLE_REWARDS, TABLE_REWARD_TRANSLATIONS, 'reward_id', lang),
      )
      .innerJoin(
        TABLE_QUEST_TRANSLATIONS,
        makeTranslationJoin(TABLE_QUESTS, TABLE_QUEST_TRANSLATIONS, 'quest_id', lang),
      )
      .where('quests.service_id', '=', serviceId)
      .select([
        'quests.quest_id',
        'quest_translations.quest_title',
        'quests.quest_type',
        'quest_rewards.reward_id',
        'quests.quest_thumbnail_image_url',
        'quests.quest_available_start_date',
        'quests.quest_available_end_date',
        'quests.order_index',
        'quest_rewards.quest_reward_priority_type',
        'reward_translations.reward_title',
        'rewards.reward_thumbnail_image_url',
      ])
      .distinctOn(['quests.quest_id', 'reward_translations.reward_id'])
      .orderBy('quests.quest_id')
      .orderBy('reward_translations.reward_id')
      .orderBy(langPref('quest_translations.language', lang))
      .orderBy(langPref('reward_translations.language', lang))
      .execute();
  }

  async selectQuestById(
    questId: string,
    serviceId: string,
    lang: LanguageCode,
  ): Promise<QuestWithTranslations | undefined> {
    await this.setServiceId(serviceId);
    return await db
      .selectFrom(TABLE_QUESTS)
      .innerJoin(
        TABLE_QUEST_TRANSLATIONS,
        makeTranslationJoin(TABLE_QUESTS, TABLE_QUEST_TRANSLATIONS, 'quest_id', lang),
      )
      .where('quests.quest_id', '=', questId)
      .where('quests.service_id', '=', serviceId)
      .selectAll()
      .$call(distinctByIdAndLang('quests.quest_id', 'quest_translations.language', lang))
      .executeTakeFirst();
  }

  async selectEnableStatusQuestByCurrentDate(serviceId: string): Promise<QuestEntity | undefined> {
    const now = new Date();
    await this.setServiceId(serviceId);
    return await db
      .selectFrom('quests')
      .where('quests.quest_type', '=', QuestType.STATUS)
      .where('quests.quest_available_start_date', '<=', now)
      .where('quests.quest_available_end_date', '>=', now)
      .where('quests.service_id', '=', serviceId)
      .selectAll()
      .executeTakeFirst();
  }

  async selectQuests(
    accountId: string,
    serviceId: string,
    rewardStatus?: string,
    questStatus?: string,
    startAtFrom?: string,
    startAtTo?: string,
    expireAtFrom?: string,
    expireAtTo?: string,
  ): Promise<string[]> {
    await this.setServiceId(serviceId);

    let query = db
      .selectFrom('quests')
      .distinct()
      .select('quests.quest_id')
      .leftJoin('quest_activities', (join) =>
        join
          .onRef('quests.quest_id', '=', 'quest_activities.quest_id')
          .on('quest_activities.account_id', '=', accountId),
      ) as SelectQueryBuilder<
      Database,
      'quests' | 'quest_activities',
      {
        quest_id: string;
      }
    >;

    query = this.applyQuestStatusFilter(query, questStatus);
    query = this.applyRewardStatusFilter(query, rewardStatus);
    query = this.applyDateFilters(query, startAtFrom, startAtTo, expireAtFrom, expireAtTo);

    const result = await query.execute();
    return result.map((row) => row.quest_id);
  }

  async selectActiveStatusQuest(
    serviceId: string,
    lang: LanguageCode,
    currentDate: Date = new Date(),
  ): Promise<QuestWithTranslations[]> {
    await this.setServiceId(serviceId);

    return await db
      .selectFrom(TABLE_QUESTS)
      .innerJoin(
        TABLE_QUEST_TRANSLATIONS,
        makeTranslationJoin(TABLE_QUESTS, TABLE_QUEST_TRANSLATIONS, 'quest_id', lang),
      )
      .where('quests.service_id', '=', serviceId)
      .where('quests.quest_available_start_date', '<=', currentDate)
      .where('quests.quest_available_end_date', '>=', currentDate)
      .where('quests.quest_type', '>=', QuestType.STATUS)
      .selectAll()
      .$call(distinctByIdAndLang('quests.quest_id', 'quest_translations.language', lang))
      .execute();
  }

  async selectQuestByActionId(actionId: string, serviceId: string): Promise<QuestEntity | undefined> {
    await this.setServiceId(serviceId);
    return await db
      .selectFrom('quest_actions')
      .innerJoin('quests', 'quest_actions.quest_id', 'quests.quest_id')
      .where('quest_actions.action_id', '=', actionId)
      .selectAll('quests')
      .executeTakeFirst();
  }

  async selectAvailableQuest(serviceId: string, questType?: QuestType): Promise<QuestEntity[]> {
    await this.setServiceId(serviceId);

    let query = db
      .selectFrom('quests')
      .selectAll()
      .where('quest_available_start_date', '<=', new Date())
      .where('quest_available_end_date', '>=', new Date());

    if (questType) {
      query = query.where('quest_type', '=', questType);
    }

    return await query.execute();
  }

  private applyQuestStatusFilter(
    query: SelectQueryBuilder<Database, 'quests' | 'quest_activities', { quest_id: string }>,
    questStatus?: string,
  ) {
    if (questStatus === undefined) {
      return query;
    }

    switch (questStatus) {
      case QuestStatus.NOT_STARTED:
        return query.where('quest_activities.quest_activity_status', 'is', null);

      case QuestStatus.IN_PROGRESS:
        return query.where('quest_activities.quest_activity_status', '=', QuestActivityStatus.PROCEEDING);

      case QuestStatus.COMPLETED:
        return query.where('quest_activities.quest_activity_status', '=', QuestActivityStatus.DONE);

      default:
        return query;
    }
  }

  private applyDateFilters(
    query: SelectQueryBuilder<Database, 'quests' | 'quest_activities', { quest_id: string }>,
    startAtFrom?: string,
    startAtTo?: string,
    expireAtFrom?: string,
    expireAtTo?: string,
  ) {
    if (startAtFrom) {
      query = query.where('quests.quest_available_start_date', '>=', new Date(startAtFrom));
    }
    if (startAtTo) {
      query = query.where('quests.quest_available_start_date', '<=', new Date(startAtTo));
    }
    if (expireAtFrom) {
      query = query.where('quests.quest_available_end_date', '>=', new Date(expireAtFrom));
    }
    if (expireAtTo) {
      query = query.where('quests.quest_available_end_date', '<=', new Date(expireAtTo));
    }
    return query;
  }

  private applyRewardStatusFilter(
    query: SelectQueryBuilder<Database, 'quests' | 'quest_activities', { quest_id: string }>,
    rewardStatus?: string,
  ) {
    if (rewardStatus === undefined) {
      return query;
    }

    query = query
      .where('quest_activities.quest_activity_status', '=', QuestActivityStatus.DONE)
      .rightJoin(TABLE_QUEST_REWARDS, 'quests.quest_id', 'quest_rewards.quest_id')
      .rightJoin('reward_components', 'quest_rewards.reward_id', 'reward_components.reward_id')
      .leftJoin('claimed_rewards', (join) =>
        join
          .onRef('reward_components.reward_component_id', '=', 'claimed_rewards.reward_component_id')
          .onRef('claimed_rewards.account_id', '=', 'quest_activities.account_id'),
      )
      .groupBy('quests.quest_id') as SelectQueryBuilder<Database, 'quests' | 'quest_activities', { quest_id: string }>;

    switch (rewardStatus) {
      case RewardStatus.NOT_ACQUIRED:
        return query.having(db.fn.count('claimed_rewards.reward_component_id'), '=', 0);

      case RewardStatus.PARTIALLY_ACQUIRED:
        return query
          .having(db.fn.count('claimed_rewards.reward_component_id'), '>', 0)
          .having(db.fn.count('reward_components.reward_id'), '>', db.fn.count('claimed_rewards.reward_component_id'));

      case RewardStatus.ACQUIRED:
        return query.having(
          db.fn.count('reward_components.reward_id'),
          '=',
          db.fn.count('claimed_rewards.reward_component_id'),
        );

      default:
        return query;
    }
  }

  async insertQuest(quest: InsertableQuestRow): Promise<QuestEntity> {
    await this.setServiceId(quest.service_id);
    return await db.insertInto('quests').values(quest).returningAll().executeTakeFirstOrThrow();
  }

  async insertQuestTranslations(questTranslations: InsertableQuestTranslationRow[]): Promise<QuestTranslationEntity[]> {
    await this.setServiceId(questTranslations[0].service_id);
    return await db.insertInto('quest_translations').values(questTranslations).returningAll().execute();
  }
}
