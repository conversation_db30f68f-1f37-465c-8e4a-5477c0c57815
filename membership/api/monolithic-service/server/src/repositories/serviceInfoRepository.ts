import { Database, db } from '../db/database';
import { injectable } from 'tsyringe';
import { MultitenantRepository } from './multitenantRepository';
import { TABLE_SERVICE_TRANSLATIONS, TABLE_SERVICES } from '../constants/database';
import { InsertableServiceRow, ServiceEntity, ServiceWithTranslations } from '../tables/servicesTable';
import { Transaction } from 'kysely';
import { LanguageCode } from '../enum/languageCode';
import { ServiceTranslationEntity } from '../tables/translations/serviceTranslationsTable';
import { InsertableServiceTranslationRow } from '../tables/translations/serviceTranslationsTable';
import { makeTranslationJoin, distinctByIdAndLang } from '../utils/i18n';

@injectable()
export class ServiceInfoRepository extends MultitenantRepository {
  async getServiceById(serviceId: string, tx?: Transaction<Database>): Promise<ServiceEntity | undefined> {
    await this.setServiceId(serviceId, tx);
    return await db.selectFrom(TABLE_SERVICES).where('service_id', '=', serviceId).selectAll().executeTakeFirst();
  }

  async selectLineChannelId(serviceId: string): Promise<string | undefined> {
    await this.setServiceId(serviceId);
    const response = await db
      .selectFrom(TABLE_SERVICES)
      .where('service_id', '=', serviceId)
      .select('line_channel_id')
      .executeTakeFirst();

    return response?.line_channel_id;
  }

  async updateServiceMembershipNftContractId(serviceId: string, nftId: string): Promise<void> {
    await this.setServiceId(serviceId);
    await db
      .updateTable(TABLE_SERVICES)
      .set({ membership_nft_contract_id: nftId })
      .where('service_id', '=', serviceId)
      .executeTakeFirst();
  }

  async insertService(service: InsertableServiceRow, dbTx?: Transaction<Database>): Promise<ServiceEntity> {
    await this.setServiceId(service.service_id);
    return await (dbTx ?? db).insertInto(TABLE_SERVICES).values(service).returningAll().executeTakeFirstOrThrow();
  }

  async updateServiceModularContract(
    serviceId: string,
    modularContractId: string,
    dbTx?: Transaction<Database>,
  ): Promise<void> {
    await this.setServiceId(serviceId);
    await (dbTx ?? db)
      .updateTable(TABLE_SERVICES)
      .set({ modular_contract_id: modularContractId })
      .where('service_id', '=', serviceId)
      .executeTakeFirst();
  }

  async getServiceList(): Promise<ServiceEntity[]> {
    return await db.selectFrom(TABLE_SERVICES).selectAll().execute();
  }
  async getServiceWithTranslationsById(
    serviceId: string,
    lang: LanguageCode,
    tx?: Transaction<Database>,
  ): Promise<ServiceWithTranslations | undefined> {
    await this.setServiceId(serviceId, tx);
    return await db
      .selectFrom(TABLE_SERVICES)
      .innerJoin(
        TABLE_SERVICE_TRANSLATIONS,
        makeTranslationJoin(TABLE_SERVICES, TABLE_SERVICE_TRANSLATIONS, 'service_id', lang),
      )
      .where('services.service_id', '=', serviceId)
      .select([
        'services.service_id',
        'services.tenant_id',
        'services.service_url',
        'services.service_logo_image_url',
        'services.market_cover_image_url',
        'services.theme_primary_color_lowest',
        'services.theme_primary_color_lower',
        'services.theme_primary_color_higher',
        'services.theme_primary_color_highest',
        'services.membership_nft_contract_id',
        'services.is_market_enabled',
        'services.stripe_account_id',
        'services.line_channel_id',
        'services.commission_rate',
        'services.modular_contract_id',
        'service_translations.service_name',
        'service_translations.service_policy',
        'service_translations.service_pane',
      ])
      .$call(distinctByIdAndLang('services.service_id', 'service_translations.language', lang))
      .executeTakeFirst();
  }

  async insertServiceTranslations(
    serviceId: string,
    serviceTranslations: InsertableServiceTranslationRow[],
    dbTx?: Transaction<Database>,
  ): Promise<ServiceTranslationEntity[]> {
    await this.setServiceId(serviceId);
    return await (dbTx ?? db)
      .insertInto(TABLE_SERVICE_TRANSLATIONS)
      .values(serviceTranslations)
      .returning(['service_id', 'language', 'service_name', 'service_policy', 'service_pane'])
      .execute();
  }
}
