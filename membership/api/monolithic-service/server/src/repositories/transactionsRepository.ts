import { injectable } from 'tsyringe';
import { Database, db } from '../db/database';
import { MultitenantRepository } from './multitenantRepository';
import { TABLE_ATTEMPT_TRANSACTIONS, TABLE_TRANSACTION_QUEUES, TABLE_TRANSACTIONS } from '../constants/database';
import { InsertableTransactionsRow, TransactionsEntity } from '../tables/transactionsTable';
import { TransactionStatus } from '../enum/transactionStatus';
import { Transaction } from 'kysely';
import { TxType } from '../enum/txType';
import { NftType } from '../enum/nftType';

@injectable()
export class TransactionsRepository extends MultitenantRepository {
  async insertTransaction(
    transaction: InsertableTransactionsRow,
    trx?: Transaction<Database>,
  ): Promise<TransactionsEntity> {
    const executor = trx ?? db;
    transaction.created_date ??= new Date();
    return await executor.insertInto(TABLE_TRANSACTIONS).values(transaction).returningAll().executeTakeFirstOrThrow();
  }

  async selectLongTimePendingTransactions(trx?: Transaction<Database>): Promise<TransactionsEntity[]> {
    const retryThresholdMs = Number(process.env.RETRY_THRESHOLD_MS) || 120000;
    return await (trx ?? db)
      .selectFrom(TABLE_TRANSACTIONS)
      .selectAll()
      .where('created_date', '<=', new Date(Date.now() - retryThresholdMs))
      .where('nonce', '>=', (eb) =>
        eb
          .selectFrom(TABLE_TRANSACTIONS)
          .select('nonce')
          .where('status', '=', TransactionStatus.CONFIRMED)
          .orderBy('nonce', 'desc')
          .limit(1),
      )
      .where('status', 'in', [TransactionStatus.EXECUTED, TransactionStatus.RETRY, TransactionStatus.FAILED])
      .orderBy('nonce', 'asc')
      .limit(20)
      .execute();
  }

  async selectTransactionsByTxHashs(txHashs: string[]): Promise<TransactionsEntity[]> {
    return await db.selectFrom(TABLE_TRANSACTIONS).selectAll().where('tx_hash', 'in', txHashs).execute();
  }

  async selectTransactionsByQueueIds(queueIds: string[]): Promise<
    Array<{
      fromAddress: string;
      txType: TxType;
      queueId: string;
      nftType: NftType;
      txHash: string | null;
      nonce: number;
      transactionId: string;
      serviceId: string;
    }>
  > {
    if (queueIds.length === 0) return [];

    const txs = await db
      .selectFrom(TABLE_TRANSACTIONS)
      .innerJoin(TABLE_ATTEMPT_TRANSACTIONS, 'attempt_transactions.transaction_id', 'transactions.transaction_id')
      .innerJoin(TABLE_TRANSACTION_QUEUES, 'attempt_transactions.queue_id', 'transaction_queues.queue_id')
      .where('transaction_queues.queue_id', 'in', queueIds)
      .where('transactions.status', 'in', [TransactionStatus.EXECUTED, TransactionStatus.RETRY])
      .select([
        'transaction_queues.from_address',
        'transaction_queues.tx_type',
        'transaction_queues.nft_type',
        'transactions.tx_hash',
        'transactions.nonce',
        'attempt_transactions.queue_id',
        'attempt_transactions.transaction_id',
        'transaction_queues.service_id',
      ])
      .execute();

    return txs.map((tx) => ({
      fromAddress: tx.from_address,
      txType: tx.tx_type,
      queueId: tx.queue_id,
      nftType: tx.nft_type,
      txHash: tx.tx_hash,
      nonce: tx.nonce,
      transactionId: tx.transaction_id,
      serviceId: tx.service_id,
    }));
  }

  async selectAllFailedTransactions(queueIds: string[]): Promise<
    Array<{
      fromAddress: string;
      txType: TxType;
      queueId: string;
      nftType: NftType;
      txHash: string | null;
      nonce: number;
      transactionId: string;
      serviceId: string;
    }>
  > {
    if (queueIds.length === 0) return [];

    const txs = await db
      .selectFrom(TABLE_TRANSACTIONS)
      .innerJoin(TABLE_ATTEMPT_TRANSACTIONS, 'attempt_transactions.transaction_id', 'transactions.transaction_id')
      .innerJoin(TABLE_TRANSACTION_QUEUES, 'attempt_transactions.queue_id', 'transaction_queues.queue_id')
      .where('transaction_queues.queue_id', 'in', queueIds)
      .where('transactions.status', 'in', [TransactionStatus.EXECUTED, TransactionStatus.RETRY])
      .select([
        'transaction_queues.from_address',
        'transaction_queues.tx_type',
        'transaction_queues.nft_type',
        'transactions.tx_hash',
        'transactions.nonce',
        'attempt_transactions.queue_id',
        'attempt_transactions.transaction_id',
        'transaction_queues.service_id',
      ])
      .execute();

    return txs.map((tx) => ({
      fromAddress: tx.from_address,
      txType: tx.tx_type,
      queueId: tx.queue_id,
      nftType: tx.nft_type,
      txHash: tx.tx_hash,
      nonce: tx.nonce,
      transactionId: tx.transaction_id,
      serviceId: tx.service_id,
    }));
  }

  async selectTransactionsByNonce(nonces: number[]): Promise<TransactionsEntity[]> {
    return await db.selectFrom(TABLE_TRANSACTIONS).selectAll().where('nonce', 'in', nonces).execute();
  }

  async selectTransactionsByStatus(status: TransactionStatus): Promise<TransactionsEntity[]> {
    return await db.selectFrom(TABLE_TRANSACTIONS).selectAll().where('status', '=', status).execute();
  }

  async updateTransactionStatus(
    transactionId: string,
    status: TransactionStatus,
    trx?: Transaction<Database>,
  ): Promise<void> {
    await (trx ?? db)
      .updateTable(TABLE_TRANSACTIONS)
      .set({ status: status })
      .where('transaction_id', '=', transactionId)
      .execute();
  }

  async updateTransactionHash(transactionId: string, txHash: string, trx?: Transaction<Database>): Promise<void> {
    await (trx ?? db)
      .updateTable(TABLE_TRANSACTIONS)
      .set({ tx_hash: txHash })
      .where('transaction_id', '=', transactionId)
      .execute();
  }
}
