import { db } from '../db/database';
import { ActionActivitiesStatus } from '../enum/actionType';
import { AchievementActionEntity } from '../tables/achievementActionTable';
import { ActionEntity, ActionWithTranslations, InsertableActionRow } from '../tables/actionTable';
import { TABLE_ACTION_TRANSLATIONS, TABLE_ACTIONS } from '../constants/database';
import { MultitenantRepository } from './multitenantRepository';
import { LanguageCode, languageCode } from '../enum/languageCode';
import { ActionTranslationEntity } from '../tables/translations/actionTranslationsTable';
import { InsertableActionTranslationRow } from '../tables/translations/actionTranslationsTable';
import { makeTranslationJoin, distinctByIdAndLang } from '../utils/i18n';

export interface ActionInfo {
  action_id: string;
  action_title: string;
  action_description: string;
  action_thumbnail_image_url: string;
  action_type: string;
  finish_date: Date | undefined;
}
export class ActionRepository extends MultitenantRepository {
  async selectActionsByQuestId(
    questId: string,
    serviceId: string,
    lang: LanguageCode = languageCode.JA,
  ): Promise<ActionWithTranslations[]> {
    await this.setServiceId(serviceId);
    return db
      .selectFrom(TABLE_ACTIONS)
      .innerJoin('quest_actions', 'quest_actions.action_id', 'actions.action_id')
      .innerJoin(
        TABLE_ACTION_TRANSLATIONS,
        makeTranslationJoin(TABLE_ACTIONS, TABLE_ACTION_TRANSLATIONS, 'action_id', lang),
      )
      .where('quest_actions.quest_id', '=', questId)
      .where('actions.service_id', '=', serviceId)
      .select([
        'actions.action_id',
        'actions.service_id',
        'action_translations.action_title',
        'action_translations.action_description',
        'action_translations.action_label',
        'actions.action_cover_image_url',
        'actions.action_thumbnail_image_url',
        'actions.action_available_start_date',
        'actions.action_available_end_date',
        'actions.action_type',
        'actions.geofence_id',
        'actions.order_index',
      ])
      .$call(distinctByIdAndLang('actions.action_id', 'action_translations.language', lang))
      .execute();
  }

  async selectStatusActionsByQuestId(
    questId: string,
    serviceId: string,
    lang: LanguageCode = languageCode.JA,
  ): Promise<(ActionWithTranslations & { milestone: number | undefined })[]> {
    await this.setServiceId(serviceId);
    return db
      .selectFrom('actions')
      .innerJoin('quest_actions', 'quest_actions.action_id', 'actions.action_id')
      .innerJoin('achievement_actions', 'achievement_actions.action_id', 'actions.action_id')
      .innerJoin(
        TABLE_ACTION_TRANSLATIONS,
        makeTranslationJoin(TABLE_ACTIONS, TABLE_ACTION_TRANSLATIONS, 'action_id', lang),
      )
      .where('quest_actions.quest_id', '=', questId)
      .where('actions.service_id', '=', serviceId)
      .select([
        'actions.action_id',
        'actions.service_id',
        'action_translations.action_title',
        'action_translations.action_description',
        'action_translations.action_label',
        'actions.action_cover_image_url',
        'actions.action_thumbnail_image_url',
        'actions.action_available_start_date',
        'actions.action_available_end_date',
        'actions.action_type',
        'actions.geofence_id',
        'actions.order_index',
        'achievement_actions.milestone',
      ])
      .$call(distinctByIdAndLang('actions.action_id', 'action_translations.language', lang))
      .execute();
  }

  async selectActionById(
    actionId: string,
    serviceId: string,
    lang: LanguageCode = languageCode.JA,
  ): Promise<ActionWithTranslations | undefined> {
    await this.setServiceId(serviceId);
    return await db
      .selectFrom('actions')
      .innerJoin(
        TABLE_ACTION_TRANSLATIONS,
        makeTranslationJoin(TABLE_ACTIONS, TABLE_ACTION_TRANSLATIONS, 'action_id', lang),
      )
      .where('actions.action_id', '=', actionId)
      .selectAll()
      .$call(distinctByIdAndLang('actions.action_id', 'action_translations.language', lang))
      .executeTakeFirst();
  }

  async selectCompletedActions(accountId: string, serviceId: string, lang: LanguageCode): Promise<ActionInfo[]> {
    await this.setServiceId(serviceId);

    return await db
      .selectFrom('accounts')
      .innerJoin('action_activities', 'accounts.account_id', 'action_activities.account_id')
      .innerJoin('actions', 'actions.action_id', 'action_activities.action_id')
      .innerJoin(
        TABLE_ACTION_TRANSLATIONS,
        makeTranslationJoin(TABLE_ACTIONS, TABLE_ACTION_TRANSLATIONS, 'action_id', lang),
      )
      .where('accounts.account_id', '=', accountId)
      .where('action_activities.action_activity_status', '=', ActionActivitiesStatus.DONE)
      .select([
        'actions.action_id',
        'action_translations.action_title',
        'action_translations.action_description',
        'actions.action_thumbnail_image_url',
        'actions.action_type',
        'action_activities.finish_date',
      ])
      .$call(distinctByIdAndLang('actions.action_id', 'action_translations.language', lang))
      .execute();
  }

  async selectAchievementActionsByQuestId(
    questId: string,
    serviceId: string,
    lang: LanguageCode = languageCode.JA,
  ): Promise<(ActionWithTranslations & AchievementActionEntity)[]> {
    await this.setServiceId(serviceId);

    return db
      .selectFrom('actions')
      .innerJoin('quest_actions', 'quest_actions.action_id', 'actions.action_id')
      .where('quest_actions.quest_id', '=', questId)
      .innerJoin('achievement_actions', 'actions.action_id', 'achievement_actions.action_id')
      .innerJoin('rewards', 'rewards.reward_id', 'achievement_actions.reward_id')
      .innerJoin(
        TABLE_ACTION_TRANSLATIONS,
        makeTranslationJoin(TABLE_ACTIONS, TABLE_ACTION_TRANSLATIONS, 'action_id', lang),
      )
      .where('actions.service_id', '=', serviceId)
      .selectAll(['actions', 'achievement_actions', 'action_translations'])
      .$call(distinctByIdAndLang('actions.action_id', 'action_translations.language', lang))
      .execute();
  }

  async insertAction(serviceId: string, action: InsertableActionRow): Promise<ActionEntity> {
    await this.setServiceId(serviceId);
    return await db.insertInto('actions').values(action).returningAll().executeTakeFirstOrThrow();
  }

  async insertActionTranslation(
    serviceId: string,
    actionTranslation: InsertableActionTranslationRow[],
  ): Promise<ActionTranslationEntity[]> {
    await this.setServiceId(serviceId);
    return await db.insertInto('action_translations').values(actionTranslation).returningAll().execute();
  }
}
