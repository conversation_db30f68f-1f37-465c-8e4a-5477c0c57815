import { db } from '../db/database';
import { MultitenantRepository } from './multitenantRepository';
import { QuestActivitiesStatus } from '../enum/actionType';
import { InsertableQuestActivityRow, QuestActivityEntity } from '../tables/questActivityTable';
import { QuestActivityStatus } from '../enum/questActivityStatus';
import { TABLE_ACCOUNTS, TABLE_QUEST_REWARDS, TABLE_QUEST_TRANSLATIONS, TABLE_QUESTS } from '../constants/database';
import { LanguageCode } from '../enum/languageCode';
import { distinctByIdAndLang, makeTranslationJoin } from '../utils/i18n';

export interface QuestInfo {
  quest_id: string;
  quest_title: string;
  quest_description: string;
  quest_thumbnail_image_url: string;
  quest_type: string;
  finish_date: Date | undefined;
}
export class QuestActivityRepository extends MultitenantRepository {
  async countCompletedQuestActivitiesByAccountId(accountId: string, serviceId: string): Promise<number> {
    await this.setServiceId(serviceId);
    const result = await db
      .selectFrom('quest_activities')
      .where('account_id', '=', accountId)
      .where('quest_activity_status', '=', QuestActivityStatus.DONE)
      .select(db.fn.count('account_id').as('count'))
      .executeTakeFirst();

    return result ? Number(result.count) : 0;
  }

  async countQuestActivitiesByAccountIdAndPeriod(
    accountId: string,
    serviceId: string,
    fromDate: Date,
    toDate: Date,
  ): Promise<number> {
    await this.setServiceId(serviceId);
    const result = await db
      .selectFrom('quest_activities')
      .where('account_id', '=', accountId)
      .where('finish_date', '>=', fromDate)
      .where('finish_date', '<=', toDate)
      .select(db.fn.count('account_id').as('count'))
      .executeTakeFirst();

    return result ? Number(result.count) : 0;
  }

  async selectCompletedQuests(accountId: string, serviceId: string, lang: LanguageCode): Promise<QuestInfo[]> {
    await this.setServiceId(serviceId);

    return await db
      .selectFrom(TABLE_ACCOUNTS)
      .innerJoin('quest_activities', 'accounts.account_id', 'quest_activities.account_id')
      .innerJoin(TABLE_QUESTS, 'quests.quest_id', 'quest_activities.quest_id')
      .innerJoin(
        TABLE_QUEST_TRANSLATIONS,
        makeTranslationJoin(TABLE_QUESTS, TABLE_QUEST_TRANSLATIONS, 'quest_id', lang),
      )
      .where('accounts.account_id', '=', accountId)
      .where('quest_activities.quest_activity_status', '=', QuestActivitiesStatus.DONE)
      .select([
        'quests.quest_id',
        'quest_translations.quest_title',
        'quest_translations.quest_description',
        'quests.quest_thumbnail_image_url',
        'quests.quest_type',
        'quest_activities.finish_date',
      ])
      .$call(distinctByIdAndLang('quests.quest_id', 'quest_translations.language', lang))
      .execute();
  }

  async upsertQuestActivity(questActivity: InsertableQuestActivityRow): Promise<void> {
    await this.setServiceId(questActivity.service_id);

    const existingActivity = await db
      .selectFrom('quest_activities')
      .where('account_id', '=', questActivity.account_id)
      .where('quest_id', '=', questActivity.quest_id)
      .selectAll()
      .executeTakeFirst();

    if (existingActivity) {
      await db
        .updateTable('quest_activities')
        .set({
          quest_activity_status: questActivity.quest_activity_status,
          finish_date: questActivity.finish_date,
        })
        .where('account_id', '=', questActivity.account_id)
        .where('quest_id', '=', questActivity.quest_id)
        .execute();
    } else {
      await db
        .insertInto('quest_activities')
        .values({
          account_id: questActivity.account_id,
          quest_id: questActivity.quest_id,
          service_id: questActivity.service_id,
          quest_activity_status: questActivity.quest_activity_status,
          finish_date: questActivity.finish_date,
        })
        .execute();
    }
  }

  async selectQuestActivity(
    accountId: string,
    questId: string,
    serviceId: string,
  ): Promise<QuestActivityEntity | undefined> {
    await this.setServiceId(serviceId);

    const result = await db
      .selectFrom('quest_activities')
      .where('quest_activities.account_id', '=', accountId)
      .where('quest_activities.quest_id', '=', questId)
      .selectAll()
      .executeTakeFirst();

    return result || undefined;
  }

  async countQuestActivities(
    accountId: string,
    serviceId: string,
    questActivityStatus: QuestActivityStatus,
    startDate: Date,
    endDate: Date,
  ): Promise<number> {
    await this.setServiceId(serviceId);

    const result = await db
      .selectFrom('quest_activities')
      .innerJoin('quests', 'quest_activities.quest_id', 'quests.quest_id')
      .where('quest_activities.account_id', '=', accountId)
      .where('quest_activities.quest_activity_status', '=', questActivityStatus)
      .where('quest_activities.finish_date', '>', startDate)
      .where('quest_activities.finish_date', '<', endDate)
      .select(db.fn.count('quest_activities.quest_id').as('count'))
      .executeTakeFirst();

    return result ? Number(result.count) : 0;
  }

  async checkQuestCompletedWithRewardId(
    accountId: string,
    rewardId: string,
    serviceId: string,
  ): Promise<boolean | undefined> {
    await this.setServiceId(serviceId);
    const completedQuestResult = await db
      .selectFrom('quest_activities')
      .innerJoin(TABLE_QUEST_REWARDS, 'quest_rewards.quest_id', 'quest_activities.quest_id')
      .where('quest_activities.account_id', '=', accountId)
      .where('quest_activities.quest_activity_status', '=', QuestActivityStatus.DONE)
      .where('quest_rewards.reward_id', '=', rewardId)
      .select(db.fn.count('quest_activities.quest_id').as('count'))
      .executeTakeFirst();

    const count = Number(completedQuestResult?.count ?? 0);
    return count === 1 ? true : count === 0 ? false : undefined;
  }
}
