import {
  TABLE_QUESTIONNAIRE_QUESTION_TRANSLATIONS,
  TABLE_QUESTIONNAIRE_QUESTIONS,
  TABLE_QUESTIONNAIRE_THEME_TRANSLATIONS,
  TABLE_QUESTIONNAIRE_THEMES,
} from '../constants/database';
import { db } from '../db/database';
import { LanguageCode } from '../enum/languageCode';
import { QuestionType } from '../enum/questionTypeEnum';
import { distinctByIdAndLang, langPref, makeTranslationJoin } from '../utils/i18n';
import { MultitenantRepository } from './multitenantRepository';

export interface ExtendedQuestionnaireThemeEntity {
  service_id: string;
  questionnaire_id: string;
  theme_id: string;
  theme_thumbnail_image_url?: string;
  theme_cover_image_url?: string;
  theme_title?: string;
  theme_description?: string;
  theme_number: number;
  theme_time_limit_seconds?: number;
  question_id: string;
  question_number: number;
  question_title: string;
  question_detail?: string;
  question_type: QuestionType;
  question_extra: object;
  correct_data?: string;
  correct_data_validation?: string;
  answer_point?: number;
  is_required: boolean;
  question_image_url?: string;
}

export interface ThemeAnswer {
  themeId: string;
  themeThumbnailImageUrl?: string;
  themeCoverImageUrl?: string;
  themeTitle?: string;
  themeNumber: number;
}

export class QuestionnaireThemeRepository extends MultitenantRepository {
  async selectQuestionnaireThemesByQuestionnaireId(
    questionnaireId: string,
    serviceId: string,
    lang: LanguageCode,
  ): Promise<ExtendedQuestionnaireThemeEntity[] | undefined> {
    await this.setServiceId(serviceId);

    const rows = await db
      .selectFrom(TABLE_QUESTIONNAIRE_THEMES)
      .leftJoin(
        TABLE_QUESTIONNAIRE_THEME_TRANSLATIONS,
        makeTranslationJoin(TABLE_QUESTIONNAIRE_THEMES, TABLE_QUESTIONNAIRE_THEME_TRANSLATIONS, 'theme_id', lang),
      )
      .innerJoin(TABLE_QUESTIONNAIRE_QUESTIONS, 'questionnaire_questions.theme_id', 'questionnaire_themes.theme_id')
      .innerJoin(
        TABLE_QUESTIONNAIRE_QUESTION_TRANSLATIONS,
        makeTranslationJoin(
          TABLE_QUESTIONNAIRE_QUESTIONS,
          TABLE_QUESTIONNAIRE_QUESTION_TRANSLATIONS,
          'question_id',
          lang,
        ),
      )
      .where('questionnaire_themes.questionnaire_id', '=', questionnaireId)
      .where('questionnaire_themes.service_id', '=', serviceId)
      .select([
        'questionnaire_themes.service_id',
        'questionnaire_themes.questionnaire_id',
        'questionnaire_themes.theme_id',
        'questionnaire_themes.theme_thumbnail_image_url',
        'questionnaire_themes.theme_cover_image_url',
        'questionnaire_theme_translations.theme_title',
        'questionnaire_theme_translations.theme_description',
        'questionnaire_themes.theme_number',
        'questionnaire_themes.theme_time_limit_seconds',
        'questionnaire_questions.question_id',
        'questionnaire_questions.question_number',
        'questionnaire_question_translations.question_title',
        'questionnaire_question_translations.question_detail',
        'questionnaire_question_translations.question_extra',
        'questionnaire_question_translations.correct_data',
        'questionnaire_question_translations.correct_data_validation',
        'questionnaire_questions.question_type',
        'questionnaire_questions.answer_point',
        'questionnaire_questions.is_required',
        'questionnaire_questions.question_image_url',
      ])
      .distinctOn(['questionnaire_themes.theme_id', 'questionnaire_questions.question_id'])
      .orderBy('questionnaire_themes.theme_id')
      .orderBy('questionnaire_questions.question_id')
      .orderBy(langPref('questionnaire_theme_translations.language', lang))
      .orderBy(langPref('questionnaire_question_translations.language', lang))
      .execute();

    return rows.map((row) => ({
      ...row,
      theme_title: row.theme_title ?? undefined,
      theme_description: row.theme_description ?? undefined,
    }));
  }

  async selectForQuestionnaireAnswers(
    serviceId: string,
    questionnaireId: string,
    lang: LanguageCode,
  ): Promise<ThemeAnswer[]> {
    await this.setServiceId(serviceId);

    const responses = await db
      .selectFrom(TABLE_QUESTIONNAIRE_THEMES)
      .leftJoin(
        TABLE_QUESTIONNAIRE_THEME_TRANSLATIONS,
        makeTranslationJoin(TABLE_QUESTIONNAIRE_THEMES, TABLE_QUESTIONNAIRE_THEME_TRANSLATIONS, 'theme_id', lang),
      )
      .where('questionnaire_themes.questionnaire_id', '=', questionnaireId)
      .select([
        'questionnaire_themes.theme_id',
        'questionnaire_themes.theme_thumbnail_image_url',
        'questionnaire_themes.theme_cover_image_url',
        'questionnaire_theme_translations.theme_title',
        'questionnaire_themes.theme_number',
      ])
      .$call(distinctByIdAndLang('questionnaire_themes.theme_id', 'questionnaire_theme_translations.language', lang))
      .execute();

    return responses.map((response) => {
      const themeAnswer: ThemeAnswer = {
        themeId: response.theme_id,
        themeThumbnailImageUrl: response.theme_thumbnail_image_url,
        themeCoverImageUrl: response.theme_cover_image_url,
        themeTitle: response.theme_title ?? undefined,
        themeNumber: response.theme_number,
      };
      return themeAnswer;
    });
  }
}
