import {
  TABLE_QUESTIONNAIRE_QUESTIONS,
  TABLE_QUESTIONNAIRE_RESULT_RANKS,
  TABLE_QUESTIONNAIRE_THEMES,
  TABLE_QUESTIONNAIRES,
  TABLE_QUESTIONNAIRE_QUESTION_TRANSLATIONS,
  TABLE_QUESTIONNAIRE_THEME_TRANSLATIONS,
  TABLE_QUESTIONNAIRE_RESULT_RANK_TRANSLATIONS,
} from '../constants/database';
import { Database, db } from '../db/database';
import { QuestionnaireType } from '../enum/questionnaireTypeEnum';

import {
  InsertableQuestionnaireQuestionRow,
  UpdateableQuestionnaireQuestionsRow,
} from '../tables/questionnaireQuestionsTable';
import {
  InsertableQuestionnaireResultRankRow,
  QuestionnaireResultRankEntity,
  QuestionnaireResultRankWithTranslations,
} from '../tables/questionnaireResultRanksTable';
import {
  InsertableQuestionnairesRow,
  QuestionnairesEntity,
  UpdateableQuestionnairesRow,
} from '../tables/questionnairesTable';
import {
  InsertableQuestionnaireThemeRow,
  QuestionnaireThemeEntity,
  UpdateableQuestionnaireThemesRow,
} from '../tables/questionnaireThemesTable';
import {
  InsertableQuestionnaireQuestionTranslationRow,
  QuestionnaireQuestionTranslationEntity,
} from '../tables/translations/questionnaireQuestionTranslationsTable';
import {
  InsertableQuestionnaireThemeTranslationRow,
  QuestionnaireThemeTranslationEntity,
} from '../tables/translations/questionnaireThemeTranslationsTable';

import { MultitenantRepository } from './multitenantRepository';
import { v4 as uuidv4 } from 'uuid';
import { QuestionnaireResultRankTranslationEntity } from '../tables/translations/questionnaireResultRankTranslationsTable';
import { InsertableQuestionnaireResultRankTranslationRow } from '../tables/translations/questionnaireResultRankTranslationsTable';
import { Transaction } from 'kysely';

export interface UpdateableThemeWithQuestions extends UpdateableQuestionnaireThemesRow {
  questions: QuestionnaireWithTranslations[];
  theme_translations: QuestionnaireThemeTranslationEntity[];
}

export interface ThemeWithQuestionsEntity extends QuestionnaireThemeEntity {
  questions: QuestionnaireWithTranslations[];
  theme_translations: QuestionnaireThemeTranslationEntity[];
}

export interface QuestionnaireActionDetail {
  resultRanks: QuestionnaireResultRankWithTranslationsEntity[];
  themes: ThemeWithQuestionsEntity[];
}

export interface QuestionnaireResultRankWithTranslationsEntity extends QuestionnaireResultRankEntity {
  rank_translations: QuestionnaireResultRankTranslationEntity[];
}

export interface QuestionnaireWithTranslations extends InsertableQuestionnaireQuestionRow {
  question_translations: QuestionnaireQuestionTranslationEntity[];
}

export interface ThemeWithQuestions {
  theme_data: InsertableQuestionnaireThemeRow;
  theme_translations: InsertableQuestionnaireThemeTranslationRow[];
  questions: {
    question_data: InsertableQuestionnaireQuestionRow;
    question_translations: InsertableQuestionnaireQuestionTranslationRow[];
  }[];
}

export class QuestionnaireRepository extends MultitenantRepository {
  async selectQuestionnaireByIdAction(actionId: string, serviceId: string): Promise<QuestionnairesEntity | undefined> {
    await this.setServiceId(serviceId);
    return await db
      .selectFrom(TABLE_QUESTIONNAIRES)
      .leftJoin('questionnaire_actions', 'questionnaire_actions.questionnaire_id', 'questionnaires.questionnaire_id')
      .where('questionnaire_actions.action_id', '=', actionId)
      .select(['questionnaires.service_id', 'questionnaires.questionnaire_id', 'questionnaires.questionnaire_type'])
      .executeTakeFirst();
  }

  async selectQuestionnaireByQuestionnaireId(
    questionnaireId: string,
    serviceId: string,
  ): Promise<QuestionnairesEntity | undefined> {
    await this.setServiceId(serviceId);
    return await db
      .selectFrom(TABLE_QUESTIONNAIRES)
      .where('questionnaire_id', '=', questionnaireId)
      .selectAll()
      .executeTakeFirst();
  }

  async insertQuestionnaireData(
    serviceId: string,
    questionnaireId: string,
    questionnaireType: QuestionnaireType,
    themes: ThemeWithQuestions[],
    resultRanks: QuestionnaireResultRankWithTranslations[],
  ): Promise<void> {
    await this.setServiceId(serviceId);

    return await db.transaction().execute(async (trx) => {
      try {
        const insertQuestionnaire: InsertableQuestionnairesRow = {
          questionnaire_id: questionnaireId,
          questionnaire_type: questionnaireType,
          service_id: serviceId,
        };
        await trx.insertInto(TABLE_QUESTIONNAIRES).values(insertQuestionnaire).executeTakeFirstOrThrow();

        for (const theme of themes) {
          const { theme_data, theme_translations, questions } = theme;
          await trx.insertInto(TABLE_QUESTIONNAIRE_THEMES).values(theme_data).executeTakeFirstOrThrow();
          if (theme_translations.length > 0) {
            await trx.insertInto(TABLE_QUESTIONNAIRE_THEME_TRANSLATIONS).values(theme_translations).execute();
          }

          for (const question of questions) {
            const { question_data, question_translations } = question;
            await trx.insertInto(TABLE_QUESTIONNAIRE_QUESTIONS).values(question_data).executeTakeFirstOrThrow();
            await trx
              .insertInto(TABLE_QUESTIONNAIRE_QUESTION_TRANSLATIONS)
              .values(question_translations)
              .executeTakeFirstOrThrow();
          }
        }

        for (const rank of resultRanks) {
          const { rank_translations, ...rankData } = rank;
          const insertRank: InsertableQuestionnaireResultRankRow = rankData;
          await trx.insertInto(TABLE_QUESTIONNAIRE_RESULT_RANKS).values(insertRank).executeTakeFirstOrThrow();
          await this.insertQuestionnaireResultRankTranslations(serviceId, rank_translations, trx);
        }
      } catch (error) {
        throw new Error(`An error occurred while inserting data: ${error}`);
      }
    });
  }

  async updateQuestionnaireActionData(
    serviceId: string,
    questionnaireId: string,
    questionnaireType: QuestionnaireType,
    themes: UpdateableThemeWithQuestions[],
    resultRanks: QuestionnaireResultRankWithTranslations[],
  ): Promise<void> {
    await this.setServiceId(serviceId);
    return await db.transaction().execute(async (trx) => {
      try {
        const updateQuestionnaire: UpdateableQuestionnairesRow = {
          questionnaire_type: questionnaireType,
          service_id: serviceId,
        };
        await trx
          .updateTable(TABLE_QUESTIONNAIRES)
          .set(updateQuestionnaire)
          .where('questionnaire_id', '=', questionnaireId)
          .executeTakeFirstOrThrow();

        if (themes && themes.length > 0) {
          for (const theme of themes) {
            const { questions, ...themeData } = theme;

            if (themeData.theme_id) {
              // Update existing theme
              await trx
                .updateTable(TABLE_QUESTIONNAIRE_THEMES)
                .set(themeData)
                .where('theme_id', '=', themeData.theme_id)
                .executeTakeFirstOrThrow();
              await this.insertQuestionnaireThemeTranslations(serviceId, themeData.theme_translations, trx);
            } else {
              // Insert new theme
              themeData.theme_id = uuidv4();
              await trx
                .insertInto(TABLE_QUESTIONNAIRE_THEMES)
                .values({
                  theme_id: themeData.theme_id,
                  questionnaire_id: themeData.questionnaire_id!,
                  service_id: serviceId,
                  theme_thumbnail_image_url: themeData.theme_thumbnail_image_url!,
                  theme_cover_image_url: themeData.theme_cover_image_url!,
                  theme_number: themeData.theme_number!,
                  theme_time_limit_seconds: themeData.theme_time_limit_seconds,
                })
                .executeTakeFirstOrThrow();
              await this.insertQuestionnaireThemeTranslations(serviceId, themeData.theme_translations, trx);
            }
            if (!questions) continue;
            // Process questions for each theme
            for (const question of questions) {
              const { question_translations, ...questionData } = question;
              const updateQuestion: UpdateableQuestionnaireQuestionsRow = questionData;
              if (question.question_id) {
                // Update existing question
                await trx
                  .updateTable(TABLE_QUESTIONNAIRE_QUESTIONS)
                  .set(updateQuestion)
                  .where('question_id', '=', question.question_id)
                  .executeTakeFirstOrThrow();
                await this.insertQuestionnaireQuestionTranslations(serviceId, question_translations, trx);
              } else {
                // Insert new question with the theme_id
                await trx
                  .insertInto(TABLE_QUESTIONNAIRE_QUESTIONS)
                  .values({
                    question_id: uuidv4(),
                    theme_id: themeData.theme_id,
                    service_id: themeData.service_id!,
                    question_number: updateQuestion.question_number!,
                    question_type: updateQuestion.question_type!,
                    is_required: updateQuestion.is_required!,
                    question_image_url: updateQuestion.question_image_url,
                    answer_point: updateQuestion.answer_point,
                  })
                  .executeTakeFirstOrThrow();
                await this.insertQuestionnaireQuestionTranslations(serviceId, question_translations, trx);
              }
            }
          }
        }

        // Process result ranks
        if (resultRanks && resultRanks.length > 0) {
          for (const rank of resultRanks) {
            if (rank.rank_id) {
              // Update existing rank
              await trx
                .updateTable(TABLE_QUESTIONNAIRE_RESULT_RANKS)
                .set(rank)
                .where('rank_id', '=', rank.rank_id)
                .executeTakeFirstOrThrow();
              await this.insertQuestionnaireResultRankTranslations(serviceId, rank.rank_translations, trx);
            } else {
              // Insert new rank
              await trx
                .insertInto(TABLE_QUESTIONNAIRE_RESULT_RANKS)
                .values(rank as InsertableQuestionnaireResultRankRow)
                .executeTakeFirstOrThrow();
              await this.insertQuestionnaireResultRankTranslations(serviceId, rank.rank_translations, trx);
            }
          }
        }
      } catch (error: unknown) {
        if (error instanceof Error) {
          throw new Error('An error occurred while updating data: ' + error.message);
        }
        throw new Error('An unexpected error occurred while updating data');
      }
    });
  }

  async getQuestionnaireActionDetail(serviceId: string, questionnaireId: string): Promise<QuestionnaireActionDetail> {
    await this.setServiceId(serviceId);

    // Fetch the questionnaire action
    const questionnaireAction = await db
      .selectFrom('questionnaire_actions')
      .where('questionnaire_id', '=', questionnaireId)
      .selectAll()
      .executeTakeFirst();

    if (!questionnaireAction) {
      throw new Error(`Questionnaire action with ID ${questionnaireId} not found`);
    }

    // Fetch themes associated with the questionnaire
    const themes = await db
      .selectFrom(TABLE_QUESTIONNAIRE_THEMES)
      .where('questionnaire_id', '=', questionnaireId)
      .selectAll()
      .execute();

    // Fetch questions for each theme
    const themesWithQuestions: ThemeWithQuestionsEntity[] = await Promise.all(
      themes.map(async (theme) => {
        const questions = await db
          .selectFrom(TABLE_QUESTIONNAIRE_QUESTIONS)
          .where('theme_id', '=', theme.theme_id)
          .selectAll()
          .execute();
        const questionsWithTranslations = await Promise.all(
          questions.map(async (q) => {
            const translations = await db
              .selectFrom(TABLE_QUESTIONNAIRE_QUESTION_TRANSLATIONS)
              .where('question_id', '=', q.question_id)
              .selectAll()
              .execute();
            return {
              ...q,
              question_translations: translations,
            };
          }),
        );

        const themeTranslations = await db
          .selectFrom(TABLE_QUESTIONNAIRE_THEME_TRANSLATIONS)
          .where('theme_id', '=', theme.theme_id)
          .selectAll()
          .execute();

        return {
          ...theme,
          questions: questionsWithTranslations,
          theme_translations: themeTranslations,
        };
      }),
    );

    // Fetch result ranks associated with the questionnaire
    const resultRanks = await Promise.all(
      (
        await db
          .selectFrom(TABLE_QUESTIONNAIRE_RESULT_RANKS)
          .where('questionnaire_id', '=', questionnaireId)
          .selectAll()
          .execute()
      ).map(async (rank) => {
        const translations = await db
          .selectFrom(TABLE_QUESTIONNAIRE_RESULT_RANK_TRANSLATIONS)
          .where('rank_id', '=', rank.rank_id)
          .selectAll()
          .execute();
        return {
          ...rank,
          rank_translations: translations,
        } as QuestionnaireResultRankWithTranslationsEntity;
      }),
    );

    return { resultRanks, themes: themesWithQuestions };
  }

  async getQuestionnaireType(serviceId: string, questionnaireId: string): Promise<QuestionnaireType> {
    await this.setServiceId(serviceId);

    // Fetch the questionnaire action
    const action = await db
      .selectFrom('questionnaires')
      .where('questionnaire_id', '=', questionnaireId)
      .select('questionnaire_type')
      .executeTakeFirstOrThrow();

    return action?.questionnaire_type;
  }

  async insertQuestionnaireQuestionTranslations(
    serviceId: string,
    translations: InsertableQuestionnaireQuestionTranslationRow[],
    trx?: Transaction<Database>
  ): Promise<QuestionnaireQuestionTranslationEntity[]> {
    await this.setServiceId(serviceId, trx);
    return await (trx ?? db).insertInto(TABLE_QUESTIONNAIRE_QUESTION_TRANSLATIONS).values(translations).returningAll().execute();
  }

  async insertQuestionnaireThemeTranslations(
    serviceId: string,
    translations: InsertableQuestionnaireThemeTranslationRow[],
    trx?: Transaction<Database>,
  ): Promise<QuestionnaireThemeTranslationEntity[]> {
    await this.setServiceId(serviceId, trx);
    return await (trx ?? db)
      .insertInto(TABLE_QUESTIONNAIRE_THEME_TRANSLATIONS)
      .values(translations)
      .returningAll()
      .execute();
  }

  async insertQuestionnaireResultRankTranslations(
    serviceId: string,
    translations: InsertableQuestionnaireResultRankTranslationRow[],
    trx?: Transaction<Database>
  ): Promise<QuestionnaireResultRankTranslationEntity[]> {
    await this.setServiceId(serviceId, trx);
    return await (trx ?? db)
      .insertInto(TABLE_QUESTIONNAIRE_RESULT_RANK_TRANSLATIONS)
      .values(translations)
      .returningAll()
      .execute();
  }
}
