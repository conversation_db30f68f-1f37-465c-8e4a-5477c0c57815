// nftBaseMetadatasRepository.ts
import { injectable } from 'tsyringe';
import { db } from '../db/database';
import { MultitenantRepository } from './multitenantRepository';
import { InsertableNftBaseMetadatasRow, NftBaseMetadatasEntity } from '../tables/nftBaseMetadatasTable';
import { ConflictError } from '../errors/conflictError';
import { TABLE_NFT_BASE_METADATAS, TABLE_NFT_CONTRACTS } from '../constants/database';

@injectable()
export class NftBaseMetadatasRepository extends MultitenantRepository {
  async selectDataByNftId(baseNftId: string, tokenId?: number): Promise<NftBaseMetadatasEntity | undefined> {
    if (tokenId) {
      return await db
        .selectFrom(TABLE_NFT_BASE_METADATAS)
        .where('base_metadata_id', '=', baseNftId)
        .where('token_id', '=', tokenId)
        .selectAll()
        .executeTakeFirst();
    } else {
      return await db
        .selectFrom(TABLE_NFT_BASE_METADATAS)
        .where('base_metadata_id', '=', baseNftId)
        .selectAll()
        .executeTakeFirst();
    }
  }

  async selectNftIdFromContractId(contractId: string): Promise<string | undefined> {
    const nftBaseMetadata = await db
      .selectFrom(`${TABLE_NFT_BASE_METADATAS} as nbm`)
      .leftJoin(`${TABLE_NFT_CONTRACTS} as nc`, 'nbm.contract_address', 'nc.nft_contract_address')
      .select('base_metadata_id')
      .where('nc.nft_contract_id', '=', contractId)
      .executeTakeFirst();
    return nftBaseMetadata?.base_metadata_id;
  }

  async selectBaseMetadataByContractAndToken(contractAddress: string, tokenId: number): Promise<object | undefined> {
    const nftBaseMetadata = await db
      .selectFrom(TABLE_NFT_BASE_METADATAS)
      .select('metadata')
      .where('contract_address', 'ilike', contractAddress)
      .where('token_id', '=', tokenId)
      .executeTakeFirst();

    return nftBaseMetadata?.metadata;
  }

  async selectDataByContractAddress(contractAddress: string): Promise<NftBaseMetadatasEntity | undefined> {
    const nftBaseMetadata = await db
      .selectFrom(TABLE_NFT_BASE_METADATAS)
      .where('contract_address', 'ilike', contractAddress)
      .selectAll()
      .execute();

    if (nftBaseMetadata.length > 1)
      throw new ConflictError('Metadata is required to uniquely stored by vontract address');
    else if (nftBaseMetadata.length === 0) return undefined;

    return nftBaseMetadata[0];
  }

  async selectDataByContractAddressAndTokenId(
    contractAddress: string,
    tokenId: number,
  ): Promise<NftBaseMetadatasEntity | undefined> {
    return await db
      .selectFrom(TABLE_NFT_BASE_METADATAS)
      .where('contract_address', 'ilike', contractAddress)
      .where('token_id', '=', tokenId)
      .selectAll()
      .executeTakeFirst();
  }

  async insertNftBaseMetadata(nftBaseMetadata: InsertableNftBaseMetadatasRow): Promise<NftBaseMetadatasEntity> {
    return await db.insertInto(TABLE_NFT_BASE_METADATAS).values(nftBaseMetadata).returningAll().executeTakeFirstOrThrow();
  }

  async updateContractAddressNftBaseMetadata(baseMetadataId: string, contractAddress: string): Promise<void> {
    await db
      .updateTable(TABLE_NFT_BASE_METADATAS)
      .set({ contract_address: contractAddress })
      .where('base_metadata_id', 'ilike', baseMetadataId)
      .execute();
  }

  async updateContractAddressAndTokenIdNftBaseMetadata(
    baseMetadataId: string,
    contractAddress: string,
    tokenId?: number,
  ): Promise<void> {
    await db
      .updateTable(TABLE_NFT_BASE_METADATAS)
      .set({ contract_address: contractAddress, token_id: tokenId })
      .where('base_metadata_id', 'ilike', baseMetadataId)
      .execute();
  }
}
