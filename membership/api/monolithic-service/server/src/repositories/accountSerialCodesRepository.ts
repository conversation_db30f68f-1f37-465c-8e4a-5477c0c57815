import { Database, db } from '../db/database';
import { MultitenantRepository } from './multitenantRepository';
import { TABLE_ACCOUNT_SERIAL_CODES } from '../constants/database';
import { AccountSerialCodesEntity } from '../tables/accountSerialCodesTable';
import { Transaction } from 'kysely';
import { v4 as uuidv4 } from 'uuid';

export class AccountSerialCodesRepository extends MultitenantRepository {

  async insert(
    accountId: string,
    serialCodeId: string,
    trx?: Transaction<Database>
  ): Promise<AccountSerialCodesEntity> {
    return await (trx ?? db)
      .insertInto(TABLE_ACCOUNT_SERIAL_CODES)
      .values({
        account_serial_code_id: uuidv4(),
        account_id: accountId,
        serial_code_id: serialCodeId,
        created_at: new Date(),
      })
      .returningAll()
      .executeTakeFirstOrThrow();
  }

  async countBySerialCodeId(
    serialCodeId: string,
    trx?: Transaction<Database>
  ): Promise<number> {
    const result = await (trx ?? db)
      .selectFrom(TABLE_ACCOUNT_SERIAL_CODES)
      .select(db.fn.count('account_serial_code_id').as('count'))
      .where('serial_code_id', '=', serialCodeId)
      .executeTakeFirst();
    return result ? Number(result.count) : 0;
  }
}
