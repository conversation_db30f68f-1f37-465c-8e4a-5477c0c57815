import { TABLE_NFT_COMPONENTS } from '../constants/database';
import { Database, db } from '../db/database';
import { InsertableNftComponentRow, NftComponentEntity } from '../tables/nftComponentsTable';
import { MultitenantRepository } from './multitenantRepository';
import { Transaction } from 'kysely';

export class NftComponentRepository extends MultitenantRepository {
  async insertNftComponents(
    serviceId: string,
    data: InsertableNftComponentRow[],
    trx?: Transaction<Database>,
  ): Promise<NftComponentEntity[]> {
    await this.setServiceId(serviceId);
    return await (trx ?? db).insertInto(TABLE_NFT_COMPONENTS).values(data).returningAll().execute();
  }
}
