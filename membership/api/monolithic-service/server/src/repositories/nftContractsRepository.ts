// nftContractsRepository.ts
import { injectable } from 'tsyringe';
import { Database, db } from '../db/database';
import { MultitenantRepository } from './multitenantRepository';

import { InsertableNftContractsRow, NftContractsEntity } from '../tables/nftContractsTable';
import { TABLE_NFT_CONTRACT_TYPES, TABLE_NFT_CONTRACTS } from '../constants/database';
import { NftType } from '../enum/nftType';
import { Transaction } from 'kysely';

export interface NftContract {
  nft_contract_id: string;
  service_id: string;
  nft_contract_type_id?: string;
  nft_collection_name?: string;
  nft_contract_address?: string;
  nft_contract_implementation_address?: string;
  nft_contract_abi?: object;
  next_token_id?: number;
  delivery_image_url?: string;
}

export type NftContractJoinNftContractType = {
  nft_contract_id: string;
  service_id: string;
  nft_contract_type_id: string | undefined;
  nft_collection_name: string | undefined;
  nft_contract_address: string | undefined;
  nft_contract_type_name: string;
  nft_contract_type_detail: string;
  nft_contract_abi: object;
  nft_contract_binary: string;
  nft_type: NftType;
  next_token_id?: number;
  delivery_image_url?: string;
};

@injectable()
export class NftContractsRepository extends MultitenantRepository {
  async getNftContractsByIds(
    serviceId: string,
    nftContractIds: string[], // Updated to array
  ): Promise<NftContractsEntity[]> {
    await this.setServiceId(serviceId);
    return await db
      .selectFrom(TABLE_NFT_CONTRACTS)
      .selectAll()
      .where('nft_contract_id', 'in', nftContractIds) // Using `in` for array of IDs
      .execute();
  }

  async selectNftContractByContractAddress(
    contractAddress: string,
    serviceId: string,
    dbTx?: Transaction<Database>,
  ): Promise<NftContractJoinNftContractType | undefined> {
    await this.setServiceId(serviceId);
    const result = await (dbTx ?? db)
      .selectFrom(TABLE_NFT_CONTRACTS)
      .innerJoin(
        TABLE_NFT_CONTRACT_TYPES,
        `${TABLE_NFT_CONTRACTS}.nft_contract_type_id`,
        `${TABLE_NFT_CONTRACT_TYPES}.nft_contract_type_id`,
      )
      .where(`${TABLE_NFT_CONTRACTS}.nft_contract_address`, '=', contractAddress)
      .selectAll()
      .executeTakeFirst();
    return result;
  }

  async getNftContractsByIdWithLock(
    nftContractId: string,
    dbTx: Transaction<Database>,
  ): Promise<NftContract | undefined> {
    return await dbTx
      .selectFrom(TABLE_NFT_CONTRACTS)
      .selectAll()
      .where('nft_contract_id', '=', nftContractId)
      .forUpdate()
      .executeTakeFirst();
  }

  async selectErc721Contracts(serviceId: string): Promise<{
    nftContractId: string;
    nftContractAddress: string | undefined;
    nextTokenId?: number;
    nftType: NftType | null;
  }[]> {
    await this.setServiceId(serviceId);
    return await db
      .selectFrom(TABLE_NFT_CONTRACTS)
      .leftJoin(
        TABLE_NFT_CONTRACT_TYPES,
        `${TABLE_NFT_CONTRACTS}.nft_contract_type_id`,
        `${TABLE_NFT_CONTRACT_TYPES}.nft_contract_type_id`,
      )
      .select([
        `${TABLE_NFT_CONTRACTS}.nft_contract_id as nftContractId`,
        `${TABLE_NFT_CONTRACTS}.nft_contract_address as nftContractAddress`,
        `${TABLE_NFT_CONTRACTS}.next_token_id as nextTokenId`,
        `${TABLE_NFT_CONTRACT_TYPES}.nft_type as nftType`,
      ])
      .where('nft_contract_implementation_address', 'is', null)
      .execute();
  }

  async selectNftContractById(nftContractId: string, dbTx?: Transaction<Database>): Promise<NftContract | undefined> {
    return (dbTx ?? db)
      .selectFrom(TABLE_NFT_CONTRACTS)
      .innerJoin(
        TABLE_NFT_CONTRACT_TYPES,
        'nft_contracts.nft_contract_type_id',
        'nft_contract_types.nft_contract_type_id',
      )
      .select([
        'nft_contracts.nft_contract_id',
        'nft_contracts.service_id',
        'nft_contracts.nft_contract_type_id',
        'nft_contracts.nft_collection_name',
        'nft_contracts.nft_contract_address',
        'nft_contracts.nft_contract_implementation_address',
        'nft_contract_types.nft_contract_abi',
        'nft_contracts.next_token_id',
        'nft_contracts.delivery_image_url',
      ])
      .where('nft_contracts.nft_contract_id', '=', nftContractId)
      .executeTakeFirst();
  }

  async insertNftContract(nftContract: InsertableNftContractsRow, dbTx?: Transaction<Database>): Promise<void> {
    await this.setServiceId(nftContract.service_id);
    await (dbTx ?? db).insertInto(TABLE_NFT_CONTRACTS).values(nftContract).executeTakeFirstOrThrow();
  }

  async selectNftContractAddressAndNftContractAbiUrl(
    contractAddress: string,
    serviceId: string,
  ): Promise<NftContractJoinNftContractType> {
    await this.setServiceId(serviceId);
    const result = await db
      .selectFrom('nft_contracts')
      .innerJoin(
        TABLE_NFT_CONTRACT_TYPES,
        'nft_contracts.nft_contract_type_id',
        'nft_contract_types.nft_contract_type_id',
      )
      .where('nft_contracts.nft_contract_address', 'ilike', contractAddress)
      .selectAll()
      .executeTakeFirstOrThrow();

    return result;
  }

  async updateContractTokenId(
    tokenIds: { contractId: string; tokenId: number }[],
    dbTx?: Transaction<Database>,
  ): Promise<void> {
    for (const { contractId, tokenId } of tokenIds) {
      await (dbTx ?? db)
        .updateTable('nft_contracts')
        .set({ next_token_id: tokenId })
        .where('nft_contract_id', '=', contractId)
        .execute();
    }
  }

  async selectNftContractIdAndNftContractAbiUrl(
    contractId: string,
    serviceId: string,
  ): Promise<NftContractJoinNftContractType> {
    await this.setServiceId(serviceId);
    const result = await db
      .selectFrom('nft_contracts')
      .innerJoin(
        TABLE_NFT_CONTRACT_TYPES,
        'nft_contracts.nft_contract_type_id',
        'nft_contract_types.nft_contract_type_id',
      )
      .where('nft_contracts.nft_contract_id', '=', contractId)
      .selectAll()
      .executeTakeFirstOrThrow();

    return result;
  }

  async selectContractIdsByType(nftType: NftType, serviceId: string): Promise<NftContract[]> {
    await this.setServiceId(serviceId);
    return await db
      .selectFrom('nft_contracts')
      .innerJoin(
        TABLE_NFT_CONTRACT_TYPES,
        'nft_contracts.nft_contract_type_id',
        'nft_contract_types.nft_contract_type_id',
      )
      .select([
        'nft_contracts.nft_contract_id',
        'nft_contracts.service_id',
        'nft_contracts.nft_contract_type_id',
        'nft_contracts.nft_collection_name',
        'nft_contracts.nft_contract_address',
        'nft_contracts.nft_contract_implementation_address',
        'nft_contract_types.nft_contract_abi',
      ])
      .where('nft_contracts.service_id', '=', serviceId)
      .where('nft_contract_types.nft_type', '=', nftType)
      .execute();
  }

  async selectNftContractAndTypeByAddress(contractAddress: string): Promise<NftContractJoinNftContractType> {
    const result = await db
      .selectFrom('nft_contracts')
      .innerJoin(
        TABLE_NFT_CONTRACT_TYPES,
        'nft_contracts.nft_contract_type_id',
        'nft_contract_types.nft_contract_type_id',
      )
      .where('nft_contracts.nft_contract_address', 'ilike', contractAddress)
      .selectAll()
      .executeTakeFirstOrThrow();

    return result;
  }

  async selectNftTypeByAddress(contractAddress: string): Promise<NftType | undefined> {
    const result = await db
      .selectFrom('nft_contracts')
      .innerJoin(
        TABLE_NFT_CONTRACT_TYPES,
        'nft_contracts.nft_contract_type_id',
        'nft_contract_types.nft_contract_type_id',
      )
      .where('nft_contracts.nft_contract_address', 'ilike', contractAddress)
      .select(['nft_contract_types.nft_type'])
      .executeTakeFirstOrThrow();

    return result.nft_type;
  }

  async updateNextTokenId(nftContractId: string, nextTokenId: number, dbTx?: Transaction<Database>): Promise<void> {
    await (dbTx ?? db)
      .updateTable(TABLE_NFT_CONTRACTS)
      .set({ next_token_id: nextTokenId })
      .where('nft_contract_id', '=', nftContractId)
      .execute();
  }

  async selectDeliveryImageUrl(contractAddress: string): Promise<string | undefined> {
    const result = await db
      .selectFrom(TABLE_NFT_CONTRACTS)
      .select(['delivery_image_url'])
      .where('nft_contract_address', '=', contractAddress)
      .executeTakeFirst();
    return result?.delivery_image_url;
  }
}
