import { injectable } from 'tsyringe';
import { db } from '../db/database';
import { MultitenantRepository } from './multitenantRepository';
import { 
  InsertableLocationCheckinActionRow, 
  LocationCheckinActionEntity 
} from '../tables/locationCheckinActionTable';

@injectable()
export class LocationCheckinActionRepository extends MultitenantRepository {
  
  /**
   * Get location checkin action by action ID
   */
  async getLocationCheckinAction(actionId: string, serviceId: string): Promise<LocationCheckinActionEntity | undefined> {
    await this.setServiceId(serviceId);
    
    return await db
      .selectFrom('location_checkin_actions')
      .selectAll()
      .where('action_id', '=', actionId)
      .executeTakeFirst();
  }

  /**
   * Insert location checkin action
   */
  async insertLocationCheckinAction(serviceId: string, data: InsertableLocationCheckinActionRow): Promise<LocationCheckinActionEntity> {
    await this.setServiceId(serviceId);
    
    return await db
      .insertInto('location_checkin_actions')
      .values(data)
      .returningAll()
      .executeTakeFirstOrThrow();
  }

  /**
   * Get geofence ID for a location checkin action
   */
  async getGeofenceIdByActionId(actionId: string, serviceId: string): Promise<string | undefined> {
    await this.setServiceId(serviceId);
    
    const result = await db
      .selectFrom('location_checkin_actions')
      .select('geofence_id')
      .where('action_id', '=', actionId)
      .executeTakeFirst();
    
    return result?.geofence_id;
  }
}
