import { injectable } from 'tsyringe';
import { db } from '../db/database';
import { MultitenantRepository } from './multitenantRepository';
import { RewardType } from '../enum/rewardType';
import { RewardUsageStatus } from '../enum/rewardUsageStatus';
import { ClaimedRewardEntity, InsertableClaimedRewardRow } from '../tables/claimedRewardTable';
import { TABLE_REWARD_TRANSLATIONS, TABLE_REWARDS } from '../constants/database';
import { LanguageCode } from '../enum/languageCode';
import { makeTranslationJoin, distinctByIdAndLang } from '../utils/i18n';

export interface RewardInfo {
  reward_id: string;
  reward_title: string;
  reward_description: string;
  reward_thumbnail_image_url: string;
  reward_usage_status: string;
  claim_date: Date | undefined;
}
@injectable()
export class ClaimedRewardRepository extends MultitenantRepository {
  async insertClaimedReward(claimedReward: InsertableClaimedRewardRow): Promise<ClaimedRewardEntity> {
    await this.setServiceId(claimedReward.service_id);

    return await db.insertInto('claimed_rewards').values(claimedReward).returningAll().executeTakeFirstOrThrow();
  }

  async countClaimedRewardsByAccountId(accountId: string, serviceId: string): Promise<number> {
    await this.setServiceId(serviceId);
    const result = await db
      .selectFrom('claimed_rewards')
      .select(db.fn.count('reward_id').as('count'))
      .where('account_id', '=', accountId)
      .executeTakeFirst();
    return result ? Number(result.count) : 0;
  }

  async countRewardsByAccountIdAndStatus(
    accountId: string,
    serviceId: string,
    rewardType: RewardType,
    rewardUsageStatus: RewardUsageStatus,
  ): Promise<number> {
    await this.setServiceId(serviceId);
    const result = await db
      .selectFrom('claimed_rewards')
      .innerJoin('rewards', 'rewards.reward_id', 'claimed_rewards.reward_id')
      .select(db.fn.count('claimed_rewards.reward_id').as('count'))
      .where('claimed_rewards.account_id', '=', accountId)
      .where('rewards.reward_type', '=', rewardType)
      .where('claimed_rewards.reward_usage_status', '=', rewardUsageStatus)
      .executeTakeFirst();
    return result ? Number(result.count) : 0;
  }

  async selectClaimedRewards(accountId: string, serviceId: string, lang: LanguageCode): Promise<RewardInfo[]> {
    await this.setServiceId(serviceId);
    return await db
      .selectFrom('accounts')
      .innerJoin('claimed_rewards', 'accounts.account_id', 'claimed_rewards.account_id')
      .innerJoin('rewards', 'rewards.reward_id', 'claimed_rewards.reward_id')
      .innerJoin(TABLE_REWARD_TRANSLATIONS, makeTranslationJoin(TABLE_REWARDS, TABLE_REWARD_TRANSLATIONS, 'reward_id', lang))
      .where('accounts.account_id', '=', accountId)
      .where('claimed_rewards.reward_usage_status', 'in', [RewardUsageStatus.ACTIVE, RewardUsageStatus.USED])
      .select([
        'rewards.reward_id',
        'reward_translations.reward_title',
        'reward_translations.reward_description',
        'rewards.reward_thumbnail_image_url',
        'claimed_rewards.reward_usage_status',
        'claimed_rewards.claim_date',
      ])
      .$call(distinctByIdAndLang('reward_translations.reward_id', 'reward_translations.language', lang))
      .execute();
  }

  async selectClaimedRewardByAccountId(accountId: string, serviceId: string): Promise<ClaimedRewardEntity[]> {
    await this.setServiceId(serviceId);

    const result = await db
      .selectFrom('claimed_rewards')
      .where('claimed_rewards.account_id', '=', accountId)
      .selectAll()
      .execute();

    return result;
  }

  async selectClaimedRewardById(
    accountId: string,
    rewardId: string,
    serviceId: string,
  ): Promise<ClaimedRewardEntity | undefined> {
    await this.setServiceId(serviceId);

    return await db
      .selectFrom('claimed_rewards')
      .where('claimed_rewards.account_id', '=', accountId)
      .where('claimed_rewards.reward_id', '=', rewardId)
      .selectAll()
      .executeTakeFirst();
  }

  async selectAllClaimedRewardByClearedQuests(
    accountId: string,
    questId: string,
    serviceId: string,
  ): Promise<string[]> {
    await this.setServiceId(serviceId);

    const claimedRewards = await db
      .selectFrom('quest_rewards as qr')
      .leftJoin('claimed_rewards as cr', 'cr.reward_id', 'qr.reward_id')
      .where('qr.quest_id', '=', questId)
      .where('cr.account_id', '=', accountId)
      .where('qr.quest_id', 'in', (qb) =>
        qb
          .selectFrom('claimed_rewards')
          .leftJoin('quest_rewards', 'claimed_rewards.reward_id', 'quest_rewards.reward_id')
          .where('claimed_rewards.account_id', '=', accountId)
          .select('quest_rewards.quest_id'),
      )

      .select('qr.reward_id')
      .execute();

    return claimedRewards.map((reward) => reward.reward_id).filter((id) => id != null);
  }

  async selectClaimedRewardByTransactionId(transactionId: string):Promise<ClaimedRewardEntity | undefined> {
    return await db
      .selectFrom('claimed_rewards')
      .where('transaction_id', '=', transactionId)
      .selectAll()
      .executeTakeFirst();
  }
}
