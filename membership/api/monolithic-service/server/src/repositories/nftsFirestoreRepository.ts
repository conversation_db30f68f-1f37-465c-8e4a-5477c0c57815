import type FirebaseFirestore from '@google-cloud/firestore';
import { QueryDocumentSnapshot } from 'firebase-admin/firestore';
import { inject, injectable } from 'tsyringe';
import { FirebaseComponent } from '../components/firebaseComponent';
import { NftType } from '../enum/nftType';
import { TransferTxType } from '../enum/transferTxType';
import { FailedTokenIdsWithMsg } from '../services/adminService';
import { logger } from '../utils/logger';
import { retryExecution } from '../utils/retry';

export interface UpdateNftMetadata {
  tokenId: string;
  metadataJson: object;
  metadataUri: string;
}

export interface FirestoreNftsDocument {
  contractAddress: string;
  tokenId: string;
  amount: number;
  accountId: string;
  serviceId: string;
  contractType: NftType;
  metadataJson: object;
  metadataUri: string;
  metadataCachedAt: string;
  updatedAt: string;
  createdAt: string;
  transactions: {
    [transactionHash: string]: {
      from: string;
      to: string;
      transactionDate: string;
      transactionType: TransferTxType;
      isConfirmed: boolean;
    };
  };
}

@injectable()
export class NftsFirestoreRepository {
  private db: FirebaseFirestore.Firestore;
  constructor(@inject('FirebaseComponent') private firebaseComponent: FirebaseComponent) {
    this.db = firebaseComponent.getFirestoreInstance();
  }

  public async selectNft(contractAddress: string, tokenId: string) {
    return this.db
      .collection('nfts')
      .where('contract_address', '==', contractAddress)
      .where('token_id', '==', tokenId)
      .get();
  }

  public async selectNftByAccountId(accountId: string, contractAddress: string, tokenId: string) {
    return this.db
      .collection('nfts')
      .where('account_id', '==', accountId)
      .where('contract_address', '==', contractAddress)
      .where('token_id', '==', tokenId)
      .get();
  }

  public async selectNftsByTxHash(transactionHash: string) {
    return this.db.collection('nfts').where(`transactions.${transactionHash}.isConfirmed`, '==', false).get();
  }

  public async insertNft(nftData: FirestoreNftsDocument): Promise<FirebaseFirestore.WriteResult> {
    const nftSnapshot = await this.selectNftByAccountId(nftData.accountId, nftData.contractAddress, nftData.tokenId);
    if (!nftSnapshot.empty) {
      const result = await this.updateExistingNft(nftSnapshot.docs[0].ref, nftData);
      return result;
    } else {
      const result = await this.createNewNft(nftData);
      return result;
    }
  }

  private async updateExistingNft(
    docRef: FirebaseFirestore.DocumentReference,
    nftData: FirestoreNftsDocument,
  ): Promise<FirebaseFirestore.WriteResult> {
    const updatePayload = {
      amount: nftData.amount,
      updated_at: nftData.updatedAt,
      transactions: nftData.transactions,
    };
    const result = await docRef.update(updatePayload);
    return result;
  }

  private async createNewNft(nftData: FirestoreNftsDocument): Promise<FirebaseFirestore.WriteResult> {
    const nftDocRef = this.db.collection('nfts').doc();
    const nftDocument = {
      contract_address: nftData.contractAddress,
      token_id: nftData.tokenId,
      amount: nftData.amount,
      account_id: nftData.accountId,
      service_id: nftData.serviceId,
      contract_type: nftData.contractType,
      metadata_json: nftData.metadataJson,
      metadata_uri: nftData.metadataUri,
      metadata_cached_at: nftData.metadataCachedAt,
      updated_at: nftData.updatedAt,
      created_at: nftData.createdAt,
      transactions: nftData.transactions,
    };

    const result = await nftDocRef.set(nftDocument);
    return result;
  }

  public async updateTransactionConfirmation(transactionHash: string, isConfirmed: boolean): Promise<void> {
    try {
      const nftDocs = await this.selectNftsByTxHash(transactionHash);
      for (const nftDoc of nftDocs.docs) {
        const transactions = nftDoc.data()?.transactions || {};
        if (!transactions[transactionHash]) {
          logger.error({
            method: 'updateTransactionConfirmation',
            txHash: transactionHash,
            error: 'Transaction not found',
          });
          continue;
        }
        transactions[transactionHash] = {
          ...transactions[transactionHash],
          isConfirmed,
        };
        await this.firebaseComponent.updateDocument('nfts', nftDoc.id, {
          transactions,
          updated_at: new Date().toISOString(),
        });
      }
    } catch (error) {
      logger.error('Error while updating transaction confirmation:', error);
    }
  }

  public async deleteNft(contractAddress: string, tokenId: string): Promise<FirebaseFirestore.WriteResult | void> {
    const nftDocRef = await this.db
      .collection('nfts')
      .where('contract_address', '==', contractAddress)
      .where('token_id', '==', tokenId)
      .get();
    if (nftDocRef.empty) {
      return;
    }
    const result = await nftDocRef.docs[0].ref.delete();
    return result;
  }

  public async updateNftsMetadata(
    contractAddress: string,
    metadata: UpdateNftMetadata,
  ): Promise<FirebaseFirestore.WriteResult[]> {
    const snapshot = await this.db
      .collection('nfts')
      .where('contract_address', '==', contractAddress)
      .where('token_id', '==', metadata.tokenId)
      .get();
    if (snapshot.empty) return [];

    const nowISO = new Date().toISOString();
    const batch = this.db.batch();

    snapshot.forEach((doc) => {
      batch.update(doc.ref, {
        metadata_uri: metadata.metadataUri,
        metadata_cached_at: nowISO,
        metadata_json: metadata.metadataJson,
        updated_at: nowISO,
      });
    });
    const retryHandler = (error: unknown) => {
      if (error instanceof Error) {
        logger.error(`Error in batch commit: ${error.message}`);
      } else {
        logger.error('Unknown error in batch commit');
      }
      return true;
    };
    const batchCommitFunc = async () => {
      return await batch.commit();
    };
    return await retryExecution<FirebaseFirestore.WriteResult[]>(batchCommitFunc, {
      retries: 3,
      retryDelay: 200,
      retryHandler,
    });
  }

  private async processBatch(
    docs: QueryDocumentSnapshot[],
    metadataMap: Map<string, UpdateNftMetadata>,
  ): Promise<number> {
    const batch = this.db.batch();
    const nowISO = new Date().toISOString();
    let updateCount = 0;

    for (const doc of docs) {
      const tokenId = doc.data().token_id;
      const metadata = metadataMap.get(tokenId);
      if (!metadata) continue;

      batch.update(doc.ref, {
        metadata_json: metadata.metadataJson,
        metadata_uri: metadata.metadataUri,
        metadata_cached_at: nowISO,
        updated_at: nowISO,
      });
      updateCount++;
    }

    if (updateCount > 0) {
      const retryHandler = (error: unknown) => {
        if (error instanceof Error) {
          logger.error(`Error in batch commit: ${error.message}`);
        } else {
          logger.error('Unknown error in batch commit');
        }
        return true;
      };
      const batchCommitFunc = async () => {
        return await batch.commit();
      };
      try {
        await retryExecution(batchCommitFunc, {
          retries: 3,
          retryDelay: 1000,
          retryHandler,
        });
      } catch (error) {
        logger.error('Batch commit failed after retries', error);
        throw error;
      }
    }
    return updateCount;
  }

  public async batchUpdateNftsMetadata(
    contractAddress: string,
    metadata: UpdateNftMetadata[],
    failedIds?: FailedTokenIdsWithMsg[],
  ): Promise<{ totalUpdated: number }> {
    const metadataMap = new Map(metadata.map((m) => [m.tokenId, m]));

    const snapshot = await this.db.collection('nfts').where('contract_address', '==', contractAddress).get();
    if (snapshot.empty) {
      return { totalUpdated: 0 };
    }
    let totalUpdated = 0;
    try {
      totalUpdated = await this.processBatch(snapshot.docs, metadataMap);
    } catch (error) {
      logger.error('Batch processing failed', error);
      if (failedIds) {
        for (const doc of snapshot.docs) {
          const tokenId = doc.data().token_id;
          failedIds.push({ tokenId, errorMsg: 'Failed to update Firestore' });
        }
      }
    }
    return { totalUpdated };
  }
}
