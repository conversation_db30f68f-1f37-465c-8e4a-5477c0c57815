import { TABLE_REWARD_COMPONENTS } from '../constants/database';
import { db } from '../db/database';
import { InsertableRewardComponentRow, RewardComponentEntity } from '../tables/rewardComponentsTable';
import { MultitenantRepository } from './multitenantRepository';
import { Transaction } from 'kysely';
import { Database } from '../db/database';

export class RewardComponentRepository extends MultitenantRepository {
  async insertRewardComponents(
    serviceId: string,
    data: InsertableRewardComponentRow[],
    trx?: Transaction<Database>,
  ): Promise<RewardComponentEntity[]> {
    await this.setServiceId(serviceId);
    return await (trx ?? db).insertInto(TABLE_REWARD_COMPONENTS).values(data).returningAll().execute();
  }
}
