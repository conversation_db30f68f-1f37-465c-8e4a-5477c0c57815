import { injectable } from 'tsyringe';
import { Database, db } from '../db/database';
import { AccountEntity, InsertableAccountRow } from '../tables/accountTable';
import { MultitenantRepository } from './multitenantRepository';
import { AccountStatus } from '../enum/accoutStatus';
import { NotFoundError } from '../errors/notFoundError';
import { Transaction } from 'kysely';

@injectable()
export class AccountRepository extends MultitenantRepository {
  async insertAccount(account: InsertableAccountRow, dbTx?: Transaction<Database>): Promise<AccountEntity> {
    await this.setServiceId(account.service_id);
    return await (dbTx ?? db).insertInto('accounts').values(account).returningAll().executeTakeFirstOrThrow();
  }

  async updateAccountMembershipNftImageUrl(accountId: string, serviceId: string, nftImageUrl: string): Promise<void> {
    await this.setServiceId(serviceId);

    await db
      .updateTable('accounts')
      .set({ membership_metadata_url: nftImageUrl, updated_at: new Date() })
      .where('account_id', '=', accountId)
      .execute();
  }

  async selectAccountByUserId(userId: string, serviceId: string): Promise<AccountEntity | undefined> {
    await this.setServiceId(serviceId);
    return await db.selectFrom('accounts').selectAll().where('user_id', '=', userId).executeTakeFirst();
  }

  async selectAccountById(accountId: string, serviceId: string): Promise<AccountEntity | undefined> {
    await this.setServiceId(serviceId);
    return await db.selectFrom('accounts').selectAll().where('account_id', '=', accountId).executeTakeFirst();
  }

  async selectAccountIdByAddress(address: string, serviceId: string): Promise<AccountEntity | undefined> {
    await this.setServiceId(serviceId);
    return await db
      .selectFrom('accounts')
      .selectAll()
      .where('token_bound_account_address', '=', address)
      .executeTakeFirst();
  }

  async selectAccountByTokenBoundAddress(address: string, serviceId: string, dbTx?: Transaction<Database>): Promise<AccountEntity | undefined> {
    await this.setServiceId(serviceId, dbTx);
    return await (dbTx ?? db)
      .selectFrom('accounts')
      .selectAll()
      .where('token_bound_account_address', 'ilike', address)
      .executeTakeFirst();
  }

  async selectAccountByTokenBoundAddresses(addresses: string[], serviceId: string, dbTx?: Transaction<Database>): Promise<AccountEntity[] | undefined> {
    await this.setServiceId(serviceId, dbTx);
    return await (dbTx ?? db)
      .selectFrom('accounts')
      .selectAll()
      .where('token_bound_account_address', 'in', addresses)
      .execute();
  }

  async updateAccountState(accountId: string, status: AccountStatus, serviceId: string): Promise<void> {
    await this.setServiceId(serviceId);
    await db
      .updateTable('accounts')
      .set({ status: status, updated_at: new Date() })
      .where('account_id', '=', accountId)
      .execute();
  }

  async updateUserLineProfile(
    accountId: string,
    serviceId: string,
    displayName?: string,
    profileImageUrl?: string,
  ): Promise<void> {
    await this.setServiceId(serviceId);
    await db
      .updateTable('accounts')
      .set({
        display_name: displayName,
        profile_image_url: profileImageUrl,
        updated_at: new Date(),
      })
      .where('account_id', '=', accountId)
      .execute();
  }

  async selectTokenBoundAccountAddress(accountId: string, serviceId: string): Promise<string> {
    await this.setServiceId(serviceId);
    const result = await db
      .selectFrom('accounts')
      .select('token_bound_account_address')
      .where('account_id', '=', accountId)
      .executeTakeFirstOrThrow();

    if (!result.token_bound_account_address) throw new NotFoundError();

    return result.token_bound_account_address;
  }

  async updateAccountTBA(
    dbTx: Transaction<Database>,
    queueId: string,
    serviceId: string,
    tokenBoundAccountAddress: string,
    membershipId: number,
    membershipMetadataUrl: string,
  ): Promise<void> {
    await this.setServiceId(serviceId, dbTx);
    await dbTx
      .updateTable('accounts')
      .set({
        token_bound_account_address: tokenBoundAccountAddress,
        membership_id: membershipId,
        membership_metadata_url: membershipMetadataUrl,
        updated_at: new Date(),
      })
      .where('queue_id', '=', queueId)
      .executeTakeFirstOrThrow();
  }

  async updateAccountTBAFromId(
    dbTx: Transaction<Database>,
    accountId: string,
    serviceId: string,
    tokenBoundAccountAddress: string,
    membershipId: number,
    membershipMetadataUrl: string,
  ): Promise<void> {
    await this.setServiceId(serviceId, dbTx);
    await dbTx
      .updateTable('accounts')
      .set({
        token_bound_account_address: tokenBoundAccountAddress,
        membership_id: membershipId,
        membership_metadata_url: membershipMetadataUrl,
        updated_at: new Date(),
      })
      .where('account_id', '=', accountId)
      .executeTakeFirstOrThrow();
  }

  async getAccountFromQueueId(
    queueIds: string[],
    serviceId: string,
    trx?: Transaction<Database>,
  ): Promise<{account_id: string, queue_id: string}[]> {
    await this.setServiceId(serviceId);
    return await (trx ?? db)
      .selectFrom('accounts')
      .select([
      'account_id',
      'queue_id',
    ])
    .where('queue_id', 'in', queueIds)
    .execute();
  }

  async updateLastLogin(accountId: string, serviceId: string): Promise<void> {
    await this.setServiceId(serviceId);
    await db
      .updateTable('accounts')
      .set({ last_login_at: new Date() })
      .where('account_id', '=', accountId)
      .execute();
  }
}
