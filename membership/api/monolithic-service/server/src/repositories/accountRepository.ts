import { injectable } from 'tsyringe';
import { Database, db } from '../db/database';
import { AccountEntity, InsertableAccountRow } from '../tables/accountTable';
import { MultitenantRepository } from './multitenantRepository';
import { AccountStatus } from '../enum/accoutStatus';
import { GlobalNotificationsEntity } from '../tables/globalNotifications';
import { AccountNotificationsEntity } from '../tables/accountNotifications';
import { NotFoundError } from '../errors/notFoundError';
import { Transaction, sql } from 'kysely';
import { v4 as uuidv4 } from 'uuid';
import { 
  TABLE_ACCOUNT_CUSTOM_FIELD_VALUES,
  TABLE_SERVICE_CUSTOM_FIELDS,
  TABLE_SERVICE_CUSTOM_FIELD_TRANSLATIONS,
  TABLE_SERVICE_CUSTOM_FIELD_OPTIONS,
  TABLE_SERVICE_CUSTOM_FIELD_OPTION_TRANSLATIONS
 } from '../constants/database';
import { LanguageCode } from '../enum/languageCode';
import { ServiceCustomFieldResult, ServiceCustomFieldOptionResult } from './servicesCustomFieldsRepository';

interface AccountCustomFieldUpdateType {
  fieldKey: string;
  customFieldId: string;
  value: string;
}

export type AccountCustomFieldType = Pick<
  ServiceCustomFieldResult,
  Exclude<keyof ServiceCustomFieldResult, "validator">
> & {
  values: string[];
}


@injectable()
export class AccountRepository extends MultitenantRepository {
  async insertAccount(account: InsertableAccountRow): Promise<AccountEntity> {
    await this.setServiceId(account.service_id);
    return await db.insertInto('accounts').values(account).returningAll().executeTakeFirstOrThrow();
  }

  async selectAccountByUserId(userId: string, serviceId: string): Promise<AccountEntity | undefined> {
    await this.setServiceId(serviceId);
    return await db.selectFrom('accounts').selectAll().where('user_id', '=', userId).executeTakeFirst();
  }

  async selectAccountById(accountId: string, serviceId: string): Promise<AccountEntity | undefined> {
    await this.setServiceId(serviceId);
    return await db.selectFrom('accounts').selectAll().where('account_id', '=', accountId).executeTakeFirst();
  }

  async selectAccountIdByAddress(address: string, serviceId: string): Promise<AccountEntity | undefined> {
    await this.setServiceId(serviceId);
    return await db
      .selectFrom('accounts')
      .selectAll()
      .where('token_bound_account_address', '=', address)
      .executeTakeFirst();
  }

  async selectAccountByTokenBoundAddress(address: string, serviceId: string, dbTx?: Transaction<Database>): Promise<AccountEntity | undefined> {
    await this.setServiceId(serviceId, dbTx);
    return await (dbTx ?? db)
      .selectFrom('accounts')
      .selectAll()
      .where('token_bound_account_address', 'ilike', address)
      .executeTakeFirst();
  }

  async selectAccountByTokenBoundAddresses(addresses: string[], serviceId: string, dbTx?: Transaction<Database>): Promise<AccountEntity[] | undefined> {
    await this.setServiceId(serviceId, dbTx);
    return await (dbTx ?? db)
      .selectFrom('accounts')
      .selectAll()
      .where('token_bound_account_address', 'in', addresses)
      .execute();
  }

  async updateAccountState(accountId: string, status: AccountStatus, serviceId: string): Promise<void> {
    await this.setServiceId(serviceId);
    await db
      .updateTable('accounts')
      .set({ status: status, updated_at: new Date() })
      .where('account_id', '=', accountId)
      .execute();
  }

  async updateUserLineProfile(
    accountId: string,
    serviceId: string,
    displayName?: string,
    profileImageUrl?: string,
  ): Promise<void> {
    await this.setServiceId(serviceId);
    await db
      .updateTable('accounts')
      .set({
        display_name: displayName,
        profile_image_url: profileImageUrl,
        updated_at: new Date(),
      })
      .where('account_id', '=', accountId)
      .execute();
  }

  async selectTokenBoundAccountAddress(accountId: string, serviceId: string): Promise<string> {
    await this.setServiceId(serviceId);
    const result = await db
      .selectFrom('accounts')
      .select('token_bound_account_address')
      .where('account_id', '=', accountId)
      .executeTakeFirstOrThrow();

    if (!result.token_bound_account_address) throw new NotFoundError();

    return result.token_bound_account_address;
  }

  async updateAccountTBA(
    dbTx: Transaction<Database>,
    queueId: string,
    serviceId: string,
    tokenBoundAccountAddress: string,
    membershipId: number,
  ): Promise<void> {
    await this.setServiceId(serviceId, dbTx);
    await dbTx
      .updateTable('accounts')
      .set({
        token_bound_account_address: tokenBoundAccountAddress,
        membership_id: membershipId,
        updated_at: new Date(),
      })
      .where('queue_id', '=', queueId)
      .executeTakeFirstOrThrow();
  }

  async updateAccountTBAFromId(
    dbTx: Transaction<Database>,
    accountId: string,
    serviceId: string,
    tokenBoundAccountAddress: string,
    membershipId: number,
  ): Promise<void> {
    await this.setServiceId(serviceId, dbTx);
    await dbTx
      .updateTable('accounts')
      .set({
        token_bound_account_address: tokenBoundAccountAddress,
        membership_id: membershipId,
        updated_at: new Date(),
      })
      .where('account_id', '=', accountId)
      .executeTakeFirstOrThrow();
  }

  async getAccountFromQueueId(
    queueIds: string[],
    serviceId: string,
    trx?: Transaction<Database>,
  ): Promise<{account_id: string, queue_id: string}[]> {
    await this.setServiceId(serviceId);
    return await (trx ?? db)
      .selectFrom('accounts')
      .select([
      'account_id',
      'queue_id',
    ])
    .where('queue_id', 'in', queueIds)
    .execute();
  }

  async createOrUpdateAccountCustomField(accountId: string, serviceId: string, customFieldValues: AccountCustomFieldUpdateType[]): Promise<void> {
    await this.setServiceId(serviceId);

    await db.transaction().execute(async (trx) => {
      const fieldGroups = new Map<string, AccountCustomFieldUpdateType[]>();
      for (const fieldValue of customFieldValues) {
        if (!fieldGroups.has(fieldValue.customFieldId)) {
          fieldGroups.set(fieldValue.customFieldId, []);
        }
        fieldGroups.get(fieldValue.customFieldId)?.push(fieldValue);
      }

      if (customFieldValues.length < 1) return;

      await Promise.all(
        Array.from(fieldGroups.entries()).map(async ([customFieldId, values]) => {
          const existingValues = await trx
            .selectFrom(TABLE_ACCOUNT_CUSTOM_FIELD_VALUES)
            .select(['custom_field_value_id'])
            .where('account_id', '=', accountId)
            .where('custom_field_id', '=', customFieldId)
            .execute();

          if (existingValues.length <= 1 && values.length <= 1) {
            const custom_field_value_id = existingValues[0]?.custom_field_value_id ?? uuidv4();

            await trx
              .insertInto(TABLE_ACCOUNT_CUSTOM_FIELD_VALUES)
              .values({
                custom_field_value_id,
                service_id: serviceId,
                custom_field_id: customFieldId,
                account_id: accountId,
                value: values[0].value,
                verified: false,
                created_at: new Date(),
              })
              .onConflict((oc) =>
                oc.column('custom_field_value_id').doUpdateSet({
                  value: values[0].value,
                })
              )
              .execute();
          } else {
            await trx
              .deleteFrom(TABLE_ACCOUNT_CUSTOM_FIELD_VALUES)
              .where('custom_field_id', '=', customFieldId)
              .where('account_id', '=', accountId)
              .execute();

            await Promise.all(
              values.map((fieldValue) =>
                trx.insertInto(TABLE_ACCOUNT_CUSTOM_FIELD_VALUES)
                  .values({
                    custom_field_value_id: uuidv4(),
                    service_id: serviceId,
                    custom_field_id: customFieldId,
                    account_id: accountId,
                    value: fieldValue.value,
                    verified: false,
                    created_at: new Date(),
                  })
                  .execute()
              )
            );
          }
        })
      );
    });
  }

  async getAccountCustomFieldValues(accountId: string, serviceId: string, lang: LanguageCode): Promise<AccountCustomFieldType[]> {
    await this.setServiceId(serviceId);

    const latestVersion = await db
      .selectFrom(TABLE_SERVICE_CUSTOM_FIELDS)
      .select(db.fn.max('version').as('max_version'))
      .where('service_id', '=', serviceId)
      .executeTakeFirst();

    if (!latestVersion?.max_version) {
      return [];
    }

    return await db
      .selectFrom(TABLE_SERVICE_CUSTOM_FIELDS)
      .leftJoin(
        TABLE_ACCOUNT_CUSTOM_FIELD_VALUES,
        (jb) =>
          jb
            .onRef(
              `${TABLE_ACCOUNT_CUSTOM_FIELD_VALUES}.custom_field_id`,
              '=',
              `${TABLE_SERVICE_CUSTOM_FIELDS}.custom_field_id`
            )
            .on(
              `${TABLE_ACCOUNT_CUSTOM_FIELD_VALUES}.account_id`,
              "=",
              accountId
            )
      )
      .innerJoin(
        TABLE_SERVICE_CUSTOM_FIELD_TRANSLATIONS,
        (jb) =>
          jb
            .onRef(
              `${TABLE_SERVICE_CUSTOM_FIELD_TRANSLATIONS}.custom_field_id`,
              "=",
              `${TABLE_SERVICE_CUSTOM_FIELDS}.custom_field_id`
            )
            .on(
              `${TABLE_SERVICE_CUSTOM_FIELD_TRANSLATIONS}.locale`,
              "=",
              sql`${lang}`
            )
      )
      .leftJoin(TABLE_SERVICE_CUSTOM_FIELD_OPTIONS, `${TABLE_SERVICE_CUSTOM_FIELD_OPTIONS}.custom_field_id`, `${TABLE_ACCOUNT_CUSTOM_FIELD_VALUES}.custom_field_id`)
      .leftJoin(
        TABLE_SERVICE_CUSTOM_FIELD_OPTION_TRANSLATIONS,
        (jb) =>
          jb
            .onRef(
              `${TABLE_SERVICE_CUSTOM_FIELD_OPTION_TRANSLATIONS}.custom_field_option_id`,
              "=",
              `${TABLE_SERVICE_CUSTOM_FIELD_OPTIONS}.custom_field_option_id`
            )
            .on(
              `${TABLE_SERVICE_CUSTOM_FIELD_OPTION_TRANSLATIONS}.locale`,
              "=",
              sql`${lang}`
            )
      )
      .select([
        `${TABLE_SERVICE_CUSTOM_FIELDS}.custom_field_id`,
        `${TABLE_SERVICE_CUSTOM_FIELDS}.service_id`,
        `${TABLE_SERVICE_CUSTOM_FIELDS}.field_key`,
        `${TABLE_SERVICE_CUSTOM_FIELDS}.version`,
        `${TABLE_SERVICE_CUSTOM_FIELDS}.type`,
        `${TABLE_SERVICE_CUSTOM_FIELDS}.default_value`,
        `${TABLE_SERVICE_CUSTOM_FIELDS}.max_length`,
        `${TABLE_SERVICE_CUSTOM_FIELDS}.min_length`,
        `${TABLE_SERVICE_CUSTOM_FIELDS}.unique`,
        `${TABLE_SERVICE_CUSTOM_FIELDS}.verify`,
        `${TABLE_SERVICE_CUSTOM_FIELDS}.optional`,
        `${TABLE_SERVICE_CUSTOM_FIELDS}.sort_order`,
        `${TABLE_SERVICE_CUSTOM_FIELDS}.created_at`,
        `${TABLE_SERVICE_CUSTOM_FIELD_TRANSLATIONS}.label`,
        `${TABLE_SERVICE_CUSTOM_FIELD_TRANSLATIONS}.locale`,
      ])
      .select(
        sql
          .raw<string[]>(`ARRAY_AGG(DISTINCT ${TABLE_ACCOUNT_CUSTOM_FIELD_VALUES}.value)`)
          .as('values')
      )
      .select(
        sql
          .raw<boolean>(`BOOL_AND(${TABLE_ACCOUNT_CUSTOM_FIELD_VALUES}.verified)`)
          .as('verified')
      )
      .select(
        sql
          .raw<ServiceCustomFieldOptionResult[]>(`
            JSON_AGG(
              DISTINCT jsonb_build_object(
                'custom_field_option_id',   ${TABLE_SERVICE_CUSTOM_FIELD_OPTIONS}.custom_field_option_id,
                'service_id',  ${TABLE_SERVICE_CUSTOM_FIELD_OPTIONS}.service_id,
                'custom_field_id',    ${TABLE_SERVICE_CUSTOM_FIELD_OPTIONS}.custom_field_id,
                'value',       ${TABLE_SERVICE_CUSTOM_FIELD_OPTIONS}.value,
                'sort_order',  ${TABLE_SERVICE_CUSTOM_FIELD_OPTIONS}.sort_order,
                'custom_field_option_translation_id', ${TABLE_SERVICE_CUSTOM_FIELD_OPTION_TRANSLATIONS}.custom_field_option_translation_id,
                'locale',      ${TABLE_SERVICE_CUSTOM_FIELD_OPTION_TRANSLATIONS}.locale,
                'label',       ${TABLE_SERVICE_CUSTOM_FIELD_OPTION_TRANSLATIONS}.label
              )
            ) FILTER (
              WHERE ${TABLE_SERVICE_CUSTOM_FIELD_OPTIONS}.custom_field_option_id IS NOT NULL
            )
          `)
          .as("options")
      )
      .where(`${TABLE_SERVICE_CUSTOM_FIELDS}.version`, '=', latestVersion.max_version)
      .where(`${TABLE_SERVICE_CUSTOM_FIELD_TRANSLATIONS}.locale`, '=', lang)
      .where(`${TABLE_SERVICE_CUSTOM_FIELDS}.service_id`, '=', serviceId)
      .groupBy(`${TABLE_SERVICE_CUSTOM_FIELDS}.custom_field_id`)
      .groupBy(`${TABLE_SERVICE_CUSTOM_FIELD_TRANSLATIONS}.label`,)
      .groupBy(`${TABLE_SERVICE_CUSTOM_FIELD_TRANSLATIONS}.locale`)
      .execute();
  }

  async isCustomFieldValueUnique(
    serviceId: string,
    customFieldId: string,
    value: string,
    excludeAccountId?: string
  ): Promise<boolean> {
    await this.setServiceId(serviceId);
    
    let query = db
      .selectFrom(TABLE_ACCOUNT_CUSTOM_FIELD_VALUES)
      .select(db.fn.count<number>('custom_field_value_id').as('count'))
      .where('service_id', '=', serviceId)
      .where('custom_field_id', '=', customFieldId)
      .where('value', '=', value);
    
    if (excludeAccountId) {
      query = query.where('account_id', '!=', excludeAccountId);
    }
    
    const result = await query.executeTakeFirst();
    
    const count = result ? Number(result.count) : 0;
    return count === 0;
  }
}
