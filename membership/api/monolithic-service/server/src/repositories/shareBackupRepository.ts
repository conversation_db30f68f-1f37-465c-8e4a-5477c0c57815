import { injectable } from 'tsyringe';
import { db } from '../db/database';
import { TABLE_SHARE_BACKUPS } from '../constants/database';
import { InsertableShareBackupRow, ShareBackupEntity } from '../tables/shareBackups';

/**
 * Repository for managing share backup data
 * Handles CRUD operations for share backups used in distributed key management
 */
@injectable()
export class ShareBackupRepository {
  /**
   * Retrieves all active share backups for a user
   * @param userId - ID of the user
   * @param trx - Optional transaction instance
   * @returns Array of share backup entities
   */
  async getShareBackupsByUserId(userId: string): Promise<ShareBackupEntity> {
    return await db
      .selectFrom(TABLE_SHARE_BACKUPS)
      .selectAll()
      .where('user_id', '=', userId)
      .where('is_active', '=', true)
      .orderBy('created_at', 'desc')
      .executeTakeFirstOrThrow();
  }

  /**
   * Adds a new share backup and deactivates existing ones for the same user
   * @param shareBackup - Share backup data to insert
   * @returns The created share backup entity
   * @throws {Error} If insertion fails
   */
  async addNewShareBackup(shareBackup: InsertableShareBackupRow): Promise<ShareBackupEntity> {
    const result = await db.transaction().execute(async (trx) => {
      await trx
        .updateTable(TABLE_SHARE_BACKUPS)
        .set({ is_active: false })
        .where('user_id', '=', shareBackup.user_id)
        .execute();

      return await trx.insertInto(TABLE_SHARE_BACKUPS).values(shareBackup).returningAll().executeTakeFirstOrThrow();
    });

    return result;
  }
}
