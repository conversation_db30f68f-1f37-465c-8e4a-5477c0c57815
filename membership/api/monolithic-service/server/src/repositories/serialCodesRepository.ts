import { Database, db } from '../db/database';
import { MultitenantRepository } from './multitenantRepository';
import { TABLE_SERIAL_CODES } from '../constants/database';
import { SerialCodesEntity } from '../tables/serialCodesTable';
import { Transaction } from 'kysely';
import { v4 as uuidv4 } from 'uuid';

export interface InsertSerialCode {
  codeHash: string;
  serialCodeProjectId: string;
  maxUseNum: number;
}

export class SerialCodesRepository extends MultitenantRepository {

  async selectSerialCodeWithLock(
    serviceId: string,
    codeHash: string,
    trx?: Transaction<Database>
  ): Promise<SerialCodesEntity | undefined> {
    await this.setServiceId(serviceId, trx);

    return await (trx ?? db)
      .selectFrom(TABLE_SERIAL_CODES)
      .selectAll()
      .where('code_hash', '=', codeHash)
      .forUpdate()
      .executeTakeFirst();
  }

  async insertSerialCodes(
    serviceId: string,
    serialCodes: InsertSerialCode[],
    trx?: Transaction<Database>
  ): Promise<SerialCodesEntity[]> {
    await this.setServiceId(serviceId, trx);

    const now = new Date();
    const values = serialCodes.map(code => {
      return {
          serial_code_id: uuidv4(),
          code_hash: code.codeHash,
          serial_code_project_id: code.serialCodeProjectId,
          max_use_num: code.maxUseNum,
          remaining_use_num: code.maxUseNum,
          created_at: now,
          updated_at: now,
        };
    });
    return await (trx ?? db)
      .insertInto(TABLE_SERIAL_CODES)
      .values(values)
      .returningAll()
      .execute();
  }

  async updateRemainingUseNum(
    serviceId: string,
    serialCodeId: string,
    remainingUseNum: number,
    trx?: Transaction<Database>
  ): Promise<void> {
    await this.setServiceId(serviceId, trx);

    const now = new Date();
    await (trx ?? db)
      .updateTable(TABLE_SERIAL_CODES)
      .set({
        remaining_use_num: remainingUseNum,
        updated_at: now,
      })
      .where('serial_code_id', '=', serialCodeId)
      .execute();
  }
}
