import { injectable } from 'tsyringe';
import { db } from '../db/database';
import { MultitenantRepository } from './multitenantRepository';
import { InsertableSerialCodeActionRow, SerialCodeActionEntity } from '../tables/SerialCodeActionsTable';
import { TABLE_SERIAL_CODE_ACTIONS } from '../constants/database';

@injectable()
export class SerialCodeActionRepository extends MultitenantRepository {
  async selectSerialCodeActionById(actionId: string, serviceId: string): Promise<SerialCodeActionEntity | undefined> {
    await this.setServiceId(serviceId);
    return await db
      .selectFrom(TABLE_SERIAL_CODE_ACTIONS)
      .where('action_id', '=', actionId)
      .selectAll()
      .executeTakeFirst();
  }

  async insertSerialCodeAction(
    serviceId: string,
    data: InsertableSerialCodeActionRow,
  ): Promise<SerialCodeActionEntity> {
    await this.setServiceId(serviceId);
    return await db.insertInto(TABLE_SERIAL_CODE_ACTIONS).values(data).returningAll().executeTakeFirstOrThrow();
  }
}
