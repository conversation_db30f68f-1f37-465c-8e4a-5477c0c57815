import { Transaction } from 'kysely';
import { v4 as uuidv4 } from 'uuid';
import { TABLE_SERIAL_CODE_PROJECT_TRANSLATIONS, TABLE_SERIAL_CODE_PROJECTS } from '../constants/database';
import { Database, db } from '../db/database';
import { LanguageCode } from '../enum/languageCode';
import { SerialCodeProjectStatus } from '../enum/serialCodeProjectStatus';
import { SerialCodeProjectsEntity } from '../tables/serialCodeProjectsTable';
import {
  InsertableSerialCodeProjectTranslationRow,
  SerialCodeProjectTranslationEntity,
} from '../tables/translations/serialCodeProjectTranslationsTable';
import { distinctByIdAndLang } from '../utils/i18n';
import { MultitenantRepository } from './multitenantRepository';

export interface SerialCodeProjectsCreation {
  serviceId: string;
  rewardId?: string;
  slug: string;
  hashKey: string;
  startAt: Date;
  endAt: Date;
}
export class SerialCodeProjectsRepository extends MultitenantRepository {
  async selectSerialCodeProject(
    serviceId: string,
    serialCodeProjectId: string,
    trx?: Transaction<Database>,
  ): Promise<SerialCodeProjectsEntity | undefined> {
    await this.setServiceId(serviceId, trx);
    const now = new Date();
    return await (trx ?? db)
      .selectFrom(TABLE_SERIAL_CODE_PROJECTS)
      .selectAll()
      .where('serial_code_project_id', '=', serialCodeProjectId)
      .where('service_id', '=', serviceId)
      .where('status', '=', SerialCodeProjectStatus.ENABLE)
      .where('start_at', '<=', now)
      .where('end_at', '>', now)
      .executeTakeFirst();
  }

  async getAvailableSerialCodeProject(
    serviceId: string,
    lang: LanguageCode,
  ): Promise<
    { serialCodeProjectId: string; name: string; description: string; slug: string; startAt: Date; endAt: Date }[]
  > {
    await this.setServiceId(serviceId);
    const now = new Date();

    return await db
      .selectFrom(`${TABLE_SERIAL_CODE_PROJECTS} as prj`)
      .innerJoin(
        `${TABLE_SERIAL_CODE_PROJECT_TRANSLATIONS} as trs`,
        'trs.serial_code_project_id',
        'prj.serial_code_project_id',
      )
      .select(['prj.serial_code_project_id', 'prj.slug', 'trs.name', 'trs.description', 'prj.start_at', 'prj.end_at'])
      .where('prj.status', '=', SerialCodeProjectStatus.ENABLE)
      .where('prj.start_at', '<=', now)
      .where('prj.end_at', '>', now)
      .where('prj.reward_id', '!=', null)
      .$call(distinctByIdAndLang('trs.serial_code_project_id', 'trs.language', lang))
      .execute()
      .then((rows) =>
        rows.map((row) => ({
          serialCodeProjectId: row.serial_code_project_id,
          name: row.name,
          description: row.description,
          slug: row.slug,
          startAt: row.start_at,
          endAt: row.end_at,
        })),
      );
  }

  async selectReferencedRewardId(serviceId: string, serialCodeProjectId: string): Promise<string | null> {
    await this.setServiceId(serviceId);

    return await db
      .selectFrom(TABLE_SERIAL_CODE_PROJECTS)
      .select('reward_id')
      .where('serial_code_project_id', '=', serialCodeProjectId)
      .executeTakeFirst()
      .then((row) => row?.reward_id ?? null);
  }

  async insert(
    serialCodeProject: SerialCodeProjectsCreation,
    trx?: Transaction<Database>,
  ): Promise<SerialCodeProjectsEntity> {
    await this.setServiceId(serialCodeProject.serviceId, trx);

    return await (trx ?? db)
      .insertInto(TABLE_SERIAL_CODE_PROJECTS)
      .values({
        serial_code_project_id: uuidv4(),
        service_id: serialCodeProject.serviceId,
        reward_id: serialCodeProject.rewardId,
        slug: serialCodeProject.slug,
        hash_key: serialCodeProject.hashKey,
        status: SerialCodeProjectStatus.ENABLE,
        start_at: serialCodeProject.startAt,
        end_at: serialCodeProject.endAt,
        created_at: new Date(),
        updated_at: new Date(),
      })
      .returningAll()
      .executeTakeFirstOrThrow();
  }

  async insertTranslation(
    serviceId: string,
    translations: InsertableSerialCodeProjectTranslationRow[],
    trx?: Transaction<Database>,
  ): Promise<SerialCodeProjectTranslationEntity[]> {
    await this.setServiceId(serviceId, trx);

    return await (trx ?? db)
      .insertInto(TABLE_SERIAL_CODE_PROJECT_TRANSLATIONS)
      .values(translations)
      .returningAll()
      .execute();
  }
}
