import { TABLE_POINT_COMPONENTS } from '../constants/database';
import { Database, db } from '../db/database';
import { InsertablePointComponentRow, PointComponentEntity } from '../tables/pointComponentsTable';
import { MultitenantRepository } from './multitenantRepository';
import { Transaction } from 'kysely';

export class PointComponentRepository extends MultitenantRepository {
  async insertPointComponents(
    serviceId: string,
    data: InsertablePointComponentRow[],
    trx?: Transaction<Database>,
  ): Promise<PointComponentEntity[]> {
    await this.setServiceId(serviceId);
    return await (trx ?? db).insertInto(TABLE_POINT_COMPONENTS).values(data).returningAll().execute();
  }
}
