import { sql, Transaction } from 'kysely';
import {
  TABLE_QUESTIONNAIRE_ACTIONS,
  TABLE_QUESTIONNAIRE_QUESTION_ANSWERS,
  TABLE_QUESTIONNAIRE_QUESTION_TRANSLATIONS,
  TABLE_QUESTIONNAIRE_QUESTIONS,
  TABLE_QUESTIONNAIRE_RESULT_ANSWERS,
  TABLE_QUESTIONNAIRE_RESULT_RANK_TRANSLATIONS,
  TABLE_QUESTIONNAIRE_RESULT_RANKS,
  TABLE_QUESTIONNAIRE_THEMES,
} from '../constants/database';
import { Database, db } from '../db/database';
import { LanguageCode } from '../enum/languageCode';
import { QuestionnaireStatus } from '../enum/questionnaireStatusEnum';
import { InsertableQuestionnaireAnswerRow, QuestionnaireAnswerEntity } from '../tables/questionnaireResultAnswersTable';
import { distinctByIdAndLang } from '../utils/i18n';
import { MultitenantRepository } from './multitenantRepository';

export interface QuestionnaireAnswer {
  themeId: string;
  questionId: string;
  questionNumber: number;
  questionTitle: string;
  isRequired: boolean;
  postedAnswer?: string;
  correctAnswer?: string;
  obtainedAnswerPoint?: number;
  isCorrect?: boolean;
}

export interface QuestionnaireRank {
  rankId: string;
  rankName: string;
  rank: number;
  isPassed: boolean;
  rankHeaderAnimation: string;
  questionnairePoint?: number;
}

export class QuestionnaireResultAnswerRepository extends MultitenantRepository {
  async insertResultAnswers(
    serviceId: string,
    accountId: string,
    questionnaireId: string,
    data: InsertableQuestionnaireAnswerRow,
    trx?: Transaction<Database>,
  ): Promise<QuestionnaireAnswerEntity> {
    await this.setServiceId(serviceId, trx);

    await (trx ?? db)
      .updateTable(TABLE_QUESTIONNAIRE_RESULT_ANSWERS)
      .set({ is_available_result: false })
      .where('account_id', '=', accountId)
      .where(
        'rank_id',
        'in',
        (trx ?? db)
          .selectFrom(TABLE_QUESTIONNAIRE_RESULT_RANKS)
          .where('questionnaire_id', '=', questionnaireId)
          .select('rank_id'),
      )
      .execute();

    try {
      return await (trx ?? db)
        .insertInto(TABLE_QUESTIONNAIRE_RESULT_ANSWERS)
        .values(data)
        .returningAll()
        .executeTakeFirstOrThrow();
    } catch (error: unknown) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Unknown error occurred during insertResultAnswers');
    }
  }

  async selectQuestionnaireAnswers(
    serviceId: string,
    accountId: string,
    questionnaireId: string,
    lang: LanguageCode,
  ): Promise<QuestionnaireAnswer[]> {
    await this.setServiceId(serviceId);

    const responses = await db
      .selectFrom(`${TABLE_QUESTIONNAIRE_QUESTION_ANSWERS} as qqa`)
      .leftJoin(
        `${TABLE_QUESTIONNAIRE_RESULT_ANSWERS} as qra`,
        'qqa.questionnaire_result_id',
        'qra.questionnaire_result_id',
      )
      .leftJoin(`${TABLE_QUESTIONNAIRE_QUESTIONS} as qq`, 'qq.question_id', 'qqa.question_id')
      .leftJoin(`${TABLE_QUESTIONNAIRE_QUESTION_TRANSLATIONS} as qqt`, (jb) =>
        jb.onRef('qqt.question_id', '=', 'qq.question_id').on('qqt.language', '=', sql`${lang}`),
      )
      .leftJoin(`${TABLE_QUESTIONNAIRE_THEMES} as qt`, 'qq.theme_id', 'qt.theme_id')
      .where('qra.is_available_result', '=', true)
      .where('qra.account_id', '=', accountId)
      .where('qt.questionnaire_id', '=', questionnaireId)
      .$call(distinctByIdAndLang('qq.question_id', 'qqt.language', lang))
      .orderBy('qra.created_at', 'desc')
      .select([
        'qq.question_id',
        'qq.theme_id',
        'qq.question_number',
        'qqt.question_title',
        'qq.is_required',
        'qqa.question_answer',
        'qqt.correct_data',
        'qq.answer_point',
        'qqa.is_correct',
      ])
      .execute();

    return responses.map((response) => {
      const answer: QuestionnaireAnswer = {
        questionId: response.question_id!,
        themeId: response.theme_id!,
        questionNumber: response.question_number!,
        isRequired: response.is_required!,
        questionTitle: response.question_title!,
        postedAnswer: response.question_answer,
        correctAnswer: response.correct_data ?? undefined,
        obtainedAnswerPoint: response.answer_point ?? undefined,
        isCorrect: response.is_correct,
      };
      return answer;
    });
  }

  async selectAnsweredResultRank(
    serviceId: string,
    accountId: string,
    questionnaireId: string,
    lang: LanguageCode,
  ): Promise<QuestionnaireRank> {
    await this.setServiceId(serviceId);

    try {
      // 翻訳テーブルに distinctByIdAndLang を適用したサブクエリを用意
      const qrrtSubquery = db
        .selectFrom(`${TABLE_QUESTIONNAIRE_RESULT_RANK_TRANSLATIONS} as qrrt`)
        .$call(distinctByIdAndLang('qrrt.rank_id', 'qrrt.language', lang))
        .select(['qrrt.rank_id', 'qrrt.language', 'qrrt.rank_name'])
        .as('qrrt'); // エイリアス必要

      const response = await db
        .selectFrom(`${TABLE_QUESTIONNAIRE_RESULT_ANSWERS} as qra`)
        .leftJoin(`${TABLE_QUESTIONNAIRE_RESULT_RANKS} as qrr`, 'qra.rank_id', 'qrr.rank_id')
        .leftJoin(qrrtSubquery, 'qrrt.rank_id', 'qrr.rank_id')
        .where('qra.service_id', '=', serviceId)
        .where('qra.account_id', '=', accountId)
        .where('qrr.questionnaire_id', '=', questionnaireId)
        .where('qra.is_available_result', '=', true)
        .orderBy('qra.created_at', 'desc')
        .select([
          'qra.rank_id',
          'qrrt.rank_name',
          'qrr.rank',
          'qrr.rank_header_animation_url',
          'qra.questionnaire_points',
          'qrr.is_passed',
        ])
        .executeTakeFirstOrThrow();

      const questionnaireRank: QuestionnaireRank = {
        rankId: response.rank_id!,
        rankName: response.rank_name!,
        rank: response.rank!,
        isPassed: response.is_passed!,
        rankHeaderAnimation: response.rank_header_animation_url!,
        questionnairePoint: response.questionnaire_points,
      };

      return questionnaireRank;
    } catch (error: unknown) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Unknown error occurred during selectAnsweredResultRank');
    }
  }

  async selectQuestionnaireResultRank(
    serviceId: string,
    accountId: string,
    questId: string,
  ): Promise<string | undefined> {
    await this.setServiceId(serviceId);
    try {
      const { rank_id } = await db
        .selectFrom(TABLE_QUESTIONNAIRE_RESULT_ANSWERS)
        .leftJoin(
          TABLE_QUESTIONNAIRE_RESULT_RANKS,
          'questionnaire_result_answers.rank_id',
          'questionnaire_result_ranks.rank_id',
        )
        .leftJoin(
          TABLE_QUESTIONNAIRE_ACTIONS,
          'questionnaire_result_ranks.questionnaire_id',
          'questionnaire_actions.questionnaire_id',
        )
        .leftJoin('quest_actions', 'questionnaire_actions.action_id', 'quest_actions.action_id')
        .where('questionnaire_result_answers.questionnaire_status', 'in', [
          QuestionnaireStatus.PASSED,
          QuestionnaireStatus.UNDEFINED,
        ])
        .where('questionnaire_result_answers.account_id', '=', accountId)
        .where('quest_actions.quest_id', '=', questId)
        .where('questionnaire_result_answers.is_available_result', '=', true)
        .orderBy('questionnaire_result_answers.created_at desc')
        .select('questionnaire_result_answers.rank_id')
        .executeTakeFirstOrThrow();

      return rank_id;
    } catch (error: unknown) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Unknown error occurred during selectQuestionnaireResultRank');
    }
  }
}
