import { z } from '@hono/zod-openapi';
import { OpenAI } from 'openai';
import { config } from '../configs/config';
import { GetI18nNFTMetadataResponse, GetI18nNFTMetadataResponseSchema } from '../dtos/nfts/schemas';
import { LanguageCode } from '../enum/languageCode';
import { InternalServerError } from '../errors/internalServerError';
import { logger } from '../utils/middleware/loggerMiddleware';
export class GptComponent {
  private openai: OpenAI;

  constructor() {
    this.openai = new OpenAI({ apiKey: config.openaiApiKey });
  }

  private async callWithRetry<T>(system: string, prompt: string, schema: z.ZodSchema<T>, maxRetries = 3): Promise<T> {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      const res = await this.openai.chat.completions.create({
        model: 'gpt-4o',
        messages: [
          { role: 'system', content: system },
          { role: 'user', content: prompt },
        ],
      });
      const text = res.choices[0].message.content ?? '';
      try {
        return schema.parse(JSON.parse(text));
      } catch (e) {
        logger.warn(`GPT format error (attempt ${attempt}):`, e);
        if (attempt === maxRetries) throw e;
      }
    }
    throw new InternalServerError('GPT format error');
  }

  async translateNftMetadata(
    metadata: GetI18nNFTMetadataResponse,
    lang: LanguageCode,
  ): Promise<GetI18nNFTMetadataResponse> {
    const system = `Translate to ${lang}.`;
    const prompt = `TL .k=name,description.→{"name":"…","description":"…"} src:${JSON.stringify(metadata)}`;
    return await this.callWithRetry<GetI18nNFTMetadataResponse>(system, prompt, GetI18nNFTMetadataResponseSchema);
  }
}
