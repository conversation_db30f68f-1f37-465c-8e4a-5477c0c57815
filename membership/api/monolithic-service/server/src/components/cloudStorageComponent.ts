import { ApiError, GetSignedUrlConfig, Storage } from '@google-cloud/storage';
import { config } from '../configs/config';
import { ForbiddenError } from '../errors/forbiddenError';
import { GatewayTimeoutError } from '../errors/gatewayTimeoutError';
import { InternalServerError } from '../errors/internalServerError';
import { NotFoundError } from '../errors/notFoundError';
import { ResourceExhaustedError } from '../errors/resourceExhaustedError';
import { ServiceUnavailableError } from '../errors/serviceUnavailableError';
import { logger } from '../utils/middleware/loggerMiddleware';

export class CloudStorageComponent {
  private storage: Storage;

  constructor() {
    this.storage = new Storage({
      projectId: config.gcpProjectId,
      retryOptions: {
        autoRetry: true,
        maxRetries: 3,
        retryDelayMultiplier: 2,
      },
    });
  }

  async generateV4UploadSignedUrl(bucketName: string, fileName: string, options: GetSignedUrlConfig) {
    try {
      const [url] = await this.storage.bucket(bucketName).file(fileName).getSignedUrl(options);
      return url;
    } catch (err: unknown) {
      logger.error({ message: 'Error generating V4 upload signed URL', err });
      this.cloudStorageError(err);
    }
  }

  private cloudStorageError(err: unknown): never {
    if (err instanceof ApiError) {
      switch (err.code) {
        case 403:
          throw new ForbiddenError('Permission denied');
        case 404:
          throw new NotFoundError('Bucket/Object not found');
        case 429:
          throw new ResourceExhaustedError('Rate limit exceeded');
        case 503:
          throw new ServiceUnavailableError('Service unavailable');
        case 504:
          throw new GatewayTimeoutError('Gateway timeout');
        default:
          throw new InternalServerError('GCS internal error');
      }
    }
    throw err;
  }
}
