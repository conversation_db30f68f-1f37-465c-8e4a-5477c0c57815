import { GcpKmsSigner } from '@cuonghx.gu-tech/ethers-gcp-kms-signer';
import {
  ethers,
  JsonRpcProvider,
  keccak256,
  Network,
  parseUnits,
  TransactionRequest,
  TransactionResponse,
} from 'ethers';
import { inject, injectable } from 'tsyringe';
import { NULL_ADDRESS } from '../configs/blockchain';
import { InternalServerError } from '../errors/internalServerError';
import { NotFoundError } from '../errors/notFoundError';
import { VaultKeyRepository } from '../repositories/vaultKeyRepository';
import { logger } from '../utils/middleware/loggerMiddleware';
import { retryExecution } from '../utils/retry';

@injectable()
export class TransactionComponent {
  private provider: JsonRpcProvider;
  private fallbackProvider: JsonRpcProvider;
  private gasLimitMultiplier: number;
  private maxFeePerGasMultiplier: number;
  private maxPriorityFeePerGasMultiplier: number;
  private baseMaxFeePerGas: bigint;
  constructor(@inject('VaultKeyRepository') private vaultKeyRepository: VaultKeyRepository) {
    if (!process.env.JSON_RPC_URL || !process.env.ALCHEMY_API_KEY || !process.env.ALCHEMY_CHAIN_NAME) {
      throw new InternalServerError('JSON_RPC_URL environment variable is not set');
    }
    if (
      !process.env.GAS_LIMIT_MULTIPLIER ||
      !process.env.BASE_MAX_FEE_PER_GAS ||
      !process.env.MAX_FEE_PER_GAS_MULTIPLIER ||
      !process.env.MAX_PRIORITY_FEE_PER_GAS_MULTIPLIER
    ) {
      throw new InternalServerError(
        'GAS_LIMIT_MULTIPLIER, BASE_MAX_FEE_PER_GAS, MAX_FEE_PER_GAS_MULTIPLIER, or MAX_PRIORITY_FEE_PER_GAS_MULTIPLIER environment variables are not set',
      );
    }

    const alchemyUrl = `https://${process.env.ALCHEMY_CHAIN_NAME}.g.alchemy.com/v2/${process.env.ALCHEMY_API_KEY}`;
    this.provider = new ethers.JsonRpcProvider(alchemyUrl);
    this.fallbackProvider = new ethers.JsonRpcProvider(process.env.JSON_RPC_URL);
    this.baseMaxFeePerGas = parseUnits(process.env.MAX_FEE_PER_GAS_AMOUNT || '600', 'gwei');
    this.maxFeePerGasMultiplier = Number(process.env.MAX_FEE_PER_GAS_MULTIPLIER || 100);
    this.gasLimitMultiplier = Number(process.env.GAS_LIMIT_MULTIPLIER || 100);
    this.maxPriorityFeePerGasMultiplier = Number(process.env.MAX_PRIORITY_FEE_PER_GAS_MULTIPLIER || 100);
  }

  async getSigner(tenantId: string, isFallback: boolean = false): Promise<GcpKmsSigner> {
    const vaultKey = await this.vaultKeyRepository.getVaultKeyId(tenantId);
    if (!vaultKey) {
      throw new NotFoundError('Vault key not found');
    }
    if (!process.env.GCP_PROJECT_ID) {
      throw new InternalServerError('GCP_PROJECT_ID environment variable is not set');
    }
    const signer = new GcpKmsSigner({
      projectId: process.env.GCP_PROJECT_ID,
      locationId: vaultKey.key_ring_location,
      keyRingId: vaultKey.key_ring_project,
      keyId: vaultKey.key_ring_name,
      versionId: vaultKey.key_version,
    });
    if (!signer) {
      throw new InternalServerError('Failed to initialize signer');
    }

    if (!isFallback) {
      return signer.connect(this.provider);
    } else {
      return signer.connect(this.fallbackProvider);
    }
  }

  async getTransactionInfo(
    address: string,
  ): Promise<{ gasPrice: bigint; info: { nonce: number; maxFeePerGas: bigint; maxPriorityFeePerGas: bigint } }> {
    const retryHandler = (error: any) => {
      logger.error(error);
      return true;
    };
    const nonce = await retryExecution<number>(
      async () => await this.provider.getTransactionCount(address, 'pending'),
      { retries: 3, retryDelay: 200, retryHandler: retryHandler },
      async () => await this.fallbackProvider.getTransactionCount(address, 'pending'),
    );

    const feeData = await retryExecution<ethers.FeeData>(
      async () => await this.provider.getFeeData(),
      { retries: 3, retryDelay: 200, retryHandler: retryHandler },
      async () => await this.fallbackProvider.getFeeData(),
    );

    if (!feeData.gasPrice || !feeData.maxFeePerGas || !feeData.maxPriorityFeePerGas) {
      throw new InternalServerError('Gas price is not available');
    }
    logger.info({ transaction: 'TransactionComponent.getTransactionInfo', feeData });

    const estimatedBaseFee = await retryExecution<bigint>(
      // base fee from gas station
      async () => {
        const response = await fetch('https://gasstation.polygon.technology/v2');
        if (!response.ok) {
          throw new Error('Failed to fetch estimated base fee');
        }
        // response is gwei
        const data = (await response.json()) as { estimatedBaseFee: number };
        // response can be like 6.1e-8, so convert to 0.000000061 with this.
        const baseFeeGwei = data.estimatedBaseFee.toFixed(9);
        const estimatedBaseFeeWei = parseUnits(baseFeeGwei, 'gwei');
        logger.info({
          transaction: 'TransactionComponent.getTransactionInfo.gasStationBaseFee',
          estimatedBaseFeeWei,
        });
        return estimatedBaseFeeWei;
      },
      { retries: 3, retryDelay: 200, retryHandler: retryHandler },
      // base fee from block(wei)
      async () => {
        const latestBlock = await this.provider.getBlock('latest');
        // wei
        const estimatedBaseFeeWei = latestBlock?.baseFeePerGas ?? null;
        if (!estimatedBaseFeeWei) {
          throw new Error('Failed to fetch estimated base fee');
        }

        logger.info({ transaction: 'TransactionComponent.getTransactionInfo.blockBaseFee', estimatedBaseFeeWei });
        return estimatedBaseFeeWei;
      },
    );

    const maxPriorityFeePerGas =
      (feeData.maxPriorityFeePerGas * BigInt(this.maxPriorityFeePerGasMultiplier)) / BigInt(100);
    const multipliedEstimatedMaxFee =
      (estimatedBaseFee * BigInt(this.maxFeePerGasMultiplier)) / BigInt(100) + maxPriorityFeePerGas;
    const maxFeePerGas =
      multipliedEstimatedMaxFee < BigInt(this.baseMaxFeePerGas)
        ? BigInt(this.baseMaxFeePerGas)
        : multipliedEstimatedMaxFee;

    const gasInfo = {
      gasPrice: feeData.gasPrice,
      info: {
        nonce,
        maxFeePerGas: maxFeePerGas,
        maxPriorityFeePerGas: maxPriorityFeePerGas,
      },
    };

    logger.info({ transaction: 'TransactionComponent.getTransactionInfo.gasInfo', gasInfo });

    return gasInfo;
  }

  async estimateFunctionCallGas(contractAddress: string, data: string): Promise<ethers.BigNumberish> {
    const retryHandler = (error: any) => {
      logger.error(error);
      return true;
    };
    const param = {
      to: contractAddress,
      data: data,
    };
    const gasEstimate = await retryExecution<bigint>(
      async () => await this.provider.estimateGas(param),
      { retries: 3, retryDelay: 200, retryHandler: retryHandler },
      async () => await this.fallbackProvider.estimateGas(param),
    );

    return (gasEstimate * BigInt(this.gasLimitMultiplier)) / BigInt(100);
  }

  async estimateDeployContractGas(data: string): Promise<ethers.BigNumberish> {
    const retryHandler = (error: any) => {
      logger.error(error);
      return true;
    };
    const gasEstimate = await retryExecution<bigint>(
      async () => await this.provider.estimateGas({ data }),
      { retries: 3, retryDelay: 50, retryHandler: retryHandler },
      async () => await this.fallbackProvider.estimateGas({ data }),
    );

    return (gasEstimate * BigInt(this.gasLimitMultiplier)) / BigInt(100);
  }

  async sendTransaction(
    tenantId: string,
    contractAddress: string,
    contractABI: ethers.Interface,
    functionName: string,
    args: any[],
  ): Promise<TransactionResponse> {
    const retryHandler = (error: any) => {
      logger.error(error);
      return true;
    };
    logger.info({ transaction: 'TransactionComponent', args: args });

    const signer = await this.getSigner(tenantId);
    const fallbackSigner = await this.getSigner(tenantId, true);
    const walletAddress = await signer.getAddress();
    logger.info({ transaction: 'TransactionComponent', vault_wallet_address: walletAddress });

    const txInfo = (await this.getTransactionInfo(walletAddress)).info;
    logger.info({ transaction: 'TransactionComponent', transaction_info: txInfo });

    const contract = new ethers.Contract(contractAddress, contractABI, signer);
    const fallbackContract = new ethers.Contract(contractAddress, contractABI, fallbackSigner);
    const callData = contract.interface.encodeFunctionData(functionName, args);
    const gasEstimate = await retryExecution<bigint>(
      async () => await contract[functionName].estimateGas(...args),
      { retries: 3, retryDelay: 200, retryHandler: retryHandler },
      async () => await fallbackContract[functionName].estimateGas(...args),
    );
    logger.info({ transaction: 'TransactionComponent', estimate_gas: gasEstimate });

    const gasMultiPiler = BigInt(this.gasLimitMultiplier);
    logger.info({ transaction: 'TransactionComponent', multiplier: gasMultiPiler });

    const gasLimit = (gasEstimate * gasMultiPiler) / 100n;
    const txResponse = await retryExecution<any>(
      async () => await contract[functionName](...args, { ...txInfo, gasLimit: gasLimit }),
      { retries: 3, retryDelay: 200, retryHandler: retryHandler },
      async () => await fallbackContract[functionName](...args, { ...txInfo, gasLimit: gasLimit }),
    );

    txResponse.nonce = txInfo.nonce;
    txResponse.callData = callData;

    return txResponse;
  }

  async sendTransactionFromSignedTransaction(signedTransaction: string): Promise<TransactionResponse | null> {
    const retryHandler = (error: any) => {
      logger.error(error);
      return true;
    };
    const txResponse = await retryExecution<TransactionResponse>(
      async () => await this.provider.broadcastTransaction(signedTransaction),
      { retries: 3, retryDelay: 200, retryHandler: retryHandler },
      async () => await this.fallbackProvider.broadcastTransaction(signedTransaction),
    );
    return txResponse;
  }

  async ethCallFromTransactionRequest(
    transactionRequest: TransactionRequest,
    signer: GcpKmsSigner,
    fallbackSigner: GcpKmsSigner,
  ): Promise<string> {
    // const retryHandler = (error: any) => {
    //   logger.error(error);
    //   return true;
    // };

    // const result = await retryExecution<string>(
    //   async () => await signer.call(transactionRequest),
    //   { retries: 3, retryDelay: 200, retryHandler: retryHandler },
    //   async () =>
    //     await fallbackSigner.call(transactionRequest),
    // );
    // return result;
    let result = '0x';
    try {
      result = await signer.call(transactionRequest);
    } catch (error: any) {
      logger.error(error);
      result = error.message;
      try {
        result = await fallbackSigner.call(transactionRequest);
      } catch (fallbackError) {
        logger.error(fallbackError);
        // result = fallbackError.message;
      }
    }
    return result;
  }

  async sendRawTransaction(
    tenantId: string,
    contractAddress: string | null,
    callData: string,
    nonce: number,
  ): Promise<TransactionResponse | null> {
    const retryHandler = (error: any) => {
      logger.error(error);
      return true;
    };

    const { transactionRequest, signer, fallbackSigner } = await this.createTransactionRequest(
      tenantId,
      contractAddress,
      callData,
      nonce,
    );
    if (!transactionRequest) {
      // nonce skip.
      return null;
    }

    const txResponse = await retryExecution<any>(
      async () => await signer.sendTransaction(transactionRequest),
      { retries: 3, retryDelay: 200, retryHandler: retryHandler },
      async () => await fallbackSigner.sendTransaction(transactionRequest),
    );

    return txResponse;
  }

  async callViewFunction(
    contractAddress: string,
    contractABI: ethers.InterfaceAbi,
    functionName: string,
    args: any[] = [],
  ): Promise<any> {
    logger.info({
      transaction: 'TransactionComponent.callViewFunction',
      contractAddress,
      functionName,
      args,
    });

    //Fallback provider is used because Alchemy is prioritized over Moralis.
    const contract = new ethers.Contract(contractAddress, contractABI, this.fallbackProvider);

    const callViewFunc = async () => {
      return contract[functionName](...args);
    };
    const retryHandler = (error: any) => {
      logger.error(error);
      return true;
    };

    const callFallbackViewFunc = async () => {
      // Fallback provider is Moralis
      const fallbackContract = new ethers.Contract(contractAddress, contractABI, this.provider);
      return fallbackContract[functionName](...args);
    };

    const result = await retryExecution<any>(
      callViewFunc,
      { retries: 3, retryDelay: 200, retryHandler },
      callFallbackViewFunc,
    );

    logger.info({
      transaction: 'TransactionComponent.callViewFunction',
      result,
    });

    return result;
  }

  async getWeiBalance(address: string): Promise<bigint> {
    return await this.provider.getBalance(address);
  }

  async getTransactionReceipt(transactionHash: string): Promise<ethers.TransactionReceipt> {
    const retryHandler = (error: any) => {
      return error.code && (error.code === 429 || error.code >= 500);
    };
    const receipt = await retryExecution<ethers.TransactionReceipt | null>(
      async () => await this.provider.getTransactionReceipt(transactionHash),
      { retries: 3, retryDelay: 50, retryHandler: retryHandler },
      async () => await this.fallbackProvider.getTransactionReceipt(transactionHash),
    );
    if (!receipt) {
      throw new InternalServerError('Failed to retrieve transaction receipt');
    }

    return receipt;
  }

  // Check if the contract is deployed(Especially for ERC6551 token bound account)
  async isContractDeployed(address: string): Promise<boolean> {
    const retryHandler = (error: any) => {
      logger.error(error);
      return true;
    };
    const code = await retryExecution<string>(
      async () => await this.provider.getCode(address),
      { retries: 3, retryDelay: 200, retryHandler: retryHandler },
      async () => await this.fallbackProvider.getCode(address),
    );
    // Contract is deployed if the code is not "0x"
    return code !== '0x';
  }

  async signTransaction(
    transactionRequest: TransactionRequest,
    signer: GcpKmsSigner,
    fallbackSigner: GcpKmsSigner,
  ): Promise<string> {
    const retryHandler = (error: any) => {
      logger.error(error);
      return true;
    };
    const signedTx = await retryExecution<string>(
      async () => {
        return await signer.signTransaction(transactionRequest);
      },
      { retries: 3, retryDelay: 200, retryHandler: retryHandler },
      async () => {
        return await fallbackSigner.signTransaction(transactionRequest);
      },
    );
    return signedTx;
  }

  async calculateTxHash(signedTx: string): Promise<string> {
    return keccak256(signedTx);
  }

  async createTransactionRequest(
    tenantId: string,
    contractAddress: string | null,
    callData: string,
    nonce: number,
  ): Promise<{
    transactionRequest: TransactionRequest | undefined;
    signer: GcpKmsSigner;
    fallbackSigner: GcpKmsSigner;
  }> {
    const toAddress = contractAddress === NULL_ADDRESS ? null : contractAddress;
    const retryHandler = (error: any) => {
      logger.error(error);
      return true;
    };

    logger.info({ transaction: 'createTransactionRequest', tenantId, contractAddress, toAddress, callData });
    const signer = await this.getSigner(tenantId);
    const fallbackSigner = await this.getSigner(tenantId, true);
    const walletAddress = await signer.getAddress();
    logger.info({ transaction: 'createTransactionRequest', vault_wallet_address: walletAddress });

    const txInfo = (await this.getTransactionInfo(walletAddress)).info;

    // // skip processing if nonce is outdated
    if (nonce < txInfo.nonce) {
      logger.warn(`Nonce ${nonce} is outdated. value is ${txInfo.nonce}. Skipping transaction broadcast.`);
      return {
        transactionRequest: undefined,
        signer: signer,
        fallbackSigner: fallbackSigner,
      };
    }

    // use provided nonce
    txInfo.nonce = nonce;
    logger.info({ transaction: 'createTransactionRequest', transaction_info: txInfo });

    logger.info({ transaction: 'createTransactionRequest', operation: 'estimateGas' });

    const gasEstimate = await retryExecution<bigint>(
      async () => await signer.estimateGas({ to: toAddress, data: callData, ...txInfo }),
      { retries: 3, retryDelay: 200, retryHandler: retryHandler },
      async () => await fallbackSigner.estimateGas({ to: toAddress, data: callData, ...txInfo }),
    );
    logger.info({ transaction: 'createTransactionRequest', estimate_gas: gasEstimate });

    logger.info({ transaction: 'createTransactionRequest', multiplier: this.gasLimitMultiplier });

    const gasLimit = (gasEstimate * BigInt(this.gasLimitMultiplier)) / BigInt(100);
    logger.info({
      transaction: 'createTransactionRequest',
      operation: 'retryTransaction',
      gasLimit,
    });

    const network = await retryExecution<Network>(
      async () => await this.provider.getNetwork(),
      { retries: 3, retryDelay: 200, retryHandler: retryHandler },
      async () => await this.fallbackProvider.getNetwork(),
    );

    return {
      transactionRequest: { to: toAddress, data: callData, ...txInfo, gasLimit: gasLimit, chainId: network.chainId },
      signer: signer,
      fallbackSigner: fallbackSigner,
    };
  }
}
