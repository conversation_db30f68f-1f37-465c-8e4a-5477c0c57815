import { A<PERSON>, Address, Chain, createPublicClient, http, PublicClient } from 'viem';
import { polygon, polygonAmoy } from 'viem/chains';

import * as dotenv from 'dotenv';
import { Block, BlockTag, fallback, Transaction, TransactionReceipt } from 'viem';
import { config } from '../configs/config';
import { InternalServerError } from '../errors/internalServerError';
import { logger } from '../utils/middleware/loggerMiddleware';
dotenv.config();

type TxResult = { success: true; value: Transaction } | { success: false; error: string };

function formatError(error: unknown): string {
  return error instanceof Error ? error.stack || error.message : JSON.stringify(error);
}

export class ViemComponent {
  private publicClient: PublicClient;

  constructor() {
    const { alchemyApiKey, alchemyChainName, jsonRpcUrl } = config;
    if (!alchemyApiKey || !alchemyChainName || !jsonRpcUrl) {
      throw new InternalServerError(
        'ALCHEMY_API_KEY or ALCHEMY_CHAIN_NAME or JSON_RPC_URL environment variable is not set',
      );
    }
    const chainMap: Record<string, Chain> = {
      polygonAmoy: polygonAmoy,
      polygon: polygon,
    };
    const chainName: Chain = chainMap[alchemyChainName] || polygonAmoy;
    const alchemyUrl = `https://${alchemyChainName}.g.alchemy.com/v2/${alchemyApiKey}`;
    this.publicClient = createPublicClient({
      chain: chainName,
      transport: fallback([http(alchemyUrl), http(jsonRpcUrl)], { retryCount: 3, retryDelay: 200 }),
    });
  }

  async getBlock(blockTag: BlockTag): Promise<Block | null> {
    try {
      const block = await this.publicClient.getBlock({ blockTag });
      return block;
    } catch (error) {
      logger.error({ method: 'getBlock', error: error });
      return null;
    }
  }

  async getTransaction(txHash: `0x${string}`): Promise<TxResult> {
    try {
      const transaction = await this.publicClient.getTransaction({ hash: txHash });
      return { success: true, value: transaction };
    } catch (error) {
      const errorMsg = formatError(error);
      logger.error({ method: 'getTransaction', txHash, error: errorMsg });
      return { success: false, error: errorMsg };
    }
  }

  async getTransactionReceipt(txHash: `0x${string}`): Promise<TransactionReceipt | null> {
    try {
      const receipt = await this.publicClient.getTransactionReceipt({ hash: txHash });
      return receipt;
    } catch (error) {
      const errorMsg = formatError(error);
      logger.error({ method: 'getTransactionReceipt', txHash, error: errorMsg });
      return null;
    }
  }

  async callViewFunction<T>(contractAddress: Address, abi: Abi, functionName: string, args: unknown[]): Promise<T> {
    try {
      return (await this.publicClient.readContract({
        address: contractAddress,
        abi: abi,
        functionName: functionName,
        args: args,
      })) as T;
    } catch (error) {
      const errorMsg = formatError(error);
      logger.error({ method: 'callViewFunction', contractAddress, abi, functionName, args, error: errorMsg });
      throw new InternalServerError('Failed to call view function');
    }
  }
}
