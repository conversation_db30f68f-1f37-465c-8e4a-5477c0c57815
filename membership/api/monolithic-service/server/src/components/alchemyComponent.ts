import { Alchemy, Network, TransactionReceipt, TransactionResponse } from 'alchemy-sdk';
import * as dotenv from 'dotenv';
import { config } from '../configs/config';
import { logger } from '../utils/middleware/loggerMiddleware';

dotenv.config();

export class AlchemyComponent {
  private alchemy: Alchemy;

  constructor() {
    const settings = {
      apiKey: config.alchemyApiKey,
      network: config.alchemyChainName as Network,
      authToken: config.alchemyAuthToken,
    };
    this.alchemy = new Alchemy(settings);
  }

  async getTransaction(txHash: string): Promise<TransactionResponse | null> {
    try {
      const transaction = await this.alchemy.transact.getTransaction(txHash);
      return transaction;
    } catch (error) {
      logger.error('Error getting transaction:', error);
      return null;
    }
  }

  async getTransactionReceipt(txHash: string): Promise<TransactionReceipt | null> {
    try {
      const transactionReceipt = await this.alchemy.core.getTransactionReceipt(txHash);
      return transactionReceipt;
    } catch (error) {
      logger.error('Error getting transaction receipt:', error);
      return null;
    }
  }

  async updateAddressActivityWebhook(webhookId: string, address: string): Promise<void> {
    try {
      const updateAddresses = await this.alchemy.notify.updateWebhook(webhookId, {
        addAddresses: [address],
        removeAddresses: [],
      });
      logger.info({ method: 'updateAddressActivityWebhook', data: updateAddresses });
    } catch (error) {
      logger.error('Error updating address activity webhook:', error);
    }
  }

  async updateNftActivityWebhook(webhookId: string, address: string): Promise<void> {
    try {
      const updateNftFilterWebhook = await this.alchemy.notify.updateWebhook(webhookId, {
        addFilters: [
          {
            contractAddress: address,
          },
        ],
      });
      logger.info({ method: 'updateNftActivityWebhook', data: updateNftFilterWebhook });
    } catch (error) {
      logger.error('Error updating NFT activity webhook:', error);
    }
  }
}
