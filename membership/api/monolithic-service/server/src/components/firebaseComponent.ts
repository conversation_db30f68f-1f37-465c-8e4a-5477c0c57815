import type FirebaseFirestore from '@google-cloud/firestore';
import * as admin from 'firebase-admin';
import { getFirestore } from 'firebase-admin/firestore';
import { config } from '../configs/config';
import { UnauthorizedError } from '../errors/unauthorizedError';
import { logger } from '../utils/middleware/loggerMiddleware';

export class FirebaseComponent {
  private app!: admin.app.App;
  private firestoreDb: string;

  constructor() {
    this.initializeFirebase();
    const db = process.env.FIRESTORE_DB;
    if (!db) {
      throw new Error('FIRESTORE_DB environment variable is not set');
    }
    this.firestoreDb = db;
  }

  private getServiceAccount(): admin.ServiceAccount {
    const serviceAccount: admin.ServiceAccount = {
      projectId: config.firebaseProjectId,
      clientEmail: config.firebaseClientEmail,
      privateKey: config.firebasePrivateKey,
    };

    return serviceAccount;
  }

  private initializeFirebase(): void {
    if (!admin.apps.length) {
      const isEmulator = !!process.env.FIRESTORE_EMULATOR_HOST;
      if (isEmulator) {
        process.env.GOOGLE_APPLICATION_CREDENTIALS = '';
        this.app = admin.initializeApp({
          projectId: config.firebaseProjectId,
        });
      } else {
        this.app = admin.initializeApp({
          credential: admin.credential.cert(this.getServiceAccount()),
        });
      }
    }
  }

  async verifyFirebaseIdToken(firebaseIdToken: string): Promise<admin.auth.DecodedIdToken> {
    try {
      return await admin.auth().verifyIdToken(firebaseIdToken);
    } catch (error: unknown) {
      logger.error('Error verifying Firebase ID token:', error);
      throw new UnauthorizedError();
    }
  }

  async createCustomToken(uid: string, additionalClaims?: object): Promise<string> {
    try {
      return await admin.auth().createCustomToken(uid, additionalClaims);
    } catch (error: unknown) {
      logger.error('Error creating custom token:', error);
      throw new UnauthorizedError();
    }
  }

  async getDocumentFromFirestore(
    collection: string,
    documentId: string,
  ): Promise<FirebaseFirestore.DocumentData | undefined> {
    try {
      const db = getFirestore(this.app, this.firestoreDb);
      const docRef = db.collection(collection).doc(documentId);
      const doc = await docRef.get();

      if (!doc.exists) {
        return undefined;
      }
      return doc.data();
    } catch (error: unknown) {
      logger.error('Error getting document from Firestore:', error);
      if (error instanceof Error) {
        throw new Error(`Failed to get document from Firestore: ${error.message}`);
      } else {
        throw new Error('Failed to get document from Firestore: Unknown error');
      }
    }
  }

  public getFirestoreInstance(): FirebaseFirestore.Firestore {
    return getFirestore(this.app, this.firestoreDb);
  }

  public async createDocument(
    collectionName: string,
    data: FirebaseFirestore.DocumentData,
  ): Promise<FirebaseFirestore.DocumentReference> {
    const docRef = this.getFirestoreInstance().collection(collectionName).doc();
    await docRef.set(data);
    return docRef;
  }

  public async updateDocument(
    collectionName: string,
    docId: string,
    updateData: FirebaseFirestore.UpdateData<FirebaseFirestore.DocumentData>,
  ): Promise<FirebaseFirestore.WriteResult> {
    const docRef = this.getFirestoreInstance().collection(collectionName).doc(docId);
    return docRef.update(updateData);
  }

  public async deleteDocument(collectionName: string, docId: string): Promise<FirebaseFirestore.WriteResult> {
    const docRef = this.getFirestoreInstance().collection(collectionName).doc(docId);
    return docRef.delete();
  }

  public async queryCollection(
    collectionName: string,
    conditions: { field: string; operator: FirebaseFirestore.WhereFilterOp; value: string | null }[],
  ): Promise<FirebaseFirestore.QuerySnapshot> {
    let query: FirebaseFirestore.Query = this.getFirestoreInstance().collection(collectionName);
    conditions.forEach((cond) => {
      query = query.where(cond.field, cond.operator, cond.value);
    });
    return query.get();
  }
}
