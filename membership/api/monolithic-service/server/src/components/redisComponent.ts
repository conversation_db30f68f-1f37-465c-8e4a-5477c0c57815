import { createClient, RedisClientType } from 'redis';
import { injectable, singleton } from 'tsyringe';
import { config } from '../configs/config';
import { logger } from '../utils/middleware/loggerMiddleware';
import { InternalServerError } from '../errors/internalServerError';

@injectable()
@singleton()
export class RedisComponent {
  private client: RedisClientType;
  private DEFAULT_TTL_SECONDS = 60 * 60 * 24 * 30; // 30 days

  constructor() {
    this.client = createClient({ url: config.redisUrl });
    this.client.on('error', (err) => logger.error('[Redis]', err));
    this.client.on('reconnecting', () => logger.info('[Redis] reconnecting…'));
    this.client.connect().catch(logger.error);

    process.once('SIGTERM', async () => {
      try {
        await this.client.quit();
      } finally {
        process.exit(0);
      }
    });
  }

  async set<T>(key: string, value: T, ttlSeconds = this.DEFAULT_TTL_SECONDS): Promise<void> {
    try {
      const payload = JSON.stringify(value);
      ttlSeconds ? await this.client.set(key, payload, { EX: ttlSeconds }) : await this.client.set(key, payload);
    } catch (error) {
      logger.error({ msg: '[Redis] set', error });
      throw new InternalServerError('Failed to set value in Redis');
    }
  }

  async get<T>(key: string): Promise<T | null> {
    try {
      const raw = await this.client.get(key);
      return raw ? (JSON.parse(raw) as T) : null;
    } catch (error) {
      logger.error({ msg: '[Redis] get', error });
      throw new InternalServerError('Failed to get value from Redis');
    }
  }

  async del(key: string): Promise<number> {
    try {
      return await this.client.del(key);
    } catch (error) {
      logger.error({ msg: '[Redis] del', error });
      throw new InternalServerError('Failed to delete value from Redis');
    }
  }

  async getDel<T>(key: string): Promise<T | null> {
    try {
      const raw = await this.client.getDel(key);
      return raw ? (JSON.parse(raw) as T) : null;
    } catch (error) {
      logger.error({ msg: '[Redis] getDel', error });
      throw new InternalServerError('Failed to get & delete value from Redis');
    }
  }

  async delete(key: string): Promise<void> {
    await this.client.del(key);
  }

  async incrByWithExpire(key: string, amount: number, ttl = 60 * 60 * 24): Promise<void> {
    try {
      const pipeline = this.client.multi();
      pipeline.incrBy(key, amount);
      pipeline.expire(key, ttl);
      await pipeline.exec();
    } catch (error) {
      logger.error('[Redis] incrByWithExpire failed', { key, amount, error });
      throw error;
    }
  }
}
