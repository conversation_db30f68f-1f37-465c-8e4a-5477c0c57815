import { injectable, singleton } from 'tsyringe';
import { createClient, RedisClientType } from 'redis';
import { config } from '../configs/config';
import { logger } from '../utils/logger';
@injectable()
@singleton()
export class RedisComponent {
  private client: RedisClientType;
  private DEFAULT_TTL_SECONDS = 60 * 60 * 24 * 30; // 30 days

  constructor() {
    this.client = createClient({ url: config.redisUrl });
    this.client.on('error', (err) => logger.error('[Redis]', err));
    this.client.on('reconnecting', () => logger.info('[Redis] reconnecting…'));
    this.client.connect().catch(logger.error);

    process.once('SIGTERM', async () => {
      try {
        await this.client.quit();
      } finally {
        process.exit(0);
      }
    });
  }

  async set<T>(key: string, value: T, ttlSeconds = this.DEFAULT_TTL_SECONDS): Promise<void> {
    const payload = JSON.stringify(value);
    if (ttlSeconds) {
      await this.client.set(key, payload, { EX: ttlSeconds });
    } else {
      await this.client.set(key, payload);
    }
  }

  async get<T>(key: string): Promise<T | null> {
    const raw = await this.client.get(key);
    return raw ? (JSON.parse(raw) as T) : null;
  }

  async delete(key: string): Promise<void> {
    await this.client.del(key);
  }
}
