import { InternalServerError } from '../errors/internalServerError';
import { logger } from './middleware/loggerMiddleware';

export interface RequestOptions {
  retries?: number; // Maximum number of retries
  retryDelay?: number; // Retry interval in milliseconds
  retryHandler?: (error: any) => boolean;
}

export async function retryExecution<T>(
  primaryFunction: () => Promise<T>,
  options?: RequestOptions,
  fallbackFunction?: () => Promise<T>,
): Promise<T> {
  const retries = options?.retries ?? 3; // Default maximum of 3 retries
  const retryDelay = options?.retryDelay ?? 100; // 100 ms interval by default

  let lastError: any;
  let skipFallback = false;

  // helper function that waits for a specified amount of time
  function delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  async function executeWithRetry(func: () => Promise<T>, funcName: string): Promise<T> {
    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        return await func();
      } catch (error) {
        // log error
        logger.error({ trigger: 'retry', error });

        lastError = error;

        // If retry handler returns false, retry terminated
        if (options?.retryHandler && !options.retryHandler(error)) {
          logger.warn({ trigger: 'retryHandler', message: `${funcName} aborted due to retryHandler.` });
          // No fallback.
          skipFallback = true;
          throw error;
        }

        if (attempt >= retries) {
          logger.warn({ trigger: 'attempt >= retries', message: `${funcName} failed after ${retries} retries.` });
          throw error;
        }

        logger.info({
          trigger: 'retry',
          message: `Retry ${attempt + 1}/${retries} for ${funcName}. Retrying in ${retryDelay}ms...`,
        });
        await delay(retryDelay);
      }
    }
    throw lastError;
  }

  // retry with primary
  try {
    return await executeWithRetry(primaryFunction, 'Primary function');
  } catch (error) {
    logger.warn({
      trigger: 'primaryFunction',
      message: 'Primary function failed. Switching to fallback function...',
    });
    logger.warn({
      trigger: 'primaryFunction',
      current_error: error,
    });
  }

  // retry with fallback
  if (fallbackFunction && !skipFallback) {
    return executeWithRetry(fallbackFunction, 'Fallback function');
  }

  // Throws an error if all attempts fail
  throw new InternalServerError(`All attempts failed. Last error: ${lastError.message || lastError}`);
}
