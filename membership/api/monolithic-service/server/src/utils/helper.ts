import { z } from '@hono/zod-openapi';
import { TextLintEngine } from 'textlint';
import { getAddress, hexToBigInt } from 'viem';
import { createHash } from 'crypto';
import camelcaseKeys from 'camelcase-keys';
import snakecaseKeys from 'snakecase-keys';

export const isValidOpenSeaMetadata = (json: unknown): boolean => {
  // Define valid OpenSea metadata keys
  const validMetaDataKeys = ['image', 'image_data', 'external_url', 'description', 'name', 'attributes'];

  // Define the OpenSea metadata schema
  const openSeaMetadataSchema = z.object({
    image: z
      .string()
      .url()
      .optional()
      .nullable()
      .refine((url) => !url || url.startsWith('https://') || url.startsWith('ipfs://') || url.startsWith('ar://'), {
        message: 'Image must be a valid URL starting with https://, ipfs://, or ar://',
      }),
    image_data: z.string().min(1).nullable().optional(),
    external_url: z.string().url().nullable().optional(),
    description: z.string().min(1, { message: 'Description cannot be empty' }),
    name: z.string().min(1, { message: 'Name cannot be empty' }),
    attributes: z
      .array(
        z.object({
          trait_type: z.string().nullable().optional(),
          value: z.string().or(z.number()).nullable().optional(),
        }),
      )
      .nullable()
      .optional(),
  });
  try {
    const metadata = openSeaMetadataSchema.parse(json);

    // Check for unexpected keys
    const keys = Object.keys(metadata);
    const hasInvalidKeys = keys.some((key) => !validMetaDataKeys.includes(key));
    if (hasInvalidKeys) {
      return false;
    }

    // Ensure either image or image_data is present, but not both
    if (!metadata.image && !metadata.image_data) {
      return false;
    }
    if (metadata.image && metadata.image_data) {
      return false;
    }

    return true;
  } catch {
    return false;
  }
};

export const checkTypos = async (text: string): Promise<string[]> => {
  const engine = new TextLintEngine();
  const typos: string[] = [] as string[];
  const results = await engine.executeOnText(text);
  if (results[0].messages.length > 0) {
    results[0].messages.forEach((message) => {
      typos.push(`Position ${message.column}: ${message.message}`);
    });
  }
  return typos;
};

export const normalizeAddress = (address: string): string => {
  return getAddress('0x' + address.slice(-40));
};

export const decodeUint256Pair = (encodedData: string): { data1: bigint; data2: bigint } => {
  if (!encodedData.startsWith('0x') || encodedData.length !== 130) {
    throw new Error('Invalid encoded data format');
  }

  const idHex = encodedData.slice(2, 66);
  const valueHex = encodedData.slice(66);

  return {
    data1: hexToBigInt(`0x${idHex}`),
    data2: hexToBigInt(`0x${valueHex}`),
  };
};

export const sha256 = (text: string): string => {
  return createHash('sha256').update(text).digest('hex');
};

export const toCamelObject = <T>(obj: unknown): T =>
  camelcaseKeys(obj as Record<string, unknown>, { deep: true }) as unknown as T;

export const toSnakeObject = <T>(obj: T) => snakecaseKeys(obj as unknown as Record<string, unknown>, { deep: true });

export const chunkArray = <T>(arr: T[], size: number): T[][] => {
  const result: T[][] = [];
  for (let i = 0; i < arr.length; i += size) {
    result.push(arr.slice(i, i + size));
  }
  return result;
};

interface SettledResult {
  id: string;
  result: boolean;
  message: string;
}

export async function handleSettledResults<T>(
  items: T[],
  getId: (item: T) => string,
  executor: (item: T) => Promise<void>,
): Promise<SettledResult[]> {
  const results = await Promise.allSettled(
    items.map(async (item) => {
      await executor(item);
      return getId(item);
    }),
  );

  return results.map((result, i) => {
    const id = getId(items[i]);
    if (result.status === 'fulfilled') {
      return {
        id,
        result: true,
        message: 'success',
      };
    } else {
      return {
        id,
        result: false,
        message: result.reason instanceof Error ? result.reason.message : String(result.reason),
      };
    }
  });
}