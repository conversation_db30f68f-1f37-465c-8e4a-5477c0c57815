import { Context, Next } from 'hono';
import { basicAuth } from 'hono/basic-auth';
import { logger } from './loggerMiddleware';

// Basic認証のミドルウェア
export const basicAuthMiddleware = basicAuth({
  username: process.env.ADMIN_USERNAME || 'admin',
  password: process.env.ADMIN_PASSWORD || 'admin123',
  realm: 'Marbull Admin Area',
  hashFunction: (password: string) => password, // プレーンテキストの場合
});

// ログ付きのBasic認証ミドルウェア
export const basicAuthWithLogging = async (c: Context, next: Next) => {
  const authHeader = c.req.header('authorization');

  if (!authHeader || !authHeader.startsWith('Basic ')) {
    logger.warn('Basic auth failed: missing credentials', {
      path: c.req.path,
      ip: c.req.header('x-forwarded-for') || c.req.header('x-real-ip'),
      userAgent: c.req.header('user-agent'),
    });
  }

  try {
    await basicAuthMiddleware(c, next);

    logger.info('Basic auth success', {
      path: c.req.path,
      ip: c.req.header('x-forwarded-for') || c.req.header('x-real-ip'),
    });
  } catch (error) {
    logger.warn('Basic auth failed: invalid credentials', {
      path: c.req.path,
      ip: c.req.header('x-forwarded-for') || c.req.header('x-real-ip'),
      userAgent: c.req.header('user-agent'),
      error: error instanceof Error ? error.message : error,
    });
    throw error;
  }
};
