import { Context, <PERSON><PERSON><PERSON><PERSON><PERSON>, Next } from 'hono';
import { ForbiddenError } from '../../errors/forbiddenError';
import { logger } from './loggerMiddleware';
import { OriginValidator } from './services/originValidator';
import { RequestHelper } from './services/requestHelper';
import type { RootConfig } from './types/firebaseAuth.types';

/**
 * Middleware for validating request origins against configured patterns
 *
 * Supports various origin patterns:
 * - Exact matches: "https://example.com"
 * - Wildcard subdomains: "*.example.com"
 * - Full wildcards: "*"
 */
class OriginValidationMiddleware {
  private configMap = new Map<string, RootConfig>();
  private originValidator = new OriginValidator();
  private requestHelper = new RequestHelper();

  /**
   * Creates a key for the config map from path and method
   * @param path - The request path
   * @param method - The HTTP method
   * @returns The combined key
   */
  private createConfigKey(path: string, method?: string): string {
    return method ? `${method.toUpperCase()}:${path}` : path;
  }

  /**
   * Converts path parameters to regex pattern
   * @param path - The path with parameters like {id} or *
   * @returns The regex pattern
   */
  private pathToRegex(path: string): string {
    // Escape special regex characters except for our wildcards
    let regexPath = path
      .replace(/[.+?^${}()|[\]\\]/g, '\\$&')
      .replace(/\*/g, '([^/]+)') // Replace * with regex pattern
      .replace(/\\\{[^}]+\\\}/g, '([^/]+)'); // Replace {param} with regex pattern
    return regexPath;
  }

  /**
   * Finds the most specific configuration matching the request path and method
   * @param requestPath - The incoming request path
   * @param requestMethod - The HTTP method
   * @returns The matching configuration or null
   */
  private findMatchingConfig(requestPath: string, requestMethod: string): RootConfig | null {
    let bestMatch: RootConfig | null = null;
    let bestMatchSpecificity = -1;

    for (const [key, config] of this.configMap.entries()) {
      if (!config.allowedOrigins) continue;

      // Extract method and path from key
      const colonIndex = key.indexOf(':');
      const isMethodSpecific = colonIndex !== -1;
      const configMethod = isMethodSpecific ? key.substring(0, colonIndex) : null;
      const configPath = isMethodSpecific ? key.substring(colonIndex + 1) : key;

      // Skip if method is specified but doesn't match
      if (configMethod && configMethod !== requestMethod.toUpperCase()) {
        continue;
      }

      // Convert config path to regex and test
      const regexPattern = `^${this.pathToRegex(configPath)}$`;
      const regex = new RegExp(regexPattern);

      if (regex.test(requestPath)) {
        // Calculate specificity (more specific paths have higher priority)
        const specificity = this.calculatePathSpecificity(configPath, isMethodSpecific);

        if (specificity > bestMatchSpecificity) {
          bestMatch = config;
          bestMatchSpecificity = specificity;
        }
      }
    }

    return bestMatch;
  }

  /**
   * Calculates path specificity for prioritizing matches
   * @param path - The config path
   * @param hasMethod - Whether the config has a specific method
   * @returns Specificity score
   */
  private calculatePathSpecificity(path: string, hasMethod: boolean): number {
    let score = 0;

    // Method-specific configs have higher priority
    if (hasMethod) score += 1000;

    // Longer paths are more specific
    score += path.length * 100;

    // Paths with fewer wildcards are more specific
    const wildcardCount = (path.match(/\*/g) || []).length;
    score -= wildcardCount * 500;

    // Exact segments (without wildcards) add to specificity
    const segments = path.split('/');
    const exactSegments = segments.filter((s) => !s.includes('*')).length;
    score += exactSegments * 200;

    return score;
  }

  /**
   * Main middleware handler for origin validation
   * @throws {ForbiddenError} If origin is not allowed
   */
  middleware: MiddlewareHandler = async (c: Context, next: Next) => {
    const metadata = this.requestHelper.getRequestMetadata(c);
    const { requestPath } = metadata;
    const requestMethod = c.req.method;

    // Find matching configuration for this path and method
    const matchingConfig = this.findMatchingConfig(requestPath, requestMethod);
    if (!matchingConfig || !matchingConfig.allowedOrigins?.length) {
      // No origin restrictions for this path
      await next();
      return;
    }

    // Extract origin from headers
    const originToCheck = this.originValidator.extractOriginDomain(c.req.header('origin'), c.req.header('referer'));
    // Validate origin against allowed patterns
    const isValidOrigin = this.originValidator.validateOrigin(originToCheck, matchingConfig.allowedOrigins);

    if (!isValidOrigin) {
      logger.warn({
        message: 'Origin validation failed',
        data: {
          ...metadata,
          method: requestMethod,
          origin: originToCheck,
          allowedOrigins: matchingConfig.allowedOrigins,
        },
      });

      throw new ForbiddenError('Access from this origin is not allowed');
    }

    await next();
  };

  /**
   * Adds or updates path configurations
   * @param configs - Array of path configurations with origin restrictions
   */
  addPathConfig(configs: RootConfig[]) {
    this.configMap.clear();
    for (const config of configs) {
      if (config.allowedOrigins?.length) {
        // Replace path parameters like {id} with wildcards
        const normalizedPath = config.path.replace(/\{[^}]+\}/g, '*');
        const key = this.createConfigKey(normalizedPath, config.method);
        this.configMap.set(key, { ...config, path: normalizedPath });
      }
    }
  }

  /**
   * Removes a specific path configuration
   * @param path - Path to remove from configurations
   * @param method - Optional HTTP method
   */
  removePathConfig(path: string, method?: string) {
    // Normalize path before creating key
    const normalizedPath = path.replace(/\{[^}]+\}/g, '*');
    const key = this.createConfigKey(normalizedPath, method);
    this.configMap.delete(key);
  }

  /**
   * Gets a copy of current configurations
   * @returns Array of current path configurations
   */
  getConfigurations(): RootConfig[] {
    return Array.from(this.configMap.values());
  }
}

// Singleton instance
export const originValidationMiddleware = new OriginValidationMiddleware();
export { OriginValidationMiddleware };
