import { OpenAPIHono } from '@hono/zod-openapi';
import { ApiAccessLevelTag } from '../../enum/apiTag';
import { firebaseAuthMiddleware, RootConfig } from './firebaseAuthAccessMiddleware';
import { logger } from './loggerMiddleware';
import { originValidationMiddleware } from './originValidationMiddleware';

// OpenAPI Operation型定義
interface OpenAPIOperation {
  operationId?: string;
  tags?: string[];
  summary?: string;
  description?: string;
  [key: string]: unknown;
}

// OpenAPI PathItem型定義
interface OpenAPIPathItem {
  get?: OpenAPIOperation;
  post?: OpenAPIOperation;
  put?: OpenAPIOperation;
  delete?: OpenAPIOperation;
  patch?: OpenAPIOperation;
  head?: OpenAPIOperation;
  options?: OpenAPIOperation;
  [key: string]: unknown;
}

interface PathConfigExtractor {
  extractFromOpenAPI(app: OpenAPIHono): RootConfig[];
}

export class OpenAPIConfigExtractor implements PathConfigExtractor {
  extractFromOpenAPI(app: OpenAPIHono): RootConfig[] {
    const configs: RootConfig[] = [];

    try {
      // OpenAPI仕様を取得
      const openAPISpec = app.getOpenAPIDocument({
        openapi: '3.0.0',
        info: {
          version: '1.0.0',
          title: 'API',
        },
      });

      // パスごとに設定を生成
      Object.entries(openAPISpec.paths || {}).forEach(([path, pathItem]) => {
        if (pathItem) {
          const typedPathItem = pathItem as OpenAPIPathItem;

          // 各HTTPメソッドについて処理
          const methods = ['get', 'post', 'put', 'delete', 'patch', 'head', 'options'] as const;

          methods.forEach((method) => {
            const operation = typedPathItem[method];
            if (operation && operation.operationId) {
              // タグから設定を決定
              const tags = operation.tags || [];
              const config = this.createConfigFromTags(path, tags, method.toUpperCase());

              if (config) {
                configs.push(config);
              }
            }
          });
        }
      });

      logger.info({
        message: 'Extracted dynamic configurations from OpenAPI',
        data: {
          count: configs.length,
          paths: configs,
        },
      });

      return configs;
    } catch (error) {
      logger.error({
        message: 'Failed to extract config from OpenAPI',
        data: {
          error: error instanceof Error ? error.message : String(error),
        },
      });
      return [];
    }
  }

  private createConfigFromTags(path: string, tags: string[], method: string): RootConfig | null {
    // タグベースでの設定生成例
    const config: RootConfig = {
      path: path,
      method: method,
      isAccessTokenOptional: false,
    };

    // タグによる設定の決定
    if (tags.includes(ApiAccessLevelTag.ADMIN)) {
      config.requireServiceId = true;
      config.allowedOrigins = ['*'];
    } else if (tags.includes(ApiAccessLevelTag.IFRAME)) {
      config.requireAccountId = false;
      config.requireServiceId = false;
      config.allowedOrigins = ['static.*.marbullx.com'];
    } else if (tags.includes(ApiAccessLevelTag.USER)) {
      config.requireAccountId = false;
      config.requireServiceId = false;
      config.allowedOrigins = ['*.marbullx.com', 'liff-membership.ngrok.dev', '*.frontend.connect.marbullx.dev'];
    } else if (tags.includes(ApiAccessLevelTag.ACCOUNT)) {
      config.requireAccountId = true;
      config.requireServiceId = true;
      config.allowedOrigins = ['*.marbullx.com', 'liff-membership.ngrok.dev', '*.frontend.connect.marbullx.dev'];
    } else if (tags.includes(ApiAccessLevelTag.GUEST)) {
      config.isAccessTokenOptional = true;
      config.requireAccountId = false;
      config.requireServiceId = false;
      config.allowedOrigins = ['*'];
    } else if (tags.includes(ApiAccessLevelTag.SYSTEM)) {
      config.isAccessTokenOptional = true;
      config.requireAccountId = false;
      config.requireServiceId = false;
      config.allowedOrigins = ['*'];
    }

    return config;
  }
}

/**
 * 動的設定を管理するクラス
 */
export function configureMiddlewares(route: OpenAPIHono, app: OpenAPIHono): void {
  const dynamicConfigs = new OpenAPIConfigExtractor().extractFromOpenAPI(route);

  originValidationMiddleware.addPathConfig(dynamicConfigs);
  firebaseAuthMiddleware.addPathConfig(dynamicConfigs);

  // 既存のミドルウェアに動的設定を追加
  app.use('*', originValidationMiddleware.middleware);
  app.use('*', firebaseAuthMiddleware.middleware);
}
