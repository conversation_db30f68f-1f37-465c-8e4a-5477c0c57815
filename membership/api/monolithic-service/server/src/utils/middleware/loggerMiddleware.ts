import { AsyncLocalStorage } from 'async_hooks';
import { MiddlewareHandler } from 'hono';
import pino from 'pino';

const asyncLocalStorage = new AsyncLocalStorage<Map<string, string>>();

function getRequestId(): string | undefined {
  const store = asyncLocalStorage.getStore();
  return store?.get('requestId');
}

const labels = pino().levels.labels;
export const logger = pino({
  mixin: (_, level) => {
    return { request_id: getRequestId(), severity: labels[level].toUpperCase() };
  },
});

export const loggerMiddleware: MiddlewareHandler = async (c, next) => {
  const requestId = crypto.randomUUID();

  await asyncLocalStorage.run(new Map(), async () => {
    const start = Date.now();
    const store = asyncLocalStorage.getStore();
    if (store) store.set('requestId', requestId);

    logger.info({
      message: `--> ${c.req.method} ${c.req.path}`,
      requestId,
      httpRequest: {
        requestMethod: c.req.method,
        requestUrl: c.req.url,
      },
    });

    await next();

    const duration = Date.now() - start;
    const responseClone = c.res.clone();

    let responseBody: unknown = undefined;
    if (responseClone.status >= 400) {
      try {
        responseBody = await responseClone.json();
      } catch {
        responseBody = '[unreadable error body]';
      }
    }

    logger.info({
      message: `<-- ${c.req.method} ${c.req.path} ${duration}ms`,
      requestId,
      jsonPayload:
        responseClone.status >= 400
          ? { error: responseBody }
          : { res: 'success' },
    });
  });
};