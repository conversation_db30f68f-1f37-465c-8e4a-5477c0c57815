import { Context } from 'hono';
import type { RequestMetadata } from '../types/originValidation.types';

/**
 * Helper class for extracting request metadata
 */
export class RequestHelper {
  /**
   * Extracts metadata from the request context
   * @param c - Hono context
   * @returns Request metadata object
   */
  getRequestMetadata(c: Context): RequestMetadata {
    return {
      requestPath: c.req.path,
      origin: c.req.header('origin'),
      userAgent: c.req.header('user-agent'),
      ip: this.getClientIp(c),
    };
  }

  /**
   * Extracts client IP from various headers
   * @param c - Hono context
   * @returns Client IP address or undefined
   */
  private getClientIp(c: Context): string | undefined {
    return c.req.header('x-forwarded-for') || c.req.header('x-real-ip') || undefined;
  }
}