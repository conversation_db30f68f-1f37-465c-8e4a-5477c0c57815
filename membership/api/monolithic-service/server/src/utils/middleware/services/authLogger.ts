import { Context } from 'hono';
import { logger } from '../loggerMiddleware';

interface LogContext {
  requestPath: string;
  method?: string;
  userId?: string;
  email?: string;
  accountId?: string;
  serviceId?: string;
}

export class AuthLogger {
  private getRequestMetadata(c: Context) {
    return {
      userAgent: c.req.header('user-agent'),
      ip: c.req.header('x-forwarded-for') || c.req.header('x-real-ip'),
    };
  }

  logAuthFailure(reason: string, context: LogContext, c: Context, additionalData?: Record<string, unknown>) {
    logger.warn({
      message: `Firebase auth failed: ${reason}`,
      data: {
        ...context,
        ...this.getRequestMetadata(c),
        ...additionalData,
      },
    });
  }

  logAuthSuccess(context: LogContext) {
    logger.info({
      message: 'Firebase auth success',
      data: context,
    });
  }

  logInfo(message: string, data: Record<string, unknown>) {
    logger.info({
      message,
      data,
    });
  }

  logError(message: string, error: unknown, additionalData?: Record<string, unknown>) {
    logger.error({
      message,
      data: {
        error: error instanceof Error ? error.message : error,
        ...additionalData,
      },
    });
  }
}
