import admin from 'firebase-admin';
import type { ExtendedDecodedIdToken } from '../types/firebaseAuth.types';
import { logger } from '../loggerMiddleware';

export class TokenValidator {
  async validateIdToken(idToken: string): Promise<ExtendedDecodedIdToken | null> {
    try {
      const decodedToken = await admin.auth().verifyIdToken(idToken);
      return decodedToken as ExtendedDecodedIdToken;
    } catch (error) {
      logger.warn({
        message: 'Firebase ID token validation failed',
        data: {
          error: error instanceof Error ? error.message : error,
          idToken: idToken.substring(0, 20) + '...',
        },
      });
      return null;
    }
  }

  extractToken(authHeader: string | undefined): string | null {
    if (!authHeader) return null;

    if (authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }

    return authHeader;
  }
}
