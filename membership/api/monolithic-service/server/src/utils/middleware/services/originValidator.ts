import { logger } from '../loggerMiddleware';

/**
 * Origin validation service for checking request origins against allowed patterns
 */
export class OriginValidator {
  /**
   * Validates if the given origin matches any of the allowed origin patterns
   * @param origin - The origin to validate
   * @param allowedOrigins - Array of allowed origin patterns
   * @returns True if origin is allowed, false otherwise
   */
  validateOrigin(origin: string | undefined, allowedOrigins: string[]): boolean {
    if (allowedOrigins.find((pattern) => pattern === '*')?.length) {
      return true;
    }

    if (!origin || !allowedOrigins.length) {
      return false;
    }

    return allowedOrigins.some((pattern) => this.matchesPattern(origin, pattern));
  }

  /**
   * Checks if an origin matches a specific pattern
   * Supports:
   * - Exact matches: "https://example.com"
   * - Wildcard subdomains: "*.example.com"
   * - Full wildcards: "*"
   * @param originDomain - The origin to check
   * @param pattern - The pattern to match against
   * @returns True if origin matches pattern
   */
  private matchesPattern(originDomain: string, pattern: string): boolean {
    // Full wildcard
    if (pattern === '*') {
      return true;
    }

    // Wildcard subdomain pattern
    if (pattern.startsWith('*.')) {
      const domain = pattern.substring(2);
      // Check if origin hostname ends with the domain
      return originDomain === domain || originDomain.endsWith('.' + domain);
    }

    // Exact match
    return originDomain === pattern;
  }

  /**
   * Extracts origin from various request headers
   * @param originHeader - Origin header value
   * @param refererHeader - Referer header value
   * @returns Extracted origin or undefined
   */
  extractOriginDomain(originHeader?: string, refererHeader?: string): string | undefined {
    if (originHeader) {
      return new URL(originHeader).hostname;
    }

    if (refererHeader) {
      try {
        return new URL(refererHeader).hostname;
      } catch {
        return undefined;
      }
    }

    return undefined;
  }
}
