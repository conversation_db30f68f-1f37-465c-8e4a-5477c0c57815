import { cors } from 'hono/cors';

const corsSetting = cors({
  origin: (origin: string) => {
    const allowedOrigins = JSON.parse(process.env.CORS_DOMAINS ?? '[]') as string[];
    for (const allowedOrigin of allowedOrigins) {
      if (allowedOrigin.startsWith('*.')) {
        if (origin.endsWith(allowedOrigin.replace('*.', ''))) return origin;
      } else {
        if (origin.endsWith(allowedOrigin)) return origin;
      }
    }
    return null;
  },
  allowHeaders: ['Service-Id-Header', 'Tenant-Id-Header', 'Line-Id-Token-Header', 'Authorization', 'Content-Type'],
  allowMethods: ['POST', 'GET', 'OPTIONS', 'PUT', 'DELETE'],
  maxAge: 600,
  credentials: true,
});

export { corsSetting }