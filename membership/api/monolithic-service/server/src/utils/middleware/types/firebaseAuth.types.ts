import admin from 'firebase-admin';

export interface ExtendedDecodedIdToken extends admin.auth.DecodedIdToken {
  access?: {
    account_id: string;
    service_id: string;
  };
  provider?: {
    name: string;
    uid: string;
  };
}

export interface RootConfig {
  path: string;
  method: string;
  requiredClaims?: Record<string, string | number | boolean>;
  isAccessTokenOptional?: boolean;
  requireAccountId?: boolean;
  requireServiceId?: boolean;
  allowedOrigins?: string[];
  strictOrigin?: boolean;
}

export interface UserInfo {
  uid: string;
  account_id?: string;
  service_id?: string;
}

export interface ValidationResult {
  isValid: boolean;
  reason?: string;
}

export interface AccessClaimsValidationResult extends ValidationResult {
  accountId?: string;
  serviceId?: string;
}
