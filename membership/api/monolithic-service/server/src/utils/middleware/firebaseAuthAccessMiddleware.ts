import { Context, <PERSON><PERSON><PERSON><PERSON><PERSON>, Next } from 'hono';
import { container } from 'tsyringe';
import { ForbiddenError } from '../../errors/forbiddenError';
import { UnauthorizedError } from '../../errors/unauthorizedError';
import { AccountRepository } from '../../repositories/accountRepository';
import { ServiceInfoRepository } from '../../repositories/serviceInfoRepository';
import { AuthLogger } from './services/authLogger';
import { TokenValidator } from './services/tokenValidator';
import type {
  AccessClaimsValidationResult,
  ExtendedDecodedIdToken,
  RootConfig,
  UserInfo,
  ValidationResult,
} from './types/firebaseAuth.types';
import { logger } from './loggerMiddleware';

/**
 * Firebase Authentication Access Middleware
 *
 * Provides route-based authentication and authorization using Firebase ID tokens.
 * Supports configurable path-specific requirements for claims, service IDs, and account IDs.
 */
class FirebaseAuthAccessMiddleware {
  private configMap = new Map<string, RootConfig>();
  private tokenValidator = new TokenValidator();
  private authLogger = new AuthLogger();

  /**
   * Creates a key for the config map from path and method
   * @param path - The request path
   * @param method - The HTTP method
   * @returns The combined key
   */
  private createConfigKey(path: string, method: string): string {
    return `${method.toUpperCase()}:${path}`;
  }

  /**
   * Converts path parameters to regex pattern
   * @param path - The path with parameters like {id} or *
   * @returns The regex pattern
   */
  private pathToRegex(path: string): string {
    // Escape special regex characters except for our wildcards
    let regexPath = path
      .replace(/[.+?^${}()|[\]\\]/g, '\\$&')
      .replace(/\*/g, '([^/]+)') // Replace * with regex pattern
      .replace(/\\\{[^}]+\\\}/g, '([^/]+)'); // Replace {param} with regex pattern
    return regexPath;
  }

  /**
   * Finds the most specific configuration matching the request path and method
   * @param requestPath - The incoming request path
   * @param requestMethod - The HTTP method (GET, POST, etc.)
   * @returns The matching configuration or null if none found
   */
  private findMatchingConfig(requestPath: string, requestMethod: string): RootConfig | null {
    let bestMatch: RootConfig | null = null;
    let bestMatchSpecificity = -1;

    for (const [key, config] of this.configMap.entries()) {
      // Extract method and path from key
      const colonIndex = key.indexOf(':');
      const configMethod = key.substring(0, colonIndex);
      const configPath = key.substring(colonIndex + 1);

      // Skip if method doesn't match
      if (configMethod !== requestMethod.toUpperCase()) {
        continue;
      }

      // Convert config path to regex and test
      const regexPattern = `^${this.pathToRegex(configPath)}$`;
      const regex = new RegExp(regexPattern);

      if (regex.test(requestPath)) {
        // Calculate specificity (more specific paths have higher priority)
        const specificity = this.calculatePathSpecificity(configPath);

        if (specificity > bestMatchSpecificity) {
          bestMatch = config;
          bestMatchSpecificity = specificity;
        }
      }
    }

    return bestMatch;
  }

  /**
   * Calculates path specificity for prioritizing matches
   * @param path - The config path
   * @returns Specificity score
   */
  private calculatePathSpecificity(path: string): number {
    let score = 0;

    // Longer paths are more specific
    score += path.length * 100;

    // Paths with fewer wildcards are more specific
    const wildcardCount = (path.match(/\*/g) || []).length;
    score -= wildcardCount * 1000;

    // Exact segments (without wildcards) add to specificity
    const segments = path.split('/');
    const exactSegments = segments.filter((s) => !s.includes('*')).length;
    score += exactSegments * 500;

    return score;
  }

  /**
   * Validates required claims against the configuration
   * @param claims - Decoded token claims
   * @param config - Path configuration
   * @returns Validation result with reason if failed
   */
  private validateClaims(claims: ExtendedDecodedIdToken, config: RootConfig): ValidationResult {
    // 必須クレームの検証
    if (config.requiredClaims) {
      for (const [key, expectedValue] of Object.entries(config.requiredClaims)) {
        const claimValue = (claims as Record<string, unknown>)[key];
        if (claimValue !== expectedValue) {
          return {
            isValid: false,
            reason: `Required claim '${key}' does not match expected value`,
          };
        }
      }
    }
    return { isValid: true };
  }

  /**
   * Validates service ID existence in the database
   * @param serviceId - Service ID from token claims
   * @param config - Path configuration
   * @returns Validation result
   */
  private async validateServiceId(serviceId: string | undefined, config: RootConfig): Promise<ValidationResult> {
    const requireServiceId = config.requireServiceId !== false;

    if (!requireServiceId || config.isAccessTokenOptional) {
      return { isValid: true };
    }

    if (!serviceId || typeof serviceId !== 'string') {
      return {
        isValid: false,
        reason: 'Missing or invalid service_id in access claims',
      };
    }

    const serviceInfoRepository = container.resolve(ServiceInfoRepository);
    const serviceExists = await serviceInfoRepository.getServiceById(serviceId);

    if (!serviceExists) {
      return {
        isValid: false,
        reason: `Service not found: ${serviceId}`,
      };
    }

    return { isValid: true };
  }

  /**
   * Validates account ID and its association with the service
   * @param accountId - Account ID from token claims
   * @param serviceId - Service ID for account validation
   * @param config - Path configuration
   * @returns Validation result
   */
  private async validateAccountId(
    accountId: string | undefined,
    serviceId: string | undefined,
    config: RootConfig,
  ): Promise<ValidationResult> {
    if (!config.requireAccountId || config.isAccessTokenOptional) {
      return { isValid: true };
    }

    if (!accountId || typeof accountId !== 'string') {
      return {
        isValid: false,
        reason: 'Missing or invalid account_id in access claims',
      };
    }

    if (!serviceId) {
      return {
        isValid: false,
        reason: 'service_id is required for account validation',
      };
    }

    const accountRepository = container.resolve(AccountRepository);
    const accountExists = await accountRepository.selectAccountById(accountId, serviceId);

    if (!accountExists) {
      return {
        isValid: false,
        reason: `Account not found: ${accountId}`,
      };
    }

    if (accountExists.service_id !== serviceId) {
      return {
        isValid: false,
        reason: `Account ${accountId} does not belong to service ${serviceId}`,
      };
    }

    return { isValid: true };
  }

  /**
   * Validates access claims including service and account IDs
   * @param decodedToken - Decoded Firebase token
   * @param config - Path configuration
   * @param requestPath - Request path for logging
   * @returns Validation result with extracted IDs if successful
   */
  private async validateAccessClaims(
    decodedToken: ExtendedDecodedIdToken,
    config: RootConfig,
    requestPath: string,
  ): Promise<AccessClaimsValidationResult> {
    try {
      const access = decodedToken.access;

      if (!access || typeof access !== 'object') {
        return {
          isValid: false,
          reason: 'Missing access claims in token',
        };
      }

      const serviceId = access.service_id;
      const accountId = access.account_id;

      const serviceValidation = await this.validateServiceId(serviceId, config);
      if (!serviceValidation.isValid) {
        return serviceValidation;
      }

      const accountValidation = await this.validateAccountId(accountId, serviceId, config);
      if (!accountValidation.isValid) {
        return accountValidation;
      }

      return {
        isValid: true,
        accountId,
        serviceId,
      };
    } catch (error) {
      this.authLogger.logError('Error validating access claims', error, {
        userId: decodedToken.uid,
        requestPath,
      });

      return {
        isValid: false,
        reason: 'Internal error during access claims validation',
      };
    }
  }

  /**
   * Main middleware handler for Firebase authentication
   * Sets 'user' context with validated user information on success
   */
  middleware: MiddlewareHandler = async (c: Context, next: Next) => {
    // Context拡張を適用
    const requestPath = c.req.path;
    const requestMethod = c.req.method;
    const authHeader = c.req.header('X-Forwarded-Authorization') ?? c.req.header('Authorization');

    // マッチする設定を検索
    const matchingConfig = this.findMatchingConfig(requestPath, requestMethod);

    if (!matchingConfig || matchingConfig.isAccessTokenOptional) {
      // 設定がない場合はそのまま通す
      await next();
      return;
    }

    const idToken = this.tokenValidator.extractToken(authHeader);
    if (!idToken) {
      this.authLogger.logAuthFailure('missing token', { requestPath, method: requestMethod }, c);
      throw new UnauthorizedError('Authorization token is required');
    }

    // アカウントIDまたはサービスIDが必要な場合、トークンの検証を行う
    const decodedToken = await this.tokenValidator.validateIdToken(idToken);
    if (!decodedToken) {
      this.authLogger.logAuthFailure('invalid token', { requestPath, method: requestMethod }, c);
      throw new UnauthorizedError('Invalid authorization token');
    }

    // クレームの検証
    const validation = this.validateClaims(decodedToken, matchingConfig);
    if (!validation.isValid) {
      this.authLogger.logAuthFailure(
        'insufficient permissions',
        {
          requestPath,
          userId: decodedToken.uid,
          email: decodedToken.email,
        },
        c,
        { reason: validation.reason },
      );

      throw new ForbiddenError(`Access denied: insufficient permissions - ${validation.reason}`);
    }

    logger.info({message: 'ID token validated successfully', data: { decodedToken }});
    // クレームからアクセス情報を抽出と検証
    const claimsValidation = await this.validateAccessClaims(decodedToken, matchingConfig, requestPath);
    if (!claimsValidation.isValid) {
      this.authLogger.logAuthFailure(
        'invalid access claims',
        {
          requestPath,
          userId: decodedToken.uid,
        },
        c,
        { reason: claimsValidation.reason },
      );

      throw new ForbiddenError(`Access denied: invalid access claims - ${claimsValidation.reason}`);
    }

    // 認証成功 - ユーザー情報をcontextに追加
    const userInfo = {
      uid: decodedToken.uid,
      account_id: claimsValidation.accountId,
      service_id: claimsValidation.serviceId,
    };

    this.authLogger.logAuthSuccess({
      requestPath,
      userId: decodedToken.uid,
      email: decodedToken.email,
      accountId: claimsValidation.accountId,
      serviceId: claimsValidation.serviceId,
    });

    c.set('user', userInfo);

    await next();
  };

  /**
   * Adds or updates path configurations
   * @param configs - Array of path configurations
   */
  addPathConfig(configs: RootConfig[]) {
    this.configMap.clear();
    for (const config of configs) {
      // Replace path parameters like {id} with wildcards
      const normalizedPath = config.path.replace(/\{[^}]+\}/g, '*');
      const key = this.createConfigKey(normalizedPath, config.method);
      this.configMap.set(key, { ...config, path: normalizedPath });
    }
  }

  /**
   * Removes a specific path configuration
   * @param path - Path to remove from configurations
   * @param method - Optional HTTP method
   */
  removePathConfig(path: string, method: string) {
    // Normalize path before creating key
    const normalizedPath = path.replace(/\{[^}]+\}/g, '*');
    const key = this.createConfigKey(normalizedPath, method);
    this.configMap.delete(key);
  }

  /**
   * Gets a copy of current configurations
   * @returns Array of current path configurations
   */
  getConfigurations(): RootConfig[] {
    return Array.from(this.configMap.values());
  }
}

/**
 * Safely extracts user information from Hono context
 * @param c - Hono context object
 * @returns User information if available, null otherwise
 */
export function parseCustomToken(c: Context): UserInfo | null {
  try {
    return (c.get('user') as UserInfo) || null;
  } catch {
    return null;
  }
}

// シングルトンインスタンス
export const firebaseAuthMiddleware = new FirebaseAuthAccessMiddleware();
export { ExtendedDecodedIdToken, FirebaseAuthAccessMiddleware, RootConfig, UserInfo };
