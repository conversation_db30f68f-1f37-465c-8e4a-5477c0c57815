import { OpenAPIObject, OperationObject, PathsObject, TagObject } from 'openapi3-ts/oas30';
import { ApiAccessLevelTag } from '../../enum/apiTag';

export function filterOpenAPIDocByTags(doc: OpenAPIObject, allowedTags: string[]): OpenAPIObject {
  const filteredPaths: PathsObject = {};

  for (const [path, methods] of Object.entries(doc.paths)) {
    const filteredMethods: Record<string, OperationObject> = {};

    for (const [method, operation] of Object.entries(methods)) {
      const op = operation as OperationObject;

      if (!op.tags || op.tags.some((tag) => allowedTags.includes(tag))) {
        filteredMethods[method] = op;
      }
    }

    if (Object.keys(filteredMethods).length > 0) {
      filteredPaths[path] = filteredMethods;
    }
  }

  const accessTags: string[] = Object.values(ApiAccessLevelTag);
  const filteredTagDefs: TagObject[] | undefined = doc.tags?.filter((tag) => !accessTags.includes(tag.name));

  return {
    ...doc,
    paths: filteredPaths,
    tags: filteredTagDefs,
  };
}
