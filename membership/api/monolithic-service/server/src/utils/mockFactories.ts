// test/testUtils.ts
import { AlchemyComponent } from '../../src/components/alchemyComponent';
import { FirebaseComponent } from '../../src/components/firebaseComponent';
import { TransactionComponent } from '../../src/components/transactionComponent';
import { ViemComponent } from '../../src/components/viemComponent';
import { AccountRepository } from '../../src/repositories/accountRepository';
import { ActionRepository } from '../../src/repositories/actionRepository';
import { AttemptTransactionsRepository } from '../../src/repositories/attemptTransactionsRepository';
import { DeliveryNftsFirestoreRepository } from '../../src/repositories/deliveryNftsFirestoreRepository';
import { NftBaseMetadatasRepository } from '../../src/repositories/nftBaseMetadatasRepository';
import { NftContractsRepository } from '../../src/repositories/nftContractsRepository';
import { NftsFirestoreRepository } from '../../src/repositories/nftsFirestoreRepository';
import { ServiceInfoRepository } from '../../src/repositories/serviceInfoRepository';
import { TransactionQueuesRepository } from '../../src/repositories/transactionQueuesRepository';
import { TransactionsRepository } from '../../src/repositories/transactionsRepository';
import { VaultKeyRepository } from '../../src/repositories/vaultKeyRepository';
import { VaultTransactionQueuesRepository } from '../../src/repositories/vaultTransactionQueuesRepository';
import { TransactionService } from '../../src/services/transactionService';
import { NftTransactionUpdateService } from '../../src/services/transactionUpdateService';
import { NftContractTypesRepository } from '../repositories/nftContractTypesRepository';
import { NftMetadatasRepository } from '../repositories/nftMetadatasRepository';
import { ShareBackupRepository } from '../repositories/shareBackupRepository';
import { TokenBoundAccountImplementationRepository } from '../repositories/tokenBoundAccountImplementationsRepository';
import { TokenBoundAccountRegistryAddressRepository } from '../repositories/tokenBoundAccountRegistryAddressRepository';
import { UserRepository } from '../repositories/userRepository';
import { BulkMintService } from '../services/bulkMintService';
import { MetadataService } from '../services/metadataService';
import { NftMintService } from '../services/nftMintService';
import { NftRegisterService } from '../services/nftRegisterService';
import { UserService } from '../services/userService';
import { WebhookService } from '../services/webhookService';

// @ts-ignore
import { jest } from '@jest/globals';

function createMockInstance<T extends object>(instance: T): jest.Mocked<T> {
  const mock = {} as jest.Mocked<T>;
  const proto = Object.getPrototypeOf(instance);
  Object.getOwnPropertyNames(proto).forEach((key) => {
    if (key !== 'constructor' && typeof (instance as any)[key] === 'function') {
      (mock as any)[key] = jest.fn();
    }
  });
  return mock;
}

//--------------------------------Components--------------------------------
export function createMockFirebaseComponent(): jest.Mocked<FirebaseComponent> {
  return createMockInstance(new FirebaseComponent());
}
export function createMockViemComponent(): jest.Mocked<ViemComponent> {
  return createMockInstance(new ViemComponent());
}
export function createMockTransactionComponent(): jest.Mocked<TransactionComponent> {
  return createMockInstance(new TransactionComponent(createMockVaultKeyRepository()));
}
export function createMockAlchemyComponent(): jest.Mocked<AlchemyComponent> {
  return createMockInstance(new AlchemyComponent());
}

//--------------------------------Services--------------------------------
export function createMockTransactionService(
  mockTransactionComponent = createMockTransactionComponent(),
  mockAttemptTransactionsRepository = createMockAttemptTransactionsRepository(),
  mockTransactionsRepository = createMockTransactionsRepository(),
  mockTransactionQueuesRepository = createMockTransactionQueuesRepository(),
  mockServiceInfoRepository = createMockServiceInfoRepository(),
  mockNftContractsRepository = createMockNftContractsRepository(),
): jest.Mocked<TransactionService> {
  return createMockInstance(
    new TransactionService(
      mockTransactionComponent,
      mockAttemptTransactionsRepository,
      mockTransactionsRepository,
      mockTransactionQueuesRepository,
      mockServiceInfoRepository,
      mockNftContractsRepository,
    ),
  );
}

export function createMockNftTransactionUpdateService(
  mockAccountRepository = createMockAccountRepository(),
  mockNftsFirestoreRepository = createMockNftsFirestoreRepository(),
  mockNftContractsRepository = createMockNftContractsRepository(),
  mockNftBaseMetadatasRepository = createMockNftBaseMetadatasRepository(),
  mockDeliveryNftsFirestoreRepository = createMockDeliveryNftsFirestoreRepository(),
  mockServiceInfoRepository = createMockServiceInfoRepository(),
  mockViemComponent = createMockViemComponent(),
  mockTransactionComponent = createMockTransactionComponent(),
  mockTransactionsRepository = createMockTransactionsRepository(),
  mockTransactionQueuesRepository = createMockTransactionQueuesRepository(),
) {
  return createMockInstance(
    new NftTransactionUpdateService(
      mockAccountRepository,
      mockNftsFirestoreRepository,
      mockNftContractsRepository,
      mockNftBaseMetadatasRepository,
      mockDeliveryNftsFirestoreRepository,
      mockServiceInfoRepository,
      mockViemComponent,
      mockTransactionComponent,
      mockTransactionsRepository,
      mockTransactionQueuesRepository,
    ),
  );
}

export function createMockNftMintService(
  mockNftContractsRepository = createMockNftContractsRepository(),
  mockTransactionQueuesRepository = createMockTransactionQueuesRepository(),
  mockVaultKeyRepository = createMockVaultKeyRepository(),
  mockServiceInfoRepository = createMockServiceInfoRepository(),
  mockDeliveryNftsFirestoreRepository = createMockDeliveryNftsFirestoreRepository(),
) {
  return createMockInstance(
    new NftMintService(
      mockNftContractsRepository,
      mockTransactionQueuesRepository,
      mockVaultKeyRepository,
      mockServiceInfoRepository,
      mockDeliveryNftsFirestoreRepository,
    ),
  );
}

export function createMockBulkMintService(
  mockTransactionService = createMockTransactionService(),
  mockNftMintService = createMockNftMintService(),
  mockNftContractsRepository = createMockNftContractsRepository(),
  mockTransactionQueuesRepository = createMockTransactionQueuesRepository(),
  mockTransactionsRepository = createMockTransactionsRepository(),
  mockVaultKeyRepository = createMockVaultKeyRepository(),
  mockServiceInfoRepository = createMockServiceInfoRepository(),
  mockTokenBoundAccountImplementationRepository = createMockTokenBoundAccountImplementationRepository(),
  mockTokenBoundAccountRegistryAddressRepository = createMockTokenBoundAccountRegistryAddressRepository(),
  mockAccountRepository = createMockAccountRepository(),
  mockDeliveryNftsFirestoreRepository = createMockDeliveryNftsFirestoreRepository(),
  mockNftBaseMetadatasRepository = createMockNftBaseMetadatasRepository(),
  mockMetadataService = createMockMetadataService(),
) {
  return createMockInstance(
    new BulkMintService(
      mockTransactionService,
      mockNftMintService,
      mockNftContractsRepository,
      mockTransactionQueuesRepository,
      mockTransactionsRepository,
      mockVaultKeyRepository,
      mockServiceInfoRepository,
      mockTokenBoundAccountImplementationRepository,
      mockTokenBoundAccountRegistryAddressRepository,
      mockAccountRepository,
      mockDeliveryNftsFirestoreRepository,
      mockNftBaseMetadatasRepository,
      mockMetadataService,
    ),
  );
}

export function createMockWebhookService(
  mockTransactionsRepository = createMockTransactionsRepository(),
  mockTransactionQueuesRepository = createMockTransactionQueuesRepository(),
  mockTokenBoundAccountRegistryAddressRepository = createMockTokenBoundAccountRegistryAddressRepository(),
  mockMetadataService = createMockMetadataService(),
  mockNftTransactionUpdateService = createMockNftTransactionUpdateService(),
  mockAlchemyComponent = createMockAlchemyComponent(),
  mockNftContractsRepository = createMockNftContractsRepository(),
  mockBulkMintService = createMockBulkMintService(),
  mockServiceInfoRepository = createMockServiceInfoRepository(),
  mockUserRepository = createMockUserRepository(),
) {
  return createMockInstance(
    new WebhookService(
      mockTransactionsRepository,
      mockTransactionQueuesRepository,
      mockTokenBoundAccountRegistryAddressRepository,
      mockMetadataService,
      mockNftTransactionUpdateService,
      mockAlchemyComponent,
      mockNftContractsRepository,
      mockBulkMintService,
      mockServiceInfoRepository,
      mockUserRepository,
    ),
  );
}

export function createMockMetadataService(
  mockNftMetadatasRepository = createMockNftMetadatasRepository(),
  mockNftBaseMetadatasRepository = createMockNftBaseMetadatasRepository(),
  mockNftContractTypesRepository = createMockNftContractTypesRepository(),
) {
  return createMockInstance(
    new MetadataService(mockNftMetadatasRepository, mockNftBaseMetadatasRepository, mockNftContractTypesRepository),
  );
}

export function createMockNftRegisterService(
  mockTransactionService = createMockTransactionService(),
  mockWebhookService = createMockWebhookService(),
  mockNftContractTypesRepository = createMockNftContractTypesRepository(),
  mockNftContractsRepository = createMockNftContractsRepository(),
  mockVaultKeyRepository = createMockVaultKeyRepository(),
  mockServiceInfoRepository = createMockServiceInfoRepository(),
  mockMetadataService = createMockMetadataService(),
  mockNftBaseMetadatasRepository = createMockNftBaseMetadatasRepository(),
  mockTransactionQueuesRepository = createMockTransactionQueuesRepository(),
  mockTransactionsRepository = createMockTransactionsRepository(),
) {
  return createMockInstance(
    new NftRegisterService(
      mockTransactionService,
      mockWebhookService,
      mockNftContractTypesRepository,
      mockNftContractsRepository,
      mockVaultKeyRepository,
      mockServiceInfoRepository,
      mockMetadataService,
      mockNftBaseMetadatasRepository,
      mockTransactionQueuesRepository,
      mockTransactionsRepository,
    ),
  );
}

export function createMockUserService(
  mockUserRepository = createMockUserRepository(),
  mockAccountRepository = createMockAccountRepository(),
  mockShareBackupRepository = createMockShareBackupRepository(),
): jest.Mocked<UserService> {
  return createMockInstance(new UserService(
    mockUserRepository,
    mockAccountRepository,
    mockShareBackupRepository,
  ));
}

//--------------------------------Repositories--------------------------------
export function createMockAccountRepository() {
  return createMockInstance(new AccountRepository());
}
export function createMockActionRepository(): jest.Mocked<ActionRepository> {
  return createMockInstance(new ActionRepository());
}
export function createMockNftsFirestoreRepository(
  mockFirebaseComponent?: jest.Mocked<FirebaseComponent>,
): jest.Mocked<NftsFirestoreRepository> {
  return createMockInstance(new NftsFirestoreRepository(mockFirebaseComponent || createMockFirebaseComponent()));
}
export function createMockNftContractsRepository(): jest.Mocked<NftContractsRepository> {
  return createMockInstance(new NftContractsRepository());
}
export function createMockNftBaseMetadatasRepository(): jest.Mocked<NftBaseMetadatasRepository> {
  return createMockInstance(new NftBaseMetadatasRepository());
}
export function createMockDeliveryNftsFirestoreRepository(
  mockFirebaseComponent?: jest.Mocked<FirebaseComponent>,
): jest.Mocked<DeliveryNftsFirestoreRepository> {
  return createMockInstance(
    new DeliveryNftsFirestoreRepository(mockFirebaseComponent || createMockFirebaseComponent()),
  );
}
export function createMockVaultTransactionQueuesRepository(): jest.Mocked<VaultTransactionQueuesRepository> {
  return createMockInstance(new VaultTransactionQueuesRepository());
}
export function createMockServiceInfoRepository(): jest.Mocked<ServiceInfoRepository> {
  return createMockInstance(new ServiceInfoRepository());
}
export function createMockVaultKeyRepository(): jest.Mocked<VaultKeyRepository> {
  return createMockInstance(new VaultKeyRepository());
}
export function createMockAttemptTransactionsRepository(): jest.Mocked<AttemptTransactionsRepository> {
  return createMockInstance(new AttemptTransactionsRepository());
}
export function createMockTransactionsRepository(): jest.Mocked<TransactionsRepository> {
  return createMockInstance(new TransactionsRepository());
}
export function createMockTransactionQueuesRepository(): jest.Mocked<TransactionQueuesRepository> {
  return createMockInstance(new TransactionQueuesRepository());
}
export function createMockTokenBoundAccountImplementationRepository(): jest.Mocked<TokenBoundAccountImplementationRepository> {
  return createMockInstance(new TokenBoundAccountImplementationRepository());
}
export function createMockTokenBoundAccountRegistryAddressRepository(): jest.Mocked<TokenBoundAccountRegistryAddressRepository> {
  return createMockInstance(new TokenBoundAccountRegistryAddressRepository());
}
export function createMockUserRepository(): jest.Mocked<UserRepository> {
  return createMockInstance(new UserRepository());
}
export function createMockNftMetadatasRepository(): jest.Mocked<NftMetadatasRepository> {
  return createMockInstance(new NftMetadatasRepository());
}
export function createMockNftContractTypesRepository(): jest.Mocked<NftContractTypesRepository> {
  return createMockInstance(new NftContractTypesRepository());
}

export function createMockShareBackupRepository(): jest.Mocked<ShareBackupRepository> {
  return createMockInstance(new ShareBackupRepository());
}