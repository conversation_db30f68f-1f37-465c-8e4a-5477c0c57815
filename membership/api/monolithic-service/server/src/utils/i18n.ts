import { LanguageCode } from '../enum/languageCode';
import { sql, <PERSON><PERSON><PERSON><PERSON><PERSON>, SelectQueryBuilder } from 'kysely';
import { Database } from '../db/database';


export const langPref = (col: string, lang: LanguageCode) => sql`CASE WHEN ${sql.ref(col)} = ${lang} THEN 0 ELSE 1 END`;

export const makeTranslationJoin =
  (base: string, translation: string, pk: string, lang: LanguageCode) =>
  (join: JoinBuilder<Database, keyof Database>) =>
    join
      .onRef(sql.ref(`${translation}.${pk}`), '=', sql.ref(`${base}.${pk}`))
      .on(sql.ref(`${translation}.language`), 'in', [lang, 'ja']);

export const distinctByIdAndLang =
  <DB, TB extends keyof DB & string, O>(
    idCol: `${string}.${string}`,
    langCol: `${string}.${string}`,
    lang: LanguageCode,
  ) =>
  (qb: SelectQueryBuilder<DB, TB, O>) =>
    qb
      .distinctOn([sql.ref(idCol), sql.ref(langCol)])
      .orderBy(sql.ref(idCol))
      .orderBy(sql.ref(langCol))
      .orderBy(sql`CASE WHEN ${sql.ref(langCol)} = ${lang} THEN 0 ELSE 1 END`);
