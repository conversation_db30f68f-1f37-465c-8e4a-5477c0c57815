export const regexPattern = {
  uuid: /^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$/,
  isoDateTime: /^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])T([01]\d|2[0-3]):([0-5]\d):([0-5]\d)(?:\.(\d{3}))?Z$/,
  isoDateTimeWithOffset:
    /^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])T([01]\d|2[0-3]):([0-5]\d):([0-5]\d).(\d\d\d)(Z|([+-](0[0-9]|1[0-3]):([0-5]\d)))?$/,
  ethereumAddress: /^0x[a-fA-F0-9]{40}$/,
  tokenId: /^\d+$/,
  color: /^0x[A-Fa-f0-9]{8}$/,
  lineChannelId: /^[0-9]{10}$/,
  blockChainHash: /^0x[a-fA-F0-9]{64}$/,
  rawHex: /^[a-fA-F0-9]{64}$/,
  countryCode: /^[A-Z]{2}$/,
  phoneNumber: /^\+?[1-9]\d{1,14}$/,
  imageUrl:
    /^(ipfs:\/\/[A-Za-z0-9]+|ar:\/\/[A-Za-z0-9_-]+|https:\/\/[A-Za-z0-9.-]+(?:\/[A-Za-z0-9._~:/?#[\]@!$&'()*+,;=%-]*)?)$/,
  hexCallData: /^0x[a-fA-F0-9]{1,1024}$/,
  janCode: /^\d{8}$|^\d{13}$/,
  jwt: /^[A-Za-z0-9\-_]+\.[A-Za-z0-9\-_]+\.[A-Za-z0-9\-_]+$/,
  slug: /^[a-z0-9]+(?:-[a-z0-9]+)*$/,
};
