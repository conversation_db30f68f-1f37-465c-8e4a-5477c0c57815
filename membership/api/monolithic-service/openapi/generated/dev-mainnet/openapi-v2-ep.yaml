swagger: "2.0"
info:
  title: API
  version: 1.3.1
paths:
  /accounts:
    post:
      produces:
        - application/json
      parameters:
        - format: uuid
          in: header
          name: service-id-header
          required: true
          type: string
        - in: header
          minLength: 1
          name: line-id-token-header
          required: true
          type: string
      responses:
        "200":
          description: successful operation
          schema:
            $ref: "#/definitions/Account"
        "400":
          description: Validation error
          schema:
            example:
              code: VALIDATION_FAILURE
              message: Wrong request has been sent
              status: 400
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "401":
          description: Unauthenticated
          schema:
            example:
              code: UNAUTHORIZED
              message: Incorrect authentication information
              status: 401
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "403":
          description: Forbidden
          schema:
            example:
              code: FORBIDDEN
              message: You do not have access to the resource
              status: 403
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "500":
          description: Server error
          schema:
            example:
              code: INTERNAL_SERVER_ERROR
              message: Internal Server Error
              status: 500
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "503":
          description: Service unavailable
          schema:
            example:
              code: SERVICE_UNAVAILABLE
              message: Service Unavailable
              status: 503
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
      tags:
        - account
        - member
      description: ""
      operationId: createAccount
      summary: Create account
    options:
      summary: CORS preflight handler
      operationId: options_accounts
      security: []
      responses:
        "204":
          description: No content
          schema:
            type: string
        "401":
          description: Cors not allowed
  /accounts/{accountId}:
    delete:
      produces:
        - application/json
      parameters:
        - description: Uniquely given identifier for each account
          in: path
          name: accountId
          pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
          required: true
          type: string
        - format: uuid
          in: header
          name: service-id-header
          required: true
          type: string
      responses:
        "200":
          description: successful operation
        "400":
          description: Validation error
          schema:
            example:
              code: VALIDATION_FAILURE
              message: Wrong request has been sent
              status: 400
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "401":
          description: Unauthenticated
          schema:
            example:
              code: UNAUTHORIZED
              message: Incorrect authentication information
              status: 401
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "403":
          description: Forbidden
          schema:
            example:
              code: FORBIDDEN
              message: You do not have access to the resource
              status: 403
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "500":
          description: Server error
          schema:
            example:
              code: INTERNAL_SERVER_ERROR
              message: Internal Server Error
              status: 500
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "503":
          description: Service unavailable
          schema:
            example:
              code: SERVICE_UNAVAILABLE
              message: Service Unavailable
              status: 503
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
      tags:
        - account
        - member
      description: ""
      operationId: deleteServiceAccount
      summary: Delete account information
    get:
      produces:
        - application/json
      parameters:
        - description: Uniquely given identifier for each account
          in: path
          name: accountId
          pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
          required: true
          type: string
        - format: uuid
          in: header
          name: service-id-header
          required: true
          type: string
      responses:
        "200":
          description: successful operation
          schema:
            $ref: "#/definitions/Account"
        "400":
          description: Validation error
          schema:
            example:
              code: VALIDATION_FAILURE
              message: Wrong request has been sent
              status: 400
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "401":
          description: Unauthenticated
          schema:
            example:
              code: UNAUTHORIZED
              message: Incorrect authentication information
              status: 401
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "403":
          description: Forbidden
          schema:
            example:
              code: FORBIDDEN
              message: You do not have access to the resource
              status: 403
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "500":
          description: Server error
          schema:
            example:
              code: INTERNAL_SERVER_ERROR
              message: Internal Server Error
              status: 500
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "503":
          description: Service unavailable
          schema:
            example:
              code: SERVICE_UNAVAILABLE
              message: Service Unavailable
              status: 503
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
      tags:
        - account
        - member
      description: ""
      operationId: fetchServiceAccount
      summary: Get account information
    options:
      summary: CORS preflight handler
      operationId: options_accounts_accountId
      security: []
      parameters:
        - name: accountId
          in: path
          type: string
          required: true
      responses:
        "204":
          description: No content
          schema:
            type: string
        "401":
          description: Cors not allowed
  /accounts/{accountId}/actions/{actionId}/complete:
    post:
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - description: Uniquely given identifier for each account
          in: path
          name: accountId
          pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
          required: true
          type: string
        - description: Uniquely given identifier for each action
          in: path
          name: actionId
          pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
          required: true
          type: string
        - format: uuid
          in: header
          name: service-id-header
          required: true
          type: string
        - description: Reports that an action has been completed and registers the status
          in: body
          name: body
          required: true
          schema:
            $ref: "#/definitions/ActionComplete"
      responses:
        "200":
          description: successful operation
        "400":
          description: Validation error
          schema:
            example:
              code: VALIDATION_FAILURE
              message: Wrong request has been sent
              status: 400
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "401":
          description: Unauthenticated
          schema:
            example:
              code: UNAUTHORIZED
              message: Incorrect authentication information
              status: 401
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "403":
          description: Forbidden
          schema:
            example:
              code: FORBIDDEN
              message: You do not have access to the resource
              status: 403
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "500":
          description: Server error
          schema:
            example:
              code: INTERNAL_SERVER_ERROR
              message: Internal Server Error
              status: 500
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "503":
          description: Service unavailable
          schema:
            example:
              code: SERVICE_UNAVAILABLE
              message: Service Unavailable
              status: 503
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
      tags:
        - account
        - member
      operationId: completeAction
      summary: Complete action
    options:
      summary: CORS preflight handler
      operationId: options_accounts_accountId_actions_actionId_complete
      security: []
      parameters:
        - name: accountId
          in: path
          type: string
          required: true
        - name: actionId
          in: path
          type: string
          required: true
      responses:
        "204":
          description: No content
          schema:
            type: string
        "401":
          description: Cors not allowed
  /accounts/{accountId}/activity-history:
    get:
      produces:
        - application/json
      parameters:
        - description: Uniquely given identifier for each account
          in: path
          name: accountId
          pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
          required: true
          type: string
      responses:
        "200":
          description: successful operation
          schema:
            $ref: "#/definitions/ActivityHistory"
        "400":
          description: Validation error
          schema:
            example:
              code: VALIDATION_FAILURE
              message: Wrong request has been sent
              status: 400
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "401":
          description: Unauthenticated
          schema:
            example:
              code: UNAUTHORIZED
              message: Incorrect authentication information
              status: 401
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "403":
          description: Forbidden
          schema:
            example:
              code: FORBIDDEN
              message: You do not have access to the resource
              status: 403
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "500":
          description: Server error
          schema:
            example:
              code: INTERNAL_SERVER_ERROR
              message: Internal Server Error
              status: 500
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "503":
          description: Service unavailable
          schema:
            example:
              code: SERVICE_UNAVAILABLE
              message: Service Unavailable
              status: 503
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
      tags:
        - account
        - member
      description: ""
      operationId: fetchAccountActivityHistory
      summary: Get activity history
    options:
      summary: CORS preflight handler
      operationId: options_accounts_accountId_activity_history
      security: []
      parameters:
        - name: accountId
          in: path
          type: string
          required: true
      responses:
        "204":
          description: No content
          schema:
            type: string
        "401":
          description: Cors not allowed
  /accounts/{accountId}/last-login:
    post:
      produces:
        - application/json
      parameters:
        - description: Uniquely given identifier for each account
          in: path
          name: accountId
          pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
          required: true
          type: string
        - format: uuid
          in: header
          name: service-id-header
          required: true
          type: string
      responses:
        "200":
          description: successful operation
        "400":
          description: Validation error
          schema:
            example:
              code: VALIDATION_FAILURE
              message: Wrong request has been sent
              status: 400
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "401":
          description: Unauthenticated
          schema:
            example:
              code: UNAUTHORIZED
              message: Incorrect authentication information
              status: 401
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "403":
          description: Forbidden
          schema:
            example:
              code: FORBIDDEN
              message: You do not have access to the resource
              status: 403
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "500":
          description: Server error
          schema:
            example:
              code: INTERNAL_SERVER_ERROR
              message: Internal Server Error
              status: 500
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "503":
          description: Service unavailable
          schema:
            example:
              code: SERVICE_UNAVAILABLE
              message: Service Unavailable
              status: 503
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
      tags:
        - account
        - member
      operationId: updateLastLogin
      summary: Update last login timestamp
    options:
      summary: CORS preflight handler
      operationId: options_accounts_accountId_last_login
      security: []
      parameters:
        - name: accountId
          in: path
          type: string
          required: true
      responses:
        "204":
          description: No content
          schema:
            type: string
        "401":
          description: Cors not allowed
  /accounts/{accountId}/line-profile:
    put:
      produces:
        - application/json
      parameters:
        - description: Uniquely given identifier for each account
          in: path
          name: accountId
          pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
          required: true
          type: string
        - format: uuid
          in: header
          name: service-id-header
          required: true
          type: string
        - in: header
          minLength: 1
          name: line-id-token-header
          required: true
          type: string
      responses:
        "200":
          description: successful operation
          schema:
            $ref: "#/definitions/Account"
        "400":
          description: Validation error
          schema:
            example:
              code: VALIDATION_FAILURE
              message: Wrong request has been sent
              status: 400
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "401":
          description: Unauthenticated
          schema:
            example:
              code: UNAUTHORIZED
              message: Incorrect authentication information
              status: 401
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "403":
          description: Forbidden
          schema:
            example:
              code: FORBIDDEN
              message: You do not have access to the resource
              status: 403
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "500":
          description: Server error
          schema:
            example:
              code: INTERNAL_SERVER_ERROR
              message: Internal Server Error
              status: 500
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "503":
          description: Service unavailable
          schema:
            example:
              code: SERVICE_UNAVAILABLE
              message: Service Unavailable
              status: 503
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
      tags:
        - account
        - member
      description: ""
      operationId: updateAccountLineProfile
      summary: Update user line profile
    options:
      summary: CORS preflight handler
      operationId: options_accounts_accountId_line_profile
      security: []
      parameters:
        - name: accountId
          in: path
          type: string
          required: true
      responses:
        "204":
          description: No content
          schema:
            type: string
        "401":
          description: Cors not allowed
  /accounts/{accountId}/notifications:
    get:
      produces:
        - application/json
      parameters:
        - description: Uniquely given identifier for each account
          in: path
          name: accountId
          pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
          required: true
          type: string
        - format: uuid
          in: header
          name: service-id-header
          required: true
          type: string
      responses:
        "200":
          description: successful operation
          schema:
            $ref: "#/definitions/NotificationList"
        "400":
          description: Validation error
          schema:
            example:
              code: VALIDATION_FAILURE
              message: Wrong request has been sent
              status: 400
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "401":
          description: Unauthenticated
          schema:
            example:
              code: UNAUTHORIZED
              message: Incorrect authentication information
              status: 401
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "403":
          description: Forbidden
          schema:
            example:
              code: FORBIDDEN
              message: You do not have access to the resource
              status: 403
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "500":
          description: Server error
          schema:
            example:
              code: INTERNAL_SERVER_ERROR
              message: Internal Server Error
              status: 500
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "503":
          description: Service unavailable
          schema:
            example:
              code: SERVICE_UNAVAILABLE
              message: Service Unavailable
              status: 503
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
      tags:
        - account
        - member
      operationId: fetchNotifications
      summary: Fetch notification list
    options:
      summary: CORS preflight handler
      operationId: options_accounts_accountId_notifications
      security: []
      parameters:
        - name: accountId
          in: path
          type: string
          required: true
      responses:
        "204":
          description: No content
          schema:
            type: string
        "401":
          description: Cors not allowed
  /accounts/{accountId}/questionnaire/{questionnaireId}:
    get:
      produces:
        - application/json
      parameters:
        - description: Uniquely given identifier for each account
          in: path
          name: accountId
          pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
          required: true
          type: string
        - description: Uniquely given identifier for each questionnaire
          in: path
          name: questionnaireId
          pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
          required: true
          type: string
        - format: uuid
          in: header
          name: service-id-header
          required: true
          type: string
      responses:
        "200":
          description: successful operation
          schema:
            $ref: "#/definitions/AccountQuestionnaireDetail"
        "400":
          description: Validation error
          schema:
            example:
              code: VALIDATION_FAILURE
              message: Wrong request has been sent
              status: 400
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "401":
          description: Unauthenticated
          schema:
            example:
              code: UNAUTHORIZED
              message: Incorrect authentication information
              status: 401
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "403":
          description: Forbidden
          schema:
            example:
              code: FORBIDDEN
              message: You do not have access to the resource
              status: 403
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "500":
          description: Server error
          schema:
            example:
              code: INTERNAL_SERVER_ERROR
              message: Internal Server Error
              status: 500
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "503":
          description: Service unavailable
          schema:
            example:
              code: SERVICE_UNAVAILABLE
              message: Service Unavailable
              status: 503
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
      tags:
        - account
        - member
      operationId: fetchAccountQuestionnaireDetail
      summary: Get questionnaire detail
    options:
      summary: CORS preflight handler
      operationId: options_accounts_accountId_questionnaire_questionnaireId
      security: []
      parameters:
        - name: accountId
          in: path
          type: string
          required: true
        - name: questionnaireId
          in: path
          type: string
          required: true
      responses:
        "204":
          description: No content
          schema:
            type: string
        "401":
          description: Cors not allowed
  /accounts/{accountId}/quests:
    get:
      produces:
        - application/json
      parameters:
        - description: Uniquely given identifier for each account
          in: path
          name: accountId
          pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
          required: true
          type: string
        - description: |-
            * Start date of the date the quest expire
            * validation
              * Conforms to ISO8601
              * expireAtTo is not null
              * expireAtTo is later than expireAtFrom
          in: query
          name: expireAtFrom
          pattern: ^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])T([01]\d|2[0-3]):([0-5]\d):([0-5]\d)(?:\.(\d{3}))?Z$
          required: false
          type: string
        - description: |-
            * End date of the date the quest expire
            * validation
              * Conforms to ISO8601 YYYY-MM-DDThh:mm:ss.sssZ
              * expireAtFrom is not null
              * expireAtTo is later than expireAtFrom
          in: query
          name: expireAtTo
          pattern: ^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])T([01]\d|2[0-3]):([0-5]\d):([0-5]\d)(?:\.(\d{3}))?Z$
          required: false
          type: string
        - description: |-
            * Start date of the date the quest start
            * validation
              * Conforms to ISO8601 YYYY-MM-DDThh:mm:ss.sssZ
              * StartAtTo is not null
              * StartAtTo is later than StartAtFrom
          in: query
          name: startAtFrom
          pattern: ^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])T([01]\d|2[0-3]):([0-5]\d):([0-5]\d)(?:\.(\d{3}))?Z$
          required: false
          type: string
        - description: |-
            * End date of the date the quest start
            * validation
              * Conforms to ISO8601 YYYY-MM-DDThh:mm:ss.sssZ
              * StartAtFrom is not null
              * StartAtTo is later than StartAtFrom
          in: query
          name: startAtTo
          pattern: ^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])T([01]\d|2[0-3]):([0-5]\d):([0-5]\d)(?:\.(\d{3}))?Z$
          required: false
          type: string
        - enum:
            - NOT_STARTED
            - IN_PROGRESS
            - COMPLETED
          in: query
          name: questStatus
          required: false
          type: string
        - description: >-
            * Reward acquisition status. If not selected, all cases will be
            retrieved

            * validation
              * NOT_ACQUIRED | PARTIALLY_ACQUIRED | ACQUIRED
          enum:
            - NOT_ACQUIRED
            - PARTIALLY_ACQUIRED
            - ACQUIRED
          in: query
          name: rewardStatus
          required: false
          type: string
        - format: uuid
          in: header
          name: service-id-header
          required: true
          type: string
      responses:
        "200":
          description: successful operation
          schema:
            $ref: "#/definitions/QuestActivityStatus"
        "400":
          description: Validation error
          schema:
            example:
              code: VALIDATION_FAILURE
              message: Wrong request has been sent
              status: 400
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "401":
          description: Unauthenticated
          schema:
            example:
              code: UNAUTHORIZED
              message: Incorrect authentication information
              status: 401
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "403":
          description: Forbidden
          schema:
            example:
              code: FORBIDDEN
              message: You do not have access to the resource
              status: 403
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "500":
          description: Server error
          schema:
            example:
              code: INTERNAL_SERVER_ERROR
              message: Internal Server Error
              status: 500
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "503":
          description: Service unavailable
          schema:
            example:
              code: SERVICE_UNAVAILABLE
              message: Service Unavailable
              status: 503
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
      tags:
        - account
        - member
      operationId: fetchAccountQuests
      summary: Get a list of filtered quests
    options:
      summary: CORS preflight handler
      operationId: options_accounts_accountId_quests
      security: []
      parameters:
        - name: accountId
          in: path
          type: string
          required: true
      responses:
        "204":
          description: No content
          schema:
            type: string
        "401":
          description: Cors not allowed
  /accounts/{accountId}/quests/{questId}:
    get:
      produces:
        - application/json
      parameters:
        - description: Uniquely given identifier for each account
          in: path
          name: accountId
          pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
          required: true
          type: string
        - description: Uniquely given identifier for each quest
          in: path
          name: questId
          pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
          required: true
          type: string
        - format: uuid
          in: header
          name: service-id-header
          required: true
          type: string
      responses:
        "200":
          description: successful operation
          schema:
            $ref: "#/definitions/QuestActivityDetail"
        "400":
          description: Validation error
          schema:
            example:
              code: VALIDATION_FAILURE
              message: Wrong request has been sent
              status: 400
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "401":
          description: Unauthenticated
          schema:
            example:
              code: UNAUTHORIZED
              message: Incorrect authentication information
              status: 401
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "403":
          description: Forbidden
          schema:
            example:
              code: FORBIDDEN
              message: You do not have access to the resource
              status: 403
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "500":
          description: Server error
          schema:
            example:
              code: INTERNAL_SERVER_ERROR
              message: Internal Server Error
              status: 500
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "503":
          description: Service unavailable
          schema:
            example:
              code: SERVICE_UNAVAILABLE
              message: Service Unavailable
              status: 503
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
      tags:
        - account
        - member
      operationId: fetchAccountQuestDetail
      summary: Get detail status of a quest
    options:
      summary: CORS preflight handler
      operationId: options_accounts_accountId_quests_questId
      security: []
      parameters:
        - name: accountId
          in: path
          type: string
          required: true
        - name: questId
          in: path
          type: string
          required: true
      responses:
        "204":
          description: No content
          schema:
            type: string
        "401":
          description: Cors not allowed
  /accounts/{accountId}/rewards/{rewardId}/claim:
    post:
      produces:
        - application/json
      parameters:
        - description: Uniquely given identifier for each account
          in: path
          name: accountId
          pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
          required: true
          type: string
        - description: Uniquely given identifier for each action
          in: path
          name: rewardId
          pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
          required: true
          type: string
        - format: uuid
          in: header
          name: service-id-header
          required: true
          type: string
      responses:
        "200":
          description: successful operation
          schema:
            $ref: "#/definitions/ClaimedReward"
        "400":
          description: Validation error
          schema:
            example:
              code: VALIDATION_FAILURE
              message: Wrong request has been sent
              status: 400
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "401":
          description: Unauthenticated
          schema:
            example:
              code: UNAUTHORIZED
              message: Incorrect authentication information
              status: 401
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "403":
          description: Forbidden
          schema:
            example:
              code: FORBIDDEN
              message: You do not have access to the resource
              status: 403
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "500":
          description: Server error
          schema:
            example:
              code: INTERNAL_SERVER_ERROR
              message: Internal Server Error
              status: 500
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "503":
          description: Service unavailable
          schema:
            example:
              code: SERVICE_UNAVAILABLE
              message: Service Unavailable
              status: 503
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
      tags:
        - account
        - member
      operationId: claimReward
      summary: Claim reward
    options:
      summary: CORS preflight handler
      operationId: options_accounts_accountId_rewards_rewardId_claim
      security: []
      parameters:
        - name: accountId
          in: path
          type: string
          required: true
        - name: rewardId
          in: path
          type: string
          required: true
      responses:
        "204":
          description: No content
          schema:
            type: string
        "401":
          description: Cors not allowed
  /accounts/{accountId}/serial-codes/redeem:
    post:
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - description: Uniquely given identifier for each account
          in: path
          name: accountId
          pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
          required: true
          type: string
        - format: uuid
          in: header
          name: service-id-header
          required: true
          type: string
        - description: Use serial code
          in: body
          name: body
          required: true
          schema:
            $ref: "#/definitions/SerialCodeRedeemReq"
      responses:
        "200":
          description: Serial code redeem and claim reward successful
          schema:
            $ref: "#/definitions/ClaimedReward"
        "400":
          description: Validation error
          schema:
            example:
              code: VALIDATION_FAILURE
              message: Wrong request has been sent
              status: 400
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "401":
          description: Unauthenticated
          schema:
            example:
              code: UNAUTHORIZED
              message: Incorrect authentication information
              status: 401
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "403":
          description: Forbidden
          schema:
            example:
              code: FORBIDDEN
              message: You do not have access to the resource
              status: 403
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "500":
          description: Server error
          schema:
            example:
              code: INTERNAL_SERVER_ERROR
              message: Internal Server Error
              status: 500
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "503":
          description: Service unavailable
          schema:
            example:
              code: SERVICE_UNAVAILABLE
              message: Service Unavailable
              status: 503
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
      tags:
        - account
        - member
      operationId: redeemSerialCode
      summary: Redeem SerialCode
    options:
      summary: CORS preflight handler
      operationId: options_accounts_accountId_serial_codes_redeem
      security: []
      parameters:
        - name: accountId
          in: path
          type: string
          required: true
      responses:
        "204":
          description: No content
          schema:
            type: string
        "401":
          description: Cors not allowed
  /accounts/{accountId}/status:
    get:
      produces:
        - application/json
      parameters:
        - description: Uniquely given identifier for each account
          in: path
          name: accountId
          pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
          required: true
          type: string
        - format: uuid
          in: header
          name: service-id-header
          required: true
          type: string
      responses:
        "200":
          description: successful operation
          schema:
            $ref: "#/definitions/Status"
        "400":
          description: Validation error
          schema:
            example:
              code: VALIDATION_FAILURE
              message: Wrong request has been sent
              status: 400
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "401":
          description: Unauthenticated
          schema:
            example:
              code: UNAUTHORIZED
              message: Incorrect authentication information
              status: 401
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "403":
          description: Forbidden
          schema:
            example:
              code: FORBIDDEN
              message: You do not have access to the resource
              status: 403
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "500":
          description: Server error
          schema:
            example:
              code: INTERNAL_SERVER_ERROR
              message: Internal Server Error
              status: 500
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "503":
          description: Service unavailable
          schema:
            example:
              code: SERVICE_UNAVAILABLE
              message: Service Unavailable
              status: 503
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
      tags:
        - account
        - member
      description: ""
      operationId: fetchAccountStatus
      summary: Get status
    options:
      summary: CORS preflight handler
      operationId: options_accounts_accountId_status
      security: []
      parameters:
        - name: accountId
          in: path
          type: string
          required: true
      responses:
        "204":
          description: No content
          schema:
            type: string
        "401":
          description: Cors not allowed
  /accounts/{accountId}/user-operation/prepare/consumption:
    post:
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - description: Uniquely given identifier for each account
          in: path
          name: accountId
          pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
          required: true
          type: string
        - format: uuid
          in: header
          name: service-id-header
          required: true
          type: string
        - description: Prepare to issue a UO to use the coupon
          in: body
          name: body
          required: true
          schema:
            $ref: "#/definitions/ConsumptionPrepare"
      responses:
        "200":
          description: successful operation
          schema:
            $ref: "#/definitions/UserOperationCallData"
        "400":
          description: Validation error
          schema:
            example:
              code: VALIDATION_FAILURE
              message: Wrong request has been sent
              status: 400
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "401":
          description: Unauthenticated
          schema:
            example:
              code: UNAUTHORIZED
              message: Incorrect authentication information
              status: 401
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "403":
          description: Forbidden
          schema:
            example:
              code: FORBIDDEN
              message: You do not have access to the resource
              status: 403
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "500":
          description: Server error
          schema:
            example:
              code: INTERNAL_SERVER_ERROR
              message: Internal Server Error
              status: 500
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "503":
          description: Service unavailable
          schema:
            example:
              code: SERVICE_UNAVAILABLE
              message: Service Unavailable
              status: 503
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
      tags:
        - account
        - member
      operationId: prepareConsumptionUO
      summary: Prepare UO for coupon consumption
    options:
      summary: CORS preflight handler
      operationId: options_accounts_accountId_user_operation_prepare_consumption
      security: []
      parameters:
        - name: accountId
          in: path
          type: string
          required: true
      responses:
        "204":
          description: No content
          schema:
            type: string
        "401":
          description: Cors not allowed
  /accounts/{accountId}/user-operation/send:
    post:
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - description: Uniquely given identifier for each account
          in: path
          name: accountId
          pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
          required: true
          type: string
        - format: uuid
          in: header
          name: service-id-header
          required: true
          type: string
        - description: Execute UO to use the coupon
          in: body
          name: body
          required: true
          schema:
            $ref: "#/definitions/UserOperationResult"
      responses:
        "200":
          description: successful operation
          schema:
            $ref: "#/definitions/SendUserOperationId"
        "400":
          description: Validation error
          schema:
            example:
              code: VALIDATION_FAILURE
              message: Wrong request has been sent
              status: 400
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "401":
          description: Unauthenticated
          schema:
            example:
              code: UNAUTHORIZED
              message: Incorrect authentication information
              status: 401
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "403":
          description: Forbidden
          schema:
            example:
              code: FORBIDDEN
              message: You do not have access to the resource
              status: 403
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "500":
          description: Server error
          schema:
            example:
              code: INTERNAL_SERVER_ERROR
              message: Internal Server Error
              status: 500
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "503":
          description: Service unavailable
          schema:
            example:
              code: SERVICE_UNAVAILABLE
              message: Service Unavailable
              status: 503
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
      tags:
        - account
        - member
      operationId: sendUO
      summary: Send UO
    options:
      summary: CORS preflight handler
      operationId: options_accounts_accountId_user_operation_send
      security: []
      parameters:
        - name: accountId
          in: path
          type: string
          required: true
      responses:
        "204":
          description: No content
          schema:
            type: string
        "401":
          description: Cors not allowed
  /admin/firestore/metadata/update:
    post:
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - description: API Key for admin authorization. Use this to authenticate
            admin-level operations.
          in: header
          name: X-Admin-API-Key
          required: true
          type: string
        - description: Update Metadata
          in: body
          name: body
          required: true
          schema:
            $ref: "#/definitions/UpdateMetadataRequest"
      responses:
        "200":
          description: successful operation
          schema:
            $ref: "#/definitions/UpdateMetadataResponse"
        "400":
          description: Validation error
          schema:
            example:
              code: VALIDATION_FAILURE
              message: Wrong request has been sent
              status: 400
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "401":
          description: Unauthenticated
          schema:
            example:
              code: UNAUTHORIZED
              message: Incorrect authentication information
              status: 401
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "403":
          description: Forbidden
          schema:
            example:
              code: FORBIDDEN
              message: You do not have access to the resource
              status: 403
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "404":
          description: Service unavailable
          schema:
            properties:
              code:
                description: Application-specific error code
                example: NOT_FOUND
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Resource does not exist.
                type: string
              status:
                description: HTTP status code of the error
                example: 404
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "500":
          description: Server error
          schema:
            example:
              code: INTERNAL_SERVER_ERROR
              message: Internal Server Error
              status: 500
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "503":
          description: Service unavailable
          schema:
            example:
              code: SERVICE_UNAVAILABLE
              message: Service Unavailable
              status: 503
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
      security:
        - api_key: []
      tags:
        - nft
        - admin
      operationId: updateMetadata
      summary: Update metadata
    options:
      summary: CORS preflight handler
      operationId: options_admin_firestore_metadata_update
      security: []
      responses:
        "204":
          description: No content
          schema:
            type: string
        "401":
          description: Cors not allowed
  /auth/custom-token:
    get:
      produces:
        - application/json
      parameters:
        - format: uuid
          in: header
          name: service-id-header
          required: true
          type: string
        - in: header
          minLength: 1
          name: line-id-token-header
          required: true
          type: string
      responses:
        "200":
          description: successful operation
          schema:
            $ref: "#/definitions/CustomToken"
        "400":
          description: Validation error
          schema:
            example:
              code: VALIDATION_FAILURE
              message: Wrong request has been sent
              status: 400
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "401":
          description: Unauthenticated
          schema:
            example:
              code: UNAUTHORIZED
              message: Incorrect authentication information
              status: 401
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "403":
          description: Forbidden
          schema:
            example:
              code: FORBIDDEN
              message: You do not have access to the resource
              status: 403
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "500":
          description: Server error
          schema:
            example:
              code: INTERNAL_SERVER_ERROR
              message: Internal Server Error
              status: 500
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "503":
          description: Service unavailable
          schema:
            example:
              code: SERVICE_UNAVAILABLE
              message: Service Unavailable
              status: 503
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
      security: []
      tags:
        - authentication
        - guest
      operationId: getCustomToken
      summary: Issue a CustomToken
    post:
      produces:
        - application/json
      parameters:
        - format: uuid
          in: header
          name: service-id-header
          required: true
          type: string
        - in: header
          minLength: 1
          name: line-id-token-header
          required: true
          type: string
      responses:
        "200":
          description: successful operation
          schema:
            $ref: "#/definitions/CustomToken"
        "400":
          description: Validation error
          schema:
            example:
              code: VALIDATION_FAILURE
              message: Wrong request has been sent
              status: 400
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "401":
          description: Unauthenticated
          schema:
            example:
              code: UNAUTHORIZED
              message: Incorrect authentication information
              status: 401
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "403":
          description: Forbidden
          schema:
            example:
              code: FORBIDDEN
              message: You do not have access to the resource
              status: 403
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "500":
          description: Server error
          schema:
            example:
              code: INTERNAL_SERVER_ERROR
              message: Internal Server Error
              status: 500
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "503":
          description: Service unavailable
          schema:
            example:
              code: SERVICE_UNAVAILABLE
              message: Service Unavailable
              status: 503
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
      tags:
        - authentication
        - member
      operationId: createCustomToken
      summary: Link lineIdToken to account
    options:
      summary: CORS preflight handler
      operationId: options_auth_custom_token
      security: []
      responses:
        "204":
          description: No content
          schema:
            type: string
        "401":
          description: Cors not allowed
  /nfts/bulk-mint:
    post:
      produces:
        - application/json
      parameters: []
      responses:
        "200":
          description: Returns the bulk mint NFT response
          schema:
            $ref: "#/definitions/BulkMintNft"
        "400":
          description: Validation error
          schema:
            example:
              code: VALIDATION_FAILURE
              message: Wrong request has been sent
              status: 400
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "401":
          description: Unauthenticated
          schema:
            example:
              code: UNAUTHORIZED
              message: Incorrect authentication information
              status: 401
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "403":
          description: Forbidden
          schema:
            example:
              code: FORBIDDEN
              message: You do not have access to the resource
              status: 403
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "500":
          description: Server error
          schema:
            example:
              code: INTERNAL_SERVER_ERROR
              message: Internal Server Error
              status: 500
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "503":
          description: Service unavailable
          schema:
            example:
              code: SERVICE_UNAVAILABLE
              message: Service Unavailable
              status: 503
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
      security: []
      tags:
        - nft
        - system
      operationId: bulkMintNFT
      summary: Bulk mint NFTs
    options:
      summary: CORS preflight handler
      operationId: options_nfts_bulk_mint
      security: []
      responses:
        "204":
          description: No content
          schema:
            type: string
        "401":
          description: Cors not allowed
  /nfts/deploy-modular:
    post:
      produces:
        - application/json
      parameters:
        - format: uuid
          in: header
          name: service-id-header
          required: true
          type: string
      responses:
        "200":
          description: Returns the deployed modular NFT response
          schema:
            $ref: "#/definitions/RegsteredModularContractSchema"
        "400":
          description: Validation error
          schema:
            example:
              code: VALIDATION_FAILURE
              message: Wrong request has been sent
              status: 400
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "401":
          description: Unauthenticated
          schema:
            example:
              code: UNAUTHORIZED
              message: Incorrect authentication information
              status: 401
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "403":
          description: Forbidden
          schema:
            example:
              code: FORBIDDEN
              message: You do not have access to the resource
              status: 403
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "500":
          description: Server error
          schema:
            example:
              code: INTERNAL_SERVER_ERROR
              message: Internal Server Error
              status: 500
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "503":
          description: Service unavailable
          schema:
            example:
              code: SERVICE_UNAVAILABLE
              message: Service Unavailable
              status: 503
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
      security:
        - api_key: []
      tags:
        - nft
        - admin
      operationId: deployModularNFT
      summary: Deploy modular NFT
    options:
      summary: CORS preflight handler
      operationId: options_nfts_deploy_modular
      security: []
      responses:
        "204":
          description: No content
          schema:
            type: string
        "401":
          description: Cors not allowed
  /nfts/finality-status:
    post:
      produces:
        - application/json
      parameters: []
      responses:
        "200":
          description: Returns the transaction finality status
          schema:
            $ref: "#/definitions/TxFinalityStatus"
        "400":
          description: Validation error
          schema:
            example:
              code: VALIDATION_FAILURE
              message: Wrong request has been sent
              status: 400
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "401":
          description: Unauthenticated
          schema:
            example:
              code: UNAUTHORIZED
              message: Incorrect authentication information
              status: 401
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "403":
          description: Forbidden
          schema:
            example:
              code: FORBIDDEN
              message: You do not have access to the resource
              status: 403
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "500":
          description: Server error
          schema:
            example:
              code: INTERNAL_SERVER_ERROR
              message: Internal Server Error
              status: 500
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "503":
          description: Service unavailable
          schema:
            example:
              code: SERVICE_UNAVAILABLE
              message: Service Unavailable
              status: 503
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
      security: []
      tags:
        - nft
        - system
      operationId: updateTxFinalityStatus
      summary: Update transaction finality status
    options:
      summary: CORS preflight handler
      operationId: options_nfts_finality_status
      security: []
      responses:
        "204":
          description: No content
          schema:
            type: string
        "401":
          description: Cors not allowed
  /nfts/grant-nfts-minter-role:
    post:
      produces:
        - application/json
      parameters:
        - format: uuid
          in: header
          name: service-id-header
          required: true
          type: string
      responses:
        "200":
          description: Returns the response for granting NFT minter role
          schema:
            $ref: "#/definitions/GrantedNftContractSchema"
        "400":
          description: Validation error
          schema:
            example:
              code: VALIDATION_FAILURE
              message: Wrong request has been sent
              status: 400
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "401":
          description: Unauthenticated
          schema:
            example:
              code: UNAUTHORIZED
              message: Incorrect authentication information
              status: 401
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "403":
          description: Forbidden
          schema:
            example:
              code: FORBIDDEN
              message: You do not have access to the resource
              status: 403
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "500":
          description: Server error
          schema:
            example:
              code: INTERNAL_SERVER_ERROR
              message: Internal Server Error
              status: 500
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "503":
          description: Service unavailable
          schema:
            example:
              code: SERVICE_UNAVAILABLE
              message: Service Unavailable
              status: 503
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
      security:
        - api_key: []
      tags:
        - nft
        - admin
      operationId: grantNftsMinterRole
      summary: Grant NFT minter role
    options:
      summary: CORS preflight handler
      operationId: options_nfts_grant_nfts_minter_role
      security: []
      responses:
        "204":
          description: No content
          schema:
            type: string
        "401":
          description: Cors not allowed
  /nfts/i18n/{contractAddress}/{tokenId}:
    get:
      produces:
        - application/json
      parameters:
        - description: Contract address of NFT(ERC721, ERC1155)
          in: path
          name: contractAddress
          pattern: ^0x[a-fA-F0-9]{40}$
          required: true
          type: string
        - description: Token id of NFT(ERC721, ERC1155)
          in: path
          name: tokenId
          required: true
          type: string
        - format: uuid
          in: header
          name: account-id-header
          required: true
          type: string
        - description: Language code
          enum:
            - de
            - en-GB
            - en-US
            - es
            - fr
            - it
            - ja
            - ko
            - pt
            - ru
            - th
            - zh-Hans
            - zh-Hant
          in: header
          name: accept-language
          required: true
          type: string
      responses:
        "200":
          description: Returns the i18n NFT metadata
          schema:
            $ref: "#/definitions/GetI18nNFTMetadataResponse"
        "400":
          description: Validation error
          schema:
            example:
              code: VALIDATION_FAILURE
              message: Wrong request has been sent
              status: 400
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "401":
          description: Unauthenticated
          schema:
            example:
              code: UNAUTHORIZED
              message: Incorrect authentication information
              status: 401
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "404":
          description: Not found
          schema:
            properties:
              code:
                description: Application-specific error code
                example: NOT_FOUND
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Resource does not exist.
                type: string
              status:
                description: HTTP status code of the error
                example: 404
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "500":
          description: Server error
          schema:
            example:
              code: INTERNAL_SERVER_ERROR
              message: Internal Server Error
              status: 500
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "503":
          description: Service unavailable
          schema:
            example:
              code: SERVICE_UNAVAILABLE
              message: Service Unavailable
              status: 503
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "504":
          description: Gateway timeout
          schema:
            example:
              code: GATEWAY_TIMEOUT
              message: Gateway Timeout
              status: 504
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
      tags:
        - nft
        - member
      operationId: getI18nNFTMetadata
      summary: Get i18n NFT metadata
    options:
      summary: CORS preflight handler
      operationId: options_nfts_i18n_contractAddress_tokenId
      security: []
      parameters:
        - name: contractAddress
          in: path
          type: string
          required: true
        - name: tokenId
          in: path
          type: string
          required: true
      responses:
        "204":
          description: No content
          schema:
            type: string
        "401":
          description: Cors not allowed
  /nfts/metadata:
    post:
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - format: uuid
          in: header
          name: service-id-header
          required: true
          type: string
        - description: Wallet and NFT whitelist to retrieve metadata
          in: body
          name: body
          required: true
          schema:
            $ref: "#/definitions/RequestAccount"
      responses:
        "200":
          description: A list of NFT metadata
          schema: {}
        "400":
          description: Validation error
          schema:
            example:
              code: VALIDATION_FAILURE
              message: Wrong request has been sent
              status: 400
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "401":
          description: Unauthenticated
          schema:
            example:
              code: UNAUTHORIZED
              message: Incorrect authentication information
              status: 401
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "403":
          description: Forbidden
          schema:
            example:
              code: FORBIDDEN
              message: You do not have access to the resource
              status: 403
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "500":
          description: Server error
          schema:
            example:
              code: INTERNAL_SERVER_ERROR
              message: Internal Server Error
              status: 500
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "503":
          description: Service unavailable
          schema:
            example:
              code: SERVICE_UNAVAILABLE
              message: Service Unavailable
              status: 503
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
      security:
        - api_key: []
      tags:
        - nft
        - admin
      operationId: postNftMetadata
      summary: Search metadata by wallet
    options:
      summary: CORS preflight handler
      operationId: options_nfts_metadata
      security: []
      responses:
        "204":
          description: No content
          schema:
            type: string
        "401":
          description: Cors not allowed
  /nfts/metadata/{nftId}/{tokenId}:
    get:
      produces:
        - application/json
      parameters:
        - description: Uniquely given identifier for each NFT(ERC721, ERC1155)
          in: path
          name: nftId
          pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
          required: true
          type: string
        - description: Token id of NFT(ERC721, ERC1155)
          in: path
          name: tokenId
          required: true
          type: string
      responses:
        "200":
          description: successful operation
          schema:
            $ref: "#/definitions/GeneralMetadata"
        "400":
          description: Validation error
          schema:
            example:
              code: VALIDATION_FAILURE
              message: Wrong request has been sent
              status: 400
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "401":
          description: Unauthenticated
          schema:
            example:
              code: UNAUTHORIZED
              message: Incorrect authentication information
              status: 401
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "403":
          description: Forbidden
          schema:
            example:
              code: FORBIDDEN
              message: You do not have access to the resource
              status: 403
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "500":
          description: Server error
          schema:
            example:
              code: INTERNAL_SERVER_ERROR
              message: Internal Server Error
              status: 500
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "503":
          description: Service unavailable
          schema:
            example:
              code: SERVICE_UNAVAILABLE
              message: Service Unavailable
              status: 503
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
      security: []
      tags:
        - nft
        - guest
      operationId: getMetadata
      summary: Get metadata
    options:
      summary: CORS preflight handler
      operationId: options_nfts_metadata_nftId_tokenId
      security: []
      parameters:
        - name: nftId
          in: path
          type: string
          required: true
        - name: tokenId
          in: path
          type: string
          required: true
      responses:
        "204":
          description: No content
          schema:
            type: string
        "401":
          description: Cors not allowed
  /nfts/mint:
    post:
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - format: uuid
          in: header
          name: service-id-header
          required: true
          type: string
        - description: NFT
          in: body
          name: body
          required: true
          schema:
            $ref: "#/definitions/NftMintRequest"
      responses:
        "200":
          description: Returns the id that ties the transaction status of the mint
          schema:
            $ref: "#/definitions/TransactionData"
        "400":
          description: Validation error
          schema:
            example:
              code: VALIDATION_FAILURE
              message: Wrong request has been sent
              status: 400
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "401":
          description: Unauthenticated
          schema:
            example:
              code: UNAUTHORIZED
              message: Incorrect authentication information
              status: 401
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "403":
          description: Forbidden
          schema:
            example:
              code: FORBIDDEN
              message: You do not have access to the resource
              status: 403
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "500":
          description: Server error
          schema:
            example:
              code: INTERNAL_SERVER_ERROR
              message: Internal Server Error
              status: 500
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "503":
          description: Service unavailable
          schema:
            example:
              code: SERVICE_UNAVAILABLE
              message: Service Unavailable
              status: 503
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
      security:
        - api_key: []
      tags:
        - nft
        - admin
      operationId: mintNFT
      summary: Issue NFTs
    options:
      summary: CORS preflight handler
      operationId: options_nfts_mint
      security: []
      responses:
        "204":
          description: No content
          schema:
            type: string
        "401":
          description: Cors not allowed
  /nfts/register:
    post:
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - format: uuid
          in: header
          name: service-id-header
          required: true
          type: string
        - description: NFT register request
          in: body
          name: body
          required: true
          schema:
            $ref: "#/definitions/NftRegister"
      responses:
        "200":
          description: NFT register response
          schema:
            $ref: "#/definitions/NftDeploy"
        "400":
          description: Validation error
          schema:
            example:
              code: VALIDATION_FAILURE
              message: Wrong request has been sent
              status: 400
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "401":
          description: Unauthenticated
          schema:
            example:
              code: UNAUTHORIZED
              message: Incorrect authentication information
              status: 401
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "403":
          description: Forbidden
          schema:
            example:
              code: FORBIDDEN
              message: You do not have access to the resource
              status: 403
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "500":
          description: Server error
          schema:
            example:
              code: INTERNAL_SERVER_ERROR
              message: Internal Server Error
              status: 500
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "503":
          description: Service unavailable
          schema:
            example:
              code: SERVICE_UNAVAILABLE
              message: Service Unavailable
              status: 503
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
      security:
        - api_key: []
      tags:
        - nft
        - admin
      operationId: registerNFT
      summary: Register and Deploy NFT
    options:
      summary: CORS preflight handler
      operationId: options_nfts_register
      security: []
      responses:
        "204":
          description: No content
          schema:
            type: string
        "401":
          description: Cors not allowed
  /nfts/retry-transactions:
    post:
      produces:
        - application/json
      parameters: []
      responses:
        "200":
          description: Returns the transaction status of the retry
          schema:
            items:
              $ref: "#/definitions/RetryTransactionResponse"
            type: array
        "400":
          description: Validation error
          schema:
            example:
              code: VALIDATION_FAILURE
              message: Wrong request has been sent
              status: 400
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "401":
          description: Unauthenticated
          schema:
            example:
              code: UNAUTHORIZED
              message: Incorrect authentication information
              status: 401
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "403":
          description: Forbidden
          schema:
            example:
              code: FORBIDDEN
              message: You do not have access to the resource
              status: 403
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "500":
          description: Server error
          schema:
            example:
              code: INTERNAL_SERVER_ERROR
              message: Internal Server Error
              status: 500
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "503":
          description: Service unavailable
          schema:
            example:
              code: SERVICE_UNAVAILABLE
              message: Service Unavailable
              status: 503
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
      security: []
      tags:
        - nft
        - system
      operationId: retryTransactions
      summary: Retry transactions
    options:
      summary: CORS preflight handler
      operationId: options_nfts_retry_transactions
      security: []
      responses:
        "204":
          description: No content
          schema:
            type: string
        "401":
          description: Cors not allowed
  /notifications:
    post:
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - format: uuid
          in: header
          name: service-id-header
          required: true
          type: string
        - description: Reports that an action has been completed and registers the status
          in: body
          name: body
          required: true
          schema:
            properties:
              accountIds:
                description: account id list
                example: []
                items:
                  pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
                  type: string
                type: array
              broadcastDate:
                description: bloadcast date.
                example: 2024-01-01T10:00:00Z
                pattern: ^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])T([01]\d|2[0-3]):([0-5]\d):([0-5]\d)(?:\.(\d{3}))?Z$
                type: string
              notificationTranslations:
                description: notification localize data list
                example: []
                items:
                  $ref: "#/definitions/Notification"
                type: array
            required:
              - notificationTranslations
              - broadcastDate
            type: object
      responses:
        "200":
          description: successful response
        "400":
          description: Validation error
          schema:
            example:
              code: VALIDATION_FAILURE
              message: Wrong request has been sent
              status: 400
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "401":
          description: Unauthenticated
          schema:
            example:
              code: UNAUTHORIZED
              message: Incorrect authentication information
              status: 401
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "403":
          description: Forbidden
          schema:
            example:
              code: FORBIDDEN
              message: You do not have access to the resource
              status: 403
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "500":
          description: Server error
          schema:
            example:
              code: INTERNAL_SERVER_ERROR
              message: Internal Server Error
              status: 500
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "503":
          description: Service unavailable
          schema:
            example:
              code: SERVICE_UNAVAILABLE
              message: Service Unavailable
              status: 503
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
      tags:
        - account
        - member
      operationId: createNotification
      summary: create notification
    options:
      summary: CORS preflight handler
      operationId: options_notifications
      security: []
      responses:
        "204":
          description: No content
          schema:
            type: string
        "401":
          description: Cors not allowed
  /products/{accountId}:
    get:
      produces:
        - application/json
      parameters:
        - description: Uniquely given identifier for each account
          in: path
          name: accountId
          pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
          required: true
          type: string
        - format: uuid
          in: header
          name: service-id-header
          required: true
          type: string
      responses:
        "200":
          description: successful get
          schema:
            items:
              $ref: "#/definitions/StripeProduct"
            type: array
        "400":
          description: Validation error
          schema:
            example:
              code: VALIDATION_FAILURE
              message: Wrong request has been sent
              status: 400
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "401":
          description: Unauthenticated
          schema:
            example:
              code: UNAUTHORIZED
              message: Incorrect authentication information
              status: 401
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "403":
          description: Forbidden
          schema:
            example:
              code: FORBIDDEN
              message: You do not have access to the resource
              status: 403
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "500":
          description: Server error
          schema:
            example:
              code: INTERNAL_SERVER_ERROR
              message: Internal Server Error
              status: 500
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "503":
          description: Service unavailable
          schema:
            example:
              code: SERVICE_UNAVAILABLE
              message: Service Unavailable
              status: 503
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
      tags:
        - product
        - member
      operationId: getProducts
      summary: Get products
    options:
      summary: CORS preflight handler
      operationId: options_products_accountId
      security: []
      parameters:
        - name: accountId
          in: path
          type: string
          required: true
      responses:
        "204":
          description: No content
          schema:
            type: string
        "401":
          description: Cors not allowed
  /products/{accountId}/checkout-session:
    delete:
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - description: Uniquely given identifier for each account
          in: path
          name: accountId
          pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
          required: true
          type: string
        - format: uuid
          in: header
          name: service-id-header
          required: true
          type: string
        - description: request body for expire Stripe session
          in: body
          name: body
          required: true
          schema:
            $ref: "#/definitions/SessionData"
      responses:
        "204":
          description: no content
        "400":
          description: Validation error
          schema:
            example:
              code: VALIDATION_FAILURE
              message: Wrong request has been sent
              status: 400
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "401":
          description: Unauthenticated
          schema:
            example:
              code: UNAUTHORIZED
              message: Incorrect authentication information
              status: 401
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "403":
          description: Forbidden
          schema:
            example:
              code: FORBIDDEN
              message: You do not have access to the resource
              status: 403
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "500":
          description: Server error
          schema:
            example:
              code: INTERNAL_SERVER_ERROR
              message: Internal Server Error
              status: 500
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "503":
          description: Service unavailable
          schema:
            example:
              code: SERVICE_UNAVAILABLE
              message: Service Unavailable
              status: 503
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
      tags:
        - product
        - member
      operationId: expireSession
      summary: Expire Stripe session
    post:
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - description: Uniquely given identifier for each account
          in: path
          name: accountId
          pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
          required: true
          type: string
        - format: uuid
          in: header
          name: service-id-header
          required: true
          type: string
        - description: request body for creating Stripe session
          in: body
          name: body
          required: true
          schema:
            $ref: "#/definitions/LineItem"
      responses:
        "200":
          description: successful session checkout
          schema:
            $ref: "#/definitions/StripeCheckoutInfo"
        "400":
          description: Validation error
          schema:
            example:
              code: VALIDATION_FAILURE
              message: Wrong request has been sent
              status: 400
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "401":
          description: Unauthenticated
          schema:
            example:
              code: UNAUTHORIZED
              message: Incorrect authentication information
              status: 401
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "403":
          description: Forbidden
          schema:
            example:
              code: FORBIDDEN
              message: You do not have access to the resource
              status: 403
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "500":
          description: Server error
          schema:
            example:
              code: INTERNAL_SERVER_ERROR
              message: Internal Server Error
              status: 500
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "503":
          description: Service unavailable
          schema:
            example:
              code: SERVICE_UNAVAILABLE
              message: Service Unavailable
              status: 503
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
      tags:
        - product
        - member
      operationId: createSession
      summary: Create Stripe session
    options:
      summary: CORS preflight handler
      operationId: options_products_accountId_checkout_session
      security: []
      parameters:
        - name: accountId
          in: path
          type: string
          required: true
      responses:
        "204":
          description: No content
          schema:
            type: string
        "401":
          description: Cors not allowed
  /products/{accountId}/checkout-session/status:
    post:
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - description: Uniquely given identifier for each account
          in: path
          name: accountId
          pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
          required: true
          type: string
        - format: uuid
          in: header
          name: service-id-header
          required: true
          type: string
        - description: request body for confirm Stripe session status
          in: body
          name: body
          required: true
          schema:
            $ref: "#/definitions/SessionData"
      responses:
        "200":
          description: Session status successfully checked
          schema:
            $ref: "#/definitions/StripeSessionConfirmStatus"
        "400":
          description: Validation error
          schema:
            example:
              code: VALIDATION_FAILURE
              message: Wrong request has been sent
              status: 400
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "401":
          description: Unauthenticated
          schema:
            example:
              code: UNAUTHORIZED
              message: Incorrect authentication information
              status: 401
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "403":
          description: Forbidden
          schema:
            example:
              code: FORBIDDEN
              message: You do not have access to the resource
              status: 403
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "500":
          description: Server error
          schema:
            example:
              code: INTERNAL_SERVER_ERROR
              message: Internal Server Error
              status: 500
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "503":
          description: Service unavailable
          schema:
            example:
              code: SERVICE_UNAVAILABLE
              message: Service Unavailable
              status: 503
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
      tags:
        - product
        - member
      operationId: confirmSession
      summary: Confirm Stripe session status
    options:
      summary: CORS preflight handler
      operationId: options_products_accountId_checkout_session_status
      security: []
      parameters:
        - name: accountId
          in: path
          type: string
          required: true
      responses:
        "204":
          description: No content
          schema:
            type: string
        "401":
          description: Cors not allowed
  /services:
    get:
      produces:
        - application/json
      parameters:
        - format: uuid
          in: header
          name: service-id-header
          required: true
          type: string
      responses:
        "200":
          description: Successfully retrieved service information
          schema:
            $ref: "#/definitions/Service"
        "400":
          description: Validation error
          schema:
            example:
              code: VALIDATION_FAILURE
              message: Wrong request has been sent
              status: 400
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "401":
          description: Unauthenticated
          schema:
            example:
              code: UNAUTHORIZED
              message: Incorrect authentication information
              status: 401
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "403":
          description: Forbidden
          schema:
            example:
              code: FORBIDDEN
              message: You do not have access to the resource
              status: 403
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "500":
          description: Server error
          schema:
            example:
              code: INTERNAL_SERVER_ERROR
              message: Internal Server Error
              status: 500
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "503":
          description: Service unavailable
          schema:
            example:
              code: SERVICE_UNAVAILABLE
              message: Service Unavailable
              status: 503
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
      security: []
      tags:
        - service
        - guest
      operationId: fetchService
      summary: Get service info
    post:
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - format: uuid
          in: header
          name: tenant-id-header
          required: true
          type: string
        - description: Register service
          in: body
          name: body
          required: true
          schema:
            $ref: "#/definitions/CreateServiceReq"
      responses:
        "200":
          description: Successfully registered service
          schema:
            $ref: "#/definitions/CreateServiceRes"
        "400":
          description: Validation error
          schema:
            example:
              code: VALIDATION_FAILURE
              message: Wrong request has been sent
              status: 400
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "401":
          description: Unauthenticated
          schema:
            example:
              code: UNAUTHORIZED
              message: Incorrect authentication information
              status: 401
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "403":
          description: Forbidden
          schema:
            example:
              code: FORBIDDEN
              message: You do not have access to the resource
              status: 403
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "500":
          description: Server error
          schema:
            example:
              code: INTERNAL_SERVER_ERROR
              message: Internal Server Error
              status: 500
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "503":
          description: Service unavailable
          schema:
            example:
              code: SERVICE_UNAVAILABLE
              message: Service Unavailable
              status: 503
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
      security:
        - api_key: []
      tags:
        - service
        - admin
      operationId: createService
      summary: Create service
    options:
      summary: CORS preflight handler
      operationId: options_services
      security: []
      responses:
        "204":
          description: No content
          schema:
            type: string
        "401":
          description: Cors not allowed
  /services/actions/{actionId}:
    get:
      produces:
        - application/json
      parameters:
        - description: Uniquely given identifier for each action
          in: path
          name: actionId
          pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
          required: true
          type: string
        - format: uuid
          in: header
          name: service-id-header
          required: true
          type: string
      responses:
        "200":
          description: Successfully retrieved action details
          schema:
            $ref: "#/definitions/ActionDetail"
        "400":
          description: Validation error
          schema:
            example:
              code: VALIDATION_FAILURE
              message: Wrong request has been sent
              status: 400
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "401":
          description: Unauthenticated
          schema:
            example:
              code: UNAUTHORIZED
              message: Incorrect authentication information
              status: 401
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "403":
          description: Forbidden
          schema:
            example:
              code: FORBIDDEN
              message: You do not have access to the resource
              status: 403
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "500":
          description: Server error
          schema:
            example:
              code: INTERNAL_SERVER_ERROR
              message: Internal Server Error
              status: 500
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "503":
          description: Service unavailable
          schema:
            example:
              code: SERVICE_UNAVAILABLE
              message: Service Unavailable
              status: 503
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
      tags:
        - quest
        - member
      operationId: fetchAction
      summary: Get the action detail
    options:
      summary: CORS preflight handler
      operationId: options_services_actions_actionId
      security: []
      parameters:
        - name: actionId
          in: path
          type: string
          required: true
      responses:
        "204":
          description: No content
          schema:
            type: string
        "401":
          description: Cors not allowed
  /services/questionnaire:
    post:
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - format: uuid
          in: header
          name: service-id-header
          required: true
          type: string
        - description: Input information for questionnaire action creation
          in: body
          name: body
          schema:
            $ref: "#/definitions/QuestionnaireCreateRequest"
      responses:
        "200":
          description: successful operation
          schema:
            $ref: "#/definitions/QuestionnaireDetailResponse"
        "400":
          description: Validation error
          schema:
            example:
              code: VALIDATION_FAILURE
              message: Wrong request has been sent
              status: 400
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "401":
          description: Unauthenticated
          schema:
            example:
              code: UNAUTHORIZED
              message: Incorrect authentication information
              status: 401
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "403":
          description: Forbidden
          schema:
            example:
              code: FORBIDDEN
              message: You do not have access to the resource
              status: 403
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "500":
          description: Server error
          schema:
            example:
              code: INTERNAL_SERVER_ERROR
              message: Internal Server Error
              status: 500
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "503":
          description: Service unavailable
          schema:
            example:
              code: SERVICE_UNAVAILABLE
              message: Service Unavailable
              status: 503
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
      security:
        - api_key: []
      tags:
        - quest
        - admin
      operationId: createQuestionnaire
      summary: Create questionnaire action
    options:
      summary: CORS preflight handler
      operationId: options_services_questionnaire
      security: []
      responses:
        "204":
          description: No content
          schema:
            type: string
        "401":
          description: Cors not allowed
  /services/questionnaire/{questionnaireId}:
    get:
      produces:
        - application/json
      parameters:
        - description: Uniquely given identifier for each questionnaire
          in: path
          name: questionnaireId
          pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
          required: true
          type: string
        - format: uuid
          in: header
          name: service-id-header
          required: true
          type: string
      responses:
        "200":
          description: successful operation
          schema:
            $ref: "#/definitions/QuestionnaireReadResponse"
        "400":
          description: Validation error
          schema:
            example:
              code: VALIDATION_FAILURE
              message: Wrong request has been sent
              status: 400
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "401":
          description: Unauthenticated
          schema:
            example:
              code: UNAUTHORIZED
              message: Incorrect authentication information
              status: 401
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "403":
          description: Forbidden
          schema:
            example:
              code: FORBIDDEN
              message: You do not have access to the resource
              status: 403
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "500":
          description: Server error
          schema:
            example:
              code: INTERNAL_SERVER_ERROR
              message: Internal Server Error
              status: 500
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "503":
          description: Service unavailable
          schema:
            example:
              code: SERVICE_UNAVAILABLE
              message: Service Unavailable
              status: 503
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
      tags:
        - quest
        - member
      operationId: GetQuestionnaire
      summary: Get questionnaire action
    put:
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - description: Uniquely given identifier for each questionnaire
          in: path
          name: questionnaireId
          pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
          required: true
          type: string
        - format: uuid
          in: header
          name: service-id-header
          required: true
          type: string
        - description: Input information for questionnaire action update
          in: body
          name: body
          schema:
            $ref: "#/definitions/QuestionnaireUpdateRequest"
      responses:
        "200":
          description: successful operation
          schema:
            $ref: "#/definitions/QuestionnaireDetailResponse"
        "400":
          description: Validation error
          schema:
            example:
              code: VALIDATION_FAILURE
              message: Wrong request has been sent
              status: 400
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "401":
          description: Unauthenticated
          schema:
            example:
              code: UNAUTHORIZED
              message: Incorrect authentication information
              status: 401
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "403":
          description: Forbidden
          schema:
            example:
              code: FORBIDDEN
              message: You do not have access to the resource
              status: 403
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "500":
          description: Server error
          schema:
            example:
              code: INTERNAL_SERVER_ERROR
              message: Internal Server Error
              status: 500
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "503":
          description: Service unavailable
          schema:
            example:
              code: SERVICE_UNAVAILABLE
              message: Service Unavailable
              status: 503
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
      security:
        - api_key: []
      tags:
        - quest
        - admin
      operationId: UpdateQuestionnaire
      summary: Update questionnaire action
    options:
      summary: CORS preflight handler
      operationId: options_services_questionnaire_questionnaireId
      security: []
      parameters:
        - name: questionnaireId
          in: path
          type: string
          required: true
      responses:
        "204":
          description: No content
          schema:
            type: string
        "401":
          description: Cors not allowed
  /services/quests:
    get:
      produces:
        - application/json
      parameters:
        - format: uuid
          in: header
          name: service-id-header
          required: true
          type: string
      responses:
        "200":
          description: Successfully retrieved list of quests
          schema:
            items:
              $ref: "#/definitions/QuestItem"
            type: array
        "400":
          description: Validation error
          schema:
            example:
              code: VALIDATION_FAILURE
              message: Wrong request has been sent
              status: 400
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "401":
          description: Unauthenticated
          schema:
            example:
              code: UNAUTHORIZED
              message: Incorrect authentication information
              status: 401
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "403":
          description: Forbidden
          schema:
            example:
              code: FORBIDDEN
              message: You do not have access to the resource
              status: 403
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "500":
          description: Server error
          schema:
            example:
              code: INTERNAL_SERVER_ERROR
              message: Internal Server Error
              status: 500
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "503":
          description: Service unavailable
          schema:
            example:
              code: SERVICE_UNAVAILABLE
              message: Service Unavailable
              status: 503
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
      tags:
        - service
        - member
      operationId: fetchQuests
      summary: Get a list of Quests
    post:
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - format: uuid
          in: header
          name: service-id-header
          required: true
          type: string
        - description: Create quest
          in: body
          name: body
          required: true
          schema:
            $ref: "#/definitions/QuestCreateRequest"
      responses:
        "200":
          description: Successfully created quest
          schema:
            $ref: "#/definitions/QuestCreateResponse"
        "400":
          description: Validation error
          schema:
            example:
              code: VALIDATION_FAILURE
              message: Wrong request has been sent
              status: 400
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "401":
          description: Unauthenticated
          schema:
            example:
              code: UNAUTHORIZED
              message: Incorrect authentication information
              status: 401
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "403":
          description: Forbidden
          schema:
            example:
              code: FORBIDDEN
              message: You do not have access to the resource
              status: 403
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "500":
          description: Server error
          schema:
            example:
              code: INTERNAL_SERVER_ERROR
              message: Internal Server Error
              status: 500
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "503":
          description: Service unavailable
          schema:
            example:
              code: SERVICE_UNAVAILABLE
              message: Service Unavailable
              status: 503
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
      security:
        - api_key: []
      tags:
        - quest
        - admin
      operationId: createQuest
      summary: Create quest
    options:
      summary: CORS preflight handler
      operationId: options_services_quests
      security: []
      responses:
        "204":
          description: No content
          schema:
            type: string
        "401":
          description: Cors not allowed
  /services/quests/status:
    get:
      produces:
        - application/json
      parameters:
        - format: uuid
          in: header
          name: service-id-header
          required: true
          type: string
      responses:
        "200":
          description: Successfully retrieved list of quests
          schema:
            items:
              $ref: "#/definitions/StatusQuest"
            type: array
        "400":
          description: Validation error
          schema:
            example:
              code: VALIDATION_FAILURE
              message: Wrong request has been sent
              status: 400
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "401":
          description: Unauthenticated
          schema:
            example:
              code: UNAUTHORIZED
              message: Incorrect authentication information
              status: 401
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "403":
          description: Forbidden
          schema:
            example:
              code: FORBIDDEN
              message: You do not have access to the resource
              status: 403
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "500":
          description: Server error
          schema:
            example:
              code: INTERNAL_SERVER_ERROR
              message: Internal Server Error
              status: 500
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "503":
          description: Service unavailable
          schema:
            example:
              code: SERVICE_UNAVAILABLE
              message: Service Unavailable
              status: 503
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
      tags:
        - quest
        - member
      operationId: fetchStatusQuests
      summary: Get Status Quest data
    options:
      summary: CORS preflight handler
      operationId: options_services_quests_status
      security: []
      responses:
        "204":
          description: No content
          schema:
            type: string
        "401":
          description: Cors not allowed
  /services/quests/{questId}:
    get:
      produces:
        - application/json
      parameters:
        - description: Uniquely given identifier for each quest
          in: path
          name: questId
          pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
          required: true
          type: string
        - format: uuid
          in: header
          name: service-id-header
          required: true
          type: string
      responses:
        "200":
          description: Successfully retrieved quest details
          schema:
            $ref: "#/definitions/QuestDetail"
        "400":
          description: Validation error
          schema:
            example:
              code: VALIDATION_FAILURE
              message: Wrong request has been sent
              status: 400
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "401":
          description: Unauthenticated
          schema:
            example:
              code: UNAUTHORIZED
              message: Incorrect authentication information
              status: 401
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "403":
          description: Forbidden
          schema:
            example:
              code: FORBIDDEN
              message: You do not have access to the resource
              status: 403
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "500":
          description: Server error
          schema:
            example:
              code: INTERNAL_SERVER_ERROR
              message: Internal Server Error
              status: 500
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "503":
          description: Service unavailable
          schema:
            example:
              code: SERVICE_UNAVAILABLE
              message: Service Unavailable
              status: 503
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
      tags:
        - quest
        - member
      operationId: fetchQuest
      summary: Get the quest detail
    options:
      summary: CORS preflight handler
      operationId: options_services_quests_questId
      security: []
      parameters:
        - name: questId
          in: path
          type: string
          required: true
      responses:
        "204":
          description: No content
          schema:
            type: string
        "401":
          description: Cors not allowed
  /services/quests/{questId}/actions:
    post:
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - description: Uniquely given identifier for each quest
          in: path
          name: questId
          pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
          required: true
          type: string
        - format: uuid
          in: header
          name: service-id-header
          required: true
          type: string
        - description: Input information for quest action creation
          in: body
          name: body
          schema:
            $ref: "#/definitions/CreateActionRequest"
      responses:
        "200":
          description: successful operation
          schema:
            $ref: "#/definitions/CreateActionResponse"
        "400":
          description: Validation error
          schema:
            example:
              code: VALIDATION_FAILURE
              message: Wrong request has been sent
              status: 400
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "401":
          description: Unauthenticated
          schema:
            example:
              code: UNAUTHORIZED
              message: Incorrect authentication information
              status: 401
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "403":
          description: Forbidden
          schema:
            example:
              code: FORBIDDEN
              message: You do not have access to the resource
              status: 403
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "500":
          description: Server error
          schema:
            example:
              code: INTERNAL_SERVER_ERROR
              message: Internal Server Error
              status: 500
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "503":
          description: Service unavailable
          schema:
            example:
              code: SERVICE_UNAVAILABLE
              message: Service Unavailable
              status: 503
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
      security:
        - api_key: []
      tags:
        - quest
        - admin
      operationId: createQuestAction
      summary: Create quest action
    options:
      summary: CORS preflight handler
      operationId: options_services_quests_questId_actions
      security: []
      parameters:
        - name: questId
          in: path
          type: string
          required: true
      responses:
        "204":
          description: No content
          schema:
            type: string
        "401":
          description: Cors not allowed
  /services/quests/{questId}/rewards:
    post:
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - description: Uniquely given identifier for each quest
          in: path
          name: questId
          pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
          required: true
          type: string
        - format: uuid
          in: header
          name: service-id-header
          required: true
          type: string
        - description: Input information for quest reward creation
          in: body
          name: body
          schema:
            $ref: "#/definitions/QuestRewardCreationRequest"
      responses:
        "200":
          description: successful operation
          schema:
            $ref: "#/definitions/QuestRewardCreationResponse"
        "400":
          description: Validation error
          schema:
            example:
              code: VALIDATION_FAILURE
              message: Wrong request has been sent
              status: 400
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "401":
          description: Unauthenticated
          schema:
            example:
              code: UNAUTHORIZED
              message: Incorrect authentication information
              status: 401
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "403":
          description: Forbidden
          schema:
            example:
              code: FORBIDDEN
              message: You do not have access to the resource
              status: 403
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "500":
          description: Server error
          schema:
            example:
              code: INTERNAL_SERVER_ERROR
              message: Internal Server Error
              status: 500
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "503":
          description: Service unavailable
          schema:
            example:
              code: SERVICE_UNAVAILABLE
              message: Service Unavailable
              status: 503
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
      security:
        - api_key: []
      tags:
        - quest
        - admin
      operationId: createQuestReward
      summary: Create quest reward
    options:
      summary: CORS preflight handler
      operationId: options_services_quests_questId_rewards
      security: []
      parameters:
        - name: questId
          in: path
          type: string
          required: true
      responses:
        "204":
          description: No content
          schema:
            type: string
        "401":
          description: Cors not allowed
  /services/rewards:
    post:
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - format: uuid
          in: header
          name: service-id-header
          required: true
          type: string
        - description: Input information for quest reward creation
          in: body
          name: body
          schema:
            $ref: "#/definitions/RewardCreationRequest"
      responses:
        "200":
          description: successful operation
          schema:
            $ref: "#/definitions/RewardCreationResponse"
        "400":
          description: Validation error
          schema:
            example:
              code: VALIDATION_FAILURE
              message: Wrong request has been sent
              status: 400
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "401":
          description: Unauthenticated
          schema:
            example:
              code: UNAUTHORIZED
              message: Incorrect authentication information
              status: 401
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "403":
          description: Forbidden
          schema:
            example:
              code: FORBIDDEN
              message: You do not have access to the resource
              status: 403
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "500":
          description: Server error
          schema:
            example:
              code: INTERNAL_SERVER_ERROR
              message: Internal Server Error
              status: 500
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "503":
          description: Service unavailable
          schema:
            example:
              code: SERVICE_UNAVAILABLE
              message: Service Unavailable
              status: 503
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
      security:
        - api_key: []
      tags:
        - quest
        - admin
      operationId: createReward
      summary: Create reward
    options:
      summary: CORS preflight handler
      operationId: options_services_rewards
      security: []
      responses:
        "204":
          description: No content
          schema:
            type: string
        "401":
          description: Cors not allowed
  /services/rewards/{rewardId}:
    get:
      produces:
        - application/json
      parameters:
        - description: Uniquely given identifier for each reward
          in: path
          name: rewardId
          pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
          required: true
          type: string
        - format: uuid
          in: header
          name: service-id-header
          required: true
          type: string
      responses:
        "200":
          description: Successfully retrieved reward details
          schema:
            $ref: "#/definitions/RewardDetail"
        "400":
          description: Validation error
          schema:
            example:
              code: VALIDATION_FAILURE
              message: Wrong request has been sent
              status: 400
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "401":
          description: Unauthenticated
          schema:
            example:
              code: UNAUTHORIZED
              message: Incorrect authentication information
              status: 401
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "403":
          description: Forbidden
          schema:
            example:
              code: FORBIDDEN
              message: You do not have access to the resource
              status: 403
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "500":
          description: Server error
          schema:
            example:
              code: INTERNAL_SERVER_ERROR
              message: Internal Server Error
              status: 500
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "503":
          description: Service unavailable
          schema:
            example:
              code: SERVICE_UNAVAILABLE
              message: Service Unavailable
              status: 503
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
      tags:
        - quest
        - member
      operationId: fetchReward
      summary: Get the reward detail
    options:
      summary: CORS preflight handler
      operationId: options_services_rewards_rewardId
      security: []
      parameters:
        - name: rewardId
          in: path
          type: string
          required: true
      responses:
        "204":
          description: No content
          schema:
            type: string
        "401":
          description: Cors not allowed
  /services/serial-codes/create:
    post:
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - format: uuid
          in: header
          name: service-id-header
          required: true
          type: string
        - description: Create serial code
          in: body
          name: body
          required: true
          schema:
            $ref: "#/definitions/SerialCodeCreateReq"
      responses:
        "200":
          description: successful operation
          schema:
            $ref: "#/definitions/SerialCodeGenerationProject"
        "400":
          description: Validation error
          schema:
            example:
              code: VALIDATION_FAILURE
              message: Wrong request has been sent
              status: 400
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "401":
          description: Unauthenticated
          schema:
            example:
              code: UNAUTHORIZED
              message: Incorrect authentication information
              status: 401
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "403":
          description: Forbidden
          schema:
            example:
              code: FORBIDDEN
              message: You do not have access to the resource
              status: 403
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "500":
          description: Server error
          schema:
            example:
              code: INTERNAL_SERVER_ERROR
              message: Internal Server Error
              status: 500
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "503":
          description: Service unavailable
          schema:
            example:
              code: SERVICE_UNAVAILABLE
              message: Service Unavailable
              status: 503
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
      security:
        - api_key: []
      tags:
        - service
        - admin
      operationId: createSerialCode
      summary: Create serial code
    options:
      summary: CORS preflight handler
      operationId: options_services_serial_codes_create
      security: []
      responses:
        "204":
          description: No content
          schema:
            type: string
        "401":
          description: Cors not allowed
  /services/serial-codes/import:
    post:
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - format: uuid
          in: header
          name: service-id-header
          required: true
          type: string
        - description: Import serial code
          in: body
          name: body
          required: true
          schema:
            $ref: "#/definitions/SerialCodesImport"
      responses:
        "200":
          description: successful operation
          schema:
            $ref: "#/definitions/SerialCodeGenerationProject"
        "400":
          description: Validation error
          schema:
            example:
              code: VALIDATION_FAILURE
              message: Wrong request has been sent
              status: 400
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "401":
          description: Unauthenticated
          schema:
            example:
              code: UNAUTHORIZED
              message: Incorrect authentication information
              status: 401
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "403":
          description: Forbidden
          schema:
            example:
              code: FORBIDDEN
              message: You do not have access to the resource
              status: 403
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "500":
          description: Server error
          schema:
            example:
              code: INTERNAL_SERVER_ERROR
              message: Internal Server Error
              status: 500
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "503":
          description: Service unavailable
          schema:
            example:
              code: SERVICE_UNAVAILABLE
              message: Service Unavailable
              status: 503
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
      security:
        - api_key: []
      tags:
        - service
        - admin
      operationId: importSerialCode
      summary: Import serial code
    options:
      summary: CORS preflight handler
      operationId: options_services_serial_codes_import
      security: []
      responses:
        "204":
          description: No content
          schema:
            type: string
        "401":
          description: Cors not allowed
  /services/serial-codes/projects:
    get:
      produces:
        - application/json
      parameters:
        - format: uuid
          in: header
          name: service-id-header
          required: true
          type: string
      responses:
        "200":
          description: successful operation
          schema:
            $ref: "#/definitions/SerialCodeProject"
        "400":
          description: Validation error
          schema:
            example:
              code: VALIDATION_FAILURE
              message: Wrong request has been sent
              status: 400
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "401":
          description: Unauthenticated
          schema:
            example:
              code: UNAUTHORIZED
              message: Incorrect authentication information
              status: 401
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "403":
          description: Forbidden
          schema:
            example:
              code: FORBIDDEN
              message: You do not have access to the resource
              status: 403
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "500":
          description: Server error
          schema:
            example:
              code: INTERNAL_SERVER_ERROR
              message: Internal Server Error
              status: 500
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "503":
          description: Service unavailable
          schema:
            example:
              code: SERVICE_UNAVAILABLE
              message: Service Unavailable
              status: 503
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
      tags:
        - service
        - member
      operationId: getSerialCodeProjects
      summary: Get serial code projects
    options:
      summary: CORS preflight handler
      operationId: options_services_serial_codes_projects
      security: []
      responses:
        "204":
          description: No content
          schema:
            type: string
        "401":
          description: Cors not allowed
  /tenants:
    post:
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - description: successfully registered tenant
          in: body
          name: body
          schema:
            $ref: "#/definitions/TenantInput"
      responses:
        "200":
          description: successful operation
          schema:
            $ref: "#/definitions/Tenant"
        "400":
          description: Validation error
          schema:
            example:
              code: VALIDATION_FAILURE
              message: Wrong request has been sent
              status: 400
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "401":
          description: Unauthenticated
          schema:
            example:
              code: UNAUTHORIZED
              message: Incorrect authentication information
              status: 401
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "403":
          description: Forbidden
          schema:
            example:
              code: FORBIDDEN
              message: You do not have access to the resource
              status: 403
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "500":
          description: Server error
          schema:
            example:
              code: INTERNAL_SERVER_ERROR
              message: Internal Server Error
              status: 500
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "503":
          description: Service unavailable
          schema:
            example:
              code: SERVICE_UNAVAILABLE
              message: Service Unavailable
              status: 503
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
      security:
        - api_key: []
      tags:
        - tenant
        - admin
      operationId: registerTenant
      summary: Register tenant
    options:
      summary: CORS preflight handler
      operationId: options_tenants
      security: []
      responses:
        "204":
          description: No content
          schema:
            type: string
        "401":
          description: Cors not allowed
  /users:
    post:
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - description: Request to create a new user
          in: body
          name: body
          required: true
          schema:
            $ref: "#/definitions/CreateUser"
      responses:
        "201":
          description: successful operation
          schema:
            $ref: "#/definitions/CreatedUser"
        "400":
          description: Validation error
          schema:
            example:
              code: VALIDATION_FAILURE
              message: Wrong request has been sent
              status: 400
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "401":
          description: Unauthenticated
          schema:
            example:
              code: UNAUTHORIZED
              message: Incorrect authentication information
              status: 401
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "403":
          description: Forbidden
          schema:
            example:
              code: FORBIDDEN
              message: You do not have access to the resource
              status: 403
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "500":
          description: Server error
          schema:
            example:
              code: INTERNAL_SERVER_ERROR
              message: Internal Server Error
              status: 500
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "503":
          description: Service unavailable
          schema:
            example:
              code: SERVICE_UNAVAILABLE
              message: Service Unavailable
              status: 503
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
      tags:
        - user
        - member
      operationId: createUser
      summary: Create user
    options:
      summary: CORS preflight handler
      operationId: options_users
      security: []
      responses:
        "204":
          description: No content
          schema:
            type: string
        "401":
          description: Cors not allowed
  /users/{userId}:
    delete:
      produces:
        - application/json
      parameters:
        - description: Uniquely given identifier for each user
          in: path
          minLength: 1
          name: userId
          pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
          required: true
          type: string
      responses:
        "200":
          description: successful operation
        "400":
          description: Validation error
          schema:
            example:
              code: VALIDATION_FAILURE
              message: Wrong request has been sent
              status: 400
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "401":
          description: Unauthenticated
          schema:
            example:
              code: UNAUTHORIZED
              message: Incorrect authentication information
              status: 401
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "403":
          description: Forbidden
          schema:
            example:
              code: FORBIDDEN
              message: You do not have access to the resource
              status: 403
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "500":
          description: Server error
          schema:
            example:
              code: INTERNAL_SERVER_ERROR
              message: Internal Server Error
              status: 500
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "503":
          description: Service unavailable
          schema:
            example:
              code: SERVICE_UNAVAILABLE
              message: Service Unavailable
              status: 503
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
      tags:
        - user
        - member
      operationId: deleteUser
      summary: Delete user by user id
    get:
      produces:
        - application/json
      parameters:
        - description: Uniquely given identifier for each user
          in: path
          minLength: 1
          name: userId
          pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
          required: true
          type: string
        - format: uuid
          in: header
          name: service-id-header
          required: true
          type: string
      responses:
        "200":
          description: successful operation
          schema:
            $ref: "#/definitions/User"
        "400":
          description: Validation error
          schema:
            example:
              code: VALIDATION_FAILURE
              message: Wrong request has been sent
              status: 400
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "401":
          description: Unauthenticated
          schema:
            example:
              code: UNAUTHORIZED
              message: Incorrect authentication information
              status: 401
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "403":
          description: Forbidden
          schema:
            example:
              code: FORBIDDEN
              message: You do not have access to the resource
              status: 403
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "500":
          description: Server error
          schema:
            example:
              code: INTERNAL_SERVER_ERROR
              message: Internal Server Error
              status: 500
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "503":
          description: Service unavailable
          schema:
            example:
              code: SERVICE_UNAVAILABLE
              message: Service Unavailable
              status: 503
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
      tags:
        - user
        - member
      operationId: getUserById
      summary: Get user by user id
    options:
      summary: CORS preflight handler
      operationId: options_users_userId
      security: []
      parameters:
        - name: userId
          in: path
          type: string
          required: true
      responses:
        "204":
          description: No content
          schema:
            type: string
        "401":
          description: Cors not allowed
  /users/{userId}/backup-key:
    get:
      produces:
        - application/json
      parameters:
        - description: Uniquely given identifier for each user
          in: path
          minLength: 1
          name: userId
          pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
          required: true
          type: string
      responses:
        "200":
          description: successful operation
          schema:
            $ref: "#/definitions/ContractAccount"
        "400":
          description: Validation error
          schema:
            example:
              code: VALIDATION_FAILURE
              message: Wrong request has been sent
              status: 400
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "401":
          description: Unauthenticated
          schema:
            example:
              code: UNAUTHORIZED
              message: Incorrect authentication information
              status: 401
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "403":
          description: Forbidden
          schema:
            example:
              code: FORBIDDEN
              message: You do not have access to the resource
              status: 403
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "500":
          description: Server error
          schema:
            example:
              code: INTERNAL_SERVER_ERROR
              message: Internal Server Error
              status: 500
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "503":
          description: Service unavailable
          schema:
            example:
              code: SERVICE_UNAVAILABLE
              message: Service Unavailable
              status: 503
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
      tags:
        - user
        - member
      operationId: fetchUserBackupKey
      summary: Obtain the backup-key
    options:
      summary: CORS preflight handler
      operationId: options_users_userId_backup_key
      security: []
      parameters:
        - name: userId
          in: path
          type: string
          required: true
      responses:
        "204":
          description: No content
          schema:
            type: string
        "401":
          description: Cors not allowed
  /users/{userId}/check:
    get:
      produces:
        - application/json
      parameters:
        - description: Uniquely given identifier for each user
          in: path
          minLength: 1
          name: userId
          pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
          required: true
          type: string
      responses:
        "200":
          description: successful operation
          schema:
            $ref: "#/definitions/UserCheck"
        "400":
          description: Validation error
          schema:
            example:
              code: VALIDATION_FAILURE
              message: Wrong request has been sent
              status: 400
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "401":
          description: Unauthenticated
          schema:
            example:
              code: UNAUTHORIZED
              message: Incorrect authentication information
              status: 401
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "403":
          description: Forbidden
          schema:
            example:
              code: FORBIDDEN
              message: You do not have access to the resource
              status: 403
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "500":
          description: Server error
          schema:
            example:
              code: INTERNAL_SERVER_ERROR
              message: Internal Server Error
              status: 500
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "503":
          description: Service unavailable
          schema:
            example:
              code: SERVICE_UNAVAILABLE
              message: Service Unavailable
              status: 503
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
      tags:
        - user
        - member
      operationId: checkUser
      summary: Check user existence
    options:
      summary: CORS preflight handler
      operationId: options_users_userId_check
      security: []
      parameters:
        - name: userId
          in: path
          type: string
          required: true
      responses:
        "204":
          description: No content
          schema:
            type: string
        "401":
          description: Cors not allowed
  /users/{userId}/contract-account:
    put:
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - description: Uniquely given identifier for each user
          in: path
          minLength: 1
          name: userId
          pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
          required: true
          type: string
        - description: Update user created contract account address
          in: body
          name: body
          required: true
          schema:
            $ref: "#/definitions/ContractAccountAddress"
      responses:
        "200":
          description: successful operation
        "400":
          description: Validation error
          schema:
            example:
              code: VALIDATION_FAILURE
              message: Wrong request has been sent
              status: 400
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "401":
          description: Unauthenticated
          schema:
            example:
              code: UNAUTHORIZED
              message: Incorrect authentication information
              status: 401
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "403":
          description: Forbidden
          schema:
            example:
              code: FORBIDDEN
              message: You do not have access to the resource
              status: 403
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "500":
          description: Server error
          schema:
            example:
              code: INTERNAL_SERVER_ERROR
              message: Internal Server Error
              status: 500
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "503":
          description: Service unavailable
          schema:
            example:
              code: SERVICE_UNAVAILABLE
              message: Service Unavailable
              status: 503
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
      tags:
        - user
        - member
      operationId: updateContractAccount
      summary: Update user contract account
    options:
      summary: CORS preflight handler
      operationId: options_users_userId_contract_account
      security: []
      parameters:
        - name: userId
          in: path
          type: string
          required: true
      responses:
        "204":
          description: No content
          schema:
            type: string
        "401":
          description: Cors not allowed
  /users/{userId}/phone-number:
    put:
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - description: Uniquely given identifier for each user
          in: path
          minLength: 1
          name: userId
          pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
          required: true
          type: string
        - description: Update user registered phone number
          in: body
          name: body
          required: true
          schema:
            $ref: "#/definitions/Phone"
      responses:
        "200":
          description: successful operation
        "400":
          description: Validation error
          schema:
            example:
              code: VALIDATION_FAILURE
              message: Wrong request has been sent
              status: 400
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "401":
          description: Unauthenticated
          schema:
            example:
              code: UNAUTHORIZED
              message: Incorrect authentication information
              status: 401
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "403":
          description: Forbidden
          schema:
            example:
              code: FORBIDDEN
              message: You do not have access to the resource
              status: 403
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "500":
          description: Server error
          schema:
            example:
              code: INTERNAL_SERVER_ERROR
              message: Internal Server Error
              status: 500
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "503":
          description: Service unavailable
          schema:
            example:
              code: SERVICE_UNAVAILABLE
              message: Service Unavailable
              status: 503
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
      tags:
        - user
        - member
      operationId: updateUserPhoneNumber
      summary: Update user phone number
    options:
      summary: CORS preflight handler
      operationId: options_users_userId_phone_number
      security: []
      parameters:
        - name: userId
          in: path
          type: string
          required: true
      responses:
        "204":
          description: No content
          schema:
            type: string
        "401":
          description: Cors not allowed
  /vaults:
    get:
      produces:
        - application/json
      parameters:
        - format: uuid
          in: header
          name: tenant-id-header
          required: true
          type: string
      responses:
        "200":
          description: successful operation
          schema:
            items:
              $ref: "#/definitions/VaultDetails"
            type: array
        "400":
          description: Validation error
          schema:
            example:
              code: VALIDATION_FAILURE
              message: Wrong request has been sent
              status: 400
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "401":
          description: Unauthenticated
          schema:
            example:
              code: UNAUTHORIZED
              message: Incorrect authentication information
              status: 401
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "403":
          description: Forbidden
          schema:
            example:
              code: FORBIDDEN
              message: You do not have access to the resource
              status: 403
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "500":
          description: Server error
          schema:
            example:
              code: INTERNAL_SERVER_ERROR
              message: Internal Server Error
              status: 500
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "503":
          description: Service unavailable
          schema:
            example:
              code: SERVICE_UNAVAILABLE
              message: Service Unavailable
              status: 503
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
      security:
        - api_key: []
      tags:
        - vault
        - admin
      operationId: getVaultDetails
      summary: Retrieve vault details for tenant
    post:
      produces:
        - application/json
      parameters:
        - format: uuid
          in: header
          name: tenant-id-header
          required: true
          type: string
      responses:
        "200":
          description: successful operation
          schema:
            $ref: "#/definitions/VaultKeyInfo"
        "400":
          description: Validation error
          schema:
            example:
              code: VALIDATION_FAILURE
              message: Wrong request has been sent
              status: 400
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "401":
          description: Unauthenticated
          schema:
            example:
              code: UNAUTHORIZED
              message: Incorrect authentication information
              status: 401
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "403":
          description: Forbidden
          schema:
            example:
              code: FORBIDDEN
              message: You do not have access to the resource
              status: 403
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "500":
          description: Server error
          schema:
            example:
              code: INTERNAL_SERVER_ERROR
              message: Internal Server Error
              status: 500
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "503":
          description: Service unavailable
          schema:
            example:
              code: SERVICE_UNAVAILABLE
              message: Service Unavailable
              status: 503
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
      security:
        - api_key: []
      tags:
        - vault
        - admin
      operationId: createVault
      summary: Generate vault key for tenant
    options:
      summary: CORS preflight handler
      operationId: options_vaults
      security: []
      responses:
        "204":
          description: No content
          schema:
            type: string
        "401":
          description: Cors not allowed
  /vaults/nonce:
    put:
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - format: uuid
          in: header
          name: tenant-id-header
          required: true
          type: string
        - description: Details for updating vault nonce
          in: body
          name: body
          required: true
          schema:
            properties:
              nonce:
                type: number
              vaultKeyId:
                pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
                type: string
            required:
              - vaultKeyId
              - nonce
            type: object
      responses:
        "200":
          description: successful operation
          schema:
            properties:
              nonce:
                type: number
              vaultKeyId:
                pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
                type: string
            required:
              - vaultKeyId
              - nonce
            type: object
        "400":
          description: Validation error
          schema:
            example:
              code: VALIDATION_FAILURE
              message: Wrong request has been sent
              status: 400
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "401":
          description: Unauthenticated
          schema:
            example:
              code: UNAUTHORIZED
              message: Incorrect authentication information
              status: 401
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "403":
          description: Forbidden
          schema:
            example:
              code: FORBIDDEN
              message: You do not have access to the resource
              status: 403
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "500":
          description: Server error
          schema:
            example:
              code: INTERNAL_SERVER_ERROR
              message: Internal Server Error
              status: 500
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "503":
          description: Service unavailable
          schema:
            example:
              code: SERVICE_UNAVAILABLE
              message: Service Unavailable
              status: 503
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
      security:
        - api_key: []
      tags:
        - vault
        - admin
      operationId: updateVaultNonce
      summary: Update vault nonce
    options:
      summary: CORS preflight handler
      operationId: options_vaults_nonce
      security: []
      responses:
        "204":
          description: No content
          schema:
            type: string
        "401":
          description: Cors not allowed
  /webhook/checkout:
    post:
      produces:
        - application/json
      parameters:
        - in: header
          minLength: 1
          name: Stripe-Signature
          required: true
          type: string
      responses:
        "200":
          description: successful operation
        "400":
          description: Validation error
          schema:
            example:
              code: VALIDATION_FAILURE
              message: Wrong request has been sent
              status: 400
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "401":
          description: Unauthenticated
          schema:
            example:
              code: UNAUTHORIZED
              message: Incorrect authentication information
              status: 401
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "403":
          description: Forbidden
          schema:
            example:
              code: FORBIDDEN
              message: You do not have access to the resource
              status: 403
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "500":
          description: Server error
          schema:
            example:
              code: INTERNAL_SERVER_ERROR
              message: Internal Server Error
              status: 500
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "503":
          description: Service unavailable
          schema:
            example:
              code: SERVICE_UNAVAILABLE
              message: Service Unavailable
              status: 503
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
      security: []
      tags:
        - product
        - system
      operationId: handleStripeEvents
      summary: Handle Stripe events
    options:
      summary: CORS preflight handler
      operationId: options_webhook_checkout
      security: []
      responses:
        "204":
          description: No content
          schema:
            type: string
        "401":
          description: Cors not allowed
  /webhook/transaction:
    post:
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - in: header
          name: X-Alchemy-Signature
          required: false
          type: string
        - description: Transaction receipt
          in: body
          name: body
          required: true
          schema:
            x-nullable: true
      responses:
        "200":
          description: Receive transaction
          schema:
            $ref: "#/definitions/WebhookTransactionResponse"
        "400":
          description: Validation error
          schema:
            example:
              code: VALIDATION_FAILURE
              message: Wrong request has been sent
              status: 400
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "401":
          description: Unauthenticated
          schema:
            example:
              code: UNAUTHORIZED
              message: Incorrect authentication information
              status: 401
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "403":
          description: Forbidden
          schema:
            example:
              code: FORBIDDEN
              message: You do not have access to the resource
              status: 403
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "500":
          description: Server error
          schema:
            example:
              code: INTERNAL_SERVER_ERROR
              message: Internal Server Error
              status: 500
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "503":
          description: Service unavailable
          schema:
            example:
              code: SERVICE_UNAVAILABLE
              message: Service Unavailable
              status: 503
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
      security: []
      tags:
        - nft
        - system
      operationId: webhookTransaction
      summary: Webhook for transaction receipt
    options:
      summary: CORS preflight handler
      operationId: options_webhook_transaction
      security: []
      responses:
        "204":
          description: No content
          schema:
            type: string
        "401":
          description: Cors not allowed
  /{nftId}/{tokenId}:
    get:
      produces:
        - application/json
      parameters:
        - description: Uniquely given identifier for each NFT(ERC721, ERC1155)
          in: path
          name: nftId
          pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
          required: true
          type: string
        - description: Token id of NFT(ERC721, ERC1155)
          in: path
          name: tokenId
          required: true
          type: string
      responses:
        "200":
          description: successful operation
          schema:
            $ref: "#/definitions/GeneralMetadata"
        "400":
          description: Validation error
          schema:
            example:
              code: VALIDATION_FAILURE
              message: Wrong request has been sent
              status: 400
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "401":
          description: Unauthenticated
          schema:
            example:
              code: UNAUTHORIZED
              message: Incorrect authentication information
              status: 401
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "403":
          description: Forbidden
          schema:
            example:
              code: FORBIDDEN
              message: You do not have access to the resource
              status: 403
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "500":
          description: Server error
          schema:
            example:
              code: INTERNAL_SERVER_ERROR
              message: Internal Server Error
              status: 500
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
        "503":
          description: Service unavailable
          schema:
            example:
              code: SERVICE_UNAVAILABLE
              message: Service Unavailable
              status: 503
            properties:
              code:
                description: Application-specific error code
                example: VALIDATION_FAILURE
                type: string
              message:
                description: A human-readable message providing more details about the error
                example: Wrong request has been sent
                type: string
              status:
                description: HTTP status code of the error
                example: 400
                type: integer
            required:
              - status
              - code
              - message
            type: object
      security: []
      tags:
        - nft
        - guest
      operationId: getMetadataOld
      summary: Get metadata
    options:
      summary: CORS preflight handler
      operationId: options_nftId_tokenId
      security: []
      parameters:
        - name: nftId
          in: path
          type: string
          required: true
        - name: tokenId
          in: path
          type: string
          required: true
      responses:
        "204":
          description: No content
          schema:
            type: string
        "401":
          description: Cors not allowed
  /docs/full/openapi.json:
    get:
      summary: Retrieve /docs/full/openapi.json
      operationId: get_docs_openapi_json
      security:
        - api_key: []
      responses:
        "200":
          description: OK
          schema:
            type: string
  /docs/front/openapi.json:
    get:
      summary: Retrieve /docs/front/openapi.json
      operationId: get_docs_front_openapi_json
      security:
        - api_key: []
      responses:
        "200":
          description: OK
          schema:
            type: string
  /docs/admin/openapi.json:
    get:
      summary: Retrieve /docs/admin/openapi.json
      operationId: get_docs_admin_openapi_json
      security:
        - api_key: []
      responses:
        "200":
          description: OK
          schema:
            type: string
definitions:
  Account:
    properties:
      accountId:
        description: Uniquely given identifier for each account
        example: 28cc7f3c-b47d-486e-b035-************
        pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
        type: string
      displayName:
        description: user nasme stored in LINE profile information
        example: LINE-user-name
        type: string
      membership:
        description: Membership information
        properties:
          contractAddress:
            description: ERC721 contract address
            example: "******************************************"
            pattern: ^0x[a-fA-F0-9]{40}$
            type: string
          tokenId:
            description: ERC721 tokenId
            example: 100
            type: number
        type: object
      membershipNftImageUrl:
        description: URL of the membership NFT image
        example: https://example.com/images/membership-nft.png
        pattern: ^(ipfs:\/\/[A-Za-z0-9]+|ar:\/\/[A-Za-z0-9_-]+|https:\/\/[A-Za-z0-9.-]+(?:\/[A-Za-z0-9._~:/?#[\]@!$&'()*+,;=%-]*)?)$
        type: string
      profileImage:
        description: user image stored in LINE profile information
        example: https://profile.image
        pattern: ^(ipfs:\/\/[A-Za-z0-9]+|ar:\/\/[A-Za-z0-9_-]+|https:\/\/[A-Za-z0-9.-]+(?:\/[A-Za-z0-9._~:/?#[\]@!$&'()*+,;=%-]*)?)$
        type: string
      tokenBoundAccountAddress:
        description: ContractAddress for Smart Contract Account linked to ERC721 in
          membership
        example: "******************************************"
        pattern: ^0x[a-fA-F0-9]{40}$
        type: string
    type: object
  AccountQuestionnaireDetail:
    additionalProperties: false
    properties:
      questionnaireId:
        description: Uniquely given identifier for each questionnaire
        example: cb9555c4-07e5-4137-a22d-44291faeb2c6
        pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
        type: string
      questionnaireThemes:
        description: List of themes in the questionnaire
        example: []
        items:
          $ref: "#/definitions/QuestionnaireTheme"
        type: array
      result:
        description: Set only if result of quiz
        properties:
          currentPoint:
            description: Points currently earned
            example: 45
            type: number
          isPassed:
            description: is action completed or not
            example: true
            type: boolean
          maxPoint:
            description: Maximum points possible
            example: 50
            type: number
          rank:
            description: Rank level
            example: 1
            type: number
          rankHeaderAnimation:
            description: URL for rank header animation
            example: https://cdn.rive.app/animations/vehicles.riv
            pattern: ^https:\/\/.*$
            type: string
          rankId:
            description: Identifier for the rank
            example: 4ae4cc33-5e08-4903-a6e6-7af79479c054
            pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$
            type: string
          rankName:
            description: Name of the rank
            example: rank
            type: string
        required:
          - rankId
          - rankName
          - rank
          - rankHeaderAnimation
          - isPassed
        type: object
    required:
      - questionnaireId
      - questionnaireThemes
    type: object
  AchievementAction:
    properties:
      actionId:
        description: Uniquely given identifier for each action
        example: 8552df83-cbd5-44db-b67e-0cbeb2785918
        pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
        type: string
      milestone:
        description: Uniquely given identifier for each action
        example: 1
        type: integer
        x-nullable: true
      rewardId:
        description: Uniquely given identifier for each reward
        example: 449B5E6E-B682-4B18-B66E-74C69425B75D
        pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
        type: string
      statusRank:
        description: Uniquely given identifier for each action
        example: 1
        type: integer
        x-nullable: true
      title:
        description: Uniquely given identifier for each action
        example: 8552df83-cbd5-44db-b67e-0cbeb2785918
        pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
        type: string
    required:
      - actionId
      - title
      - rewardId
      - milestone
      - statusRank
    type: object
  Action:
    properties:
      actionId:
        description: Uniquely given identifier for each action
        example: 28cc7f3c-b47d-486e-b035-************
        pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
        type: string
      actionType:
        description: Type to classify action details.
        enum:
          - ACHIEVEMENT
          - QR-CHECKIN
          - ONLINE-CHECKIN
          - QUESTIONNAIRE
          - SERIAL-CODE
        example: ONLINE-CHECKIN
        type: string
      availableEndDate:
        description: End date and time when the action is disabled
        example: 2024-01-20T10:00:00Z
        pattern: ^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])T([01]\d|2[0-3]):([0-5]\d):([0-5]\d).(\d\d\d)(Z|([+-](0[0-9]|1[0-3]):([0-5]\d)))?$
        type: string
      availableStartDate:
        description: Start date and time when the action is activated
        example: 2024-01-01T10:00:00Z
        pattern: ^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])T([01]\d|2[0-3]):([0-5]\d):([0-5]\d).(\d\d\d)(Z|([+-](0[0-9]|1[0-3]):([0-5]\d)))?$
        type: string
      orderIndex:
        description: quest item order priority (default is desc and >= 0)
        example: 0
        type: integer
        x-nullable: true
      thumbnailImageUrl:
        description: URL of the Action thumbnail image
        example: https://marbullx.com/logo
        pattern: ^(ipfs:\/\/[A-Za-z0-9]+|ar:\/\/[A-Za-z0-9_-]+|https:\/\/[A-Za-z0-9.-]+(?:\/[A-Za-z0-9._~:/?#[\]@!$&'()*+,;=%-]*)?)$
        type: string
      title:
        description: The name of the action
        example: Test Action
        pattern: ^.{4,128}$
        type: string
    required:
      - actionId
      - title
      - thumbnailImageUrl
      - orderIndex
      - availableStartDate
      - availableEndDate
      - actionType
    type: object
  ActionAchievement:
    properties:
      milestone:
        description: Number of quests completed to achieve an achievement
        example: 9
        type: integer
        x-nullable: true
      priority:
        description: Priority of the achievement. The higher the number, the higher the
          priority.
        example: 0
        type: integer
        x-nullable: true
      type:
        enum:
          - ACHIEVEMENT
        type: string
    required:
      - type
      - priority
      - milestone
    type: object
  ActionComplete:
    properties:
      actionType:
        description: Type to classify action details.
        enum:
          - ACHIEVEMENT
          - QR-CHECKIN
          - ONLINE-CHECKIN
          - QUESTIONNAIRE
          - SERIAL-CODE
        example: ONLINE-CHECKIN
        type: string
      answerData:
        description: Base64 encoded data from survey responses saved in JSON format.
          Required only QUESTIONNAIRE action.
        properties:
          questionAnswers:
            description: List of question answers. Each item includes questionId and answer.
            example: []
            items:
              properties:
                answer:
                  description: Answer provided for the question
                  example: "100"
                  type: string
                  x-nullable: true
                questionId:
                  description: Uniquely given identifier for each question
                  example: 8552df83-cbd5-44db-b67e-0cbeb2785918
                  pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
                  type: string
              required:
                - questionId
                - answer
              type: object
            type: array
          questionnaireId:
            description: Questionnaire identifier
            example: 8552df83-cbd5-44db-b67e-0cbeb2785918
            pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
            type: string
        required:
          - questionnaireId
          - questionAnswers
        type: object
      qrVerificationData:
        description: Data acquired by reading with QR reading. Required only QR-CHECKIN
          action.
        example: action-qr-test-id
        pattern: ^.+$
        type: string
      serialCodeData:
        description: Data acquired by serial code action. Required only SERIAL-CODE action.
        example: A23B-C456-DEF7-89GH
        maxLength: 36
        minLength: 4
        pattern: ^.+$
        type: string
      serialCodeProjectId:
        description: Data acquired by serial code action. Required only SERIAL-CODE action.
        example: 8552df83-cbd5-44db-b67e-0cbeb2785918
        pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
        type: string
    required:
      - actionType
    type: object
  ActionDetail:
    properties:
      actionDetailInfo:
        $ref: "#/definitions/ActionDetailInfo"
      actionId:
        description: Uniquely given identifier for each action
        example: 28cc7f3c-b47d-486e-b035-************
        pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
        type: string
      actionLabel:
        description: Name of the button to execute the action
        example: Use Coupon
        pattern: ^.{4,16}$
        type: string
      actionType:
        description: Type to classify action details.
        enum:
          - ACHIEVEMENT
          - QR-CHECKIN
          - ONLINE-CHECKIN
          - QUESTIONNAIRE
          - SERIAL-CODE
        example: ONLINE-CHECKIN
        type: string
      availableEndDate:
        description: End date and time when the action is disabled
        example: 2024-01-20T10:00:00Z
        pattern: ^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])T([01]\d|2[0-3]):([0-5]\d):([0-5]\d).(\d\d\d)(Z|([+-](0[0-9]|1[0-3]):([0-5]\d)))?$
        type: string
      availableStartDate:
        description: Start date and time when the action is activated
        example: 2024-01-01T10:00:00Z
        pattern: ^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])T([01]\d|2[0-3]):([0-5]\d):([0-5]\d).(\d\d\d)(Z|([+-](0[0-9]|1[0-3]):([0-5]\d)))?$
        type: string
      coverImageUrl:
        description: URL of the Reward cover image
        example: https://marbullx.com/action
        pattern: ^(ipfs:\/\/[A-Za-z0-9]+|ar:\/\/[A-Za-z0-9_-]+|https:\/\/[A-Za-z0-9.-]+(?:\/[A-Za-z0-9._~:/?#[\]@!$&'()*+,;=%-]*)?)$
        type: string
      description:
        description: Markdown text encoded in base64
        example: IyBIZWxsbyB3b3JsZCEKIyMgcG9saWN5IGRhdGEKVGhpcyBpcyB3aGVyZSB0aGUgdGV4dCBvZiB0aGUgcG9saWN5IGRhdGEgd2lsbCBiZSBsaXN0ZWQuCgotIGhpbnQgMQotIGhpbnQgMgoKW2ltYWdlXShodHRwczovL2ltYWdlLmNvbSkKCg==
        format: byte
        type: string
      title:
        description: The name of the action
        example: Test Action
        pattern: ^.{4,128}$
        type: string
    required:
      - actionId
      - title
      - coverImageUrl
      - description
      - actionType
      - availableStartDate
      - availableEndDate
    type: object
  ActionDetailInfo:
    description: |-
      Select and specify the propety that matches the actionType
        * ACHIEVEMENT: priority, title, milestone
        * ONLINE-CHECKIN: targetUrl
        * QUESTIONNAIRE: questionnaireId
        * SERIAL-CODE: serialCodeProjectId
  ActionOnlineCheckin:
    properties:
      targetUrl:
        description: URL of the checkin target page
        example: https://marbullx.com/test/image
        pattern: ^(ipfs:\/\/[A-Za-z0-9]+|ar:\/\/[A-Za-z0-9_-]+|https:\/\/[A-Za-z0-9.-]+(?:\/[A-Za-z0-9._~:/?#[\]@!$&'()*+,;=%-]*)?)$
        type: string
      type:
        enum:
          - ONLINE-CHECKIN
        type: string
    required:
      - type
      - targetUrl
    type: object
  ActionQuestionnaire:
    properties:
      questionnaireId:
        description: The uniquely given id of the questionnaire
        example: 9b9767a1-255e-4dcf-9618-7679d6370404
        pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
        type: string
      type:
        enum:
          - QUESTIONNAIRE
        type: string
    required:
      - type
      - questionnaireId
    type: object
  ActionSerialCode:
    properties:
      serialCodeProjectId:
        description: The uniquely given id of the serial code project
        example: 9b9767a1-255e-4dcf-9618-7679d6370404
        pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
        type: string
      type:
        enum:
          - SERIAL-CODE
        type: string
    required:
      - type
      - serialCodeProjectId
    type: object
  ActionSetAchievement:
    properties:
      milestone:
        description: Number of quests completed to achieve an achievement
        example: 9
        type: integer
        x-nullable: true
      rewardId:
        description: The uniquely given id of the reward
        example: 8552df83-cbd5-44db-b67e-0cbeb2785918
        pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
        type: string
      statusRank:
        description: Status rank of the achievement. The higher the number, the higher
          the status.
        example: 0
        type: integer
        x-nullable: true
      type:
        enum:
          - ACHIEVEMENT
        type: string
    required:
      - type
      - rewardId
      - statusRank
      - milestone
    type: object
  ActionSetDetailInfo:
    description: |-
      Select and specify the propety that matches the actionType
        * ACHIEVEMENT: priority, title, milestone
        * ONLINE-CHECKIN: targetUrl
        * QR-CHECKIN: qrVerificationData
        * QUESTIONNAIRE: questionnaireId
        * SERIAL-CODE: serialCodeProjectId
  ActionSetOnlineCheckin:
    properties:
      targetUrl:
        description: URL of the checkin target page
        example: https://marbullx.com/test/image
        pattern: ^(ipfs:\/\/[A-Za-z0-9]+|ar:\/\/[A-Za-z0-9_-]+|https:\/\/[A-Za-z0-9.-]+(?:\/[A-Za-z0-9._~:/?#[\]@!$&'()*+,;=%-]*)?)$
        type: string
      type:
        enum:
          - ONLINE-CHECKIN
        type: string
    required:
      - type
      - targetUrl
    type: object
  ActionSetQrCheckin:
    properties:
      qrVerificationData:
        description: UUID of the QR code verification data
        example: 9b9767a1-255e-4dcf-9618-7679d6370404
        pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
        type: string
      type:
        enum:
          - QR-CHECKIN
        type: string
    required:
      - type
      - qrVerificationData
    type: object
  ActionSetQuestionnaire:
    properties:
      questionnaireId:
        description: The uniquely given id of the questionnaire
        example: 9b9767a1-255e-4dcf-9618-7679d6370404
        pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
        type: string
      type:
        enum:
          - QUESTIONNAIRE
        type: string
    required:
      - type
      - questionnaireId
    type: object
  ActionSetSerialCode:
    properties:
      serialCodeProjectId:
        description: The uniquely given id of the serial code project
        example: 9b9767a1-255e-4dcf-9618-7679d6370404
        pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
        type: string
      type:
        enum:
          - SERIAL-CODE
        type: string
    required:
      - type
      - serialCodeProjectId
    type: object
  ActivityHistory:
    properties:
      completedActions:
        description: List of completed actions
        example: []
        items:
          $ref: "#/definitions/CompletedActionHistory"
        type: array
      completedQuests:
        description: List of quest completion information
        example: []
        items:
          $ref: "#/definitions/CompletedQuestHistory"
        type: array
      rewards:
        description: List of information on acquisition and use of rewards
        example: []
        items:
          $ref: "#/definitions/RewardHistory"
        type: array
    type: object
  AdminApiKeyHeader:
    description: API Key for admin authorization. Use this to authenticate
      admin-level operations.
    type: string
  BulkMintNft:
    properties:
      mintTransactions:
        items:
          $ref: "#/definitions/MintTransactionInfo"
        type: array
    required:
      - mintTransactions
    type: object
  CertificateNft:
    properties:
      certificates:
        description: List of certificate NFTs with overview and detail
        items:
          $ref: "#/definitions/CertificateNftMetadata"
        type: array
      type:
        enum:
          - CERTIFICATE
        type: string
    required:
      - type
      - certificates
    type: object
  CertificateNftDetail:
    properties:
      expireDate:
        description: The expiration date of the coupon
        example: 2024-01-01T10:00:00Z
        type: string
      issuer:
        description: The issuer of the certificate
        example: Marbull
        type: string
      publishDate:
        description: The start date when the coupon is available
        example: 2024-01-01T10:00:00Z
        type: string
    required:
      - publishDate
      - expireDate
      - issuer
    type: object
  CertificateNftMetadata:
    allOf:
      - $ref: "#/definitions/NftMetadata"
      - $ref: "#/definitions/CertificateNftOverview"
      - $ref: "#/definitions/CertificateNftDetail"
    description: certificate NFTs with overview and detail
  CertificateNftOverview:
    properties:
      tags:
        description: Tags indicating standard or status
        enum:
          - DIGITAL-CONTENT
          - DISCOUNT-COUPON
          - EXCHANGE-COUPON
          - STATUS-CERTIFICATE
          - STANDARD-CERTIFICATE
        example: STATUS-CERTIFICATE
        type: string
      rank:
        description: >-
          - When status is "status", the higher the number, the higher the
          priority certificate, and the higher the number, the higher the
          priority certificate.

          - Always 0 for standard
        example: 3
        type: number
    required:
      - tags
    type: object
  ClaimedReward:
    properties:
      coverImageUrl:
        description: URL of the Reward cover image
        example: https://marbullx.com/reward
        pattern: ^(ipfs:\/\/[A-Za-z0-9]+|ar:\/\/[A-Za-z0-9_-]+|https:\/\/[A-Za-z0-9.-]+(?:\/[A-Za-z0-9._~:/?#[\]@!$&'()*+,;=%-]*)?)$
        type: string
      rewardId:
        description: The uniquely given id of the reward
        example: 8552df83-cbd5-44db-b67e-0cbeb2785918
        pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
        type: string
      rewardType:
        enum:
          - DIGITAL-CONTENT
          - DISCOUNT-COUPON
          - EXCHANGE-COUPON
          - STATUS-CERTIFICATE
          - STANDARD-CERTIFICATE
        type: string
      title:
        description: The name of the reward
        example: Test Reward
        pattern: ^.{4,128}$
        type: string
    required:
      - rewardId
      - title
      - coverImageUrl
      - rewardType
    type: object
  CompletedActionHistory:
    properties:
      completedTime:
        description: Date and time the action was completed
        example: 2024-01-01T10:00:00Z
        pattern: ^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])T([01]\d|2[0-3]):([0-5]\d):([0-5]\d).(\d\d\d)(Z|([+-](0[0-9]|1[0-3]):([0-5]\d)))?$
        type: string
      thumbnailImageUrl:
        description: Thumbnail image of Action
        example: https://marbullx.com/icon/action/1
        pattern: ^(ipfs:\/\/[A-Za-z0-9]+|ar:\/\/[A-Za-z0-9_-]+|https:\/\/[A-Za-z0-9.-]+(?:\/[A-Za-z0-9._~:/?#[\]@!$&'()*+,;=%-]*)?)$
        type: string
      title:
        description: Title of Action
        example: Action123
        pattern: ^.+$
        type: string
    type: object
  CompletedQuestHistory:
    properties:
      completedTime:
        description: Date and time the quest was completed
        example: 2024-01-01T10:00:00Z
        pattern: ^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])T([01]\d|2[0-3]):([0-5]\d):([0-5]\d).(\d\d\d)(Z|([+-](0[0-9]|1[0-3]):([0-5]\d)))?$
        type: string
      thumbnailImageUrl:
        description: Thumbnail image of Quest
        example: https://marbullx.com/icon/quest/1
        pattern: ^(ipfs:\/\/[A-Za-z0-9]+|ar:\/\/[A-Za-z0-9_-]+|https:\/\/[A-Za-z0-9.-]+(?:\/[A-Za-z0-9._~:/?#[\]@!$&'()*+,;=%-]*)?)$
        type: string
      title:
        description: Title of Quest
        example: Quest123
        pattern: ^.+$
        type: string
    type: object
  ConsumptionPrepare:
    properties:
      contractAddress:
        pattern: ^0x[a-fA-F0-9]{40}$
        type: string
      tokenId:
        type: number
    required:
      - contractAddress
      - tokenId
    type: object
  ContentNft:
    properties:
      contents:
        description: List of content NFTs with overview
        items:
          $ref: "#/definitions/ContentNftMetadata"
        type: array
      type:
        enum:
          - CONTENT
        type: string
    required:
      - type
      - contents
    type: object
  ContentNftMetadata:
    allOf:
      - $ref: "#/definitions/NftMetadata"
      - $ref: "#/definitions/ContentNftOverview"
    description: content NFTs with overview
  ContentNftOverview:
    properties:
      tags:
        description: Tags indicating digital content
        enum:
          - DIGITAL-CONTENT
          - DISCOUNT-COUPON
          - EXCHANGE-COUPON
          - STATUS-CERTIFICATE
          - STANDARD-CERTIFICATE
        example: DIGITAL-CONTENT
        type: string
    required:
      - tags
    type: object
  ContractAccount:
    properties:
      contractAccountAddress:
        description: |-
          * Address format with EVM checksum according to `EIP-55`
          * Perform checksum verification
        example: "******************************************"
        pattern: ^0x[a-fA-F0-9]{40}$
        type: string
      mnemonicBackupKey:
        description: Hex encoded AES GSM 256 common key
        example: "0x51599a5cd6ac766ff116c8e56dc8d8f006a3f8f11f3230a6ae784a2c491dffa1"
        pattern: ^0x[a-fA-F0-9]{64}$
        type: string
    required:
      - mnemonicBackupKey
    type: object
  ContractAccountAddress:
    properties:
      contractAccountAddress:
        description: |-
          * Address format with EVM checksum according to `EIP-55`
          * Perform checksum verification
        example: "******************************************"
        pattern: ^0x[a-fA-F0-9]{40}$
        type: string
    required:
      - contractAccountAddress
    type: object
  ContractItemSchema:
    properties:
      nftContractAddress:
        description: The address of the NFT contract
        example: "******************************************"
        pattern: ^0x[a-fA-F0-9]{40}$
        type: string
      nftContractId:
        description: The uniquely given id of the nft contract
        example: cee3e4aa-b0bf-482d-bb70-57a979c032c5
        pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
        type: string
    required:
      - nftContractId
      - nftContractAddress
    type: object
  CouponNft:
    properties:
      coupons:
        description: List of coupon NFTs with overview and detail
        items:
          $ref: "#/definitions/CouponNftMetadata"
        type: array
      type:
        enum:
          - COUPON
        type: string
    required:
      - type
      - coupons
    type: object
  CouponNftDetail:
    properties:
      discountAmount:
        description: The discount amount associated with the coupon
        example: 1000
        format: float
        type: number
      discountUnit:
        description: The unit of the discount amount
        example: JPY
        type: string
      exchangeProduct:
        description: The product to be exchanged
        example: Product A
        type: string
      janCode:
        description: JAN code of the coupon. accept null
        example: "1234567890128"
        pattern: ^\d{8}$|^\d{13}$
        type: string
    type: object
  CouponNftMetadata:
    allOf:
      - $ref: "#/definitions/NftMetadata"
      - $ref: "#/definitions/CouponNftOverview"
      - $ref: "#/definitions/CouponNftDetail"
    description: coupon NFTs with overview and detail
  CouponNftOverview:
    properties:
      tags:
        description: Tags indicating discount or exchange
        enum:
          - DIGITAL-CONTENT
          - DISCOUNT-COUPON
          - EXCHANGE-COUPON
          - STATUS-CERTIFICATE
          - STANDARD-CERTIFICATE
        example: DISCOUNT-COUPON
        type: string
      fromDate:
        description: The start date when the coupon is available
        example: 2024-01-01T10:00:00Z
        pattern: ^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])T([01]\d|2[0-3]):([0-5]\d):([0-5]\d).(\d\d\d)(Z|([+-](0[0-9]|1[0-3]):([0-5]\d)))?$
        type: string
      toDate:
        description: The expiration date of the coupon
        example: 2024-01-01T10:00:00Z
        type: string
    required:
      - fromDate
      - toDate
      - tags
    type: object
  CreateActionRequest:
    properties:
      actionDetailInfo:
        $ref: "#/definitions/ActionSetDetailInfo"
      actionTranslations:
        items:
          properties:
            description:
              minLength: 1
              type: string
            label:
              maxLength: 128
              minLength: 1
              type: string
            language:
              description: Language code
              enum:
                - de
                - en-GB
                - en-US
                - es
                - fr
                - it
                - ja
                - ko
                - pt
                - ru
                - th
                - zh-Hans
                - zh-Hant
              example: en-US
              type: string
            title:
              maxLength: 256
              minLength: 1
              type: string
          required:
            - language
            - title
            - description
          type: object
        type: array
      availableEndDate:
        pattern: ^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])T([01]\d|2[0-3]):([0-5]\d):([0-5]\d)(?:\.(\d{3}))?Z$
        type: string
      availableStartDate:
        pattern: ^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])T([01]\d|2[0-3]):([0-5]\d):([0-5]\d)(?:\.(\d{3}))?Z$
        type: string
      coverImageUrl:
        format: uri
        maxLength: 512
        type: string
      geofenceId:
        minLength: 1
        type: string
        x-nullable: true
      orderIndex:
        exclusiveMinimum: true
        minimum: 0
        type: integer
      thumbnailImageUrl:
        format: uri
        maxLength: 512
        type: string
    required:
      - actionTranslations
      - thumbnailImageUrl
      - coverImageUrl
      - availableStartDate
      - availableEndDate
      - orderIndex
      - actionDetailInfo
    type: object
  CreateActionResponse:
    properties:
      actionDetailInfo:
        $ref: "#/definitions/ActionSetDetailInfo"
      actionId:
        format: uuid
        type: string
      actionTranslations:
        items:
          properties:
            description:
              minLength: 1
              type: string
            label:
              maxLength: 128
              minLength: 1
              type: string
            language:
              description: Language code
              enum:
                - de
                - en-GB
                - en-US
                - es
                - fr
                - it
                - ja
                - ko
                - pt
                - ru
                - th
                - zh-Hans
                - zh-Hant
              example: en-US
              type: string
            title:
              maxLength: 256
              minLength: 1
              type: string
          required:
            - language
            - title
            - description
          type: object
        type: array
      availableEndDate:
        pattern: ^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])T([01]\d|2[0-3]):([0-5]\d):([0-5]\d)(?:\.(\d{3}))?Z$
        type: string
      availableStartDate:
        pattern: ^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])T([01]\d|2[0-3]):([0-5]\d):([0-5]\d)(?:\.(\d{3}))?Z$
        type: string
      coverImageUrl:
        format: uri
        maxLength: 512
        type: string
      geofenceId:
        minLength: 1
        type: string
        x-nullable: true
      orderIndex:
        exclusiveMinimum: true
        minimum: 0
        type: integer
      serviceId:
        format: uuid
        type: string
      thumbnailImageUrl:
        format: uri
        maxLength: 512
        type: string
    required:
      - actionId
      - serviceId
      - actionTranslations
      - thumbnailImageUrl
      - coverImageUrl
      - availableStartDate
      - availableEndDate
      - orderIndex
      - actionDetailInfo
    type: object
  CreateQuestion:
    properties:
      answerPoint:
        example: 10
        type: number
      isRequired:
        description: Indicates if the question is required
        example: true
        type: boolean
      questionImageUrl:
        description: URL for the question image
        example: ""
        format: uri
        pattern: ^https://.*$
        type: string
      questionNumber:
        example: 1
        type: number
      questionTranslations:
        items:
          properties:
            correctData:
              type: string
            correctDataValidation:
              type: string
            language:
              description: Language code
              enum:
                - de
                - en-GB
                - en-US
                - es
                - fr
                - it
                - ja
                - ko
                - pt
                - ru
                - th
                - zh-Hans
                - zh-Hant
              example: en-US
              type: string
            questionDetail:
              type: string
            questionExtra:
              $ref: "#/definitions/QuestionExtra"
            questionTitle:
              minLength: 1
              type: string
          required:
            - language
            - questionTitle
            - questionExtra
          type: object
        type: array
      questionType:
        description: Type of the question
        enum:
          - SINGLE-CHOICE
          - MULTI-CHOICE
          - TEXT
          - TEXT-LINES
          - NUMBER
          - IMAGE
        type: string
    required:
      - questionNumber
      - questionType
      - questionTranslations
      - isRequired
    type: object
  CreateRank:
    properties:
      isPassed:
        example: true
        type: boolean
      lowerLimitPoints:
        example: 0
        type: integer
      rank:
        example: 1
        type: integer
      rankHeaderAnimationUrl:
        example: https://cdn.rive.app/animations/vehicles.riv
        format: uri
        maxLength: 512
        type: string
      rankTranslations:
        items:
          properties:
            language:
              description: Language code
              enum:
                - de
                - en-GB
                - en-US
                - es
                - fr
                - it
                - ja
                - ko
                - pt
                - ru
                - th
                - zh-Hans
                - zh-Hant
              example: en-US
              type: string
            rankName:
              minLength: 1
              type: string
          required:
            - language
            - rankName
          type: object
        type: array
      upperLimitPoints:
        example: 10
        type: integer
    required:
      - rankHeaderAnimationUrl
      - rank
      - lowerLimitPoints
      - upperLimitPoints
      - isPassed
      - rankTranslations
    type: object
  CreateServiceReq:
    properties:
      isMarketEnabled:
        type: boolean
      lineChannelId:
        pattern: ^[0-9]{10}$
        type: string
      marketCoverImageUrl:
        format: uri
        maxLength: 128
        type: string
      modularContractId:
        type: string
      serviceLogoImageUrl:
        format: uri
        maxLength: 128
        type: string
      serviceTranslations:
        items:
          properties:
            language:
              description: Language code
              enum:
                - de
                - en-GB
                - en-US
                - es
                - fr
                - it
                - ja
                - ko
                - pt
                - ru
                - th
                - zh-Hans
                - zh-Hant
              example: en-US
              type: string
            serviceName:
              maxLength: 512
              minLength: 1
              type: string
            servicePane:
              type: string
            servicePolicy:
              minLength: 1
              type: string
          required:
            - language
            - serviceName
            - servicePolicy
          type: object
        type: array
      serviceUrl:
        format: uri
        maxLength: 512
        type: string
      stripeAccountId:
        type: string
      themePrimaryColorHigher:
        pattern: ^0x[A-Fa-f0-9]{8}$
        type: string
      themePrimaryColorHighest:
        pattern: ^0x[A-Fa-f0-9]{8}$
        type: string
      themePrimaryColorLower:
        pattern: ^0x[A-Fa-f0-9]{8}$
        type: string
      themePrimaryColorLowest:
        pattern: ^0x[A-Fa-f0-9]{8}$
        type: string
    required:
      - serviceTranslations
      - serviceUrl
      - serviceLogoImageUrl
      - isMarketEnabled
      - lineChannelId
      - modularContractId
    type: object
  CreateServiceRes:
    properties:
      isMarketEnabled:
        type: boolean
      logoImageUrl:
        format: uri
        maxLength: 128
        type: string
      marketCoverImageUrl:
        format: uri
        maxLength: 128
        type: string
      serviceId:
        format: uuid
        type: string
      serviceTranslations:
        items:
          properties:
            language:
              description: Language code
              enum:
                - de
                - en-GB
                - en-US
                - es
                - fr
                - it
                - ja
                - ko
                - pt
                - ru
                - th
                - zh-Hans
                - zh-Hant
              example: en-US
              type: string
            serviceName:
              maxLength: 512
              minLength: 1
              type: string
            servicePane:
              type: string
            servicePolicy:
              minLength: 1
              type: string
          required:
            - language
            - serviceName
            - servicePolicy
          type: object
        type: array
      themePrimaryColorHigher:
        pattern: ^0x[A-Fa-f0-9]{8}$
        type: string
      themePrimaryColorHighest:
        pattern: ^0x[A-Fa-f0-9]{8}$
        type: string
      themePrimaryColorLower:
        pattern: ^0x[A-Fa-f0-9]{8}$
        type: string
      themePrimaryColorLowest:
        pattern: ^0x[A-Fa-f0-9]{8}$
        type: string
    required:
      - serviceId
      - serviceTranslations
      - logoImageUrl
      - isMarketEnabled
    type: object
  CreateTheme:
    properties:
      questions:
        items:
          $ref: "#/definitions/CreateQuestion"
        type: array
      themeCoverImageUrl:
        example: https://example.com/theme-cover.png
        format: uri
        maxLength: 512
        type: string
      themeNumber:
        example: 1
        type: number
      themeThumbnailImageUrl:
        example: https://example.com/theme-thumbnail.png
        format: uri
        maxLength: 512
        type: string
      themeTimeLimitSeconds:
        example: 600
        type: number
      themeTranslations:
        items:
          properties:
            language:
              description: Language code
              enum:
                - de
                - en-GB
                - en-US
                - es
                - fr
                - it
                - ja
                - ko
                - pt
                - ru
                - th
                - zh-Hans
                - zh-Hant
              example: en-US
              type: string
            themeDescription:
              minLength: 1
              type: string
            themeTitle:
              minLength: 1
              type: string
          required:
            - language
          type: object
        type: array
    required:
      - themeNumber
      - questions
    type: object
  CreateUser:
    properties:
      countryCode:
        description: Country code according to `ISO 3166-1 alpha-2`
        example: JP
        pattern: ^[A-Z]{2}$
        type: string
      mnemonicBackupKey:
        description: Hex encoded AES GSM 256 common key
        example: "0x51599a5cd6ac766ff116c8e56dc8d8f006a3f8f11f3230a6ae784a2c491dffa1"
        maxLength: 512
        pattern: ^[a-fA-F0-9]{64}$
        type: string
      phoneNumber:
        description: Global phone number format according to `E.164`
        example: "+************"
        pattern: ^\+?[1-9]\d{1,14}$
        type: string
      userId:
        description: Uniquely given identifier for each user
        example: 19bcd4c4-892a-4ecc-9832-90bd2b0a7ed0
        minLength: 1
        pattern: ^[a-zA-Z0-9_-]{1,128}$
        type: string
    required:
      - userId
      - countryCode
      - phoneNumber
      - mnemonicBackupKey
    type: object
  CreatedUser:
    properties:
      account:
        properties:
          accountId:
            description: None if no account for the service has been issued
            example: 19bcd4c4-892a-4ecc-9832-90bd2b0a7ed0
            pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
            type: string
          serviceId:
            description: Uniquely given identifier for each service
            example: 19bcd4c4-892a-4ecc-9832-90bd2b0a7ed0
            pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
            type: string
        required:
          - serviceId
        type: object
      contractAccountAddress:
        description: |-
          * Address format with EVM checksum according to `EIP-55`
          * Perform checksum verification
        example: "******************************************"
        pattern: ^0x[a-fA-F0-9]{40}$
        type: string
      phone:
        $ref: "#/definitions/Phone"
      userId:
        description: Uniquely given identifier for each user
        example: 19bcd4c4-892a-4ecc-9832-90bd2b0a7ed0
        minLength: 1
        pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
        type: string
    required:
      - userId
      - phone
      - account
    type: object
  CustomToken:
    properties:
      token:
        description: JWT for custom authentication
        example: eyJhbGciOiJSUzI1NiIsImtpZCI6IjFlZGFlNDI1ZDdjZGFhNzc4YjRhZTA3YTMzNjA4MTBhNGVmOGUyYzMiLCJ0eXAiOiJKV1QifQ.***************************************************************************************************************************************************************************************************************************************************************.Wp6XjMnF8BcihtbO2Gmh-YLM8W1Pyo5qRvGtjbKp1g88VhTKzYB7ysCQfPb-GRQHg0RaJjfITtBcMiDbf_zvLwC02DTrdzNLltzM5pFY1aOE1JXZjHbM4TxRDqSVNf7ZavA0noKlmzFT1Hfg8hQmtSPhGxyFgn30pU6xsbWpy8
        pattern: ^[A-Za-z0-9\-_]+\.[A-Za-z0-9\-_]+\.[A-Za-z0-9\-_]+$
        type: string
    type: object
  GeneralMetadata:
    properties:
      animation_url:
        description: URI for multimedia content such as video or audio.
        example: https://example.com/video.mp4
        type: string
      attributes:
        description: Traits or attributes of the NFT.
        items:
          properties:
            trait_type:
              description: Type of the attribute.
              example: Color
              type: string
            value:
              description: Value of the attribute.
              example: Blue
              type: string
          required:
            - trait_type
            - value
          type: object
        type: array
      background_color:
        description: Background color of the item without the `#` prefix.
        example: "000000"
        type: string
      description:
        description: Description of the NFT.
        example: This is a cool NFT that represents ownership of unique digital art.
        type: string
      external_url:
        description: External link to the item.
        example: https://example.com
        type: string
      image:
        description: URI to the image associated with the NFT.
        example: https://example.com/image.png
        type: string
      name:
        description: Name of the NFT.
        example: Cool NFT
        type: string
      youtube_url:
        description: YouTube video link associated with the NFT.
        example: https://youtube.com/watch?v=dQw4w9WgXcQ
        type: string
    required:
      - name
      - description
      - image
    type: object
  GeneratedCode:
    properties:
      code:
        description: Serial code
        example: X11018Z
        maxLength: 36
        minLength: 4
        type: string
      codeHash:
        description: Serial code to sha256 Hash
        example: "0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef"
        pattern: ^0x[a-fA-F0-9]{64}$
        type: string
      maxUseNum:
        description: Serial code use num
        example: 100
        minimum: 1
        type: number
      serialCodeId:
        description: Serial code ID
        example: 8552df83-cbd5-44db-b67e-0cbeb2785918
        pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
        type: string
    required:
      - serialCodeId
      - code
      - codeHash
      - maxUseNum
    type: object
  GetI18nNFTMetadataResponse:
    properties:
      description:
        description: The translated description of the NFT
        example: >-
          **Eligible Restaurant**

          Yakitori Yuka


          **Offer Details**

          Complimentary 10,000 yen course for one person


          **Terms of Use**

          - Valid for one-time use by the recipient only.

          - No change will be given, please understand.

          - To use this offer, please call to reserve your visit date and inform
          them of the offer usage.

          - Reservations for your desired date and time are not guaranteed.

          - Cannot be combined with other services.

          - Drink charges will be incurred separately.

          - Can be used by two or more people, but the course fee for
          accompanying guests will be charged separately.


          **How to Use**

          At checkout, show the screen to the staff and press the "Use Coupon"
          button.
        type: string
      name:
        description: The translated name of the NFT
        example: Cool NFT
        type: string
    required:
      - name
      - description
    type: object
  GrantedNftContractSchema:
    properties:
      roleGrantFailedList:
        description: List of NFT contracts where the role grant failed
        example:
          - nftContractAddress: "******************************************"
            nftContractId: cee3e4aa-b0bf-482d-bb70-57a979c032c5
        items:
          $ref: "#/definitions/ContractItemSchema"
        type: array
      roleGrantedList:
        description: List of NFT contracts where the role was successfully granted
        example:
          - nftContractAddress: "******************************************"
            nftContractId: cee3e4aa-b0bf-482d-bb70-57a979c032c5
        items:
          $ref: "#/definitions/ContractItemSchema"
        type: array
    required:
      - roleGrantedList
      - roleGrantFailedList
    type: object
  ImageQuestionExtra:
    properties:
      type:
        enum:
          - IMAGE
        type: string
      validations:
        description: questionType is IMAGE
        items:
          $ref: "#/definitions/QuestionValidation"
        type: array
    required:
      - type
      - validations
    type: object
  LineItem:
    properties:
      priceId:
        description: priceId returned by Stripe
        example: price_1MoBy5LkdIwHu7ixZhnattbh
        type: string
      quantity:
        description: remaining quantity for this product
        example: 10
        format: integer
        type: integer
    required:
      - priceId
      - quantity
    type: object
  MintTransactionInfo:
    properties:
      nonce:
        description: nonce
        example: 590
        type: number
      queueIds:
        description: List of queue IDs associated with the mint transaction
        example:
          - 8552df83-cbd5-44db-b67e-0cbeb2785918
        items:
          pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
          type: string
        type: array
      transactionId:
        description: Id of uniquely paid out transaction
        example: 8552df83-cbd5-44db-b67e-0cbeb2785918
        pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
        type: string
      txHash:
        description: transaction hash
        example: "******************************************"
        pattern: ^0x[a-fA-F0-9]{64}$
        type: string
    required:
      - transactionId
      - txHash
      - nonce
      - queueIds
    type: object
  MultiChoiceQuestionExtra:
    properties:
      maximumSelectable:
        description: Maximum number of selections allowed
        example: 3
        minimum: 1
        type: number
      minimumSelectable:
        description: Minimum number of selections required
        example: 1
        minimum: 1
        type: number
      selections:
        description: questionType is MULTI-CHOICE
        items:
          $ref: "#/definitions/MultiChoiceSelection"
        type: array
      type:
        enum:
          - MULTI-CHOICE
        type: string
    required:
      - type
      - selections
    type: object
  MultiChoiceSelection:
    properties:
      order:
        minimum: 0
        type: integer
      selectionText:
        minLength: 1
        type: string
    required:
      - order
      - selectionText
    type: object
  NftDeploy:
    properties:
      contractAddress:
        description: registered NFT Contract Address
        example: "******************************************"
        pattern: ^0x[a-fA-F0-9]{40}$
        type: string
      nftContractId:
        description: registered NFT Contract Id
        example: 8552df83-cbd5-44db-b67e-0cbeb2785918
        pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
        type: string
    required:
      - nftContractId
      - contractAddress
    type: object
  NftMetadata:
    properties:
      amount:
        description: |-
          - Number of this NFT owned
            - Always 1 for ERC721
            - 1 or more for ERC1155
        example: 1
        type: number
      contractAddress:
        description: ERC721/ERC1155 contract address of the coupon
        example: "******************************************"
        pattern: ^0x[a-fA-F0-9]{40}$
        type: string
      description:
        description: Markdown text encoded in base64
        example: IyBBcHBsaWVzIHRvIGFsbCBwdXJjaGFzZXMgb3ZlciAkNTAu
        format: byte
        type: string
      imageUrl:
        description: URL of the coupon image
        example: https://example.com/images/coupon.png
        format: uri
        pattern: ^(ipfs:\/\/[A-Za-z0-9]+|ar:\/\/[A-Za-z0-9_-]+|https:\/\/[A-Za-z0-9.-]+(?:\/[A-Za-z0-9._~:/?#[\]@!$&'()*+,;=%-]*)?)$
        type: string
      nftContractId:
        description: The uniquely given id of the nft contract
        example: cee3e4aa-b0bf-482d-bb70-57a979c032c5
        pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
        type: string
      rewardId:
        description: The uniquely given id of the reward
        example: 8552df83-cbd5-44db-b67e-0cbeb2785918
        pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
        type: string
      title:
        description: The name of the NFT
        example: 10% Off Discount Coupon
        pattern: ^.{4,128}$
        type: string
      tokenId:
        description: NFT tokenId
        example: 32
        type: number
    required:
      - nftContractId
      - contractAddress
      - tokenId
      - amount
      - title
      - description
      - imageUrl
    type: object
  NftMintRequest:
    properties:
      nftContractId:
        description: The uniquely given id of the nft contract
        example: cee3e4aa-b0bf-482d-bb70-57a979c032c5
        pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
        type: string
      nftTokenId:
        description: The uniquely given id of the token
        example: 32
        type: integer
        x-nullable: true
      nftType:
        description: Specify the type of nft to issue
        enum:
          - COUPON
          - CONTENT
          - CERTIFICATE
          - MEMBERSHIP
          - TICKET
          - MODULAR_CORE
          - MODULE_BULKMINT
          - MODULE_BULKCREATEACCOUNT
        example: CONTENT
        type: string
      serviceId:
        description: Id paid out uniquely for each tenant service
        example: 8552df83-cbd5-44db-b67e-0cbeb2785918
        pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
        type: string
      toAddress:
        description: WalletAddress of NFT's Mint destination
        example: "******************************************"
        pattern: ^0x[a-fA-F0-9]{40}$
        type: string
    required:
      - serviceId
      - nftType
      - nftContractId
      - toAddress
    type: object
  NftRegister:
    properties:
      deliveryImageUrl:
        description: URI to the delivery image of the NFT
        example: ""
        format: uri
        maxLength: 512
        type: string
      metadata:
        description: URI to the metadata of the NFT
        example: ""
        minLength: 1
        type: string
      nftCollectionName:
        description: Name of the NFT collection
        example: Marbull NFT collection
        minLength: 1
        type: string
      nftContractTypeId:
        description: The uniquely given id of the NFT format
        example: 8552df83-cbd5-44db-b67e-0cbeb2785918
        pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
        type: string
      nftName:
        description: Name to be specified when issuing NFT
        example: marbull NFT
        minLength: 1
        type: string
      nftSymbol:
        description: Symbol to be specified when issuing NFT
        example: MBL1
        minLength: 1
        pattern: ^[A-Z0-9]{3,5}$
        type: string
    required:
      - nftContractTypeId
      - nftName
      - nftSymbol
      - nftCollectionName
      - metadata
    type: object
  Notification:
    properties:
      language:
        description: language code
        example: ja
        pattern: ^[a-z]{2}(?:-[A-Z]{2})?$
        type: string
      text:
        description: detail
        example: message
        pattern: ^.+$
        type: string
      title:
        description: title
        example: message
        pattern: ^.+$
        type: string
    required:
      - language
      - title
      - text
    type: object
  NotificationItem:
    properties:
      broadcastDate:
        description: Time when notifications are broadcasted
        example: 2024-01-01T10:00:00Z
        pattern: ^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])T([01]\d|2[0-3]):([0-5]\d):([0-5]\d).(\d\d\d)(Z|([+-](0[0-9]|1[0-3]):([0-5]\d)))?$
        type: string
      notificationId:
        description: The uniquely given id of the notification
        example: 8552df83-cbd5-44db-b67e-0cbeb2785918
        pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
        type: string
      text:
        description: Markdown text encoded in base64
        example: 44GK5a6i5qeY5ZCE5L2NCgrml6XpoIPjgojjgorjgZTliKnnlKjjgYTjgZ/jgaDjgY3jgIHoqqDjgavjgYLjgorjgYzjgajjgYbjgZTjgZbjgYTjgb7jgZnjgILkuIvoqJjjga7ml6XnqIvjgafjgrfjgrnjg4bjg6Djg6Hjg7Pjg4bjg4rjg7PjgrnjgpLlrp/mlr3jgYTjgZ/jgZfjgb7jgZnjgIIKCiMjIOODoeODs+ODhuODiuODs+OCueaXpeaZggotICoq5pel5pmCKio6IDIwMjTlubQxMOaciDIw5pel77yI5pel77yJ5Y2I5YmNMTowMCDjgJwg5Y2I5YmNNTowMAoKIyMg5b2x6Z+/56+E5ZuyCuODoeODs+ODhuODiuODs+OCueacn+mWk+S4reOBr+OAgeS7peS4i+OBruOCteODvOODk+OCueOBjOS4gOaZgueahOOBq+OBlOWIqeeUqOOBhOOBn+OBoOOBkeOBvuOBm+OCk+OAggotIFvjgrXjg7zjg5PjgrnlkI0xXQotIFvjgrXjg7zjg5PjgrnlkI0yXQoKIyMg44GU5rOo5oSPCuODoeODs+ODhuODiuODs+OCuee1guS6huW+jOOAgeOCteODvOODk+OCueOBr+iHquWLleeahOOBq+WGjemWi+OBleOCjOOBvuOBmeOAguOBiuaJi+aVsOOCkuOBiuOBi+OBkeOBl+OBvuOBmeOBjOOAgeOBlOeQhuino+OBqOOBlOWNlOWKm+OCkuOBiumhmOOBhOOBhOOBn+OBl+OBvuOBmeOAggoK5LuK5b6M44Go44KC44CB44Gp44GG44Ge44KI44KN44GX44GP44GK6aGY44GE55Sz44GX5LiK44GS44G+44GZ44CCCgotLS0KCuOBiuWVj+OBhOWQiOOCj+OBmzogc3VwcG9ydEBleGFtcGxlLmNvbQ==
        type: string
      title:
        description: Title defined per Notification
        example: システムメンテナンスのお知らせ
        type: string
    required:
      - notificationId
      - title
      - text
      - broadcastDate
    type: object
  NotificationList:
    properties:
      notifications:
        description: Global and Account notifications list
        example: []
        items:
          $ref: "#/definitions/NotificationItem"
        type: array
    required:
      - notifications
    type: object
  NumberQuestionExtra:
    properties:
      type:
        enum:
          - NUMBER
        type: string
      validations:
        description: questionType is NUMBER
        items:
          $ref: "#/definitions/QuestionValidation"
        type: array
    required:
      - type
      - validations
    type: object
  Phone:
    description: User phone information
    properties:
      countryCode:
        description: Country code according to `ISO 3166-1 alpha-2`
        example: JP
        pattern: ^[A-Z]{2}$
        type: string
      phoneNumber:
        description: Global phone number format according to `E.164`
        example: "+************"
        pattern: ^\+?[1-9]\d{1,14}$
        type: string
    required:
      - countryCode
      - phoneNumber
    type: object
  QuestActivityDetail:
    properties:
      claimedRewardIds:
        description: List of Id's of rewards already retrieved
        example: []
        items:
          description: Uniquely given identifier for each reward
          example: 5850b40d-333b-4fbe-8c87-8e34d0f47404
          pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
          type: string
        type: array
      clearedActionIds:
        description: List of cleared action ids
        example: []
        items:
          description: Uniquely given identifier for each action
          example: 8552df83-cbd5-44db-b67e-0cbeb2785918
          pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
          type: string
        type: array
      questStatus:
        enum:
          - NOT_STARTED
          - IN_PROGRESS
          - COMPLETED
        type: string
    type: object
  QuestActivityStatus:
    properties:
      questIds:
        description: List of questIds
        example: []
        items:
          description: Uniquely given identifier for each quest
          example: 8552df83-cbd5-44db-b67e-0cbeb2785918
          pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
          type: string
        type: array
    type: object
  QuestCreateRequest:
    properties:
      availableEndDate:
        description: Quest available end date
        example: 2024-01-01
        pattern: ^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])T([01]\d|2[0-3]):([0-5]\d):([0-5]\d)(?:\.(\d{3}))?Z$
        type: string
      availableStartDate:
        description: Quest available start date
        example: 2024-01-01
        pattern: ^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])T([01]\d|2[0-3]):([0-5]\d):([0-5]\d)(?:\.(\d{3}))?Z$
        type: string
      coverImageUrl:
        description: Quest image URL
        example: https://example.com/covers/xxx.png
        format: uri
        maxLength: 512
        pattern: ^(ipfs:\/\/[A-Za-z0-9]+|ar:\/\/[A-Za-z0-9_-]+|https:\/\/[A-Za-z0-9.-]+(?:\/[A-Za-z0-9._~:/?#[\]@!$&'()*+,;=%-]*)?)$
        type: string
      orderIndex:
        description: quest item order priority (default is desc and >= 0)
        example: 0
        exclusiveMinimum: true
        minimum: 0
        type: integer
      questTranslations:
        items:
          properties:
            description:
              description: Quest description
              example: Quest 1 description
              minLength: 1
              type: string
            language:
              description: Language code
              enum:
                - de
                - en-GB
                - en-US
                - es
                - fr
                - it
                - ja
                - ko
                - pt
                - ru
                - th
                - zh-Hans
                - zh-Hant
              example: en-US
              type: string
            title:
              description: Quest title
              example: Quest 1
              minLength: 1
              type: string
          required:
            - language
            - title
            - description
          type: object
        type: array
      questType:
        description: Types of quest that can be selected
        enum:
          - STATUS
          - CUSTOM
          - RETENTION
        example: CUSTOM
        type: string
      thumbnailImageUrl:
        description: Quest image URL
        example: https://example.com/icons/xxx.png
        format: uri
        maxLength: 512
        pattern: ^(ipfs:\/\/[A-Za-z0-9]+|ar:\/\/[A-Za-z0-9_-]+|https:\/\/[A-Za-z0-9.-]+(?:\/[A-Za-z0-9._~:/?#[\]@!$&'()*+,;=%-]*)?)$
        type: string
    required:
      - questTranslations
      - thumbnailImageUrl
      - coverImageUrl
      - orderIndex
      - availableStartDate
      - availableEndDate
      - questType
    type: object
  QuestCreateResponse:
    properties:
      availableEndDate:
        description: End date and time when the quest is disabled
        example: 2024-01-20T10:00:00Z
        pattern: ^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])T([01]\d|2[0-3]):([0-5]\d):([0-5]\d)(?:\.(\d{3}))?Z$
        type: string
      availableStartDate:
        description: Start date and time when the quest is activated
        example: 2024-01-01T10:00:00Z
        pattern: ^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])T([01]\d|2[0-3]):([0-5]\d):([0-5]\d)(?:\.(\d{3}))?Z$
        type: string
      coverImageUrl:
        description: URL of the Quest cover image
        example: https://marbullx.com/cover
        format: uri
        maxLength: 1024
        pattern: ^(ipfs:\/\/[A-Za-z0-9]+|ar:\/\/[A-Za-z0-9_-]+|https:\/\/[A-Za-z0-9.-]+(?:\/[A-Za-z0-9._~:/?#[\]@!$&'()*+,;=%-]*)?)$
        type: string
      orderIndex:
        description: quest item order priority (default is desc and >= 0)
        example: 0
        exclusiveMinimum: true
        minimum: 0
        type: integer
      questId:
        description: Uniquely given identifier for each quest
        example: 8552df83-cbd5-44db-b67e-0cbeb2785918
        pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
        type: string
      questTranslations:
        items:
          properties:
            description:
              description: Quest description
              example: Quest 1 description
              minLength: 1
              type: string
            language:
              description: Language code
              enum:
                - de
                - en-GB
                - en-US
                - es
                - fr
                - it
                - ja
                - ko
                - pt
                - ru
                - th
                - zh-Hans
                - zh-Hant
              example: en-US
              type: string
            title:
              description: Quest title
              example: Quest 1
              minLength: 1
              type: string
          required:
            - language
            - title
            - description
          type: object
        type: array
      questType:
        description: Types of quest that can be selected
        enum:
          - STATUS
          - CUSTOM
          - RETENTION
        example: CUSTOM
        type: string
      thumbnailImageUrl:
        description: URL of the Quest thumbnail image
        example: https://marbullx.com/thumbnail
        format: uri
        maxLength: 1024
        pattern: ^(ipfs:\/\/[A-Za-z0-9]+|ar:\/\/[A-Za-z0-9_-]+|https:\/\/[A-Za-z0-9.-]+(?:\/[A-Za-z0-9._~:/?#[\]@!$&'()*+,;=%-]*)?)$
        type: string
    required:
      - questId
      - questTranslations
      - coverImageUrl
      - thumbnailImageUrl
      - orderIndex
      - availableStartDate
      - availableEndDate
      - questType
    type: object
  QuestDetail:
    properties:
      actions:
        description: List of action information linked to Quest
        items:
          $ref: "#/definitions/Action"
        type: array
      coverImageUrl:
        description: URL of the Quest cover image
        example: https://marbullx.com/logo
        pattern: ^(ipfs:\/\/[A-Za-z0-9]+|ar:\/\/[A-Za-z0-9_-]+|https:\/\/[A-Za-z0-9.-]+(?:\/[A-Za-z0-9._~:/?#[\]@!$&'()*+,;=%-]*)?)$
        type: string
      description:
        description: Markdown text encoded in base64
        example: IyBIZWxsbyB3b3JsZCEKIyMgcG9saWN5IGRhdGEKVGhpcyBpcyB3aGVyZSB0aGUgdGV4dCBvZiB0aGUgcG9saWN5IGRhdGEgd2lsbCBiZSBsaXN0ZWQuCgotIGhpbnQgMQotIGhpbnQgMgoKW2ltYWdlXShodHRwczovL2ltYWdlLmNvbSkKCg==
        format: byte
        type: string
      expiredAt:
        description: End date and time when the quest is disabled
        example: 2024-01-20T10:00:00Z
        pattern: ^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])T([01]\d|2[0-3]):([0-5]\d):([0-5]\d).(\d\d\d)(Z|([+-](0[0-9]|1[0-3]):([0-5]\d)))?$
        type: string
      questId:
        description: Uniquely given identifier for each quest
        example: 8552df83-cbd5-44db-b67e-0cbeb2785918
        pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
        type: string
      questType:
        description: Types of quest that can be selected
        enum:
          - STATUS
          - CUSTOM
          - RETENTION
        example: CUSTOM
        type: string
      rewards:
        description: List of reward information linked to Quest
        items:
          $ref: "#/definitions/Reward"
        type: array
      startedAt:
        description: Start date and time when the quest is activated
        example: 2024-01-01T10:00:00Z
        pattern: ^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])T([01]\d|2[0-3]):([0-5]\d):([0-5]\d).(\d\d\d)(Z|([+-](0[0-9]|1[0-3]):([0-5]\d)))?$
        type: string
      title:
        description: The name of the quest
        example: Quest title
        pattern: ^.{4,128}$
        type: string
    required:
      - questId
      - title
      - description
      - coverImageUrl
      - startedAt
      - expiredAt
      - questType
      - rewards
      - actions
    type: object
  QuestItem:
    properties:
      expiredAt:
        description: End date and time when the quest is disabled
        example: 2024-01-20T10:00:00Z
        pattern: ^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])T([01]\d|2[0-3]):([0-5]\d):([0-5]\d)(?:\.(\d{3}))?Z$
        type: string
      mainRewardThumbnailImageUrl:
        description: URL of the Main reward thumbnail image
        example: https://marbullx.com/image/reward
        pattern: ^(ipfs:\/\/[A-Za-z0-9]+|ar:\/\/[A-Za-z0-9_-]+|https:\/\/[A-Za-z0-9.-]+(?:\/[A-Za-z0-9._~:/?#[\]@!$&'()*+,;=%-]*)?)$
        type: string
      mainRewardTitle:
        description: The name of the main Reward
        example: Reward name aaa
        pattern: ^.{4,128}$
        type: string
      orderIndex:
        description: quest item order priority (default is desc and >= 0)
        example: 0
        exclusiveMinimum: true
        minimum: 0
        type: integer
      questId:
        description: Uniquely given identifier for each quest
        example: 8552df83-cbd5-44db-b67e-0cbeb2785918
        pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
        type: string
      questType:
        description: Types of quest that can be selected
        enum:
          - STATUS
          - CUSTOM
          - RETENTION
        example: CUSTOM
        type: string
      startedAt:
        description: Start date and time when the quest is activated
        example: 2024-01-01T10:00:00Z
        pattern: ^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])T([01]\d|2[0-3]):([0-5]\d):([0-5]\d)(?:\.(\d{3}))?Z$
        type: string
      thumbnailImageUrl:
        description: URL of the Quest thumbnail image
        example: https://marbullx.com/image/quest
        pattern: ^(ipfs:\/\/[A-Za-z0-9]+|ar:\/\/[A-Za-z0-9_-]+|https:\/\/[A-Za-z0-9.-]+(?:\/[A-Za-z0-9._~:/?#[\]@!$&'()*+,;=%-]*)?)$
        type: string
      title:
        description: The name of the service
        example: Quest title
        pattern: ^.{4,128}$
        type: string
    required:
      - questId
      - title
      - questType
      - mainRewardTitle
      - mainRewardThumbnailImageUrl
      - thumbnailImageUrl
      - orderIndex
      - startedAt
      - expiredAt
    type: object
  QuestRewardCreationRequest:
    properties:
      acquirementType:
        enum:
          - DISTRIBUTION
          - LOTTERY
          - GACHA
          - QUIZ
        type: string
      certificateReward:
        properties:
          certificateType:
            enum:
              - STANDARD
              - STATUS
            type: string
          statusCertificateRank:
            minimum: 0
            type: integer
            x-nullable: true
        required:
          - certificateType
        type: object
      coverImageUrl:
        format: uri
        maxLength: 512
        type: string
      deliveryImageUrl:
        format: uri
        maxLength: 512
        type: string
      nftCollectionName:
        minLength: 1
        type: string
      nftContractTypeId:
        format: uuid
        type: string
      nftMetadata:
        type: string
      nftName:
        minLength: 1
        type: string
      nftSymbol:
        minLength: 1
        type: string
      orderIndex:
        exclusiveMinimum: true
        minimum: 0
        type: integer
      questRewardPriorityType:
        enum:
          - MAIN
          - SUB
        type: string
      rewardTranslations:
        items:
          properties:
            description:
              description: Description of the reward
              minLength: 1
              type: string
            language:
              description: Language code
              enum:
                - de
                - en-GB
                - en-US
                - es
                - fr
                - it
                - ja
                - ko
                - pt
                - ru
                - th
                - zh-Hans
                - zh-Hant
              example: en-US
              type: string
            title:
              description: Title of the reward
              maxLength: 128
              minLength: 1
              type: string
          required:
            - language
            - title
            - description
          type: object
        type: array
      rewardType:
        enum:
          - COUPON
          - CONTENT
          - CERTIFICATE
        type: string
      selector:
        properties:
          gachaWeight:
            exclusiveMinimum: true
            minimum: 0
            type: number
            x-nullable: true
          rankId:
            format: uuid
            type: string
            x-nullable: true
        type: object
      thumbnailImageUrl:
        format: uri
        maxLength: 512
        type: string
    required:
      - nftContractTypeId
      - thumbnailImageUrl
      - coverImageUrl
      - rewardTranslations
      - acquirementType
      - orderIndex
      - rewardType
      - questRewardPriorityType
      - deliveryImageUrl
      - nftName
      - nftSymbol
      - nftCollectionName
      - nftMetadata
    type: object
  QuestRewardCreationResponse:
    properties:
      acquirementType:
        enum:
          - DISTRIBUTION
          - LOTTERY
          - GACHA
          - QUIZ
        type: string
      coverImageUrl:
        format: uri
        maxLength: 1024
        type: string
      nftContractId:
        format: uuid
        type: string
      questRewardPriorityType:
        enum:
          - MAIN
          - SUB
        type: string
      rewardId:
        format: uuid
        type: string
      rewardTranslations:
        items:
          properties:
            description:
              description: Description of the reward
              minLength: 1
              type: string
            language:
              description: Language code
              enum:
                - de
                - en-GB
                - en-US
                - es
                - fr
                - it
                - ja
                - ko
                - pt
                - ru
                - th
                - zh-Hans
                - zh-Hant
              example: en-US
              type: string
            title:
              description: Title of the reward
              maxLength: 128
              minLength: 1
              type: string
          required:
            - language
            - title
            - description
          type: object
        type: array
      rewardType:
        enum:
          - COUPON
          - CONTENT
          - CERTIFICATE
        type: string
      serviceId:
        format: uuid
        type: string
      thumbnailImageUrl:
        format: uri
        maxLength: 1024
        type: string
    required:
      - rewardId
      - serviceId
      - nftContractId
      - coverImageUrl
      - thumbnailImageUrl
      - rewardTranslations
      - acquirementType
      - rewardType
      - questRewardPriorityType
    type: object
  QuestionExtra: {}
  QuestionValidation:
    properties:
      errorMessage:
        minLength: 1
        type: string
      validationOrder:
        minimum: 0
        type: integer
      validationRegex:
        minLength: 2
        type: string
    required:
      - validationOrder
      - validationRegex
      - errorMessage
    type: object
  QuestionnaireCreateRequest:
    properties:
      questionnaireType:
        enum:
          - QUIZ
          - SURVEY
          - MESSAGE
        example: QUIZ
        type: string
      ranks:
        items:
          $ref: "#/definitions/CreateRank"
        type: array
      themes:
        items:
          $ref: "#/definitions/CreateTheme"
        type: array
    required:
      - questionnaireType
      - themes
    type: object
  QuestionnaireDetailResponse:
    properties:
      questionnaireId:
        description: Uniquely given identifier for each questionnaire
        example: cb9555c4-07e5-4137-a22d-44291faeb2c6
        pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
        type: string
      questionnaireType:
        enum:
          - QUIZ
          - SURVEY
          - MESSAGE
        example: QUIZ
        type: string
      questions:
        items:
          $ref: "#/definitions/ResponseQuestionUpdate"
        minItems: 1
        type: array
      ranks:
        $ref: "#/definitions/ResponseRanks"
      themes:
        items:
          $ref: "#/definitions/ResponseThemeUpdate"
        minItems: 1
        type: array
    required:
      - questionnaireId
      - questionnaireType
      - themes
      - questions
    type: object
  QuestionnaireQuestion:
    properties:
      correctAnswer:
        description: The correct answer to the question
        example: Correct answer
        type: string
      isCorrect:
        description: Whether the user's answer was correct
        example: true
        type: boolean
      isRequired:
        description: Whether the question is required to be answered
        example: true
        type: boolean
      obtainedAnswerPoint:
        description: Points obtained for the answer
        example: 10
        type: number
      postedAnswer:
        description: The answer posted by the user
        example: Your answer
        type: string
        x-nullable: true
      questionId:
        description: Unique identifier for the question
        example: 7a7b4284-84f2-4dec-994f-148f89d54957
        pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
        type: string
    required:
      - questionId
      - isRequired
    type: object
  QuestionnaireReadResponse:
    properties:
      questionnaireId:
        description: Uniquely given identifier for each questionnaire
        example: cb9555c4-07e5-4137-a22d-44291faeb2c6
        pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
        type: string
      questionnaireType:
        enum:
          - QUIZ
          - SURVEY
          - MESSAGE
        example: QUIZ
        type: string
      questions:
        items:
          $ref: "#/definitions/ResponseQuestion"
        minItems: 1
        type: array
      themes:
        items:
          $ref: "#/definitions/ResponseTheme"
        minItems: 1
        type: array
    required:
      - questionnaireId
      - questionnaireType
      - themes
      - questions
    type: object
  QuestionnaireTheme:
    properties:
      correctCount:
        description: Number of correct answers in the theme
        example: 7
        type: number
      failedCount:
        description: Number of failed answers in the theme
        example: 1
        type: number
      questions:
        description: List of questions within the theme
        example: []
        items:
          $ref: "#/definitions/QuestionnaireQuestion"
        type: array
      themeId:
        description: Unique identifier for the theme
        example: 7a7b4284-84f2-4dec-994f-148f89d54957
        pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
        type: string
    required:
      - themeId
      - questions
    type: object
  QuestionnaireUpdateRequest:
    properties:
      questionnaireType:
        enum:
          - QUIZ
          - SURVEY
          - MESSAGE
        example: QUIZ
        type: string
      ranks:
        items:
          $ref: "#/definitions/UpdateRank"
        type: array
      themes:
        items:
          $ref: "#/definitions/UpdateTheme"
        minItems: 1
        type: array
    required:
      - questionnaireType
      - themes
    type: object
  RegsteredModularContractSchema:
    properties:
      modularContractAddress:
        description: The address of the modular contract
        example: "******************************************"
        pattern: ^0x[a-fA-F0-9]{40}$
        type: string
      serviceId:
        description: The uniquely given id of the service
        example: 8552df83-cbd5-44db-b67e-0cbeb2785918
        pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
        type: string
    required:
      - serviceId
      - modularContractAddress
    type: object
  RequestAccount:
    properties:
      accountId:
        description: The uniquely given id of the account
        example: cee3e4aa-b0bf-482d-bb70-57a979c032c5
        pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
        type: string
      rewardType:
        description: The type of the reward
        enum:
          - COUPON
          - CONTENT
          - CERTIFICATE
        example: COUPON
        type: string
    required:
      - accountId
      - rewardType
    type: object
  ResponseQuestion:
    properties:
      isRequired:
        description: Indicates if the question is required
        example: true
        type: boolean
      questionDetail:
        description: Detailed description of the question
        example: ""
        type: string
      questionExtra:
        $ref: "#/definitions/QuestionExtra"
      questionId:
        description: Unique identifier for the question
        example: 7a7b4284-84f2-4dec-994f-148f89d54957
        pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$
        type: string
      questionImageUrl:
        description: URL for the question image
        example: ""
        format: uri
        pattern: ^https://.*$
        type: string
      questionNumber:
        description: The number of the question
        example: 0
        type: integer
      questionTitle:
        description: The title of the question
        example: ""
        type: string
      questionType:
        description: Type of the question
        enum:
          - SINGLE-CHOICE
          - MULTI-CHOICE
          - TEXT
          - TEXT-LINES
          - NUMBER
          - IMAGE
        type: string
      themeId:
        description: Unique identifier for the theme
        example: 9a0f4999-cb69-48ef-b564-ea5df315ca80
        pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$
        type: string
    required:
      - themeId
      - questionId
      - questionNumber
      - questionTitle
      - questionType
      - isRequired
    type: object
  ResponseQuestionUpdate:
    properties:
      answerPoint:
        example: 10
        type: integer
      isRequired:
        description: Indicates if the question is required
        example: true
        type: boolean
      questionId:
        description: Unique identifier for the question
        example: 7a7b4284-84f2-4dec-994f-148f89d54957
        pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$
        type: string
      questionImageUrl:
        description: URL for the question image
        example: ""
        format: uri
        pattern: ^https://.*$
        type: string
      questionNumber:
        description: The number of the question
        example: 0
        type: integer
      questionTranslations:
        items:
          properties:
            correctData:
              type: string
            correctDataValidation:
              type: string
            language:
              description: Language code
              enum:
                - de
                - en-GB
                - en-US
                - es
                - fr
                - it
                - ja
                - ko
                - pt
                - ru
                - th
                - zh-Hans
                - zh-Hant
              example: en-US
              type: string
            questionDetail:
              type: string
            questionExtra:
              $ref: "#/definitions/QuestionExtra"
            questionTitle:
              minLength: 1
              type: string
          required:
            - language
            - questionTitle
            - questionExtra
          type: object
        type: array
      questionType:
        description: Type of the question
        enum:
          - SINGLE-CHOICE
          - MULTI-CHOICE
          - TEXT
          - TEXT-LINES
          - NUMBER
          - IMAGE
        type: string
    required:
      - questionId
      - questionNumber
      - questionType
      - questionTranslations
      - isRequired
    type: object
  ResponseRankDetail:
    properties:
      isPassed:
        example: true
        type: boolean
      lowerLimitPoints:
        example: 0
        type: integer
      rank:
        example: 1
        type: integer
      rankHeaderAnimationUrl:
        example: https://cdn.rive.app/animations/vehicles.riv
        format: uri
        maxLength: 512
        type: string
      rankId:
        example: 4ae4cc33-5e08-4903-a6e6-7af79479c054
        pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$
        type: string
      rankTranslations:
        items:
          properties:
            language:
              description: Language code
              enum:
                - de
                - en-GB
                - en-US
                - es
                - fr
                - it
                - ja
                - ko
                - pt
                - ru
                - th
                - zh-Hans
                - zh-Hant
              example: en-US
              type: string
            rankName:
              minLength: 1
              type: string
          required:
            - language
            - rankName
          type: object
        minItems: 1
        type: array
      upperLimitPoints:
        example: 10
        type: integer
    required:
      - rankId
      - rankHeaderAnimationUrl
      - rank
      - lowerLimitPoints
      - upperLimitPoints
      - isPassed
      - rankTranslations
    type: object
  ResponseRanks:
    properties:
      maxRank:
        example: 5
        type: integer
      passingPoints:
        example: 10
        type: integer
      rankDetails:
        description: List of rank details
        items:
          $ref: "#/definitions/ResponseRankDetail"
        type: array
    required:
      - maxRank
      - passingPoints
      - rankDetails
    type: object
  ResponseTheme:
    properties:
      themeCoverImageUrl:
        description: URL for the theme cover image
        example: ""
        format: uri
        pattern: ^https://.*$
        type: string
      themeDescription:
        description: Description of the theme
        example: ""
        minLength: 1
        type: string
      themeId:
        description: Unique identifier for the theme
        example: 9a0f4999-cb69-48ef-b564-ea5df315ca80
        pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$
        type: string
      themeNumber:
        description: The number associated with the theme
        example: 0
        minimum: 1
        type: integer
      themeThumbnailImageUrl:
        description: URL for the theme thumbnail image
        example: ""
        format: uri
        pattern: ^https://.*$
        type: string
      themeTimeLimitSeconds:
        description: Time limit for the theme in seconds
        example: 100
        type: number
      themeTitle:
        description: The title of the theme
        example: ""
        minLength: 1
        type: string
    required:
      - themeId
      - themeNumber
    type: object
  ResponseThemeUpdate:
    properties:
      themeCoverImageUrl:
        example: https://example.com/theme-cover.png
        format: uri
        maxLength: 512
        type: string
      themeId:
        description: Unique identifier for the theme
        example: 9a0f4999-cb69-48ef-b564-ea5df315ca80
        pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$
        type: string
      themeNumber:
        description: The number associated with the theme
        example: 0
        minimum: 1
        type: integer
      themeThumbnailImageUrl:
        example: https://example.com/theme-thumbnail.png
        format: uri
        maxLength: 512
        type: string
      themeTimeLimitSeconds:
        description: Time limit for the theme in seconds
        example: 100
        type: integer
      themeTranslations:
        items:
          properties:
            language:
              description: Language code
              enum:
                - de
                - en-GB
                - en-US
                - es
                - fr
                - it
                - ja
                - ko
                - pt
                - ru
                - th
                - zh-Hans
                - zh-Hant
              example: en-US
              type: string
            themeDescription:
              minLength: 1
              type: string
            themeTitle:
              minLength: 1
              type: string
          required:
            - language
          type: object
        type: array
    required:
      - themeId
      - themeNumber
      - themeTranslations
    type: object
  RetryTransactionResponse:
    properties:
      fromAddress:
        description: source address
        example: "******************************************"
        pattern: ^0x[a-fA-F0-9]{40}$
        type: string
      nonce:
        description: nonce
        example: 590
        type: number
      toAddress:
        description: destination address
        example: "******************************************"
        pattern: ^0x[a-fA-F0-9]{40}$
        type: string
      transactionId:
        description: Id of uniquely paid out transaction
        example: 8552df83-cbd5-44db-b67e-0cbeb2785918
        pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
        type: string
      txHash:
        description: transaction hash
        example: "******************************************"
        pattern: ^0x[a-fA-F0-9]{64}$
        type: string
    type: object
  Reward:
    properties:
      orderIndex:
        description: quest item order priority (default is desc and >= 0)
        example: 0
        type: integer
        x-nullable: true
      rewardId:
        description: The uniquely given id of the reward
        example: 8552df83-cbd5-44db-b67e-0cbeb2785918
        pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
        type: string
      rewardPriorityType:
        description: Quest reward priority type
        enum:
          - MAIN
          - SUB
        example: MAIN
        type: string
      thumbnailImageUrl:
        description: URL of the Reward thumbnail image
        example: https://marbullx.com/logo
        pattern: ^(ipfs:\/\/[A-Za-z0-9]+|ar:\/\/[A-Za-z0-9_-]+|https:\/\/[A-Za-z0-9.-]+(?:\/[A-Za-z0-9._~:/?#[\]@!$&'()*+,;=%-]*)?)$
        type: string
      title:
        description: The name of the reward
        example: Test Reward
        pattern: ^.{4,128}$
        type: string
    required:
      - rewardId
      - title
      - thumbnailImageUrl
      - orderIndex
      - rewardPriorityType
    type: object
  RewardCreationRequest:
    properties:
      acquirementType:
        enum:
          - DISTRIBUTION
          - LOTTERY
          - GACHA
          - QUIZ
        type: string
      certificateReward:
        properties:
          certificateType:
            enum:
              - STANDARD
              - STATUS
            type: string
          statusCertificateRank:
            minimum: 0
            type: integer
            x-nullable: true
        required:
          - certificateType
        type: object
      coverImageUrl:
        format: uri
        maxLength: 512
        type: string
      deliveryImageUrl:
        format: uri
        maxLength: 512
        type: string
      nftCollectionName:
        minLength: 1
        type: string
      nftContractTypeId:
        format: uuid
        type: string
      nftMetadata:
        type: string
      nftName:
        minLength: 1
        type: string
      nftSymbol:
        minLength: 1
        type: string
      orderIndex:
        exclusiveMinimum: true
        minimum: 0
        type: integer
      rewardTranslations:
        items:
          properties:
            description:
              description: Description of the reward
              minLength: 1
              type: string
            language:
              description: Language code
              enum:
                - de
                - en-GB
                - en-US
                - es
                - fr
                - it
                - ja
                - ko
                - pt
                - ru
                - th
                - zh-Hans
                - zh-Hant
              example: en-US
              type: string
            title:
              description: Title of the reward
              maxLength: 128
              minLength: 1
              type: string
          required:
            - language
            - title
            - description
          type: object
        type: array
      rewardType:
        enum:
          - COUPON
          - CONTENT
          - CERTIFICATE
        type: string
      thumbnailImageUrl:
        format: uri
        maxLength: 512
        type: string
    required:
      - nftContractTypeId
      - thumbnailImageUrl
      - coverImageUrl
      - rewardTranslations
      - acquirementType
      - orderIndex
      - rewardType
      - deliveryImageUrl
      - nftName
      - nftSymbol
      - nftCollectionName
      - nftMetadata
    type: object
  RewardCreationResponse:
    properties:
      acquirementType:
        enum:
          - DISTRIBUTION
          - LOTTERY
          - GACHA
          - QUIZ
        type: string
      coverImageUrl:
        format: uri
        maxLength: 1024
        type: string
      nftContractId:
        format: uuid
        type: string
      rewardId:
        format: uuid
        type: string
      rewardTranslations:
        items:
          properties:
            description:
              description: Description of the reward
              minLength: 1
              type: string
            language:
              description: Language code
              enum:
                - de
                - en-GB
                - en-US
                - es
                - fr
                - it
                - ja
                - ko
                - pt
                - ru
                - th
                - zh-Hans
                - zh-Hant
              example: en-US
              type: string
            title:
              description: Title of the reward
              maxLength: 128
              minLength: 1
              type: string
          required:
            - language
            - title
            - description
          type: object
        type: array
      rewardType:
        enum:
          - COUPON
          - CONTENT
          - CERTIFICATE
        type: string
      serviceId:
        format: uuid
        type: string
      thumbnailImageUrl:
        format: uri
        maxLength: 1024
        type: string
    required:
      - rewardId
      - serviceId
      - nftContractId
      - coverImageUrl
      - thumbnailImageUrl
      - rewardTranslations
      - acquirementType
      - rewardType
    type: object
  RewardDetail:
    properties:
      acquirementType:
        enum:
          - DISTRIBUTION
          - LOTTERY
          - GACHA
          - QUIZ
        example: DISTRIBUTION
        type: string
      coverImageUrl:
        description: URL of the Reward cover image
        example: https://marbullx.com/reward
        pattern: ^(ipfs:\/\/[A-Za-z0-9]+|ar:\/\/[A-Za-z0-9_-]+|https:\/\/[A-Za-z0-9.-]+(?:\/[A-Za-z0-9._~:/?#[\]@!$&'()*+,;=%-]*)?)$
        type: string
      description:
        description: Markdown text encoded in base64
        example: IyBIZWxsbyB3b3JsZCEKIyMgcG9saWN5IGRhdGEKVGhpcyBpcyB3aGVyZSB0aGUgdGV4dCBvZiB0aGUgcG9saWN5IGRhdGEgd2lsbCBiZSBsaXN0ZWQuCgotIGhpbnQgMQotIGhpbnQgMgoKW2ltYWdlXShodHRwczovL2ltYWdlLmNvbSkKCg==
        format: byte
        type: string
      rewardId:
        description: The uniquely given id of the reward
        example: 8552df83-cbd5-44db-b67e-0cbeb2785918
        pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
        type: string
      rewardPriorityType:
        description: Quest reward priority type
        enum:
          - MAIN
          - SUB
        example: MAIN
        type: string
      rewardType:
        description: The type of the reward
        enum:
          - COUPON
          - CONTENT
          - CERTIFICATE
        example: COUPON
        type: string
      title:
        description: The name of the reward
        example: Test Reward
        pattern: ^.{4,128}$
        type: string
    required:
      - rewardId
      - title
      - coverImageUrl
      - description
      - acquirementType
      - rewardType
      - rewardPriorityType
    type: object
  RewardHistory:
    properties:
      actionType:
        description: What kind of activity is Reward
        enum:
          - OBTAINED
          - USED
        example: OBTAINED
        type: string
      completedTime:
        description: Date and time the reward was completed or used
        example: 2024-01-01T10:00:00Z
        pattern: ^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])T([01]\d|2[0-3]):([0-5]\d):([0-5]\d).(\d\d\d)(Z|([+-](0[0-9]|1[0-3]):([0-5]\d)))?$
        type: string
      thumbnailImageUrl:
        description: Thumbnail image of Reward
        example: https://marbullx.com/icon/reward/1
        pattern: ^(ipfs:\/\/[A-Za-z0-9]+|ar:\/\/[A-Za-z0-9_-]+|https:\/\/[A-Za-z0-9.-]+(?:\/[A-Za-z0-9._~:/?#[\]@!$&'()*+,;=%-]*)?)$
        type: string
      title:
        description: Title of Reward
        example: reward123
        pattern: ^.+$
        type: string
    type: object
  SendUserOperationId:
    properties:
      operationId:
        pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
        type: string
    required:
      - operationId
    type: object
  SerialCode:
    properties:
      code:
        description: Serial code
        example: X11018Z
        maxLength: 36
        minLength: 4
        type: string
      maxUseNum:
        description: Serial code use num
        example: 100
        minimum: 1
        type: number
    required:
      - code
      - maxUseNum
    type: object
  SerialCodeCreateReq:
    properties:
      codeProcedures:
        items:
          $ref: "#/definitions/serialCodeProcedure"
        minItems: 1
        type: array
      endAt:
        description: The expiration date of the serial code.
        example: 2024-01-01T10:00:00Z
        pattern: ^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])T([01]\d|2[0-3]):([0-5]\d):([0-5]\d)(?:\.(\d{3}))?Z$
        type: string
      rewardId:
        description: Reward id can get after redeem Serial code
        example: 8552df83-cbd5-44db-b67e-0cbeb2785918
        pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
        type: string
      serialCodeTranslations:
        items:
          $ref: "#/definitions/SerialCodeTranslations"
        type: array
      slug:
        description: "Slug for the serial code project, used to identify the project in
          URLs. Must be unique.(allowed characters: a-z, 0-9, -, minimum length:
          5, maximum length: 255)"
        example: my-serial-code-project
        maxLength: 255
        minLength: 5
        pattern: ^[a-z0-9]+(?:-[a-z0-9]+)*$
        type: string
      startAt:
        description: The expiration date of the serial code.
        example: 2024-01-01T10:00:00Z
        pattern: ^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])T([01]\d|2[0-3]):([0-5]\d):([0-5]\d)(?:\.(\d{3}))?Z$
        type: string
    required:
      - serialCodeTranslations
      - codeProcedures
      - slug
      - rewardId
      - startAt
      - endAt
    type: object
  SerialCodeGenerationProject:
    properties:
      endAt:
        description: The expiration date of the serial code.
        example: 2024-01-01T10:00:00Z
        pattern: ^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])T([01]\d|2[0-3]):([0-5]\d):([0-5]\d)(?:\.(\d{3}))?Z$
        type: string
      rewardId:
        description: Reward id can get after redeem Serial code
        example: 8552df83-cbd5-44db-b67e-0cbeb2785918
        pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
        type: string
      serialCodeProjectId:
        description: Serial code ID
        example: 8552df83-cbd5-44db-b67e-0cbeb2785918
        pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
        type: string
      serialCodeProjectTranslations:
        description: List of translations for the serial code project
        items:
          $ref: "#/definitions/SerialCodeTranslations"
        type: array
      serialCodes:
        items:
          $ref: "#/definitions/GeneratedCode"
        type: array
      slug:
        description: "Slug for the serial code project, used to identify the project in
          URLs. Must be unique.(allowed characters: a-z, 0-9, -, minimum length:
          5, maximum length: 255)"
        example: my-serial-code-project
        maxLength: 255
        minLength: 5
        pattern: ^[a-z0-9]+(?:-[a-z0-9]+)*$
        type: string
      startAt:
        description: The expiration date of the serial code.
        example: 2024-01-01T10:00:00Z
        pattern: ^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])T([01]\d|2[0-3]):([0-5]\d):([0-5]\d)(?:\.(\d{3}))?Z$
        type: string
    required:
      - serialCodeProjectId
      - slug
      - serialCodeProjectTranslations
      - rewardId
      - startAt
      - endAt
      - serialCodes
    type: object
  SerialCodeProject:
    properties:
      projects:
        items:
          properties:
            description:
              minLength: 1
              type: string
            endAt:
              pattern: ^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])T([01]\d|2[0-3]):([0-5]\d):([0-5]\d)(?:\.(\d{3}))?Z$
              type: string
            name:
              minLength: 1
              type: string
            serialCodeProjectId:
              format: uuid
              type: string
            slug:
              maxLength: 255
              minLength: 5
              pattern: ^[a-z0-9]+(?:-[a-z0-9]+)*$
              type: string
            startAt:
              pattern: ^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])T([01]\d|2[0-3]):([0-5]\d):([0-5]\d)(?:\.(\d{3}))?Z$
              type: string
          required:
            - serialCodeProjectId
            - slug
            - name
            - description
            - startAt
            - endAt
          type: object
        type: array
    required:
      - projects
    type: object
  SerialCodeRedeemReq:
    properties:
      serialCode:
        maxLength: 36
        minLength: 4
        type: string
      serialCodeProjectId:
        format: uuid
        type: string
    required:
      - serialCode
      - serialCodeProjectId
    type: object
  SerialCodeTranslations:
    properties:
      description:
        description: ProjectDescription
        example: ProjectDescription
        minLength: 1
        type: string
      language:
        description: Language code
        enum:
          - de
          - en-GB
          - en-US
          - es
          - fr
          - it
          - ja
          - ko
          - pt
          - ru
          - th
          - zh-Hans
          - zh-Hant
        example: en-US
        type: string
      name:
        description: ProjectName
        example: ProjectName
        minLength: 1
        type: string
    required:
      - language
      - name
      - description
    type: object
  SerialCodesImport:
    properties:
      endAt:
        description: The expiration date of the serial code.
        example: 2024-01-01T10:00:00Z
        pattern: ^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])T([01]\d|2[0-3]):([0-5]\d):([0-5]\d)(?:\.(\d{3}))?Z$
        type: string
      hashKey:
        description: Used for hashing when saving serial codes. Specify an arbitrary string.
        example: abc123DEF456...
        minLength: 1
        type: string
      rewardId:
        description: Reward id can get after redeem Serial code
        example: 8552df83-cbd5-44db-b67e-0cbeb2785918
        pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
        type: string
      serialCodeTranslations:
        items:
          $ref: "#/definitions/SerialCodeTranslations"
        type: array
      serialCodes:
        items:
          $ref: "#/definitions/SerialCode"
        maxItems: 36
        minItems: 4
        type: array
      slug:
        description: "Slug for the serial code project, used to identify the project in
          URLs. Must be unique.(allowed characters: a-z, 0-9, -, minimum length:
          5, maximum length: 255)"
        example: my-serial-code-project
        maxLength: 255
        minLength: 5
        pattern: ^[a-z0-9]+(?:-[a-z0-9]+)*$
        type: string
      startAt:
        description: The expiration date of the serial code.
        example: 2024-01-01T10:00:00Z
        pattern: ^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])T([01]\d|2[0-3]):([0-5]\d):([0-5]\d)(?:\.(\d{3}))?Z$
        type: string
    required:
      - serialCodeTranslations
      - serialCodes
      - slug
      - rewardId
      - startAt
      - endAt
    type: object
  Service:
    properties:
      isMarketEnabled:
        description: Whether the service is enabled in the market
        example: true
        type: boolean
      logoImageUrl:
        description: URL of the logo image
        example: https://marbullx.com/logo
        pattern: ^(ipfs:\/\/[A-Za-z0-9]+|ar:\/\/[A-Za-z0-9_-]+|https:\/\/[A-Za-z0-9.-]+(?:\/[A-Za-z0-9._~:/?#[\]@!$&'()*+,;=%-]*)?)$
        type: string
      marketCoverImageUrl:
        description: URL of the cover image
        example: https://marbullx.com/cover
        pattern: ^(ipfs:\/\/[A-Za-z0-9]+|ar:\/\/[A-Za-z0-9_-]+|https:\/\/[A-Za-z0-9.-]+(?:\/[A-Za-z0-9._~:/?#[\]@!$&'()*+,;=%-]*)?)$
        type: string
      name:
        description: The name of the service
        example: Test Service
        pattern: ^.{4,128}$
        type: string
      policy:
        description: Markdown text encoded in base64
        example: "## サービス利用規約"
        format: byte
        minLength: 1
        type: string
      serviceId:
        description: The uniquely given id of the service
        example: 8552df83-cbd5-44db-b67e-0cbeb2785918
        pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
        type: string
      themePrimaryColorHigher:
        description: ARGB hex color code
        example: "0xFFF94D62"
        pattern: ^0x[A-Fa-f0-9]{8}$
        type: string
      themePrimaryColorHighest:
        description: ARGB hex color code
        example: "0xFF6F000D"
        pattern: ^0x[A-Fa-f0-9]{8}$
        type: string
      themePrimaryColorLower:
        description: ARGB hex color code
        example: "0xFFFD8E9C"
        pattern: ^0x[A-Fa-f0-9]{8}$
        type: string
      themePrimaryColorLowest:
        description: ARGB hex color code
        example: "0xFFFDD4D9"
        pattern: ^0x[A-Fa-f0-9]{8}$
        type: string
    required:
      - serviceId
      - name
      - policy
      - logoImageUrl
      - isMarketEnabled
    type: object
  SessionData:
    properties:
      sessionId:
        description: Unique ID for each Stripe session
        example: cs_test_a11YYufWQzNY63zpQ6QSNRQhkUpVph4WRmzW0zWJO2znZKdVujZ0N0S22u
        type: string
    required:
      - sessionId
    type: object
  SingleChoiceQuestionExtra:
    properties:
      maximumSelectable:
        description: Maximum number of selections allowed
        example: 3
        maximum: 1
        minimum: 1
        type: number
      minimumSelectable:
        description: Minimum number of selections required
        example: 1
        maximum: 1
        minimum: 1
        type: number
      selections:
        description: questionType is SINGLE-CHOICE
        items:
          $ref: "#/definitions/SingleChoiceSelection"
        type: array
      type:
        enum:
          - SINGLE-CHOICE
        type: string
    required:
      - type
      - selections
    type: object
  SingleChoiceSelection:
    properties:
      order:
        minimum: 0
        type: integer
      selectionText:
        minLength: 1
        type: string
    required:
      - order
      - selectionText
    type: object
  Status:
    properties:
      badges:
        description: List of currently held status certificates that are valid
        example: []
        items:
          $ref: "#/definitions/StatusCertificate"
        type: array
      completedQuests:
        description: Aggregate number of quests currently completed
        example: 4
        type: number
      currentPeriodCompletedQuests:
        description: Aggregate number of quests currently completed (by current Status
          Quest period)
        example: 1
        type: number
      obtainedRewards:
        description: Aggregate number of rewards currently retrieved
        example: 10
        type: number
      unusedCoupon:
        description: Aggregate number of current unused coupons
        example: 2
        type: number
    type: object
  StatusCertificate:
    properties:
      description:
        description: Markdown text encoded in base64
        example: IyBDZXJ0aWZpZWQgT3BlbkFQSSBTcGVjaWFsaXN0Lg==
        type: string
      imageUrl:
        description: URL of the certificate image.
        example: https://example.com/images/certificate.png
        pattern: ^(ipfs:\/\/[A-Za-z0-9]+|ar:\/\/[A-Za-z0-9_-]+|https:\/\/[A-Za-z0-9.-]+(?:\/[A-Za-z0-9._~:/?#[\]@!$&'()*+,;=%-]*)?)$
        type: string
      rank:
        description: Status Badge Priority. The higher the higher the contribution.
        example: 1
        type: number
      rewardId:
        description: The uniquely given id of the reward
        example: 8552df83-cbd5-44db-b67e-0cbeb2785918
        pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
        type: string
      title:
        description: The name of the certificate
        example: Test cert
        pattern: ^.{4,128}$
        type: string
    type: object
  StatusQuest:
    properties:
      actions:
        description: List of action information linked to Quest
        items:
          $ref: "#/definitions/AchievementAction"
        type: array
      description:
        description: Markdown text encoded in base64
        example: IyBIZWxsbyB3b3JsZCEKIyMgcG9saWN5IGRhdGEKVGhpcyBpcyB3aGVyZSB0aGUgdGV4dCBvZiB0aGUgcG9saWN5IGRhdGEgd2lsbCBiZSBsaXN0ZWQuCgotIGhpbnQgMQotIGhpbnQgMgoKW2ltYWdlXShodHRwczovL2ltYWdlLmNvbSkKCg==
        format: byte
        type: string
      expiredAt:
        description: End date and time when the quest is disabled
        example: 2024-01-20T10:00:00Z
        pattern: ^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])T([01]\d|2[0-3]):([0-5]\d):([0-5]\d)(?:\.(\d{3}))?Z$
        type: string
      questId:
        description: Uniquely given identifier for each quest
        example: 8552df83-cbd5-44db-b67e-0cbeb2785918
        pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
        type: string
      startedAt:
        description: Start date and time when the quest is activated
        example: 2024-01-01T10:00:00Z
        pattern: ^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])T([01]\d|2[0-3]):([0-5]\d):([0-5]\d)(?:\.(\d{3}))?Z$
        type: string
      title:
        description: The name of the service
        example: Quest title
        pattern: ^.{4,128}$
        type: string
    required:
      - questId
      - title
      - description
      - startedAt
      - expiredAt
      - actions
    type: object
  StripeCheckoutInfo:
    properties:
      account:
        description: Stripe linked accounts that have paid out credential.
        example: acct_2H3G4F5E6D7C8B9A
        type: string
      credential:
        description: Stripe credential information.
        example: sk_test_51Psm8iE6HxsB9azvGay7HeR6LJnaSWP4L35kscOy1ChPPk4TGHKgxPhgBxDwe0spwKMoAi4eBcNqlvpPhzZycmpD00mQs1FY4b
        type: string
      sessionId:
        description: Stripe checkout session id
        example: sk_test_51Psm8iE6HxsB9azvGay7HeR6LJnaSWP4L35kscOy1ChPPk4TGHKgxPhgBxDwe0spwKMoAi4eBcNqlvpPhzZycmpD00mQs1FY4b
        type: string
    required:
      - sessionId
      - account
      - credential
    type: object
  StripeProduct:
    properties:
      availableAt:
        description: The date at which the associated product is available from
        example: 2024-01-01T09:00:00Z
        format: date-time
        pattern: ^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])T([01]\d|2[0-3]):([0-5]\d):([0-5]\d)(?:\.(\d{3}))?Z$
        type: string
      category:
        description: Category name returned by Stripe metadata
        example: category-a
        type: string
      coverImageUrl:
        description: URI to the cover image associated with the coupon.
        example: https://example.com/image.png
        format: uri
        type: string
      currency:
        description: Currency of the price
        example: jpy
        type: string
      description:
        description: Here is the base64 information on the detailed product description
        example: SGVyZSBpcyB0aGUgYmFzZTY0IGluZm9ybWF0aW9uIG9uIHRoZSBkZXRhaWxlZCBwcm9kdWN0IGRlc2NyaXB0aW9u
        format: byte
        type: string
      expireAt:
        description: The date until which the associated product is available for
        example: 2024-01-01T08:59:59Z
        format: date-time
        pattern: ^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])T([01]\d|2[0-3]):([0-5]\d):([0-5]\d)(?:\.(\d{3}))?Z$
        type: string
      imageUrl:
        description: URI to the image associated with the coupon.
        example: https://example.com/image.png
        format: uri
        type: string
      orderIndex:
        description: quest item order priority (default is desc and >= 1)
        example: 1
        exclusiveMinimum: true
        format: integer
        minimum: 0
        type: integer
      priceId:
        description: priceId returned by Stripe
        example: price_1MoBy5LkdIwHu7ixZhnattbh
        format: uuid
        type: string
      productId:
        description: Stripe product id
        example: prod_NWjs8kKbJWmuuc
        format: uuid
        type: string
      productName:
        description: Name of the product
        example: Cool NFT
        minLength: 1
        type: string
      quantity:
        description: remaining quantity for this product
        example: 10
        exclusiveMinimum: true
        format: integer
        minimum: 0
        type: integer
      thumbnailImageUrl:
        description: URI to the thumbnail image associated with the coupon.
        example: https://example.com/image.png
        format: uri
        type: string
      unitPrice:
        description: Price of the product
        example: 100
        exclusiveMinimum: true
        format: integer
        minimum: 0
        type: integer
    required:
      - productId
      - productName
      - description
      - imageUrl
      - thumbnailImageUrl
      - coverImageUrl
      - availableAt
      - expireAt
      - category
      - priceId
      - unitPrice
      - currency
      - quantity
      - orderIndex
    type: object
  StripeSessionConfirmStatus:
    properties:
      nftImageUrl:
        description: NFT image url distributed at the time of purchase.
        example: https://dummy.umage.com
        pattern: ^(ipfs:\/\/[A-Za-z0-9]+|ar:\/\/[A-Za-z0-9_-]+|https:\/\/[A-Za-z0-9.-]+(?:\/[A-Za-z0-9._~:/?#[\]@!$&'()*+,;=%-]*)?)$
        type: string
      nftTitle:
        description: NFT titles distributed at the time of purchase.
        example: NFT name 1
        type: string
      productName:
        description: Name of the target product to be purchased on Stripe
        example: Product 1
        type: string
      status:
        description: Settlement status
        enum:
          - PROCESSING
          - FAILED
          - SUCCEED
        example: SUCCEED
        type: string
    required:
      - status
    type: object
  Tenant:
    properties:
      keyRingLocation:
        description: Location of the KeyRing that generated the VaultKey
        example: asia-northeast1
        minLength: 1
        type: string
      keyRingProject:
        description: Name of the GCP project that generated the VaultKey
        example: marbull-vault-1
        minLength: 1
        type: string
      planId:
        description: The ID of the tenant
        example: 8552df83-cbd5-44db-b67e-0cbeb2785918
        pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
        type: string
      tenantId:
        description: The ID of the tenant
        example: 8552df83-cbd5-44db-b67e-0cbeb2785918
        pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
        type: string
      tenantTranslations:
        items:
          properties:
            language:
              description: Language code
              enum:
                - de
                - en-GB
                - en-US
                - es
                - fr
                - it
                - ja
                - ko
                - pt
                - ru
                - th
                - zh-Hans
                - zh-Hant
              example: en-US
              type: string
            tenantName:
              description: Register a tenant name. Basically, it should be the name of the
                company.
              example: Marbull X
              maxLength: 512
              minLength: 1
              type: string
          required:
            - language
            - tenantName
          type: object
        type: array
      vaultKeyName:
        description: Name of ECDSA key to be used for signing
        example: evm-key
        minLength: 1
        type: string
      vaultWalletAddress:
        description: |-
          * Address format with EVM checksum according to `EIP-55`
          * Perform checksum verification
        example: "******************************************"
        pattern: ^0x[a-fA-F0-9]{40}$
        type: string
    required:
      - tenantId
      - tenantTranslations
      - planId
      - vaultWalletAddress
      - keyRingLocation
      - keyRingProject
      - vaultKeyName
    type: object
  TenantInput:
    properties:
      planId:
        description: Uniquely paid out Id for each plan
        example: f47ac10b-58cc-4372-a567-0e02b2c3d479
        format: uuid
        pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
        type: string
      tenantTranslations:
        items:
          properties:
            language:
              description: Language code
              enum:
                - de
                - en-GB
                - en-US
                - es
                - fr
                - it
                - ja
                - ko
                - pt
                - ru
                - th
                - zh-Hans
                - zh-Hant
              example: en-US
              type: string
            tenantName:
              description: Register a tenant name. Basically, it should be the name of the
                company.
              example: Marbull X
              maxLength: 512
              minLength: 1
              type: string
          required:
            - language
            - tenantName
          type: object
        type: array
    required:
      - planId
      - tenantTranslations
    type: object
  TextLinesQuestionExtra:
    properties:
      type:
        enum:
          - TEXT-LINES
        type: string
      validations:
        description: questionType is TEXT-LINES
        items:
          $ref: "#/definitions/QuestionValidation"
        type: array
    required:
      - type
      - validations
    type: object
  TextQuestionExtra:
    properties:
      type:
        enum:
          - TEXT
        type: string
      validations:
        description: questionType is TEXT
        items:
          $ref: "#/definitions/QuestionValidation"
        type: array
    required:
      - type
      - validations
    type: object
  TransactionData:
    properties:
      contractAddress:
        description: ERC721/ERC1155 contract address of the coupon
        example: "******************************************"
        pattern: ^0x[a-fA-F0-9]{40}$
        type: string
      queueId:
        description: Id of uniquely paid out transaction
        example: 8552df83-cbd5-44db-b67e-0cbeb2785918
        pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
        type: string
      tokenBoundAccountAddress:
        description: TBA wallet address linked membership NFT
        example: "******************************************"
        pattern: ^0x[a-fA-F0-9]{40}$
        type: string
    required:
      - queueId
      - contractAddress
      - tokenBoundAccountAddress
    type: object
  TxFinalityStatus:
    properties:
      message:
        description: A message providing additional information about the transaction status
        example: Transaction completed successfully
        type: string
      status:
        description: The finality status of the transaction
        example: success
        type: string
    required:
      - status
      - message
    type: object
  UpdateMetadataRequest:
    properties:
      chainId:
        description: Chain ID
        example: 137
        type: integer
      contractAddress:
        description: registered NFT Contract Address
        example: "******************************************"
        pattern: ^0x[a-fA-F0-9]{40}$
        type: string
      tokenId:
        description: NFT tokenId
        example:
          - "32"
        items:
          type: string
        type: array
    required:
      - contractAddress
      - chainId
    type: object
  UpdateMetadataResponse:
    properties:
      failedIds:
        description: List of tokenIds that failed to update metadata
        items:
          properties:
            errorMsg:
              type: string
            tokenId:
              type: string
          required:
            - tokenId
          type: object
        type: array
      updatedTokenUris:
        description: List of tokenIds that were updated successfully
        items:
          properties:
            tokenId:
              type: string
            tokenUri:
              format: uri
              type: string
          required:
            - tokenId
            - tokenUri
          type: object
        type: array
    required:
      - updatedTokenUris
    type: object
  UpdateQuestion:
    properties:
      answerPoint:
        example: 10
        type: number
      isRequired:
        description: Indicates if the question is required
        example: true
        type: boolean
      questionId:
        example: cb9555c4-07e5-4137-a22d-44291faeb2c6
        pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
        type: string
      questionImageUrl:
        description: URL for the question image
        example: ""
        format: uri
        pattern: ^https://.*$
        type: string
      questionNumber:
        example: 1
        type: number
      questionTranslations:
        items:
          properties:
            correctData:
              type: string
            correctDataValidation:
              type: string
            language:
              description: Language code
              enum:
                - de
                - en-GB
                - en-US
                - es
                - fr
                - it
                - ja
                - ko
                - pt
                - ru
                - th
                - zh-Hans
                - zh-Hant
              example: en-US
              type: string
            questionDetail:
              type: string
            questionExtra:
              $ref: "#/definitions/QuestionExtra"
            questionTitle:
              minLength: 1
              type: string
          required:
            - language
            - questionTitle
            - questionExtra
          type: object
        type: array
      questionType:
        description: Type of the question
        enum:
          - SINGLE-CHOICE
          - MULTI-CHOICE
          - TEXT
          - TEXT-LINES
          - NUMBER
          - IMAGE
        type: string
    required:
      - questionId
      - questionNumber
      - questionType
      - questionTranslations
      - isRequired
    type: object
  UpdateRank:
    properties:
      isPassed:
        example: true
        type: boolean
      lowerLimitPoints:
        example: 0
        type: integer
      rank:
        example: 1
        type: integer
      rankHeaderAnimationUrl:
        example: https://cdn.rive.app/animations/vehicles.riv
        format: uri
        maxLength: 512
        type: string
      rankId:
        example: cb9555c4-07e5-4137-a22d-44291faeb2c6
        pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
        type: string
      rankTranslations:
        items:
          properties:
            language:
              description: Language code
              enum:
                - de
                - en-GB
                - en-US
                - es
                - fr
                - it
                - ja
                - ko
                - pt
                - ru
                - th
                - zh-Hans
                - zh-Hant
              example: en-US
              type: string
            rankName:
              minLength: 1
              type: string
          required:
            - language
            - rankName
          type: object
        type: array
      upperLimitPoints:
        example: 10
        type: integer
    required:
      - rankId
      - rankHeaderAnimationUrl
      - rankTranslations
      - rank
      - lowerLimitPoints
      - upperLimitPoints
      - isPassed
    type: object
  UpdateTheme:
    properties:
      questions:
        items:
          $ref: "#/definitions/UpdateQuestion"
        type: array
      themeCoverImageUrl:
        example: https://example.com/theme-cover.png
        format: uri
        maxLength: 512
        type: string
      themeId:
        example: cb9555c4-07e5-4137-a22d-44291faeb2c6
        pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
        type: string
      themeNumber:
        example: 1
        type: number
      themeThumbnailImageUrl:
        example: https://example.com/theme-thumbnail.png
        format: uri
        maxLength: 512
        type: string
      themeTimeLimitSeconds:
        example: 600
        type: number
      themeTranslations:
        items:
          properties:
            language:
              description: Language code
              enum:
                - de
                - en-GB
                - en-US
                - es
                - fr
                - it
                - ja
                - ko
                - pt
                - ru
                - th
                - zh-Hans
                - zh-Hant
              example: en-US
              type: string
            themeDescription:
              minLength: 1
              type: string
            themeTitle:
              minLength: 1
              type: string
          required:
            - language
          type: object
        type: array
    required:
      - themeId
      - themeNumber
      - questions
    type: object
  User:
    properties:
      account:
        description: Linked service account info
        properties:
          accountId:
            description: None if no account for the service has been issued
            example: 19bcd4c4-892a-4ecc-9832-90bd2b0a7ed0
            pattern: ^[a-zA-Z0-9_-]{1,128}$
            type: string
          serviceId:
            description: Uniquely given identifier for each service
            example: 19bcd4c4-892a-4ecc-9832-90bd2b0a7ed0
            pattern: ^[a-zA-Z0-9_-]{1,128}$
            type: string
        required:
          - serviceId
        type: object
      contractAccountAddress:
        description: |-
          * Address format with EVM checksum according to `EIP-55`
          * Perform checksum verification
        example: "******************************************"
        pattern: ^0x[a-fA-F0-9]{40}$
        type: string
      phone:
        $ref: "#/definitions/Phone"
      userId:
        description: Uniquely given identifier for each user
        example: 19bcd4c4-892a-4ecc-9832-90bd2b0a7ed0
        minLength: 1
        pattern: ^[a-zA-Z0-9_-]{1,128}$
        type: string
    required:
      - userId
      - phone
      - account
    type: object
  UserCheck:
    properties:
      isUserExist:
        example: true
        type: boolean
    required:
      - isUserExist
    type: object
  UserOperationCallData:
    properties:
      callData:
        pattern: ^0x[a-fA-F0-9]{1,1024}$
        type: string
    required:
      - callData
    type: object
  UserOperationResult:
    properties:
      uoHash:
        pattern: ^0x[a-fA-F0-9]{64}$
        type: string
    required:
      - uoHash
    type: object
  VaultDetails:
    properties:
      chain:
        description: Blockchain network type (e.g., Polygon TestNet or MainNet)
        example: Polygon MainNet
        type: string
      consumptionData:
        description: Consumption data
        properties:
          last12h:
            description: Consumption in the last 12 hours in POL
            example: 100
            type: number
          last1h:
            description: Consumption in the last 1 hour in POL
            example: 10
            type: number
          last24h:
            description: Consumption in the last 24 hours in POL
            example: 200
            type: number
          last6h:
            description: Consumption in the last 6 hours in POL
            example: 50
            type: number
          lastUpdatedAt:
            description: The timestamp of the last update for consumption data
            example: 2023-10-01T12:00:00Z
            pattern: ^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])T([01]\d|2[0-3]):([0-5]\d):([0-5]\d)(?:\.(\d{3}))?Z$
            type: string
        type: object
      currentBalance:
        description: Current balance in POL
        example: "1000.5"
        type: string
      tenantName:
        description: Name of the tenant associated with the Vault
        example: Tenant A
        type: string
      vaultWalletAddress:
        description: |-
          * Address format with EVM checksum according to `EIP-55`
          * Perform checksum verification
        example: "******************************************"
        pattern: ^0x[a-fA-F0-9]{40}$
        type: string
    required:
      - tenantName
      - vaultWalletAddress
      - chain
      - currentBalance
      - consumptionData
    type: object
  VaultKeyInfo:
    properties:
      keyRingLocation:
        description: Location of the KeyRing that generated the VaultKey
        example: asia-northeast1
        type: string
      keyRingName:
        description: Name of the KeyRing to generate, matching the TenantId.
        example: 8552df83-cbd5-44db-b67e-0cbeb2785918
        pattern: ^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89abAB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$
        type: string
      keyRingProject:
        description: Name of the GCP project that generated the VaultKey
        example: marbull-vault-1
        type: string
      vaultKeyName:
        description: Name of the VaultKey stored in KeyRing
        example: evm-key
        type: string
      vaultWalletAddress:
        description: |-
          * Address format with EVM checksum according to `EIP-55`
          * Perform checksum verification
        example: "******************************************"
        pattern: ^0x[a-fA-F0-9]{40}$
        type: string
    required:
      - keyRingProject
      - keyRingLocation
      - keyRingName
      - vaultKeyName
      - vaultWalletAddress
    type: object
  WebhookTransactionResponse:
    properties:
      message:
        description: Processing result message or error details
        type: string
      success:
        description: Webhook processing result
        type: boolean
      timestamp:
        description: Processing timestamp
        format: date-time
        pattern: ^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])T([01]\d|2[0-3]):([0-5]\d):([0-5]\d)(?:\.(\d{3}))?Z$
        type: string
      transactionHash:
        description: Transaction hash
        pattern: ^0x[a-fA-F0-9]{64}$
        type: string
    type: object
  serialCodeProcedure:
    properties:
      createCodeNum:
        description: Serial code create num
        example: 100
        minimum: 1
        type: number
      maxUseNum:
        description: Serial code use num
        example: 100
        minimum: 1
        type: number
    required:
      - createCodeNum
      - maxUseNum
    type: object
securityDefinitions:
  firebase:
    authorizationUrl: ""
    flow: implicit
    type: oauth2
    x-google-issuer: https://securetoken.google.com/marbull-membership-dev
    x-google-jwks_uri: https://www.googleapis.com/service_accounts/v1/metadata/x509/<EMAIL>
    x-google-audiences: marbull-membership-dev
  api_key:
    type: apiKey
    name: x-api-key
    in: header
tags:
  - description: テナント情報の管理
    name: tenant
  - description: テナントに紐づくサービスの管理
    name: service
  - description: テナントが保有する秘密鍵の管理
    name: vault
  - description: サービスレベルのクエスト情報
    name: quest
  - description: サービスレベルのマーケット情報
    name: product
  - description: サービス横断でのユーザー情報
    name: user
  - description: サービスレベルのユーザー情報
    name: account
  - description: 認証・認可
    name: authentication
  - description: NFT関連情報
    name: nft
x-components:
  parameters: {}
basePath: /
schemes:
  - https
x-google-backend:
  address: https://monolithic-service-*************.asia-northeast1.run.app
  deadline: 120
