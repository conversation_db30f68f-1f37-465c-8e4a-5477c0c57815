const fs = require('fs');
const yaml = require('yaml');
const args = require('minimist')(process.argv.slice(2));

const audience = args.audience;
const backendAddress = args.backend;
const src = args.src;
const dst = args.dst || 'openapi-v2-ep.yaml';

const doc = yaml.parse(fs.readFileSync(src, 'utf8'));

doc.swagger = "2.0";
doc.basePath = '/';
doc.schemes = ['https'];
delete doc.host;

doc.securityDefinitions = {
    firebase: {
        authorizationUrl: '',
        flow: 'implicit',
        type: 'oauth2',
        'x-google-issuer': `https://securetoken.google.com/${audience}`,
        'x-google-jwks_uri':
            'https://www.googleapis.com/service_accounts/v1/metadata/x509/<EMAIL>',
        'x-google-audiences': audience,
    },
    api_key: {
        type: 'apiKey',
        name: 'x-api-key',
        in: 'header',
    },
};

doc['x-google-backend'] = {
    address: backendAddress,
    deadline: 120.0,
};

// ────────────────
// CORS 用に既存パスへ OPTIONS を追加する処理
// ────────────────
// ヘルパー：パスパラメータ抽出
function extractPathParameters(path) {
    const matches = [...path.matchAll(/{([^}]+)}/g)];
    return matches.map((match) => ({
        name: match[1],
        in: 'path',
        type: 'string',
        required: true
    }));
}

// ヘルパー：path を operationId に変換
function generateOperationId(path) {
    return (
        'options_' +
        path
            .replace(/[{}]/g, '') // {id} → id
            .replace(/[^a-zA-Z0-9]/g, '_') // / → _
            .replace(/^_+|_+$/g, '') // 先頭末尾の _ を削除
    );
}

// 各 path に `options` を追加（未定義の場合）
for (const path in doc.paths) {
    const pathItem = doc.paths[path];

    if (!pathItem.options) {
        const pathParams = extractPathParameters(path);
        const operationId = generateOperationId(path);

        pathItem.options = {
            summary: 'CORS preflight handler',
            operationId,
            security: [],
            parameters: pathParams,
            responses: {
                '204': {
                    description: 'No content',
                    schema: {
                        type: "string"
                    },
                },
                "401": {
                    description: "Cors not allowed"
                }
            },
        };
        if (pathParams.length === 0) {
            delete pathItem.options.parameters;
        }
    }
}

// ────────────────
// ここから下に、新たに GET /docs/openapi.json 系のエントリーを追加
// ────────────────
function addDocGetPath(pathKey, operationId) {
    // 既に同じキーが存在しなければ追加
    if (!doc.paths[pathKey]) {
        doc.paths[pathKey] = {
            get: {
                summary: `Retrieve ${pathKey}`,
                operationId: operationId,
                security: [{ api_key: [] }],        // セキュリティはAPIKey
                responses: {
                    '200': {
                        description: 'OK',
                        // ファイルをそのまま返却する場合、スキーマは省略してもかまいませんが、
                        // 例示として以下のように「string（raw）」を返す想定にしています。
                        schema: {
                            type: 'string'
                        }
                    }
                }
            }
        };
    }
}

// `/docs/openapi.json` を GET
addDocGetPath('/docs/full/openapi.json', 'get_docs_openapi_json');
// `/docs/front/openapi.json` を GET
addDocGetPath('/docs/front/openapi.json', 'get_docs_front_openapi_json');
// `/docs/admin/openapi.json` を GET
addDocGetPath('/docs/admin/openapi.json', 'get_docs_admin_openapi_json');

// ────────────────
// ファイル出力
// ────────────────
fs.writeFileSync('../' + dst, yaml.stringify(doc));
console.log('✅ OPTIONS + operationId 付きで出力しました');