redocly bundle openapi.json -o .tmp/openapi.yaml && \
api-spec-converter --from=openapi_3 --to=swagger_2 --syntax=yaml .tmp/openapi.yaml > converted-swagger.yaml && \
rm .tmp/openapi.yaml

cd _convert

# infra-dev
node ./src/convert_ce_format.js \
--audience marbull-connect-infra-dev \
--backend https://monolithic-service-1067257605296.asia-northeast1.run.app \
--src ../converted-swagger.yaml \
--dst generated/infra-dev/openapi-v2-ep.yaml

# dev-mainnet
node ./src/convert_ce_format.js \
--audience marbull-membership-dev \
--backend https://monolithic-service-1094211490525.asia-northeast1.run.app \
--src ../converted-swagger.yaml \
--dst generated/dev-mainnet/openapi-v2-ep.yaml

# dev-testnet
node ./src/convert_ce_format.js \
--audience marbull-membership-dev \
--backend https://monolithic-service-887879667985.asia-northeast1.run.app \
--src ../converted-swagger.yaml \
--dst generated/dev-testnet/openapi-v2-ep.yaml

# stg
node ./src/convert_ce_format.js \
--audience marbull-membership-dev \
--backend https://monolithic-service-284562847457.asia-northeast1.run.app \
--src ../converted-swagger.yaml \
--dst generated/stg/openapi-v2-ep.yaml

# prod
node ./src/convert_ce_format.js \
--audience marbull-membership \
--backend https://monolithic-service-572578320001.asia-northeast1.run.app \
--src ../converted-swagger.yaml \
--dst generated/prod/openapi-v2-ep.yaml