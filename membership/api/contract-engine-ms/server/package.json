{"name": "api_contract-engine-ms", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node ./dist/index.js", "migrate": "npx kysely migrate:latest", "lint": "eslint .", "lint:fix": "eslint . --fix", "prettier": "prettier --ignore-path .gitignore --write \"./src/**/*.+(js|ts|json)\"", "prettier:fix": "prettier --write src", "test": "npx jest"}, "dependencies": {"@hono/node-server": "1.12.2", "@hono/zod-validator": "0.2.2", "hono": "4.5.11", "kysely": "0.27.4", "pg": "8.12.0", "reflect-metadata": "0.2.2", "tsyringe": "4.8.0", "zod": "3.23.8"}, "devDependencies": {"@eslint/js": "9.11.0", "@types/jest": "29.5.13", "@types/node": "22.5.4", "@types/pg": "8.11.9", "eslint": "9.11.0", "eslint-config-prettier": "9.1.0", "jest": "29.7.0", "kysely-ctl": "0.9.0", "prettier": "3.3.3", "ts-jest": "29.2.5", "tsx": "4.19.1", "typescript-eslint": "8.6.0"}}