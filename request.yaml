curl -X POST "http://127.0.0.10/accounts/c709a7aa-61d4-4ff2-a540-13a5950beaa8/email-otp" \
  -H "Content-Type: application/json" \
  -H "Accept-Language: ja" \
  -H "Service-Id-Header: 5ac198b3-ade6-403a-9f26-1a0eafc65b09" \
  -d '{
    "email": "<EMAIL>"
  }'

curl -X POST "http://127.0.0.10/accounts/c709a7aa-61d4-4ff2-a540-13a5950beaa8/email-otp/verify" \
  -H "Content-Type: application/json" \
  -H "Service-Id-Header: 5ac198b3-ade6-403a-9f26-1a0eafc65b09" \
  -d '{
    "email": "<EMAIL>",
    "otp": "552130"
  }'


curl -X GET "http://127.0.0.10/accounts/c709a7aa-61d4-4ff2-a540-13a5950beaa8/custom-field" \
  -H "Service-Id-Header: 5ac198b3-ade6-403a-9f26-1a0eafc65b09"\
  -H "Accept-Language: ja"  | jq .

curl -X POST http://127.0.0.10/accounts/c709a7aa-61d4-4ff2-a540-13a5950beaa8/custom-field \
  -H "Content-Type: application/json" \
  -H "Service-Id-Header: 5ac198b3-ade6-403a-9f26-1a0eafc65b09" \
  -d '{
    "email": ["<EMAIL>"],
    "gender": ["other"],
    "phoneNumber": ["+**********"]
  }' | jq .

user2
curl -X POST http://127.0.0.10/accounts/c709a7aa-61d4-4ff2-a540-13a5950beaa9/custom-field \
  -H "Content-Type: application/json" \
  -H "Service-Id-Header: 5ac198b3-ade6-403a-9f26-1a0eafc65b09" \
  -d '{
    "gender": ["male", "other"],
    "phoneNumber": "+**********"
  }' | jq .

curl -X POST http://127.0.0.10/accounts/c709a7aa-61d4-4ff2-a540-13a5950beaa9/custom-field \
  -H "Content-Type: application/json" \
  -H "Service-Id-Header: 5ac198b3-ade6-403a-9f26-1a0eafc65b09" \
  -d '{
    "email": ["<EMAIL>"],
    "gender": "female",
    "hobby": ["sport", "reading"],
    "phoneNumber": "+**********"
  }' | jq .

curl -X POST http://127.0.0.10/accounts/c709a7aa-61d4-4ff2-a540-13a5950beaa8/custom-field \
  -H "Content-Type: application/json" \
  -H "Service-Id-Header: 5ac198b3-ade6-403a-9f26-1a0eafc65b09" \
  -d '{
    "email": "<EMAIL>",
    "phoneNumber": "+**********",
    "hobby": ["sport", "reading"]
  }' | jq .

curl -X POST http://127.0.0.10/accounts/c709a7aa-61d4-4ff2-a540-13a5950beaa8/custom-field \
  -H "Content-Type: application/json" \
  -H "Service-Id-Header: 5ac198b3-ade6-403a-9f26-1a0eafc65b09" \
  -d '{
    "phoneNumber": "+**********",
    "hobby": ["cooking", "reading"]
  }' | jq .

curl -X GET http://127.0.0.10/services/custom-fields \
  -H "Content-Type: application/json" \
  -H "Service-Id-Header: 5ac198b3-ade6-403a-9f26-1a0eafc65b09" \
  -H "Accept-Language: ja" | jq .

curl -X POST http://127.0.0.10/services/custom-fields \
  -H "Content-Type: application/json" \
  -H "Service-Id-Header: 5ac198b3-ade6-403a-9f26-1a0eafc65b09" \
  -d '{
    "fields": [
      {
        "fieldKey": "email",
        "type": "EMAIL",
        "optional": false,
        "sortOrder": 1,
        "maxLength": 15,
        "minLength": 10,
        "unique": true,
        "verify": true,
        "translations": [
          {
            "locale": "en-US",
            "label": "Email Address"
          },
          {
            "locale": "ja",
            "label": "メールアドレス"
          }
        ],
        "validator": {
          "description": "Email address validator",
          "pattern": "^[\\w-\\.]+@([\\w-]+\\.)+[\\w-]{2,4}$",
          "translations": [
            {
              "locale": "en-US",
              "errorMessage": "Please enter a valid email address",
              "description": "Email address validator"
            },
            {
              "locale": "ja",
              "errorMessage": "有効なメールアドレスを入力してください",
              "description": "メールアドレスバリデーター"
            }
          ]
        }
      },
      {
        "fieldKey": "gender",
        "type": "SELECTION",
        "optional": true,
        "sortOrder": 2,
        "unique": false,
        "verify": false,
        "translations": [
          {
            "locale": "en-US",
            "label": "Gender"
          },
          {
            "locale": "ja",
            "label": "性別"
          }
        ],
        "options": [
          {
            "value": "male",
            "sortOrder": 0,
            "translations": [
              {
                "locale": "en-US",
                "label": "Male"
              },
              {
                "locale": "ja",
                "label": "男性"
              }
            ]
          },
          {
            "value": "female",
            "sortOrder": 1,
            "translations": [
              {
                "locale": "en-US",
                "label": "Female"
              },
              {
                "locale": "ja",
                "label": "女性"
              }
            ]
          },
          {
            "value": "other",
            "sortOrder": 2,
            "translations": [
              {
                "locale": "en-US",
                "label": "Other"
              },
              {
                "locale": "ja",
                "label": "その他"
              }
            ]
          }
        ]
      },
      {
        "fieldKey": "hobby",
        "type": "MULTIPLE-SELECTION",
        "optional": true,
        "sortOrder": 3,
        "unique": false,
        "verify": false,
        "translations": [
          {
            "locale": "en-US",
            "label": "Hobby"
          },
          {
            "locale": "ja",
            "label": "趣味"
          }
        ],
        "options": [
          {
            "value": "sport",
            "sortOrder": 0,
            "translations": [
              {
                "locale": "en-US",
                "label": "Sport"
              },
              {
                "locale": "ja",
                "label": "スポーツ"
              }
            ]
          },
          {
            "value": "reading",
            "sortOrder": 1,
            "translations": [
              {
                "locale": "en-US",
                "label": "reading"
              },
              {
                "locale": "ja",
                "label": "読書"
              }
            ]
          },
          {
            "value": "cooking",
            "sortOrder": 2,
            "translations": [
              {
                "locale": "en-US",
                "label": "Cooking"
              },
              {
                "locale": "ja",
                "label": "料理"
              }
            ]
          }
        ]
      },
      {
        "defaultValue": "+**********",
        "fieldKey": "phoneNumber",
        "type": "PHONE",
        "optional": true,
        "sortOrder": 4,
        "unique": false,
        "verify": false,
        "translations": [
          {
            "locale": "en-US",
            "label": "Phone Number"
          },
          {
            "locale": "ja",
            "label": "電話番号"
          }
        ],
        "validator": {
          "pattern": "^\\+?[0-9]{10,15}$",
          "translations": [
            {
              "locale": "en-US",
              "errorMessage": "Please enter a valid phone number",
              "description": "Phone number validator"
            },
            {
              "locale": "ja",
              "errorMessage": "有効な電話番号を入力してください",
              "description": "電話番号バリデーター"
            }
          ]
        }
      }
    ]
  }' | jq .